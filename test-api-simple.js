// Simple test to check API endpoints
import fetch from 'node-fetch';

async function testAPI() {
  console.log('🧪 Testing Stock Adjustments API...');
  
  try {
    // Test GET endpoint
    console.log('📡 Testing GET /api/inventory/adjustments...');
    const getResponse = await fetch('http://localhost:3000/api/inventory/adjustments');
    
    console.log('📊 GET Response Status:', getResponse.status);
    console.log('📊 GET Response Headers:', Object.fromEntries(getResponse.headers.entries()));
    
    const getResponseText = await getResponse.text();
    console.log('📊 GET Response Body:', getResponseText);
    
    if (!getResponse.ok) {
      console.log('❌ GET request failed');
      try {
        const errorData = JSON.parse(getResponseText);
        console.log('🔍 Error details:', errorData);
      } catch (e) {
        console.log('🔍 Could not parse error as JSON');
      }
    } else {
      console.log('✅ GET request successful');
      try {
        const data = JSON.parse(getResponseText);
        console.log('📋 Data structure:', {
          adjustments: Array.isArray(data.adjustments) ? `Array(${data.adjustments.length})` : typeof data.adjustments,
          pagination: data.pagination
        });
      } catch (e) {
        console.log('🔍 Could not parse response as JSON');
      }
    }
    
  } catch (error) {
    console.error('💥 Test failed:', error.message);
  }
}

testAPI();
