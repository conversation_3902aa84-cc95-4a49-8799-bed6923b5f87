generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                         String                   @id @default(cuid())
  name                       String
  email                      String                   @unique
  password                   String?
  role                       UserRole                 @default(CASHIER)
  createdAt                  DateTime                 @default(now())
  updatedAt                  DateTime                 @updatedAt
  active                     Boolean                  @default(true)
  activityLogs               ActivityLog[]
  receivedMessages           Message[]                @relation("ReceivedMessages")
  sentMessages               Message[]                @relation("SentMessages")
  receivedNotifications      Notification[]           @relation("UserNotifications")
  participantInConversations Participant[]
  starredConversations       StarredConversation[]
  approvedPOs                PurchaseOrder[]          @relation("ApprovedPurchaseOrders")
  purchaseOrders             PurchaseOrder[]          @relation("CreatedPurchaseOrders")
  sessions                   Session[]
  approvals                  Transaction[]            @relation("ApproverTransactions")
  transactions               Transaction[]            @relation("CashierTransactions")
  stockAdjustments           StockAdjustment[]
  stockHistory               StockHistory[]
  requestedSimpleTransfers   SimpleStockTransfer[]    @relation("RequestedSimpleTransfers")
  approvedSimpleTransfers    SimpleStockTransfer[]    @relation("ApprovedSimpleTransfers")
  temporaryPrices            TemporaryPrice[]
  drawerSessions             DrawerSession[]
  cashReconciliations        CashReconciliation[]
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ActivityLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?
  timestamp DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Product {
  id                  String                @id @default(cuid())
  name                String
  description         String?
  sku                 String                @unique
  barcode             String?               @unique
  categoryId          String?
  unitId              String
  supplierId          String?
  basePrice           Decimal               @db.Decimal(10, 2)
  purchasePrice       Decimal?              @db.Decimal(10, 2)
  optionalPrice1      Decimal?              @db.Decimal(10, 2)
  optionalPrice2      Decimal?              @db.Decimal(10, 2)
  discountValue       Decimal?              @db.Decimal(10, 2)
  discountType        DiscountType?
  expiryDate          DateTime?
  imageUrl            String?
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  active              Boolean               @default(true)
  category            Category?             @relation(fields: [categoryId], references: [id])
  unit                Unit                  @relation(fields: [unitId], references: [id])
  supplier            Supplier?             @relation(fields: [supplierId], references: [id])
  purchaseOrderItems  PurchaseOrderItem[]
  storeStock          StoreStock?
  transactionItems    TransactionItem[]
  warehouseStock      WarehouseStock?
  stockAdjustments    StockAdjustment[]
  stockHistory        StockHistory[]
  simpleStockTransfers SimpleStockTransfer[]
  temporaryPrice      TemporaryPrice?
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Unit {
  id           String    @id @default(cuid())
  name         String    @unique
  abbreviation String
  description  String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  products     Product[]
}

model StoreStock {
  id                String           @id @default(cuid())
  productId         String           @unique
  quantity          Decimal          @db.Decimal(10, 2)
  minThreshold      Decimal          @db.Decimal(10, 2)
  maxThreshold      Decimal?         @db.Decimal(10, 2)
  lastUpdated       DateTime         @default(now())
  product           Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  stockAdjustments  StockAdjustment[]
  stockHistory      StockHistory[]
}

model WarehouseStock {
  id                String           @id @default(cuid())
  productId         String           @unique
  quantity          Decimal          @db.Decimal(10, 2)
  minThreshold      Decimal          @default(0) @db.Decimal(10, 2)
  maxThreshold      Decimal?         @db.Decimal(10, 2)
  lastUpdated       DateTime         @default(now())
  product           Product          @relation(fields: [productId], references: [id], onDelete: Cascade)
  stockAdjustments  StockAdjustment[]
  stockHistory      StockHistory[]
}

model StockAdjustment {
  id              String            @id @default(cuid())
  date            DateTime          @default(now())
  productId       String
  storeStockId    String?
  warehouseStockId String?
  previousQuantity Decimal          @db.Decimal(10, 2)
  newQuantity     Decimal          @db.Decimal(10, 2)
  adjustmentQuantity Decimal       @db.Decimal(10, 2)
  reason          AdjustmentReason
  notes           String?
  userId          String
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  product         Product           @relation(fields: [productId], references: [id])
  storeStock      StoreStock?       @relation(fields: [storeStockId], references: [id])
  warehouseStock  WarehouseStock?   @relation(fields: [warehouseStockId], references: [id])
  user            User              @relation(fields: [userId], references: [id])
}

model StockHistory {
  id              String            @id @default(cuid())
  date            DateTime          @default(now())
  productId       String
  storeStockId    String?
  warehouseStockId String?
  previousQuantity Decimal          @db.Decimal(10, 2)
  newQuantity     Decimal          @db.Decimal(10, 2)
  changeQuantity  Decimal          @db.Decimal(10, 2)
  source          StockChangeSource
  referenceId     String?           // ID of the related transaction, adjustment, transfer, etc.
  referenceType   String?           // Type of the reference (Transaction, Adjustment, Transfer, etc.)
  notes           String?
  userId          String
  createdAt       DateTime          @default(now())
  product         Product           @relation(fields: [productId], references: [id])
  storeStock      StoreStock?       @relation(fields: [storeStockId], references: [id])
  warehouseStock  WarehouseStock?   @relation(fields: [warehouseStockId], references: [id])
  user            User              @relation(fields: [userId], references: [id])
}



model SimpleStockTransfer {
  id              String            @id @default(cuid())
  date            DateTime          @default(now())
  productId       String
  quantity        Decimal           @db.Decimal(10, 2)
  fromStore       Boolean           // true = from store, false = from warehouse
  toStore         Boolean           // true = to store, false = to warehouse
  status          TransferStatus    @default(PENDING)
  notes           String?
  requestedById   String
  approvedById    String?
  approvedAt      DateTime?
  completedAt     DateTime?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  product         Product           @relation(fields: [productId], references: [id])
  requestedBy     User              @relation("RequestedSimpleTransfers", fields: [requestedById], references: [id])
  approvedBy      User?             @relation("ApprovedSimpleTransfers", fields: [approvedById], references: [id])
}

model Transaction {
  id              String            @id @default(cuid())
  transactionDate DateTime          @default(now())
  cashierId       String
  customerId      String?
  drawerSessionId String?
  subtotal        Decimal           @db.Decimal(10, 2)
  discount        Decimal           @default(0) @db.Decimal(10, 2)
  tax             Decimal           @default(0) @db.Decimal(10, 2)
  total           Decimal           @db.Decimal(10, 2)
  paymentMethod   PaymentMethod
  paymentStatus   PaymentStatus     @default(PENDING)
  dueDate         DateTime?
  approverId      String?
  approvedAt      DateTime?
  status          TransactionStatus @default(PENDING)
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  returns         Return[]
  approver        User?             @relation("ApproverTransactions", fields: [approverId], references: [id])
  cashier         User              @relation("CashierTransactions", fields: [cashierId], references: [id])
  customer        Customer?         @relation(fields: [customerId], references: [id])
  drawerSession   DrawerSession?    @relation(fields: [drawerSessionId], references: [id])
  items           TransactionItem[]
}

model TransactionItem {
  id            String      @id @default(cuid())
  transactionId String
  productId     String
  quantity      Decimal     @db.Decimal(10, 2)
  unitPrice     Decimal     @db.Decimal(10, 2)
  discount      Decimal     @default(0) @db.Decimal(10, 2)
  subtotal      Decimal     @db.Decimal(10, 2)
  product       Product     @relation(fields: [productId], references: [id])
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
}

model Customer {
  id           String        @id @default(cuid())
  name         String
  phone        String?
  email        String?
  address      String?
  customerType CustomerType  @default(REGULAR)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  returns      Return[]
  transactions Transaction[]
}

model PurchaseOrder {
  id              String              @id @default(cuid())
  orderDate       DateTime            @default(now())
  createdById     String
  supplierId      String
  subtotal        Decimal             @db.Decimal(10, 2)
  tax             Decimal             @default(0) @db.Decimal(10, 2)
  total           Decimal             @db.Decimal(10, 2)
  status          POStatus            @default(DRAFT)
  approvedById    String?
  approvedAt      DateTime?
  receivedAt      DateTime?
  notes           String?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
  approvedBy      User?               @relation("ApprovedPurchaseOrders", fields: [approvedById], references: [id])
  createdBy       User                @relation("CreatedPurchaseOrders", fields: [createdById], references: [id])
  supplier        Supplier            @relation(fields: [supplierId], references: [id])
  items           PurchaseOrderItem[]
  supplierReturns SupplierReturn[]
}

model PurchaseOrderItem {
  id              String        @id @default(cuid())
  purchaseOrderId String
  productId       String
  quantity        Decimal       @db.Decimal(10, 2)
  unitPrice       Decimal       @db.Decimal(10, 2)
  subtotal        Decimal       @db.Decimal(10, 2)
  product         Product       @relation(fields: [productId], references: [id])
  purchaseOrder   PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
}

model Supplier {
  id             String           @id @default(cuid())
  name           String
  contactPerson  String?
  phone          String?
  email          String?
  address        String?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  products       Product[]
  purchaseOrders PurchaseOrder[]
  returns        SupplierReturn[]
}

model Return {
  id            String       @id @default(cuid())
  returnDate    DateTime     @default(now())
  transactionId String
  customerId    String
  reason        String
  total         Decimal      @db.Decimal(10, 2)
  status        ReturnStatus @default(PENDING)
  notes         String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  customer      Customer     @relation(fields: [customerId], references: [id])
  transaction   Transaction  @relation(fields: [transactionId], references: [id])
  items         ReturnItem[]
}

model ReturnItem {
  id        String  @id @default(cuid())
  returnId  String
  productId String
  quantity  Decimal @db.Decimal(10, 2)
  unitPrice Decimal @db.Decimal(10, 2)
  subtotal  Decimal @db.Decimal(10, 2)
  return    Return  @relation(fields: [returnId], references: [id], onDelete: Cascade)
}

model SupplierReturn {
  id              String               @id @default(cuid())
  returnDate      DateTime             @default(now())
  purchaseOrderId String
  supplierId      String
  reason          String
  total           Decimal              @db.Decimal(10, 2)
  status          ReturnStatus         @default(PENDING)
  notes           String?
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt
  purchaseOrder   PurchaseOrder        @relation(fields: [purchaseOrderId], references: [id])
  supplier        Supplier             @relation(fields: [supplierId], references: [id])
  items           SupplierReturnItem[]
}

model SupplierReturnItem {
  id               String         @id @default(cuid())
  supplierReturnId String
  productId        String
  quantity         Decimal        @db.Decimal(10, 2)
  unitPrice        Decimal        @db.Decimal(10, 2)
  subtotal         Decimal        @db.Decimal(10, 2)
  supplierReturn   SupplierReturn @relation(fields: [supplierReturnId], references: [id], onDelete: Cascade)
}

model Notification {
  id        String           @id @default(cuid())
  userId    String
  title     String
  message   String
  type      NotificationType @default(SYSTEM)
  isRead    Boolean          @default(false)
  createdAt DateTime         @default(now())
  user      User             @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
}

model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Conversation {
  id           String                @id @default(cuid())
  title        String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  lastActivity DateTime              @default(now())
  messages     Message[]
  participants Participant[]
  starredBy    StarredConversation[]
}

model StarredConversation {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  starredAt      DateTime     @default(now())
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Participant {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  joinedAt       DateTime     @default(now())
  leftAt         DateTime?
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Message {
  id             String       @id @default(cuid())
  conversationId String
  senderId       String
  receiverId     String?
  content        String
  isRead         Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  receiver       User?        @relation("ReceivedMessages", fields: [receiverId], references: [id])
  sender         User         @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
}

enum UserRole {
  DEVELOPER
  SUPER_ADMIN
  CASHIER
  FINANCE_ADMIN
  WAREHOUSE_ADMIN
  MARKETING
}

enum CustomerType {
  REGULAR
  FRIEND
  FAMILY
}

enum PaymentMethod {
  CASH
  DEBIT
  QRIS
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  OVERDUE
  CANCELLED
}

enum TransactionStatus {
  PENDING
  COMPLETED
  VOIDED
  RETURNED
}

model CashDrawer {
  id            String          @id @default(cuid())
  name          String
  location      String?
  isActive      Boolean         @default(true)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  drawerSessions DrawerSession[]
}

model DrawerSession {
  id                    String              @id @default(cuid())
  drawerId              String
  userId                String
  businessDate          DateTime            @db.Date
  openingBalance        Decimal             @db.Decimal(10, 2)
  expectedClosingBalance Decimal?           @db.Decimal(10, 2)
  actualClosingBalance   Decimal?           @db.Decimal(10, 2)
  discrepancy           Decimal?            @db.Decimal(10, 2)
  openedAt              DateTime            @default(now())
  closedAt              DateTime?
  status                DrawerSessionStatus @default(OPEN)
  notes                 String?
  transactions          Transaction[]
  drawer                CashDrawer          @relation(fields: [drawerId], references: [id])
  user                  User                @relation(fields: [userId], references: [id])
}
model CashReconciliation {
  id                String               @id @default(cuid())
  businessDate      DateTime             @db.Date
  userId            String
  openingBalance    Decimal              @db.Decimal(10, 2)
  expectedAmount    Decimal              @db.Decimal(10, 2)
  actualAmount      Decimal              @db.Decimal(10, 2)
  discrepancy       Decimal              @db.Decimal(10, 2)
  notes             String?
  status            ReconciliationStatus @default(PENDING)
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  user              User                 @relation(fields: [userId], references: [id])
}

model DrawerOverride {
enum ReconciliationStatus {
  PENDING
  COMPLETED
  APPROVED
  REJECTED
}

enum POStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  ORDERED
  RECEIVED
  CANCELLED
}

enum ReturnStatus {
  PENDING
  APPROVED
  COMPLETED
  REJECTED
}

enum NotificationType {
  SYSTEM
  MESSAGE
  ALERT
  INFO
}

enum DiscountType {
  FIXED
  PERCENTAGE
}

enum AdjustmentReason {
  INVENTORY_COUNT
  DAMAGED
  EXPIRED
  THEFT
  LOSS
  RETURN
  CORRECTION
  OTHER
}

enum StockChangeSource {
  PURCHASE
  SALE
  ADJUSTMENT
  TRANSFER
  RETURN
  INITIAL
  OTHER
}

enum StockLocationType {
  STORE
  WAREHOUSE
}

enum TransferStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
  CANCELLED
}

enum OverrideStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
}

  startDate   DateTime
  endDate     DateTime
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdBy   String
  user        User        @relation(fields: [createdBy], references: [id])
}