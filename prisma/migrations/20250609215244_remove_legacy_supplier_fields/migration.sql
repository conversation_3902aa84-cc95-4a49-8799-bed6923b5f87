-- Migration: Remove legacy supplier fields from Product model
-- This migration removes the old direct supplier relationship fields
-- after data has been migrated to the ProductSupplier junction table.

-- Remove foreign key constraint first
ALTER TABLE "Product" DROP CONSTRAINT IF EXISTS "Product_supplierId_fkey";

-- Remove the supplier fields
ALTER TABLE "Product" DROP COLUMN IF EXISTS "supplierId";

-- Note: purchasePrice field is kept for backward compatibility
-- It can be used as a fallback when no ProductSupplier relationship exists
-- ALTER TABLE "Product" DROP COLUMN IF EXISTS "purchasePrice";

-- Add comment to track migration
COMMENT ON TABLE "Product" IS 'Updated: Removed legacy supplierId field, now uses ProductSupplier junction table';
