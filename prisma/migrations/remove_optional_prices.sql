-- Migration to remove optionalPrice1 and optionalPrice2 columns from Product table
-- Run this manually if needed: psql -d npos -f prisma/migrations/remove_optional_prices.sql

-- Remove optionalPrice1 and optionalPrice2 columns from Product table
ALTER TABLE "Product" DROP COLUMN IF EXISTS "optionalPrice1";
ALTER TABLE "Product" DROP COLUMN IF EXISTS "optionalPrice2";

-- Verify the columns are removed
-- SELECT column_name FROM information_schema.columns WHERE table_name = 'Product' AND column_name IN ('optionalPrice1', 'optionalPrice2');
