-- CreateEnum
CREATE TYPE "ScheduledReportType" AS ENUM ('weekly', 'monthly', 'quarterly');

-- CreateEnum
CREATE TYPE "ScheduledReportCategory" AS ENUM ('performance', 'cost', 'quality', 'relationship');

-- CreateTable
CREATE TABLE "scheduled_reports" (
    "id" TEXT NOT NULL,
    "type" "ScheduledReportType" NOT NULL,
    "reportCategory" "ScheduledReportCategory" NOT NULL,
    "recipients" JSONB NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastGenerated" TIMESTAMP(3),
    "nextScheduled" TIMESTAMP(3) NOT NULL,
    "parameters" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "scheduled_reports_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "scheduled_reports_nextScheduled_idx" ON "scheduled_reports"("nextScheduled");

-- CreateIndex
CREATE INDEX "scheduled_reports_isActive_idx" ON "scheduled_reports"("isActive");

-- CreateIndex
CREATE INDEX "scheduled_reports_type_idx" ON "scheduled_reports"("type");

-- CreateIndex
CREATE INDEX "scheduled_reports_reportCategory_idx" ON "scheduled_reports"("reportCategory");
