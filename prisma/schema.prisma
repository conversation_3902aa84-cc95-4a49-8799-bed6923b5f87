generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                          String                   @id @default(cuid())
  name                        String
  email                       String                   @unique
  password                    String?
  role                        UserRole                 @default(CASHIER)
  createdAt                   DateTime                 @default(now())
  updatedAt                   DateTime                 @updatedAt
  active                      Boolean                  @default(true)
  activityLogs                ActivityLog[]
  resolvedAuditAlerts         CashAuditAlert[]
  cashDrawer                  CashDrawer?              @relation("UserDrawer")
  investigatedReconciliations CashReconciliation[]     @relation("CashReconciliationInvestigator")
  cashReconciliations         CashReconciliation[]
  drawerSessions              DrawerSession[]
  receivedMessages            Message[]                @relation("ReceivedMessages")
  sentMessages                Message[]                @relation("SentMessages")
  receivedNotifications       Notification[]           @relation("UserNotifications")
  notificationPreferences     NotificationPreference[]
  poStatusHistoryEntries      POStatusHistory[]
  participantInConversations  Participant[]
  approvedPOs                 PurchaseOrder[]          @relation("ApprovedPurchaseOrders")
  cancelledPOs                PurchaseOrder[]          @relation("CancelledPurchaseOrders")
  purchaseOrders              PurchaseOrder[]          @relation("CreatedPurchaseOrders")
  receivedPurchaseOrders      PurchaseOrderReceiving[]
  createdPOTemplates          PurchaseOrderTemplate[]  @relation("CreatedPOTemplates")
  processedReturnResolutions  Return[]                 @relation("ReturnResolutionProcessor")
  revenueTargets              RevenueTarget[]
  sessions                    Session[]
  approvedSimpleTransfers     SimpleStockTransfer[]    @relation("ApprovedSimpleTransfers")
  requestedSimpleTransfers    SimpleStockTransfer[]    @relation("RequestedSimpleTransfers")
  starredConversations        StarredConversation[]
  approvedAdjustments         StockAdjustment[]        @relation("AdjustmentApprover")
  stockAdjustments            StockAdjustment[]
  stockHistory                StockHistory[]
  temporaryPrices             TemporaryPrice[]
  approvals                   Transaction[]            @relation("ApproverTransactions")
  transactions                Transaction[]            @relation("CashierTransactions")
  createdInvoices             Invoice[]                @relation("CreatedInvoices")
  approvedInvoices            Invoice[]                @relation("ApprovedInvoices")
  createdInvoicePayments      InvoicePayment[]         @relation("CreatedInvoicePayments")
  reportedQualityIssues       QualityIssue[]           @relation("ReportedQualityIssues")
  resolvedQualityIssues       QualityIssue[]           @relation("ResolvedQualityIssues")
  escalatedQualityIssues      QualityIssue[]           @relation("EscalatedQualityIssues")
  escalationTargets           QualityEscalation[]      @relation("EscalationTarget")
  escalationInitiations       QualityEscalation[]      @relation("EscalationInitiator")
  escalationResponses         QualityEscalation[]      @relation("EscalationResponder")
  assignedQualityImprovements SupplierQualityImprovement[] @relation("AssignedQualityImprovements")
  createdQualityImprovements  SupplierQualityImprovement[] @relation("CreatedQualityImprovements")
  approvedQualityImprovements SupplierQualityImprovement[] @relation("ApprovedQualityImprovements")
  assignedQualityActions      QualityImprovementAction[] @relation("AssignedQualityActions")
  acknowledgedQualityAlerts   QualityAlert[]           @relation("AcknowledgedQualityAlerts")
  resolvedQualityAlerts       QualityAlert[]           @relation("ResolvedQualityAlerts")
  dismissedQualityAlerts      QualityAlert[]           @relation("DismissedQualityAlerts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model ActivityLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?
  timestamp DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Product {
  id                         String                      @id @default(cuid())
  name                       String
  description                String?
  sku                        String                      @unique
  barcode                    String?                     @unique
  categoryId                 String?
  unitId                     String
  basePrice                  Decimal                     @db.Decimal(10, 2)
  imageUrl                   String?
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @updatedAt
  active                     Boolean                     @default(true)
  discountType               DiscountType?
  discountValue              Decimal?                    @db.Decimal(10, 2)
  expiryDate                 DateTime?
  purchasePrice              Decimal?                    @db.Decimal(10, 2)
  category                   Category?                   @relation(fields: [categoryId], references: [id])
  unit                       Unit                        @relation(fields: [unitId], references: [id])
  productSuppliers           ProductSupplier[]
  purchaseOrderItems         PurchaseOrderItem[]
  purchaseOrderTemplateItems PurchaseOrderTemplateItem[]
  returnItems                ReturnItem[]
  simpleStockTransfers       SimpleStockTransfer[]
  stockAdjustments           StockAdjustment[]
  stockBatches               StockBatch[]
  stockHistory               StockHistory[]
  storeStock                 StoreStock?
  supplierReturnItems        SupplierReturnItem[]
  temporaryPrice             TemporaryPrice?
  transactionItems           TransactionItem[]
  warehouseStock             WarehouseStock?
  invoiceItems               InvoiceItem[]
  qualityIssues              QualityIssue[]
  qualityAlerts              QualityAlert[]
}

model ProductSupplier {
  id                         String                      @id @default(cuid())
  productId                  String
  supplierId                 String
  supplierProductCode        String?
  supplierProductName        String?
  purchasePrice              Decimal                     @db.Decimal(10, 2)
  minimumOrderQuantity       Decimal?                    @db.Decimal(10, 2)
  leadTimeDays               Int?
  isPreferred                Boolean                     @default(false)
  isActive                   Boolean                     @default(true)
  lastOrderDate              DateTime?
  lastPurchasePrice          Decimal?                    @db.Decimal(10, 2)
  notes                      String?
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @updatedAt
  product                    Product                     @relation(fields: [productId], references: [id], onDelete: Cascade)
  supplier                   Supplier                    @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  purchaseOrderItems         PurchaseOrderItem[]
  purchaseOrderTemplateItems PurchaseOrderTemplateItem[]
  stockBatches               StockBatch[]
  stockHistory               StockHistory[]

  @@unique([productId, supplierId])
  @@index([productId])
  @@index([supplierId])
  @@index([isPreferred])
  @@index([isActive])
}

model StockBatch {
  id                String            @id @default(cuid())
  productId         String
  productSupplierId String
  batchNumber       String?
  receivedDate      DateTime          @default(now())
  expiryDate        DateTime?
  quantity          Decimal           @db.Decimal(10, 2)
  remainingQuantity Decimal           @db.Decimal(10, 2)
  purchasePrice     Decimal           @db.Decimal(10, 2)
  purchaseOrderId   String?
  warehouseStockId  String?
  storeStockId      String?
  status            BatchStatus       @default(ACTIVE)
  notes             String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  stockAdjustments  StockAdjustment[]
  product           Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
  productSupplier   ProductSupplier   @relation(fields: [productSupplierId], references: [id], onDelete: Cascade)
  purchaseOrder     PurchaseOrder?    @relation(fields: [purchaseOrderId], references: [id])
  storeStock        StoreStock?       @relation(fields: [storeStockId], references: [id])
  warehouseStock    WarehouseStock?   @relation(fields: [warehouseStockId], references: [id])
  stockHistory      StockHistory[]
  transactionItems  TransactionItem[]
  returnItems       ReturnItem[]      @relation("ReturnItemBatch")
  qualityIssues     QualityIssue[]    @relation("QualityIssueBatch")
  qualityAlerts     QualityAlert[]    @relation("QualityAlertBatch")

  @@index([productId])
  @@index([productSupplierId])
  @@index([status])
  @@index([expiryDate])
  @@index([receivedDate])
}

model Category {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  products    Product[]
}

model Unit {
  id           String    @id @default(cuid())
  name         String    @unique
  abbreviation String
  description  String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  products     Product[]
}

model StoreStock {
  id               String            @id @default(cuid())
  productId        String            @unique
  quantity         Decimal           @db.Decimal(10, 2)
  minThreshold     Decimal           @db.Decimal(10, 2)
  lastUpdated      DateTime          @default(now())
  maxThreshold     Decimal?          @db.Decimal(10, 2)
  stockAdjustments StockAdjustment[]
  stockBatches     StockBatch[]
  stockHistory     StockHistory[]
  product          Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model WarehouseStock {
  id               String            @id @default(cuid())
  productId        String            @unique
  quantity         Decimal           @db.Decimal(10, 2)
  lastUpdated      DateTime          @default(now())
  maxThreshold     Decimal?          @db.Decimal(10, 2)
  minThreshold     Decimal           @default(0) @db.Decimal(10, 2)
  stockAdjustments StockAdjustment[]
  stockBatches     StockBatch[]
  stockHistory     StockHistory[]
  product          Product           @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model StockAdjustment {
  id                 String           @id @default(cuid())
  date               DateTime         @default(now())
  productId          String
  batchId            String?
  storeStockId       String?
  warehouseStockId   String?
  previousQuantity   Decimal          @db.Decimal(10, 2)
  newQuantity        Decimal          @db.Decimal(10, 2)
  adjustmentQuantity Decimal          @db.Decimal(10, 2)
  reason             AdjustmentReason
  notes              String?
  userId             String
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  approvedAt         DateTime?
  approvedById       String?
  rejectedAt         DateTime?
  rejectionReason    String?
  status             AdjustmentStatus @default(PENDING_APPROVAL)
  approvedBy         User?            @relation("AdjustmentApprover", fields: [approvedById], references: [id])
  stockBatch         StockBatch?      @relation(fields: [batchId], references: [id])
  product            Product          @relation(fields: [productId], references: [id])
  storeStock         StoreStock?      @relation(fields: [storeStockId], references: [id])
  user               User             @relation(fields: [userId], references: [id])
  warehouseStock     WarehouseStock?  @relation(fields: [warehouseStockId], references: [id])
}

model StockHistory {
  id                String            @id @default(cuid())
  date              DateTime          @default(now())
  productId         String
  productSupplierId String?
  batchId           String?
  storeStockId      String?
  warehouseStockId  String?
  previousQuantity  Decimal           @db.Decimal(10, 2)
  newQuantity       Decimal           @db.Decimal(10, 2)
  changeQuantity    Decimal           @db.Decimal(10, 2)
  source            StockChangeSource
  referenceId       String?
  referenceType     String?
  notes             String?
  userId            String
  createdAt         DateTime          @default(now())
  stockBatch        StockBatch?       @relation(fields: [batchId], references: [id])
  product           Product           @relation(fields: [productId], references: [id])
  productSupplier   ProductSupplier?  @relation(fields: [productSupplierId], references: [id])
  storeStock        StoreStock?       @relation(fields: [storeStockId], references: [id])
  user              User              @relation(fields: [userId], references: [id])
  warehouseStock    WarehouseStock?   @relation(fields: [warehouseStockId], references: [id])
}

model SimpleStockTransfer {
  id            String         @id @default(cuid())
  date          DateTime       @default(now())
  productId     String
  quantity      Decimal        @db.Decimal(10, 2)
  fromStore     Boolean
  toStore       Boolean
  status        TransferStatus @default(PENDING)
  notes         String?
  requestedById String
  approvedById  String?
  approvedAt    DateTime?
  completedAt   DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  approvedBy    User?          @relation("ApprovedSimpleTransfers", fields: [approvedById], references: [id])
  product       Product        @relation(fields: [productId], references: [id])
  requestedBy   User           @relation("RequestedSimpleTransfers", fields: [requestedById], references: [id])
}

model Transaction {
  id              String            @id @default(cuid())
  transactionDate DateTime          @default(now())
  cashierId       String
  customerId      String?
  subtotal        Decimal           @db.Decimal(10, 2)
  discount        Decimal           @default(0) @db.Decimal(10, 2)
  tax             Decimal           @default(0) @db.Decimal(10, 2)
  total           Decimal           @db.Decimal(10, 2)
  paymentMethod   PaymentMethod
  paymentStatus   PaymentStatus     @default(PENDING)
  cashReceived    Decimal?          @db.Decimal(10, 2)
  changeAmount    Decimal?          @db.Decimal(10, 2)
  dueDate         DateTime?
  approverId      String?
  approvedAt      DateTime?
  status          TransactionStatus @default(PENDING)
  notes           String?
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt
  drawerSessionId String?
  terminalId      String?
  returns         Return[]
  approver        User?             @relation("ApproverTransactions", fields: [approverId], references: [id])
  cashier         User              @relation("CashierTransactions", fields: [cashierId], references: [id])
  customer        Customer?         @relation(fields: [customerId], references: [id])
  drawerSession   DrawerSession?    @relation(fields: [drawerSessionId], references: [id])
  terminal        Terminal?         @relation(fields: [terminalId], references: [id])
  items           TransactionItem[]
}

model TransactionItem {
  id            String      @id @default(cuid())
  transactionId String
  productId     String
  batchId       String?
  quantity      Decimal     @db.Decimal(10, 2)
  unitPrice     Decimal     @db.Decimal(10, 2)
  discount      Decimal     @default(0) @db.Decimal(10, 2)
  subtotal      Decimal     @db.Decimal(10, 2)
  stockBatch    StockBatch? @relation(fields: [batchId], references: [id])
  product       Product     @relation(fields: [productId], references: [id])
  transaction   Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
}

model Customer {
  id           String        @id @default(cuid())
  name         String
  phone        String?
  email        String?
  address      String?
  customerType CustomerType  @default(REGULAR)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  returns      Return[]
  transactions Transaction[]
}

model PurchaseOrder {
  id                   String                   @id @default(cuid())
  orderDate            DateTime                 @default(now())
  createdById          String
  supplierId           String
  subtotal             Decimal                  @db.Decimal(10, 2)
  tax                  Decimal                  @default(0) @db.Decimal(10, 2)
  taxPercentage        Decimal?                 @db.Decimal(5, 2)
  total                Decimal                  @db.Decimal(10, 2)
  status               POStatus                 @default(DRAFT)
  approvedById         String?
  approvedAt           DateTime?
  orderedAt            DateTime?
  shippedAt            DateTime?
  expectedDeliveryDate DateTime?
  receivedAt           DateTime?
  cancelledAt          DateTime?
  cancelledById        String?
  cancelReason         String?
  priority             POPriority               @default(NORMAL)
  notes                String?
  approvalNotes        String?
  createdAt            DateTime                 @default(now())
  updatedAt            DateTime                 @updatedAt
  lastTransitionAt     DateTime?
  timeInCurrentStatus  Int?
  hasDelays            Boolean                  @default(false)
  delayReason          String?
  performanceScore     Float?
  qualityScore         Float?
  supplierScore        Float?
  totalBatches         Int                      @default(0)
  activeBatches        Int                      @default(0)
  expiringBatches      Int                      @default(0)
  expiredBatches       Int                      @default(0)
  notifications        Notification[]           @relation("PurchaseOrderNotifications")
  statusHistory        POStatusHistory[]
  approvedBy           User?                    @relation("ApprovedPurchaseOrders", fields: [approvedById], references: [id])
  cancelledBy          User?                    @relation("CancelledPurchaseOrders", fields: [cancelledById], references: [id])
  createdBy            User                     @relation("CreatedPurchaseOrders", fields: [createdById], references: [id])
  supplier             Supplier                 @relation(fields: [supplierId], references: [id])
  items                PurchaseOrderItem[]
  receivings           PurchaseOrderReceiving[]
  stockBatches         StockBatch[]
  supplierReturns      SupplierReturn[]
  invoices             Invoice[]

  @@index([status, lastTransitionAt])
  @@index([hasDelays])
  @@index([performanceScore])
  @@index([supplierScore])
}

model PurchaseOrderItem {
  id                  String           @id @default(cuid())
  purchaseOrderId     String
  productId           String
  productSupplierId   String?
  supplierProductCode String?
  quantity            Decimal          @db.Decimal(10, 2)
  receivedQuantity    Decimal          @default(0) @db.Decimal(10, 2)
  unitPrice           Decimal          @db.Decimal(10, 2)
  subtotal            Decimal          @db.Decimal(10, 2)
  product             Product          @relation(fields: [productId], references: [id])
  productSupplier     ProductSupplier? @relation(fields: [productSupplierId], references: [id])
  purchaseOrder       PurchaseOrder    @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  invoiceItems        InvoiceItem[]

  @@index([productSupplierId])
}

model PurchaseOrderReceiving {
  id                String                       @id @default(cuid())
  purchaseOrderId   String
  receivedById      String
  receivedAt        DateTime                     @default(now())
  notes             String?
  discrepancyReason String?
  createdAt         DateTime                     @default(now())
  purchaseOrder     PurchaseOrder                @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  receivedBy        User                         @relation(fields: [receivedById], references: [id])
  items             PurchaseOrderReceivingItem[]
}

model PurchaseOrderReceivingItem {
  id                       String                 @id @default(cuid())
  purchaseOrderReceivingId String
  purchaseOrderItemId      String
  receivedQuantity         Decimal                @db.Decimal(10, 2)
  discrepancyQuantity      Decimal                @default(0) @db.Decimal(10, 2)
  discrepancyReason        String?
  notes                    String?
  receiving                PurchaseOrderReceiving @relation(fields: [purchaseOrderReceivingId], references: [id], onDelete: Cascade)
}

model PurchaseOrderTemplate {
  id            String                      @id @default(cuid())
  name          String
  description   String?
  supplierId    String
  taxPercentage Decimal?                    @db.Decimal(5, 2)
  notes         String?
  isActive      Boolean                     @default(true)
  createdById   String
  createdAt     DateTime                    @default(now())
  updatedAt     DateTime                    @updatedAt
  createdBy     User                        @relation("CreatedPOTemplates", fields: [createdById], references: [id])
  supplier      Supplier                    @relation(fields: [supplierId], references: [id])
  items         PurchaseOrderTemplateItem[]
}

model PurchaseOrderTemplateItem {
  id                      String                @id @default(cuid())
  purchaseOrderTemplateId String
  productId               String
  quantity                Decimal               @db.Decimal(10, 2)
  unitPrice               Decimal               @db.Decimal(10, 2)
  productSupplierId       String?
  product                 Product               @relation(fields: [productId], references: [id])
  productSupplier         ProductSupplier?      @relation(fields: [productSupplierId], references: [id])
  template                PurchaseOrderTemplate @relation(fields: [purchaseOrderTemplateId], references: [id], onDelete: Cascade)
}

model Supplier {
  id                     String                  @id @default(cuid())
  name                   String
  contactPerson          String?
  phone                  String?
  email                  String?
  address                String?
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @updatedAt
  isActive               Boolean                 @default(true)
  productSuppliers       ProductSupplier[]
  purchaseOrders         PurchaseOrder[]
  purchaseOrderTemplates PurchaseOrderTemplate[]
  returns                SupplierReturn[]
  invoices               Invoice[]
  qualityIssues          QualityIssue[]
  qualityImprovements    SupplierQualityImprovement[]
  qualityAlerts          QualityAlert[]
}

model ScheduledReport {
  id             String                @id @default(cuid())
  type           ScheduledReportType
  reportCategory ScheduledReportCategory
  recipients     Json
  isActive       Boolean               @default(true)
  lastGenerated  DateTime?
  nextScheduled  DateTime
  parameters     Json?
  createdAt      DateTime              @default(now())
  updatedAt      DateTime              @updatedAt

  @@index([nextScheduled])
  @@index([isActive])
  @@index([type])
  @@index([reportCategory])
  @@map("scheduled_reports")
}

model Return {
  id                            String              @id @default(cuid())
  returnDate                    DateTime            @default(now())
  transactionId                 String
  customerId                    String?
  reason                        String
  total                         Decimal             @db.Decimal(10, 2)
  status                        ReturnStatus        @default(PENDING)
  disposition                   ReturnDisposition?
  dispositionReason             String?
  addToSupplierQueue            Boolean             @default(false)
  supplierReturnQueueId         String?
  customerResolution            CustomerResolution?
  customerResolutionNotes       String?
  customerResolutionProcessedAt DateTime?
  customerResolutionProcessedBy String?
  awaitingRestock               Boolean             @default(false)
  notes                         String?
  createdAt                     DateTime            @default(now())
  updatedAt                     DateTime            @updatedAt
  customer                      Customer?           @relation(fields: [customerId], references: [id])
  customerResolutionProcessor   User?               @relation("ReturnResolutionProcessor", fields: [customerResolutionProcessedBy], references: [id])
  supplierReturnQueue           SupplierReturn?     @relation(fields: [supplierReturnQueueId], references: [id])
  transaction                   Transaction         @relation(fields: [transactionId], references: [id])
  items                         ReturnItem[]
}

model ReturnItem {
  id        String      @id @default(cuid())
  returnId  String
  productId String
  batchId   String?     // NEW: Track which batch the returned item came from
  quantity  Decimal     @db.Decimal(10, 2)
  unitPrice Decimal     @db.Decimal(10, 2)
  subtotal  Decimal     @db.Decimal(10, 2)
  defectType String?    // NEW: Type of defect if applicable
  defectNotes String?   // NEW: Additional notes about the defect
  product   Product     @relation(fields: [productId], references: [id])
  return    Return      @relation(fields: [returnId], references: [id], onDelete: Cascade)
  batch     StockBatch? @relation("ReturnItemBatch", fields: [batchId], references: [id])
  qualityIssues QualityIssue[] // NEW: Related quality issues
}

model SupplierReturn {
  id              String               @id @default(cuid())
  returnDate      DateTime             @default(now())
  purchaseOrderId String?
  supplierId      String
  reason          String
  total           Decimal              @db.Decimal(10, 2)
  status          ReturnStatus         @default(PENDING)
  notes           String?
  createdAt       DateTime             @default(now())
  updatedAt       DateTime             @updatedAt
  customerReturns Return[]
  purchaseOrder   PurchaseOrder?       @relation(fields: [purchaseOrderId], references: [id])
  supplier        Supplier             @relation(fields: [supplierId], references: [id])
  items           SupplierReturnItem[]
}

model SupplierReturnItem {
  id               String         @id @default(cuid())
  supplierReturnId String
  productId        String
  quantity         Decimal        @db.Decimal(10, 2)
  unitPrice        Decimal        @db.Decimal(10, 2)
  subtotal         Decimal        @db.Decimal(10, 2)
  product          Product        @relation(fields: [productId], references: [id])
  supplierReturn   SupplierReturn @relation(fields: [supplierReturnId], references: [id], onDelete: Cascade)
}

model Invoice {
  id                    String                @id @default(cuid())
  invoiceNumber         String                @unique
  purchaseOrderId       String?
  supplierId            String
  invoiceDate           DateTime
  dueDate               DateTime?
  subtotal              Decimal               @db.Decimal(10, 2)
  tax                   Decimal               @default(0) @db.Decimal(10, 2)
  taxPercentage         Decimal?              @db.Decimal(5, 2)
  total                 Decimal               @db.Decimal(10, 2)
  status                InvoiceStatus         @default(PENDING)
  paymentStatus         InvoicePaymentStatus  @default(UNPAID)
  paymentMethod         String?
  paymentReference      String?
  paidAmount            Decimal               @default(0) @db.Decimal(10, 2)
  paidAt                DateTime?
  notes                 String?
  attachmentUrl         String?
  hasInstallments       Boolean               @default(false)
  numberOfInstallments  Int?
  createdById           String
  approvedById          String?
  approvedAt            DateTime?
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt

  // Relations
  purchaseOrder         PurchaseOrder?        @relation(fields: [purchaseOrderId], references: [id])
  supplier              Supplier              @relation(fields: [supplierId], references: [id])
  createdBy             User                  @relation("CreatedInvoices", fields: [createdById], references: [id])
  approvedBy            User?                 @relation("ApprovedInvoices", fields: [approvedById], references: [id])
  items                 InvoiceItem[]
  payments              InvoicePayment[]
  installments          InvoiceInstallment[]

  @@index([purchaseOrderId])
  @@index([supplierId])
  @@index([invoiceDate])
  @@index([status])
  @@index([paymentStatus])
}

model InvoiceItem {
  id                    String                @id @default(cuid())
  invoiceId             String
  purchaseOrderItemId   String?
  productId             String
  description           String
  quantity              Decimal               @db.Decimal(10, 2)
  unitPrice             Decimal               @db.Decimal(10, 2)
  subtotal              Decimal               @db.Decimal(10, 2)

  // Relations
  invoice               Invoice               @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  purchaseOrderItem     PurchaseOrderItem?    @relation(fields: [purchaseOrderItemId], references: [id])
  product               Product               @relation(fields: [productId], references: [id])

  @@index([invoiceId])
  @@index([productId])
}

model InvoicePayment {
  id                    String                @id @default(cuid())
  invoiceId             String
  amount                Decimal               @db.Decimal(10, 2)
  paymentDate           DateTime              @default(now())
  paymentMethod         String
  paymentReference      String?
  notes                 String?
  proofImageUrl         String?               // Payment proof image URL
  createdById           String
  createdAt             DateTime              @default(now())

  // Relations
  invoice               Invoice               @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  createdBy             User                  @relation("CreatedInvoicePayments", fields: [createdById], references: [id])

  @@index([invoiceId])
  @@index([paymentDate])
}

model InvoiceInstallment {
  id                    String                @id @default(cuid())
  invoiceId             String
  installmentNumber     Int
  dueDate               DateTime
  amount                Decimal               @db.Decimal(10, 2)
  description           String?
  status                InstallmentStatus     @default(PENDING)
  paidAmount            Decimal               @default(0) @db.Decimal(10, 2)
  paidAt                DateTime?
  createdAt             DateTime              @default(now())
  updatedAt             DateTime              @updatedAt

  // Relations
  invoice               Invoice               @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@unique([invoiceId, installmentNumber])
  @@index([invoiceId])
  @@index([dueDate])
  @@index([status])
}

model Notification {
  id              String               @id @default(cuid())
  userId          String
  title           String
  message         String
  type            NotificationType     @default(SYSTEM)
  isRead          Boolean              @default(false)
  createdAt       DateTime             @default(now())
  purchaseOrderId String?
  actionUrl       String?
  metadata        Json?
  deliveryMethods Json?
  eventId         String?
  eventType       String?
  expiresAt       DateTime?
  priority        NotificationPriority @default(NORMAL)
  purchaseOrder   PurchaseOrder?       @relation("PurchaseOrderNotifications", fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  user            User                 @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)
}

model NotificationPreference {
  id              String                       @id @default(cuid())
  userId          String
  eventType       String
  enabled         Boolean                      @default(true)
  deliveryMethods NotificationDeliveryMethod[]
  frequency       NotificationFrequency        @default(IMMEDIATE)
  quietHours      Json?
  createdAt       DateTime                     @default(now())
  updatedAt       DateTime                     @updatedAt
  user            User                         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, eventType])
}

model NotificationEvent {
  id          String    @id @default(cuid())
  eventType   String
  eventId     String
  sourceId    String?
  sourceType  String?
  payload     Json
  processed   Boolean   @default(false)
  createdAt   DateTime  @default(now())
  processedAt DateTime?

  @@index([eventType])
  @@index([processed])
  @@index([createdAt])
}

model SystemSetting {
  id          String   @id @default(cuid())
  key         String   @unique
  value       String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model StoreInfo {
  id        String   @id @default("default-store")
  storeName String
  phone     String?
  address   String?
  email     String?
  website   String?
  taxId     String?
  logoUrl   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Conversation {
  id           String                @id @default(cuid())
  title        String?
  createdAt    DateTime              @default(now())
  updatedAt    DateTime              @updatedAt
  lastActivity DateTime              @default(now())
  messages     Message[]
  participants Participant[]
  starredBy    StarredConversation[]
}

model StarredConversation {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  starredAt      DateTime     @default(now())
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Participant {
  id             String       @id @default(cuid())
  userId         String
  conversationId String
  joinedAt       DateTime     @default(now())
  leftAt         DateTime?
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, conversationId])
}

model Message {
  id             String       @id @default(cuid())
  conversationId String
  senderId       String
  receiverId     String?
  content        String
  isRead         Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  receiver       User?        @relation("ReceivedMessages", fields: [receiverId], references: [id])
  sender         User         @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
}

model CashDrawer {
  id             String          @id @default(cuid())
  name           String
  location       String?
  isActive       Boolean         @default(true)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  userId         String?         @unique
  user           User?           @relation("UserDrawer", fields: [userId], references: [id])
  drawerSessions DrawerSession[]
  terminal       Terminal?       @relation("DrawerTerminal")
}

model Terminal {
  id             String          @id @default(cuid())
  name           String
  ipAddress      String?
  macAddress     String?
  location       String?
  description    String?
  isActive       Boolean         @default(true)
  drawerId       String?         @unique
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  drawerSessions DrawerSession[]
  drawer         CashDrawer?     @relation("DrawerTerminal", fields: [drawerId], references: [id])
  transactions   Transaction[]
}

model DrawerSession {
  id                     String              @id @default(cuid())
  drawerId               String
  userId                 String
  terminalId             String
  businessDate           DateTime            @db.Date
  openingBalance         Decimal             @db.Decimal(10, 2)
  expectedClosingBalance Decimal?            @db.Decimal(10, 2)
  actualClosingBalance   Decimal?            @db.Decimal(10, 2)
  discrepancy            Decimal?            @db.Decimal(10, 2)
  openedAt               DateTime            @default(now())
  closedAt               DateTime?
  status                 DrawerSessionStatus @default(OPEN)
  notes                  String?
  shiftNumber            Int?
  previousSessionId      String?             @unique
  drawer                 CashDrawer          @relation(fields: [drawerId], references: [id])
  previousSession        DrawerSession?      @relation("SessionChain", fields: [previousSessionId], references: [id])
  nextSession            DrawerSession?      @relation("SessionChain")
  terminal               Terminal            @relation(fields: [terminalId], references: [id])
  user                   User                @relation(fields: [userId], references: [id])
  transactions           Transaction[]
}

model CashReconciliation {
  id                  String               @id @default(cuid())
  businessDate        DateTime             @db.Date
  userId              String
  openingBalance      Decimal              @db.Decimal(10, 2)
  expectedAmount      Decimal              @db.Decimal(10, 2)
  actualAmount        Decimal              @db.Decimal(10, 2)
  discrepancy         Decimal              @db.Decimal(10, 2)
  notes               String?
  status              ReconciliationStatus @default(PENDING)
  discrepancyCategory DiscrepancyCategory?
  resolutionStatus    ResolutionStatus     @default(PENDING)
  resolutionNotes     String?
  investigatedBy      String?
  resolvedAt          DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  auditAlerts         CashAuditAlert[]
  investigator        User?                @relation("CashReconciliationInvestigator", fields: [investigatedBy], references: [id])
  user                User                 @relation(fields: [userId], references: [id])
}

model CashAuditAlert {
  id                   String             @id @default(cuid())
  cashReconciliationId String
  alertType            AuditAlertType
  severity             AlertSeverity      @default(MEDIUM)
  message              String
  threshold            Decimal?           @db.Decimal(10, 2)
  actualValue          Decimal?           @db.Decimal(10, 2)
  isResolved           Boolean            @default(false)
  resolvedBy           String?
  resolvedAt           DateTime?
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @updatedAt
  cashReconciliation   CashReconciliation @relation(fields: [cashReconciliationId], references: [id], onDelete: Cascade)
  resolver             User?              @relation(fields: [resolvedBy], references: [id])
}

model TemporaryPrice {
  id        String       @id @default(cuid())
  productId String       @unique
  value     Decimal      @db.Decimal(10, 2)
  type      DiscountType
  startDate DateTime
  endDate   DateTime
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  createdBy String
  user      User         @relation(fields: [createdBy], references: [id])
  product   Product      @relation(fields: [productId], references: [id], onDelete: Cascade)
}

model RevenueTarget {
  id          String            @id @default(cuid())
  name        String
  description String?
  targetType  RevenueTargetType @default(MONTHLY)
  startDate   DateTime          @db.Date
  endDate     DateTime          @db.Date
  amount      Decimal           @db.Decimal(12, 2)
  isActive    Boolean           @default(true)
  createdBy   String
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  user        User              @relation(fields: [createdBy], references: [id])

  @@unique([startDate, endDate, targetType])
}

model POStatusHistory {
  id              String               @id @default(cuid())
  purchaseOrderId String
  fromStatus      POStatus
  toStatus        POStatus
  reason          POStatusChangeReason
  notes           String?
  metadata        Json?
  performanceData Json?
  createdAt       DateTime             @default(now())
  createdById     String
  createdBy       User                 @relation(fields: [createdById], references: [id])
  purchaseOrder   PurchaseOrder        @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)

  @@index([purchaseOrderId])
  @@index([fromStatus])
  @@index([toStatus])
  @@index([createdAt])
}

model QualityIssue {
  id                String                @id @default(cuid())
  returnItemId      String?
  batchId           String?
  productId         String
  supplierId        String
  issueType         QualityIssueType
  severity          QualityIssueSeverity  @default(MEDIUM)
  description       String
  defectCategory    String?
  affectedQuantity  Decimal               @db.Decimal(10, 2)
  reportedBy        String
  reportedAt        DateTime              @default(now())
  status            QualityIssueStatus    @default(OPEN)
  resolutionNotes   String?
  resolvedBy        String?
  resolvedAt        DateTime?
  escalationLevel   Int                   @default(0)
  escalatedAt       DateTime?
  escalatedBy       String?
  createdAt         DateTime              @default(now())
  updatedAt         DateTime              @updatedAt

  // Relations
  returnItem        ReturnItem?           @relation(fields: [returnItemId], references: [id])
  batch             StockBatch?           @relation("QualityIssueBatch", fields: [batchId], references: [id])
  product           Product               @relation(fields: [productId], references: [id])
  supplier          Supplier              @relation(fields: [supplierId], references: [id])
  reporter          User                  @relation("ReportedQualityIssues", fields: [reportedBy], references: [id])
  resolver          User?                 @relation("ResolvedQualityIssues", fields: [resolvedBy], references: [id])
  escalator         User?                 @relation("EscalatedQualityIssues", fields: [escalatedBy], references: [id])
  escalations       QualityEscalation[]
  improvements      SupplierQualityImprovement[]

  @@index([productId])
  @@index([supplierId])
  @@index([batchId])
  @@index([status])
  @@index([reportedAt])
  @@index([severity])
}

model QualityEscalation {
  id                String                    @id @default(cuid())
  qualityIssueId    String
  escalationLevel   Int
  escalatedTo       String
  escalatedBy       String
  escalationReason  String
  escalatedAt       DateTime                  @default(now())
  status            QualityEscalationStatus   @default(PENDING)
  responseRequired  Boolean                   @default(true)
  responseDeadline  DateTime?
  responseNotes     String?
  respondedBy       String?
  respondedAt       DateTime?
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @updatedAt

  // Relations
  qualityIssue      QualityIssue              @relation(fields: [qualityIssueId], references: [id], onDelete: Cascade)
  escalatedToUser   User                      @relation("EscalationTarget", fields: [escalatedTo], references: [id])
  escalatedByUser   User                      @relation("EscalationInitiator", fields: [escalatedBy], references: [id])
  responder         User?                     @relation("EscalationResponder", fields: [respondedBy], references: [id])

  @@index([qualityIssueId])
  @@index([escalatedTo])
  @@index([status])
  @@index([escalatedAt])
}

model SupplierQualityImprovement {
  id                    String                        @id @default(cuid())
  supplierId            String
  qualityIssueId        String?
  improvementType       QualityImprovementType
  title                 String
  description           String
  targetMetric          String?                       // e.g., "return_rate", "defect_rate"
  currentValue          Decimal?                      @db.Decimal(10, 4)
  targetValue           Decimal?                      @db.Decimal(10, 4)
  startDate             DateTime
  targetCompletionDate  DateTime
  actualCompletionDate  DateTime?
  status                QualityImprovementStatus      @default(PLANNED)
  priority              QualityImprovementPriority    @default(MEDIUM)
  assignedTo            String?
  createdBy             String
  approvedBy            String?
  approvedAt            DateTime?
  progress              Int                           @default(0) // 0-100
  effectivenessScore    Decimal?                      @db.Decimal(5, 2) // 0-100
  notes                 String?
  createdAt             DateTime                      @default(now())
  updatedAt             DateTime                      @updatedAt

  // Relations
  supplier              Supplier                      @relation(fields: [supplierId], references: [id])
  qualityIssue          QualityIssue?                 @relation(fields: [qualityIssueId], references: [id])
  assignee              User?                         @relation("AssignedQualityImprovements", fields: [assignedTo], references: [id])
  creator               User                          @relation("CreatedQualityImprovements", fields: [createdBy], references: [id])
  approver              User?                         @relation("ApprovedQualityImprovements", fields: [approvedBy], references: [id])
  actions               QualityImprovementAction[]

  @@index([supplierId])
  @@index([qualityIssueId])
  @@index([status])
  @@index([startDate])
  @@index([targetCompletionDate])
}

model QualityImprovementAction {
  id                        String                      @id @default(cuid())
  qualityImprovementId      String
  actionType                QualityActionType
  title                     String
  description               String
  assignedTo                String?
  dueDate                   DateTime?
  completedAt               DateTime?
  status                    QualityActionStatus         @default(PENDING)
  notes                     String?
  createdAt                 DateTime                    @default(now())
  updatedAt                 DateTime                    @updatedAt

  // Relations
  qualityImprovement        SupplierQualityImprovement  @relation(fields: [qualityImprovementId], references: [id], onDelete: Cascade)
  assignee                  User?                       @relation("AssignedQualityActions", fields: [assignedTo], references: [id])

  @@index([qualityImprovementId])
  @@index([assignedTo])
  @@index([status])
  @@index([dueDate])
}

model QualityAlert {
  id                String              @id @default(cuid())
  alertType         String              // e.g., "RETURN_RATE_EXCEEDED", "DEFECT_RATE_EXCEEDED"
  severity          QualityAlertSeverity @default(MEDIUM)
  message           String
  supplierId        String?
  productId         String?
  batchId           String?
  threshold         Decimal?            @db.Decimal(10, 4)
  currentValue      Decimal?            @db.Decimal(10, 4)
  triggeredAt       DateTime            @default(now())
  acknowledgedAt    DateTime?
  acknowledgedBy    String?
  resolvedAt        DateTime?
  resolvedBy        String?
  dismissedAt       DateTime?
  dismissedBy       String?
  status            QualityAlertStatus  @default(ACTIVE)
  escalationLevel   Int                 @default(0)
  metadata          Json?               // Store additional alert-specific data
  ruleId            String?             // Reference to the alert rule that triggered this
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  supplier          Supplier?           @relation(fields: [supplierId], references: [id])
  product           Product?            @relation(fields: [productId], references: [id])
  batch             StockBatch?         @relation("QualityAlertBatch", fields: [batchId], references: [id])
  acknowledger      User?               @relation("AcknowledgedQualityAlerts", fields: [acknowledgedBy], references: [id])
  resolver          User?               @relation("ResolvedQualityAlerts", fields: [resolvedBy], references: [id])
  dismisser         User?               @relation("DismissedQualityAlerts", fields: [dismissedBy], references: [id])

  @@index([supplierId])
  @@index([productId])
  @@index([batchId])
  @@index([status])
  @@index([severity])
  @@index([triggeredAt])
  @@index([alertType])
}

enum UserRole {
  SUPER_ADMIN
  CASHIER
  FINANCE_ADMIN
  WAREHOUSE_ADMIN
  MARKETING
  DEVELOPER
}

enum CustomerType {
  REGULAR
  FRIEND
  FAMILY
}

enum PaymentMethod {
  CASH
  DEBIT
  QRIS
}

enum PaymentStatus {
  PENDING
  PAID
  PARTIAL
  OVERDUE
  CANCELLED
}

enum TransactionStatus {
  PENDING
  COMPLETED
  VOIDED
  RETURNED
}

enum DrawerSessionStatus {
  OPEN
  CLOSED
  RECONCILED
}

enum ReconciliationStatus {
  PENDING
  COMPLETED
  APPROVED
  REJECTED
}

enum POStatus {
  DRAFT
  PENDING_APPROVAL
  APPROVED
  ORDERED
  SHIPPED
  PARTIALLY_RECEIVED
  RECEIVED
  CANCELLED
  OVERDUE
  ON_HOLD
  EXPEDITED
}

enum ReturnStatus {
  PENDING
  APPROVED
  COMPLETED
  REJECTED
}

enum ReturnDisposition {
  RETURN_TO_STOCK
  DO_NOT_RETURN_TO_STOCK
}

enum CustomerResolution {
  REPLACEMENT
  REFUND
  PENDING_REPLACEMENT
  NONE
}

enum NotificationType {
  SYSTEM
  MESSAGE
  ALERT
  INFO
  PURCHASE_ORDER_APPROVAL
  PURCHASE_ORDER_APPROVED
  PURCHASE_ORDER_REJECTED
  PURCHASE_ORDER_RECEIVED
  INVENTORY_LOW_STOCK
  INVENTORY_OUT_OF_STOCK
  BATCH_EXPIRING
  BATCH_EXPIRED
  CASH_AUDIT_ALERT
  REVENUE_TARGET_ACHIEVED
  REVENUE_TARGET_MISSED
  SYSTEM_MAINTENANCE
  USER_ACTION_REQUIRED
  STOCK_ADJUSTMENT_PENDING
  STOCK_ADJUSTMENT_APPROVED
  STOCK_ADJUSTMENT_REJECTED
  FINANCIAL
  QUALITY_ISSUE_REPORTED
  QUALITY_ISSUE_ESCALATED
  QUALITY_THRESHOLD_BREACHED
  QUALITY_IMPROVEMENT_DUE
  SUPPLIER_QUALITY_ALERT
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum NotificationDeliveryMethod {
  IN_APP
  TOAST
  EMAIL
  SMS
  PUSH
}

enum NotificationFrequency {
  IMMEDIATE
  HOURLY
  DAILY
  WEEKLY
  NEVER
}

enum DiscountType {
  FIXED
  PERCENTAGE
}

enum AdjustmentReason {
  INVENTORY_COUNT
  DAMAGED
  EXPIRED
  THEFT
  LOSS
  RETURN
  CORRECTION
  OTHER
}

enum AdjustmentStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
  APPLIED
}

enum StockChangeSource {
  PURCHASE
  SALE
  ADJUSTMENT
  TRANSFER
  RETURN
  INITIAL
  OTHER
}

enum StockLocationType {
  STORE
  WAREHOUSE
}

enum RevenueTargetType {
  DAILY
  WEEKLY
  MONTHLY
  QUARTERLY
  YEARLY
}

enum TransferStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
  CANCELLED
}

enum DiscrepancyCategory {
  COUNTING_ERROR
  SYSTEM_ERROR
  THEFT_SUSPECTED
  CASH_SHORTAGE
  CASH_SURPLUS
  REGISTER_ERROR
  TRAINING_ERROR
  PROCEDURAL_ERROR
  UNKNOWN
  OTHER
}

enum ResolutionStatus {
  PENDING
  INVESTIGATING
  RESOLVED
  WRITTEN_OFF
  ESCALATED
  CLOSED
}

enum AuditAlertType {
  LARGE_DISCREPANCY
  FREQUENT_SHORTAGES
  PATTERN_DETECTED
  THRESHOLD_EXCEEDED
  UNUSUAL_ACTIVITY
  COMPLIANCE_VIOLATION
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum BatchStatus {
  ACTIVE
  EXPIRED
  RECALLED
  SOLD_OUT
}

enum POStatusChangeReason {
  BUSINESS_REQUIREMENT
  SUPPLIER_REQUEST
  INVENTORY_SHORTAGE
  QUALITY_ISSUE
  DELIVERY_DELAY
  PRICE_CHANGE
  MANAGEMENT_DECISION
  SYSTEM_ERROR
  CUSTOMER_REQUEST
  OPERATIONAL_CHANGE
  OTHER
}

enum POPriority {
  LOW
  NORMAL
  HIGH
  URGENT
  EXPEDITED
}

enum ScheduledReportType {
  weekly
  monthly
  quarterly
}

enum ScheduledReportCategory {
  performance
  cost
  quality
  relationship
}

enum InvoiceStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum InvoicePaymentStatus {
  UNPAID
  PARTIALLY_PAID
  PAID
  OVERDUE
}

enum InstallmentStatus {
  PENDING
  PAID
  OVERDUE
  CANCELLED
}

enum QualityIssueType {
  DEFECTIVE_PRODUCT
  PACKAGING_DAMAGE
  WRONG_SPECIFICATION
  CONTAMINATION
  EXPIRY_ISSUE
  QUANTITY_DISCREPANCY
  QUALITY_DEGRADATION
  CUSTOMER_COMPLAINT
  OTHER
}

enum QualityIssueSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum QualityIssueStatus {
  OPEN
  IN_PROGRESS
  RESOLVED
  CLOSED
  ESCALATED
}

enum QualityEscalationStatus {
  PENDING
  ACKNOWLEDGED
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum QualityImprovementType {
  PROCESS_IMPROVEMENT
  TRAINING
  EQUIPMENT_UPGRADE
  SUPPLIER_AUDIT
  QUALITY_CONTROL_ENHANCEMENT
  CORRECTIVE_ACTION
  PREVENTIVE_ACTION
  SUPPLIER_COLLABORATION
  OTHER
}

enum QualityImprovementStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum QualityImprovementPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum QualityActionType {
  SUPPLIER_MEETING
  PROCESS_REVIEW
  TRAINING_SESSION
  AUDIT
  DOCUMENTATION_UPDATE
  SYSTEM_CHANGE
  MONITORING_SETUP
  FOLLOW_UP
  OTHER
}

enum QualityActionStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum QualityAlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum QualityAlertStatus {
  ACTIVE
  ACKNOWLEDGED
  RESOLVED
  DISMISSED
  ESCALATED
}
