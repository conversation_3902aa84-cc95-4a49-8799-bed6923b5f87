import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seeding...');

  // Create a super admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Super Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'SUPER_ADMIN',
      active: true,
    },
  });
  
  console.log('Created super admin user:', superAdmin.email);

  // Create a cashier user
  const cashierPassword = await bcrypt.hash('cashier123', 10);
  
  const cashier = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Cashier User',
      email: '<EMAIL>',
      password: cashierPassword,
      role: 'CASHIER',
      active: true,
    },
  });
  
  console.log('Created cashier user:', cashier.email);

  // Create a finance admin user
  const financePassword = await bcrypt.hash('finance123', 10);
  
  const financeAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Finance Admin',
      email: '<EMAIL>',
      password: financePassword,
      role: 'FINANCE_ADMIN',
      active: true,
    },
  });
  
  console.log('Created finance admin user:', financeAdmin.email);

  // Create a developer user
  const developerPassword = await bcrypt.hash('developer123', 10);
  
  const developer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      name: 'Developer',
      email: '<EMAIL>',
      password: developerPassword,
      role: 'DEVELOPER',
      active: true,
    },
  });
  
  console.log('Created developer user:', developer.email);

  // Create a cash drawer
  //const cashDrawer = await prisma.cashDrawer.upsert({
    //where: { name: 'Main Drawer' },
    //update: {},
    //create: {
      //name: 'Main Drawer',
      //location: 'Front Counter',
      //isActive: true,
    //},
  //});
  
  //console.log('Created cash drawer:', cashDrawer.name);

  const cashDrawer = await prisma.cashDrawer.create({
  data: {
    name: 'Main Drawer',
    location: 'Front Counter',
    isActive: true,
  },
});
console.log('Created cash drawer:', cashDrawer.name);


  // Create some basic system settings
  const settings = [
    { key: 'STORE_NAME', value: 'Next POS Store', description: 'Store name displayed in receipts and UI' },
    { key: 'STORE_ADDRESS', value: '123 Main St, City, Country', description: 'Store address displayed in receipts' },
    { key: 'STORE_PHONE', value: '****** 567 8900', description: 'Store phone number' },
    { key: 'CURRENCY_SYMBOL', value: '$', description: 'Currency symbol used throughout the application' },
    { key: 'TAX_RATE', value: '7.5', description: 'Default tax rate percentage' },
    { key: 'RECEIPT_FOOTER', value: 'Thank you for shopping with us!', description: 'Message displayed at the bottom of receipts' },
    { key: 'ENABLE_CHAT', value: 'true', description: 'Enable or disable the chat feature' },
  ];

  for (const setting of settings) {
    await prisma.systemSetting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: {
        key: setting.key,
        value: setting.value,
        description: setting.description,
      },
    });
    console.log(`Created/updated setting: ${setting.key}`);
  }

  console.log('Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
