/**
 * Test script to verify the Advanced Analytics API endpoints are working
 * This tests the actual engine logic without authentication
 */

const { DemandForecastingEngine } = require('./src/lib/demand-forecasting.ts');
const { SeasonalSupplierPredictionEngine } = require('./src/lib/seasonal-supplier-predictions.ts');

async function testAnalyticsEngines() {
  console.log('🧪 Testing Advanced Analytics Engines...\n');

  try {
    // Test 1: Test Demand Forecasting Engine directly
    console.log('1. Testing Demand Forecasting Engine:');
    
    // Use a known supplier ID from our debug
    const testSupplierId = 'cmbown5h3000acj2z8v3lgmyr'; // PT. Cemerlang Indah
    
    console.log(`   🎯 Testing with supplier: ${testSupplierId}`);
    
    try {
      const demandForecast = await DemandForecastingEngine.generateSupplierDemandForecast(
        testSupplierId,
        '60days'
      );
      
      console.log('   ✅ Demand Forecasting Engine working!');
      console.log(`   📊 Supplier: ${demandForecast.supplierName}`);
      console.log(`   📦 Total products: ${demandForecast.totalProducts}`);
      console.log(`   📈 Total predicted demand: ${demandForecast.aggregateMetrics.totalPredictedDemand}`);
      console.log(`   🎯 Average confidence: ${Math.round(demandForecast.aggregateMetrics.averageConfidenceLevel)}%`);
      console.log(`   💡 Recommendations: ${demandForecast.recommendations.length}`);
      
      if (demandForecast.forecasts.length > 0) {
        const topForecast = demandForecast.forecasts[0];
        console.log(`   🔝 Top product: ${topForecast.productName} (${topForecast.predictedDemand} units predicted)`);
      }
      
    } catch (error) {
      console.log('   ❌ Demand Forecasting Engine failed:', error.message);
      console.log('   📝 Error details:', error.stack);
    }

    console.log('');

    // Test 2: Test Seasonal Predictions Engine directly
    console.log('2. Testing Seasonal Predictions Engine:');
    
    try {
      const seasonalPredictions = await SeasonalSupplierPredictionEngine.generateSeasonalPredictions(
        testSupplierId
      );
      
      console.log('   ✅ Seasonal Predictions Engine working!');
      console.log(`   📊 Supplier: ${seasonalPredictions.supplierName}`);
      console.log(`   🌍 Seasons analyzed: ${seasonalPredictions.upcomingSeasons.length}`);
      console.log(`   ⚠️  Overall risk level: ${seasonalPredictions.overallRiskAssessment.riskLevel}`);
      console.log(`   💡 Strategic recommendations: ${seasonalPredictions.strategicRecommendations.length}`);
      
      if (seasonalPredictions.upcomingSeasons.length > 0) {
        const q4Prediction = seasonalPredictions.upcomingSeasons.find(s => s.season === 'Q4');
        if (q4Prediction) {
          console.log(`   🎄 Q4 quality prediction: ${q4Prediction.predictions.qualityScore.predicted}/100`);
          console.log(`   🚚 Q4 delivery rate: ${q4Prediction.predictions.deliveryPerformance.onTimeDeliveryRate}%`);
        }
      }
      
    } catch (error) {
      console.log('   ❌ Seasonal Predictions Engine failed:', error.message);
      console.log('   📝 Error details:', error.stack);
    }

    console.log('\n🎉 Engine testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.log('\n🔧 Error details:');
    console.log('   Message:', error.message);
    console.log('   Stack:', error.stack);
  }
}

// Run the test
testAnalyticsEngines();
