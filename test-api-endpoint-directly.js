/**
 * Direct API Endpoint Test Script
 * This script tests the /api/invoices endpoint directly to isolate server-side issues
 */

const testAPIDirectly = async () => {
  console.log('🧪 Testing Invoice API Endpoint Directly...\n');

  try {
    // Test 1: Check if API endpoint exists
    console.log('📋 Test 1: API Endpoint Availability');
    
    const healthCheck = await fetch('http://localhost:3000/api/invoices', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    console.log(`   GET /api/invoices status: ${healthCheck.status}`);
    
    if (healthCheck.status === 401) {
      console.log('   ℹ️  Authentication required (expected for protected endpoint)');
    } else if (healthCheck.status === 200) {
      console.log('   ✅ API endpoint is accessible');
    } else {
      console.log(`   ⚠️  Unexpected status: ${healthCheck.status}`);
    }

    // Test 2: Test with minimal valid data
    console.log('\n📝 Test 2: Minimal Valid Invoice Data');
    
    const minimalInvoiceData = {
      invoiceNumber: `TEST-${Date.now()}`,
      supplierId: "test-supplier-id", // This needs to be replaced with actual supplier ID
      invoiceDate: new Date().toISOString(),
      taxPercentage: 11,
      enableInstallments: false,
      items: [
        {
          productId: "test-product-id", // This needs to be replaced with actual product ID
          description: "Test Product",
          quantity: 1,
          unitPrice: 100
        }
      ]
    };

    console.log('   Minimal test data:');
    console.log('   ', JSON.stringify(minimalInvoiceData, null, 2));
    
    console.log('\n   ⚠️  To test this data:');
    console.log('   1. Replace "test-supplier-id" with actual supplier ID from database');
    console.log('   2. Replace "test-product-id" with actual product ID from database');
    console.log('   3. Ensure you are authenticated in the browser');
    console.log('   4. Use browser console or network tab to test');

    // Test 3: Check database prerequisites
    console.log('\n🗄️  Test 3: Database Prerequisites Check');
    console.log('   Required database records:');
    console.log('   □ At least one Supplier record exists');
    console.log('   □ At least one Product record exists');
    console.log('   □ User is authenticated with proper role');
    console.log('   □ Database connection is working');
    console.log('   □ All required tables exist');

    // Test 4: Authentication check
    console.log('\n🔐 Test 4: Authentication Requirements');
    console.log('   Required for invoice creation:');
    console.log('   □ User must be logged in');
    console.log('   □ User role must be: SUPER_ADMIN, FINANCE_ADMIN, or WAREHOUSE_ADMIN');
    console.log('   □ Valid JWT token in request headers');
    console.log('   □ Token not expired');

    return {
      testPassed: true,
      message: 'Manual testing required with actual database IDs'
    };

  } catch (error) {
    console.error('❌ API test failed:', error.message);
    return {
      testPassed: false,
      message: error.message
    };
  }
};

// Get actual database IDs for testing
const getDatabaseIDs = async () => {
  console.log('\n🔍 Getting Database IDs for Testing:');
  
  try {
    // Try to get suppliers
    const suppliersResponse = await fetch('http://localhost:3000/api/suppliers');
    if (suppliersResponse.ok) {
      const suppliers = await suppliersResponse.json();
      if (suppliers.length > 0) {
        console.log(`   ✅ Found ${suppliers.length} suppliers`);
        console.log(`   First supplier ID: ${suppliers[0].id}`);
        console.log(`   First supplier name: ${suppliers[0].name}`);
      } else {
        console.log('   ❌ No suppliers found in database');
      }
    } else {
      console.log(`   ⚠️  Could not fetch suppliers (status: ${suppliersResponse.status})`);
    }

    // Try to get products
    const productsResponse = await fetch('http://localhost:3000/api/products');
    if (productsResponse.ok) {
      const products = await productsResponse.json();
      if (products.length > 0) {
        console.log(`   ✅ Found ${products.length} products`);
        console.log(`   First product ID: ${products[0].id}`);
        console.log(`   First product name: ${products[0].name}`);
      } else {
        console.log('   ❌ No products found in database');
      }
    } else {
      console.log(`   ⚠️  Could not fetch products (status: ${productsResponse.status})`);
    }

  } catch (error) {
    console.log('   ❌ Error fetching database IDs:', error.message);
  }
};

// Test with real data (if available)
const testWithRealData = async () => {
  console.log('\n🎯 Test with Real Data:');
  
  try {
    // Get real suppliers and products
    const [suppliersResponse, productsResponse] = await Promise.all([
      fetch('http://localhost:3000/api/suppliers'),
      fetch('http://localhost:3000/api/products')
    ]);

    if (!suppliersResponse.ok || !productsResponse.ok) {
      console.log('   ⚠️  Cannot fetch suppliers or products for real data test');
      return;
    }

    const suppliers = await suppliersResponse.json();
    const products = await productsResponse.json();

    if (suppliers.length === 0 || products.length === 0) {
      console.log('   ⚠️  No suppliers or products available for testing');
      return;
    }

    const realInvoiceData = {
      invoiceNumber: `TEST-REAL-${Date.now()}`,
      supplierId: suppliers[0].id,
      invoiceDate: new Date().toISOString(),
      taxPercentage: 11,
      enableInstallments: false,
      items: [
        {
          productId: products[0].id,
          description: products[0].name,
          quantity: 1,
          unitPrice: 100
        }
      ]
    };

    console.log('   Real test data prepared:');
    console.log('   Supplier:', suppliers[0].name);
    console.log('   Product:', products[0].name);
    console.log('   Invoice Number:', realInvoiceData.invoiceNumber);
    
    console.log('\n   To test with this data:');
    console.log('   1. Copy the data below');
    console.log('   2. Use browser console: fetch("/api/invoices", { method: "POST", headers: {"Content-Type": "application/json"}, body: JSON.stringify(data) })');
    console.log('   3. Check server console for detailed logs');
    
    console.log('\n   Test data:');
    console.log(JSON.stringify(realInvoiceData, null, 2));

  } catch (error) {
    console.log('   ❌ Error preparing real test data:', error.message);
  }
};

// Debug server logs
const debugServerLogs = () => {
  console.log('\n🐛 Server Log Debugging:');
  console.log('   With the enhanced logging, you should see:');
  console.log('   🚀 Invoice creation API called');
  console.log('   🔐 Verifying authentication...');
  console.log('   ✅ Authentication successful, user: [email] role: [role]');
  console.log('   ✅ Permissions verified');
  console.log('   📥 Parsing request body...');
  console.log('   📋 Request body received: [full request data]');
  console.log('   🔍 Validating data with schema...');
  console.log('   ✅ Data validation successful: [validated data]');
  console.log('   🏢 Verifying supplier exists: [supplier-id]');
  console.log('   ✅ Supplier found: [supplier-name]');
  console.log('   💰 Calculating invoice totals...');
  console.log('   💰 Totals calculated - Subtotal: X, Tax: Y, Total: Z');
  console.log('   💾 Creating invoice in database...');
  console.log('   ✅ Invoice created successfully: [invoice-id]');
  
  console.log('\n   If you see an error instead:');
  console.log('   ❌ Look for the specific error message');
  console.log('   🔍 Check Zod validation error details');
  console.log('   🗄️  Check Prisma error codes');
  console.log('   📤 Check the final error response');
};

// Main test runner
const runDirectAPITests = () => {
  console.log('🚀 Direct API Endpoint Testing - Comprehensive Suite\n');
  
  const result = testAPIDirectly();
  getDatabaseIDs();
  testWithRealData();
  debugServerLogs();
  
  console.log('\n📊 Test Summary:');
  console.log('   ✅ Enhanced server-side logging added');
  console.log('   ✅ Comprehensive error handling implemented');
  console.log('   ✅ Database prerequisite checks available');
  console.log('   ✅ Real data testing prepared');
  
  console.log('\n🎯 Next Steps:');
  console.log('   1. Check server console for detailed logs');
  console.log('   2. Verify database has suppliers and products');
  console.log('   3. Test with real data using browser console');
  console.log('   4. Check authentication and permissions');
  console.log('   5. Monitor network tab for request/response details');
  
  console.log('\n🔧 If Empty Error Response Persists:');
  console.log('   • Check if server is running and accessible');
  console.log('   • Verify API route file exists and is properly exported');
  console.log('   • Check for syntax errors in API code');
  console.log('   • Verify database connection and schema');
  console.log('   • Check server console for unhandled exceptions');
  
  return result;
};

// Export for browser console usage
if (typeof window !== 'undefined') {
  window.testAPIDirectly = testAPIDirectly;
  window.runDirectAPITests = runDirectAPITests;
  window.getDatabaseIDs = getDatabaseIDs;
  window.testWithRealData = testWithRealData;
  console.log('🔧 Direct API testing functions loaded.');
  console.log('Run runDirectAPITests() to start comprehensive testing.');
} else {
  // Node.js environment
  runDirectAPITests();
}
