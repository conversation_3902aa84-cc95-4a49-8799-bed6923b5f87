// Simple test script to test the stock adjustment API
const fetch = require('node-fetch');

async function testStockAdjustmentAPI() {
  try {
    console.log('Testing stock adjustment API...');
    
    // Test data
    const testData = {
      productId: "test-product-id",
      locationType: "STORE",
      adjustmentQuantity: -5,
      reason: "DAMAGED",
      notes: "Test adjustment for WAREHOUSE_ADMIN"
    };

    console.log('Test data:', JSON.stringify(testData, null, 2));

    // Make the API call
    const response = await fetch('http://localhost:3000/api/inventory/adjustments', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add a test session token here if needed
      },
      body: JSON.stringify(testData)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    const responseData = await response.text();
    console.log('Response body:', responseData);

    if (!response.ok) {
      console.error('API call failed');
      try {
        const errorData = JSON.parse(responseData);
        console.error('Error details:', errorData);
      } catch (e) {
        console.error('Could not parse error response as JSON');
      }
    } else {
      console.log('API call successful');
      try {
        const successData = JSON.parse(responseData);
        console.log('Success data:', successData);
      } catch (e) {
        console.error('Could not parse success response as JSON');
      }
    }

  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

// Run the test
testStockAdjustmentAPI();
