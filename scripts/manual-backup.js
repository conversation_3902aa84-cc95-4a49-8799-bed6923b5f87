#!/usr/bin/env node

/**
 * Manual Backup Script
 * 
 * This script allows for creating manual backups from the command line.
 * 
 * Usage:
 *   node scripts/manual-backup.js [comment]
 * 
 * Example:
 *   node scripts/manual-backup.js "Before major update"
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get comment from command line arguments
const comment = process.argv.slice(2).join(' ') || 'Manual backup';

// Configuration
const config = {
  backupDir: path.join(process.cwd(), 'backups'),
  logFile: path.join(process.cwd(), 'logs', 'backup.log'),
};

// Ensure directories exist
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

const logDir = path.dirname(config.logFile);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Log function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  // Append to log file
  fs.appendFileSync(config.logFile, logMessage);
}

// Run the backup
async function runManualBackup() {
  log(`Starting manual backup with comment: "${comment}"...`);
  
  // Create a temporary script to run the backup with the comment
  const tempScriptPath = path.join(process.cwd(), 'temp-backup-script.js');
  
  const scriptContent = `
    const { createBackup } = require('./src/lib/backup/db-backup');
    
    async function run() {
      try {
        const result = await createBackup({
          comment: ${JSON.stringify(comment)},
          filename: 'manual-backup-' + new Date().toISOString().replace(/[:.]/g, '-') + '.sql'
        });
        console.log(JSON.stringify(result));
        process.exit(0);
      } catch (error) {
        console.error(error.message);
        process.exit(1);
      }
    }
    
    run();
  `;
  
  fs.writeFileSync(tempScriptPath, scriptContent);
  
  // Use ts-node to run the script
  const process = spawn('npx', ['ts-node', tempScriptPath], {
    cwd: process.cwd(),
    env: { ...process.env },
  });
  
  let output = '';
  let errorOutput = '';
  
  process.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  process.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });
  
  process.on('close', (code) => {
    // Clean up the temporary script
    if (fs.existsSync(tempScriptPath)) {
      fs.unlinkSync(tempScriptPath);
    }
    
    if (code === 0) {
      try {
        const result = JSON.parse(output.trim());
        log(`Manual backup completed successfully: ${result.filePath}`);
      } catch (e) {
        log(`Manual backup completed successfully: ${output.trim()}`);
      }
    } else {
      log(`Manual backup failed with code ${code}: ${errorOutput.trim() || output.trim()}`);
    }
  });
}

// Run the backup
runManualBackup().catch((error) => {
  log(`Error running manual backup: ${error.message}`);
  process.exit(1);
});
