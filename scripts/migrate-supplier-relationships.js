/**
 * Data Migration Script: Product-Supplier Relationships
 * 
 * This script migrates existing Product.supplierId relationships to the new
 * ProductSupplier junction table system, ensuring data integrity and
 * backward compatibility.
 * 
 * CRITICAL: This migration must be run before removing the old supplier
 * fields from the Product model.
 * 
 * Usage: node scripts/migrate-supplier-relationships.js
 */

import { PrismaClient } from './prisma-client.js';

const prisma = new PrismaClient();

// Migration statistics
const stats = {
  totalProducts: 0,
  productsWithSuppliers: 0,
  migratedRelationships: 0,
  skippedExisting: 0,
  errors: 0,
  warnings: 0
};

// Validation flags
const VALIDATE_ONLY = process.argv.includes('--validate-only');
const DRY_RUN = process.argv.includes('--dry-run');
const FORCE = process.argv.includes('--force');

/**
 * Log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Validate migration prerequisites
 */
async function validatePrerequisites() {
  log('Validating migration prerequisites...');
  
  try {
    // Check if ProductSupplier table exists
    const productSupplierCount = await prisma.productSupplier.count();
    log(`ProductSupplier table exists with ${productSupplierCount} records`);
    
    // Check if Product table has supplierId field
    const productsWithSuppliers = await prisma.product.count({
      where: {
        supplierId: { not: null }
      }
    });
    log(`Found ${productsWithSuppliers} products with supplier relationships`);
    
    // Check if all referenced suppliers exist
    const products = await prisma.product.findMany({
      where: {
        supplierId: { not: null }
      },
      select: {
        id: true,
        name: true,
        supplierId: true
      }
    });
    
    const supplierIds = [...new Set(products.map(p => p.supplierId).filter(Boolean))];
    const existingSuppliers = await prisma.supplier.findMany({
      where: {
        id: { in: supplierIds }
      },
      select: { id: true }
    });
    
    const existingSupplierIds = new Set(existingSuppliers.map(s => s.id));
    const missingSupplierIds = supplierIds.filter(id => !existingSupplierIds.has(id));
    
    if (missingSupplierIds.length > 0) {
      log(`WARNING: Found ${missingSupplierIds.length} products referencing non-existent suppliers`, 'WARN');
      log(`Missing supplier IDs: ${missingSupplierIds.join(', ')}`, 'WARN');
      stats.warnings += missingSupplierIds.length;
      
      if (!FORCE) {
        throw new Error('Migration aborted due to missing suppliers. Use --force to continue anyway.');
      }
    }
    
    return {
      productsWithSuppliers,
      supplierIds,
      missingSupplierIds
    };
    
  } catch (error) {
    log(`Validation failed: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Check for existing ProductSupplier relationships
 */
async function checkExistingRelationships(products) {
  log('Checking for existing ProductSupplier relationships...');
  
  const existingRelationships = new Map();
  
  for (const product of products) {
    if (!product.supplierId) continue;
    
    const existing = await prisma.productSupplier.findUnique({
      where: {
        productId_supplierId: {
          productId: product.id,
          supplierId: product.supplierId
        }
      }
    });
    
    if (existing) {
      existingRelationships.set(`${product.id}-${product.supplierId}`, existing);
    }
  }
  
  log(`Found ${existingRelationships.size} existing ProductSupplier relationships`);
  return existingRelationships;
}

/**
 * Migrate a single product-supplier relationship
 */
async function migrateProductSupplier(product, existingRelationships, tx) {
  if (!product.supplierId) {
    return { success: true, skipped: true, reason: 'No supplier assigned' };
  }
  
  const relationshipKey = `${product.id}-${product.supplierId}`;
  
  // Check if relationship already exists
  if (existingRelationships.has(relationshipKey)) {
    stats.skippedExisting++;
    return { success: true, skipped: true, reason: 'Relationship already exists' };
  }
  
  try {
    // Verify supplier exists
    const supplier = await tx.supplier.findUnique({
      where: { id: product.supplierId },
      select: { id: true, name: true }
    });
    
    if (!supplier) {
      log(`WARNING: Product ${product.name} (${product.id}) references non-existent supplier ${product.supplierId}`, 'WARN');
      stats.warnings++;
      return { success: false, skipped: true, reason: 'Supplier not found' };
    }
    
    // Create ProductSupplier relationship
    const productSupplier = await tx.productSupplier.create({
      data: {
        productId: product.id,
        supplierId: product.supplierId,
        purchasePrice: product.purchasePrice || 0,
        isPreferred: true, // Set migrated suppliers as preferred
        isActive: true,
        notes: 'Migrated from legacy Product.supplierId relationship',
        // Optional fields can be null initially
        supplierProductCode: null,
        supplierProductName: null,
        minimumOrderQuantity: null,
        leadTimeDays: null,
        lastOrderDate: null,
        lastPurchasePrice: null
      }
    });
    
    stats.migratedRelationships++;
    return { 
      success: true, 
      skipped: false, 
      productSupplier,
      reason: 'Successfully migrated'
    };
    
  } catch (error) {
    stats.errors++;
    log(`ERROR migrating product ${product.name} (${product.id}): ${error.message}`, 'ERROR');
    return { success: false, skipped: false, error: error.message };
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  log('Starting Product-Supplier relationship migration...');
  log(`Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE MIGRATION'}`);
  
  try {
    // Validate prerequisites
    const validation = await validatePrerequisites();
    
    if (VALIDATE_ONLY) {
      log('Validation completed successfully. Use without --validate-only to run migration.');
      return;
    }
    
    // Get all products with supplier relationships
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        sku: true,
        supplierId: true,
        purchasePrice: true
      }
    });
    
    stats.totalProducts = products.length;
    stats.productsWithSuppliers = products.filter(p => p.supplierId).length;
    
    log(`Found ${stats.totalProducts} total products`);
    log(`Found ${stats.productsWithSuppliers} products with supplier relationships`);
    
    if (stats.productsWithSuppliers === 0) {
      log('No products with supplier relationships found. Migration not needed.');
      return;
    }
    
    // Check existing relationships
    const existingRelationships = await checkExistingRelationships(products);
    
    if (DRY_RUN) {
      log('DRY RUN: Simulating migration...');
      
      for (const product of products) {
        if (!product.supplierId) continue;
        
        const relationshipKey = `${product.id}-${product.supplierId}`;
        if (existingRelationships.has(relationshipKey)) {
          log(`DRY RUN: Would skip ${product.name} - relationship exists`);
        } else {
          log(`DRY RUN: Would migrate ${product.name} -> supplier ${product.supplierId}`);
        }
      }
      
      log('DRY RUN completed. Use without --dry-run to execute migration.');
      return;
    }
    
    // Execute migration in transaction
    log('Executing migration in database transaction...');
    
    await prisma.$transaction(async (tx) => {
      const results = [];
      
      for (const product of products) {
        if (!product.supplierId) continue;
        
        const result = await migrateProductSupplier(product, existingRelationships, tx);
        results.push({ product, result });
        
        if (result.success && !result.skipped) {
          log(`Migrated: ${product.name} -> ${product.supplierId}`);
        } else if (result.skipped) {
          log(`Skipped: ${product.name} - ${result.reason}`);
        }
      }
      
      return results;
    });
    
    log('Migration completed successfully!');
    
  } catch (error) {
    log(`Migration failed: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Print migration statistics
 */
function printStats() {
  log('=== Migration Statistics ===');
  log(`Total products: ${stats.totalProducts}`);
  log(`Products with suppliers: ${stats.productsWithSuppliers}`);
  log(`Migrated relationships: ${stats.migratedRelationships}`);
  log(`Skipped existing: ${stats.skippedExisting}`);
  log(`Warnings: ${stats.warnings}`);
  log(`Errors: ${stats.errors}`);
  log('============================');
}

/**
 * Main execution
 */
async function main() {
  try {
    await runMigration();
    printStats();
    
    if (stats.errors > 0) {
      process.exit(1);
    }
    
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'ERROR');
    printStats();
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Show usage if help requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node scripts/migrate-supplier-relationships.js [options]

Options:
  --validate-only    Only validate prerequisites, don't run migration
  --dry-run         Simulate migration without making changes
  --force           Continue migration even if warnings are found
  --help, -h        Show this help message

Examples:
  node scripts/migrate-supplier-relationships.js --validate-only
  node scripts/migrate-supplier-relationships.js --dry-run
  node scripts/migrate-supplier-relationships.js
  node scripts/migrate-supplier-relationships.js --force
`);
  process.exit(0);
}

// Run migration
main();
