#!/usr/bin/env node

/**
 * Debug Script: Check Batch Location Status
 * 
 * This script checks the current state of batches in the database
 * to understand why location badges are not showing.
 */

import { PrismaClient } from '../src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Checking batch location status...\n');
  
  try {
    // Get all batches with their location associations
    const batches = await prisma.stockBatch.findMany({
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true
          }
        },
        warehouseStock: {
          select: {
            id: true,
            quantity: true
          }
        },
        storeStock: {
          select: {
            id: true,
            quantity: true
          }
        }
      },
      orderBy: {
        receivedDate: 'desc'
      }
    });

    console.log(`📊 Found ${batches.length} total batches\n`);

    // Analyze batch location associations
    let withWarehouseStock = 0;
    let withStoreStock = 0;
    let withBothLocations = 0;
    let orphanedBatches = 0;

    console.log('📋 Batch Details:');
    console.log('================');

    batches.forEach((batch, index) => {
      const hasWarehouse = !!batch.warehouseStock;
      const hasStore = !!batch.storeStock;
      
      if (hasWarehouse) withWarehouseStock++;
      if (hasStore) withStoreStock++;
      if (hasWarehouse && hasStore) withBothLocations++;
      if (!hasWarehouse && !hasStore) orphanedBatches++;

      console.log(`${index + 1}. ${batch.product.name}`);
      console.log(`   Batch ID: ${batch.id}`);
      console.log(`   Warehouse Stock ID: ${batch.warehouseStockId || 'null'}`);
      console.log(`   Store Stock ID: ${batch.storeStockId || 'null'}`);
      console.log(`   Has Warehouse Stock: ${hasWarehouse}`);
      console.log(`   Has Store Stock: ${hasStore}`);
      console.log(`   Quantity: ${batch.remainingQuantity}`);
      console.log(`   Status: ${hasWarehouse && hasStore ? 'Both Locations' : hasWarehouse ? 'Warehouse Only' : hasStore ? 'Store Only' : 'ORPHANED'}`);
      console.log('');
    });

    console.log('📈 Summary:');
    console.log('===========');
    console.log(`Total batches: ${batches.length}`);
    console.log(`With warehouse stock: ${withWarehouseStock}`);
    console.log(`With store stock: ${withStoreStock}`);
    console.log(`With both locations: ${withBothLocations}`);
    console.log(`Orphaned (no location): ${orphanedBatches}`);

    if (orphanedBatches > 0) {
      console.log(`\n⚠️  WARNING: ${orphanedBatches} batches are orphaned (no location associations)`);
      console.log('   This explains why location badges are not showing in the UI.');
    } else {
      console.log('\n✅ All batches have proper location associations.');
    }

    // Check warehouse and store stock records
    const warehouseStockCount = await prisma.warehouseStock.count();
    const storeStockCount = await prisma.storeStock.count();

    console.log(`\n📦 Stock Records:`);
    console.log(`Warehouse stock records: ${warehouseStockCount}`);
    console.log(`Store stock records: ${storeStockCount}`);

  } catch (error) {
    console.error('❌ Error checking batch status:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
main()
  .catch((error) => {
    console.error('💥 Check failed:', error);
    process.exit(1);
  });
