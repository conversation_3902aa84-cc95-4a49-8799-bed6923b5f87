// Validation script to check the generated analytics test data
// This script verifies that the data generation was successful and data integrity is maintained

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function validateData() {
  console.log('🔍 Validating Analytics Test Data');
  console.log('=================================');
  console.log('');

  try {
    // Check suppliers
    const suppliers = await prisma.supplier.findMany();
    console.log(`✅ Suppliers: ${suppliers.length} found`);
    
    // Check product-supplier relationships
    const productSuppliers = await prisma.productSupplier.findMany();
    console.log(`✅ Product-Supplier relationships: ${productSuppliers.length} found`);
    
    // Check purchase orders
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      include: {
        items: true,
        supplier: true
      }
    });
    console.log(`✅ Purchase Orders: ${purchaseOrders.length} found`);
    
    // Analyze PO status distribution
    const statusCounts = {};
    purchaseOrders.forEach(po => {
      statusCounts[po.status] = (statusCounts[po.status] || 0) + 1;
    });
    console.log('   Status distribution:', statusCounts);
    
    // Check stock batches
    const stockBatches = await prisma.stockBatch.findMany();
    console.log(`✅ Stock Batches: ${stockBatches.length} found`);
    
    // Check warehouse vs store batches
    const warehouseBatches = stockBatches.filter(b => b.warehouseStockId);
    const storeBatches = stockBatches.filter(b => b.storeStockId);
    console.log(`   Warehouse batches: ${warehouseBatches.length}`);
    console.log(`   Store batches: ${storeBatches.length}`);
    
    // Check transactions
    const transactions = await prisma.transaction.findMany({
      include: {
        items: true
      }
    });
    console.log(`✅ Customer Transactions: ${transactions.length} found`);
    
    // Check returns
    const returns = await prisma.return.findMany({
      include: {
        items: true
      }
    });
    console.log(`✅ Customer Returns: ${returns.length} found`);
    
    if (transactions.length > 0) {
      const returnRate = (returns.length / transactions.length * 100).toFixed(2);
      console.log(`   Return rate: ${returnRate}%`);
    }
    
    // Check stock history
    const stockHistory = await prisma.stockHistory.findMany();
    console.log(`✅ Stock History entries: ${stockHistory.length} found`);
    
    // Validate data integrity
    console.log('');
    console.log('🔍 Data Integrity Checks:');
    
    // Check that all received POs have batches
    const receivedPOs = purchaseOrders.filter(po => po.status === 'RECEIVED');
    const batchesFromPOs = stockBatches.filter(b => b.purchaseOrderId);
    console.log(`   Received POs: ${receivedPOs.length}`);
    console.log(`   Batches from POs: ${batchesFromPOs.length}`);
    
    // Check FIFO logic - total stock should equal sum of batch quantities
    const products = await prisma.product.findMany({
      include: {
        storeStock: true,
        warehouseStock: true,
        stockBatches: true
      }
    });
    
    let fifoErrors = 0;
    for (const product of products) {
      const storeStockQty = product.storeStock?.quantity || 0;
      const warehouseStockQty = product.warehouseStock?.quantity || 0;
      const totalStockQty = parseFloat(storeStockQty) + parseFloat(warehouseStockQty);
      
      const storeBatchQty = product.stockBatches
        .filter(b => b.storeStockId)
        .reduce((sum, b) => sum + parseFloat(b.remainingQuantity), 0);
      const warehouseBatchQty = product.stockBatches
        .filter(b => b.warehouseStockId)
        .reduce((sum, b) => sum + parseFloat(b.remainingQuantity), 0);
      const totalBatchQty = storeBatchQty + warehouseBatchQty;
      
      if (Math.abs(totalStockQty - totalBatchQty) > 0.01) {
        fifoErrors++;
        console.log(`   ❌ FIFO error for ${product.name}: Stock=${totalStockQty}, Batches=${totalBatchQty}`);
      }
    }
    
    if (fifoErrors === 0) {
      console.log(`   ✅ FIFO integrity: All ${products.length} products have consistent stock quantities`);
    } else {
      console.log(`   ❌ FIFO integrity: ${fifoErrors} products have inconsistent quantities`);
    }
    
    // Check date ranges
    const oldestPO = purchaseOrders.reduce((oldest, po) => 
      !oldest || po.orderDate < oldest.orderDate ? po : oldest, null);
    const newestPO = purchaseOrders.reduce((newest, po) => 
      !newest || po.orderDate > newest.orderDate ? po : newest, null);
    
    if (oldestPO && newestPO) {
      console.log(`   ✅ Date range: ${oldestPO.orderDate.toDateString()} to ${newestPO.orderDate.toDateString()}`);
    }
    
    // Check currency (should be IDR amounts)
    const avgPOTotal = purchaseOrders.reduce((sum, po) => sum + parseFloat(po.total), 0) / purchaseOrders.length;
    console.log(`   ✅ Average PO total: ${avgPOTotal.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`);
    
    // Summary
    console.log('');
    console.log('📊 Data Generation Summary:');
    console.log(`   • ${suppliers.length} suppliers created/used`);
    console.log(`   • ${productSuppliers.length} product-supplier relationships`);
    console.log(`   • ${purchaseOrders.length} purchase orders generated`);
    console.log(`   • ${stockBatches.length} stock batches created`);
    console.log(`   • ${storeBatches.length} store transfers completed`);
    console.log(`   • ${transactions.length} customer transactions`);
    console.log(`   • ${returns.length} customer returns`);
    console.log(`   • ${stockHistory.length} stock history entries`);
    
    console.log('');
    console.log('✅ Analytics test data validation completed successfully!');
    console.log('');
    console.log('🎯 Ready for testing supplier performance analytics:');
    console.log('   • Quality metrics (return rates by supplier)');
    console.log('   • Delivery performance (on-time delivery rates)');
    console.log('   • Cost analysis (price trends, supplier comparisons)');
    console.log('   • Relationship health (order frequency, reliability)');
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run validation
validateData();
