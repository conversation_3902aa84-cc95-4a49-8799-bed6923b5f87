# Quick Start Guide: Analytics Test Data Generation

## Prerequisites

1. **Ensure you have base data** (run these first if you haven't):
```bash
npm run seed                    # Creates users, categories, units
node prisma/seed-products.js    # Creates 100 sample products
```

2. **Install required dependencies**:
```bash
npm install @prisma/client @faker-js/faker bcryptjs
```

## Generate Analytics Test Data

### Option 1: Using NPM Script (Recommended)
```bash
npm run generate:analytics-data
```

### Option 2: Direct execution
```bash
node scripts/run-analytics-data-generation.js
```

## Validate Generated Data

After generation, verify everything worked correctly:
```bash
npm run validate:analytics-data
```

## What Gets Generated

- **50 Purchase Orders** across 8-12 suppliers over 6 months
- **Realistic delivery patterns** (3-14 day delays)
- **Stock batches** with proper FIFO logic
- **Store transfers** (70% of warehouse stock moved to store)
- **250+ customer transactions** with realistic buying patterns
- **Customer returns** (~8% return rate) with various reasons
- **Complete audit trails** for all stock movements

## Testing Analytics Features

After data generation, test these features:

1. **Supplier Performance Dashboard**
   - Navigate to `/inventory/suppliers/analytics`
   - View quality metrics, delivery performance, cost analysis

2. **Individual Supplier Analytics**
   - Go to `/inventory/suppliers`
   - Click on any supplier to see detailed performance

3. **Purchase Order Analytics**
   - Visit `/inventory/purchase-orders`
   - Review delivery performance and cost trends

## Troubleshooting

### "No products found" error
```bash
node prisma/seed-products.js
```

### Database connection issues
- Check your `DATABASE_URL` environment variable
- Ensure PostgreSQL is running
- Run `npx prisma db push` to sync schema

### Permission errors
```bash
chmod +x scripts/run-analytics-data-generation.js
```

## Configuration

Edit `scripts/generate-analytics-test-data.js` to customize:
- Number of purchase orders
- Date range
- Return rates
- Supplier count
- Transaction patterns

## Data Cleanup

To remove generated test data (keep base products/categories):
```sql
-- Run in your database to clean up test data
DELETE FROM "StockHistory";
DELETE FROM "ReturnItem";
DELETE FROM "Return";
DELETE FROM "TransactionItem";
DELETE FROM "Transaction";
DELETE FROM "StockBatch";
DELETE FROM "PurchaseOrderItem";
DELETE FROM "PurchaseOrder";
DELETE FROM "ProductSupplier";
-- Optionally remove test suppliers
DELETE FROM "Supplier" WHERE name LIKE 'PT %' OR name LIKE 'CV %';
```

## Support

If you encounter issues:
1. Check the console output for specific error messages
2. Verify all prerequisites are met
3. Ensure database connectivity
4. Review the troubleshooting section above
