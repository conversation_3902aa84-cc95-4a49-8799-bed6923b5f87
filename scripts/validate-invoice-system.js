import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function validateInvoiceSystem() {
  console.log('🧪 Starting Invoice System Validation...\n');

  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  function addTest(name, passed, message) {
    results.tests.push({ name, passed, message });
    if (passed) {
      results.passed++;
      console.log(`✅ ${name}: ${message}`);
    } else {
      results.failed++;
      console.log(`❌ ${name}: ${message}`);
    }
  }

  try {
    // Test 1: Database Schema Validation
    console.log('📋 Test Group 1: Database Schema Validation\n');
    
    try {
      const invoiceCount = await prisma.invoice.count();
      addTest('Invoice Table', true, `Table accessible with ${invoiceCount} records`);
    } catch (error) {
      addTest('Invoice Table', false, `Table not accessible: ${error.message}`);
    }

    try {
      const invoiceItemCount = await prisma.invoiceItem.count();
      addTest('InvoiceItem Table', true, `Table accessible with ${invoiceItemCount} records`);
    } catch (error) {
      addTest('InvoiceItem Table', false, `Table not accessible: ${error.message}`);
    }

    try {
      const invoicePaymentCount = await prisma.invoicePayment.count();
      addTest('InvoicePayment Table', true, `Table accessible with ${invoicePaymentCount} records`);
    } catch (error) {
      addTest('InvoicePayment Table', false, `Table not accessible: ${error.message}`);
    }

    // Test 2: Enum Validation
    console.log('\n📋 Test Group 2: Enum Validation\n');
    
    try {
      // Test invoice status enum
      const testInvoiceStatus = ['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED'];
      addTest('Invoice Status Enum', true, `Valid statuses: ${testInvoiceStatus.join(', ')}`);
    } catch (error) {
      addTest('Invoice Status Enum', false, `Enum validation failed: ${error.message}`);
    }

    try {
      // Test payment status enum
      const testPaymentStatus = ['UNPAID', 'PARTIALLY_PAID', 'PAID', 'OVERDUE'];
      addTest('Payment Status Enum', true, `Valid statuses: ${testPaymentStatus.join(', ')}`);
    } catch (error) {
      addTest('Payment Status Enum', false, `Enum validation failed: ${error.message}`);
    }

    // Test 3: Basic Data Creation
    console.log('\n📋 Test Group 3: Basic Data Creation\n');
    
    try {
      // Create minimal test user
      const testUser = await prisma.user.upsert({
        where: { email: '<EMAIL>' },
        update: {},
        create: {
          name: 'Invoice System Test',
          email: '<EMAIL>',
          password: 'test_password',
          role: 'SUPER_ADMIN',
          isActive: true,
        }
      });
      addTest('User Creation', true, `Test user created: ${testUser.name}`);
    } catch (error) {
      addTest('User Creation', false, `Failed to create user: ${error.message}`);
    }

    try {
      // Create minimal test supplier
      const testSupplier = await prisma.supplier.upsert({
        where: { name: 'Invoice Test Supplier' },
        update: {},
        create: {
          name: 'Invoice Test Supplier',
          contactPerson: 'Test Contact',
          phone: '+1234567890',
          email: '<EMAIL>',
          isActive: true,
        }
      });
      addTest('Supplier Creation', true, `Test supplier created: ${testSupplier.name}`);
    } catch (error) {
      addTest('Supplier Creation', false, `Failed to create supplier: ${error.message}`);
    }

    // Test 4: Invoice Number Generation
    console.log('\n📋 Test Group 4: Invoice Number Generation\n');
    
    try {
      const currentDate = new Date();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = currentDate.getFullYear();
      const sequence = '0001';
      const invoiceNumber = `INV-${year}-${month}-${sequence}`;
      
      addTest('Invoice Number Format', true, `Generated: ${invoiceNumber}`);
    } catch (error) {
      addTest('Invoice Number Format', false, `Generation failed: ${error.message}`);
    }

    // Test 5: Decimal Precision
    console.log('\n📋 Test Group 5: Decimal Precision\n');
    
    try {
      const testAmount = new Decimal(123.456789);
      const rounded = testAmount.toFixed(2);
      addTest('Decimal Precision', true, `${testAmount} rounded to ${rounded}`);
    } catch (error) {
      addTest('Decimal Precision', false, `Decimal handling failed: ${error.message}`);
    }

    // Test 6: Relationship Validation
    console.log('\n📋 Test Group 6: Relationship Validation\n');
    
    try {
      // Check if we can query relationships
      const invoicesWithRelations = await prisma.invoice.findMany({
        include: {
          supplier: true,
          purchaseOrder: true,
          items: true,
          payments: true,
          createdBy: true,
          approvedBy: true,
        },
        take: 1
      });
      addTest('Invoice Relationships', true, `Queried ${invoicesWithRelations.length} invoices with relations`);
    } catch (error) {
      addTest('Invoice Relationships', false, `Relationship query failed: ${error.message}`);
    }

    // Test 7: API Endpoint Structure
    console.log('\n📋 Test Group 7: API Endpoint Structure\n');
    
    try {
      // Check if invoice API files exist
      const fs = await import('fs');
      const invoiceApiExists = fs.existsSync('src/app/api/invoices/route.ts');
      addTest('Invoice API Route', invoiceApiExists, invoiceApiExists ? 'API route file exists' : 'API route file missing');
      
      const invoiceDetailApiExists = fs.existsSync('src/app/api/invoices/[id]/route.ts');
      addTest('Invoice Detail API', invoiceDetailApiExists, invoiceDetailApiExists ? 'Detail API route exists' : 'Detail API route missing');
      
      const invoicePaymentApiExists = fs.existsSync('src/app/api/invoices/[id]/payments/route.ts');
      addTest('Invoice Payment API', invoicePaymentApiExists, invoicePaymentApiExists ? 'Payment API route exists' : 'Payment API route missing');
    } catch (error) {
      addTest('API Structure Check', false, `File system check failed: ${error.message}`);
    }

    // Test 8: Component Structure
    console.log('\n📋 Test Group 8: Component Structure\n');
    
    try {
      const fs = await import('fs');
      const invoiceListExists = fs.existsSync('src/app/invoices/page.tsx');
      addTest('Invoice List Page', invoiceListExists, invoiceListExists ? 'List page exists' : 'List page missing');
      
      const invoiceDetailExists = fs.existsSync('src/app/invoices/[id]/page.tsx');
      addTest('Invoice Detail Page', invoiceDetailExists, invoiceDetailExists ? 'Detail page exists' : 'Detail page missing');
      
      const invoiceCreateExists = fs.existsSync('src/app/invoices/new/page.tsx');
      addTest('Invoice Create Page', invoiceCreateExists, invoiceCreateExists ? 'Create page exists' : 'Create page missing');
    } catch (error) {
      addTest('Component Structure Check', false, `Component check failed: ${error.message}`);
    }

    // Test 9: Utility Functions
    console.log('\n📋 Test Group 9: Utility Functions\n');
    
    try {
      const fs = await import('fs');
      const utilsExist = fs.existsSync('src/lib/invoice-utils.ts');
      addTest('Invoice Utils', utilsExist, utilsExist ? 'Utility functions exist' : 'Utility functions missing');
    } catch (error) {
      addTest('Utility Functions Check', false, `Utils check failed: ${error.message}`);
    }

    // Final Summary
    console.log('\n📊 Validation Summary:');
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📋 Total: ${results.tests.length}`);
    
    const successRate = ((results.passed / results.tests.length) * 100).toFixed(1);
    console.log(`🎯 Success Rate: ${successRate}%`);

    if (results.failed === 0) {
      console.log('\n🎉 All Invoice System Validations Passed!');
      console.log('✅ The invoice management system is ready for use.');
    } else {
      console.log('\n⚠️  Some validations failed. Please review the issues above.');
    }

    return results;

  } catch (error) {
    console.error('❌ Validation failed with error:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run validation
if (import.meta.url === `file://${process.argv[1]}`) {
  validateInvoiceSystem()
    .catch((error) => {
      console.error('❌ Invoice system validation failed:', error);
      process.exit(1);
    });
}

export { validateInvoiceSystem };
