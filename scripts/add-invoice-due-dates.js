import { PrismaClient } from '@prisma/client';
import { addDays } from 'date-fns';

const prisma = new PrismaClient();

async function addInvoiceDueDates() {
  try {
    console.log('🔄 Adding due dates to existing invoices...\n');

    // Get all invoices without due dates
    const invoicesWithoutDueDates = await prisma.invoice.findMany({
      where: {
        dueDate: null,
        paymentStatus: { not: 'PAID' }
      },
      select: {
        id: true,
        invoiceNumber: true,
        paymentStatus: true,
        total: true
      }
    });

    console.log(`Found ${invoicesWithoutDueDates.length} invoices without due dates`);

    for (const invoice of invoicesWithoutDueDates) {
      // Set due date to 30 days from now for unpaid invoices
      // Set due date to 15 days from now for partially paid invoices
      const daysToAdd = invoice.paymentStatus === 'PARTIALLY_PAID' ? 15 : 30;
      const dueDate = addDays(new Date(), daysToAdd);

      await prisma.invoice.update({
        where: { id: invoice.id },
        data: { dueDate }
      });

      console.log(`✅ Updated ${invoice.invoiceNumber}: Due date set to ${dueDate.toISOString().split('T')[0]}`);
    }

    // Also create a few more test invoices with different statuses and due dates
    const suppliers = await prisma.supplier.findMany({ take: 2 });
    const products = await prisma.product.findMany({ take: 3 });

    if (suppliers.length > 0 && products.length > 0) {
      console.log('\n🆕 Creating additional test invoices...');

      // Create an overdue invoice
      const overdueInvoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-2025-06-${String(Date.now()).slice(-4)}`,
          supplierId: suppliers[0].id,
          invoiceDate: new Date(),
          dueDate: addDays(new Date(), -5), // 5 days overdue
          subtotal: 5000000,
          tax: 500000,
          total: 5500000,
          status: 'APPROVED',
          paymentStatus: 'OVERDUE',
          paidAmount: 0,
          createdById: 'cmc8ekhkh0006cj7upzvr3ybu', // Use existing user ID
          items: {
            create: [
              {
                productId: products[0].id,
                description: products[0].name,
                quantity: 10,
                unitPrice: 500000,
                subtotal: 5000000
              }
            ]
          }
        }
      });

      console.log(`✅ Created overdue invoice: ${overdueInvoice.invoiceNumber}`);

      // Create an unpaid invoice due soon
      const upcomingInvoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-2025-06-${String(Date.now() + 1000).slice(-4)}`,
          supplierId: suppliers[1].id,
          invoiceDate: new Date(),
          dueDate: addDays(new Date(), 7), // Due in 7 days
          subtotal: 3000000,
          tax: 300000,
          total: 3300000,
          status: 'APPROVED',
          paymentStatus: 'UNPAID',
          paidAmount: 0,
          createdById: 'cmc8ekhkh0006cj7upzvr3ybu',
          items: {
            create: [
              {
                productId: products[1].id,
                description: products[1].name,
                quantity: 5,
                unitPrice: 600000,
                subtotal: 3000000
              }
            ]
          }
        }
      });

      console.log(`✅ Created upcoming invoice: ${upcomingInvoice.invoiceNumber}`);

      // Create a pending invoice
      const pendingInvoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-2025-06-${String(Date.now() + 2000).slice(-4)}`,
          supplierId: suppliers[0].id,
          invoiceDate: new Date(),
          dueDate: addDays(new Date(), 14), // Due in 14 days
          subtotal: 2000000,
          tax: 200000,
          total: 2200000,
          status: 'PENDING',
          paymentStatus: 'UNPAID',
          paidAmount: 0,
          createdById: 'cmc8ekhkh0006cj7upzvr3ybu',
          items: {
            create: [
              {
                productId: products[2].id,
                description: products[2].name,
                quantity: 4,
                unitPrice: 500000,
                subtotal: 2000000
              }
            ]
          }
        }
      });

      console.log(`✅ Created pending invoice: ${pendingInvoice.invoiceNumber}`);
    }

    console.log('\n✅ Invoice due dates updated successfully!');

  } catch (error) {
    console.error('❌ Error updating invoice due dates:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addInvoiceDueDates();
