// Using built-in fetch (Node.js 18+)

// Configuration
const BASE_URL = 'http://localhost:3000';
const API_BASE = `${BASE_URL}/api`;

// Test session token (you'll need to get this from your browser)
let SESSION_TOKEN = null;

// Helper function to make authenticated requests
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (SESSION_TOKEN) {
    headers['Cookie'] = `session-token=${SESSION_TOKEN}`;
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  const data = await response.json();
  
  return {
    status: response.status,
    ok: response.ok,
    data,
  };
}

// Test functions
async function testInvoiceList() {
  console.log('\n🧪 Testing Invoice List API...');
  
  try {
    const response = await apiRequest('/invoices');
    
    if (response.ok) {
      console.log('✅ Invoice list retrieved successfully');
      console.log(`📊 Found ${response.data.invoices.length} invoices`);
      console.log(`📄 Pagination: Page ${response.data.pagination.page} of ${response.data.pagination.pages}`);
      
      if (response.data.invoices.length > 0) {
        const firstInvoice = response.data.invoices[0];
        console.log(`📝 Sample invoice: ${firstInvoice.invoiceNumber} - ${firstInvoice.supplier.name}`);
      }
    } else {
      console.log('❌ Failed to retrieve invoice list');
      console.log('Error:', response.data);
    }
  } catch (error) {
    console.log('❌ Error testing invoice list:', error.message);
  }
}

async function testInvoiceSummary() {
  console.log('\n🧪 Testing Invoice Summary API...');
  
  try {
    const response = await apiRequest('/invoices/summary');
    
    if (response.ok) {
      console.log('✅ Invoice summary retrieved successfully');
      console.log('📊 Summary stats:');
      console.log(`   Total Invoices: ${response.data.totalInvoices}`);
      console.log(`   Pending: ${response.data.pendingInvoices}`);
      console.log(`   Approved: ${response.data.approvedInvoices}`);
      console.log(`   Paid: ${response.data.paidInvoices}`);
      console.log(`   Overdue: ${response.data.overdueInvoices}`);
      console.log(`   Total Amount: IDR ${response.data.totalAmount.toLocaleString()}`);
      console.log(`   Paid Amount: IDR ${response.data.paidAmount.toLocaleString()}`);
    } else {
      console.log('❌ Failed to retrieve invoice summary');
      console.log('Error:', response.data);
    }
  } catch (error) {
    console.log('❌ Error testing invoice summary:', error.message);
  }
}

async function testInvoiceDetail() {
  console.log('\n🧪 Testing Invoice Detail API...');
  
  try {
    // First get a list to find an invoice ID
    const listResponse = await apiRequest('/invoices?limit=1');
    
    if (!listResponse.ok || listResponse.data.invoices.length === 0) {
      console.log('❌ No invoices found for detail test');
      return;
    }

    const invoiceId = listResponse.data.invoices[0].id;
    const response = await apiRequest(`/invoices/${invoiceId}`);
    
    if (response.ok) {
      console.log('✅ Invoice detail retrieved successfully');
      console.log(`📝 Invoice: ${response.data.invoiceNumber}`);
      console.log(`🏢 Supplier: ${response.data.supplier.name}`);
      console.log(`📅 Date: ${new Date(response.data.invoiceDate).toLocaleDateString()}`);
      console.log(`💰 Total: IDR ${response.data.total.toLocaleString()}`);
      console.log(`📦 Items: ${response.data.items.length}`);
      console.log(`💳 Payments: ${response.data.payments.length}`);
    } else {
      console.log('❌ Failed to retrieve invoice detail');
      console.log('Error:', response.data);
    }
  } catch (error) {
    console.log('❌ Error testing invoice detail:', error.message);
  }
}

async function testInvoiceFiltering() {
  console.log('\n🧪 Testing Invoice Filtering...');
  
  try {
    // Test status filter
    const statusResponse = await apiRequest('/invoices?status=APPROVED');
    console.log(`✅ Status filter: Found ${statusResponse.data.invoices.length} approved invoices`);
    
    // Test payment status filter
    const paymentResponse = await apiRequest('/invoices?paymentStatus=PAID');
    console.log(`✅ Payment filter: Found ${paymentResponse.data.invoices.length} paid invoices`);
    
    // Test search
    const searchResponse = await apiRequest('/invoices?search=INV');
    console.log(`✅ Search filter: Found ${searchResponse.data.invoices.length} invoices matching 'INV'`);
    
  } catch (error) {
    console.log('❌ Error testing invoice filtering:', error.message);
  }
}

async function testInvoicePayments() {
  console.log('\n🧪 Testing Invoice Payments API...');
  
  try {
    // Get an approved invoice
    const listResponse = await apiRequest('/invoices?status=APPROVED&limit=1');
    
    if (!listResponse.ok || listResponse.data.invoices.length === 0) {
      console.log('❌ No approved invoices found for payment test');
      return;
    }

    const invoiceId = listResponse.data.invoices[0].id;
    const response = await apiRequest(`/invoices/${invoiceId}/payments`);
    
    if (response.ok) {
      console.log('✅ Invoice payments retrieved successfully');
      console.log(`💳 Found ${response.data.payments.length} payments`);
      console.log(`💰 Summary:`);
      console.log(`   Total: IDR ${response.data.summary.totalAmount.toLocaleString()}`);
      console.log(`   Paid: IDR ${response.data.summary.paidAmount.toLocaleString()}`);
      console.log(`   Remaining: IDR ${response.data.summary.remainingAmount.toLocaleString()}`);
    } else {
      console.log('❌ Failed to retrieve invoice payments');
      console.log('Error:', response.data);
    }
  } catch (error) {
    console.log('❌ Error testing invoice payments:', error.message);
  }
}

async function testCreateInvoice() {
  console.log('\n🧪 Testing Invoice Creation API...');
  
  try {
    // Get a received PO for testing
    const poResponse = await apiRequest('/purchase-orders?status=RECEIVED&limit=1');
    
    if (!poResponse.ok) {
      console.log('❌ Could not fetch purchase orders for invoice creation test');
      return;
    }

    // Note: This would require the PO API to exist and return data
    console.log('ℹ️  Invoice creation test requires a received purchase order');
    console.log('ℹ️  This test would create a new invoice from a PO');
    
  } catch (error) {
    console.log('❌ Error testing invoice creation:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting Invoice API Tests...');
  console.log('📝 Note: Make sure the development server is running on localhost:3000');
  
  if (!SESSION_TOKEN) {
    console.log('\n⚠️  No session token provided. Some tests may fail due to authentication.');
    console.log('💡 To get a session token:');
    console.log('   1. Open your browser and log into the NPOS system');
    console.log('   2. Open Developer Tools (F12)');
    console.log('   3. Go to Application/Storage > Cookies');
    console.log('   4. Copy the "session-token" value');
    console.log('   5. Set SESSION_TOKEN variable in this script');
    console.log('\n⏳ Proceeding with tests (some may fail)...');
  }

  await testInvoiceSummary();
  await testInvoiceList();
  await testInvoiceDetail();
  await testInvoiceFiltering();
  await testInvoicePayments();
  await testCreateInvoice();
  
  console.log('\n✅ All tests completed!');
  console.log('\n🌐 Manual Testing URLs:');
  console.log(`   Invoice List: ${BASE_URL}/invoices`);
  console.log(`   API Endpoints: ${API_BASE}/invoices`);
  console.log(`   Summary API: ${API_BASE}/invoices/summary`);
}

// Command line argument handling
if (process.argv.length > 2) {
  SESSION_TOKEN = process.argv[2];
  console.log('🔑 Using provided session token');
}

// Run tests
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests()
    .catch((error) => {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    });
}

export {
  testInvoiceList,
  testInvoiceSummary,
  testInvoiceDetail,
  testInvoiceFiltering,
  testInvoicePayments,
  runAllTests
};
