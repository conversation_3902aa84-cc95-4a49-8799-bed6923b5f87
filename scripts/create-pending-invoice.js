import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function createPendingInvoice() {
  try {
    console.log('🔧 Creating a PENDING invoice for testing Edit button...\n');

    // Get a supplier and user
    const supplier = await prisma.supplier.findFirst();
    const user = await prisma.user.findFirst();
    const product = await prisma.product.findFirst();

    if (!supplier || !user || !product) {
      console.log('❌ Missing required data (supplier, user, or product)');
      return;
    }

    // Generate unique invoice number
    const now = new Date();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const year = now.getFullYear();
    const timestamp = Date.now().toString().slice(-4);
    const invoiceNumber = `INV-${year}-${month}-TEST-${timestamp}`;

    // Create invoice data
    const subtotal = 1000000; // 1M IDR
    const taxPercentage = 11;
    const tax = subtotal * (taxPercentage / 100);
    const total = subtotal + tax;

    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        supplierId: supplier.id,
        invoiceDate: now,
        dueDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        subtotal: new Decimal(subtotal),
        tax: new Decimal(tax),
        taxPercentage: new Decimal(taxPercentage),
        total: new Decimal(total),
        status: 'PENDING', // This is the key - PENDING status
        paymentStatus: 'UNPAID',
        paidAmount: new Decimal(0), // Zero paid amount
        notes: 'Test invoice for Edit button functionality',
        createdById: user.id,
        items: {
          create: [
            {
              productId: product.id,
              description: product.name,
              quantity: new Decimal(10),
              unitPrice: new Decimal(100000),
              subtotal: new Decimal(1000000),
            }
          ]
        }
      },
      include: {
        supplier: true,
        items: {
          include: {
            product: true
          }
        }
      }
    });

    console.log('✅ Created PENDING invoice:');
    console.log(`   Invoice Number: ${invoice.invoiceNumber}`);
    console.log(`   Status: ${invoice.status}`);
    console.log(`   Paid Amount: ${Number(invoice.paidAmount)}`);
    console.log(`   Total: ${Number(invoice.total).toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`);
    console.log(`   Supplier: ${invoice.supplier.name}`);
    console.log(`   ID: ${invoice.id}`);
    console.log(`   Can Edit: ${invoice.status === 'PENDING' && Number(invoice.paidAmount) === 0 ? '✅ YES' : '❌ NO'}`);
    console.log('\n🎯 This invoice should show the Edit button in both list and detail pages!');

  } catch (error) {
    console.error('❌ Error creating pending invoice:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createPendingInvoice();
