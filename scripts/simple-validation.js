/**
 * Simple Migration Validation Script
 * 
 * This script performs basic validation of the migration status.
 */

import { PrismaClient } from './prisma-client.js';

const prisma = new PrismaClient();

/**
 * Log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Simple validation
 */
async function simpleValidation() {
  try {
    log('Running simple migration validation...');
    
    // Basic counts
    const totalProducts = await prisma.product.count();
    const productsWithOldSuppliers = 0; // No longer exists after migration
    const productSupplierCount = await prisma.productSupplier.count();
    
    log(`Total products: ${totalProducts}`);
    log(`Products with old suppliers: ${productsWithOldSuppliers}`);
    log(`ProductSupplier relationships: ${productSupplierCount}`);
    
    // Check preferred suppliers
    const preferredCount = await prisma.productSupplier.count({
      where: { isPreferred: true }
    });
    log(`Preferred supplier relationships: ${preferredCount}`);
    
    // Check products with suppliers
    const productsWithSuppliers = await prisma.product.findMany({
      where: {
        productSuppliers: {
          some: {}
        }
      },
      include: {
        productSuppliers: {
          include: {
            supplier: true
          }
        }
      }
    });
    
    log(`Products with new supplier relationships: ${productsWithSuppliers.length}`);
    
    // Check for issues
    let issues = 0;
    
    for (const product of productsWithSuppliers) {
      const preferredSuppliers = product.productSuppliers.filter(ps => ps.isPreferred);
      
      if (product.productSuppliers.length > 0 && preferredSuppliers.length === 0) {
        log(`WARNING: Product ${product.name} has no preferred supplier`, 'WARN');
        issues++;
      } else if (preferredSuppliers.length > 1) {
        log(`WARNING: Product ${product.name} has multiple preferred suppliers`, 'WARN');
        issues++;
      }
    }
    
    // Summary
    log('\n=== VALIDATION SUMMARY ===');
    log(`Migration needed: ${productsWithOldSuppliers > 0 ? 'YES' : 'NO'}`);
    log(`Schema cleanup ready: ${productsWithOldSuppliers === 0 ? 'YES' : 'NO'}`);
    log(`Issues found: ${issues}`);
    log(`Status: ${issues === 0 ? 'PASSED' : 'ISSUES FOUND'}`);
    
    return issues === 0;
    
  } catch (error) {
    log(`Validation failed: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    const success = await simpleValidation();
    
    if (!success) {
      process.exit(1);
    }
    
    log('Validation completed successfully!');
    
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'ERROR');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run validation
main();
