/**
 * Create Migration Script: Remove Old Supplier Fields
 * 
 * This script creates a Prisma migration to remove the old supplier fields
 * from the Product model after the data migration is complete.
 * 
 * IMPORTANT: Only run this AFTER the data migration script has been executed
 * and validated successfully.
 * 
 * Usage: node scripts/create-remove-supplier-migration.js
 */

import fs from 'fs';
import path from 'path';

/**
 * Log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Generate migration timestamp
 */
function generateMigrationTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hour = String(now.getHours()).padStart(2, '0');
  const minute = String(now.getMinutes()).padStart(2, '0');
  const second = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}${month}${day}${hour}${minute}${second}`;
}

/**
 * Create the migration SQL content
 */
function createMigrationSQL() {
  return `-- Migration: Remove legacy supplier fields from Product model
-- This migration removes the old direct supplier relationship fields
-- after data has been migrated to the ProductSupplier junction table.

-- Remove foreign key constraint first
ALTER TABLE "Product" DROP CONSTRAINT IF EXISTS "Product_supplierId_fkey";

-- Remove the supplier fields
ALTER TABLE "Product" DROP COLUMN IF EXISTS "supplierId";

-- Note: purchasePrice field is kept for backward compatibility
-- It can be used as a fallback when no ProductSupplier relationship exists
-- ALTER TABLE "Product" DROP COLUMN IF EXISTS "purchasePrice";

-- Add comment to track migration
COMMENT ON TABLE "Product" IS 'Updated: Removed legacy supplierId field, now uses ProductSupplier junction table';
`;
}

/**
 * Create the migration directory and file
 */
function createMigrationFile() {
  try {
    const timestamp = generateMigrationTimestamp();
    const migrationName = `${timestamp}_remove_legacy_supplier_fields`;
    const migrationsDir = path.join(process.cwd(), 'prisma', 'migrations');
    const migrationDir = path.join(migrationsDir, migrationName);
    
    // Create migration directory
    if (!fs.existsSync(migrationDir)) {
      fs.mkdirSync(migrationDir, { recursive: true });
      log(`Created migration directory: ${migrationDir}`);
    }
    
    // Create migration.sql file
    const migrationFile = path.join(migrationDir, 'migration.sql');
    const migrationSQL = createMigrationSQL();
    
    fs.writeFileSync(migrationFile, migrationSQL);
    log(`Created migration file: ${migrationFile}`);
    
    return {
      migrationName,
      migrationDir,
      migrationFile
    };
    
  } catch (error) {
    log(`Failed to create migration file: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Update the Prisma schema file
 */
function updatePrismaSchema() {
  try {
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    
    if (!fs.existsSync(schemaPath)) {
      throw new Error('Prisma schema file not found');
    }
    
    let schemaContent = fs.readFileSync(schemaPath, 'utf8');
    
    // Remove supplierId field and supplier relationship from Product model
    // We'll do this by replacing the specific lines
    
    // Remove supplierId field
    schemaContent = schemaContent.replace(
      /\s*supplierId\s+String\?\s*\n/g,
      ''
    );
    
    // Remove supplier relationship
    schemaContent = schemaContent.replace(
      /\s*supplier\s+Supplier\?\s+@relation\(fields:\s*\[supplierId\],\s*references:\s*\[id\]\)\s*\n/g,
      ''
    );
    
    // Create backup of original schema
    const backupPath = `${schemaPath}.backup.${Date.now()}`;
    fs.writeFileSync(backupPath, fs.readFileSync(schemaPath));
    log(`Created schema backup: ${backupPath}`);
    
    // Write updated schema
    fs.writeFileSync(schemaPath, schemaContent);
    log(`Updated Prisma schema: ${schemaPath}`);
    
    return {
      schemaPath,
      backupPath
    };
    
  } catch (error) {
    log(`Failed to update Prisma schema: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Display instructions for next steps
 */
function displayInstructions(migrationInfo, schemaInfo) {
  log('=== MIGRATION CREATED SUCCESSFULLY ===');
  log('');
  log('Migration Details:');
  log(`  Name: ${migrationInfo.migrationName}`);
  log(`  Directory: ${migrationInfo.migrationDir}`);
  log(`  SQL File: ${migrationInfo.migrationFile}`);
  log('');
  log('Schema Updates:');
  log(`  Updated: ${schemaInfo.schemaPath}`);
  log(`  Backup: ${schemaInfo.backupPath}`);
  log('');
  log('NEXT STEPS:');
  log('1. Review the generated migration SQL file');
  log('2. Test the migration on a development database:');
  log('   npx prisma migrate dev');
  log('3. Validate that the migration works correctly');
  log('4. Run the validation script:');
  log('   node scripts/validate-migration.js');
  log('5. If everything looks good, apply to production:');
  log('   npx prisma migrate deploy');
  log('');
  log('IMPORTANT NOTES:');
  log('- The purchasePrice field is kept for backward compatibility');
  log('- Make sure the data migration script ran successfully first');
  log('- Test thoroughly before applying to production');
  log('- Keep the schema backup file for rollback if needed');
  log('=====================================');
}

/**
 * Main execution
 */
async function main() {
  try {
    log('Creating migration to remove legacy supplier fields...');
    
    // Check if we're in the right directory
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('Please run this script from the project root directory');
    }
    
    // Create migration file
    const migrationInfo = createMigrationFile();
    
    // Update Prisma schema
    const schemaInfo = updatePrismaSchema();
    
    // Display instructions
    displayInstructions(migrationInfo, schemaInfo);
    
    log('Migration creation completed successfully!');
    
  } catch (error) {
    log(`Migration creation failed: ${error.message}`, 'ERROR');
    process.exit(1);
  }
}

// Show usage if help requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node scripts/create-remove-supplier-migration.js

This script creates a Prisma migration to remove the old supplier fields
from the Product model after data migration is complete.

IMPORTANT: Only run this AFTER the data migration script has been executed
and validated successfully.

Examples:
  node scripts/create-remove-supplier-migration.js
`);
  process.exit(0);
}

// Run migration creation
main();
