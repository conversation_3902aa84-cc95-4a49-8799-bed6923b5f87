import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkInvoiceData() {
  try {
    console.log('🔍 Checking current invoice data...\n');

    // Get all invoices with their basic info
    const invoices = await prisma.invoice.findMany({
      select: {
        id: true,
        invoiceNumber: true,
        status: true,
        paidAmount: true,
        total: true,
        supplier: {
          select: { name: true }
        },
        _count: {
          select: { payments: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📊 Found ${invoices.length} invoices:\n`);

    invoices.forEach((invoice, index) => {
      const paidAmount = Number(invoice.paidAmount);
      const total = Number(invoice.total);
      const hasPayments = invoice._count.payments > 0;
      const canEdit = invoice.status === 'PENDING' && paidAmount === 0;
      
      console.log(`${index + 1}. ${invoice.invoiceNumber}`);
      console.log(`   Status: ${invoice.status}`);
      console.log(`   Supplier: ${invoice.supplier.name}`);
      console.log(`   Total: ${total.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`);
      console.log(`   Paid: ${paidAmount.toLocaleString('id-ID', { style: 'currency', currency: 'IDR' })}`);
      console.log(`   Payments: ${invoice._count.payments}`);
      console.log(`   Can Edit: ${canEdit ? '✅ YES' : '❌ NO'}`);
      console.log(`   ID: ${invoice.id}`);
      console.log('');
    });

    // Summary
    const statusCounts = invoices.reduce((acc, inv) => {
      acc[inv.status] = (acc[inv.status] || 0) + 1;
      return acc;
    }, {});

    const editableCount = invoices.filter(inv => inv.status === 'PENDING' && Number(inv.paidAmount) === 0).length;

    console.log('📈 Summary:');
    console.log(`   Total invoices: ${invoices.length}`);
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${status}: ${count}`);
    });
    console.log(`   Editable (PENDING + no payments): ${editableCount}`);

  } catch (error) {
    console.error('❌ Error checking invoice data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkInvoiceData();
