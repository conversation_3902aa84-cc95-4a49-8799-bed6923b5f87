#!/usr/bin/env node

/**
 * Runner script for analytics test data generation
 * 
 * This script provides a simple interface to run the comprehensive
 * analytics test data generation with proper error handling and
 * progress reporting.
 * 
 * Usage:
 *   node scripts/run-analytics-data-generation.js
 *   npm run generate:analytics-data
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Starting Analytics Test Data Generation');
console.log('==========================================');
console.log('');

// Check if required dependencies are available
try {
  require('@prisma/client');
  require('@faker-js/faker');
} catch (error) {
  console.error('❌ Missing required dependencies. Please install them first:');
  console.error('   npm install @prisma/client @faker-js/faker');
  process.exit(1);
}

// Check if database is accessible
console.log('🔍 Checking database connection...');
try {
  execSync('npx prisma db push --accept-data-loss', { stdio: 'pipe' });
  console.log('✅ Database connection successful');
} catch (error) {
  console.error('❌ Database connection failed. Please check your DATABASE_URL and ensure the database is running.');
  console.error('Error:', error.message);
  process.exit(1);
}

console.log('');
console.log('📋 Generation Configuration:');
console.log('   • Purchase Orders: 50');
console.log('   • Suppliers: 8-12');
console.log('   • Time Period: Last 6 months');
console.log('   • Items per PO: 3-5');
console.log('   • Store Transfers: ~70% of batches');
console.log('   • Customer Transactions: 250');
console.log('   • Return Rate: ~8%');
console.log('   • Currency: Indonesian Rupiah (IDR)');
console.log('');

console.log('⚠️  WARNING: This will generate a large amount of test data.');
console.log('   Make sure you are running this on a development database.');
console.log('');

// Confirmation prompt (skip in CI environments)
if (!process.env.CI && !process.argv.includes('--force')) {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  rl.question('Do you want to continue? (y/N): ', (answer) => {
    rl.close();
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Operation cancelled by user.');
      process.exit(0);
    }
    
    runGeneration();
  });
} else {
  runGeneration();
}

function runGeneration() {
  console.log('🎯 Starting data generation...');
  console.log('');
  
  const startTime = Date.now();
  
  try {
    // Run the main generation script
    const scriptPath = path.join(__dirname, 'generate-analytics-test-data.cjs');
    execSync(`node "${scriptPath}"`, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    const endTime = Date.now();
    const duration = Math.round((endTime - startTime) / 1000);
    
    console.log('');
    console.log('🎉 Analytics test data generation completed successfully!');
    console.log(`⏱️  Total time: ${duration} seconds`);
    console.log('');
    console.log('📊 What was generated:');
    console.log('   ✅ Suppliers with realistic Indonesian names');
    console.log('   ✅ Product-Supplier relationships with pricing');
    console.log('   ✅ Purchase Orders with various statuses');
    console.log('   ✅ Stock Batches with FIFO logic');
    console.log('   ✅ Store Transfers (warehouse → store)');
    console.log('   ✅ Customer Transactions with realistic patterns');
    console.log('   ✅ Customer Returns with various reasons');
    console.log('   ✅ Stock History for audit trails');
    console.log('');
    console.log('🔍 You can now test the supplier performance analytics features:');
    console.log('   • Quality metrics (return rates)');
    console.log('   • Delivery performance (on-time delivery)');
    console.log('   • Pricing trends over time');
    console.log('   • Relationship health scoring');
    console.log('   • Cost analysis and comparisons');
    console.log('');
    console.log('🌐 Access the analytics at: /inventory/suppliers/analytics');
    
  } catch (error) {
    console.error('');
    console.error('❌ Data generation failed:');
    console.error(error.message);
    console.error('');
    console.error('💡 Troubleshooting tips:');
    console.error('   • Ensure your database is running and accessible');
    console.error('   • Check that you have run: npx prisma generate');
    console.error('   • Verify your DATABASE_URL environment variable');
    console.error('   • Make sure you have existing products (run seed scripts first)');
    console.error('');
    process.exit(1);
  }
}
