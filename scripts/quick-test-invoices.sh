#!/bin/bash

# Quick Invoice Testing Script
# This script runs the essential tests for invoice management

echo "🚀 Quick Invoice Management Test Suite"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if server is running
echo -e "\n${BLUE}1. Checking if development server is running...${NC}"
if curl -s http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ Server is running on localhost:3000${NC}"
else
    echo -e "${RED}❌ Server is not running. Please start with: npm run dev${NC}"
    exit 1
fi

# Check database connection
echo -e "\n${BLUE}2. Checking database connection...${NC}"
if npx prisma db pull > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Database connection successful${NC}"
else
    echo -e "${RED}❌ Database connection failed${NC}"
    exit 1
fi

# Apply schema changes
echo -e "\n${BLUE}3. Applying database schema...${NC}"
if npx prisma db push > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Schema applied successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Schema may already be up to date${NC}"
fi

# Seed test data
echo -e "\n${BLUE}4. Seeding test data...${NC}"
if node scripts/seed-invoice-test-data.js; then
    echo -e "${GREEN}✅ Test data seeded successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Test data seeding had issues (may already exist)${NC}"
fi

# Test APIs
echo -e "\n${BLUE}5. Testing APIs...${NC}"
if node scripts/test-invoice-apis.js; then
    echo -e "${GREEN}✅ API tests completed${NC}"
else
    echo -e "${YELLOW}⚠️  Some API tests may have failed (check authentication)${NC}"
fi

# Test frontend accessibility
echo -e "\n${BLUE}6. Testing frontend accessibility...${NC}"
if curl -s http://localhost:3000/invoices > /dev/null; then
    echo -e "${GREEN}✅ Invoice page is accessible${NC}"
else
    echo -e "${RED}❌ Invoice page is not accessible${NC}"
fi

# Summary
echo -e "\n${GREEN}🎉 Quick test suite completed!${NC}"
echo -e "\n${BLUE}📋 Next Steps:${NC}"
echo "1. Open browser and go to: http://localhost:3000/invoices"
echo "2. Log in with admin credentials"
echo "3. Test the invoice management interface"
echo "4. Check the browser console for any errors"

echo -e "\n${BLUE}🔧 Manual Testing:${NC}"
echo "- Test invoice filtering and search"
echo "- Test responsive design on different screen sizes"
echo "- Test invoice detail views (when implemented)"
echo "- Test payment recording (when implemented)"

echo -e "\n${BLUE}📚 Documentation:${NC}"
echo "- Full testing guide: docs/invoice-testing-guide.md"
echo "- Implementation details: docs/invoice-management-implementation.md"

echo -e "\n${GREEN}✨ Happy testing!${NC}"
