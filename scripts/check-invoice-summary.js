import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkInvoiceSummary() {
  try {
    console.log('🔍 Checking invoice summary data...\n');

    // Get basic invoice counts
    const totalInvoices = await prisma.invoice.count();
    const pendingInvoices = await prisma.invoice.count({ where: { status: 'PENDING' } });
    const approvedInvoices = await prisma.invoice.count({ where: { status: 'APPROVED' } });
    const paidInvoices = await prisma.invoice.count({ where: { paymentStatus: 'PAID' } });
    const overdueInvoices = await prisma.invoice.count({ where: { paymentStatus: 'OVERDUE' } });
    const unpaidInvoices = await prisma.invoice.count({ where: { paymentStatus: 'UNPAID' } });

    console.log('📊 Invoice Counts:');
    console.log(`  Total Invoices: ${totalInvoices}`);
    console.log(`  Pending: ${pendingInvoices}`);
    console.log(`  Approved: ${approvedInvoices}`);
    console.log(`  Paid: ${paidInvoices}`);
    console.log(`  Unpaid: ${unpaidInvoices}`);
    console.log(`  Overdue: ${overdueInvoices}\n`);

    // Get amount totals
    const totalAmount = await prisma.invoice.aggregate({
      _sum: { total: true }
    });
    const paidAmount = await prisma.invoice.aggregate({
      _sum: { paidAmount: true }
    });

    const totalValue = Number(totalAmount._sum.total || 0);
    const paidValue = Number(paidAmount._sum.paidAmount || 0);
    const outstandingValue = totalValue - paidValue;

    console.log('💰 Financial Summary:');
    console.log(`  Total Amount: IDR ${totalValue.toLocaleString()}`);
    console.log(`  Paid Amount: IDR ${paidValue.toLocaleString()}`);
    console.log(`  Outstanding: IDR ${outstandingValue.toLocaleString()}\n`);

    // Get closest due date
    const closestDue = await prisma.invoice.findFirst({
      where: {
        dueDate: { not: null },
        paymentStatus: { in: ['UNPAID', 'PARTIALLY_PAID', 'OVERDUE'] }
      },
      select: {
        invoiceNumber: true,
        dueDate: true,
        total: true,
        paymentStatus: true
      },
      orderBy: {
        dueDate: 'asc'
      }
    });

    if (closestDue) {
      const daysUntilDue = Math.ceil((new Date(closestDue.dueDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
      console.log('📅 Next Due Invoice:');
      console.log(`  Invoice: ${closestDue.invoiceNumber}`);
      console.log(`  Due Date: ${closestDue.dueDate.toISOString().split('T')[0]}`);
      console.log(`  Days Until Due: ${daysUntilDue}`);
      console.log(`  Amount: IDR ${Number(closestDue.total).toLocaleString()}`);
      console.log(`  Status: ${closestDue.paymentStatus}\n`);
    } else {
      console.log('📅 No pending invoices with due dates\n');
    }

    // Show some sample invoices
    const sampleInvoices = await prisma.invoice.findMany({
      take: 5,
      select: {
        invoiceNumber: true,
        status: true,
        paymentStatus: true,
        total: true,
        paidAmount: true,
        dueDate: true,
        supplier: {
          select: { name: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    if (sampleInvoices.length > 0) {
      console.log('📋 Sample Invoices:');
      sampleInvoices.forEach((invoice, index) => {
        console.log(`  ${index + 1}. ${invoice.invoiceNumber} - ${invoice.supplier.name}`);
        console.log(`     Status: ${invoice.status} | Payment: ${invoice.paymentStatus}`);
        console.log(`     Total: IDR ${Number(invoice.total).toLocaleString()} | Paid: IDR ${Number(invoice.paidAmount).toLocaleString()}`);
        if (invoice.dueDate) {
          console.log(`     Due: ${invoice.dueDate.toISOString().split('T')[0]}`);
        }
        console.log('');
      });
    } else {
      console.log('📋 No invoices found in the system\n');
    }

  } catch (error) {
    console.error('❌ Error checking invoice summary:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkInvoiceSummary();
