#!/usr/bin/env node

/**
 * Data Migration Script: Fix Batch Location Associations
 * 
 * This script fixes the issue where existing batches don't have proper
 * warehouseStockId or storeStockId associations, causing:
 * 1. Missing location badges in the batch tracking page
 * 2. Incorrect batch counts in stock management page
 * 
 * The script will:
 * 1. Find all batches without location associations
 * 2. Create or find appropriate stock records for each product
 * 3. Link batches to warehouse stock by default (can be customized)
 * 4. Update batch records with proper location IDs
 */

import { PrismaClient } from '../src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function main() {
  console.log('🔧 Starting batch location fix...');
  
  try {
    // Find all batches without location associations
    const orphanedBatches = await prisma.stockBatch.findMany({
      where: {
        AND: [
          { warehouseStockId: null },
          { storeStockId: null }
        ]
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true
          }
        }
      }
    });

    console.log(`📊 Found ${orphanedBatches.length} batches without location associations`);

    if (orphanedBatches.length === 0) {
      console.log('✅ No orphaned batches found. All batches are properly linked.');
      return;
    }

    // Group batches by product
    const batchesByProduct = orphanedBatches.reduce((acc, batch) => {
      const productId = batch.productId;
      if (!acc[productId]) {
        acc[productId] = {
          product: batch.product,
          batches: []
        };
      }
      acc[productId].batches.push(batch);
      return acc;
    }, {});

    console.log(`📦 Processing ${Object.keys(batchesByProduct).length} products...`);

    let totalFixed = 0;

    // Process each product
    for (const [productId, data] of Object.entries(batchesByProduct)) {
      const { product, batches } = data;
      
      console.log(`\n🔄 Processing ${product.name} (${batches.length} batches)...`);

      // Find or create warehouse stock record
      let warehouseStock = await prisma.warehouseStock.findUnique({
        where: { productId }
      });

      if (!warehouseStock) {
        console.log(`  📝 Creating warehouse stock record for ${product.name}...`);
        warehouseStock = await prisma.warehouseStock.create({
          data: {
            productId,
            quantity: 0, // Will be updated by the batch linking
            minThreshold: 0,
            maxThreshold: null
          }
        });
      }

      // Update all batches for this product to link to warehouse stock
      const batchIds = batches.map(b => b.id);
      
      const updateResult = await prisma.stockBatch.updateMany({
        where: {
          id: { in: batchIds }
        },
        data: {
          warehouseStockId: warehouseStock.id
        }
      });

      console.log(`  ✅ Linked ${updateResult.count} batches to warehouse stock`);
      totalFixed += updateResult.count;

      // Update warehouse stock quantity to match total batch quantities
      const totalQuantity = batches.reduce((sum, batch) => {
        return sum + parseFloat(batch.remainingQuantity.toString());
      }, 0);

      await prisma.warehouseStock.update({
        where: { id: warehouseStock.id },
        data: {
          quantity: totalQuantity,
          lastUpdated: new Date()
        }
      });

      console.log(`  📊 Updated warehouse stock quantity to ${totalQuantity}`);
    }

    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`📈 Summary:`);
    console.log(`   - Products processed: ${Object.keys(batchesByProduct).length}`);
    console.log(`   - Batches fixed: ${totalFixed}`);
    console.log(`   - All batches now linked to warehouse stock`);
    
    console.log(`\n✨ Location badges should now appear in the batch tracking page!`);

  } catch (error) {
    console.error('❌ Error during migration:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
main()
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
