/**
 * Fix Preferred Suppliers Script
 * 
 * This script fixes the issue where products have supplier relationships
 * but no preferred supplier is designated.
 * 
 * Usage: node scripts/fix-preferred-suppliers.js
 */

import { PrismaClient } from './prisma-client.js';

const prisma = new PrismaClient();

// Fix statistics
const stats = {
  totalProducts: 0,
  productsFixed: 0,
  productsSkipped: 0,
  errors: 0
};

// Options
const DRY_RUN = process.argv.includes('--dry-run');

/**
 * Log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Fix preferred suppliers for products
 */
async function fixPreferredSuppliers() {
  log('Starting preferred supplier fix...');
  log(`Mode: ${DRY_RUN ? 'DRY RUN' : 'LIVE FIX'}`);
  
  try {
    // Get all products with supplier relationships
    const products = await prisma.product.findMany({
      where: {
        productSuppliers: {
          some: {}
        }
      },
      include: {
        productSuppliers: {
          where: {
            isActive: true
          },
          include: {
            supplier: true
          }
        }
      }
    });
    
    stats.totalProducts = products.length;
    log(`Found ${stats.totalProducts} products with supplier relationships`);
    
    if (stats.totalProducts === 0) {
      log('No products with supplier relationships found.');
      return;
    }
    
    // Process each product
    for (const product of products) {
      try {
        const preferredSuppliers = product.productSuppliers.filter(ps => ps.isPreferred);
        
        if (preferredSuppliers.length === 1) {
          // Already has exactly one preferred supplier
          stats.productsSkipped++;
          log(`✓ Product ${product.name} already has preferred supplier: ${preferredSuppliers[0].supplier.name}`);
          continue;
        }
        
        if (preferredSuppliers.length > 1) {
          // Multiple preferred suppliers - fix by keeping the first one
          log(`Fixing product ${product.name} - has ${preferredSuppliers.length} preferred suppliers`);
          
          if (!DRY_RUN) {
            // Set all to non-preferred first
            await prisma.productSupplier.updateMany({
              where: {
                productId: product.id,
                isActive: true
              },
              data: {
                isPreferred: false
              }
            });
            
            // Set the first one as preferred (could use other logic like lowest price, etc.)
            await prisma.productSupplier.update({
              where: {
                productId_supplierId: {
                  productId: product.id,
                  supplierId: preferredSuppliers[0].supplierId
                }
              },
              data: {
                isPreferred: true
              }
            });
            
            log(`✓ Fixed: Set ${preferredSuppliers[0].supplier.name} as preferred supplier for ${product.name}`);
          } else {
            log(`DRY RUN: Would set ${preferredSuppliers[0].supplier.name} as preferred supplier for ${product.name}`);
          }
          
          stats.productsFixed++;
          continue;
        }
        
        if (preferredSuppliers.length === 0) {
          // No preferred supplier - set the first one as preferred
          if (product.productSuppliers.length > 0) {
            const supplierToPrefer = product.productSuppliers[0];
            
            log(`Fixing product ${product.name} - no preferred supplier, setting ${supplierToPrefer.supplier.name} as preferred`);
            
            if (!DRY_RUN) {
              await prisma.productSupplier.update({
                where: {
                  productId_supplierId: {
                    productId: product.id,
                    supplierId: supplierToPrefer.supplierId
                  }
                },
                data: {
                  isPreferred: true
                }
              });
              
              log(`✓ Fixed: Set ${supplierToPrefer.supplier.name} as preferred supplier for ${product.name}`);
            } else {
              log(`DRY RUN: Would set ${supplierToPrefer.supplier.name} as preferred supplier for ${product.name}`);
            }
            
            stats.productsFixed++;
          }
        }
        
      } catch (error) {
        stats.errors++;
        log(`ERROR fixing product ${product.name}: ${error.message}`, 'ERROR');
      }
    }
    
    log('Preferred supplier fix completed!');
    
  } catch (error) {
    log(`Fix failed: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Print fix statistics
 */
function printStats() {
  log('=== Fix Statistics ===');
  log(`Total products: ${stats.totalProducts}`);
  log(`Products fixed: ${stats.productsFixed}`);
  log(`Products skipped: ${stats.productsSkipped}`);
  log(`Errors: ${stats.errors}`);
  log('======================');
}

/**
 * Main execution
 */
async function main() {
  try {
    await fixPreferredSuppliers();
    printStats();
    
    if (stats.errors > 0) {
      process.exit(1);
    }
    
    if (!DRY_RUN && stats.productsFixed > 0) {
      log('Fix completed successfully! Run validation script to verify.');
    } else if (DRY_RUN) {
      log('Dry run completed. Use without --dry-run to apply fixes.');
    }
    
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'ERROR');
    printStats();
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Show usage if help requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node scripts/fix-preferred-suppliers.js [options]

Options:
  --dry-run         Simulate fix without making changes
  --help, -h        Show this help message

Examples:
  node scripts/fix-preferred-suppliers.js --dry-run
  node scripts/fix-preferred-suppliers.js
`);
  process.exit(0);
}

// Run fix
main();
