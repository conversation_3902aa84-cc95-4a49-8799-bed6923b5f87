// Comprehensive data generation script for supplier performance analytics testing
// This script generates realistic test data including POs, batches, transfers, transactions, and returns

const { PrismaClient } = require('@prisma/client');
const { faker } = require('@faker-js/faker');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// Configuration
const CONFIG = {
  PURCHASE_ORDERS: 50,
  SUPPLIERS_MIN: 8,
  SUPPLIERS_MAX: 12,
  MONTHS_BACK: 6,
  ITEMS_PER_PO_MIN: 3,
  ITEMS_PER_PO_MAX: 5,
  DELIVERY_DELAY_MIN: 3,
  DELIVERY_DELAY_MAX: 14,
  TRANSFER_PERCENTAGE: 0.7, // 70% of batches get transferred to store
  TRANSACTIONS_COUNT: 250,
  RETURN_RATE: 0.08, // 8% return rate
  IDR_EXCHANGE_RATE: 100, // Simplified IDR conversion for database constraints
};

// Return reasons for realistic data
const RETURN_REASONS = [
  'Defective',
  'Wrong item',
  'Customer changed mind',
  'Damaged',
  'Expired',
  'Poor quality',
  'Not as described',
  'Size issue'
];

// Supplier names for Indonesian context
const SUPPLIER_NAMES = [
  'PT Sumber Makmur',
  'CV Berkah Jaya',
  'PT Indo Distribusi',
  'Toko Grosir Sentral',
  'PT Mitra Sukses',
  'CV Cahaya Abadi',
  'PT Global Trading',
  'Supplier Nusantara',
  'PT Karya Mandiri',
  'CV Sejahtera Bersama',
  'PT Prima Utama',
  'Distributor Andalas'
];

// Helper functions
function getRandomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function getRandomPurchasePrice(basePrice, minPercent, maxPercent) {
  const percentage = faker.number.float({ min: minPercent, max: maxPercent, precision: 0.01 });
  const purchasePrice = basePrice * percentage;
  // Ensure the price fits within database constraints (max 99,999,999.99)
  return Math.min(Math.round(purchasePrice * 100) / 100, 9999999.99);
}

function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

function addMonths(date, months) {
  const result = new Date(date);
  result.setMonth(result.getMonth() + months);
  return result;
}

// Progress logging
function logProgress(message, current = null, total = null) {
  const timestamp = new Date().toISOString().substring(11, 19);
  const progress = current && total ? ` (${current}/${total})` : '';
  console.log(`[${timestamp}] ${message}${progress}`);
}

async function main() {
  try {
    logProgress('🚀 Starting comprehensive analytics test data generation...');
    
    // Get existing data
    logProgress('📊 Fetching existing database entities...');
    const users = await prisma.user.findMany();
    const categories = await prisma.category.findMany();
    const units = await prisma.unit.findMany();
    const products = await prisma.product.findMany({ include: { category: true, unit: true } });
    
    if (products.length === 0) {
      throw new Error('No products found. Please run the product seeding script first.');
    }
    
    logProgress(`Found ${products.length} products, ${categories.length} categories, ${units.length} units`);
    
    // Get or create admin user
    let adminUser = users.find(u => u.role === 'SUPER_ADMIN');
    if (!adminUser) {
      logProgress('Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      adminUser = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'SUPER_ADMIN',
          active: true,
        }
      });
    }
    
    // Get or create cashier user
    let cashierUser = users.find(u => u.role === 'CASHIER');
    if (!cashierUser) {
      logProgress('Creating cashier user...');
      const hashedPassword = await bcrypt.hash('cashier123', 10);
      cashierUser = await prisma.user.create({
        data: {
          name: 'Cashier User',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'CASHIER',
          active: true,
        }
      });
    }
    
    // Create suppliers if needed
    logProgress('🏢 Setting up suppliers...');
    let suppliers = await prisma.supplier.findMany();
    const suppliersNeeded = Math.max(0, CONFIG.SUPPLIERS_MIN - suppliers.length);
    
    for (let i = 0; i < suppliersNeeded; i++) {
      const supplierName = SUPPLIER_NAMES[i % SUPPLIER_NAMES.length] + (i >= SUPPLIER_NAMES.length ? ` ${Math.floor(i / SUPPLIER_NAMES.length) + 1}` : '');
      const supplier = await prisma.supplier.create({
        data: {
          name: supplierName,
          contactPerson: faker.person.fullName(),
          phone: faker.phone.number('+62 ###-####-####'),
          email: faker.internet.email(),
          address: `${faker.location.streetAddress()}, ${faker.location.city()}, Indonesia`,
          isActive: true,
        }
      });
      suppliers.push(supplier);
      logProgress(`Created supplier: ${supplier.name}`);
    }
    
    // Limit suppliers to max count
    suppliers = suppliers.slice(0, CONFIG.SUPPLIERS_MAX);
    logProgress(`Using ${suppliers.length} suppliers for data generation`);
    
    // Create ProductSupplier relationships
    logProgress('🔗 Creating product-supplier relationships...');
    const productSuppliers = [];

    for (const product of products) {
      // Each product will have 1-3 suppliers
      const supplierCount = faker.number.int({ min: 1, max: 3 });
      const selectedSuppliers = faker.helpers.arrayElements(suppliers, supplierCount);

      for (let i = 0; i < selectedSuppliers.length; i++) {
        const supplier = selectedSuppliers[i];
        const basePrice = parseFloat(product.basePrice);
        const purchasePrice = getRandomPurchasePrice(basePrice, 0.6, 0.8);

        try {
          const productSupplier = await prisma.productSupplier.create({
            data: {
              productId: product.id,
              supplierId: supplier.id,
              supplierProductCode: `${supplier.name.substring(0, 3).toUpperCase()}-${product.sku}`,
              supplierProductName: `${supplier.name} ${product.name}`,
              purchasePrice: purchasePrice,
              minimumOrderQuantity: faker.number.int({ min: 5, max: 50 }),
              leadTimeDays: faker.number.int({ min: 1, max: 14 }),
              isPreferred: i === 0, // First supplier is preferred
              isActive: true,
              notes: faker.lorem.sentence(),
            }
          });
          productSuppliers.push(productSupplier);
        } catch (error) {
          // Skip if relationship already exists
          if (!error.message.includes('Unique constraint')) {
            console.error(`Error creating product-supplier relationship: ${error.message}`);
          }
        }
      }
    }

    logProgress(`Created ${productSuppliers.length} product-supplier relationships`);

    // Generate date range for the last 6 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - CONFIG.MONTHS_BACK);

    logProgress(`📅 Generating data from ${startDate.toDateString()} to ${endDate.toDateString()}`);

    // Generate Purchase Orders
    const purchaseOrders = await generatePurchaseOrders(suppliers, productSuppliers, startDate, endDate, adminUser);

    // Generate Stock Batches for received POs
    const stockBatches = await generateStockBatches(purchaseOrders, productSuppliers, adminUser);

    // Generate Store Transfers
    await generateStoreTransfers(stockBatches, adminUser);

    // Generate Customer Transactions and Returns
    await generateCustomerTransactions(stockBatches, cashierUser, startDate, endDate);

    logProgress('✅ Analytics test data generation completed successfully!');
    
  } catch (error) {
    console.error('❌ Error during data generation:', error);
    throw error;
  }
}

// Helper function to generate Purchase Orders
async function generatePurchaseOrders(suppliers, productSuppliers, startDate, endDate, adminUser) {
  logProgress('📋 Generating Purchase Orders...');
  const purchaseOrders = [];

  for (let i = 0; i < CONFIG.PURCHASE_ORDERS; i++) {
    const supplier = faker.helpers.arrayElement(suppliers);
    const orderDate = getRandomDate(startDate, endDate);

    // Get products available from this supplier
    const availableProductSuppliers = productSuppliers.filter(ps => ps.supplierId === supplier.id);
    if (availableProductSuppliers.length === 0) continue;

    const maxItems = Math.min(CONFIG.ITEMS_PER_PO_MAX, availableProductSuppliers.length);
    const minItems = Math.min(CONFIG.ITEMS_PER_PO_MIN, maxItems);
    const itemCount = faker.number.int({
      min: minItems,
      max: maxItems
    });

    const selectedProductSuppliers = faker.helpers.arrayElements(availableProductSuppliers, itemCount);

    let subtotal = 0;
    const items = selectedProductSuppliers.map(ps => {
      const quantity = faker.number.int({ min: 10, max: 100 });
      const unitPrice = ps.purchasePrice;
      const itemSubtotal = quantity * unitPrice;
      subtotal += itemSubtotal;

      return {
        productId: ps.productId,
        productSupplierId: ps.id,
        quantity: quantity,
        unitPrice: unitPrice,
        subtotal: itemSubtotal,
      };
    });

    const taxPercentage = faker.number.float({ min: 0, max: 11, precision: 0.5 });
    const tax = subtotal * (taxPercentage / 100);
    const total = subtotal + tax;

    // Determine delivery timing and status
    const deliveryDelay = faker.number.int({
      min: CONFIG.DELIVERY_DELAY_MIN,
      max: CONFIG.DELIVERY_DELAY_MAX
    });
    const expectedDeliveryDate = addDays(orderDate, deliveryDelay);
    const actualDeliveryDate = addDays(orderDate, deliveryDelay + faker.number.int({ min: -2, max: 7 }));

    // Determine PO status based on dates
    let status = 'RECEIVED';
    let receivedAt = actualDeliveryDate;
    let approvedAt = addDays(orderDate, faker.number.int({ min: 0, max: 2 }));
    let orderedAt = addDays(approvedAt, faker.number.int({ min: 0, max: 1 }));

    // Some POs might still be pending/approved (10%)
    if (faker.number.float() < 0.1 && actualDeliveryDate > new Date()) {
      status = faker.helpers.arrayElement(['PENDING_APPROVAL', 'APPROVED', 'ORDERED']);
      receivedAt = null;
      if (status === 'PENDING_APPROVAL') {
        approvedAt = null;
        orderedAt = null;
      } else if (status === 'APPROVED') {
        orderedAt = null;
      }
    }

    try {
      const purchaseOrder = await prisma.purchaseOrder.create({
        data: {
          orderDate: orderDate,
          createdById: adminUser.id,
          supplierId: supplier.id,
          subtotal: subtotal,
          tax: tax,
          taxPercentage: taxPercentage,
          total: total,
          status: status,
          approvedById: status !== 'PENDING_APPROVAL' ? adminUser.id : null,
          approvedAt: approvedAt,
          orderedAt: orderedAt,
          expectedDeliveryDate: expectedDeliveryDate,
          receivedAt: receivedAt,
          notes: faker.lorem.sentence(),
          items: {
            create: items
          }
        },
        include: {
          items: true
        }
      });

      purchaseOrders.push(purchaseOrder);
      logProgress(`Created PO ${i + 1}`, i + 1, CONFIG.PURCHASE_ORDERS);
    } catch (error) {
      console.error(`Error creating purchase order ${i + 1}: ${error.message}`);
    }
  }

  logProgress(`✅ Created ${purchaseOrders.length} purchase orders`);
  return purchaseOrders;
}

// Helper function to generate Stock Batches
async function generateStockBatches(purchaseOrders, productSuppliers, adminUser) {
  logProgress('📦 Generating stock batches...');
  const stockBatches = [];

  for (const po of purchaseOrders) {
    if (po.status === 'RECEIVED' && po.receivedAt) {
      for (const item of po.items) {
        const productSupplier = productSuppliers.find(ps => ps.id === item.productSupplierId);
        if (!productSupplier) continue;

        const batchNumber = `BATCH-${po.id.substring(0, 8)}-${item.productId.substring(0, 4)}`;
        const expiryDate = addMonths(po.receivedAt, faker.number.int({ min: 6, max: 24 }));

        // Create warehouse stock entry if it doesn't exist
        let warehouseStock = await prisma.warehouseStock.findUnique({
          where: { productId: item.productId }
        });

        if (!warehouseStock) {
          warehouseStock = await prisma.warehouseStock.create({
            data: {
              productId: item.productId,
              quantity: 0,
              minThreshold: faker.number.int({ min: 10, max: 50 }),
              maxThreshold: faker.number.int({ min: 100, max: 500 }),
            }
          });
        }

        try {
          const stockBatch = await prisma.stockBatch.create({
            data: {
              productId: item.productId,
              productSupplierId: productSupplier.id,
              batchNumber: batchNumber,
              receivedDate: po.receivedAt,
              expiryDate: expiryDate,
              quantity: item.quantity,
              remainingQuantity: item.quantity,
              purchasePrice: item.unitPrice,
              purchaseOrderId: po.id,
              warehouseStockId: warehouseStock.id,
              status: 'ACTIVE',
              notes: `Batch from PO ${po.id}`,
            }
          });

          stockBatches.push(stockBatch);

          // Update warehouse stock quantity
          await prisma.warehouseStock.update({
            where: { id: warehouseStock.id },
            data: {
              quantity: {
                increment: item.quantity
              },
              lastUpdated: po.receivedAt,
            }
          });

          // Create stock history
          await prisma.stockHistory.create({
            data: {
              productId: item.productId,
              productSupplierId: productSupplier.id,
              batchId: stockBatch.id,
              warehouseStockId: warehouseStock.id,
              previousQuantity: 0,
              newQuantity: item.quantity,
              changeQuantity: item.quantity,
              source: 'PURCHASE',
              referenceId: po.id,
              referenceType: 'PurchaseOrder',
              notes: `Stock received from PO ${po.id}`,
              userId: adminUser.id,
              date: po.receivedAt,
            }
          });

        } catch (error) {
          console.error(`Error creating stock batch for PO ${po.id}: ${error.message}`);
        }
      }
    }
  }

  logProgress(`✅ Created ${stockBatches.length} stock batches`);
  return stockBatches;
}

// Helper function to generate Store Transfers
async function generateStoreTransfers(stockBatches, adminUser) {
  logProgress('🔄 Generating store transfers...');
  let transferCount = 0;

  for (const batch of stockBatches) {
    // Transfer 70% of batches to store
    if (faker.number.float() < CONFIG.TRANSFER_PERCENTAGE) {
      const transferQuantity = faker.number.int({
        min: Math.ceil(batch.remainingQuantity * 0.3),
        max: Math.ceil(batch.remainingQuantity * 0.8)
      });

      if (transferQuantity <= 0) continue;

      // Create store stock entry if it doesn't exist
      let storeStock = await prisma.storeStock.findUnique({
        where: { productId: batch.productId }
      });

      if (!storeStock) {
        storeStock = await prisma.storeStock.create({
          data: {
            productId: batch.productId,
            quantity: 0,
            minThreshold: faker.number.int({ min: 5, max: 20 }),
            maxThreshold: faker.number.int({ min: 50, max: 200 }),
          }
        });
      }

      const transferDate = addDays(batch.receivedDate, faker.number.int({ min: 1, max: 7 }));

      try {
        // Create new batch for store
        const storeBatch = await prisma.stockBatch.create({
          data: {
            productId: batch.productId,
            productSupplierId: batch.productSupplierId,
            batchNumber: batch.batchNumber, // Same batch number
            receivedDate: batch.receivedDate,
            expiryDate: batch.expiryDate,
            quantity: transferQuantity,
            remainingQuantity: transferQuantity,
            purchasePrice: batch.purchasePrice,
            purchaseOrderId: batch.purchaseOrderId,
            storeStockId: storeStock.id,
            status: 'ACTIVE',
            notes: `Transferred from warehouse batch ${batch.id}`,
          }
        });

        // Update original warehouse batch
        await prisma.stockBatch.update({
          where: { id: batch.id },
          data: {
            remainingQuantity: {
              decrement: transferQuantity
            }
          }
        });

        // Update warehouse stock
        await prisma.warehouseStock.update({
          where: { id: batch.warehouseStockId },
          data: {
            quantity: {
              decrement: transferQuantity
            },
            lastUpdated: transferDate,
          }
        });

        // Update store stock
        await prisma.storeStock.update({
          where: { id: storeStock.id },
          data: {
            quantity: {
              increment: transferQuantity
            },
            lastUpdated: transferDate,
          }
        });

        // Create stock history for transfer
        await prisma.stockHistory.create({
          data: {
            productId: batch.productId,
            productSupplierId: batch.productSupplierId,
            batchId: storeBatch.id,
            storeStockId: storeStock.id,
            previousQuantity: 0,
            newQuantity: transferQuantity,
            changeQuantity: transferQuantity,
            source: 'TRANSFER',
            referenceId: batch.id,
            referenceType: 'StockTransfer',
            notes: `Transferred from warehouse batch ${batch.id}`,
            userId: adminUser.id,
            date: transferDate,
          }
        });

        transferCount++;
      } catch (error) {
        console.error(`Error creating store transfer for batch ${batch.id}: ${error.message}`);
      }
    }
  }

  logProgress(`✅ Created ${transferCount} store transfers`);
}

// Helper function to generate Customer Transactions and Returns
async function generateCustomerTransactions(stockBatches, cashierUser, startDate, endDate) {
  logProgress('🛒 Generating customer transactions...');

  // Get store batches only
  const storeBatches = await prisma.stockBatch.findMany({
    where: {
      storeStockId: { not: null },
      remainingQuantity: { gt: 0 }
    },
    include: {
      product: true
    }
  });

  if (storeBatches.length === 0) {
    logProgress('No store batches available for transactions');
    return;
  }

  const transactions = [];
  const returns = [];

  for (let i = 0; i < CONFIG.TRANSACTIONS_COUNT; i++) {
    const transactionDate = getRandomDate(startDate, endDate);

    // Select 1-5 random products for this transaction
    const itemCount = faker.number.int({ min: 1, max: 5 });
    const availableBatches = storeBatches.filter(b =>
      b.remainingQuantity > 0 &&
      new Date(b.receivedDate) <= transactionDate
    );

    if (availableBatches.length === 0) continue;

    const selectedBatches = faker.helpers.arrayElements(
      availableBatches,
      Math.min(itemCount, availableBatches.length)
    );

    let subtotal = 0;
    const transactionItems = [];

    for (const batch of selectedBatches) {
      const maxQuantity = Math.min(batch.remainingQuantity, 10);
      const quantity = faker.number.int({ min: 1, max: maxQuantity });
      const unitPrice = parseFloat(batch.product.basePrice);
      const itemSubtotal = quantity * unitPrice;
      subtotal += itemSubtotal;

      transactionItems.push({
        productId: batch.productId,
        batchId: batch.id,
        quantity: quantity,
        unitPrice: unitPrice,
        subtotal: itemSubtotal,
      });
    }

    const discount = faker.number.float({ min: 0, max: subtotal * 0.1 });
    const tax = (subtotal - discount) * 0.11; // 11% tax
    const total = subtotal - discount + tax;

    try {
      const transaction = await prisma.transaction.create({
        data: {
          transactionDate: transactionDate,
          cashierId: cashierUser.id,
          subtotal: subtotal,
          discount: discount,
          tax: tax,
          total: total,
          paymentMethod: faker.helpers.arrayElement(['CASH', 'DEBIT', 'QRIS']),
          paymentStatus: 'PAID',
          status: 'COMPLETED',
          cashReceived: total + faker.number.float({ min: 0, max: 50000 }),
          changeAmount: faker.number.float({ min: 0, max: 50000 }),
          notes: faker.lorem.sentence(),
          items: {
            create: transactionItems
          }
        },
        include: {
          items: true
        }
      });

      transactions.push(transaction);

      // Update batch quantities and create stock history
      for (const item of transactionItems) {
        await prisma.stockBatch.update({
          where: { id: item.batchId },
          data: {
            remainingQuantity: {
              decrement: item.quantity
            }
          }
        });

        // Update store stock
        const batch = storeBatches.find(b => b.id === item.batchId);
        await prisma.storeStock.update({
          where: { id: batch.storeStockId },
          data: {
            quantity: {
              decrement: item.quantity
            },
            lastUpdated: transactionDate,
          }
        });

        // Create stock history
        await prisma.stockHistory.create({
          data: {
            productId: item.productId,
            batchId: item.batchId,
            storeStockId: batch.storeStockId,
            previousQuantity: batch.remainingQuantity,
            newQuantity: batch.remainingQuantity - item.quantity,
            changeQuantity: -item.quantity,
            source: 'SALE',
            referenceId: transaction.id,
            referenceType: 'Transaction',
            notes: `Sale transaction ${transaction.id}`,
            userId: cashierUser.id,
            date: transactionDate,
          }
        });
      }

      // Generate returns for some transactions (8% return rate)
      if (faker.number.float() < CONFIG.RETURN_RATE) {
        const returnDate = addDays(transactionDate, faker.number.int({ min: 1, max: 30 }));
        const returnReason = faker.helpers.arrayElement(RETURN_REASONS);

        // Return 1-2 items from the transaction
        const returnItems = faker.helpers.arrayElements(
          transaction.items,
          faker.number.int({ min: 1, max: Math.min(2, transaction.items.length) })
        );

        let returnTotal = 0;
        const returnItemsData = returnItems.map(item => {
          const returnQuantity = faker.number.int({ min: 1, max: item.quantity });
          const returnSubtotal = returnQuantity * item.unitPrice;
          returnTotal += returnSubtotal;

          return {
            productId: item.productId,
            quantity: returnQuantity,
            unitPrice: item.unitPrice,
            subtotal: returnSubtotal,
          };
        });

        try {
          const customerReturn = await prisma.return.create({
            data: {
              returnDate: returnDate,
              transactionId: transaction.id,
              reason: returnReason,
              total: returnTotal,
              status: 'COMPLETED',
              disposition: 'RETURN_TO_STOCK',
              customerResolution: 'REFUND',
              items: {
                create: returnItemsData
              }
            }
          });

          returns.push(customerReturn);
        } catch (error) {
          console.error(`Error creating return for transaction ${transaction.id}: ${error.message}`);
        }
      }

      if ((i + 1) % 50 === 0) {
        logProgress(`Created transaction ${i + 1}`, i + 1, CONFIG.TRANSACTIONS_COUNT);
      }

    } catch (error) {
      console.error(`Error creating transaction ${i + 1}: ${error.message}`);
    }
  }

  logProgress(`✅ Created ${transactions.length} transactions and ${returns.length} returns`);
}

// Export for potential use as module
module.exports = { main };

// Run if called directly
if (require.main === module) {
  main()
    .catch((e) => {
      console.error('❌ Fatal error:', e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
      logProgress('🏁 Database connection closed');
    });
}
