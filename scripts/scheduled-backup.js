#!/usr/bin/env node

/**
 * Scheduled Backup Script
 * 
 * This script is designed to be run as a scheduled task (e.g., via cron)
 * to create automatic backups of the database.
 * 
 * Example cron entry (daily at 11:00 PM):
 * 0 23 * * * cd /path/to/project && node scripts/scheduled-backup.js
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  backupDir: path.join(process.cwd(), 'backups'),
  maxBackups: 30, // Keep last 30 backups
  logFile: path.join(process.cwd(), 'logs', 'backup.log'),
};

// Ensure directories exist
if (!fs.existsSync(config.backupDir)) {
  fs.mkdirSync(config.backupDir, { recursive: true });
}

const logDir = path.dirname(config.logFile);
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Log function
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}\n`;
  
  console.log(message);
  
  // Append to log file
  fs.appendFileSync(config.logFile, logMessage);
}

// Run the backup
async function runBackup() {
  log('Starting scheduled backup...');
  
  // Use the scheduled-backup module from our project
  const scriptPath = path.join(process.cwd(), 'src', 'lib', 'backup', 'scheduled-backup.ts');
  
  // Use ts-node to run the TypeScript file
  const process = spawn('npx', ['ts-node', scriptPath], {
    cwd: process.cwd(),
    env: { ...process.env },
  });
  
  let output = '';
  let errorOutput = '';
  
  process.stdout.on('data', (data) => {
    output += data.toString();
  });
  
  process.stderr.on('data', (data) => {
    errorOutput += data.toString();
  });
  
  process.on('close', (code) => {
    if (code === 0) {
      log(`Backup completed successfully: ${output.trim()}`);
    } else {
      log(`Backup failed with code ${code}: ${errorOutput.trim() || output.trim()}`);
    }
  });
}

// Run the backup
runBackup().catch((error) => {
  log(`Error running backup: ${error.message}`);
  process.exit(1);
});
