# Analytics Test Data Generation

This directory contains scripts for generating comprehensive test data to validate the supplier performance analytics features in the NPOS application.

## Overview

The data generation script creates realistic test data spanning the last 6 months, including:

- **50 Purchase Orders** distributed across 8-12 suppliers
- **Realistic delivery patterns** with 3-14 day delays
- **Stock batches** with proper FIFO logic
- **Store transfers** (70% of warehouse stock)
- **250+ customer transactions** with realistic patterns
- **Customer returns** (8% return rate) with various reasons
- **Complete audit trails** via stock history

## Files

- `generate-analytics-test-data.js` - Main data generation script
- `run-analytics-data-generation.js` - User-friendly runner with safety checks
- `README-analytics-data-generation.md` - This documentation

## Prerequisites

1. **Database Setup**: Ensure your PostgreSQL database is running and accessible
2. **Environment**: Set your `DATABASE_URL` environment variable
3. **Schema**: Run `npx prisma generate` and `npx prisma db push`
4. **Base Data**: Ensure you have existing products, categories, and units (run seed scripts first)

```bash
# Install dependencies
npm install @prisma/client @faker-js/faker

# Setup database schema
npx prisma generate
npx prisma db push

# Create base data (if not already done)
node prisma/seed.ts
node prisma/seed-products.js
```

## Usage

### Option 1: Using the Runner Script (Recommended)
```bash
node scripts/run-analytics-data-generation.js
```

### Option 2: Direct Execution
```bash
node scripts/generate-analytics-test-data.js
```

### Option 3: NPM Script (if added to package.json)
```bash
npm run generate:analytics-data
```

## Generated Data Structure

### Suppliers (8-12 total)
- Indonesian company names (PT, CV prefixes)
- Realistic contact information
- Jakarta/Indonesian addresses
- Phone numbers in Indonesian format

### Purchase Orders (50 total)
- **Date Range**: Last 6 months to present
- **Status Distribution**:
  - 90% RECEIVED (with realistic delivery dates)
  - 10% PENDING_APPROVAL/APPROVED/ORDERED
- **Items**: 3-5 products per PO
- **Pricing**: IDR currency (Indonesian Rupiah)
- **Delivery Performance**: Realistic delays (some early, some late)

### Stock Batches
- Created for all received POs
- Proper batch numbering system
- Realistic expiry dates (6-24 months)
- FIFO logic maintained
- Warehouse location initially

### Store Transfers
- 70% of warehouse batches transferred to store
- Partial transfers (30-80% of batch quantity)
- Proper stock location tracking
- Complete audit trail

### Customer Transactions (250 total)
- Realistic transaction patterns
- 1-5 items per transaction
- Multiple payment methods (CASH, DEBIT, QRIS)
- Proper tax calculations (11%)
- FIFO stock deduction

### Customer Returns (~8% rate)
- Realistic return reasons:
  - Defective
  - Wrong item
  - Customer changed mind
  - Damaged
  - Expired
  - Poor quality
- Return dates 1-30 days after purchase
- Proper return processing

## Data Quality Features

### Referential Integrity
- All foreign key relationships maintained
- Proper cascade handling
- No orphaned records

### FIFO Logic
- Stock deductions follow First-In-First-Out
- Batch quantities always consistent
- Total stock = sum of active batch quantities

### Realistic Patterns
- Some suppliers perform better than others
- Seasonal variations in ordering
- Realistic price fluctuations
- Authentic Indonesian business context

### Audit Trails
- Complete stock history for all movements
- User attribution for all actions
- Timestamp accuracy
- Reference tracking

## Analytics Features Tested

The generated data enables testing of:

### Quality Metrics
- Return rates by supplier
- Return reasons analysis
- Product quality trends
- Defect rate calculations

### Delivery Performance
- On-time delivery rates
- Average delivery delays
- Delivery consistency
- Lead time analysis

### Cost Analysis
- Price trend analysis
- Supplier cost comparisons
- Purchase volume analysis
- Cost per unit trends

### Relationship Health
- Order frequency patterns
- Supplier reliability scoring
- Performance consistency
- Business relationship strength

## Configuration

Edit the `CONFIG` object in `generate-analytics-test-data.js` to customize:

```javascript
const CONFIG = {
  PURCHASE_ORDERS: 50,        // Number of POs to generate
  SUPPLIERS_MIN: 8,           // Minimum suppliers
  SUPPLIERS_MAX: 12,          // Maximum suppliers
  MONTHS_BACK: 6,             // Historical data period
  ITEMS_PER_PO_MIN: 3,        // Min items per PO
  ITEMS_PER_PO_MAX: 5,        // Max items per PO
  DELIVERY_DELAY_MIN: 3,      // Min delivery days
  DELIVERY_DELAY_MAX: 14,     // Max delivery days
  TRANSFER_PERCENTAGE: 0.7,   // Store transfer rate
  TRANSACTIONS_COUNT: 250,    // Customer transactions
  RETURN_RATE: 0.08,          // Return rate (8%)
  IDR_EXCHANGE_RATE: 15000,   // USD to IDR conversion
};
```

## Safety Features

- **Confirmation prompt** before execution
- **Database connection validation**
- **Dependency checking**
- **Error handling and rollback**
- **Progress logging**
- **Execution time tracking**

## Troubleshooting

### Common Issues

1. **"No products found"**
   - Run product seeding first: `node prisma/seed-products.js`

2. **Database connection errors**
   - Check `DATABASE_URL` environment variable
   - Ensure PostgreSQL is running
   - Verify database exists

3. **Prisma client errors**
   - Run `npx prisma generate`
   - Ensure schema is up to date

4. **Memory issues with large datasets**
   - Reduce `CONFIG.PURCHASE_ORDERS` value
   - Run in smaller batches

### Performance Tips

- Run on a local development database
- Ensure adequate disk space
- Close other database connections
- Monitor memory usage during execution

## Integration with Analytics

After running the data generation, you can test the analytics features at:

- `/inventory/suppliers` - Supplier list with performance indicators
- `/inventory/suppliers/analytics` - Comprehensive analytics dashboard
- `/inventory/purchase-orders` - PO management with performance data
- `/inventory/reports` - Supplier performance reports

The generated data provides realistic scenarios for testing all analytics calculations and visualizations.
