import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function seedInvoiceTestData() {
  console.log('🌱 Starting invoice test data seeding...');

  try {
    // Get existing data for relationships
    const users = await prisma.user.findMany({
      where: { role: { in: ['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'] } }
    });

    const suppliers = await prisma.supplier.findMany({
      where: { isActive: true },
      take: 5
    });

    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: { 
        status: { in: ['RECEIVED', 'PARTIALLY_RECEIVED'] }
      },
      include: {
        items: {
          include: {
            product: true
          }
        }
      },
      take: 10
    });

    if (users.length === 0) {
      console.log('❌ No admin users found. Please run user seeder first.');
      return;
    }

    if (suppliers.length === 0) {
      console.log('❌ No suppliers found. Please run supplier seeder first.');
      return;
    }

    if (purchaseOrders.length === 0) {
      console.log('❌ No received purchase orders found. Please create some POs first.');
      return;
    }

    console.log(`📊 Found ${users.length} users, ${suppliers.length} suppliers, ${purchaseOrders.length} POs`);

    // Generate test invoices
    const invoices = [];
    const currentDate = new Date();

    for (let i = 0; i < Math.min(15, purchaseOrders.length); i++) {
      const po = purchaseOrders[i];
      const user = users[Math.floor(Math.random() * users.length)];
      
      // Generate invoice number
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = currentDate.getFullYear();
      const sequence = String(i + 1).padStart(4, '0');
      const invoiceNumber = `INV-${year}-${month}-${sequence}`;

      // Calculate invoice date (1-30 days after PO date)
      const invoiceDate = new Date(po.orderDate);
      invoiceDate.setDate(invoiceDate.getDate() + Math.floor(Math.random() * 30) + 1);

      // Calculate due date (15-45 days after invoice date)
      const dueDate = new Date(invoiceDate);
      dueDate.setDate(dueDate.getDate() + Math.floor(Math.random() * 30) + 15);

      // Random status distribution
      const statusRandom = Math.random();
      let status, paymentStatus, approvedBy = null, approvedAt = null;
      
      if (statusRandom < 0.1) {
        status = 'PENDING';
        paymentStatus = 'UNPAID';
      } else if (statusRandom < 0.15) {
        status = 'REJECTED';
        paymentStatus = 'UNPAID';
      } else if (statusRandom < 0.2) {
        status = 'CANCELLED';
        paymentStatus = 'UNPAID';
      } else {
        status = 'APPROVED';
        approvedBy = users.find(u => u.role === 'SUPER_ADMIN')?.id || user.id;
        approvedAt = new Date(invoiceDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000);
        
        // Payment status for approved invoices
        const paymentRandom = Math.random();
        if (paymentRandom < 0.3) {
          paymentStatus = 'PAID';
        } else if (paymentRandom < 0.5) {
          paymentStatus = 'PARTIALLY_PAID';
        } else if (dueDate < currentDate) {
          paymentStatus = 'OVERDUE';
        } else {
          paymentStatus = 'UNPAID';
        }
      }

      // Calculate totals from PO items
      let subtotal = 0;
      const invoiceItems = po.items.map(item => {
        const quantity = Number(item.receivedQuantity) || Number(item.quantity);
        const unitPrice = Number(item.unitPrice);
        const itemSubtotal = quantity * unitPrice;
        subtotal += itemSubtotal;

        return {
          purchaseOrderItemId: item.id,
          productId: item.productId,
          description: item.product.name,
          quantity: new Decimal(quantity),
          unitPrice: new Decimal(unitPrice),
          subtotal: new Decimal(itemSubtotal),
        };
      });

      const taxPercentage = Number(po.taxPercentage) || 11; // Default 11% VAT
      const tax = (subtotal * taxPercentage) / 100;
      const total = subtotal + tax;

      // Calculate paid amount based on payment status
      let paidAmount = 0;
      let paidAt = null;
      
      if (paymentStatus === 'PAID') {
        paidAmount = total;
        paidAt = new Date(approvedAt.getTime() + Math.random() * 30 * 24 * 60 * 60 * 1000);
      } else if (paymentStatus === 'PARTIALLY_PAID') {
        paidAmount = total * (0.3 + Math.random() * 0.4); // 30-70% paid
      }

      const invoice = {
        invoiceNumber,
        purchaseOrderId: po.id,
        supplierId: po.supplierId,
        invoiceDate,
        dueDate,
        subtotal: new Decimal(subtotal),
        tax: new Decimal(tax),
        taxPercentage: new Decimal(taxPercentage),
        total: new Decimal(total),
        status,
        paymentStatus,
        paidAmount: new Decimal(paidAmount),
        paidAt,
        notes: `Test invoice generated from PO ${po.id.slice(-8)}`,
        createdById: user.id,
        approvedById: approvedBy,
        approvedAt,
        items: {
          create: invoiceItems
        }
      };

      invoices.push(invoice);
    }

    // Create invoices with items
    console.log(`📝 Creating ${invoices.length} test invoices...`);
    
    for (const invoiceData of invoices) {
      await prisma.invoice.create({
        data: invoiceData
      });
    }

    // Create some test payments for paid/partially paid invoices
    console.log('💰 Creating test payments...');
    
    const createdInvoices = await prisma.invoice.findMany({
      where: {
        paymentStatus: { in: ['PAID', 'PARTIALLY_PAID'] }
      }
    });

    for (const invoice of createdInvoices) {
      const paymentCount = invoice.paymentStatus === 'PAID' ? 
        Math.floor(Math.random() * 3) + 1 : // 1-3 payments for fully paid
        Math.floor(Math.random() * 2) + 1;   // 1-2 payments for partially paid

      let remainingAmount = Number(invoice.paidAmount);
      
      for (let i = 0; i < paymentCount && remainingAmount > 0; i++) {
        const paymentAmount = i === paymentCount - 1 ? 
          remainingAmount : // Last payment gets remaining amount
          Math.min(remainingAmount * (0.3 + Math.random() * 0.4), remainingAmount);

        const paymentDate = new Date(invoice.approvedAt);
        paymentDate.setDate(paymentDate.getDate() + Math.floor(Math.random() * 30));

        const paymentMethods = ['BANK_TRANSFER', 'CASH', 'CHECK', 'CREDIT_CARD'];
        const paymentMethod = paymentMethods[Math.floor(Math.random() * paymentMethods.length)];

        await prisma.invoicePayment.create({
          data: {
            invoiceId: invoice.id,
            amount: new Decimal(paymentAmount),
            paymentDate,
            paymentMethod,
            paymentReference: `PAY-${Date.now()}-${i}`,
            notes: `Test payment ${i + 1} for invoice ${invoice.invoiceNumber}`,
            createdById: users[Math.floor(Math.random() * users.length)].id,
          }
        });

        remainingAmount -= paymentAmount;
      }
    }

    // Get final statistics
    const stats = await prisma.invoice.groupBy({
      by: ['status', 'paymentStatus'],
      _count: true
    });

    const totalInvoices = await prisma.invoice.count();
    const totalAmount = await prisma.invoice.aggregate({
      _sum: { total: true, paidAmount: true }
    });

    console.log('\n✅ Invoice test data seeding completed!');
    console.log('\n📊 Summary:');
    console.log(`Total Invoices: ${totalInvoices}`);
    console.log(`Total Amount: IDR ${Number(totalAmount._sum.total || 0).toLocaleString()}`);
    console.log(`Paid Amount: IDR ${Number(totalAmount._sum.paidAmount || 0).toLocaleString()}`);
    
    console.log('\n📈 Status Distribution:');
    stats.forEach(stat => {
      console.log(`${stat.status} - ${stat.paymentStatus}: ${stat._count} invoices`);
    });

    console.log('\n🎯 Test URLs:');
    console.log('- Invoice List: http://localhost:3000/invoices');
    console.log('- API Test: http://localhost:3000/api/invoices');
    console.log('- Summary API: http://localhost:3000/api/invoices/summary');

  } catch (error) {
    console.error('❌ Error seeding invoice test data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeder
if (import.meta.url === `file://${process.argv[1]}`) {
  seedInvoiceTestData()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { seedInvoiceTestData };
