/**
 * Database State Checker
 * 
 * This script checks the current state of the database to understand
 * the supplier relationships and migration status.
 */

import { PrismaClient } from './prisma-client.js';

const prisma = new PrismaClient();

/**
 * Log with timestamp
 */
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] [${level}] ${message}`);
}

/**
 * Check database state
 */
async function checkDatabaseState() {
  try {
    log('Checking current database state...');
    
    // Count total products
    const totalProducts = await prisma.product.count();
    log(`Total products: ${totalProducts}`);
    
    // Count products with old supplier relationships
    const productsWithOldSuppliers = await prisma.product.count({
      where: {
        supplierId: { not: null }
      }
    });
    log(`Products with old supplier relationships: ${productsWithOldSuppliers}`);
    
    // Count ProductSupplier relationships
    const productSupplierCount = await prisma.productSupplier.count();
    log(`ProductSupplier relationships: ${productSupplierCount}`);
    
    // Count suppliers
    const supplierCount = await prisma.supplier.count();
    log(`Total suppliers: ${supplierCount}`);
    
    // Get sample products with old relationships
    if (productsWithOldSuppliers > 0) {
      log('\nSample products with old supplier relationships:');
      const sampleProducts = await prisma.product.findMany({
        where: {
          supplierId: { not: null }
        },
        select: {
          id: true,
          name: true,
          sku: true,
          supplierId: true,
          purchasePrice: true,
          supplier: {
            select: {
              id: true,
              name: true
            }
          }
        },
        take: 5
      });
      
      sampleProducts.forEach(product => {
        log(`  - ${product.name} (${product.sku}) -> Supplier: ${product.supplier?.name || 'Unknown'} (${product.supplierId}), Price: $${product.purchasePrice || 'N/A'}`);
      });
    }
    
    // Get sample ProductSupplier relationships
    if (productSupplierCount > 0) {
      log('\nSample ProductSupplier relationships:');
      const sampleRelationships = await prisma.productSupplier.findMany({
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true
            }
          },
          supplier: {
            select: {
              id: true,
              name: true
            }
          }
        },
        take: 5
      });
      
      sampleRelationships.forEach(rel => {
        log(`  - ${rel.product.name} (${rel.product.sku}) -> ${rel.supplier.name}, Price: $${rel.purchasePrice}, Preferred: ${rel.isPreferred}`);
      });
    }
    
    // Check for data consistency issues
    log('\nChecking for data consistency issues...');
    
    // Products with both old and new relationships
    const productsWithBoth = await prisma.product.findMany({
      where: {
        AND: [
          { supplierId: { not: null } },
          { productSuppliers: { some: {} } }
        ]
      },
      select: {
        id: true,
        name: true,
        supplierId: true,
        productSuppliers: {
          select: {
            supplierId: true,
            isPreferred: true
          }
        }
      }
    });
    
    if (productsWithBoth.length > 0) {
      log(`WARNING: Found ${productsWithBoth.length} products with both old and new supplier relationships`);
      productsWithBoth.forEach(product => {
        const newSupplierIds = product.productSuppliers.map(ps => ps.supplierId);
        const hasMatchingNew = newSupplierIds.includes(product.supplierId);
        log(`  - ${product.name}: Old supplier ${product.supplierId}, New suppliers: [${newSupplierIds.join(', ')}], Matching: ${hasMatchingNew}`);
      });
    } else {
      log('✓ No products found with both old and new supplier relationships');
    }
    
    // Products with multiple preferred suppliers
    const productsWithMultiplePreferred = await prisma.product.findMany({
      where: {
        productSuppliers: {
          some: {
            isPreferred: true
          }
        }
      },
      include: {
        productSuppliers: {
          where: {
            isPreferred: true
          },
          include: {
            supplier: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });
    
    const problemProducts = productsWithMultiplePreferred.filter(p => p.productSuppliers.length > 1);
    if (problemProducts.length > 0) {
      log(`WARNING: Found ${problemProducts.length} products with multiple preferred suppliers`);
      problemProducts.forEach(product => {
        const preferredSuppliers = product.productSuppliers.map(ps => ps.supplier.name);
        log(`  - ${product.name}: Preferred suppliers: [${preferredSuppliers.join(', ')}]`);
      });
    } else {
      log('✓ No products found with multiple preferred suppliers');
    }
    
    // Summary
    log('\n=== SUMMARY ===');
    log(`Migration needed: ${productsWithOldSuppliers > 0 ? 'YES' : 'NO'}`);
    log(`Data consistency: ${productsWithBoth.length === 0 && problemProducts.length === 0 ? 'GOOD' : 'ISSUES FOUND'}`);
    log(`Ready for schema cleanup: ${productsWithOldSuppliers === 0 ? 'YES' : 'NO'}`);
    
  } catch (error) {
    log(`Error checking database state: ${error.message}`, 'ERROR');
    throw error;
  }
}

/**
 * Main execution
 */
async function main() {
  try {
    await checkDatabaseState();
  } catch (error) {
    log(`Fatal error: ${error.message}`, 'ERROR');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run check
main();
