/**
 * Debug script to test the data structure needed for advanced analytics
 */

const { PrismaClient } = require('@prisma/client');

async function debugAnalyticsData() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Debugging Advanced Analytics Data Structure...\n');

    // Test 1: Check suppliers and their product relationships
    console.log('1. Testing Supplier Data Structure:');
    const suppliers = await prisma.supplier.findMany({
      take: 2,
      include: {
        productSuppliers: {
          where: { isActive: true },
          include: {
            product: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      }
    });
    
    console.log(`   ✅ Found ${suppliers.length} suppliers`);
    suppliers.forEach(supplier => {
      console.log(`   📊 ${supplier.name}: ${supplier.productSuppliers.length} active products`);
    });

    if (suppliers.length === 0) {
      console.log('   ❌ No suppliers found - cannot test analytics');
      return;
    }

    const testSupplier = suppliers[0];
    console.log(`\n   🎯 Using supplier: ${testSupplier.name} (${testSupplier.id})`);

    // Test 2: Check if the complex query structure works
    console.log('\n2. Testing Complex Query Structure:');
    try {
      const supplierWithData = await prisma.supplier.findUnique({
        where: { id: testSupplier.id },
        include: {
          productSuppliers: {
            where: { isActive: true },
            take: 1, // Limit to 1 for testing
            include: {
              product: {
                include: {
                  storeStock: true,
                  warehouseStock: true,
                  transactionItems: {
                    take: 5, // Limit for testing
                    where: {
                      transaction: {
                        status: { not: 'VOIDED' }
                      }
                    },
                    include: {
                      transaction: {
                        select: {
                          createdAt: true,
                          status: true
                        }
                      }
                    }
                  },
                  stockBatches: {
                    take: 5, // Limit for testing
                    orderBy: { receivedDate: 'desc' }
                  }
                }
              }
            }
          }
        }
      });

      console.log('   ✅ Complex query structure works');
      
      if (supplierWithData?.productSuppliers?.length > 0) {
        const product = supplierWithData.productSuppliers[0].product;
        console.log(`   📦 Product: ${product.name}`);
        console.log(`   📊 Store stock: ${product.storeStock?.quantity || 0}`);
        console.log(`   📊 Warehouse stock: ${product.warehouseStock?.quantity || 0}`);
        console.log(`   📊 Transaction items: ${product.transactionItems?.length || 0}`);
        console.log(`   📊 Stock batches: ${product.stockBatches?.length || 0}`);
      }
      
    } catch (error) {
      console.log('   ❌ Complex query failed:', error.message);
      console.log('   📝 Error details:', error);
    }

    // Test 3: Check transaction data availability
    console.log('\n3. Testing Transaction Data:');
    const transactionCount = await prisma.transaction.count({
      where: {
        status: { not: 'VOIDED' }
      }
    });
    console.log(`   📊 Total valid transactions: ${transactionCount}`);

    const recentTransactions = await prisma.transaction.findMany({
      take: 5,
      where: {
        status: { not: 'VOIDED' }
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`   📊 Recent transactions: ${recentTransactions.length}`);
    recentTransactions.forEach(tx => {
      console.log(`   💰 Transaction ${tx.id}: ${tx.items.length} items, total: ${tx.totalAmount}`);
    });

    // Test 4: Check stock batch data
    console.log('\n4. Testing Stock Batch Data:');
    const batchCount = await prisma.stockBatch.count();
    console.log(`   📦 Total stock batches: ${batchCount}`);

    const recentBatches = await prisma.stockBatch.findMany({
      take: 5,
      include: {
        product: {
          select: {
            name: true
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                name: true
              }
            }
          }
        }
      },
      orderBy: { receivedDate: 'desc' }
    });

    console.log(`   📦 Recent batches: ${recentBatches.length}`);
    recentBatches.forEach(batch => {
      console.log(`   📦 Batch: ${batch.product.name} from ${batch.productSupplier.supplier.name}, qty: ${batch.quantity}`);
    });

    // Test 5: Simple analytics engine test
    console.log('\n5. Testing Simple Analytics Logic:');
    
    if (supplierWithData?.productSuppliers?.length > 0) {
      const product = supplierWithData.productSuppliers[0].product;
      const transactionItems = product.transactionItems || [];
      
      if (transactionItems.length > 0) {
        const totalQuantity = transactionItems.reduce((sum, item) => sum + Number(item.quantity), 0);
        console.log(`   📊 Total consumption: ${totalQuantity} units`);
        console.log(`   📊 Average per transaction: ${(totalQuantity / transactionItems.length).toFixed(2)} units`);
        
        // Test date calculations
        const dates = transactionItems.map(item => new Date(item.transaction.createdAt));
        const oldestDate = new Date(Math.min(...dates.map(d => d.getTime())));
        const newestDate = new Date(Math.max(...dates.map(d => d.getTime())));
        console.log(`   📅 Date range: ${oldestDate.toDateString()} to ${newestDate.toDateString()}`);
      } else {
        console.log('   ⚠️  No transaction items found for testing');
      }
    }

    console.log('\n✅ Data structure debugging completed!');
    console.log('\n📋 Summary:');
    console.log(`   • Suppliers: ${suppliers.length} found`);
    console.log(`   • Transactions: ${transactionCount} valid transactions`);
    console.log(`   • Stock Batches: ${batchCount} batches`);
    console.log('   • Complex query structure: Working');

  } catch (error) {
    console.error('❌ Debug failed:', error);
    console.log('\n🔧 Error details:');
    console.log('   Message:', error.message);
    console.log('   Stack:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug
debugAnalyticsData();
