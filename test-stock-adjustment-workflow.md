# Stock Adjustment Approval Workflow - Testing Guide

## Overview
This document provides a comprehensive testing guide for the newly implemented role-based access control and approval workflow system for stock adjustments.

## Test Scenarios

### 1. Access Control Testing

#### Test 1.1: SUPER_ADMIN Access
**Expected Result**: Full access with immediate application
- ✅ Can access `/inventory/stock/adjustments` page
- ✅ Can create adjustments that are immediately applied
- ✅ Can approve/reject pending adjustments from WAREHOUSE_ADMIN
- ✅ Sees approval action buttons for pending adjustments

#### Test 1.2: WAREHOUSE_ADMIN Access  
**Expected Result**: Access with approval workflow
- ✅ Can access `/inventory/stock/adjustments` page
- ✅ Can create adjustments that require approval
- ✅ Cannot approve/reject adjustments
- ✅ Sees warning message about approval requirement

#### Test 1.3: Other Roles (CASHIER, FINANCE_ADMIN, etc.)
**Expected Result**: Access denied
- ❌ Cannot access `/inventory/stock/adjustments` page
- ❌ Redirected to access denied page
- ❌ API endpoints return 403 Forbidden

### 2. Workflow Testing

#### Test 2.1: SUPER_ADMIN Workflow
1. Login as SUPER_ADMIN
2. Navigate to Stock Adjustments
3. Create new adjustment
4. **Expected**: 
   - Status: APPLIED
   - Stock quantities updated immediately
   - Batch integration processed
   - Success message: "Stock adjustment created and applied successfully"

#### Test 2.2: WAREHOUSE_ADMIN Workflow
1. Login as WAREHOUSE_ADMIN  
2. Navigate to Stock Adjustments
3. Create new adjustment
4. **Expected**:
   - Status: PENDING_APPROVAL
   - Stock quantities NOT updated
   - No batch integration
   - Success message: "Stock adjustment created and submitted for approval"
   - Notification sent to SUPER_ADMIN users

#### Test 2.3: Approval Process
1. WAREHOUSE_ADMIN creates adjustment (status: PENDING_APPROVAL)
2. SUPER_ADMIN receives notification
3. SUPER_ADMIN approves adjustment
4. **Expected**:
   - Status changes to APPLIED
   - Stock quantities updated
   - Batch integration processed
   - Notification sent to requesting WAREHOUSE_ADMIN

#### Test 2.4: Rejection Process
1. WAREHOUSE_ADMIN creates adjustment (status: PENDING_APPROVAL)
2. SUPER_ADMIN rejects with reason
3. **Expected**:
   - Status changes to REJECTED
   - Stock quantities remain unchanged
   - No batch integration
   - Notification sent to requesting WAREHOUSE_ADMIN with rejection reason

### 3. API Testing

#### Test 3.1: POST /api/inventory/adjustments
**SUPER_ADMIN Request**:
```json
{
  "productId": "product-id",
  "locationType": "STORE", 
  "adjustmentQuantity": -5,
  "reason": "DAMAGED",
  "notes": "Test adjustment"
}
```
**Expected Response**: Status 200, adjustment with status "APPLIED"

**WAREHOUSE_ADMIN Request**: Same payload
**Expected Response**: Status 200, adjustment with status "PENDING_APPROVAL"

**CASHIER Request**: Same payload  
**Expected Response**: Status 403, "Insufficient permissions"

#### Test 3.2: PUT /api/inventory/adjustments/[id]/approve
**SUPER_ADMIN Request**:
```json
{
  "action": "approve"
}
```
**Expected Response**: Status 200, adjustment approved and applied

**WAREHOUSE_ADMIN Request**: Same payload
**Expected Response**: Status 403, "Insufficient permissions"

### 4. UI Testing

#### Test 4.1: Status Display
- ✅ Status column shows correct badges with appropriate colors
- ✅ Pending: Secondary badge
- ✅ Applied: Default badge  
- ✅ Rejected: Destructive badge

#### Test 4.2: Action Buttons
- ✅ SUPER_ADMIN sees Approve/Reject buttons for pending adjustments
- ✅ WAREHOUSE_ADMIN does not see action buttons
- ✅ No action buttons for non-pending adjustments

#### Test 4.3: Filtering
- ✅ Status filter works correctly
- ✅ Can filter by "Pending Approval", "Applied", "Rejected"
- ✅ Combined filters work (status + reason + search)

### 5. Notification Testing

#### Test 5.1: Pending Approval Notifications
1. WAREHOUSE_ADMIN creates adjustment
2. **Expected**: All SUPER_ADMIN users receive notification
3. **Content**: "User requested a stock adjustment for Product (+/-X) - Reason"

#### Test 5.2: Approval Notifications  
1. SUPER_ADMIN approves adjustment
2. **Expected**: Requesting WAREHOUSE_ADMIN receives notification
3. **Content**: "Your stock adjustment for Product has been approved by SuperAdmin"

#### Test 5.3: Rejection Notifications
1. SUPER_ADMIN rejects adjustment with reason
2. **Expected**: Requesting WAREHOUSE_ADMIN receives notification  
3. **Content**: "Your stock adjustment for Product has been rejected by SuperAdmin. Reason: [reason]"

### 6. Data Integrity Testing

#### Test 6.1: Batch Integration Timing
- ✅ SUPER_ADMIN: Batch integration occurs immediately
- ✅ WAREHOUSE_ADMIN: No batch integration until approval
- ✅ Approved adjustments: Batch integration occurs during approval
- ✅ Rejected adjustments: No batch integration ever

#### Test 6.2: Stock Quantity Consistency
- ✅ Stock quantities only change when status is APPLIED
- ✅ Pending adjustments don't affect current stock
- ✅ Rejected adjustments never affect stock

### 7. Error Handling Testing

#### Test 7.1: Insufficient Stock
1. Create negative adjustment larger than available batch stock
2. **Expected**: Error message about insufficient batch stock
3. **Result**: Adjustment not created

#### Test 7.2: Invalid Permissions
1. Try to approve adjustment as WAREHOUSE_ADMIN
2. **Expected**: 403 Forbidden error
3. **Result**: No status change

#### Test 7.3: Non-existent Adjustment
1. Try to approve non-existent adjustment ID
2. **Expected**: 404 Not Found error

## Test Data Setup

### Users Required
- SUPER_ADMIN user (e.g., <EMAIL>)
- WAREHOUSE_ADMIN user (e.g., <EMAIL>)  
- CASHIER user (e.g., <EMAIL>)

### Products Required
- At least 2 products with existing stock
- Products should have batch data for integration testing

### Stock Setup
- Store stock with sufficient quantities for testing
- Warehouse stock with sufficient quantities
- Some products with low stock for edge case testing

## Success Criteria

✅ All access control tests pass
✅ All workflow tests pass  
✅ All API tests pass
✅ All UI tests pass
✅ All notification tests pass
✅ All data integrity tests pass
✅ All error handling tests pass

## Notes

- Test with both positive and negative adjustments
- Test with different adjustment reasons
- Test with and without notes
- Verify audit logs are created correctly
- Check that activity logs reflect the approval workflow
- Ensure backward compatibility with existing adjustments
