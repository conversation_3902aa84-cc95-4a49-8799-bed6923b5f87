// Test script to create Purchase Orders with different delivery scenarios
import { PrismaClient } from './src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function createTestPOsWithDeliveryDates() {
  try {
    console.log('🧪 Creating test Purchase Orders with different delivery scenarios...');

    // Get required data
    const supplier = await prisma.supplier.findFirst();
    const product = await prisma.product.findFirst();
    const user = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (!supplier || !product || !user) {
      console.log('❌ Missing required data. Please ensure you have suppliers, products, and users in the database.');
      return;
    }

    console.log(`✅ Using supplier: ${supplier.name}`);
    console.log(`✅ Using product: ${product.name}`);
    console.log(`✅ Using user: ${user.name}`);

    // Test Case 1: PO with future delivery date (should have perfect scores)
    console.log('\n📋 Creating Test Case 1: Future delivery date (perfect scores)...');
    const futureDate = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days from now
    
    const po1 = await prisma.purchaseOrder.create({
      data: {
        createdById: user.id,
        supplierId: supplier.id,
        orderDate: new Date(),
        expectedDeliveryDate: futureDate,
        subtotal: 100000,
        tax: 10000,
        taxPercentage: 10,
        total: 110000,
        status: 'APPROVED',
        notes: 'Test PO 1 - Future delivery date',
        items: {
          create: [
            {
              productId: product.id,
              quantity: 10,
              unitPrice: 10000,
              subtotal: 100000,
            }
          ]
        }
      }
    });

    console.log(`✅ Created PO1: ${po1.id} (Expected: ${futureDate.toISOString().split('T')[0]})`);

    // Test Case 2: PO with past delivery date (should have performance penalty)
    console.log('\n📋 Creating Test Case 2: Past delivery date (performance penalty)...');
    const pastDate = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000); // 3 days ago
    
    const po2 = await prisma.purchaseOrder.create({
      data: {
        createdById: user.id,
        supplierId: supplier.id,
        orderDate: new Date(),
        expectedDeliveryDate: pastDate,
        subtotal: 150000,
        tax: 15000,
        taxPercentage: 10,
        total: 165000,
        status: 'ORDERED',
        notes: 'Test PO 2 - Past delivery date (delayed)',
        items: {
          create: [
            {
              productId: product.id,
              quantity: 15,
              unitPrice: 10000,
              subtotal: 150000,
            }
          ]
        }
      }
    });

    console.log(`✅ Created PO2: ${po2.id} (Expected: ${pastDate.toISOString().split('T')[0]} - DELAYED)`);

    // Test Case 3: PO with no expected delivery date (should have perfect scores)
    console.log('\n📋 Creating Test Case 3: No delivery date (perfect scores)...');
    
    const po3 = await prisma.purchaseOrder.create({
      data: {
        createdById: user.id,
        supplierId: supplier.id,
        orderDate: new Date(),
        expectedDeliveryDate: null,
        subtotal: 200000,
        tax: 20000,
        taxPercentage: 10,
        total: 220000,
        status: 'DRAFT',
        notes: 'Test PO 3 - No expected delivery date',
        items: {
          create: [
            {
              productId: product.id,
              quantity: 20,
              unitPrice: 10000,
              subtotal: 200000,
            }
          ]
        }
      }
    });

    console.log(`✅ Created PO3: ${po3.id} (No expected delivery date)`);

    console.log('\n🎉 Test Purchase Orders created successfully!');
    console.log('\n📊 Test URLs:');
    console.log(`🔗 PO1 (Future delivery): http://localhost:3001/inventory/purchase-orders/${po1.id}`);
    console.log(`🔗 PO2 (Past delivery - DELAYED): http://localhost:3001/inventory/purchase-orders/${po2.id}`);
    console.log(`🔗 PO3 (No delivery date): http://localhost:3001/inventory/purchase-orders/${po3.id}`);

    console.log('\n📈 Expected Performance Scores:');
    console.log('PO1: Performance=100, Quality=100, Supplier=100 (no delays)');
    console.log('PO2: Performance<100, Quality=100, Supplier<100 (3 days delayed)');
    console.log('PO3: Performance=100, Quality=100, Supplier=100 (no expected date)');

    return { po1: po1.id, po2: po2.id, po3: po3.id };

  } catch (error) {
    console.error('❌ Error creating test POs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
createTestPOsWithDeliveryDates().then(() => {
  console.log('✅ Script completed successfully');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
