-- Notification System Database Check
-- Run these queries to verify the current state of the notification system

-- 1. Check if notification templates exist
SELECT 
    "eventType", 
    "name", 
    "titleTemplate", 
    "messageTemplate",
    "defaultDeliveryMethods",
    "defaultPriority"
FROM "NotificationTemplate" 
WHERE "eventType" = 'po.status.changed';

-- 2. Check all notification templates
SELECT 
    "eventType", 
    "name",
    "createdAt"
FROM "NotificationTemplate" 
ORDER BY "createdAt" DESC;

-- 3. Check user notification preferences for PO status changes
SELECT 
    np."eventType", 
    np."enabled", 
    np."deliveryMethods", 
    np."frequency",
    u."name", 
    u."role",
    u."email"
FROM "NotificationPreference" np
JOIN "User" u ON np."userId" = u."id"
WHERE np."eventType" = 'po.status.changed'
ORDER BY u."name";

-- 4. Check all user notification preferences
SELECT 
    np."eventType", 
    np."enabled", 
    COUNT(*) as user_count
FROM "NotificationPreference" np
GROUP BY np."eventType", np."enabled"
ORDER BY np."eventType";

-- 5. Check if notification events are being created
SELECT 
    "eventType", 
    "eventId", 
    "sourceId", 
    "processed", 
    "createdAt",
    "processedAt"
FROM "NotificationEvent"
WHERE "eventType" = 'po.status.changed'
ORDER BY "createdAt" DESC
LIMIT 10;

-- 6. Check all recent notification events
SELECT 
    "eventType", 
    "processed", 
    COUNT(*) as event_count,
    MAX("createdAt") as latest_event
FROM "NotificationEvent"
GROUP BY "eventType", "processed"
ORDER BY latest_event DESC;

-- 7. Check if notifications are being created
SELECT 
    n."title", 
    n."message", 
    n."deliveryMethods", 
    n."isRead", 
    n."eventType",
    u."name" as user_name,
    u."role" as user_role,
    n."createdAt"
FROM "Notification" n
JOIN "User" u ON n."userId" = u."id"
WHERE n."eventType" = 'po.status.changed'
ORDER BY n."createdAt" DESC
LIMIT 10;

-- 8. Check all recent notifications
SELECT 
    n."eventType",
    n."type",
    n."isRead",
    COUNT(*) as notification_count,
    MAX(n."createdAt") as latest_notification
FROM "Notification" n
GROUP BY n."eventType", n."type", n."isRead"
ORDER BY latest_notification DESC;

-- 9. Check users and their roles
SELECT 
    "id",
    "name", 
    "email", 
    "role",
    "active"
FROM "User"
WHERE "active" = true
ORDER BY "role", "name";

-- 10. Check recent Purchase Orders for testing
SELECT 
    po."id",
    po."status",
    po."total",
    s."name" as supplier_name,
    po."createdAt",
    po."updatedAt"
FROM "PurchaseOrder" po
JOIN "Supplier" s ON po."supplierId" = s."id"
ORDER BY po."updatedAt" DESC
LIMIT 5;

-- 11. Summary counts
SELECT 
    'NotificationTemplate' as table_name,
    COUNT(*) as record_count
FROM "NotificationTemplate"
UNION ALL
SELECT 
    'NotificationPreference' as table_name,
    COUNT(*) as record_count
FROM "NotificationPreference"
UNION ALL
SELECT 
    'NotificationEvent' as table_name,
    COUNT(*) as record_count
FROM "NotificationEvent"
UNION ALL
SELECT 
    'Notification' as table_name,
    COUNT(*) as record_count
FROM "Notification"
UNION ALL
SELECT 
    'User' as table_name,
    COUNT(*) as record_count
FROM "User"
WHERE "active" = true
UNION ALL
SELECT 
    'PurchaseOrder' as table_name,
    COUNT(*) as record_count
FROM "PurchaseOrder";

-- 12. Check for any error patterns in recent notifications
SELECT 
    n."eventType",
    n."title",
    n."message",
    n."metadata",
    n."createdAt"
FROM "Notification" n
WHERE n."createdAt" >= NOW() - INTERVAL '1 hour'
ORDER BY n."createdAt" DESC
LIMIT 20;
