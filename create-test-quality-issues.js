import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Creating test quality issues...');

    // Get existing data for relationships
    const products = await prisma.product.findMany({ take: 5 });
    const suppliers = await prisma.supplier.findMany({ take: 5 });
    const users = await prisma.user.findMany({ 
      where: { role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'] } },
      take: 3
    });

    if (products.length === 0) {
      console.log('❌ No products found. Please create some products first.');
      return;
    }

    if (suppliers.length === 0) {
      console.log('❌ No suppliers found. Please create some suppliers first.');
      return;
    }

    if (users.length === 0) {
      console.log('❌ No admin users found. Please create some users first.');
      return;
    }

    console.log(`Found ${products.length} products, ${suppliers.length} suppliers, ${users.length} users`);

    // Create test quality issues
    const qualityIssues = [
      {
        productId: products[0].id,
        supplierId: suppliers[0].id,
        issueType: 'DEFECTIVE_PRODUCT',
        severity: 'HIGH',
        description: 'Product arrived with visible damage and does not function properly',
        defectCategory: 'Manufacturing Defect',
        affectedQuantity: 5.0,
        reportedBy: users[0].id,
        status: 'OPEN'
      },
      {
        productId: products[1 % products.length].id,
        supplierId: suppliers[1 % suppliers.length].id,
        issueType: 'PACKAGING_DAMAGE',
        severity: 'MEDIUM',
        description: 'Packaging was damaged during shipping, some items may be affected',
        defectCategory: 'Shipping Damage',
        affectedQuantity: 12.0,
        reportedBy: users[1 % users.length].id,
        status: 'IN_PROGRESS'
      },
      {
        productId: products[0].id,
        supplierId: suppliers[0].id,
        issueType: 'WRONG_SPECIFICATION',
        severity: 'CRITICAL',
        description: 'Received wrong product model, does not match order specifications',
        defectCategory: 'Order Mismatch',
        affectedQuantity: 25.0,
        reportedBy: users[0].id,
        status: 'ESCALATED',
        escalationLevel: 1
      }
    ];

    console.log(`Creating ${qualityIssues.length} test quality issues...`);

    for (let i = 0; i < qualityIssues.length; i++) {
      const issue = qualityIssues[i];
      const created = await prisma.qualityIssue.create({
        data: issue,
        include: {
          product: { select: { name: true, sku: true } },
          supplier: { select: { name: true } },
          reporter: { select: { name: true } }
        }
      });

      console.log(`✅ Created quality issue ${i + 1}: ${created.issueType} for ${created.product.name} (${created.product.sku}) from ${created.supplier.name}`);
    }

    console.log('\n🎉 Test quality issues created successfully!');
    
    // Show summary
    const totalIssues = await prisma.qualityIssue.count();
    const issuesByStatus = await prisma.qualityIssue.groupBy({
      by: ['status'],
      _count: true
    });

    console.log(`\n📊 Summary:`);
    console.log(`Total quality issues: ${totalIssues}`);
    issuesByStatus.forEach(group => {
      console.log(`- ${group.status}: ${group._count} issues`);
    });

  } catch (error) {
    console.error('Error creating test quality issues:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
