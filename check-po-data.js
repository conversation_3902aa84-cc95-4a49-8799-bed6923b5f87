// Check Purchase Order data directly from database
import { PrismaClient } from './src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkPOData() {
  try {
    console.log('🔍 Checking Purchase Order data in database...');

    const poId = 'cmbotzf0s0001cjx71v3z8iq2';
    
    // Get PO with all related data
    const po = await prisma.purchaseOrder.findUnique({
      where: { id: poId },
      include: {
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
        stockBatches: true,
        statusHistory: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!po) {
      console.log('❌ Purchase Order not found');
      return;
    }

    console.log('✅ Purchase Order found:');
    console.log('   - ID:', po.id);
    console.log('   - Status:', po.status);
    console.log('   - Supplier:', po.supplier.name);
    console.log('   - Items count:', po.items.length);
    console.log('   - Stock batches count:', po.stockBatches.length);
    console.log('   - Status history count:', po.statusHistory.length);
    console.log('   - Expected delivery date:', po.expectedDeliveryDate);
    console.log('   - Performance score (DB):', po.performanceScore);
    console.log('   - Quality score (DB):', po.qualityScore);
    console.log('   - Supplier score (DB):', po.supplierScore);

    // Check stock batches
    if (po.stockBatches.length > 0) {
      console.log('\n📦 Stock Batches:');
      po.stockBatches.forEach((batch, index) => {
        console.log(`   ${index + 1}. ${batch.batchNumber} - Status: ${batch.status}, Expiry: ${batch.expiryDate}`);
      });
    } else {
      console.log('\n📦 No stock batches found');
    }

    // Test performance calculation manually
    console.log('\n🧮 Testing performance calculation...');
    
    // Import the calculation function
    const { calculatePerformanceMetrics } = await import('./src/lib/po-analytics.js');
    
    const metrics = await calculatePerformanceMetrics(po, po.status);
    console.log('📊 Calculated Performance Metrics:');
    console.log('   - Performance Score:', metrics.performanceScore);
    console.log('   - Quality Score:', metrics.qualityScore);
    console.log('   - Supplier Score:', metrics.supplierScore);
    console.log('   - Has Delays:', metrics.hasDelays);
    console.log('   - Delay Reason:', metrics.delayReason);
    console.log('   - Batch Metrics:', metrics.batchMetrics);

  } catch (error) {
    console.error('❌ Error checking PO data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkPOData();
