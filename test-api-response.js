// Test script to check API responses for performance metrics
async function testPOAPI() {
  try {
    console.log('🧪 Testing Purchase Order API responses...');

    const poId = 'cmbotzf0s0001cjx71v3z8iq2';
    
    // Test main PO API
    console.log('\n📋 Testing main PO API...');
    const poResponse = await fetch(`http://localhost:3001/api/purchase-orders/${poId}`);
    
    if (poResponse.ok) {
      const poData = await poResponse.json();
      console.log('✅ Main PO API Response Status:', poResponse.status);
      console.log('📊 Performance Data:', poData.performanceData);
      
      if (poData.performanceData) {
        console.log('   - Performance Score:', poData.performanceData.performanceScore);
        console.log('   - Quality Score:', poData.performanceData.qualityScore);
        console.log('   - Supplier Score:', poData.performanceData.supplierScore);
        console.log('   - Batch Metrics:', poData.performanceData.batchMetrics);
      } else {
        console.log('❌ No performance data found in main API response');
      }
    } else {
      console.log('❌ Main PO API failed:', poResponse.status, await poResponse.text());
    }

    // Test status history API
    console.log('\n📈 Testing status history API...');
    const historyResponse = await fetch(`http://localhost:3001/api/purchase-orders/${poId}/status-history`);
    
    if (historyResponse.ok) {
      const historyData = await historyResponse.json();
      console.log('✅ Status History API Response Status:', historyResponse.status);
      console.log('📊 Status History Length:', historyData.statusHistory?.length || 0);
      
      if (historyData.statusHistory && historyData.statusHistory.length > 0) {
        const firstEntry = historyData.statusHistory[0];
        console.log('📋 First History Entry Performance Data:', firstEntry.performanceData);
        
        if (firstEntry.performanceData) {
          console.log('   - Performance Score:', firstEntry.performanceData.performanceScore);
          console.log('   - Quality Score:', firstEntry.performanceData.qualityScore);
          console.log('   - Supplier Score:', firstEntry.performanceData.supplierScore);
          console.log('   - Batch Metrics:', firstEntry.performanceData.batchMetrics);
        }
      } else {
        console.log('❌ No status history entries found');
      }
    } else {
      console.log('❌ Status History API failed:', historyResponse.status, await historyResponse.text());
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Error testing APIs:', error);
  }
}

// Run the test
testPOAPI();
