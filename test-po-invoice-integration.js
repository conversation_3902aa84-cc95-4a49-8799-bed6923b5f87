/**
 * Test script to verify Purchase Order to Invoice integration
 * This script tests that POs with ORDERED status appear in the invoice creation dropdown
 */

const testPOInvoiceIntegration = async () => {
  console.log('🧪 Testing PO to Invoice Integration...\n');

  try {
    // Test 1: Fetch POs with different statuses
    console.log('📋 Test 1: Fetching Purchase Orders...');
    const response = await fetch('http://localhost:3000/api/purchase-orders?limit=100');
    
    if (!response.ok) {
      throw new Error(`Failed to fetch POs: ${response.status}`);
    }

    const data = await response.json();
    const allPOs = data.purchaseOrders || [];
    
    console.log(`   Total POs found: ${allPOs.length}`);
    
    // Group POs by status
    const posByStatus = allPOs.reduce((acc, po) => {
      acc[po.status] = (acc[po.status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('   POs by status:');
    Object.entries(posByStatus).forEach(([status, count]) => {
      console.log(`     ${status}: ${count}`);
    });

    // Test 2: Filter eligible POs (same logic as invoice form)
    console.log('\n📝 Test 2: Filtering Eligible POs for Invoice Creation...');
    const eligibleStatuses = ['ORDERED', 'PARTIALLY_RECEIVED', 'RECEIVED'];
    const eligiblePOs = allPOs.filter(po => eligibleStatuses.includes(po.status));
    
    console.log(`   Eligible POs: ${eligiblePOs.length}`);
    console.log('   Eligible PO details:');
    
    eligiblePOs.forEach(po => {
      console.log(`     - ${po.id.slice(-8).toUpperCase()} | ${po.supplier.name} | ${po.status} | Items: ${po.items?.length || 0}`);
    });

    // Test 3: Check for ORDERED status specifically
    console.log('\n🎯 Test 3: Checking ORDERED Status POs...');
    const orderedPOs = allPOs.filter(po => po.status === 'ORDERED');
    
    console.log(`   ORDERED POs: ${orderedPOs.length}`);
    if (orderedPOs.length > 0) {
      console.log('   ✅ ORDERED POs found - these should now appear in invoice dropdown');
      orderedPOs.forEach(po => {
        console.log(`     - ${po.id.slice(-8).toUpperCase()} | ${po.supplier.name} | ${po.items?.length || 0} items`);
      });
    } else {
      console.log('   ⚠️  No ORDERED POs found - create a PO and set it to ORDERED status to test');
    }

    // Test 4: Verify PO structure for auto-fill
    console.log('\n🔧 Test 4: Verifying PO Structure for Auto-fill...');
    if (eligiblePOs.length > 0) {
      const samplePO = eligiblePOs[0];
      console.log(`   Sample PO: ${samplePO.id.slice(-8).toUpperCase()}`);
      console.log(`   Has supplier: ${!!samplePO.supplier}`);
      console.log(`   Has items: ${!!samplePO.items && samplePO.items.length > 0}`);
      
      if (samplePO.items && samplePO.items.length > 0) {
        const sampleItem = samplePO.items[0];
        console.log('   Sample item structure:');
        console.log(`     - productId: ${!!sampleItem.productId}`);
        console.log(`     - product.name: ${!!sampleItem.product?.name}`);
        console.log(`     - quantity: ${sampleItem.quantity}`);
        console.log(`     - unitPrice: ${sampleItem.unitPrice}`);
        console.log('   ✅ PO structure is compatible with auto-fill');
      }
    }

    // Test Summary
    console.log('\n📊 Test Summary:');
    console.log(`   Total POs: ${allPOs.length}`);
    console.log(`   Eligible for invoicing: ${eligiblePOs.length}`);
    console.log(`   ORDERED status: ${orderedPOs.length}`);
    console.log(`   PARTIALLY_RECEIVED status: ${allPOs.filter(po => po.status === 'PARTIALLY_RECEIVED').length}`);
    console.log(`   RECEIVED status: ${allPOs.filter(po => po.status === 'RECEIVED').length}`);
    
    if (eligiblePOs.length > 0) {
      console.log('\n✅ SUCCESS: POs are available for invoice creation');
      console.log('   The invoice creation form should now show these POs in the dropdown');
    } else {
      console.log('\n⚠️  WARNING: No eligible POs found');
      console.log('   Create some POs and set them to ORDERED, PARTIALLY_RECEIVED, or RECEIVED status');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  const fetch = require('node-fetch');
  testPOInvoiceIntegration();
} else {
  // Browser environment
  console.log('Run this in the browser console on the invoice creation page to test the integration');
  window.testPOInvoiceIntegration = testPOInvoiceIntegration;
}
