// Test script to create a Purchase Order and test performance metrics
import { PrismaClient } from './src/generated/prisma/index.js';

const prisma = new PrismaClient();

async function createTestPO() {
  try {
    console.log('🧪 Creating test Purchase Order with performance data...');

    // Get first available supplier and product
    const supplier = await prisma.supplier.findFirst();
    const product = await prisma.product.findFirst();
    const user = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' }
    });

    if (!supplier || !product || !user) {
      console.log('❌ Missing required data. Please ensure you have suppliers, products, and users in the database.');
      return;
    }

    console.log(`✅ Using supplier: ${supplier.name}`);
    console.log(`✅ Using product: ${product.name}`);
    console.log(`✅ Using user: ${user.name}`);

    // Create a Purchase Order
    const po = await prisma.purchaseOrder.create({
      data: {
        createdById: user.id,
        supplierId: supplier.id,
        orderDate: new Date(),
        subtotal: 100000,
        tax: 10000,
        taxPercentage: 10,
        total: 110000,
        status: 'DRAFT',
        notes: 'Test PO for performance metrics testing',
        // Add some performance-related fields
        expectedDeliveryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        items: {
          create: [
            {
              productId: product.id,
              quantity: 10,
              unitPrice: 10000,
              subtotal: 100000,
            }
          ]
        }
      },
      include: {
        items: true,
        supplier: true,
      }
    });

    console.log(`✅ Created Purchase Order: ${po.id}`);

    // Create some stock batches to test batch metrics
    const stockBatch1 = await prisma.stockBatch.create({
      data: {
        productId: product.id,
        productSupplierId: null, // We'll leave this null for now
        batchNumber: `BATCH-${Date.now()}-1`,
        receivedDate: new Date(),
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        quantity: 5,
        remainingQuantity: 5,
        purchasePrice: 10000,
        purchaseOrderId: po.id,
        status: 'ACTIVE',
        notes: 'Test batch 1 - active'
      }
    });

    const stockBatch2 = await prisma.stockBatch.create({
      data: {
        productId: product.id,
        productSupplierId: null,
        batchNumber: `BATCH-${Date.now()}-2`,
        receivedDate: new Date(),
        expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days from now (expiring soon)
        quantity: 3,
        remainingQuantity: 3,
        purchasePrice: 10000,
        purchaseOrderId: po.id,
        status: 'ACTIVE',
        notes: 'Test batch 2 - expiring soon'
      }
    });

    const stockBatch3 = await prisma.stockBatch.create({
      data: {
        productId: product.id,
        productSupplierId: null,
        batchNumber: `BATCH-${Date.now()}-3`,
        receivedDate: new Date(),
        expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago (expired)
        quantity: 2,
        remainingQuantity: 0,
        purchasePrice: 10000,
        purchaseOrderId: po.id,
        status: 'EXPIRED',
        notes: 'Test batch 3 - expired'
      }
    });

    console.log(`✅ Created 3 test stock batches:`);
    console.log(`   - Active batch: ${stockBatch1.id}`);
    console.log(`   - Expiring batch: ${stockBatch2.id}`);
    console.log(`   - Expired batch: ${stockBatch3.id}`);

    // Create a status history entry
    await prisma.pOStatusHistory.create({
      data: {
        purchaseOrderId: po.id,
        fromStatus: 'DRAFT',
        toStatus: 'DRAFT',
        reason: 'BUSINESS_REQUIREMENT',
        notes: 'Initial creation',
        createdById: user.id,
        metadata: {
          timeInStatus: 0,
          performanceScore: 100,
          qualityScore: 100,
          supplierScore: 100,
          totalBatches: 3,
          activeBatches: 2,
          expiringBatches: 1,
          expiredBatches: 1
        }
      }
    });

    console.log(`✅ Created status history entry`);

    console.log('\n🎉 Test Purchase Order created successfully!');
    console.log(`📋 Purchase Order ID: ${po.id}`);
    console.log(`🔗 Test URL: http://localhost:3001/inventory/purchase-orders/${po.id}`);
    
    return po.id;

  } catch (error) {
    console.error('❌ Error creating test PO:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
createTestPO();
