// Simple script to check if the database schema has been updated
import { PrismaClient } from './src/generated/prisma/index.js';

async function checkDatabaseSchema() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Checking database schema...');
    console.log('📡 Connecting to database...');
    
    // Try to query with new fields
    console.log('📊 Testing StockAdjustment query with new fields...');
    
    try {
      const adjustments = await prisma.stockAdjustment.findMany({
        take: 1,
        include: {
          product: true,
          user: true,
          approvedBy: true
        }
      });
      
      console.log('✅ New schema fields are available');
      console.log('📋 Sample adjustment:', JSON.stringify(adjustments[0], null, 2));
      
    } catch (error) {
      console.log('❌ New schema fields not available:', error.message);
      
      // Try basic query
      console.log('🔄 Trying basic query...');
      const basicAdjustments = await prisma.stockAdjustment.findMany({
        take: 1,
        include: {
          product: true,
          user: true
        }
      });
      
      console.log('✅ Basic query works');
      console.log('📋 Sample adjustment (basic):', JSON.stringify(basicAdjustments[0], null, 2));
    }
    
    // Check if AdjustmentStatus enum exists
    console.log('🔍 Checking AdjustmentStatus enum...');
    try {
      const result = await prisma.$queryRaw`
        SELECT enumlabel FROM pg_enum 
        WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'AdjustmentStatus')
      `;
      console.log('✅ AdjustmentStatus enum values:', result);
    } catch (error) {
      console.log('❌ AdjustmentStatus enum not found:', error.message);
    }
    
    // Check table structure
    console.log('🔍 Checking StockAdjustment table structure...');
    try {
      const columns = await prisma.$queryRaw`
        SELECT column_name, data_type, is_nullable, column_default 
        FROM information_schema.columns 
        WHERE table_name = 'StockAdjustment' 
        ORDER BY ordinal_position
      `;
      console.log('📋 Table columns:', columns);
    } catch (error) {
      console.log('❌ Could not check table structure:', error.message);
    }
    
  } catch (error) {
    console.error('💥 Database connection failed:', error);
  } finally {
    console.log('🔌 Disconnecting from database...');
    await prisma.$disconnect();
    console.log('✅ Database check complete');
  }
}

// Run the check
checkDatabaseSchema();
