-- Manual migration to add stock adjustment approval workflow fields
-- Run this if the automatic migration fails

-- Add AdjustmentStatus enum
DO $$ BEGIN
    CREATE TYPE "AdjustmentStatus" AS ENUM ('PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'APPLIED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Add new notification types
DO $$ BEGIN
    ALTER TYPE "NotificationType" ADD VALUE 'STOCK_ADJUSTMENT_PENDING';
    ALTER TYPE "NotificationType" ADD VALUE 'STOCK_ADJUSTMENT_APPROVED';
    ALTER TYPE "NotificationType" ADD VALUE 'STOCK_ADJUSTMENT_REJECTED';
EXCEPTION
    WHEN duplicate_object THEN null;
    WHEN invalid_text_representation THEN null;
END $$;

-- Add new columns to StockAdjustment table
ALTER TABLE "StockAdjustment" 
ADD COLUMN IF NOT EXISTS "status" "AdjustmentStatus" NOT NULL DEFAULT 'PENDING_APPROVAL',
ADD COLUMN IF NOT EXISTS "approvedById" TEXT,
ADD COLUMN IF NOT EXISTS "approvedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "rejectedAt" TIMESTAMP(3),
ADD COLUMN IF NOT EXISTS "rejectionReason" TEXT;

-- Add foreign key constraint for approvedBy
DO $$ BEGIN
    ALTER TABLE "StockAdjustment" 
    ADD CONSTRAINT "StockAdjustment_approvedById_fkey" 
    FOREIGN KEY ("approvedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Update existing records to have APPLIED status (for backward compatibility)
UPDATE "StockAdjustment" 
SET "status" = 'APPLIED' 
WHERE "status" = 'PENDING_APPROVAL';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS "StockAdjustment_status_idx" ON "StockAdjustment"("status");
CREATE INDEX IF NOT EXISTS "StockAdjustment_approvedById_idx" ON "StockAdjustment"("approvedById");
