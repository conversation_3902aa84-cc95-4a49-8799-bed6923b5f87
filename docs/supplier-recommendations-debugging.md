# Supplier Recommendations Debugging Guide

## Issues Fixed

### 1. Authentication Error
**Problem**: API endpoints were checking for `auth.success` but `verifyAuthToken` returns `auth.authenticated`

**Fix Applied**:
```typescript
// Before (incorrect)
if (!auth.success) {
  return NextResponse.json({ error: auth.error }, { status: 401 });
}

// After (correct)
if (!auth.authenticated || !auth.user) {
  return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
}
```

**Files Updated**:
- `src/app/api/purchase-orders/supplier-recommendations/route.ts`
- `src/app/api/supplier-recommendations/route.ts`
- `src/app/api/supplier-recommendations/metrics/[supplierId]/route.ts`

### 2. Invalid Items Filtering
**Problem**: Empty or invalid productId values were being sent to the API

**Fix Applied**:
```typescript
// Filter out invalid items before sending to API
items={items
  .filter(item => item.productId && item.quantity > 0)
  .map(item => ({
    productId: item.productId,
    quantity: item.quantity
  }))}
```

**File Updated**: `src/app/inventory/purchase-orders/new/page.tsx`

### 3. Enhanced Error Handling
**Problem**: Errors in supplier scoring were causing complete failures

**Fix Applied**:
- Added try-catch blocks around individual scoring calculations
- Fallback scores for failed calculations
- Better error logging and debugging information
- Graceful degradation when quality/delivery metrics fail

**Files Updated**:
- `src/lib/supplier-selection-engine.ts`
- `src/components/purchase-orders/SupplierRecommendations.tsx`

## Debugging Steps

### 1. Check Browser Console
Open browser developer tools and look for:
```javascript
// These logs should appear when adding products to PO
console.log('Fetching recommendations for items:', validItems);
console.log('Response status:', response.status);
console.log('Recommendations data:', data);
```

### 2. Check Server Logs
Look for these logs in your terminal:
```
Getting supplier recommendation for product {productId}, quantity {quantity}
Found {count} suppliers for product {productName}
Error calculating supplier score for {supplierName}: {error}
```

### 3. Test API Directly
Use the provided test script:
```bash
node test-supplier-recommendations.js
```

Or test with curl:
```bash
curl -X POST http://localhost:3001/api/purchase-orders/supplier-recommendations \
  -H "Content-Type: application/json" \
  -H "Cookie: session-token=your-token" \
  -d '{"items":[{"productId":"test-id","quantity":10}]}'
```

### 4. Database Verification
Ensure you have:
- Active products with valid IDs
- ProductSupplier relationships
- Active suppliers
- Purchase order history (for delivery metrics)

```sql
-- Check if products exist
SELECT id, name FROM Product WHERE active = true LIMIT 5;

-- Check supplier relationships
SELECT ps.*, s.name as supplier_name, p.name as product_name 
FROM ProductSupplier ps
JOIN Supplier s ON ps.supplierId = s.id
JOIN Product p ON ps.productId = p.id
WHERE ps.isActive = true
LIMIT 5;
```

## Common Issues & Solutions

### Issue: "No valid items to analyze"
**Cause**: Items array contains empty productId or zero quantities
**Solution**: Ensure products are selected and quantities are > 0 before the component renders

### Issue: "Insufficient permissions"
**Cause**: User role doesn't have access to supplier recommendations
**Solution**: Ensure user has SUPER_ADMIN, WAREHOUSE_ADMIN, or FINANCE_ADMIN role

### Issue: "No suppliers found for this product"
**Cause**: Product has no active ProductSupplier relationships
**Solution**: Add suppliers to products in the Products management section

### Issue: Empty recommendations array
**Cause**: All supplier scoring failed or no active suppliers
**Solution**: Check server logs for scoring errors and verify supplier data

## Testing Checklist

- [ ] User is authenticated with proper role
- [ ] Products have active supplier relationships
- [ ] Items array contains valid productId and quantity values
- [ ] API endpoints return 200 status
- [ ] Supplier scoring calculations complete without errors
- [ ] Frontend displays recommendations correctly

## Performance Considerations

The supplier selection engine performs multiple database queries:
1. Product lookup with suppliers
2. Price comparison across suppliers
3. Quality metrics calculation
4. Delivery performance analysis

For better performance:
- Ensure database indexes on frequently queried fields
- Consider caching quality metrics for frequently accessed suppliers
- Monitor query performance in production

## Monitoring

Add these metrics to your monitoring:
- API response times for supplier recommendations
- Success/failure rates of recommendation requests
- Number of products without supplier recommendations
- User adoption of recommendation features

## Future Improvements

1. **Caching**: Cache supplier metrics for better performance
2. **Background Processing**: Pre-calculate recommendations for popular products
3. **Machine Learning**: Improve scoring based on historical selection patterns
4. **Real-time Updates**: Update recommendations when supplier data changes
