# Product Dropdown Display Fix

## Issue Description
The product dropdown in the purchase order creation page was displaying malformed text like "{ } [Supplier Name]" instead of proper product information after implementing the supplier recommendation system fixes.

## Root Cause Analysis

### Problem 1: Data Structure Mismatch
The API endpoint `/api/suppliers/[supplierId]/products` was returning a data structure that didn't match what the frontend expected:

**Before (Incorrect Structure)**:
```typescript
// API returned ProductSupplier data with nested product
{
  id: "ps-123", // ProductSupplier ID (wrong for frontend)
  productId: "prod-456",
  purchasePrice: 100,
  // ... other ProductSupplier fields
  product: {
    id: "prod-456", // Actual product ID
    name: "Product Name",
    sku: "SKU123"
  }
}
```

**Frontend Expected**:
```typescript
// Frontend expected product data at top level with nested supplierInfo
{
  id: "prod-456", // Product ID at top level
  name: "Product Name",
  sku: "SKU123",
  supplierInfo: {
    id: "ps-123", // ProductSupplier ID
    purchasePrice: 100,
    minimumOrderQuantity: 10,
    isPreferred: true
    // ... other supplier-specific fields
  }
}
```

### Problem 2: Interface Mismatch
The TypeScript interface in the frontend didn't match the actual data structure being returned by the API.

## Solution Implemented

### 1. Fixed API Data Transformation
Updated `/api/suppliers/[id]/products/route.ts` to transform data correctly:

```typescript
// Transform the data to match frontend expectations for PO creation
const transformedProducts = supplierProducts.map(sp => ({
  // Product fields at top level (for PO creation compatibility)
  id: sp.product.id, // Use product ID as the main ID
  name: sp.product.name,
  sku: sp.product.sku,
  basePrice: Number(sp.product.basePrice),
  purchasePrice: Number(sp.purchasePrice), // Fallback for legacy compatibility
  category: sp.product.category,
  unit: sp.product.unit,
  
  // Supplier-specific information nested under supplierInfo
  supplierInfo: {
    id: sp.id, // ProductSupplier relationship ID
    productId: sp.productId,
    supplierId: sp.supplierId,
    supplierProductCode: sp.supplierProductCode,
    supplierProductName: sp.supplierProductName,
    purchasePrice: Number(sp.purchasePrice),
    minimumOrderQuantity: sp.minimumOrderQuantity ? Number(sp.minimumOrderQuantity) : null,
    leadTimeDays: sp.leadTimeDays,
    isPreferred: sp.isPreferred,
    isActive: sp.isActive,
    notes: sp.notes,
    createdAt: sp.createdAt.toISOString(),
    updatedAt: sp.updatedAt.toISOString(),
  }
}));
```

### 2. Updated TypeScript Interface
Fixed the Product interface in `src/app/inventory/purchase-orders/new/page.tsx`:

```typescript
interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice?: number;
  purchasePrice?: number; // Fallback for legacy compatibility
  category?: {
    id: string;
    name: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  };
  // Supplier-specific information
  supplierInfo?: {
    id: string; // ProductSupplier relationship ID
    productId: string;
    supplierId: string;
    supplierProductCode?: string | null;
    supplierProductName?: string | null;
    purchasePrice: number;
    minimumOrderQuantity?: number | null;
    leadTimeDays?: number | null;
    isPreferred: boolean;
    isActive: boolean;
    notes?: string | null;
    createdAt: string;
    updatedAt: string;
  };
}
```

### 3. Enhanced Fallback Logic
Updated the `updateItem` function to handle both new and legacy price structures:

```typescript
// Use supplier-specific information if available
if (product.supplierInfo) {
  updatedItems[index].unitPrice = Number(product.supplierInfo.purchasePrice);
  updatedItems[index].quantity = Number(product.supplierInfo.minimumOrderQuantity) || 1;
  updatedItems[index].productSupplierId = product.supplierInfo.id;
} else {
  // Fallback to product's general purchase price
  updatedItems[index].unitPrice = product.purchasePrice || product.basePrice || 0;
  updatedItems[index].quantity = 1;
}
```

### 4. Added Debug Logging
Added console logging to help debug data structure issues:

```typescript
const data = await response.json();
console.log('Fetched supplier products:', data.products);
setProducts(data.products || []);
```

## Expected Behavior After Fix

The product dropdown should now correctly display:

1. **Product Name** - Clear, readable product name
2. **SKU** - Product SKU in parentheses
3. **Supplier Badge** - Blue badge showing supplier name
4. **Supplier Info** - Price, MOQ, preferred status, and supplier product code

Example display:
```
Product Name (SKU123)                    [Supplier Name]
Price: Rp 100,000 • MOQ: 10 • Preferred • Code: SUP-001
```

## Testing Steps

1. **Start Development Server**: `npm run dev`
2. **Navigate to PO Creation**: `/inventory/purchase-orders/new`
3. **Select a Supplier**: Choose any active supplier
4. **Check Product Dropdown**: Verify products display correctly
5. **Browser Console**: Check for debug logs showing correct data structure

## Files Modified

- `src/app/api/suppliers/[id]/products/route.ts` - Fixed data transformation
- `src/app/inventory/purchase-orders/new/page.tsx` - Updated interface and logic
- Added test script: `test-supplier-products.js`

## Backward Compatibility

The fix maintains backward compatibility by:
- Keeping `purchasePrice` at the product level as fallback
- Supporting both `basePrice` and `purchasePrice` fields
- Graceful handling when `supplierInfo` is missing

## Future Considerations

1. **Consistent Data Structure**: Consider standardizing the product data structure across all API endpoints
2. **Type Safety**: Implement stricter TypeScript types to catch structure mismatches earlier
3. **API Documentation**: Document the expected data structures for each endpoint
4. **Testing**: Add unit tests for data transformation logic

This fix ensures that the product dropdown displays correctly while maintaining the functionality of the supplier recommendation system.
