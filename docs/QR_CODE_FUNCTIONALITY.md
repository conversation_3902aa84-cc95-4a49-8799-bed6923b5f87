# Purchase Order QR Code Functionality

## Overview

The QR code functionality allows warehouse staff to generate QR codes for Purchase Orders that have reached RECEIVED status. These QR codes contain comprehensive batch tracking information and can be printed as physical stickers for inventory management.

## Features

### 1. QR Code Generation
- **Trigger**: Available only for Purchase Orders with `RECEIVED` status
- **Access Control**: Limited to `SUPER_ADMIN`, `WAREHOUSE_ADMIN`, and `FINANCE_ADMIN` roles
- **Location**: "Generate QR Code" button in the PO detail page action bar

### 2. QR Code Content Structure
The QR code contains structured JSON data with:

```json
{
  "type": "PURCHASE_ORDER_BATCH_INFO",
  "version": "1.0",
  "generatedAt": "ISO timestamp",
  "purchaseOrder": {
    "id": "PO ID",
    "orderDate": "ISO timestamp",
    "status": "RECEIVED",
    "total": "numeric value",
    "supplier": {
      "name": "Supplier name",
      "contactPerson": "Contact person",
      "phone": "Phone number"
    }
  },
  "batches": [
    {
      "id": "Batch ID",
      "batchNumber": "Batch number",
      "receivedDate": "ISO timestamp",
      "expiryDate": "ISO timestamp or null",
      "quantity": "numeric value",
      "remainingQuantity": "numeric value",
      "purchasePrice": "numeric value",
      "product": {
        "id": "Product ID",
        "name": "Product name",
        "sku": "Product SKU",
        "barcode": "Product barcode or null"
      },
      "supplier": {
        "name": "Supplier name"
      },
      "notes": "Batch notes or null"
    }
  ],
  "summary": {
    "totalBatches": "number",
    "totalProducts": "number",
    "totalValue": "numeric value"
  },
  "humanReadable": {
    "poNumber": "PO-XXXXXXXX",
    "supplier": "Supplier name",
    "receivedDate": "Formatted date",
    "batchCount": "number",
    "productList": ["Product names with batch info"]
  }
}
```

### 3. Display Features
- **QR Code Image**: 512x512 pixel PNG with error correction level M
- **Human-Readable Backup**: Summary information visible without scanning
- **Download**: Save QR code as PNG file
- **Print**: Print QR code with formatted information for physical stickers

### 4. Business Purpose
- **Physical Inventory Management**: Link physical products to digital batch records
- **Warehouse Operations**: Quick identification of batch information during stock management
- **Traceability**: Complete audit trail from PO to individual batches
- **Mobile Scanning**: QR codes can be scanned with standard QR code readers

## API Endpoints

### GET `/api/purchase-orders/[id]/qr-code`

**Parameters:**
- `format`: `json` (default) or `image`

**Response (format=json):**
```json
{
  "data": { /* QR data structure */ },
  "qrString": "JSON string for QR generation"
}
```

**Response (format=image):**
```json
{
  "qrCodeImage": "data:image/png;base64,...",
  "data": { /* QR data structure */ }
}
```

## Components

### QRCodeDisplay Component
- **Location**: `src/components/purchase-orders/QRCodeDisplay.tsx`
- **Features**: 
  - QR code generation and display
  - Download functionality
  - Print functionality with formatted layout
  - Human-readable information display
  - Error handling and loading states

### Integration Points
- **PO Detail Page**: `src/app/inventory/purchase-orders/[id]/page.tsx`
- **Modal Dialog**: Uses shadcn/ui Dialog component
- **Role-based Access**: Integrated with existing authentication system

## Usage Instructions

1. **Navigate** to a Purchase Order detail page
2. **Verify** the PO status is "RECEIVED"
3. **Click** the "Generate QR Code" button (visible only to authorized roles)
4. **Generate** the QR code in the modal dialog
5. **Download** or **Print** the QR code for physical use
6. **Attach** printed QR stickers to product packaging

## Technical Implementation

### Dependencies
- `qrcode`: QR code generation library
- `@types/qrcode`: TypeScript definitions
- Existing shadcn/ui components for UI

### Security
- Role-based access control
- Authentication required for API endpoints
- Input validation and error handling

### Performance
- QR codes generated on-demand
- Optimized image size (512x512)
- Efficient batch data querying

## Future Enhancements

Potential improvements could include:
- Batch-specific QR codes (individual batch stickers)
- QR code scanning functionality for inventory operations
- Integration with mobile inventory management apps
- Bulk QR code generation for multiple POs
