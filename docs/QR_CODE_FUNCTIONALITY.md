# Purchase Order QR Code Functionality

## Overview

The QR code functionality allows warehouse staff to generate QR codes for Purchase Orders that have reached RECEIVED status. These QR codes contain comprehensive batch tracking information and can be printed as physical stickers for inventory management.

## Features

### 1. QR Code Generation
- **Trigger**: Available only for Purchase Orders with `RECEIVED` status
- **Access Control**: Limited to `SUPER_ADMIN`, `WAREHOUSE_ADMIN`, and `FINANCE_ADMIN` roles
- **Location**: "Generate QR Code" button in the PO detail page action bar

### 2. QR Code Content Structure
The QR code contains human-readable text format optimized for standard QR code scanners:

```
PURCHASE ORDER BATCH INFO
PO: PO-XXXXXXXX
Supplier: Supplier Name
Received: Jun 23, 2025
Batches: 2
Products: 2
Total Value: Rp 15,000,000

BATCH DETAILS:
1. Product Name A
   Batch: BATCH-001
   Qty: 10 | Expiry: Dec 31, 2025
2. Product Name B
   Batch: BATCH-002
   Qty: 5 | Expiry: No expiry

Generated: Jun 23, 2025, 10:30 AM
NPOS Inventory System
```

**Structured Data (for API responses):**
The API also provides structured JSON data for programmatic access, while the QR code itself contains the human-readable format above.

### 3. Display Features
- **QR Code Image**: 512x512 pixel PNG with error correction level M
- **Human-Readable Content Preview**: Shows exactly what will appear when scanned
- **Print Quantity Options**: Select 1, 4, 9, 16, or 25 copies with automatic grid layout
- **Grid Layout Preview**: Visual preview of print arrangement (1x1, 2x2, 3x3, 4x4, 5x5)
- **Download**: Save single QR code as PNG file
- **Smart Print Layout**: Automatically scales QR codes and arranges in optimal grid for sticker sheets

### 4. Business Purpose
- **Physical Inventory Management**: Link physical products to digital batch records
- **Warehouse Operations**: Quick identification of batch information during stock management
- **Traceability**: Complete audit trail from PO to individual batches
- **Mobile Scanning**: QR codes can be scanned with standard QR code readers

## API Endpoints

### GET `/api/purchase-orders/[id]/qr-code`

**Parameters:**
- `format`: `json` (default) or `image`

**Response (format=json):**
```json
{
  "data": { /* QR data structure */ },
  "qrString": "JSON string for QR generation"
}
```

**Response (format=image):**
```json
{
  "qrCodeImage": "data:image/png;base64,...",
  "data": { /* QR data structure */ }
}
```

## Components

### QRCodeDisplay Component
- **Location**: `src/components/purchase-orders/QRCodeDisplay.tsx`
- **Features**: 
  - QR code generation and display
  - Download functionality
  - Print functionality with formatted layout
  - Human-readable information display
  - Error handling and loading states

### Integration Points
- **PO Detail Page**: `src/app/inventory/purchase-orders/[id]/page.tsx`
- **Modal Dialog**: Uses shadcn/ui Dialog component
- **Role-based Access**: Integrated with existing authentication system

## Usage Instructions

1. **Navigate** to a Purchase Order detail page
2. **Verify** the PO status is "RECEIVED"
3. **Click** the "Generate QR Code" button (visible only to authorized roles)
4. **Generate** the QR code in the modal dialog
5. **Download** or **Print** the QR code for physical use
6. **Attach** printed QR stickers to product packaging

## Technical Implementation

### Dependencies
- `qrcode`: QR code generation library
- `@types/qrcode`: TypeScript definitions
- Existing shadcn/ui components for UI

### Security
- Role-based access control
- Authentication required for API endpoints
- Input validation and error handling

### Performance
- QR codes generated on-demand
- Optimized image size (512x512)
- Efficient batch data querying

## Future Enhancements

Potential improvements could include:
- Batch-specific QR codes (individual batch stickers)
- QR code scanning functionality for inventory operations
- Integration with mobile inventory management apps
- Bulk QR code generation for multiple POs
