# Invoice Management Testing Guide

## 🎯 **Overview**
This guide provides step-by-step instructions for testing the invoice management functionality in the NPOS system.

## 📋 **Prerequisites**

### **1. Development Environment**
- Node.js and npm installed
- PostgreSQL database running
- NPOS application running on `http://localhost:3000`

### **2. Database Setup**
- Existing users with appropriate roles (SUPER_ADMIN, FINANCE_ADMIN, WAREHOUSE_ADMIN)
- Suppliers in the system
- Purchase orders with RECEIVED or PARTIALLY_RECEIVED status

## 🚀 **Step 1: Database Migration**

### **Apply Schema Changes**
```bash
# Generate and apply Prisma migration
npx prisma db push

# Or run the SQL migration manually
psql -d your_database_name -f prisma/migrations/add_invoice_management.sql
```

### **Verify Schema**
```bash
# Check if tables were created
npx prisma studio
# Look for Invoice, InvoiceItem, and InvoicePayment tables
```

## 🌱 **Step 2: Seed Test Data**

### **Run the Invoice Seeder**
```bash
# Make sure you're in the project root
node scripts/seed-invoice-test-data.js
```

### **Expected Output**
```
🌱 Starting invoice test data seeding...
📊 Found 3 users, 5 suppliers, 8 POs
📝 Creating 8 test invoices...
💰 Creating test payments...

✅ Invoice test data seeding completed!

📊 Summary:
Total Invoices: 8
Total Amount: IDR 15,750,000
Paid Amount: IDR 8,200,000

📈 Status Distribution:
APPROVED - PAID: 3 invoices
APPROVED - PARTIALLY_PAID: 2 invoices
APPROVED - UNPAID: 2 invoices
PENDING - UNPAID: 1 invoices

🎯 Test URLs:
- Invoice List: http://localhost:3000/invoices
- API Test: http://localhost:3000/api/invoices
- Summary API: http://localhost:3000/api/invoices/summary
```

## 🧪 **Step 3: API Testing**

### **Automated API Tests**
```bash
# Run API tests without authentication
node scripts/test-invoice-apis.js

# Run with session token (recommended)
node scripts/test-invoice-apis.js "your-session-token-here"
```

### **Get Session Token**
1. Open browser and log into NPOS system
2. Open Developer Tools (F12)
3. Go to Application/Storage > Cookies
4. Copy the "session-token" value
5. Use it in the test command above

### **Manual API Testing**

#### **Test Invoice List**
```bash
curl -X GET "http://localhost:3000/api/invoices" \
  -H "Cookie: session-token=your-token-here"
```

#### **Test Invoice Summary**
```bash
curl -X GET "http://localhost:3000/api/invoices/summary" \
  -H "Cookie: session-token=your-token-here"
```

#### **Test Invoice Detail**
```bash
curl -X GET "http://localhost:3000/api/invoices/INVOICE_ID_HERE" \
  -H "Cookie: session-token=your-token-here"
```

## 🖥️ **Step 4: Frontend Testing**

### **Access Invoice Management**
1. Start the development server: `npm run dev`
2. Navigate to: `http://localhost:3000/invoices`
3. Log in with admin credentials

### **Test Invoice List Page**

#### **Basic Functionality**
- [ ] Page loads without errors
- [ ] Summary cards display correct statistics
- [ ] Invoice table shows test data
- [ ] Pagination works (if more than 10 invoices)

#### **Filtering & Search**
- [ ] Status filter (Pending, Approved, Rejected, Cancelled)
- [ ] Payment status filter (Unpaid, Partially Paid, Paid, Overdue)
- [ ] Search by invoice number
- [ ] Search by supplier name
- [ ] Clear filters functionality

#### **Data Display**
- [ ] Invoice numbers are clickable links
- [ ] Status badges show correct colors
- [ ] Payment status badges show correct colors
- [ ] Currency amounts formatted correctly (IDR)
- [ ] Dates formatted correctly

### **Test Responsive Design**
- [ ] Desktop view (1920x1080)
- [ ] Tablet view (768x1024)
- [ ] Mobile view (375x667)
- [ ] Summary cards stack properly on mobile
- [ ] Table scrolls horizontally on mobile

## 🔧 **Step 5: API Endpoint Testing**

### **Invoice CRUD Operations**

#### **GET /api/invoices**
```javascript
// Test parameters
?page=1&limit=10
?status=APPROVED
?paymentStatus=PAID
?search=INV-2024
?supplierId=supplier-id
```

#### **GET /api/invoices/[id]**
- Test with valid invoice ID
- Test with invalid invoice ID
- Verify all related data is included

#### **PUT /api/invoices/[id]**
```javascript
// Test status updates
{
  "status": "APPROVED",
  "notes": "Approved for payment"
}
```

#### **DELETE /api/invoices/[id]**
- Test deletion restrictions
- Test role-based permissions

### **Payment Management**

#### **GET /api/invoices/[id]/payments**
- Test payment history retrieval
- Verify payment summary calculations

#### **POST /api/invoices/[id]/payments**
```javascript
// Test payment creation
{
  "amount": 1000000,
  "paymentMethod": "BANK_TRANSFER",
  "paymentReference": "TXN-123456",
  "notes": "Payment via bank transfer"
}
```

### **PO Integration**

#### **POST /api/purchase-orders/[id]/generate-invoice**
```javascript
// Test invoice generation from PO
{
  "invoiceNumber": "INV-2024-01-0001",
  "invoiceDate": "2024-01-15T00:00:00Z",
  "dueDate": "2024-02-15T00:00:00Z",
  "taxPercentage": 11,
  "includeAllItems": true
}
```

## 🔍 **Step 6: Error Handling Testing**

### **Authentication Errors**
- [ ] Test without session token (401 Unauthorized)
- [ ] Test with invalid session token (401 Unauthorized)
- [ ] Test with insufficient permissions (403 Forbidden)

### **Validation Errors**
- [ ] Test with invalid invoice data
- [ ] Test with missing required fields
- [ ] Test with invalid date formats
- [ ] Test with negative amounts

### **Business Logic Errors**
- [ ] Test duplicate invoice numbers
- [ ] Test invoice creation from non-received PO
- [ ] Test overpayment scenarios
- [ ] Test payment on unapproved invoice

## 📊 **Step 7: Data Integrity Testing**

### **Database Constraints**
- [ ] Foreign key constraints work correctly
- [ ] Unique constraints prevent duplicates
- [ ] Cascade deletes work properly

### **Calculation Accuracy**
- [ ] Subtotal calculations are correct
- [ ] Tax calculations are accurate
- [ ] Total amounts match subtotal + tax
- [ ] Payment status updates correctly

### **Status Transitions**
- [ ] Invoice status workflow is enforced
- [ ] Payment status updates automatically
- [ ] Approval workflow works correctly

## 🎯 **Step 8: Performance Testing**

### **Load Testing**
```bash
# Create more test data
for i in {1..100}; do
  node scripts/seed-invoice-test-data.js
done
```

### **Query Performance**
- [ ] Invoice list loads quickly with 100+ invoices
- [ ] Filtering and search perform well
- [ ] Pagination works efficiently
- [ ] Summary calculations are fast

## ✅ **Expected Results**

### **Successful Test Indicators**
- [ ] All API endpoints return expected status codes
- [ ] Frontend displays data correctly
- [ ] Filtering and search work as expected
- [ ] No console errors in browser
- [ ] Database constraints are enforced
- [ ] Calculations are accurate

### **Common Issues & Solutions**

#### **Database Connection Errors**
```bash
# Check database connection
npx prisma db pull
```

#### **Missing Dependencies**
```bash
# Install missing packages
npm install
```

#### **Authentication Issues**
- Verify user roles in database
- Check session token validity
- Ensure proper cookie handling

#### **Data Issues**
- Verify test data was seeded correctly
- Check foreign key relationships
- Ensure proper data types

## 🚨 **Troubleshooting**

### **If Tests Fail**
1. Check server logs for errors
2. Verify database schema is up to date
3. Ensure test data exists
4. Check authentication setup
5. Verify API endpoints are accessible

### **Debug Commands**
```bash
# Check database schema
npx prisma studio

# View server logs
npm run dev

# Test database connection
npx prisma db pull

# Reset and reseed data
npx prisma db push --force-reset
node scripts/seed-invoice-test-data.js
```

## 📝 **Test Checklist**

### **Database & Backend**
- [ ] Schema migration applied successfully
- [ ] Test data seeded correctly
- [ ] All API endpoints respond correctly
- [ ] Authentication and authorization work
- [ ] Data validation functions properly
- [ ] Business logic enforced correctly

### **Frontend**
- [ ] Invoice list page loads and displays data
- [ ] Filtering and search functionality works
- [ ] Responsive design works on all devices
- [ ] Status indicators display correctly
- [ ] Navigation and links work properly

### **Integration**
- [ ] PO to invoice generation works
- [ ] Payment recording functions correctly
- [ ] Status updates propagate properly
- [ ] Summary statistics are accurate

## 🎉 **Success Criteria**
The invoice management system is ready for production when:
- All automated tests pass
- Frontend displays data correctly
- All user workflows function properly
- Performance meets requirements
- Security and permissions work correctly
- Data integrity is maintained
