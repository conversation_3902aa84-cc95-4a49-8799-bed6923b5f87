# Intelligent Supplier Selection Engine

## Overview

The Intelligent Supplier Selection Engine is an AI-powered system that automatically recommends the best suppliers for products based on multiple weighted criteria. It integrates seamlessly with the existing NPOS purchase order workflow to help users make informed supplier decisions.

## Features

### 🎯 Multi-Criteria Scoring Algorithm
- **Price Competitiveness (30% weight)**: Compares prices across all suppliers for the same product
- **Quality Performance (25% weight)**: Based on return rates, defect rates, and quality history
- **Delivery Reliability (20% weight)**: On-time delivery rate and average delivery speed
- **MOQ Compatibility (15% weight)**: How well the order quantity matches minimum requirements
- **Preferred Status (10% weight)**: Bonus for being designated as preferred supplier

### 🔍 Intelligent Recommendations
- Single product recommendations with detailed scoring breakdown
- Bulk recommendations for multiple products
- Purchase order splitting strategies
- Confidence levels (high/medium/low) with reasoning
- Warning alerts for potential issues

### 📊 Performance Analytics
- Real-time supplier performance metrics
- Quality trend analysis
- Delivery performance tracking
- Cost optimization insights

## API Endpoints

### Get Single Product Recommendation
```
GET /api/supplier-recommendations?productId={id}&quantity={qty}
```

### Get Bulk Recommendations
```
POST /api/purchase-orders/supplier-recommendations
Body: {
  "items": [
    { "productId": "product-1", "quantity": 10 },
    { "productId": "product-2", "quantity": 5 }
  ]
}
```

### Get Supplier Metrics
```
GET /api/supplier-recommendations/metrics/{supplierId}
```

## Usage in Purchase Orders

### 1. Automatic Integration
When creating purchase orders, the system automatically:
- Analyzes all added products
- Scores available suppliers
- Provides recommendations with reasoning
- Suggests optimal PO splitting strategies

### 2. Interactive UI Components
- **SupplierRecommendations**: Main recommendation display
- **SupplierScoreBreakdown**: Detailed scoring analysis
- Real-time updates as items are added/modified

### 3. Smart Supplier Selection
```typescript
// Example: Get recommendation for a product
const recommendation = await SupplierSelectionEngine.getSupplierRecommendation(
  'product-id',
  requestedQuantity
);

// Apply recommendations to PO
handleRecommendationApply(recommendations);
```

## Scoring Algorithm Details

### Price Score Calculation
```typescript
// Inverse relationship: lower price = higher score
priceScore = 100 - ((currentPrice - minPrice) / (maxPrice - minPrice)) * 100
```

### Quality Score Integration
- Uses existing `supplier-quality-metrics.ts` system
- Factors in return rates, defect rates, and quality trends
- Default score of 75 for suppliers without quality data

### Delivery Score Calculation
```typescript
// Based on on-time delivery rate (70%) and speed (30%)
deliveryScore = (onTimeRate * 0.7) + (speedScore * 0.3)
```

### MOQ Compatibility
```typescript
// Penalty for orders below MOQ
if (requestedQuantity < moq) {
  penalty = Math.min(80, (shortfall / moq) * 100)
  moqScore = 100 - penalty
}
```

## Configuration

### Scoring Weights
```typescript
const WEIGHTS = {
  PRICE: 30,      // Price competitiveness
  QUALITY: 25,    // Quality performance
  DELIVERY: 20,   // Delivery reliability
  MOQ: 15,        // MOQ compatibility
  PREFERRED: 10,  // Preferred supplier status
};
```

### Confidence Thresholds
- **High Confidence**: Score ≥ 85 with gap ≥ 10 points
- **Medium Confidence**: Score ≥ 70
- **Low Confidence**: Score < 70

## Integration Points

### Existing Systems
- **Supplier Management**: Uses existing supplier-product relationships
- **Quality Metrics**: Integrates with `supplier-quality-metrics.ts`
- **Purchase Orders**: Seamless integration with PO creation workflow
- **Inventory Optimization**: Leverages existing reorder point logic

### Database Schema
- Utilizes existing `ProductSupplier` relationships
- Reads from `PurchaseOrder` delivery history
- Integrates with `SupplierReturn` quality data

## Benefits

### For Procurement Teams
- **Faster Decision Making**: Automated supplier scoring eliminates manual analysis
- **Cost Optimization**: Identifies most cost-effective suppliers automatically
- **Risk Mitigation**: Highlights quality and delivery concerns
- **Consistency**: Standardized evaluation criteria across all purchases

### For Business Operations
- **Improved Supplier Performance**: Data-driven supplier selection
- **Reduced Stockouts**: Better delivery reliability assessment
- **Cost Savings**: Optimized supplier selection based on total value
- **Quality Improvement**: Quality-aware supplier recommendations

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Predictive supplier performance
2. **Seasonal Adjustments**: Time-based supplier performance weighting
3. **Contract Integration**: Factor in contract terms and pricing agreements
4. **Supplier Capacity Management**: Real-time capacity checking
5. **Multi-Supplier Order Splitting**: Automated order distribution

### Advanced Analytics
- Supplier performance trending
- Market price analysis
- Supply chain risk assessment
- Demand-based supplier optimization

## Technical Implementation

### Core Engine
```typescript
class SupplierSelectionEngine {
  static async getSupplierRecommendation(productId, quantity)
  static async getBulkSupplierRecommendations(items)
  static async getSupplierMetrics(supplierId)
}
```

### React Components
```typescript
<SupplierRecommendations 
  items={items}
  onSupplierSelect={handleSupplierSelect}
  onRecommendationApply={handleRecommendationApply}
/>

<SupplierScoreBreakdown 
  supplier={supplierScore}
  productName={productName}
  requestedQuantity={quantity}
/>
```

## Testing

### Unit Tests
- Supplier scoring algorithm validation
- Edge case handling (no suppliers, single supplier)
- Bulk recommendation processing
- Score calculation accuracy

### Integration Tests
- API endpoint functionality
- Database integration
- UI component rendering
- End-to-end purchase order workflow

## Monitoring & Analytics

### Key Metrics
- Recommendation accuracy
- User adoption rate
- Cost savings achieved
- Supplier performance improvements

### Logging
- Recommendation requests and responses
- Supplier selection decisions
- Performance metric calculations
- Error tracking and debugging

This intelligent supplier selection engine represents a significant advancement in the NPOS system's automation capabilities, providing data-driven insights that help optimize procurement decisions while maintaining integration with existing workflows.
