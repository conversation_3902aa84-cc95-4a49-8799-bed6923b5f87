# Automatic Purchase Order Generation System

## Overview

The Automatic PO Generation System transforms the NPOS from reactive to proactive procurement by automatically monitoring inventory levels, analyzing consumption patterns, and generating intelligent purchase order suggestions when products hit their reorder points.

## Key Features

### 🤖 **Intelligent Monitoring**
- **Real-time Stock Analysis**: Continuously monitors inventory levels across all products
- **Consumption Pattern Recognition**: Analyzes historical sales data to predict future demand
- **Reorder Point Calculations**: Dynamic reorder points based on consumption rate, safety stock, and lead times
- **Trend Analysis**: Identifies increasing, stable, or decreasing demand patterns

### 🎯 **Smart Supplier Selection**
- **Integrated Supplier Engine**: Leverages the Intelligent Supplier Selection Engine for optimal supplier choice
- **Multi-criteria Scoring**: Considers price, quality, delivery reliability, MOQ compatibility, and preferred status
- **Automated Reasoning**: Provides clear explanations for supplier recommendations

### 📊 **Demand Forecasting Integration**
- **Predictive Analytics**: Uses existing demand forecasting system for accurate quantity recommendations
- **Seasonal Adjustments**: Factors in seasonal demand patterns and trends
- **Confidence Scoring**: Provides confidence levels for all predictions and recommendations

### 🚨 **Urgency-Based Prioritization**
- **Critical**: ≤3 days until stockout (immediate action required)
- **High**: 4-7 days until stockout (urgent action needed)
- **Medium**: 8-14 days until stockout (plan for ordering)
- **Low**: >14 days until stockout (monitor closely)

### 🔔 **Comprehensive Notification System**
- **Role-Based Alerts**: Targets SUPER_ADMIN, WAREHOUSE_ADMIN, and FINANCE_ADMIN
- **Multi-Channel Delivery**: In-app notifications, toast messages, and email alerts
- **Batch Notifications**: Groups multiple suggestions for efficiency
- **Daily Summaries**: Comprehensive daily reports with key metrics

## System Architecture

### Core Components

#### 1. **AutoPOGenerationEngine** (`src/lib/auto-po-generation-engine.ts`)
```typescript
class AutoPOGenerationEngine {
  // Main entry point for generating PO suggestions
  static async generatePOSuggestions(categoryId?: string)
  
  // Analyze individual products for reorder needs
  private static async analyzeProductForPOSuggestion(product: any)
  
  // Calculate consumption patterns and trends
  private static analyzeConsumptionRate(transactionItems: any[])
  
  // Determine urgency levels based on stockout timeline
  private static calculateUrgencyLevel(daysUntilStockout: number, trend: string)
}
```

#### 2. **API Endpoints**
- `GET /api/po-suggestions` - Retrieve current PO suggestions with filtering
- `POST /api/po-suggestions` - Force refresh PO suggestions
- `POST /api/po-suggestions/create-po` - Create POs from suggestions (single/bulk)
- `POST /api/cron/po-suggestions` - Scheduled task endpoint

#### 3. **UI Components**
- `POSuggestionsDashboard` - Main dashboard with filtering and bulk actions
- `POSuggestionDetails` - Detailed view with adjustment capabilities
- `POSuggestionsWidget` - Dashboard widget for quick overview

#### 4. **Notification Integration**
- Extended event system with PO suggestion event types
- Custom notification templates with urgency indicators
- Role-based targeting for procurement teams

## Configuration

### Default Settings
```typescript
private static readonly SAFETY_STOCK_DAYS = 15;
private static readonly LEAD_TIME_BUFFER_DAYS = 7;
private static readonly FORECAST_PERIOD_DAYS = 30;
```

### Urgency Thresholds
- **Critical**: ≤3 days (adjusted for demand trends)
- **High**: 4-7 days
- **Medium**: 8-14 days
- **Low**: >14 days

### Supplier Selection Weights
- Price Competitiveness: 30%
- Quality Performance: 25%
- Delivery Reliability: 20%
- MOQ Compatibility: 15%
- Preferred Status: 10%

## Usage Examples

### Generate PO Suggestions
```typescript
import { AutoPOGenerationEngine } from '@/lib/auto-po-generation-engine';

// Generate suggestions for all products
const result = await AutoPOGenerationEngine.generatePOSuggestions();

// Generate suggestions for specific category
const categoryResult = await AutoPOGenerationEngine.generatePOSuggestions('category-id');

console.log(`Generated ${result.suggestions.length} suggestions`);
console.log(`Critical: ${result.summary.criticalCount}`);
console.log(`Total estimated cost: ${result.summary.totalEstimatedCost}`);
```

### API Usage
```bash
# Get current suggestions
curl -X GET "/api/po-suggestions?urgency=critical&limit=10"

# Create PO from suggestion
curl -X POST "/api/po-suggestions/create-po" \
  -H "Content-Type: application/json" \
  -d '{
    "suggestionId": "po-suggestion-123",
    "adjustedQuantity": 50,
    "notes": "Urgent reorder for high-demand product"
  }'

# Bulk create POs
curl -X POST "/api/po-suggestions/create-po" \
  -H "Content-Type: application/json" \
  -d '{
    "suggestions": [
      {"suggestionId": "suggestion-1", "adjustedQuantity": 100},
      {"suggestionId": "suggestion-2", "adjustedQuantity": 50}
    ],
    "groupBySupplierId": true
  }'
```

### React Component Usage
```tsx
import POSuggestionsDashboard from '@/components/po-suggestions/POSuggestionsDashboard';

function ProcurementPage() {
  const handleCreatePO = async (suggestion, adjustedQuantity, notes) => {
    // Create PO logic
  };

  const handleBulkCreate = async (suggestions) => {
    // Bulk PO creation logic
  };

  return (
    <POSuggestionsDashboard
      onCreatePO={handleCreatePO}
      onBulkCreatePO={handleBulkCreate}
    />
  );
}
```

## Integration Points

### 1. **Inventory Optimization System**
- Extends existing `OptimizationRecommendation` interface
- Adds `auto_po_suggestion` recommendation type
- Integrates with existing analytics and reporting

### 2. **Supplier Selection Engine**
- Uses intelligent supplier scoring for recommendations
- Provides detailed reasoning for supplier choices
- Maintains consistency with manual supplier selection

### 3. **Demand Forecasting**
- Leverages existing forecasting algorithms
- Applies seasonal adjustments and trend analysis
- Uses confidence scoring for recommendation quality

### 4. **Notification System**
- Extends event system with new PO suggestion events
- Role-based notification targeting
- Multi-channel delivery (in-app, email, toast)

## Scheduling & Automation

### Cron Job Setup
```bash
# Run every 6 hours
0 */6 * * * curl -X POST "https://your-domain.com/api/cron/po-suggestions" \
  -H "Authorization: Bearer YOUR_CRON_SECRET"
```

### Environment Variables
```env
CRON_SECRET=your-secure-cron-secret-here
```

### Vercel Cron Configuration
```json
{
  "crons": [
    {
      "path": "/api/cron/po-suggestions",
      "schedule": "0 */6 * * *"
    }
  ]
}
```

## Monitoring & Analytics

### Key Metrics
- **Suggestion Accuracy**: Percentage of suggestions that result in actual POs
- **Stockout Prevention**: Reduction in stockout incidents
- **Cost Optimization**: Savings from optimal supplier selection
- **Response Time**: Time from suggestion to PO creation

### Dashboard Widgets
- Total active suggestions
- Critical/high priority counts
- Estimated investment required
- Top suppliers by suggestion volume

### Reporting
- Daily suggestion summaries
- Weekly procurement analytics
- Monthly supplier performance correlation
- Quarterly ROI analysis

## Benefits

### For Procurement Teams
- **Proactive Ordering**: Prevents stockouts before they occur
- **Intelligent Recommendations**: Data-driven supplier selection
- **Time Savings**: Reduces manual monitoring and analysis
- **Consistency**: Standardized procurement decisions

### For Business Operations
- **Improved Cash Flow**: Optimized ordering quantities and timing
- **Reduced Stockouts**: Maintains customer satisfaction
- **Better Supplier Relationships**: Consistent, predictable ordering
- **Cost Optimization**: Automatic supplier price comparison

### For Management
- **Visibility**: Clear insights into procurement needs and trends
- **Control**: Approval workflows for high-value orders
- **Analytics**: Comprehensive reporting on procurement efficiency
- **Risk Mitigation**: Early warning system for supply chain issues

## Future Enhancements

### Planned Features
1. **Machine Learning Integration**: Improve prediction accuracy over time
2. **Supplier Capacity Management**: Real-time capacity checking
3. **Contract Integration**: Factor in contract terms and pricing agreements
4. **Advanced Analytics**: Predictive analytics for demand patterns
5. **Mobile Notifications**: Push notifications for critical suggestions

### Advanced Automation
- **Auto-PO Creation**: Automatic PO generation for trusted suppliers
- **Dynamic Pricing**: Real-time price comparison and negotiation
- **Supply Chain Optimization**: Multi-tier supplier analysis
- **Seasonal Planning**: Automated seasonal inventory planning

This Automatic PO Generation System represents a significant advancement in procurement automation, transforming reactive ordering into intelligent, proactive inventory management.
