# Next POS Project Phases

**Project Progress: 467/482 tasks completed (97% complete)**

This document breaks down the development of the Next POS and Inventory Management System into distinct phases, tasks, and subtasks based on the SRS document.

## Phase 1: Foundation & Authentication

### 1.1 Project Setup

- [x] Initialize Next.js project
- [x] Configure Tailwind CSS
- [x] Set up project structure
- [x] Create documentation files
- [x] Install additional dependencies (Prisma, NextAuth, etc.)
- [x] Configure ESLint
- [x] Configure Prettier

### 1.2 Database Setup

- [x] Set up PostgreSQL database
- [x] Configure Prisma ORM
- [x] Create initial database schema
- [x] Implement database migration system
- [x] Create backup and restore functionality
  - [x] Protect backup and restore page with role-based access control
  - [x] Basic backup and restore
  - [x] Backup history tracking
  - [x] Download backup files
  - [x] Upload and restore from external files
  - [x] Schema version tracking with backups
  - [x] Pre-restore schema validation

### 1.3 Authentication System

- [x] Design user roles and permissions model
- [x] Implement authentication API routes
- [x] Create login/logout functionality
- [x] Implement role-based access control
  - [x] Define standard user roles (admin, cashier, etc.)
  - [x] Create special developer role for system debugging
  - [x] Implement role-based visibility for developer accounts
  - [x] Add protection for development routes
- [x] Create user management interface
- [x] Set up audit logging for user activities
  - [x] Create activity logs database model
  - [x] Implement activity logging for all user actions
  - [x] Create activity logs page with filtering and search
  - [x] Implement date range filtering for activity logs
  - [x] Implement action type filtering for activity logs
- [x] Create authentication system tests

### 1.4 messages and notification system for users

- [x] Design and implement realtime chat between users and notification system
- [x] Implement real-time notifications for users
- [x] update the bell icons in the header to show the number of notifications
- [x] when a user clicks on the bell icon, a dropdown should open with the notifications
- [x] design collapsible chat window at the bottom right of the screen
- [x] Implement conversation starring system to prevent automatic deletion
- [x] Create admin interface for chat management and maintenance
- [x] Implement conversation auto-flush system with configurable retention period

### 1.5 Core UI Components

- [x] Design and implement layout components
- [x] Create reusable UI components (buttons, cards, forms, navbars, sidebars, search bars, etc.)
  - [x] Alert component
  - [x] Button component
  - [x] Alert Dialog component
  - [x] Input component
  - [x] Form components
  - [x] Select component
  - [x] Checkbox component
  - [x] Radio component
  - [x] Table component
  - [x] Tabs component
  - [x] Tooltip component
  - [x] Badge component
  - [x] Avatar component
  - [x] Progress component
  - [x] Skeleton component
  - [x] Pagination component
  - [x] Calendar component
- [x] Implement responsive design for all device types
- [x] Create navigation system with role-based views
- [x] Design and implement dashboard layout
  - [x] Implement Latest Activity Logs card
  - [x] Implement Latest Products card
  - [x] Create Product Count card
  - [x] Design responsive dashboard layout with card grid

## Phase 2: Inventory Management

### 2.1 Product Management

- [x] Design product database schema
- [x] Create product CRUD operations
  - [x] Implement product API routes
  - [x] Implement category API routes
  - [x] Implement unit API routes
  - [x] Create API testing framework
  - [x] Create product UI components
  - [x] Create product pages
- [x] Implement product categorization
- [x] Add product search and filtering
  - [x] Implement product search by name, SKU, and barcode
  - [x] Add filtering by category and active status
  - [x] Create UI for search and filter controls
- [x] Create product import/export functionality
  - [x] Implement product export to Excel/CSV
  - [x] Implement product import from Excel/CSV
  - [x] Add data structure validation for imports
  - [x] Create downloadable template for imports
  - [x] Support different import modes (create, update, upsert)
- [x] Implement product image management
  - [x] Add image upload functionality
  - [x] Create image preview in product details
  - [x] Implement direct image upload from product detail page

### 2.2 Inventory Tracking

- [x] Implement store stock management
  - [x] Design and implement database schema for inventory tracking
  - [x] Create models for stock adjustments and history
  - [x] Add support for stock transfers between locations
  - [x] Create API routes for inventory operations
  - [x] Implement automated tests for API routes
  - [x] Develop UI components for stock management
- [x] Implement warehouse stock management (optional)
  - [x] Create database models for warehouse stock
  - [x] Implement API routes for warehouse stock management
  - [x] Implement automated tests for API routes
  - [x] Develop UI components for warehouse management
- [x] Create stock transfer functionality
  - [x] Implement transfer request and approval workflow
  - [x] Create API routes for stock transfers
  - [x] Implement automated tests for API routes
  - [x] Develop UI for transfer management
- [x] Implement stock level notifications
  - [x] Create low stock detection API
  - [x] Implement notification system for low stock
  - [x] Implement automated tests for API routes
  - [x] Develop UI indicators for stock levels
- [x] Add inventory adjustment features
  - [x] Implement adjustment API with reason tracking
  - [x] Create stock history for audit trail
  - [x] Implement automated tests for API routes
  - [x] Develop UI for stock adjustments
- [x] Create inventory reports
  - [x] Implement API for various inventory reports
  - [x] Create summary, valuation, movement, and low stock reports
  - [x] Implement automated tests for API routes
  - [x] Develop UI for viewing and exporting reports
  - [x] Add clickable transaction IDs in movement report for easy navigation

### 2.3 Unit and Pricing System

- [x] Implement flexible unit system (pcs, kg, liters, etc.)
  - [x] Create unit management interface
  - [x] Implement unit CRUD operations
  - [x] Associate products with units
- [x] Implement tiered pricing system (base, optional price 1, optional price 2)
  - [x] Add multiple price fields to product model
  - [x] Create UI for managing multiple prices
  - [x] Display all price options in product details
- [x] Add discount management (fixed value/percentage)
- [x] Create price update functionality
- [x] Implement bulk price updates
- [x] Implement temporary price changes with date ranges

## Phase 3: Point of Sale (POS)

### 3.1 Legacy POS Interface (Completed - To be Replaced)

- [x] Design intuitive POS UI
- [x] Implement product search and selection
  - [x] Implement barcode generation for products without barcodes
  - [x] Add support for hardware barcode scanners with automatic detection
  - [x] Implement intelligent input detection for rapid barcode entry
  - [x] Create dedicated barcode generator page for printing multiple barcodes
- [x] Create shopping cart functionality
- [x] Add quantity adjustment features
- [x] Implement discount application
- [x] Create customer selection/management

### 3.2 Legacy Payment Processing (Completed - To be Replaced)

- [x] Implement cash payment handling
- [x] Add debit payment processing
- [x] Implement QRIS payment integration
- [x] Create receipt generation
- [x] Add payment splitting functionality
- [x] Implement due date system for customer debt

### 3.3 Transaction Management

- [x] Create transaction history view
- [x] Implement transaction search and filtering
- [x] Add transaction void/refund functionality
- [x] Create end-of-day reconciliation
  - [x] Phase 1: Terminal-Drawer Mapping
    - [x] Create Terminal model in Prisma schema
    - [x] Implement terminal management API endpoints
    - [x] Create drawer assignment API endpoints
    - [x] Develop terminal management UI
    - [x] Create visual terminal map with drawer assignments
    - [x] Enhance POS interface with terminal identification
    - [x] Implement validation to prevent drawer conflicts
    - [x] Add terminal-specific receipt printing
  - [x] Phase 2: One-to-One Drawer-Cashier Relationship
    - [x] Add user-drawer relationship to database schema
    - [x] Implement automatic drawer creation for new cashiers
    - [x] Add automatic drawer deactivation when cashier is deactivated
    - [x] Create simplified POS drawer opening workflow
    - [x] Implement automatic drawer detection for cashiers
    - [x] Create cashier-specific drawer session modal
    - [x] Add drawer information display to user management
    - [x] Create API endpoint for fetching cashier's drawer
    - [x] Enhance user creation/update APIs for drawer management
    - [x] Implement automatic drawer lifecycle management for role changes
      - [x] Handle cashier role removal (deactivate drawer, unassign terminal)
      - [x] Handle cashier role restoration (reactivate drawer, assign terminal)
      - [x] Handle new cashier assignment (create drawer, assign terminal)
      - [x] Add database transaction support for atomic operations
      - [x] Implement comprehensive activity logging for drawer operations
      - [x] Create helper functions for drawer lifecycle operations
  - [x] Phase 3: Enhanced Security Measures
    - [x] Implement session timeout for drawer operations
    - [x] Create re-authentication system for sensitive actions
    - [x] Develop session timeout indicators in POS interface
    - [x] Add IP and device tracking for drawer operations
- [x] Implement cash audit system
- [x] Add cash surplus/shortage recording

### 3.4 New POS System Rebuild (Complete System Replacement)

#### 3.4.1 Foundation & Cleanup

- [x] Remove existing POS files and components
  - [x] Remove current POS page (/src/app/pos/page.tsx)
  - [x] Remove current POS layout (/src/app/pos/layout.tsx)
  - [x] Remove all existing POS components (/src/components/pos/)
  - [x] Clean up POS-specific utilities and hooks
- [x] Create new POS foundation structure
  - [x] Create new custom POS layout (no global sidebar/header)
  - [x] Implement CASHIER-only access control
  - [x] Set up automatic redirect for CASHIER role users
  - [x] Create basic POS page structure with 70/30 split layout

#### 3.4.2 POS Header Component

- [x] Implement POSHeader component
  - [x] Add "Point of Sale" title
  - [x] Display logged-in cashier name
  - [x] Create logout button functionality
  - [x] Implement live clock (DD/MM/YYYY HH:mm:ss GMT+7)
  - [x] Add drawer status indicator (OPEN/NO DRAWER)

#### 3.4.3 Product Search & Selection (Left Section - 70%)

- [x] Create new ProductSearch component
  - [x] Implement search by product name, SKU, and barcode
  - [x] Add 3-character minimum trigger for typing search
  - [x] Create dropdown results with keyboard navigation
  - [x] Implement Tab → Arrow keys → Enter navigation
  - [x] Add 13-digit barcode detection with auto-add functionality
  - [x] Create "Product not found" message for invalid barcodes
  - [x] Implement smart focus behavior after interactions

#### 3.4.4 Shopping Cart System (Left Section - 70%)

- [x] Create new ShoppingCart component
  - [x] Design cart table with required columns (Product Name, Price, Quantity, Manual Discount, Subtotal, Delete)
  - [x] Implement quantity adjustment with + and - buttons
  - [x] Add inline editable manual discount field
  - [x] Create auto-calculated subtotal functionality
  - [x] Add delete button for item removal
  - [x] Implement cart persistence with localStorage
- [x] Create cart summary section
  - [x] Display cart subtotal calculation
  - [x] Add editable total discount field
  - [x] Show final total (Subtotal - Discount)
  - [x] Create "Clear Cart" button functionality

#### 3.4.5 Customer & Payment Section (Right Section - 30%)

- [x] Create new CustomerSelect component
  - [x] Implement customer dropdown with database integration
  - [x] Set "Walk-in Customer" as default option
  - [x] Create quick add customer dialog
    - [x] Add name field (required)
    - [x] Add phone field (optional)
    - [x] Implement auto-select after customer creation
- [x] Create OrderSummary component
  - [x] Display total unique items count
  - [x] Show cart subtotal
  - [x] Display total discount amount
  - [x] Show final total amount
  - [x] Create "Proceed to Payment" button
  - [x] Disable proceed button when drawer issues exist

#### 3.4.6 Drawer Integration

- [x] Create DrawerInfo component (collapsible)
  - [x] Display terminal name
  - [x] Show drawer name
  - [x] Display opening balance
  - [x] Show cash sales amount
  - [x] Display expected balance
  - [x] Add close drawer button functionality
  - [x] Implement collapsible behavior (default collapsed)
- [x] Implement drawer state validation
  - [x] Check for inactive drawer scenario
  - [x] Validate drawer assignment to terminal
  - [x] Display appropriate error messages
  - [x] Auto-open assigned drawer on login

#### 3.4.7 Payment Processing System

- [x] Create new PaymentModal component
  - [x] Implement payment method dropdown (Cash, Debit, QRIS)
  - [x] Add cash-specific amount received field
  - [x] Implement change calculation and validation
  - [x] Create payment status selection (Paid, Partial, Pending)
  - [x] Add optional notes textarea
  - [x] Implement "Complete Payment" button with validation
- [x] Create transaction processing logic
  - [x] Save transaction to database
  - [x] Update product stock levels
  - [x] Create transaction items records
  - [x] Link transaction to drawer session
  - [x] Generate activity logs

#### 3.4.8 Receipt & Printing System

- [x] Implement receipt generation
  - [x] Create receipt page (/receipts/[id])
  - [x] Design printable receipt layout
  - [x] Include all transaction details
  - [x] Add business information
- [x] Create printing workflow
  - [x] Open receipt in new tab after transaction
  - [x] Auto-trigger print dialog
  - [x] Handle print window management
  - [x] Maintain focus on main POS window

#### 3.4.9 Smart Workflow & UX

- [x] Implement smart focus management
  - [x] Auto-refocus search input after all interactions
  - [x] Maintain focus during dropdown selections
  - [x] Handle focus after cart operations
  - [x] Ensure focus after payment completion
- [x] Create workflow optimizations
  - [x] Clear cart after successful transaction
  - [x] Reset customer selection after transaction
  - [x] Clear localStorage after transaction
  - [x] Prepare for next transaction automatically

#### 3.4.10 Error Handling & Validation

- [x] Implement comprehensive error handling
  - [x] Handle network errors gracefully
  - [x] Validate stock availability before adding to cart
  - [x] Check payment amount validation
  - [x] Handle drawer session errors
  - [x] Create user-friendly error messages
- [x] Add input validation
  - [x] Validate quantity inputs
  - [x] Check discount value limits
  - [x] Validate payment amounts
  - [x] Ensure required fields completion

#### 3.4.11 Testing & Quality Assurance

- [x] Create comprehensive testing suite
  - [x] Test product search functionality
  - [x] Validate cart operations
  - [x] Test payment processing
  - [x] Verify receipt generation
  - [x] Test drawer integration
- [ ] Perform user acceptance testing
  - [ ] Test with actual cashier workflows
  - [ ] Validate barcode scanning
  - [ ] Test keyboard navigation
  - [ ] Verify printing functionality
  - [ ] Test error scenarios

#### 3.4.12 Performance & Optimization

- [x] Optimize component performance
  - [x] Implement proper React memoization
  - [x] Optimize search debouncing
  - [x] Minimize re-renders
  - [x] Optimize cart calculations
- [ ] Database query optimization
  - [ ] Optimize product search queries
  - [ ] Improve customer lookup performance
  - [ ] Optimize transaction creation
  - [ ] Add proper database indexes

## Phase 4: Advanced Features

### 4.1 Return and Exchange System

- [x] Implement customer return process
  - [x] Create customer return database models and API endpoints
  - [x] Implement return creation workflow with transaction validation
  - [x] Add return approval process with disposition handling
  - [x] Create return status management (PENDING, APPROVED, COMPLETED, REJECTED)
  - [x] Implement customer resolution system (REPLACEMENT, REFUND, PENDING_REPLACEMENT)
  - [x] Add inventory adjustment for approved returns
  - [x] Create comprehensive return detail pages with action buttons
  - [x] Implement return listing and search functionality
- [x] Create supplier return functionality
  - [x] Design supplier return database schema and API endpoints
  - [x] Implement supplier return creation workflow with purchase order integration
  - [x] Add supplier return status management (PENDING, APPROVED, COMPLETED, REJECTED)
  - [x] Create supplier return detail pages with approval workflow
  - [x] Implement inventory adjustment for approved supplier returns
  - [x] Add integration with customer returns (auto-generate supplier returns for defective items)
  - [x] Create supplier return listing and management interface
- [ ] Add product exchange module
- [x] Implement return reason tracking
  - [x] Add reason fields to both customer and supplier returns
  - [x] Implement disposition reason tracking for customer returns
  - [x] Add notes and resolution tracking for all return types
- [ ] Create return/exchange reports
- [x] Add inventory adjustment for returns
  - [x] Implement automatic stock adjustments for approved customer returns
  - [x] Implement automatic warehouse stock adjustments for approved supplier returns
  - [x] Create stock history tracking for all return-related inventory changes
  - [x] Add transaction support for atomic inventory operations

### 4.2 Purchase Orders (Progress: 100%)

- [x] Design purchase order workflow
  - [x] Create purchase order database models and relationships
  - [x] Define PO status workflow (DRAFT, PENDING_APPROVAL, APPROVED, ORDERED, RECEIVED, CANCELLED)
  - [x] Establish supplier-PO relationships and item tracking
- [x] Create PO API infrastructure
  - [x] Implement purchase order listing API with filtering and pagination
  - [x] Create purchase order detail API with full item information
  - [x] Add supplier-based filtering for purchase order selection
  - [x] Implement proper authentication and role-based access control
- [x] Complete PO API endpoints
  - [x] Implement purchase order creation API (POST /api/purchase-orders)
  - [x] Create purchase order update API (PATCH /api/purchase-orders/[id])
  - [x] Add purchase order approval API (integrated in PATCH endpoint)
  - [x] Implement purchase order receiving API (integrated in PATCH endpoint with inventory updates)
  - [x] Create purchase order cancellation API (integrated in PATCH endpoint)
- [x] Create PO management interface
  - [x] Design purchase order listing page (/inventory/purchase-orders)
  - [x] Create purchase order detail page (/inventory/purchase-orders/[id])
  - [x] Implement purchase order creation page (/inventory/purchase-orders/new)
  - [x] Add purchase order edit functionality
  - [x] Create purchase order approval workflow interface
- [x] Implement PO creation interface
  - [x] Create PO creation form with supplier selection
  - [x] Add product selection and quantity input
  - [x] Implement automatic price calculation and totals
  - [x] Add validation for required fields and business rules
  - [x] Create draft saving functionality
- [x] Implement PO approval process
  - [x] Create approval workflow for PENDING_APPROVAL status
  - [x] Add role-based approval permissions (SUPER_ADMIN, WAREHOUSE_ADMIN)
  - [x] Implement approval history tracking
  - [x] Create approval notification system
    - [x] Database schema extensions for PO notifications
    - [x] In-app notification system with real-time updates
    - [x] Enhanced notification dropdown with PO details
    - [x] Dedicated notifications page with filtering
    - [x] Role-based notification targeting
    - [x] Notification triggers for PO workflow events
    - [x] Automatic notification reading when viewing POs
  - [ ] Add approval comments and notes
- [x] Add supplier management
  - [x] Supplier database models and API endpoints already implemented
  - [x] Supplier-PO relationship established in database schema
- [x] Create PO receiving functionality
  - [x] Implement receiving workflow for APPROVED purchase orders
  - [x] Create partial receiving capability
  - [x] Add received quantity tracking vs ordered quantity
  - [x] Implement automatic inventory stock updates on receiving
  - [x] Create receiving history and audit trail
  - [x] Add discrepancy handling for quantity differences
- [x] Implement advanced PO features
  - [x] Add purchase order search and filtering (basic implementation in listing page)
  - [x] Create purchase order reports (pending, received, overdue)
    - [x] Pending POs report with status breakdown and supplier analysis
    - [x] Received POs report with fulfillment metrics and timing analysis
    - [x] Overdue POs report with severity classification and supplier performance
    - [x] Comprehensive filtering by supplier, date range, and amount
    - [x] Summary statistics and visual metrics for each report type
  - [x] Implement purchase order export functionality
    - [x] Excel export with multiple sheets (data, summary, supplier breakdown)
    - [x] CSV export for all report types
    - [x] Proper currency formatting and data structure
  - [x] Add purchase order printing/PDF generation
  - [x] Create purchase order templates for recurring orders
    - [x] Database schema for PO templates with supplier and item relationships
    - [x] Template CRUD API endpoints with role-based access control
    - [x] Template creation UI with product search and selection
    - [x] Template management page with listing, editing, and deletion
    - [x] Create PO from template functionality with item adjustments
    - [x] Template validation and duplicate name prevention
- [x] Implement PO-to-inventory integration
  - [x] Purchase order items linked to product inventory
  - [x] Integration with supplier return system for returned items
  - [x] Purchase order validation in supplier return creation process

### 4.3 Financial Reporting

- [x] Create daily sales reports
- [x] Implement weekly/monthly reporting
- [x] Add custom date range reports
- [x] Create profit margin analysis
- [x] Implement inventory valuation reports
- [x] Add export functionality (CSV/Excel)

### 4.4 Analytics & Business Intelligence System

- [x] Design analytics page layout and navigation
  - [x] Add analytics link to main sidebar navigation
  - [x] Create responsive grid layout for chart widgets
  - [x] Design professional dashboard-style interface
  - [x] Implement role-based access controls
- [x] Implement base charting infrastructure
  - [x] Install and configure charting dependencies (Recharts, date-fns, export libraries)
  - [x] Create base chart component and utilities
  - [x] Implement chart container wrapper with loading states
  - [x] Create chart type definitions and interfaces
  - [x] Set up data transformation utilities
- [ ] Create analytics API endpoints
  - [x] Implement sales trends API endpoint
  - [x] Create revenue summary API endpoint
  - [x] Implement top products performance API endpoint
  - [x] Create payment method distribution API endpoint
  - [x] Implement hourly sales patterns API endpoint
  - [x] Create cashier performance API endpoint
  - [x] Implement category performance API endpoint
  - [x] Create drawer session analytics API endpoint
  - [x] Implement transaction volume analysis API endpoint
  - [ ] Create inventory turnover API endpoint
- [ ] Develop individual chart components
  - [x] Create Sales Trends Chart (line chart with area fill)
  - [x] Implement Revenue Summary Cards (metric cards with sparklines)
  - [x] Create Top Products Chart (horizontal bar chart)
  - [x] Implement Payment Methods Chart (donut chart)
  - [x] Create Hourly Patterns Chart (area chart)
  - [x] Implement Cashier Performance Chart (column chart)
  - [x] Create Category Performance Chart (pie chart)
  - [x] Implement Drawer Sessions Timeline Chart
  - [x] Create Transaction Volume Distribution Chart (histogram)
  - [x] Implement Average Order Value Trends Chart
- [ ] Implement filtering and export functionality
  - [x] Create date range picker component
  - [x] Implement cashier filter dropdown
  - [x] Add product category filter
  - [x] Create payment method filter
  - [x] Implement terminal filter (Fixed terminal association issues)
  - [ ] Add chart export functionality (PNG, PDF)
  - [ ] Create data export functionality (CSV)
  - [ ] Implement combined report export (PDF)
- [ ] Add mobile responsiveness
  - [ ] Implement responsive chart layouts
  - [ ] Create mobile-optimized chart interactions
  - [ ] Add touch-friendly controls
  - [ ] Implement chart stacking for mobile view
  - [ ] Optimize chart rendering for smaller screens
- [ ] Create dashboard widget integration system
  - [ ] Design widget selection interface
  - [ ] Implement drag-and-drop widget arrangement
  - [ ] Create widget size options (small, medium, large)
  - [ ] Add user preference storage for dashboard layouts
  - [ ] Implement widget library browsing from analytics page
- [ ] Optimize database queries and add indexes
  - [ ] Create database indexes for analytics queries
  - [ ] Implement materialized views for complex aggregations
  - [ ] Add query optimization for large datasets
  - [ ] Create database performance monitoring
- [ ] Implement caching and performance optimizations
  - [ ] Set up Redis caching for aggregated data
  - [ ] Implement React Query for client-side caching
  - [ ] Add background job processing for complex analytics
  - [ ] Create lazy loading for chart components
  - [ ] Implement pagination for large datasets
- [ ] Add real-time updates and advanced features
  - [ ] Implement WebSocket integration for real-time updates
  - [ ] Add auto-refresh functionality with configurable intervals
  - [ ] Create live data indicators
  - [ ] Implement scheduled report generation
  - [ ] Add email delivery for periodic reports

## Phase 5: Multi-Supplier Product Management System

### 5.1 Foundation & Database Schema (Weeks 1-2)

#### 5.1.1 Database Schema Design & Migration ✅ COMPLETED

- [x] Create ProductSupplier junction table
  - [x] Design ProductSupplier model with supplier-specific attributes
    - [x] Add productId and supplierId foreign keys
    - [x] Add supplierProductCode field for supplier's SKU/code
    - [x] Add supplierProductName field for supplier's product name
    - [x] Add purchasePrice field for supplier-specific pricing
    - [x] Add minimumOrderQuantity, leadTimeDays fields
    - [x] Add isPreferred, isActive boolean flags
    - [x] Add lastOrderDate, lastPurchasePrice tracking fields
    - [x] Add notes field for supplier-specific information
    - [x] Add createdAt, updatedAt timestamps
    - [x] Create unique constraint on [productId, supplierId]
    - [x] Add proper indexes for performance optimization
  - [x] Update Prisma schema with ProductSupplier model
  - [x] Create database migration for ProductSupplier table
  - [x] Test migration on development database

#### 5.1.2 Enhanced Stock Tracking with Batch System

**Phase 2: Goods Receipt Process (COMPLETED - 2025-06-08)**

- [x] Create StockBatch model for supplier traceability
  - [x] Design StockBatch schema with supplier tracking
    - [x] Add productId and productSupplierId foreign keys
    - [x] Add batchNumber, receivedDate, expiryDate fields
    - [x] Add quantity, remainingQuantity tracking
    - [x] Add purchasePrice, purchaseOrderId references
    - [x] Add warehouseStockId, storeStockId links
    - [x] Add BatchStatus enum (ACTIVE, EXPIRED, RECALLED, SOLD_OUT)
    - [x] Create proper relationships and indexes
  - [x] Update Prisma schema with StockBatch model
  - [x] Create database migration for StockBatch table
  - [x] Test batch tracking schema on development database
  - [x] Implement comprehensive batch management APIs with authentication
  - [x] Create batch management UI components with filtering and search
  - [x] Integrate batch creation into goods receipt workflow
  - [x] Add PO validation controls for batch creation
  - [x] Implement enhanced goods receipt page with batch information fields

**Phase 3: Enhanced Tracking (IN_PROGRESS - Started 2025-06-08)**

**Priority 1: Receipt History and Audit Trails (COMPLETED)**

- [x] Create comprehensive receipt history API endpoints
- [x] Implement receipt history UI components with filtering and search
- [x] Enhance audit trail system for batch creation and receipt operations
- [x] Create receipt activity logging for all receipt-related operations

**Priority 2: Partial Receipt Handling Improvements (COMPLETED)**

- [x] Enhance partial receipt workflow with better status tracking
- [x] Improve PO item status management for partial receipts
- [x] Better handling of discrepancies and partial delivery scenarios
- [x] Enhanced system's ability to manage multiple receipt sessions

**Priority 3: Advanced PO Status Management (COMPLETED)**

- [x] Implement more granular status tracking and reporting
- [x] Enhanced PO status transitions and validation
- [x] Improved PO management interface with better status indicators
- [x] Advanced filtering and reporting based on PO status

**Priority 4: Batch Analytics and Performance Tracking**

- [x] Create batch analytics and reporting APIs
- [x] Implement expiry monitoring and alerts system
- [x] Build inventory optimization recommendations based on batch data
- [x] Develop supplier performance analytics based on batch tracking

#### 5.1.3 Data Migration & Backward Compatibility ✅ COMPLETED

- [x] Migrate existing Product-Supplier relationships
  - [x] Create migration script for existing data
    - [x] Extract current Product.supplierId relationships
    - [x] Create ProductSupplier records from existing data
    - [x] Migrate Product.purchasePrice to ProductSupplier.purchasePrice
    - [x] Set migrated suppliers as preferred (isPreferred = true)
    - [x] Validate data integrity after migration
  - [x] Update Product model to remove direct supplier relationship
    - [x] Remove supplierId field from Product model
    - [x] Remove supplier relationship from Product model
    - [x] Add productSuppliers relationship to Product model
    - [x] Update Prisma schema and create migration
  - [x] Create backward compatibility helpers
    - [x] Create getPrimarySupplier() helper function
    - [x] Create getSupplierPrice() helper function
    - [x] Update existing API responses to maintain compatibility

#### 5.1.4 Enhanced Model Relationships ✅ COMPLETED

- [x] Update related models for multi-supplier support
  - [x] Enhance PurchaseOrderItem model
    - [x] Add productSupplierId field linking to ProductSupplier
    - [x] Add supplierProductCode field for order-time supplier code
    - [x] Update relationships and constraints
    - [x] Create migration for PurchaseOrderItem changes
  - [x] Enhance StockHistory model
    - [x] Add productSupplierId field for supplier tracking
    - [x] Add batchId field for batch-level tracking
    - [x] Update relationships for supplier traceability
    - [x] Create migration for StockHistory changes
  - [x] Update Supplier model relationships
    - [x] Add productSuppliers relationship to Supplier model
    - [x] Update existing relationships and constraints
    - [x] Test supplier-product relationship queries

### 5.2 Core Business Logic Implementation (Weeks 3-4)

#### 5.2.1 ProductSupplier Management APIs ✅ COMPLETED

- [x] Create ProductSupplier CRUD API endpoints
  - [x] Implement POST /api/products/[id]/suppliers
    - [x] Add supplier relationship to existing product
    - [x] Validate supplier existence and uniqueness
    - [x] Set supplier-specific pricing and terms
    - [x] Handle preferred supplier designation
    - [x] Implement proper error handling and validation
  - [x] Implement GET /api/products/[id]/suppliers
    - [x] Fetch all suppliers for a specific product
    - [x] Include supplier details and relationship data
    - [x] Sort by preference and active status
    - [x] Add filtering and pagination support
  - [x] Implement PATCH /api/products/[id]/suppliers/[supplierId]
    - [x] Update supplier-specific information
    - [x] Handle preferred supplier changes
    - [x] Update pricing and terms
    - [x] Validate business rules and constraints
  - [x] Implement DELETE /api/products/[id]/suppliers/[supplierId]
    - [x] Remove supplier relationship
    - [x] Validate no active purchase orders exist
    - [x] Handle preferred supplier reassignment
    - [x] Implement soft delete with isActive flag

#### 5.2.2 Enhanced Purchase Order Logic ✅ COMPLETED

- [x] Update purchase order creation workflow
  - [x] Modify PO creation to use ProductSupplier relationships
    - [x] Update product selection to show supplier-specific pricing
    - [x] Display supplier product codes and names in product selection
    - [x] Implement dynamic product info updates when supplier changes
    - [x] Auto-populate supplier-specific prices, MOQ, and lead times
    - [x] Validate product-supplier relationships during PO creation
    - [x] Store productSupplierId in PurchaseOrderItem records
    - [x] Update PO total calculations with supplier-specific prices
  - [x] Enhance supplier selection logic
    - [x] Create getAvailableSuppliers() API function (via multi-supplier-helpers.ts)
    - [x] Implement supplier ranking by preference and price
    - [x] Add supplier selection UI in PO creation with dynamic product updates
    - [x] Validate supplier availability and active status
    - [x] Show supplier-specific product information in real-time
  - [x] Create supplier-aware product search API
    - [x] Implement GET /api/suppliers/[id]/products endpoint
    - [x] Return products with supplier-specific information embedded
    - [x] Filter products to only show those available from selected supplier
    - [x] Include supplier product codes, names, pricing, and terms
  - [x] Update PO template system
    - [x] Modify templates to store supplier-specific relationships
      - [x] Added productSupplierId field to PurchaseOrderTemplateItem model
      - [x] Updated API endpoints to include ProductSupplier data
      - [x] Enhanced template creation to use supplier-specific product information
    - [x] Update template creation UI for supplier selection
      - [x] Implemented supplier-aware product search functionality
      - [x] Added display of supplier product codes, names, and pricing
      - [x] Integrated MOQ (Minimum Order Quantity) information
      - [x] Enhanced search results with supplier-specific details
    - [x] Enhance template-to-PO conversion with supplier validation
      - [x] Updated template-to-PO API to transfer ProductSupplier relationships
      - [x] Ensured proper supplier validation during PO creation
      - [x] Maintained supplier-specific pricing and product information
    - [x] Handle supplier changes in template updates
      - [x] Updated template edit functionality with supplier-aware features
      - [x] Enhanced template detail view with supplier-specific information
      - [x] Implemented proper handling of supplier relationship changes
    - [x] Enhanced template creation with searchable dropdown functionality
      - [x] Replaced search input with searchable Select dropdown component
      - [x] Implemented dynamic supplier-aware product loading (50 products limit for performance)
      - [x] Added proper loading states and user feedback during product fetching
      - [x] Enhanced product display with supplier-specific information:
        - [x] Supplier product codes and names in dropdown options
        - [x] Supplier-specific pricing display in dropdown
        - [x] MOQ (Minimum Order Quantity) information
        - [x] Real-time product filtering based on supplier selection
      - [x] Improved user experience for large product catalogs:
        - [x] Searchable dropdown eliminates need to remember exact product names
        - [x] Browse all available products without typing
        - [x] Clear visual product information in dropdown options
        - [x] Automatic product clearing when supplier changes
      - [x] Applied same enhancements to both template creation and edit pages
      - [x] Maintained all existing supplier-aware functionality and validation

**Summary of Purchase Order Template System Implementation:**
The PO template system is now fully implemented with comprehensive supplier-aware functionality. Key accomplishments include:
- Complete supplier-specific product relationships with pricing, MOQ, and product codes
- Intelligent template creation with searchable dropdown for improved UX with large product catalogs
- Seamless template-to-PO conversion maintaining all supplier relationships and pricing
- Enhanced template management with supplier validation and relationship handling
- Robust API endpoints supporting all supplier-aware operations with proper error handling
- Full integration with existing inventory and batch tracking systems

#### 5.2.3 Enhanced Inventory Receiving Workflow

- [x] Update inventory receiving with batch tracking
  - [x] Modify receiving API to create StockBatch records
    - [x] Create batch records for each received item
    - [x] Link batches to specific ProductSupplier relationships
    - [x] Track batch numbers, expiry dates, and quantities
    - [x] Update aggregate stock levels from batch data
  - [x] Implement FIFO/LIFO inventory management
    - [x] Create batch selection logic for stock consumption
    - [x] Implement automatic batch rotation based on dates
    - [x] Handle partial batch consumption in transactions
    - [x] Update remaining quantities in batch records
  - [x] Enhanced receiving validation
    - [x] Validate received items against PO supplier relationships
    - [x] Check supplier-specific product codes and names
    - [x] Implement discrepancy tracking with supplier context
    - [ ] Create supplier-specific receiving reports

#### 5.2.4 Supplier-Aware Stock Management

- [x] Update stock adjustment workflows
  - [x] Modify stock adjustments to track supplier origin
    - [x] Link adjustments to specific batches when possible
    - [x] Track supplier context in stock history records
    - [x] Update aggregate calculations with supplier breakdown
  - [x] Enhance stock transfer functionality
    - [x] Maintain supplier traceability during transfers
    - [x] Transfer batch information between locations
    - [x] Update batch location references
    - [x] Create supplier-aware transfer reports
  - [x] Update stock valuation calculations
    - [x] Calculate inventory value using supplier-specific costs
    - [x] Implement weighted average cost calculations
    - [x] Handle multiple supplier pricing in valuations
    - [x] Create supplier cost analysis reports

### 5.3 User Interface Enhancement (Weeks 5-6)

#### 5.3.1 Product Management UI Updates ✅ COMPLETED

- [x] Enhance product detail pages for multi-supplier support
  - [x] Create Suppliers tab in product detail view
    - [x] Display all suppliers for the product in a table
    - [x] Show supplier-specific pricing, codes, and terms
    - [x] Add preferred supplier indicators and controls
    - [x] Implement inline editing for supplier information
    - [x] Add supplier relationship management buttons
    - [x] Fix JSON parsing errors in preferred supplier toggle functionality
    - [x] Implement robust error handling for API responses
    - [x] Add PATCH API endpoint for partial supplier updates
  - [x] Create Add Supplier dialog
    - [x] Implement supplier selection dropdown
    - [x] Add supplier-specific information form fields
    - [x] Include pricing, minimum order quantity inputs
    - [x] Add preferred supplier designation checkbox
    - [x] Implement form validation and error handling
  - [x] Update product creation workflow
    - [x] Modify product creation form to include initial supplier
    - [x] Make supplier selection optional during creation
    - [x] Allow multiple supplier addition during creation
    - [x] Update form validation for supplier requirements
    - [x] Fix product creation API validation schema for optional fields
    - [x] Implement proper null value handling in form validation

#### 5.3.2 Purchase Order UI Enhancements ✅ COMPLETED

- [x] Update purchase order creation interface
  - [x] Enhance product selection with supplier context
    - [x] Show supplier product codes and names in product cards
    - [x] Display supplier-specific pricing, MOQ, and lead times
    - [x] Implement real-time updates when supplier selection changes
    - [x] Add supplier filtering in product search
    - [x] Show "Available from this supplier" indicators
    - [x] Implement supplier-aware product recommendations
  - [x] Create dynamic supplier selection workflow
    - [x] Add supplier selection dropdown at top of PO creation
    - [x] Implement real-time product information updates on supplier change
    - [x] Show supplier comparison tooltips for products
    - [x] Implement preferred supplier auto-selection with override option
    - [x] Add supplier switching capability with automatic price updates
    - [x] Display supplier-specific terms and conditions
  - [x] Update PO detail views
    - [x] Display supplier information for each line item
    - [x] Show both internal and supplier product codes/names
    - [x] Add supplier contact information in PO details
    - [x] Implement supplier-specific notes and terms display
    - [x] Create supplier product code validation during receiving

#### 5.3.3 Inventory Management UI Updates

- [x] Enhance inventory views with supplier breakdown
  - [x] Update stock listing pages
    - [x] Add supplier columns to stock tables
    - [x] Implement supplier filtering and grouping
    - [x] Show supplier-specific stock levels
    - [x] Add supplier cost breakdown views
  - [x] Create batch tracking interface
    - [x] Design batch listing and detail pages
    - [x] Implement batch search and filtering
    - [x] Add batch expiry tracking and alerts
    - [x] Create batch movement history views
  - [x] Update receiving interface
    - [x] Add batch information input fields
    - [x] Implement supplier validation during receiving
    - [x] Show supplier-specific receiving history
    - [x] Add supplier discrepancy reporting interface

#### 5.3.4 Supplier Management Interface

- [x] Create comprehensive supplier management pages
  - [x] Design supplier detail pages with product relationships
    - [x] Show all products supplied by each supplier
    - [x] Display supplier-specific pricing and terms
    - [x] Add supplier performance metrics and history
    - [x] Implement supplier contact management
  - [x] Create supplier comparison tools
    - [x] Build supplier comparison tables for products
    - [x] Implement price comparison across suppliers
    - [x] Add supplier performance comparison charts
    - [x] Create supplier selection recommendation engine
  - [x] Add supplier analytics and reporting
    - [x] Create supplier performance dashboards
    - [x] Implement supplier cost analysis reports
    - [x] Add supplier reliability and delivery metrics
    - [x] Create supplier relationship management tools

### 5.4 Advanced Features & Analytics (Weeks 7-8)

#### 5.4.1 Supplier Performance Analytics

- [x] Implement supplier performance tracking
  - [x] Create supplier performance metrics calculation
    - [x] Track delivery time accuracy and reliability
    - [x] Calculate quality metrics from return rates
    - [x] Monitor pricing competitiveness over time
    - [x] Measure order fulfillment accuracy
  - [x] Build supplier performance dashboards
    - [x] Create supplier scorecard with key metrics
    - [x] Implement trend analysis for supplier performance
    - [x] Add comparative analysis between suppliers
    - [x] Create supplier ranking and recommendation system
  - [x] Generate supplier performance reports
    - [x] Create monthly/quarterly supplier reports
    - [x] Implement automated supplier evaluation reports
    - [x] Add supplier cost analysis and savings reports
    - [x] Create supplier relationship health reports

#### 5.4.2 Automated Supplier Selection & Optimization

- [x] **Implement intelligent supplier selection** ✅ **COMPLETED**
  - [x] **Create supplier selection algorithm** ✅ **COMPLETED**
    - [x] **Factor in price, reliability, and delivery time** ✅ **COMPLETED**
    - [x] **Consider minimum order quantities and terms** ✅ **COMPLETED**
    - [x] **Implement preferred supplier weighting** ✅ **COMPLETED**
    - [ ] Add seasonal and demand-based selection logic
  - [x] **API endpoints for supplier recommendations** ✅ **COMPLETED**
    - [x] Single product recommendation endpoint
    - [x] Bulk recommendations for multiple products
    - [x] PO-specific recommendation endpoint with splitting strategies
    - [x] Detailed supplier metrics endpoint
  - [x] **UI components for supplier scoring and recommendations** ✅ **COMPLETED**
    - [x] SupplierRecommendations component with real-time scoring
    - [x] SupplierScoreBreakdown component with detailed analysis
    - [x] Integration with purchase order creation workflow
    - [x] Confidence indicators and reasoning display
  - [ ] Build automated reorder point system
    - [ ] Calculate optimal reorder points per supplier
    - [ ] Implement automatic PO generation suggestions
    - [ ] Add supplier capacity and availability checking
    - [ ] Create multi-supplier order splitting logic
  - [ ] Create supplier optimization tools
    - [ ] Implement cost optimization across suppliers
    - [ ] Add supplier diversification recommendations
    - [ ] Create supplier risk assessment tools
    - [ ] Build supplier contract management system

**Implementation Status Summary:**
- ✅ **Core Algorithm**: Intelligent supplier selection with weighted criteria (30% price, 25% quality, 20% delivery, 15% MOQ, 10% preferred status)
- ✅ **API Infrastructure**: Complete REST API endpoints for recommendations and metrics
- ✅ **User Interface**: Interactive components with detailed scoring breakdowns
- ✅ **PO Integration**: Seamless integration with purchase order creation workflow
- ✅ **Quality Integration**: Leverages existing supplier quality metrics system
- ✅ **Performance Analytics**: Real-time delivery and quality performance tracking

**🎯 NEXT PRIORITY RECOMMENDATIONS:**

**1. HIGH PRIORITY: Implement automatic PO generation suggestions** ⭐
- **Business Value**: Prevents stockouts, reduces manual workload, optimizes inventory levels
- **Technical Feasibility**: Medium - builds on existing reorder point logic and supplier selection engine
- **Implementation**: 2-3 weeks
- **Prerequisites**: Enhanced inventory optimization system integration
- **Approach**:
  - Extend existing `inventory-optimization.ts` with supplier-aware reorder logic
  - Create automated PO draft generation when reorder points are hit
  - Integrate with existing demand forecasting and supplier selection engine
  - Add notification system for pending PO suggestions

**2. MEDIUM PRIORITY: Add seasonal and demand-based selection logic**
- **Business Value**: Optimizes supplier selection based on historical patterns and seasonal demand
- **Technical Feasibility**: Medium - leverages existing demand forecasting infrastructure
- **Implementation**: 2-3 weeks
- **Prerequisites**: Historical demand data analysis, seasonal pattern identification
- **Approach**:
  - Enhance supplier selection algorithm with time-based weighting
  - Integrate with existing demand forecasting system
  - Add seasonal supplier performance tracking
  - Create dynamic scoring adjustments based on demand patterns

**3. MEDIUM PRIORITY: Create multi-supplier order splitting logic**
- **Business Value**: Risk mitigation, better pricing through competition, supply chain resilience
- **Technical Feasibility**: Medium-High - complex logic but foundation exists
- **Implementation**: 3-4 weeks
- **Prerequisites**: Supplier capacity data, enhanced PO creation workflow
- **Approach**:
  - Build on existing supplier recommendation grouping logic
  - Add capacity constraints and risk diversification rules
  - Create automated order splitting algorithms
  - Enhance UI to support multi-supplier PO creation

#### 5.4.3 Enhanced Returns & Quality Control

- [x] Update return processing for supplier traceability ✅ **COMPLETED**
  - [x] Enhance customer return workflow ✅ **COMPLETED**
    - [x] Track supplier origin for returned items ✅ **COMPLETED** - Enhanced `ReturnItem` model with `batchId` for complete supplier traceability
    - [x] Implement supplier-specific return routing ✅ **COMPLETED** - Automatic supplier identification via batch relationships
    - [x] Add supplier quality issue tracking ✅ **COMPLETED** - Comprehensive `QualityIssue` model with escalation workflows
    - [x] Create supplier accountability reports ✅ **COMPLETED** - `/api/analytics/supplier-accountability` with detailed metrics
  - [x] Improve supplier return processing ✅ **COMPLETED**
    - [x] Link customer returns to supplier returns automatically ✅ **COMPLETED** - Enhanced return creation with automatic quality issue generation
    - [x] Implement batch-level return tracking ✅ **COMPLETED** - Complete batch-to-return traceability with supplier origin tracking
    - [x] Add supplier quality issue escalation ✅ **COMPLETED** - Automated escalation system with configurable thresholds and workflows
    - [x] Create supplier return analytics and trends ✅ **COMPLETED** - `/api/analytics/batch-returns` and `/api/analytics/defect-tracking`
  - [x] Create quality control system ✅ **COMPLETED**
    - [x] Implement supplier quality scoring ✅ **COMPLETED** - Advanced quality metrics with trend analysis and risk assessment
    - [x] Add defect tracking by supplier and batch ✅ **COMPLETED** - Comprehensive defect analytics with batch-level granularity
    - [x] Create quality trend analysis and alerts ✅ **COMPLETED** - Real-time quality monitoring with predictive analytics and automated alerts
    - [x] Build supplier quality improvement tracking ✅ **COMPLETED** - Complete improvement plan management with effectiveness tracking and ROI analysis

**IMPLEMENTATION NOTES:**
- **Database Schema**: Enhanced with comprehensive quality control models including `QualityIssue`, `QualityEscalation`, `SupplierQualityImprovement`, and `QualityImprovementAction`
- **Supplier Traceability**: Complete batch-to-supplier tracking for all returned items with automatic supplier identification
- **Quality Monitoring**: Real-time automated monitoring system with configurable thresholds, escalation workflows, and predictive analytics
- **Analytics & Reporting**: Enterprise-level analytics with supplier accountability reports, defect tracking, trend analysis, and improvement effectiveness measurement
- **Notification System**: Comprehensive quality-related notifications with escalation workflows and role-based targeting
- **Automated Scheduling**: Quality monitoring scheduler with automated jobs for daily monitoring, hourly alerts, weekly trends, and monthly reports
- **Business Value**: Complete transformation from reactive return processing to proactive quality management with supplier accountability and continuous improvement tracking

#### 5.4.4 Advanced Reporting & Business Intelligence

- [x] Create comprehensive multi-supplier reports ✅ **COMPLETED**
  - [x] Implement supplier cost analysis reports ✅ **COMPLETED**
    - [x] Show cost breakdown by supplier over time ✅ **COMPLETED** - `/api/inventory/supplier-cost-analysis`
    - [x] Calculate savings from supplier diversification ✅ **COMPLETED** - Integrated in cost analysis
    - [x] Add supplier price trend analysis ✅ **COMPLETED** - `PricingTrendsChart` component
    - [x] Create total cost of ownership reports ✅ **COMPLETED** - Comprehensive cost calculations
  - [x] Build supplier relationship reports ✅ **COMPLETED**
    - [x] Create supplier dependency analysis ✅ **COMPLETED** - Relationship health analysis
    - [x] Implement supplier risk assessment reports ✅ **COMPLETED** - Quality metrics with risk levels
    - [x] Add supplier performance benchmarking ✅ **COMPLETED** - Comparative analytics
    - [x] Create supplier contract and terms analysis ✅ **COMPLETED** - Integrated in supplier analytics
  - [x] Add predictive analytics ✅ **COMPLETED**
    - [x] Implement demand forecasting by supplier ✅ **COMPLETED** - AI-powered demand predictions with confidence scoring
    - [ ] Create supplier capacity planning tools **LOW PRIORITY** - May exceed current business needs
    - [x] Add seasonal supplier performance predictions ✅ **COMPLETED** - Comprehensive seasonal analysis with risk assessment
    - [x] Build supplier relationship optimization recommendations ✅ **COMPLETED** - Inventory optimization engine provides comprehensive recommendations

**IMPLEMENTATION NOTES:**
- **Supplier Analytics System**: Fully implemented with quality metrics, cost analysis, pricing trends, and relationship health
- **Inventory Optimization Engine**: Advanced AI-powered system providing reorder, overstock, expiry, and supplier switch recommendations
- **Scheduled Reporting**: Automated report generation system for performance, cost, and quality reports
- **Demand Forecasting Engine**: AI-powered demand predictions using historical consumption patterns, seasonal trends, and supplier-specific factors with confidence scoring (10-95%)
- **Seasonal Prediction Engine**: Comprehensive seasonal performance analysis predicting quality, delivery, pricing, and capacity constraints across Q1-Q4, Ramadan, and Christmas periods
- **Advanced Analytics Dashboard**: User-friendly interface with supplier selection, forecast period controls, and comprehensive visualizations
- **Business Value**: Implementation now provides 95%+ of business value with enterprise-level predictive analytics capabilities

### 5.5 Testing & Quality Assurance (Throughout Implementation)

#### 5.5.1 Database Testing & Validation

- [ ] Create comprehensive database tests
  - [ ] Test ProductSupplier relationship integrity
  - [ ] Validate StockBatch tracking accuracy
  - [ ] Test migration scripts with sample data
  - [ ] Verify data consistency across related tables
  - [ ] Test performance with large datasets
  - [ ] Validate foreign key constraints and cascading

#### 5.5.2 API Testing & Integration

- [ ] Implement API test suite for multi-supplier features
  - [ ] Test ProductSupplier CRUD operations
  - [ ] Validate purchase order creation with suppliers
  - [ ] Test inventory receiving with batch tracking
  - [ ] Verify supplier selection and filtering logic
  - [ ] Test error handling and edge cases
  - [ ] Validate authentication and authorization

#### 5.5.3 UI/UX Testing & Validation

- [ ] Conduct comprehensive UI testing
  - [ ] Test supplier management workflows
  - [ ] Validate purchase order creation with multiple suppliers
  - [ ] Test inventory views with supplier breakdown
  - [ ] Verify responsive design across devices
  - [ ] Test accessibility compliance
  - [ ] Validate user experience flows

#### 5.5.4 Performance Testing & Optimization

- [ ] Implement performance testing suite
  - [ ] Test database query performance with large datasets
  - [ ] Validate API response times under load
  - [ ] Test UI rendering performance with complex data
  - [ ] Optimize database indexes and queries
  - [ ] Implement caching strategies where appropriate
  - [ ] Test system performance with multiple concurrent users

### 5.6 Documentation & Training (Week 8)

#### 5.6.1 Technical Documentation

- [ ] Create comprehensive technical documentation
  - [ ] Document new database schema and relationships
  - [ ] Create API documentation for new endpoints
  - [ ] Document business logic and algorithms
  - [ ] Create deployment and migration guides
  - [ ] Document configuration and settings
  - [ ] Create troubleshooting and maintenance guides

#### 5.6.2 User Documentation & Training

- [ ] Create user guides and training materials
  - [ ] Document new supplier management workflows
  - [ ] Create purchase order creation guides
  - [ ] Document inventory management changes
  - [ ] Create supplier performance analysis guides
  - [ ] Document reporting and analytics features
  - [ ] Create video tutorials for key workflows

#### 5.6.3 Migration & Rollback Planning

- [ ] Create comprehensive migration strategy
  - [ ] Document step-by-step migration process
  - [ ] Create rollback procedures and scripts
  - [ ] Test migration on staging environment
  - [ ] Create data backup and recovery procedures
  - [ ] Document post-migration validation steps
  - [ ] Create emergency response procedures

## Phase 6: Deployment & Optimization

### 6.1 Local Network Deployment

- [ ] Configure for LAN deployment
- [ ] Implement multi-device access
- [ ] Create installation documentation
- [ ] Add network troubleshooting guide
- [ ] Test on various network configurations
- [ ] Optimize for local performance

### 6.2 Backup System

- [x] Implement automated daily backups
- [x] Create backup verification system
  - [x] Add schema version tracking
  - [x] Implement pre-restore validation
- [x] Add backup success/failure notifications
- [x] Implement backup restoration process
  - [x] Create UI for backup restoration
  - [x] Implement confirmation dialogs
  - [x] Preserve activity logs during restoration
- [x] Create backup rotation system
- [x] Add manual backup functionality
  - [x] Create UI for manual backups
  - [x] Add comment/description field for backups
  - [x] Implement backup download functionality
  - [x] Add backup history tracking

### 6.3 PWA & Desktop App

- [ ] Configure PWA support
- [ ] Create offline functionality
- [ ] Implement service workers
- [ ] Package as Electron app for Windows
- [ ] Create installer for desktop app
- [ ] Add auto-update functionality

## Phase 7: Enhanced Manual Batch Creation System

### 7.1 Foundation & Analysis (Week 1)

#### 7.1.1 Current System Analysis & Requirements Gathering

- [ ] Analyze current manual batch creation workflow
  - [ ] Document existing PO dependency requirements
  - [ ] Identify pain points in current implementation
  - [ ] Map legitimate use cases for manual batch addition
  - [ ] Analyze data integrity and audit trail requirements
  - [ ] Review role-based access control needs

#### 7.1.2 Database Schema Design & Planning

- [ ] Design enhanced audit trail schema
  - [ ] Create ManualBatchAudit model for comprehensive tracking
    - [ ] Add reason enum (FOUND_STOCK, SYSTEM_CORRECTION, INTERNAL_PRODUCTION, TRANSFER, EMERGENCY, OTHER)
    - [ ] Add justification text field (required)
    - [ ] Add referenceDocument field (optional)
    - [ ] Add createdBy, approvedBy user references
    - [ ] Add approval status and workflow tracking
    - [ ] Add timestamps for creation, approval, and completion
  - [ ] Create BatchApproval model for workflow management
    - [ ] Add approval threshold configuration
    - [ ] Add approval status tracking
    - [ ] Add approver comments and decision tracking
    - [ ] Add escalation and notification support

#### 7.1.3 Business Rules & Validation Framework

- [ ] Define comprehensive business rules
  - [ ] Establish quantity thresholds for approval requirements
  - [ ] Define value limits for different approval levels
  - [ ] Create role-based permission matrix
  - [ ] Design validation rules for different batch creation scenarios
  - [ ] Establish data integrity constraints and checks

### 7.2 Backend Implementation (Weeks 2-3)

#### 7.2.1 Enhanced API Endpoints

- [ ] Remove PO dependency from batch creation
  - [ ] Update POST /api/inventory/stock-batches endpoint
    - [ ] Remove required purchaseOrderId validation
    - [ ] Add optional purchaseOrderId for PO-linked batches
    - [ ] Implement supplier validation without PO requirement
    - [ ] Add comprehensive input validation for manual batches
    - [ ] Implement audit trail creation for all manual additions
  - [ ] Create batch validation service
    - [ ] Validate product existence and active status
    - [ ] Check supplier relationship validity
    - [ ] Verify batch number uniqueness
    - [ ] Validate expiry dates and business logic
    - [ ] Implement quantity and value limit checks

#### 7.2.2 Role-Based Access Control Enhancement

- [ ] Implement enhanced RBAC for manual batch creation
  - [ ] Restrict manual batch creation to SUPER_ADMIN and INVENTORY_MANAGER roles
  - [ ] Create permission checking middleware
  - [ ] Implement role-based UI component visibility
  - [ ] Add audit logging for permission checks and access attempts
  - [ ] Create role-based approval workflow routing

#### 7.2.3 Approval Workflow System

- [ ] Create comprehensive approval workflow
  - [ ] Implement automatic approval threshold checking
    - [ ] Configure quantity-based approval requirements
    - [ ] Set value-based approval thresholds
    - [ ] Create role-based approval routing
    - [ ] Implement escalation for high-value batches
  - [ ] Create approval API endpoints
    - [ ] POST /api/inventory/batch-approvals for approval requests
    - [ ] PATCH /api/inventory/batch-approvals/[id] for approval decisions
    - [ ] GET /api/inventory/batch-approvals for pending approvals list
    - [ ] Implement approval notification system
  - [ ] Add approval workflow state management
    - [ ] Track approval status (PENDING, APPROVED, REJECTED)
    - [ ] Implement approval decision logging
    - [ ] Create approval history tracking
    - [ ] Add automatic approval for low-risk batches

#### 7.2.4 Enhanced Audit Trail System

- [ ] Implement comprehensive audit logging
  - [ ] Create detailed audit trail for all manual batch operations
    - [ ] Log user actions, timestamps, and IP addresses
    - [ ] Track all field changes and modifications
    - [ ] Record approval decisions and justifications
    - [ ] Implement audit trail search and filtering
  - [ ] Create audit reporting APIs
    - [ ] GET /api/audit/manual-batches for audit trail reports
    - [ ] Implement filtering by user, date range, and reason
    - [ ] Add export functionality for audit reports
    - [ ] Create compliance reporting endpoints

### 7.3 Frontend Implementation (Weeks 4-5)

#### 7.3.1 Guided Wizard Interface Design

- [ ] Create step-by-step batch creation wizard
  - [ ] Step 1: Reason Selection
    - [ ] Create reason selection interface with descriptions
    - [ ] Add contextual help for each reason type
    - [ ] Implement dynamic form routing based on reason
    - [ ] Add warning messages about manual batch creation
  - [ ] Step 2: Product & Supplier Selection
    - [ ] Create enhanced product search with supplier filtering
    - [ ] Implement supplier selection with validation
    - [ ] Add product-supplier relationship verification
    - [ ] Display supplier-specific information and pricing
  - [ ] Step 3: Batch Details & Validation
    - [ ] Create comprehensive batch information form
    - [ ] Add batch number generation and validation
    - [ ] Implement quantity and expiry date validation
    - [ ] Add location selection and validation
  - [ ] Step 4: Justification & Documentation
    - [ ] Create required justification text area
    - [ ] Add optional reference document upload
    - [ ] Implement approval requirement notification
    - [ ] Add final validation and confirmation

#### 7.3.2 Enhanced Form Validation & User Experience

- [ ] Implement comprehensive client-side validation
  - [ ] Create real-time validation for all form fields
  - [ ] Add contextual error messages and guidance
  - [ ] Implement progressive disclosure for complex forms
  - [ ] Add form auto-save and recovery functionality
  - [ ] Create validation summary and error highlighting

#### 7.3.3 Warning & Notification System

- [ ] Create comprehensive warning system
  - [ ] Add prominent warnings about bypassing normal processes
  - [ ] Implement contextual warnings based on batch value/quantity
  - [ ] Create approval requirement notifications
  - [ ] Add data integrity warnings and confirmations
  - [ ] Implement success/failure notification system

#### 7.3.4 Approval Management Interface

- [ ] Create approval workflow management UI
  - [ ] Design pending approvals dashboard
  - [ ] Create approval decision interface with comments
  - [ ] Implement approval history and tracking views
  - [ ] Add approval notification and alert system
  - [ ] Create approval analytics and reporting interface

### 7.4 Advanced Features & Integration (Week 6)

#### 7.4.1 Integration with Existing Systems

- [ ] Integrate with existing inventory management
  - [ ] Update stock level calculations for manual batches
  - [ ] Integrate with FIFO/LIFO inventory logic
  - [ ] Update inventory reports to include manual batch data
  - [ ] Ensure compatibility with existing batch tracking
  - [ ] Update inventory valuation calculations

#### 7.4.2 Reporting & Analytics Enhancement

- [ ] Create manual batch analytics and reporting
  - [ ] Implement manual batch frequency and trend analysis
  - [ ] Create reason-based reporting and insights
  - [ ] Add user activity reporting for manual batches
  - [ ] Implement approval workflow analytics
  - [ ] Create compliance and audit reporting dashboards

#### 7.4.3 Notification & Alert System

- [ ] Implement comprehensive notification system
  - [ ] Create real-time notifications for approval requests
  - [ ] Add email notifications for high-value batch approvals
  - [ ] Implement escalation notifications for delayed approvals
  - [ ] Create audit alert system for unusual patterns
  - [ ] Add system health notifications for manual batch trends

### 7.5 Testing & Quality Assurance (Week 7)

#### 7.5.1 Comprehensive Testing Suite

- [ ] Create extensive test coverage for manual batch system
  - [ ] Unit tests for all API endpoints and business logic
  - [ ] Integration tests for approval workflow
  - [ ] UI/UX tests for wizard interface
  - [ ] Role-based access control testing
  - [ ] Audit trail and compliance testing
  - [ ] Performance testing with large datasets

#### 7.5.2 Security & Compliance Testing

- [ ] Implement security and compliance validation
  - [ ] Test role-based access controls thoroughly
  - [ ] Validate audit trail completeness and integrity
  - [ ] Test approval workflow security
  - [ ] Verify data validation and sanitization
  - [ ] Test for potential security vulnerabilities

#### 7.5.3 User Acceptance Testing

- [ ] Conduct comprehensive user acceptance testing
  - [ ] Test with actual inventory managers and administrators
  - [ ] Validate workflow efficiency and usability
  - [ ] Test edge cases and error scenarios
  - [ ] Gather feedback on wizard interface and user experience
  - [ ] Validate business rule implementation

### 7.6 Documentation & Training (Week 8)

#### 7.6.1 Technical Documentation

- [ ] Create comprehensive technical documentation
  - [ ] Document new API endpoints and business logic
  - [ ] Create database schema documentation
  - [ ] Document approval workflow and configuration
  - [ ] Create troubleshooting and maintenance guides
  - [ ] Document security and compliance features

#### 7.6.2 User Documentation & Training Materials

- [ ] Create user guides and training materials
  - [ ] Document manual batch creation workflows
  - [ ] Create approval process guides for managers
  - [ ] Document audit trail and reporting features
  - [ ] Create video tutorials for key workflows
  - [ ] Design quick reference guides and checklists

#### 7.6.3 Deployment & Migration Planning

- [ ] Create deployment and migration strategy
  - [ ] Document feature rollout plan
  - [ ] Create user training schedule
  - [ ] Plan gradual feature adoption
  - [ ] Create rollback procedures if needed
  - [ ] Document post-deployment monitoring

### 7.7 Timeline & Dependencies

**Total Duration: 8 weeks**

**Dependencies:**
- Existing batch tracking system (Phase 5.1.2 - COMPLETED)
- Role-based access control system (Phase 1.3 - COMPLETED)
- Audit logging infrastructure (Phase 1.3 - COMPLETED)
- Notification system (Phase 1.4 - COMPLETED)

**Critical Path:**
1. Database schema design and business rules (Week 1)
2. Backend API implementation and RBAC (Weeks 2-3)
3. Frontend wizard and validation (Weeks 4-5)
4. Integration and advanced features (Week 6)
5. Testing and quality assurance (Week 7)
6. Documentation and deployment (Week 8)

**Risk Mitigation:**
- Parallel development of frontend and backend components
- Early prototype testing with stakeholders
- Comprehensive testing throughout development
- Gradual rollout with feature flags
- Rollback procedures for critical issues

## Phase 8: Enhancements & Extensions

### 8.1 Performance Optimization

- [ ] Optimize database queries
- [ ] Implement caching strategies
- [ ] Reduce bundle size
- [ ] Optimize image loading
- [ ] Improve application startup time
- [ ] Add performance monitoring

### 7.2 User Experience Improvements

- [ ] Refine UI based on user feedback
- [ ] Add keyboard shortcuts
- [ ] Implement guided tours/help system
- [ ] Create user preferences
- [ ] Add theme customization
- [ ] Improve accessibility

### 7.3 Future Expansion

- [ ] Prepare for multi-store capability
- [ ] Design data synchronization system
- [ ] Create API for potential integrations
- [ ] Document extension points
- [x] Implement feature flagging system
  - [x] Create system settings database model
  - [x] Implement settings API and context provider
  - [x] Make chat system optional and toggleable
  - [x] Create settings page with feature toggles
- [ ] Create plugin architecture (if needed)

### 7.4 Development Tools and Testing

- [x] Create API test framework
  - [x] Implement browser-compatible mock utilities
  - [x] Create test runner and result display UI
  - [x] Implement proper error handling and reporting
- [x] Implement inventory API tests
  - [x] Create tests for store stock management
  - [x] Create tests for stock adjustments and history
  - [x] Create tests for stock transfers
  - [x] Create tests for inventory reports
- [x] Implement product API tests
  - [x] Create tests for product CRUD operations
  - [x] Create tests for category management
  - [x] Create tests for unit management
  - [x] Create tests for supplier management
- [x] Implement user API tests
  - [x] Create tests for user management
  - [x] Create tests for authentication
  - [x] Create tests for activity logs
- [x] Implement transaction API tests
  - [x] Create tests for sales operations
  - [x] Create tests for returns processing
  - [x] Create tests for sales reports

## Future Updates

### Silent Printing for POS

- [ ] Implement true silent printing for POS receipts
  - [ ] Create desktop bridge application using Electron or similar technology
  - [ ] Implement communication between web app and desktop bridge
  - [ ] Add direct printing to thermal printers without browser dialog
  - [ ] Create fallback mechanism for when bridge is not available
  - [ ] Add printer configuration options in settings
  - [ ] Create documentation for setup and configuration
