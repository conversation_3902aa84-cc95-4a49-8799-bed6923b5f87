# 🔍 Debug Notification System - Step by Step Guide

## 🎯 **Current Status**
The notification system is properly initialized, but notifications aren't being created. Let's debug this systematically.

## 📋 **Step-by-Step Debugging Process**

### **Step 1: Verify Server Logs During PO Status Change**

When you change a PO status, you should see these logs in sequence:

1. **PO API Call Logs:**
   ```
   ✅ Notifications sent for PO [ID] status change: DRAFT → PENDING_APPROVAL
   ```

2. **Notification Integration Logs:**
   ```
   Notifying PO status change: [PO_ID] from DRAFT to PENDING_APPROVAL
   PO status change notifications sent successfully for [PO_ID]
   ```

3. **Event System Logs:**
   ```
   Emitting event: po.status.changed ([EVENT_ID])
   Processing event [EVENT_ID] with X handlers
   Hand<PERSON> executed successfully for event [EVENT_ID]
   ```

4. **Notification Engine Logs:**
   ```
   🔔 Processing notification event: po.status.changed
   ✅ Found template: Purchase Order Status Change for event po.status.changed
   👥 Generating notifications for X users: [...]
   📧 Created notification for user XXX: Purchase Order Status Changed
   📱 In-App notification created
   🎉 Successfully processed X notifications for event: po.status.changed
   ```

### **Step 2: Check What Logs You're Actually Seeing**

**Test this now:**
1. Open your browser dev tools (F12) → Console tab
2. Open server terminal to watch logs
3. Change a PO status from DRAFT → PENDING_APPROVAL
4. **Record exactly what logs appear** (copy/paste them)

**Expected vs Actual:**
- ✅ If you see all 4 log groups above → System is working, check database
- ❌ If you see only #1 → Issue in notification integration
- ❌ If you see #1-2 but not #3-4 → Issue in event system
- ❌ If you see #1-3 but not #4 → Issue in notification engine

### **Step 3: Database Verification Queries**

Run these SQL queries to check what's happening:

```sql
-- 1. Check if notification templates exist
SELECT "eventType", "name", "titleTemplate", "messageTemplate" 
FROM "NotificationTemplate" 
WHERE "eventType" = 'po.status.changed';

-- 2. Check if user preferences exist and are enabled
SELECT np."eventType", np."enabled", np."deliveryMethods", u."name", u."role"
FROM "NotificationPreference" np
JOIN "User" u ON np."userId" = u."id"
WHERE np."eventType" = 'po.status.changed';

-- 3. Check if events are being created
SELECT "eventType", "eventId", "sourceId", "processed", "createdAt"
FROM "NotificationEvent"
WHERE "eventType" = 'po.status.changed'
ORDER BY "createdAt" DESC
LIMIT 5;

-- 4. Check if notifications are being created
SELECT n."title", n."message", n."deliveryMethods", n."isRead", u."name", n."createdAt"
FROM "Notification" n
JOIN "User" u ON n."userId" = u."id"
WHERE n."eventType" = 'po.status.changed'
ORDER BY n."createdAt" DESC
LIMIT 5;
```

### **Step 4: Manual Event Test**

If the automatic system isn't working, let's test manually:

**Open browser console on any page and run:**
```javascript
// Test 1: Check if notification system is initialized
fetch('/api/notifications/status', {
  method: 'GET',
  credentials: 'include'
}).then(r => r.json()).then(console.log);

// Test 2: Manually trigger a notification (replace PO_ID with real ID)
fetch('/api/notifications/test-po-event', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  credentials: 'include',
  body: JSON.stringify({
    poId: 'YOUR_PO_ID_HERE',
    fromStatus: 'DRAFT',
    toStatus: 'PENDING_APPROVAL'
  })
}).then(r => r.json()).then(console.log);
```

### **Step 5: Create Test API Endpoint**

Let me create a test endpoint to manually trigger notifications:

**File: `src/app/api/notifications/test-po-event/route.ts`**
```typescript
import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { notifyPOStatusChange } from "@/lib/notifications/integrations/purchase-order-integration";

export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { poId, fromStatus, toStatus } = await request.json();

    console.log(`🧪 Testing PO notification: ${poId} from ${fromStatus} to ${toStatus}`);

    await notifyPOStatusChange(poId, fromStatus, toStatus, auth.user.id, {
      reason: 'TEST',
      notes: 'Manual test of notification system',
      supplierName: 'Test Supplier',
      total: 1000,
      poNumber: 'TEST-001',
    });

    return NextResponse.json({ 
      success: true, 
      message: "Test notification triggered successfully" 
    });

  } catch (error) {
    console.error("Test notification error:", error);
    return NextResponse.json({ 
      error: "Test failed", 
      details: error.message 
    }, { status: 500 });
  }
}
```

### **Step 6: Check Notification Display Components**

If notifications are being created in the database but not displayed:

**Check these files:**
1. **Notification Bell Component** - Should query unread notifications
2. **Notifications Page** - Should display all notifications
3. **API endpoints** - Should return notification data correctly

**Test notification API directly:**
```bash
curl -X GET http://localhost:3001/api/notifications \
  -H "Cookie: session-token=YOUR_SESSION_TOKEN" \
  -v
```

### **Step 7: Common Issues and Solutions**

**Issue 1: No logs at all**
- **Cause**: PO status change API not being called
- **Solution**: Check if the status change button is working, check network tab

**Issue 2: Logs stop at "Notifying PO status change"**
- **Cause**: Event system not receiving events
- **Solution**: Check if `emitEvent` function is working

**Issue 3: Events created but no notifications**
- **Cause**: No templates or preferences, or notification engine failing
- **Solution**: Re-run notification system initialization

**Issue 4: Notifications created but not displayed**
- **Cause**: Frontend components not fetching/displaying notifications
- **Solution**: Check notification API endpoints and frontend components

## 🎯 **Next Steps Based on Your Findings**

**After running Step 2 (checking logs), tell me:**

1. **What logs do you see?** (copy/paste exact logs)
2. **What database query results do you get?** (run the SQL queries)
3. **Any errors in browser console?**

Based on your findings, I'll provide specific fixes for the exact issue you're experiencing.

## 🚀 **Quick Test Commands**

```bash
# 1. Check if server is running with notification system
curl http://localhost:3001/api/notifications/status

# 2. Check database tables
psql -d your_database -c "SELECT COUNT(*) FROM \"NotificationTemplate\";"
psql -d your_database -c "SELECT COUNT(*) FROM \"NotificationPreference\";"
psql -d your_database -c "SELECT COUNT(*) FROM \"NotificationEvent\";"
psql -d your_database -c "SELECT COUNT(*) FROM \"Notification\";"

# 3. Watch server logs while testing
tail -f your_server_logs
```

Let me know what you find in Step 2, and I'll help you fix the specific issue! 🔍
