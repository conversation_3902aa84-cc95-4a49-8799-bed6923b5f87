# Database Backup and Restore System

This document describes the backup and restore functionality for the Next POS application.

## Overview

The backup system provides the following features:

- Manual backups through the admin UI
- Scheduled automatic backups
- Backup rotation (keeping a limited number of backups)
- Backup restoration
- Backup metadata (timestamp, size, comments)
- Multiple backup methods (PostgreSQL tools or Prisma-based)
- Comprehensive backup and restore history logging
- Schema version tracking with each backup
- Pre-restore schema validation to prevent data inconsistencies

## Components

The backup system consists of the following components:

1. **Core Backup Library** (`src/lib/backup/db-backup.ts`)

   - Provides functions for creating, listing, and restoring backups
   - Supports two backup methods:
     - PostgreSQL's native tools (`pg_dump` and `pg_restore`) when available
     - Prisma-based backup and restore as a fallback method

2. **Backup History** (`src/lib/backup/backup-history.ts`)

   - Tracks all backup and restore operations
   - Logs success and failure events
   - Provides information about the last backup and restore operations

3. **Scheduled Backup Utility** (`src/lib/backup/scheduled-backup.ts`)

   - Handles automatic backups with rotation
   - Can be run as a standalone script or from the application

4. **API Routes**

   - `/api/backup` - For listing, creating, and deleting backups
   - `/api/backup/restore` - For restoring from a backup
   - `/api/backup/download/[filename]` - For downloading backup files
   - `/api/backup/upload` - For uploading external backup files
   - `/api/backup/history` - For retrieving backup and restore history

5. **Admin UI** (`src/app/admin/backup/page.tsx`)

   - User interface for managing backups
   - Displays backup history and allows manual operations
   - Includes a history component showing recent backup and restore operations
   - Provides ability to download backup files directly from the UI
   - Allows uploading external backup files for restoration

6. **Command-line Scripts**
   - `scripts/scheduled-backup.js` - For running scheduled backups via cron
   - `scripts/manual-backup.js` - For creating manual backups from the command line

## Backup Storage

Backups are stored in the `/backups` directory at the project root. Each backup consists of:

- A `.sql` file containing the database dump
- A `.sql.json` metadata file with additional information including:
  - Timestamp
  - Size
  - Comment
  - Backup method used
  - Schema version at the time of backup

## Schema Version Tracking

The backup system includes schema version tracking to ensure data integrity:

1. **Schema Version Detection**: The system automatically detects the current database schema version based on the Prisma schema file.
2. **Version Storage**: Each backup stores the schema version at the time the backup was created.
3. **Pre-Restore Validation**: Before restoring a backup, the system compares the backup's schema version with the current schema version.
4. **Strict Safety Enforcement**: If a schema version mismatch is detected, the system displays a warning and completely prevents restoration.
5. **Data Protection**: This strict validation ensures database integrity by preventing the restoration of backups with incompatible schema versions, which could cause data inconsistencies or application errors.

## Setting Up Scheduled Backups

### Windows (Task Scheduler)

1. Open Task Scheduler
2. Create a new task
3. Set the trigger (e.g., daily at 11:00 PM)
4. Set the action to run `node scripts/scheduled-backup.js`
5. Set the start directory to the project root

### Linux/macOS (Cron)

Add a line to your crontab:

```
0 23 * * * cd /path/to/project && node scripts/scheduled-backup.js
```

This will run the backup daily at 11:00 PM.

## Manual Backups

### From the Admin UI

1. Navigate to `/admin/backup`
2. Click "Create New Backup"

### From the Command Line

```
node scripts/manual-backup.js "Optional comment for the backup"
```

## Restoring from a Backup

### From the Admin UI

1. Navigate to `/admin/backup`
2. Find the backup you want to restore
3. Click "Restore" and confirm

### From the Command Line

You can use the PostgreSQL `pg_restore` command directly:

```
pg_restore -h localhost -p 5432 -U postgres -d npos -c "/path/to/backup.sql"
```

## Downloading Backups

### From the Admin UI

1. Navigate to `/admin/backup`
2. Find the backup you want to download
3. Click the "Download" button
4. The backup file will be downloaded to your local machine

### Programmatically

You can download backup files programmatically by making a GET request to:

```
/api/backup/download/[filename]
```

This endpoint will serve the backup file with appropriate headers for download.

## Uploading Backups

### From the Admin UI

1. Navigate to `/admin/backup`
2. Click the "Restore from File" button
3. Select a SQL backup file from your local machine
4. Add an optional comment
5. Click "Upload"
6. The file will be uploaded and added to your backup list
7. You can then restore from this backup using the standard restore process

### Programmatically

You can upload backup files programmatically by making a POST request to:

```
/api/backup/upload
```

This endpoint accepts a multipart form with the following fields:

- `file`: The SQL backup file
- `comment` (optional): A comment about the backup

## Backup Rotation

By default, the system keeps the last 30 backups. Older backups are automatically deleted when new backups are created. You can change this setting in:

- `src/lib/backup/scheduled-backup.ts` (for the application)
- `scripts/scheduled-backup.js` (for the command-line script)

## Best Practices

1. **Regular Testing**: Periodically test the restore process to ensure backups are valid
2. **External Storage**: Copy important backups to external storage or cloud storage
3. **Pre-Update Backups**: Always create a backup before major updates or changes
4. **Monitoring**: Check the backup logs regularly to ensure backups are running successfully

## Troubleshooting

### Backup Fails

1. Check that PostgreSQL is running
2. Verify that the database connection details are correct
3. Ensure the backup directory is writable
4. Check the logs in `/logs/backup.log`
5. If you see an error about `pg_dump` not being found, the system will automatically fall back to the Prisma-based backup method
6. If both methods fail, check that your database is accessible and that Prisma is properly configured

### Restore Fails

1. Verify that the backup file exists and is not corrupted
2. Check that you have sufficient permissions to restore the database
3. Ensure no connections are blocking the restore operation
4. For pg_dump backups, make sure PostgreSQL tools are installed if you see an error about `pg_restore` not being found
5. For Prisma-based backups, check that your database is accessible and that Prisma is properly configured
