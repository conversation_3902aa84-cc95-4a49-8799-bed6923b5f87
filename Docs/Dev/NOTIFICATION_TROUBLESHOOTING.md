# Notification System Troubleshooting Guide

## Issue: Module Import Error - "Export preferences doesn't exist"

### Problem Description
When accessing `/settings/notifications`, you encounter an error:
```
Export preferences doesn't exist in target module @/lib/notifications
```

### Root Cause
This error typically occurs due to one of the following reasons:
1. Database tables haven't been created yet
2. Module loading order issues
3. Missing database migration
4. Circular import dependencies

### Solution Steps

#### Step 1: Check Database Setup
First, verify that the notification system database tables exist:

```bash
# Run the database check script
npx ts-node src/scripts/check-notification-database.ts
```

If tables are missing, run the migration:

```bash
# Generate Prisma client
npx prisma generate

# Run database migration
npx prisma migrate dev --name add-notification-system

# If migration fails, try pushing schema
npx prisma db push
```

#### Step 2: Test Module Imports
Test if the notification modules can be imported correctly:

```bash
# Run the import test script
npx ts-node src/scripts/test-notification-imports.ts
```

#### Step 3: Initialize the Notification System
If the database is set up correctly, initialize the notification system:

```bash
# Run the initialization script
npx ts-node src/scripts/initialize-notification-system.ts
```

#### Step 4: Verify API Routes
The API routes have been updated to use dynamic imports to avoid module loading issues. The key changes:

**Before:**
```typescript
import { preferences } from "@/lib/notifications";
```

**After:**
```typescript
// Dynamic import to avoid module loading issues
const { preferences } = await import("@/lib/notifications");
```

#### Step 5: Test the Notification Preferences Page
After completing the above steps, test the notification preferences page:

1. Navigate to `http://localhost:3000/settings/notifications`
2. Verify the page loads without errors
3. Check that preferences are displayed
4. Test updating preferences

### Quick Fix Commands

If you're still experiencing issues, run these commands in order:

```bash
# 1. Clean and reinstall dependencies
npm install

# 2. Generate Prisma client
npx prisma generate

# 3. Run database migration
npx prisma migrate dev --name add-notification-system

# 4. Initialize notification system
npx ts-node src/scripts/initialize-notification-system.ts

# 5. Test imports
npx ts-node src/scripts/test-notification-imports.ts

# 6. Start the development server
npm run dev
```

### Alternative: Manual Database Setup

If the migration fails, you can manually create the tables:

```sql
-- Create NotificationTemplate table
CREATE TABLE "NotificationTemplate" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT UNIQUE NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "titleTemplate" TEXT NOT NULL,
  "messageTemplate" TEXT NOT NULL,
  "defaultDeliveryMethods" TEXT[] NOT NULL,
  "defaultPriority" TEXT DEFAULT 'NORMAL',
  "variables" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create NotificationPreference table
CREATE TABLE "NotificationPreference" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "eventType" TEXT NOT NULL,
  "enabled" BOOLEAN DEFAULT true,
  "deliveryMethods" TEXT[] NOT NULL,
  "frequency" TEXT DEFAULT 'IMMEDIATE',
  "quietHours" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("userId", "eventType")
);

-- Create NotificationEvent table
CREATE TABLE "NotificationEvent" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT NOT NULL,
  "eventId" TEXT NOT NULL,
  "sourceId" TEXT,
  "sourceType" TEXT,
  "payload" JSONB NOT NULL,
  "processed" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "processedAt" TIMESTAMP
);

-- Add new columns to existing Notification table
ALTER TABLE "Notification" ADD COLUMN "eventType" TEXT;
ALTER TABLE "Notification" ADD COLUMN "eventId" TEXT;
ALTER TABLE "Notification" ADD COLUMN "deliveryMethods" JSONB;
ALTER TABLE "Notification" ADD COLUMN "priority" TEXT DEFAULT 'NORMAL';
ALTER TABLE "Notification" ADD COLUMN "expiresAt" TIMESTAMP;
```

### Verification Checklist

After implementing the fixes, verify:

- [ ] Database tables exist (NotificationTemplate, NotificationPreference, NotificationEvent)
- [ ] Notification table has new columns (eventType, eventId, deliveryMethods, priority, expiresAt)
- [ ] API routes use dynamic imports
- [ ] `/settings/notifications` page loads without errors
- [ ] User can view and update notification preferences
- [ ] Navigation links work (sidebar, header, notification dropdown)

### Common Error Messages and Solutions

#### "Table 'NotificationPreference' doesn't exist"
**Solution:** Run the database migration or manually create tables.

#### "Cannot read property 'getForUser' of undefined"
**Solution:** The preferences object is not being imported correctly. Ensure dynamic imports are used in API routes.

#### "Module not found: @/lib/notifications"
**Solution:** Check that all notification system files exist and TypeScript paths are configured correctly.

#### "Prisma Client validation error"
**Solution:** Regenerate Prisma client with `npx prisma generate`.

### Support Scripts

The following scripts are available to help troubleshoot:

- `src/scripts/check-notification-database.ts` - Check database setup
- `src/scripts/test-notification-imports.ts` - Test module imports
- `src/scripts/fix-notification-imports.ts` - Automated fix script
- `src/scripts/initialize-notification-system.ts` - Initialize the system

### Contact Information

If you continue to experience issues after following this guide:

1. Check the console logs for detailed error messages
2. Verify your database connection is working
3. Ensure all dependencies are installed correctly
4. Review the implementation files for any custom modifications

The notification system should work correctly after following these troubleshooting steps.
