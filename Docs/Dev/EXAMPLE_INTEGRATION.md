# Example Integration: Adding Notifications to a New Feature

This document demonstrates how to integrate the modular notification system with a new feature. We'll use a hypothetical "Customer Loyalty Program" feature as an example.

## Scenario: Customer Loyalty Program

Let's say we're adding a customer loyalty program that:
- Awards points for purchases
- Sends notifications when customers reach milestones
- Notifies staff about high-value customers
- Alerts when points are about to expire

## Step 1: Define Event Types

First, define the events that should trigger notifications:

```typescript
// src/lib/features/loyalty/events.ts
export const LOYALTY_EVENT_TYPES = {
  POINTS_EARNED: 'loyalty.points.earned',
  MILESTONE_REACHED: 'loyalty.milestone.reached',
  POINTS_EXPIRING: 'loyalty.points.expiring',
  VIP_STATUS_ACHIEVED: 'loyalty.vip.achieved',
  REWARD_REDEEMED: 'loyalty.reward.redeemed',
} as const;
```

## Step 2: Create Notification Templates

Create templates for each event type:

```typescript
// src/lib/features/loyalty/templates.ts
import { NotificationTemplate } from '@/lib/notifications/template-manager';
import { LOYALTY_EVENT_TYPES } from './events';

export const LOYALTY_TEMPLATES: NotificationTemplate[] = [
  {
    eventType: LOYALTY_EVENT_TYPES.POINTS_EARNED,
    name: 'Points Earned',
    description: 'Notification when customer earns loyalty points',
    titleTemplate: 'You earned {{data.pointsEarned}} points!',
    messageTemplate: 'You earned {{data.pointsEarned}} points from your purchase at {{data.storeName}}. Total points: {{data.totalPoints}}',
    defaultDeliveryMethods: ['IN_APP', 'EMAIL'],
    defaultPriority: 'NORMAL',
    variables: {
      'data.pointsEarned': 'Points earned in this transaction',
      'data.totalPoints': 'Customer total points balance',
      'data.storeName': 'Store name',
      'data.transactionAmount': 'Transaction amount',
    },
  },
  {
    eventType: LOYALTY_EVENT_TYPES.MILESTONE_REACHED,
    name: 'Loyalty Milestone Reached',
    description: 'Notification when customer reaches a loyalty milestone',
    titleTemplate: 'Congratulations! You reached {{data.milestoneName}}! 🎉',
    messageTemplate: 'You have reached {{data.milestoneName}} with {{data.totalPoints}} points! You unlocked: {{data.rewards}}',
    defaultDeliveryMethods: ['IN_APP', 'EMAIL', 'TOAST'],
    defaultPriority: 'HIGH',
    variables: {
      'data.milestoneName': 'Name of the milestone (e.g., Silver, Gold)',
      'data.totalPoints': 'Customer total points',
      'data.rewards': 'Rewards unlocked',
      'data.nextMilestone': 'Next milestone to reach',
    },
  },
  {
    eventType: LOYALTY_EVENT_TYPES.VIP_STATUS_ACHIEVED,
    name: 'VIP Status Achieved',
    description: 'Notification when customer becomes VIP',
    titleTemplate: 'Welcome to VIP Status! 👑',
    messageTemplate: 'Congratulations {{user.name}}! You are now a VIP customer with exclusive benefits.',
    defaultDeliveryMethods: ['IN_APP', 'EMAIL'],
    defaultPriority: 'HIGH',
    variables: {
      'user.name': 'Customer name',
      'data.vipBenefits': 'List of VIP benefits',
      'data.achievedDate': 'Date VIP status was achieved',
    },
  },
  {
    eventType: LOYALTY_EVENT_TYPES.POINTS_EXPIRING,
    name: 'Points Expiring Soon',
    description: 'Notification when loyalty points are about to expire',
    titleTemplate: 'Your points expire soon!',
    messageTemplate: '{{data.expiringPoints}} points will expire on {{data.expiryDate}}. Use them before you lose them!',
    defaultDeliveryMethods: ['IN_APP', 'EMAIL'],
    defaultPriority: 'HIGH',
    variables: {
      'data.expiringPoints': 'Number of points expiring',
      'data.expiryDate': 'Expiry date',
      'data.totalPoints': 'Total points balance',
    },
  },
];
```

## Step 3: Register Event Handlers

Register handlers for loyalty events:

```typescript
// src/lib/features/loyalty/notification-handlers.ts
import { registerFeatureNotificationHandler } from '@/lib/notifications/notification-registry';
import { LOYALTY_EVENT_TYPES } from './events';

export function registerLoyaltyNotificationHandlers(): void {
  // Points earned handler
  registerFeatureNotificationHandler(
    LOYALTY_EVENT_TYPES.POINTS_EARNED,
    async (event) => {
      console.log(`Customer earned ${event.payload.pointsEarned} points`);
      
      // Additional business logic
      await updateCustomerEngagement(event.payload.customerId);
      await checkForMilestones(event.payload.customerId, event.payload.totalPoints);
    },
    10
  );

  // Milestone reached handler
  registerFeatureNotificationHandler(
    LOYALTY_EVENT_TYPES.MILESTONE_REACHED,
    async (event) => {
      console.log(`Customer reached milestone: ${event.payload.milestoneName}`);
      
      // Trigger additional rewards or actions
      await grantMilestoneRewards(event.payload.customerId, event.payload.milestoneName);
    },
    10
  );

  // VIP status handler
  registerFeatureNotificationHandler(
    LOYALTY_EVENT_TYPES.VIP_STATUS_ACHIEVED,
    async (event) => {
      console.log(`Customer achieved VIP status: ${event.payload.customerId}`);
      
      // Notify staff about new VIP customer
      await notifyStaffAboutVIP(event.payload.customerId);
    },
    10
  );

  console.log('Loyalty notification handlers registered');
}

// Helper functions
async function updateCustomerEngagement(customerId: string) {
  // Update customer engagement metrics
}

async function checkForMilestones(customerId: string, totalPoints: number) {
  // Check if customer reached any milestones
}

async function grantMilestoneRewards(customerId: string, milestone: string) {
  // Grant rewards for reaching milestone
}

async function notifyStaffAboutVIP(customerId: string) {
  // Notify staff about new VIP customer
}
```

## Step 4: Create Convenience Functions

Create easy-to-use functions for emitting loyalty events:

```typescript
// src/lib/features/loyalty/notifications.ts
import { notify } from '@/lib/notifications';
import { LOYALTY_EVENT_TYPES } from './events';

export async function notifyPointsEarned(
  customerId: string,
  pointsEarned: number,
  totalPoints: number,
  transactionData: Record<string, any>
): Promise<void> {
  await notify({
    eventType: LOYALTY_EVENT_TYPES.POINTS_EARNED,
    sourceId: customerId,
    sourceType: 'customer',
    userId: customerId, // Assuming customer is also a user
    payload: {
      customerId,
      pointsEarned,
      totalPoints,
      storeName: transactionData.storeName,
      transactionAmount: transactionData.amount,
      transactionId: transactionData.transactionId,
    },
  });
}

export async function notifyMilestoneReached(
  customerId: string,
  milestoneName: string,
  totalPoints: number,
  rewards: string[]
): Promise<void> {
  await notify({
    eventType: LOYALTY_EVENT_TYPES.MILESTONE_REACHED,
    sourceId: customerId,
    sourceType: 'customer',
    userId: customerId,
    payload: {
      customerId,
      milestoneName,
      totalPoints,
      rewards: rewards.join(', '),
      achievedDate: new Date().toISOString(),
    },
  });
}

export async function notifyVIPStatusAchieved(
  customerId: string,
  vipBenefits: string[]
): Promise<void> {
  await notify({
    eventType: LOYALTY_EVENT_TYPES.VIP_STATUS_ACHIEVED,
    sourceId: customerId,
    sourceType: 'customer',
    userId: customerId,
    payload: {
      customerId,
      vipBenefits: vipBenefits.join(', '),
      achievedDate: new Date().toISOString(),
    },
  });
}

export async function notifyPointsExpiring(
  customerId: string,
  expiringPoints: number,
  expiryDate: string,
  totalPoints: number
): Promise<void> {
  await notify({
    eventType: LOYALTY_EVENT_TYPES.POINTS_EXPIRING,
    sourceId: customerId,
    sourceType: 'customer',
    userId: customerId,
    payload: {
      customerId,
      expiringPoints,
      expiryDate,
      totalPoints,
    },
  });
}
```

## Step 5: Initialize Feature Notifications

Create an initialization function for the loyalty feature:

```typescript
// src/lib/features/loyalty/init.ts
import { templateManager } from '@/lib/notifications/template-manager';
import { registerLoyaltyNotificationHandlers } from './notification-handlers';
import { LOYALTY_TEMPLATES } from './templates';

export async function initializeLoyaltyNotifications(): Promise<void> {
  console.log('Initializing loyalty program notifications...');

  // Register event handlers
  registerLoyaltyNotificationHandlers();

  // Create notification templates
  for (const template of LOYALTY_TEMPLATES) {
    await templateManager.upsertTemplate(template);
  }

  console.log('Loyalty program notifications initialized');
}
```

## Step 6: Integrate with Business Logic

Integrate notifications into your loyalty program business logic:

```typescript
// src/lib/features/loyalty/service.ts
import { 
  notifyPointsEarned, 
  notifyMilestoneReached, 
  notifyVIPStatusAchieved 
} from './notifications';

export class LoyaltyService {
  async processTransaction(customerId: string, transactionAmount: number): Promise<void> {
    // Calculate points earned
    const pointsEarned = Math.floor(transactionAmount * 0.1); // 10% of amount
    
    // Update customer points
    const customer = await this.updateCustomerPoints(customerId, pointsEarned);
    
    // Send points earned notification
    await notifyPointsEarned(customerId, pointsEarned, customer.totalPoints, {
      storeName: 'Main Store',
      amount: transactionAmount,
      transactionId: 'txn_123',
    });

    // Check for milestone achievements
    const milestone = await this.checkMilestones(customer.totalPoints);
    if (milestone) {
      await notifyMilestoneReached(
        customerId, 
        milestone.name, 
        customer.totalPoints, 
        milestone.rewards
      );
    }

    // Check for VIP status
    if (customer.totalPoints >= 10000 && !customer.isVIP) {
      await this.grantVIPStatus(customerId);
      await notifyVIPStatusAchieved(customerId, [
        'Free shipping',
        'Priority support',
        'Exclusive offers',
      ]);
    }
  }

  private async updateCustomerPoints(customerId: string, points: number) {
    // Update database
    // Return updated customer
  }

  private async checkMilestones(totalPoints: number) {
    // Check if points qualify for any milestones
    // Return milestone info if achieved
  }

  private async grantVIPStatus(customerId: string) {
    // Update customer VIP status
  }
}
```

## Step 7: Add to Application Initialization

Add loyalty notifications to your application startup:

```typescript
// src/app/layout.tsx or startup file
import { initializeNotifications } from '@/lib/notifications';
import { initializeLoyaltyNotifications } from '@/lib/features/loyalty/init';

export default async function RootLayout({ children }) {
  // Initialize core notification system
  await initializeNotifications();
  
  // Initialize feature-specific notifications
  await initializeLoyaltyNotifications();

  return (
    // Your layout JSX
  );
}
```

## Step 8: Test the Integration

Create a test to verify the integration works:

```typescript
// src/lib/features/loyalty/__tests__/notifications.test.ts
import { notifyPointsEarned } from '../notifications';
import { eventSystem } from '@/lib/events/event-system';

describe('Loyalty Notifications', () => {
  beforeEach(() => {
    eventSystem.clearHandlers();
  });

  it('should emit points earned event', async () => {
    const mockHandler = jest.fn();
    eventSystem.registerHandler({
      eventType: 'loyalty.points.earned',
      handler: mockHandler,
    });

    await notifyPointsEarned('customer123', 50, 500, {
      storeName: 'Test Store',
      amount: 500,
      transactionId: 'test_txn',
    });

    expect(mockHandler).toHaveBeenCalledWith(
      expect.objectContaining({
        eventType: 'loyalty.points.earned',
        sourceId: 'customer123',
        payload: expect.objectContaining({
          pointsEarned: 50,
          totalPoints: 500,
        }),
      })
    );
  });
});
```

## Benefits of This Integration

1. **Decoupled**: Loyalty feature notifications are separate from core notification code
2. **Reusable**: Templates and handlers can be reused across different parts of the loyalty feature
3. **Configurable**: Users can customize their loyalty notification preferences
4. **Testable**: Easy to test notification logic in isolation
5. **Maintainable**: Changes to loyalty notifications don't affect other features

## Summary

This example demonstrates how any new feature can integrate with the modular notification system by:

1. Defining event types specific to the feature
2. Creating notification templates for each event
3. Registering event handlers for additional business logic
4. Creating convenience functions for easy event emission
5. Integrating notifications into business logic
6. Adding feature initialization to application startup

The modular design ensures that features can add sophisticated notification capabilities without modifying core notification code, making the system truly extensible and maintainable.
