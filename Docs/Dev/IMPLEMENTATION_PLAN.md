# Modular Notification System Implementation Plan

## Overview
This document provides a step-by-step implementation plan for deploying the new modular, event-driven notification system.

## Prerequisites
- Node.js and npm/yarn installed
- Prisma CLI available
- Database access
- Application backup (recommended)

## Phase 1: Database Setup (30 minutes)

### Step 1.1: Update Prisma Schema
The schema has already been updated in `prisma/schema.prisma`. Review the changes:
- New tables: `NotificationTemplate`, `NotificationPreference`, `NotificationEvent`
- Enhanced `Notification` table with new columns
- New enums for priorities, delivery methods, and frequencies

### Step 1.2: Generate and Run Migration
```bash
# Generate Prisma client with new schema
npx prisma generate

# Create and run database migration
npx prisma migrate dev --name add-notification-system

# Verify migration
npx prisma db push
```

### Step 1.3: Verify Database Schema
```bash
# Check if tables were created
npx prisma studio
# Or use your preferred database client to verify tables exist
```

## Phase 2: System Initialization (15 minutes)

### Step 2.1: Run Initialization Script
```bash
# Run the initialization script
npx ts-node src/scripts/initialize-notification-system.ts
```

This script will:
- Initialize default notification templates
- Create user preferences for existing users
- Migrate existing notifications
- Create sample notifications for testing

### Step 2.2: Verify Initialization
Check that:
- Templates are created in `NotificationTemplate` table
- User preferences are created in `NotificationPreference` table
- Existing notifications have been migrated with new fields

## Phase 3: Application Integration (45 minutes)

### Step 3.1: Update Application Startup
Add notification system initialization to your application startup:

```typescript
// In your main application file (e.g., src/app/layout.tsx or startup script)
import { initializeNotifications } from '@/lib/notifications';

// Call during application startup
await initializeNotifications();
```

### Step 3.2: Replace Existing Notification Calls
Update existing notification usage throughout the application:

#### Before (Old System):
```typescript
import { createNotification } from '@/lib/notifications';

await createNotification({
  userId: 'user123',
  title: 'PO Approved',
  message: 'Your purchase order has been approved',
  type: 'PURCHASE_ORDER_APPROVED',
  purchaseOrderId: 'po123',
});
```

#### After (New System):
```typescript
import { notifyPOApproved } from '@/lib/notifications';

await notifyPOApproved('po123', 'approver_id', {
  poNumber: 'PO-001',
  supplierName: 'Acme Corp',
  total: 1500.00,
});
```

### Step 3.3: Update Purchase Order Workflows
Replace notification calls in:
- `src/lib/po-status-management.ts`
- `src/app/api/purchase-orders/[id]/route.ts`
- Any other PO-related files

Example integration:
```typescript
// Replace existing notification logic with:
import { notifyPOStatusChanged } from '@/lib/notifications/integrations/purchase-order-integration';

await notifyPOStatusChanged(poId, fromStatus, toStatus, userId, metadata);
```

### Step 3.4: Update Inventory Workflows
Add notifications to inventory management:
```typescript
import { notifyLowStock, notifyOutOfStock } from '@/lib/notifications';

// In inventory checking logic
if (currentStock <= minThreshold) {
  if (currentStock === 0) {
    await notifyOutOfStock(productId, productName, { category, location });
  } else {
    await notifyLowStock(productId, productName, currentStock, minThreshold, { category, location });
  }
}
```

## Phase 4: UI Components (30 minutes)

### Step 4.1: Add Notification Preferences Page
Create a new page for notification preferences:

```typescript
// src/app/settings/notifications/page.tsx
import { NotificationPreferences } from '@/components/notifications/NotificationPreferences';

export default function NotificationPreferencesPage() {
  return (
    <div className="container mx-auto py-6">
      <NotificationPreferences />
    </div>
  );
}
```

### Step 4.2: Add Navigation Link
Add a link to notification preferences in your settings menu:

```typescript
// In your settings navigation component
<Link href="/settings/notifications">
  <Bell className="h-4 w-4 mr-2" />
  Notification Preferences
</Link>
```

### Step 4.3: Update Notification Dropdown (Optional)
The existing notification dropdown should continue working. Optionally enhance it to show notification priorities or delivery method indicators.

## Phase 5: Testing (30 minutes)

### Step 5.1: Test Event Emission
Create a test script to verify event emission:

```typescript
// test-notifications.ts
import { notify } from '@/lib/notifications';

async function testNotifications() {
  // Test PO approval
  await notify({
    eventType: 'po.approved',
    sourceId: 'test_po_123',
    sourceType: 'purchase_order',
    payload: {
      poNumber: 'TEST-001',
      supplierName: 'Test Supplier',
      total: 100.00,
    },
  });

  console.log('Test notification sent');
}

testNotifications().catch(console.error);
```

### Step 5.2: Test User Preferences
1. Navigate to `/settings/notifications`
2. Verify preferences load correctly
3. Update some preferences and save
4. Verify changes are persisted

### Step 5.3: Test Notification Generation
1. Trigger a purchase order status change
2. Verify notifications are created in the database
3. Check that notifications appear in the UI
4. Verify user preferences are respected

### Step 5.4: Test Template System
1. Access `/api/notifications/templates` (as admin)
2. Verify templates are loaded
3. Update a template and test notification generation

## Phase 6: Monitoring and Optimization (Ongoing)

### Step 6.1: Monitor Event Processing
Add logging to monitor:
- Event emission frequency
- Processing times
- Failed events
- User engagement with notifications

### Step 6.2: Performance Optimization
- Monitor database query performance
- Optimize notification queries for large user bases
- Consider caching for frequently accessed templates

### Step 6.3: User Feedback
- Collect user feedback on notification preferences
- Monitor notification click-through rates
- Adjust default templates based on usage patterns

## Rollback Plan

If issues arise, you can rollback by:

1. **Disable New System**: Comment out `initializeNotifications()` call
2. **Revert Code Changes**: Use git to revert to previous notification calls
3. **Database Rollback**: Run migration rollback if needed:
   ```bash
   npx prisma migrate reset
   ```

## Troubleshooting

### Common Issues

#### Database Migration Fails
- Check database permissions
- Verify Prisma schema syntax
- Check for conflicting table names

#### Templates Not Loading
- Verify initialization script ran successfully
- Check database for `NotificationTemplate` records
- Review console logs for errors

#### Notifications Not Appearing
- Check user preferences are enabled
- Verify event emission is working
- Check notification engine processing

#### Performance Issues
- Monitor database query performance
- Consider adding database indexes
- Optimize event processing batch sizes

### Debug Commands

```bash
# Check database state
npx prisma studio

# View recent notifications
npx prisma db seed --preview-feature

# Check event processing
tail -f logs/notification-system.log
```

## Success Criteria

The implementation is successful when:

1. ✅ All database migrations complete without errors
2. ✅ Default templates and preferences are created
3. ✅ Existing notifications continue to work
4. ✅ New event-driven notifications are generated
5. ✅ User preferences UI is functional
6. ✅ No performance degradation in existing workflows
7. ✅ All tests pass
8. ✅ Users can customize their notification preferences

## Next Steps After Implementation

1. **Add Email Delivery**: Implement email notification delivery
2. **Add SMS Delivery**: Implement SMS notification delivery (optional)
3. **Real-time Notifications**: Add WebSocket support for instant notifications
4. **Analytics Dashboard**: Create admin dashboard for notification analytics
5. **Advanced Templates**: Add conditional logic and advanced template features
6. **Mobile App Integration**: Add push notification support for mobile apps

## Support and Maintenance

- Monitor system logs for errors
- Regular database maintenance for event cleanup
- Template updates based on user feedback
- Performance monitoring and optimization
- User training on new preference features
