# Modular Event-Driven Notification System Architecture

## Overview

This document describes the architecture and implementation of the new modular, event-driven notification system designed to replace the existing tightly-coupled notification implementation.

## Architecture Components

### 1. Event System (`src/lib/events/event-system.ts`)
- **Purpose**: Core event emission and handling infrastructure
- **Features**:
  - Type-safe event definitions
  - Event persistence for reliability
  - Async event processing
  - Handler registration with priorities
  - Event recovery and batch processing

### 2. Notification Engine (`src/lib/notifications/notification-engine.ts`)
- **Purpose**: Processes events and generates notifications
- **Features**:
  - Template-based notification generation
  - User preference filtering
  - Multi-channel delivery support
  - Target user determination
  - Notification scheduling

### 3. Template Manager (`src/lib/notifications/template-manager.ts`)
- **Purpose**: Manages notification templates
- **Features**:
  - Default template seeding
  - Template CRUD operations
  - Template validation
  - Variable substitution
  - Multi-language support (future)

### 4. Preference Manager (`src/lib/notifications/preference-manager.ts`)
- **Purpose**: Manages user notification preferences
- **Features**:
  - User preference initialization
  - Granular preference controls
  - Quiet hours support
  - Bulk preference operations
  - Preference validation

### 5. Notification Registry (`src/lib/notifications/notification-registry.ts`)
- **Purpose**: Central registration system for notification handlers
- **Features**:
  - Feature-specific handler registration
  - Event type management
  - Handler priority system
  - Unprocessed event recovery

## Database Schema Changes

### New Tables

#### NotificationTemplate
```sql
CREATE TABLE "NotificationTemplate" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT UNIQUE NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "titleTemplate" TEXT NOT NULL,
  "messageTemplate" TEXT NOT NULL,
  "defaultDeliveryMethods" TEXT[] NOT NULL,
  "defaultPriority" "NotificationPriority" DEFAULT 'NORMAL',
  "variables" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### NotificationPreference
```sql
CREATE TABLE "NotificationPreference" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "eventType" TEXT NOT NULL,
  "enabled" BOOLEAN DEFAULT true,
  "deliveryMethods" TEXT[] NOT NULL,
  "frequency" "NotificationFrequency" DEFAULT 'IMMEDIATE',
  "quietHours" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("userId", "eventType")
);
```

#### NotificationEvent
```sql
CREATE TABLE "NotificationEvent" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT NOT NULL,
  "eventId" TEXT NOT NULL,
  "sourceId" TEXT,
  "sourceType" TEXT,
  "payload" JSONB NOT NULL,
  "processed" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "processedAt" TIMESTAMP
);
```

### Enhanced Notification Table
```sql
ALTER TABLE "Notification" ADD COLUMN "eventType" TEXT;
ALTER TABLE "Notification" ADD COLUMN "eventId" TEXT;
ALTER TABLE "Notification" ADD COLUMN "deliveryMethods" JSONB;
ALTER TABLE "Notification" ADD COLUMN "priority" "NotificationPriority" DEFAULT 'NORMAL';
ALTER TABLE "Notification" ADD COLUMN "expiresAt" TIMESTAMP;
```

### New Enums
```sql
CREATE TYPE "NotificationPriority" AS ENUM ('LOW', 'NORMAL', 'HIGH', 'URGENT');
CREATE TYPE "NotificationDeliveryMethod" AS ENUM ('IN_APP', 'TOAST', 'EMAIL', 'SMS', 'PUSH');
CREATE TYPE "NotificationFrequency" AS ENUM ('IMMEDIATE', 'HOURLY', 'DAILY', 'WEEKLY', 'NEVER');
```

## Event Types

### Purchase Order Events
- `po.status.changed` - General status change
- `po.created` - New PO created
- `po.approved` - PO approved
- `po.rejected` - PO rejected
- `po.received` - PO received
- `po.overdue` - PO overdue

### Inventory Events
- `inventory.low_stock` - Product low stock
- `inventory.out_of_stock` - Product out of stock
- `inventory.batch.expiring` - Batch expiring soon
- `inventory.batch.expired` - Batch expired

### Cash Management Events
- `cash.audit.alert` - Cash audit discrepancy
- `cash.reconciliation.required` - Reconciliation needed

### Revenue Events
- `revenue.target.achieved` - Revenue target met
- `revenue.target.missed` - Revenue target missed

### System Events
- `system.maintenance` - Scheduled maintenance
- `user.action.required` - User action needed

## API Endpoints

### Notification Preferences
- `GET /api/notifications/preferences` - Get user preferences
- `PUT /api/notifications/preferences` - Update user preferences
- `POST /api/notifications/preferences` - Bulk enable/disable

### Notification Templates (Admin Only)
- `GET /api/notifications/templates` - Get all templates
- `POST /api/notifications/templates` - Create/update template
- `GET /api/notifications/templates/[eventType]` - Get specific template
- `PUT /api/notifications/templates/[eventType]` - Update specific template
- `DELETE /api/notifications/templates/[eventType]` - Delete template

## Usage Examples

### Basic Event Emission
```typescript
import { notify } from '@/lib/notifications';

// Emit a purchase order approval event
await notify({
  eventType: 'po.approved',
  sourceId: 'po_123',
  sourceType: 'purchase_order',
  payload: {
    poNumber: 'PO-001',
    supplierName: 'Acme Corp',
    total: 1500.00,
    approvedBy: 'John Doe',
  },
});
```

### Convenience Functions
```typescript
import { notifyPOApproved, notifyLowStock } from '@/lib/notifications';

// Purchase Order approved
await notifyPOApproved('po_123', 'john_doe', {
  poNumber: 'PO-001',
  supplierName: 'Acme Corp',
  total: 1500.00,
});

// Low stock alert
await notifyLowStock('product_456', 'Widget A', 5, 10, {
  category: 'Electronics',
  location: 'Warehouse A',
});
```

### Feature Integration
```typescript
import { registerFeatureNotificationHandler } from '@/lib/notifications/notification-registry';

// Register a custom handler for your feature
registerFeatureNotificationHandler(
  'custom.feature.event',
  async (event) => {
    // Your custom notification logic
    console.log('Custom feature event:', event);
  },
  20 // Priority
);
```

## Migration Strategy

### Phase 1: Database Migration
1. Run Prisma migration to add new tables and columns
2. Update enum definitions
3. Verify schema changes

### Phase 2: System Initialization
1. Run initialization script to seed templates and preferences
2. Migrate existing notifications to new format
3. Test notification system functionality

### Phase 3: Code Migration
1. Replace existing notification calls with new API
2. Update Purchase Order workflows
3. Update Inventory management workflows
4. Update Cash audit workflows

### Phase 4: UI Integration
1. Add notification preferences page
2. Update notification dropdown component
3. Add template management interface (admin)
4. Test user experience

### Phase 5: Delivery Methods
1. Implement email delivery
2. Implement SMS delivery (optional)
3. Implement push notifications (optional)
4. Test multi-channel delivery

## Benefits

### For Developers
- **Decoupled Architecture**: Features can add notifications without modifying core code
- **Type Safety**: Strong typing for events and notifications
- **Easy Testing**: Mock event system for unit tests
- **Extensibility**: Simple API for adding new notification types

### For Users
- **Granular Control**: Per-event-type notification preferences
- **Multiple Delivery Methods**: Choose how to receive notifications
- **Quiet Hours**: Avoid notifications during specified times
- **Consistent Experience**: Standardized notification templates

### For Administrators
- **Template Management**: Customize notification content
- **User Management**: Bulk preference operations
- **Monitoring**: Event tracking and notification analytics
- **Reliability**: Event persistence and recovery mechanisms

## Future Enhancements

1. **Real-time Notifications**: WebSocket integration for instant delivery
2. **Notification Analytics**: Track delivery rates and user engagement
3. **A/B Testing**: Test different notification templates
4. **Machine Learning**: Smart notification timing and content
5. **Multi-language Support**: Localized notification templates
6. **Advanced Scheduling**: Complex notification scheduling rules
7. **Integration APIs**: Webhook support for external systems
