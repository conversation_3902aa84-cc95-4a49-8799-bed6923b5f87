# Notification Import Error Fix Guide

## Issue Summary
The notification preferences page is failing with a module import error: "Export 'preferences' doesn't exist in target module @/lib/notifications"

## Root Cause
The error is likely due to one or more of the following:
1. Database tables haven't been created yet
2. Module loading order issues during build/runtime
3. TypeScript compilation issues
4. Missing dependencies in the notification system

## Step-by-Step Resolution

### Step 1: Verify Database Setup
First, ensure the notification system database tables exist:

```bash
# Check if Prisma client is up to date
npx prisma generate

# Run database migration to create notification tables
npx prisma migrate dev --name add-notification-system

# If migration fails, try pushing the schema directly
npx prisma db push
```

### Step 2: Test Module Imports
Run the debug script to identify the exact issue:

```bash
# Test notification module imports
npx ts-node src/scripts/debug-notification-exports.ts
```

This will show you:
- Which modules are importing correctly
- Whether the preferences object exists
- Database connectivity status
- Detailed error information

### Step 3: Initialize Notification System
If the database is set up correctly, initialize the notification system:

```bash
# Run the initialization script
npx ts-node src/scripts/initialize-notification-system.ts
```

### Step 4: Clear Build Cache
Sometimes the issue is due to cached builds:

```bash
# Clear Next.js cache
rm -rf .next

# Clear node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Regenerate Prisma client
npx prisma generate
```

### Step 5: Test the API Endpoint Directly
Test the API endpoint to see detailed error logs:

```bash
# Start the development server
npm run dev

# In another terminal, test the API endpoint
curl -X GET http://localhost:3000/api/notifications/preferences \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

Check the console logs for detailed error information.

### Step 6: Verify File Structure
Ensure all notification system files exist:

```
src/lib/notifications/
├── index.ts                    # Main API (exports preferences)
├── preference-manager.ts       # Preference management
├── template-manager.ts         # Template management
├── notification-engine.ts      # Notification processing
└── notification-registry.ts    # Event handlers

src/lib/events/
└── event-system.ts            # Event system

src/app/api/notifications/
└── preferences/
    └── route.ts               # API route (uses dynamic imports)
```

### Step 7: Manual Database Table Creation
If migrations continue to fail, manually create the tables:

```sql
-- Connect to your database and run these commands

-- Create NotificationTemplate table
CREATE TABLE IF NOT EXISTS "NotificationTemplate" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT UNIQUE NOT NULL,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "titleTemplate" TEXT NOT NULL,
  "messageTemplate" TEXT NOT NULL,
  "defaultDeliveryMethods" TEXT[] NOT NULL,
  "defaultPriority" TEXT DEFAULT 'NORMAL',
  "variables" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create NotificationPreference table
CREATE TABLE IF NOT EXISTS "NotificationPreference" (
  "id" TEXT PRIMARY KEY,
  "userId" TEXT NOT NULL,
  "eventType" TEXT NOT NULL,
  "enabled" BOOLEAN DEFAULT true,
  "deliveryMethods" TEXT[] NOT NULL,
  "frequency" TEXT DEFAULT 'IMMEDIATE',
  "quietHours" JSONB,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE("userId", "eventType")
);

-- Create NotificationEvent table
CREATE TABLE IF NOT EXISTS "NotificationEvent" (
  "id" TEXT PRIMARY KEY,
  "eventType" TEXT NOT NULL,
  "eventId" TEXT NOT NULL,
  "sourceId" TEXT,
  "sourceType" TEXT,
  "payload" JSONB NOT NULL,
  "processed" BOOLEAN DEFAULT false,
  "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  "processedAt" TIMESTAMP
);

-- Add new columns to existing Notification table (if they don't exist)
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "eventType" TEXT;
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "eventId" TEXT;
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "deliveryMethods" JSONB;
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "priority" TEXT DEFAULT 'NORMAL';
ALTER TABLE "Notification" ADD COLUMN IF NOT EXISTS "expiresAt" TIMESTAMP;
```

### Step 8: Test the Fix
After completing the above steps:

1. Restart your development server:
   ```bash
   npm run dev
   ```

2. Navigate to the notification preferences page:
   ```
   http://localhost:3000/settings/notifications
   ```

3. Check the browser console and server logs for any remaining errors.

## Troubleshooting Common Issues

### Issue: "Table 'NotificationPreference' doesn't exist"
**Solution:** Run the database migration or manually create tables (Step 7).

### Issue: "Cannot read property 'getForUser' of undefined"
**Solution:** The preferences object is not being imported correctly. Check that:
- All notification system files exist
- Database tables are created
- No circular import dependencies

### Issue: "Module not found: @/lib/notifications"
**Solution:** 
- Verify TypeScript path configuration in `tsconfig.json`
- Ensure all notification system files exist
- Clear build cache and reinstall dependencies

### Issue: Build-time vs Runtime Errors
If you see the error during build but not runtime (or vice versa):
- Clear Next.js cache: `rm -rf .next`
- Restart development server
- Check for TypeScript compilation errors

## Verification Checklist

After implementing the fixes, verify:

- [ ] Database tables exist (NotificationTemplate, NotificationPreference, NotificationEvent)
- [ ] Notification table has new columns
- [ ] API route uses dynamic imports
- [ ] Debug script runs without errors
- [ ] `/settings/notifications` page loads
- [ ] Console shows successful module imports
- [ ] User can view and update preferences

## Additional Support

If the issue persists after following this guide:

1. **Check the detailed logs** from the debug script
2. **Verify your database connection** is working
3. **Ensure all dependencies** are installed correctly
4. **Review any custom modifications** to the notification system files

The enhanced API route now includes extensive logging that will help identify the exact point of failure.
