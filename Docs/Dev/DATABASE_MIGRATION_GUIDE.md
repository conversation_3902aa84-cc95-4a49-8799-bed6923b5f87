# 🗄️ Notification System Database Migration Guide

## 📋 **Current Status**
The database schema is already in sync with Prisma, which means the notification system tables should exist. However, they may be empty and need initialization.

## 🚀 **Step-by-Step Migration & Initialization Process**

### **Step 1: Verify Database Connection**
First, ensure your database is running and accessible:

```bash
# Navigate to your project directory
cd /path/to/your/npos/project

# Test database connection
npx prisma db push
```

**Expected Output:**
```
✔ The database is already in sync with the Prisma schema.
✔ Generated Prisma Client
```

### **Step 2: Generate Prisma Client**
Ensure the Prisma client is up to date:

```bash
npx prisma generate
```

### **Step 3: Test Notification System in Browser**

1. **Open the notification preferences page:**
   ```
   http://localhost:3000/settings/notifications
   ```

2. **Log in as a SUPER_ADMIN user** (required for initialization)

3. **Look for the notification system status:**
   - If you see "No Notification Preferences Found" → System needs initialization
   - If you see notification preferences → System is already working

### **Step 4: Initialize the Notification System**

If the system needs initialization:

1. **Click "Check Status" button** to see current system state
2. **Review the status indicators:**
   - 🟢 Green = Working correctly
   - 🟡 Yellow = Needs attention  
   - 🔴 Red = Requires action

3. **Follow the recommendations shown**

4. **Click "Initialize System" button** (SUPER_ADMIN only)

5. **Wait for initialization to complete** - you should see:
   ```
   ✅ Notification system initialized successfully! 
   Created X templates and Y preferences.
   ```

### **Step 5: Verify Success**

After initialization:

1. **Refresh the page** or click "Check Status" again
2. **Verify status indicators are green:**
   - 🟢 Database: Connected
   - 🟢 Tables: Exist
   - 🟢 Templates: 5+ 
   - 🟢 Preferences: 15+

3. **Check that notification preferences are displayed**
4. **Test updating preferences** and saving changes

## 🔧 **Troubleshooting Common Issues**

### **Issue: "Database connection failed"**
**Symptoms:** Red database indicator, connection errors
**Solutions:**
```bash
# Check if database server is running
sudo systemctl status postgresql  # For PostgreSQL
# or
sudo systemctl status mysql       # For MySQL

# Verify connection string in .env file
cat .env | grep DATABASE_URL

# Test connection manually
npx prisma studio
```

### **Issue: "Notification tables not found"**
**Symptoms:** Red tables indicator, table missing errors
**Solutions:**
```bash
# Force schema push
npx prisma db push --force-reset

# Or run migration
npx prisma migrate dev --name add-notification-system

# Regenerate client
npx prisma generate
```

### **Issue: "Authentication failed"**
**Symptoms:** 401/403 errors when clicking buttons
**Solutions:**
1. **Log out and log back in**
2. **Ensure you're using a SUPER_ADMIN account**
3. **Clear browser cookies if needed**
4. **Check user role in database:**
   ```sql
   SELECT id, name, email, role FROM "User" WHERE role = 'SUPER_ADMIN';
   ```

### **Issue: "Insufficient permissions"**
**Symptoms:** "Only super administrators can initialize" error
**Solutions:**
1. **Log in with a SUPER_ADMIN account**
2. **Or update your user role:**
   ```sql
   UPDATE "User" SET role = 'SUPER_ADMIN' WHERE email = '<EMAIL>';
   ```

### **Issue: "No users found"**
**Symptoms:** Cannot create preferences, no users in system
**Solutions:**
1. **Create a user account through the application**
2. **Or manually insert a SUPER_ADMIN user:**
   ```sql
   INSERT INTO "User" (id, name, email, role, active) 
   VALUES ('admin-1', 'Admin User', '<EMAIL>', 'SUPER_ADMIN', true);
   ```

## 📊 **Expected Database Tables**

After successful migration, you should have these tables:

| Table Name | Purpose | Expected Records |
|------------|---------|------------------|
| `NotificationTemplate` | Template definitions | 5+ default templates |
| `NotificationPreference` | User preferences | Users × Templates |
| `NotificationEvent` | Event log | 0 initially |
| `Notification` | Actual notifications | 0 initially |

## 🎯 **Verification Commands**

To manually verify the database setup:

```bash
# Connect to your database and run these queries:

# Check if tables exist
\dt Notification*  # PostgreSQL
SHOW TABLES LIKE 'Notification%';  # MySQL

# Count records in each table
SELECT COUNT(*) FROM "NotificationTemplate";
SELECT COUNT(*) FROM "NotificationPreference";
SELECT COUNT(*) FROM "NotificationEvent";

# Check sample data
SELECT eventType, name FROM "NotificationTemplate" LIMIT 5;
SELECT eventType, enabled FROM "NotificationPreference" LIMIT 5;
```

## 🎉 **Success Indicators**

You'll know the migration and initialization was successful when:

✅ **Database Status Page shows:**
- 🟢 Database: Connected
- 🟢 Tables: Exist  
- 🟢 Templates: 5+
- 🟢 Preferences: 15+

✅ **Notification Preferences Page shows:**
- List of notification types
- Toggle switches for each notification
- Delivery method options
- Save button that works

✅ **No Error Messages:**
- No "tables don't exist" errors
- No "failed to fetch preferences" errors
- No authentication issues

## 📞 **Getting Help**

If you encounter issues not covered in this guide:

1. **Check browser console** for detailed error messages
2. **Check server logs** for backend errors
3. **Use the "Check Status" button** for system diagnostics
4. **Review the recommendations** shown in the interface

## 🚀 **Quick Start Commands**

For a complete fresh setup:

```bash
# 1. Ensure database is running
# 2. Push schema to database
npx prisma db push

# 3. Generate Prisma client
npx prisma generate

# 4. Start development server
npm run dev

# 5. Open browser and initialize
# http://localhost:3000/settings/notifications
# Click "Initialize System" as SUPER_ADMIN
```

The notification system should be fully functional after following these steps! 🎉
