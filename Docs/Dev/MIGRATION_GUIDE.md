# Product-Supplier Relationship Migration Guide

This guide provides step-by-step instructions for migrating from the legacy single-supplier Product model to the new multi-supplier ProductSupplier junction table system.

## ⚠️ CRITICAL NOTICE

This migration is **REQUIRED** before proceeding to Phase 7. The current system has both old and new supplier relationship models coexisting, which creates data inconsistency and potential conflicts.

## 🎯 Migration Objectives

1. **Migrate existing data**: Convert all Product.supplierId relationships to ProductSupplier records
2. **Remove legacy fields**: Clean up the Product model by removing old supplier fields
3. **Ensure data integrity**: Validate that all supplier relationships are preserved
4. **Test compatibility**: Verify that backward compatibility helpers work correctly

## 📋 Prerequisites

- [ ] Database backup created
- [ ] Development environment ready for testing
- [ ] All team members notified of migration
- [ ] Staging environment available for testing

## 🚀 Migration Steps

### Step 1: Validate Current State

First, validate the current database state and check what needs to be migrated:

```bash
# Validate prerequisites and check current state
node scripts/migrate-supplier-relationships.js --validate-only
```

This will show you:
- How many products have supplier relationships
- Whether all referenced suppliers exist
- Any potential issues that need attention

### Step 2: Run Dry Run

Test the migration without making any changes:

```bash
# Simulate the migration
node scripts/migrate-supplier-relationships.js --dry-run
```

This will show you exactly what the migration would do without actually changing the database.

### Step 3: Execute Data Migration

Run the actual data migration:

```bash
# Execute the migration
node scripts/migrate-supplier-relationships.js
```

If there are warnings about missing suppliers, you can force the migration:

```bash
# Force migration despite warnings
node scripts/migrate-supplier-relationships.js --force
```

### Step 4: Validate Migration Results

After the data migration, validate that everything worked correctly:

```bash
# Validate the migration results
node scripts/validate-migration.js
```

This will:
- Check data integrity
- Test backward compatibility helpers
- Generate a comprehensive report

### Step 5: Create Schema Migration

Once data migration is validated, create the Prisma migration to remove old fields:

```bash
# Create the schema migration
node scripts/create-remove-supplier-migration.js
```

This will:
- Create a new Prisma migration file
- Update the schema.prisma file
- Create a backup of the original schema

### Step 6: Apply Schema Migration

Apply the schema migration to remove the old supplier fields:

```bash
# Apply the migration to development database
npx prisma migrate dev

# Generate Prisma client with updated schema
npx prisma generate
```

### Step 7: Final Validation

Run the validation script again to ensure everything still works:

```bash
# Final validation
node scripts/validate-migration.js
```

### Step 8: Test Application

Test the application thoroughly:
- [ ] Product creation and editing
- [ ] Supplier management
- [ ] Purchase order creation
- [ ] Inventory receiving
- [ ] Batch tracking

## 📊 Migration Scripts Overview

### `migrate-supplier-relationships.js`
- **Purpose**: Migrates data from Product.supplierId to ProductSupplier table
- **Options**:
  - `--validate-only`: Check prerequisites without migrating
  - `--dry-run`: Simulate migration without changes
  - `--force`: Continue despite warnings
- **Safety**: Uses database transactions for atomicity

### `validate-migration.js`
- **Purpose**: Validates migration results and tests compatibility
- **Checks**:
  - Data integrity
  - Orphaned relationships
  - Backward compatibility helpers
  - Preferred supplier logic

### `create-remove-supplier-migration.js`
- **Purpose**: Creates Prisma migration to remove old fields
- **Actions**:
  - Generates migration SQL
  - Updates schema.prisma
  - Creates schema backup

## 🔍 What Gets Migrated

### Data Migration
- `Product.supplierId` → `ProductSupplier.supplierId`
- `Product.purchasePrice` → `ProductSupplier.purchasePrice`
- All migrated suppliers are set as preferred (`isPreferred = true`)
- Migration notes added to track the source

### Schema Changes
- Remove `supplierId` field from Product model
- Remove `supplier` relationship from Product model
- Keep `purchasePrice` field for backward compatibility
- ProductSupplier relationships remain intact

## 🛡️ Safety Measures

### Backup Strategy
- Always create database backup before migration
- Schema backup created automatically
- Transaction-based migration for atomicity

### Validation Checks
- Verify all referenced suppliers exist
- Check for duplicate relationships
- Validate preferred supplier logic
- Test backward compatibility helpers

### Rollback Plan
If migration fails:
1. Restore database from backup
2. Restore schema from backup file
3. Investigate and fix issues
4. Retry migration

## 🚨 Common Issues and Solutions

### Issue: Missing Suppliers
**Problem**: Products reference suppliers that don't exist
**Solution**: Use `--force` flag or create missing suppliers first

### Issue: Duplicate Relationships
**Problem**: ProductSupplier relationships already exist
**Solution**: Script automatically skips existing relationships

### Issue: Preferred Supplier Conflicts
**Problem**: Multiple preferred suppliers for one product
**Solution**: Migration sets only migrated supplier as preferred

### Issue: Backward Compatibility Failures
**Problem**: Helper functions don't work after migration
**Solution**: Check that ProductSupplier relationships were created correctly

## 📈 Expected Results

After successful migration:
- All Product.supplierId relationships converted to ProductSupplier records
- No data loss or corruption
- Backward compatibility helpers work correctly
- Application functions normally with new schema
- Clean, consistent data model ready for Phase 7

## 🔧 Troubleshooting

### Debug Mode
Add debug logging to migration scripts:
```bash
DEBUG=* node scripts/migrate-supplier-relationships.js
```

### Manual Verification
Check migration results manually:
```sql
-- Check old vs new relationships
SELECT 
  p.id,
  p.name,
  p.supplierId as old_supplier,
  ps.supplierId as new_supplier,
  ps.isPreferred
FROM "Product" p
LEFT JOIN "ProductSupplier" ps ON p.id = ps.productId
WHERE p.supplierId IS NOT NULL;
```

### Recovery Commands
If you need to rollback:
```bash
# Restore database backup
pg_restore -d your_database backup_file.sql

# Restore schema backup
cp prisma/schema.prisma.backup.timestamp prisma/schema.prisma

# Regenerate Prisma client
npx prisma generate
```

## 📞 Support

If you encounter issues during migration:
1. Check the migration logs for specific error messages
2. Run validation scripts to identify problems
3. Review this guide for common solutions
4. Create database backup before attempting fixes

## ✅ Migration Checklist

- [ ] Database backup created
- [ ] Validation script run successfully
- [ ] Dry run completed without errors
- [ ] Data migration executed successfully
- [ ] Migration results validated
- [ ] Schema migration created and applied
- [ ] Final validation passed
- [ ] Application tested thoroughly
- [ ] Team notified of completion

## 🎉 Post-Migration

After successful migration:
1. Update documentation to reflect new data model
2. Remove migration scripts from production deployment
3. Monitor application for any issues
4. Proceed with Phase 7 implementation

---

**Remember**: This migration is critical for system stability and must be completed before Phase 7. Take your time, test thoroughly, and don't hesitate to ask for help if needed.
