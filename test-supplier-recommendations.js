// Simple test script to verify supplier recommendations API
const testSupplierRecommendations = async () => {
  try {
    console.log('Testing supplier recommendations API...');
    
    // Test data - replace with actual product IDs from your database
    const testItems = [
      { productId: 'test-product-1', quantity: 10 },
      { productId: 'test-product-2', quantity: 5 }
    ];

    const response = await fetch('http://localhost:3001/api/purchase-orders/supplier-recommendations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers if needed
        'Cookie': 'session-token=your-session-token-here'
      },
      body: JSON.stringify({ items: testItems })
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('Success! Response data:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testSupplierRecommendations();
