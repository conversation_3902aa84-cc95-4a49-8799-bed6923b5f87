import { createMockAuthRequest, runApiTest, mockPrisma } from "./testUtils";
import { NextResponse } from "next/server";

// Mock API handlers for Product API

// Mock Products API
const getProducts = async (req: Request) => {
  return NextResponse.json({
    products: await mockPrisma.product.findMany(),
    pagination: {
      total: await mockPrisma.product.count(),
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createProduct = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    product: {
      id: "mock-product-id",
      name: body.name || "New Test Product",
      sku: body.sku || "TEST-NEW",
      description: body.description || "Test product description",
      basePrice: body.basePrice || 15.99,
      optionalPrice1: body.optionalPrice1 || null,
      optionalPrice2: body.optionalPrice2 || null,
      categoryId: body.categoryId || "mock-category-id",
      unitId: body.unitId || "mock-unit-id",
      barcode: body.barcode || "1234567890123",
      imageUrl: body.imageUrl || null,
      active: body.active !== undefined ? body.active : true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const getProduct = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    product: await mockPrisma.product.findUnique()
  });
};

const updateProduct = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    product: {
      id: params.id || "mock-product-id",
      name: body.name || "Updated Test Product",
      sku: body.sku || "TEST-UPD",
      description: body.description || "Updated test product description",
      basePrice: body.basePrice || 19.99,
      categoryId: body.categoryId || "mock-category-id",
      unitId: body.unitId || "mock-unit-id",
      barcode: body.barcode || "1234567890123",
      imageUrl: body.imageUrl || null,
      active: body.active !== undefined ? body.active : true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const deleteProduct = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    success: true,
    message: "Product deleted successfully"
  });
};

// Mock Categories API
const getCategories = async (req: Request) => {
  return NextResponse.json({
    categories: [
      {
        id: "mock-category-id-1",
        name: "Test Category 1",
        description: "Test category description 1",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 5 }
      },
      {
        id: "mock-category-id-2",
        name: "Test Category 2",
        description: "Test category description 2",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 3 }
      }
    ],
    pagination: {
      total: 2,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createCategory = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    category: {
      id: "mock-new-category-id",
      name: body.name || "New Test Category",
      description: body.description || "New test category description",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const updateCategory = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    category: {
      id: params.id || "mock-category-id",
      name: body.name || "Updated Test Category",
      description: body.description || "Updated test category description",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const deleteCategory = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    success: true,
    message: "Category deleted successfully"
  });
};

// Mock Units API
const getUnits = async (req: Request) => {
  return NextResponse.json({
    units: [
      {
        id: "mock-unit-id-1",
        name: "Piece",
        abbreviation: "pc",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 8 }
      },
      {
        id: "mock-unit-id-2",
        name: "Kilogram",
        abbreviation: "kg",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 4 }
      }
    ],
    pagination: {
      total: 2,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createUnit = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    unit: {
      id: "mock-new-unit-id",
      name: body.name || "New Test Unit",
      abbreviation: body.abbreviation || "ntu",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const updateUnit = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    unit: {
      id: params.id || "mock-unit-id",
      name: body.name || "Updated Test Unit",
      abbreviation: body.abbreviation || "utu",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const deleteUnit = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    success: true,
    message: "Unit deleted successfully"
  });
};

// Mock Suppliers API
const getSuppliers = async (req: Request) => {
  return NextResponse.json({
    suppliers: [
      {
        id: "mock-supplier-id-1",
        name: "Test Supplier 1",
        contactName: "John Doe",
        email: "<EMAIL>",
        phone: "************",
        address: "123 Supplier St",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 6 }
      },
      {
        id: "mock-supplier-id-2",
        name: "Test Supplier 2",
        contactName: "Jane Smith",
        email: "<EMAIL>",
        phone: "************",
        address: "456 Vendor Ave",
        createdAt: new Date(),
        updatedAt: new Date(),
        _count: { products: 4 }
      }
    ],
    pagination: {
      total: 2,
      page: 1,
      limit: 10,
      pages: 1
    }
  });
};

const createSupplier = async (req: Request) => {
  const body = await req.json();
  return NextResponse.json({
    supplier: {
      id: "mock-new-supplier-id",
      name: body.name || "New Test Supplier",
      contactName: body.contactName || "New Contact",
      email: body.email || "<EMAIL>",
      phone: body.phone || "************",
      address: body.address || "789 New Supplier Rd",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const updateSupplier = async (req: Request, { params }: { params: { id: string } }) => {
  const body = await req.json();
  return NextResponse.json({
    supplier: {
      id: params.id || "mock-supplier-id",
      name: body.name || "Updated Test Supplier",
      contactName: body.contactName || "Updated Contact",
      email: body.email || "<EMAIL>",
      phone: body.phone || "************",
      address: body.address || "321 Updated Supplier Blvd",
      createdAt: new Date(),
      updatedAt: new Date()
    }
  });
};

const deleteSupplier = async (req: Request, { params }: { params: { id: string } }) => {
  return NextResponse.json({
    success: true,
    message: "Supplier deleted successfully"
  });
};

// Product API Tests
export async function runProductTests() {
  const tests = [];

  // Product CRUD Tests
  tests.push(await runApiTest(
    "Get Products List",
    getProducts,
    createMockAuthRequest("http://localhost:3000/api/inventory/products")
  ));

  tests.push(await runApiTest(
    "Create New Product",
    createProduct,
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/products",
      "POST",
      {
        name: "Test Product",
        sku: "TEST-123",
        description: "Test product description",
        basePrice: 15.99,
        categoryId: "mock-category-id",
        unitId: "mock-unit-id"
      }
    )
  ));

  tests.push(await runApiTest(
    "Get Product by ID",
    (req) => getProduct(req, { params: { id: "mock-product-id" } }),
    createMockAuthRequest("http://localhost:3000/api/inventory/products/mock-product-id")
  ));

  tests.push(await runApiTest(
    "Update Product",
    (req) => updateProduct(req, { params: { id: "mock-product-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/products/mock-product-id",
      "PUT",
      {
        name: "Updated Product",
        basePrice: 19.99
      }
    )
  ));

  tests.push(await runApiTest(
    "Delete Product",
    (req) => deleteProduct(req, { params: { id: "mock-product-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/products/mock-product-id",
      "DELETE"
    )
  ));

  // Category Tests
  tests.push(await runApiTest(
    "Get Categories List",
    getCategories,
    createMockAuthRequest("http://localhost:3000/api/inventory/categories")
  ));

  tests.push(await runApiTest(
    "Create New Category",
    createCategory,
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/categories",
      "POST",
      {
        name: "Test Category",
        description: "Test category description"
      }
    )
  ));

  tests.push(await runApiTest(
    "Update Category",
    (req) => updateCategory(req, { params: { id: "mock-category-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/categories/mock-category-id",
      "PUT",
      {
        name: "Updated Category",
        description: "Updated category description"
      }
    )
  ));

  tests.push(await runApiTest(
    "Delete Category",
    (req) => deleteCategory(req, { params: { id: "mock-category-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/categories/mock-category-id",
      "DELETE"
    )
  ));

  // Unit Tests
  tests.push(await runApiTest(
    "Get Units List",
    getUnits,
    createMockAuthRequest("http://localhost:3000/api/inventory/units")
  ));

  tests.push(await runApiTest(
    "Create New Unit",
    createUnit,
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/units",
      "POST",
      {
        name: "Test Unit",
        abbreviation: "tu"
      }
    )
  ));

  tests.push(await runApiTest(
    "Update Unit",
    (req) => updateUnit(req, { params: { id: "mock-unit-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/units/mock-unit-id",
      "PUT",
      {
        name: "Updated Unit",
        abbreviation: "uu"
      }
    )
  ));

  tests.push(await runApiTest(
    "Delete Unit",
    (req) => deleteUnit(req, { params: { id: "mock-unit-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/units/mock-unit-id",
      "DELETE"
    )
  ));

  // Supplier Tests
  tests.push(await runApiTest(
    "Get Suppliers List",
    getSuppliers,
    createMockAuthRequest("http://localhost:3000/api/inventory/suppliers")
  ));

  tests.push(await runApiTest(
    "Create New Supplier",
    createSupplier,
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/suppliers",
      "POST",
      {
        name: "Test Supplier",
        contactName: "John Doe",
        email: "<EMAIL>",
        phone: "************",
        address: "123 Supplier St"
      }
    )
  ));

  tests.push(await runApiTest(
    "Update Supplier",
    (req) => updateSupplier(req, { params: { id: "mock-supplier-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/suppliers/mock-supplier-id",
      "PUT",
      {
        name: "Updated Supplier",
        contactName: "Jane Smith",
        email: "<EMAIL>"
      }
    )
  ));

  tests.push(await runApiTest(
    "Delete Supplier",
    (req) => deleteSupplier(req, { params: { id: "mock-supplier-id" } }),
    createMockAuthRequest(
      "http://localhost:3000/api/inventory/suppliers/mock-supplier-id",
      "DELETE"
    )
  ));

  return tests;
}

export async function runAllProductTests() {
  return await runProductTests();
}
