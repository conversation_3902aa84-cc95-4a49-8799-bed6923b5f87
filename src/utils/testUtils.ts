import { NextRequest } from "next/server";

// Create a mock function for browser environment
const createMockFn = () => {
  const fn = (...args: any[]) => {
    fn.calls.push(args);
    return fn.returnValue;
  };
  fn.calls = [] as any[][];
  fn.returnValue = undefined;
  fn.mockReturnValue = (value: any) => {
    fn.returnValue = value;
    return fn;
  };
  fn.mockResolvedValue = (value: any) => {
    fn.returnValue = Promise.resolve(value);
    return fn;
  };
  fn.mockRejectedValue = (error: any) => {
    fn.returnValue = Promise.reject(error);
    return fn;
  };
  return fn;
};

// Mock authentication for API tests
export function createMockAuthRequest(
  url: string = "http://localhost:3000",
  method: string = "GET",
  body: any = null,
  role: string = "SUPER_ADMIN"
): NextRequest {
  // Create a mock URL with search params if provided
  const urlObj = new URL(url);

  // Create mock cookies with a session token
  const headers = new Headers();
  headers.append("Cookie", `session-token=mock-token-for-testing`);

  // Create request options
  const options: RequestInit = {
    method,
    headers,
  };

  // Add body if provided
  if (body) {
    options.body = JSON.stringify(body);
  }

  // Create the request
  const request = new NextRequest(urlObj, options);

  // We'll handle JWT verification in the API handler mocks
  return request;
}

// Helper to run a test and return the result
export async function runApiTest(
  name: string,
  handler: (req: NextRequest) => Promise<Response>,
  request: NextRequest
): Promise<{
  name: string;
  success: boolean;
  status: number;
  message: string;
  duration: number;
  response?: any;
  error?: any;
}> {
  const startTime = performance.now();
  try {
    // Add authentication headers to the request
    // This ensures the request is properly authenticated for the API
    if (!request.headers.has('Cookie')) {
      request.headers.set('Cookie', 'session-token=mock-token-for-testing');
    }

    // Add content-type header if not present
    if (!request.headers.has('Content-Type')) {
      request.headers.set('Content-Type', 'application/json');
    }

    // Mock the JWT verification that would happen in the handler
    // This is a simplified approach for client-side testing
    const response = await handler(request);
    const endTime = performance.now();
    const duration = Math.round((endTime - startTime) * 100) / 100;
    const status = response.status;

    // Clone the response before reading it (since response body can only be read once)
    const responseClone = response.clone();

    // Check content type to avoid parsing HTML as JSON
    const contentType = response.headers.get('content-type');
    let responseData;
    let responseText = '';

    try {
      // First try to get the text content regardless of content type
      responseText = await responseClone.text();

      // Then try to parse as JSON if it looks like JSON
      if (contentType && contentType.includes('application/json') ||
          (responseText.trim().startsWith('{') && responseText.trim().endsWith('}')) ||
          (responseText.trim().startsWith('[') && responseText.trim().endsWith(']'))) {
        try {
          responseData = JSON.parse(responseText);
        } catch (jsonError) {
          console.warn(`Failed to parse response as JSON despite content type: ${contentType}`);
          responseData = {
            nonJsonResponse: true,
            contentType: contentType || 'unknown',
            textPreview: responseText.substring(0, 150) + (responseText.length > 150 ? '...' : ''),
            parseError: (jsonError as Error).message
          };
        }
      } else {
        // For non-JSON responses, just use the text
        responseData = {
          nonJsonResponse: true,
          contentType: contentType || 'unknown',
          textPreview: responseText.substring(0, 150) + (responseText.length > 150 ? '...' : '')
        };
      }
    } catch (textError) {
      // If we can't even get the text, create a fallback response
      console.error('Error reading response text:', textError);
      responseData = {
        nonJsonResponse: true,
        contentType: contentType || 'unknown',
        error: 'Failed to read response body',
        errorDetails: (textError as Error).message
      };
    }

    return {
      name,
      success: status >= 200 && status < 300,
      status,
      message: `Status: ${status}`,
      duration,
      response: responseData
    };
  } catch (error) {
    const endTime = performance.now();
    const duration = Math.round((endTime - startTime) * 100) / 100;
    console.error(`Test "${name}" failed with error:`, error);

    return {
      name,
      success: false,
      status: 500,
      message: `Error: ${(error as Error).message}`,
      duration,
      error: {
        message: (error as Error).message,
        stack: (error as Error).stack,
        name: (error as Error).name
      }
    };
  }
}

// Mock Prisma for testing
export const mockPrisma = {
  product: {
    findUnique: createMockFn().mockResolvedValue({
      id: "mock-product-id",
      name: "Test Product",
      sku: "TEST-123",
      basePrice: 10.99,
      active: true,
      category: { id: "mock-category-id", name: "Test Category" },
      unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-product-id-1",
        name: "Test Product 1",
        sku: "TEST-123",
        basePrice: 10.99,
        active: true,
        category: { id: "mock-category-id", name: "Test Category" },
        unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" },
        storeStock: { quantity: 10, minThreshold: 5 }
      },
      {
        id: "mock-product-id-2",
        name: "Test Product 2",
        sku: "TEST-456",
        basePrice: 20.99,
        active: true,
        category: { id: "mock-category-id", name: "Test Category" },
        unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" },
        storeStock: { quantity: 3, minThreshold: 5 }
      }
    ]),
    count: createMockFn().mockResolvedValue(2)
  },
  storeStock: {
    findUnique: createMockFn().mockResolvedValue({
      id: "mock-store-stock-id",
      productId: "mock-product-id",
      quantity: 10,
      minThreshold: 5,
      lastUpdated: new Date()
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-store-stock-id-1",
        productId: "mock-product-id-1",
        quantity: 10,
        minThreshold: 5,
        lastUpdated: new Date(),
        product: {
          id: "mock-product-id-1",
          name: "Test Product 1",
          sku: "TEST-123",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        }
      },
      {
        id: "mock-store-stock-id-2",
        productId: "mock-product-id-2",
        quantity: 3,
        minThreshold: 5,
        lastUpdated: new Date(),
        product: {
          id: "mock-product-id-2",
          name: "Test Product 2",
          sku: "TEST-456",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        }
      }
    ]),
    count: createMockFn().mockResolvedValue(2),
    upsert: createMockFn().mockResolvedValue({
      id: "mock-store-stock-id",
      productId: "mock-product-id",
      quantity: 15,
      minThreshold: 5,
      lastUpdated: new Date()
    }),
    update: createMockFn().mockResolvedValue({
      id: "mock-store-stock-id",
      productId: "mock-product-id",
      quantity: 15,
      minThreshold: 5,
      lastUpdated: new Date()
    })
  },
  warehouseStock: {
    findUnique: createMockFn().mockResolvedValue({
      id: "mock-warehouse-stock-id",
      productId: "mock-product-id",
      quantity: 20,
      minThreshold: 10,
      lastUpdated: new Date()
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-warehouse-stock-id-1",
        productId: "mock-product-id-1",
        quantity: 20,
        minThreshold: 10,
        lastUpdated: new Date(),
        product: {
          id: "mock-product-id-1",
          name: "Test Product 1",
          sku: "TEST-123",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        }
      }
    ]),
    count: createMockFn().mockResolvedValue(1),
    update: createMockFn().mockResolvedValue({
      id: "mock-warehouse-stock-id",
      productId: "mock-product-id",
      quantity: 25,
      minThreshold: 10,
      lastUpdated: new Date()
    })
  },
  stockAdjustment: {
    create: createMockFn().mockResolvedValue({
      id: "mock-adjustment-id",
      productId: "mock-product-id",
      storeStockId: "mock-store-stock-id",
      previousQuantity: 10,
      newQuantity: 15,
      adjustmentQuantity: 5,
      reason: "INVENTORY_COUNT",
      date: new Date(),
      userId: "mock-user-id"
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-adjustment-id-1",
        productId: "mock-product-id-1",
        storeStockId: "mock-store-stock-id-1",
        previousQuantity: 10,
        newQuantity: 15,
        adjustmentQuantity: 5,
        reason: "INVENTORY_COUNT",
        date: new Date(),
        userId: "mock-user-id",
        product: {
          id: "mock-product-id-1",
          name: "Test Product 1",
          sku: "TEST-123",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        },
        user: {
          id: "mock-user-id",
          name: "Test User",
          email: "<EMAIL>",
          role: "SUPER_ADMIN"
        }
      }
    ]),
    count: createMockFn().mockResolvedValue(1)
  },
  stockHistory: {
    create: createMockFn().mockResolvedValue({
      id: "mock-history-id",
      productId: "mock-product-id",
      storeStockId: "mock-store-stock-id",
      previousQuantity: 10,
      newQuantity: 15,
      changeQuantity: 5,
      source: "ADJUSTMENT",
      date: new Date(),
      userId: "mock-user-id"
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-history-id-1",
        productId: "mock-product-id-1",
        storeStockId: "mock-store-stock-id-1",
        previousQuantity: 10,
        newQuantity: 15,
        changeQuantity: 5,
        source: "ADJUSTMENT",
        date: new Date(),
        userId: "mock-user-id",
        product: {
          id: "mock-product-id-1",
          name: "Test Product 1",
          sku: "TEST-123",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        },
        user: {
          id: "mock-user-id",
          name: "Test User",
          email: "<EMAIL>",
          role: "SUPER_ADMIN"
        }
      }
    ]),
    count: createMockFn().mockResolvedValue(1)
  },
  stockTransfer: {
    create: createMockFn().mockResolvedValue({
      id: "mock-transfer-id",
      productId: "mock-product-id",
      quantity: 5,
      sourceType: "STORE",
      sourceId: "mock-store-stock-id",
      destinationType: "WAREHOUSE",
      destinationId: "mock-warehouse-stock-id",
      status: "PENDING",
      requestedById: "mock-user-id",
      date: new Date()
    }),
    findUnique: createMockFn().mockResolvedValue({
      id: "mock-transfer-id",
      productId: "mock-product-id",
      quantity: 5,
      sourceType: "STORE",
      sourceId: "mock-store-stock-id",
      destinationType: "WAREHOUSE",
      destinationId: "mock-warehouse-stock-id",
      status: "PENDING",
      requestedById: "mock-user-id",
      date: new Date(),
      product: {
        id: "mock-product-id",
        name: "Test Product",
        sku: "TEST-123",
        category: { id: "mock-category-id", name: "Test Category" },
        unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
      },
      requestedBy: {
        id: "mock-user-id",
        name: "Test User",
        email: "<EMAIL>",
        role: "SUPER_ADMIN"
      }
    }),
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-transfer-id-1",
        productId: "mock-product-id-1",
        quantity: 5,
        sourceType: "STORE",
        sourceId: "mock-store-stock-id-1",
        destinationType: "WAREHOUSE",
        destinationId: "mock-warehouse-stock-id-1",
        status: "PENDING",
        requestedById: "mock-user-id",
        date: new Date(),
        product: {
          id: "mock-product-id-1",
          name: "Test Product 1",
          sku: "TEST-123",
          category: { id: "mock-category-id", name: "Test Category" },
          unit: { id: "mock-unit-id", name: "Piece", abbreviation: "pc" }
        },
        requestedBy: {
          id: "mock-user-id",
          name: "Test User",
          email: "<EMAIL>",
          role: "SUPER_ADMIN"
        }
      }
    ]),
    count: createMockFn().mockResolvedValue(1),
    update: createMockFn().mockResolvedValue({
      id: "mock-transfer-id",
      productId: "mock-product-id",
      quantity: 5,
      sourceType: "STORE",
      sourceId: "mock-store-stock-id",
      destinationType: "WAREHOUSE",
      destinationId: "mock-warehouse-stock-id",
      status: "APPROVED",
      requestedById: "mock-user-id",
      approvedById: "mock-user-id",
      approvedAt: new Date(),
      date: new Date()
    })
  },
  activityLog: {
    create: createMockFn().mockResolvedValue({
      id: "mock-activity-log-id",
      userId: "mock-user-id",
      action: "UPDATE_STORE_STOCK",
      details: "Updated store stock for Test Product",
      timestamp: new Date()
    })
  },
  notification: {
    create: createMockFn().mockResolvedValue({
      id: "mock-notification-id",
      userId: "mock-user-id",
      title: "Low Stock Alert",
      message: "2 products are below minimum stock levels.",
      type: "ALERT",
      read: false,
      createdAt: new Date()
    })
  },
  user: {
    findMany: createMockFn().mockResolvedValue([
      {
        id: "mock-user-id-1",
        name: "Admin User",
        email: "<EMAIL>",
        role: "SUPER_ADMIN",
        active: true
      },
      {
        id: "mock-user-id-2",
        name: "Warehouse Admin",
        email: "<EMAIL>",
        role: "WAREHOUSE_ADMIN",
        active: true
      }
    ])
  }
};
