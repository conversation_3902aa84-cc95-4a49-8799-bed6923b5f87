"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import {
  TrendingUp,
  Package,
  DollarSign,
  BarChart3,
  Users,
  ArrowUpDown,
  Trophy,
  Target,
  Activity,
  ExternalLink
} from "lucide-react";
import Link from "next/link";

interface SupplierCostAnalysisData {
  reportType: string;
  analysisType: "single" | "comparison";
  generatedAt: string;
  filters: {
    supplierId?: string;
    startDate?: string;
    endDate?: string;
  };
  // Single supplier analysis
  supplier?: {
    id: string;
    name: string;
    contactPerson?: string;
  };
  summary?: {
    totalBatches: number;
    totalQuantityReceived: number;
    totalValueReceived: number;
    averageCostPerUnit: number;
    currentInventoryQuantity: number;
    currentInventoryValue: number;
    inventoryTurnover: number;
  };
  productAnalysis?: Array<{
    productId: string;
    productName: string;
    productSku: string;
    category: string;
    unit: string;
    batchCount: number;
    totalQuantityReceived: number;
    totalValueReceived: number;
    averageCost: number;
    currentQuantity: number;
    currentValue: number;
    turnoverRate: number;
  }>;
  // Comparison analysis
  suppliers?: Array<{
    supplierId: string;
    supplierName: string;
    contactPerson?: string;
    phone?: string;
    email?: string;
    metrics: {
      totalBatches: number;
      totalQuantityReceived: number;
      totalValueReceived: number;
      averageCostPerUnit: number;
      currentInventoryQuantity: number;
      currentInventoryValue: number;
      uniqueProducts: number;
      productCategories: number;
      inventoryTurnover: number;
    };
    performance: {
      costEfficiency: number;
      inventoryTurnover: number;
      productDiversity: number;
      categoryDiversity: number;
    };
    compositeScore: number;
    ranking: number;
  }>;
}

interface SupplierCostAnalysisProps {
  data: SupplierCostAnalysisData;
}

export function SupplierCostAnalysis({ data }: SupplierCostAnalysisProps) {
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  const { analysisType, generatedAt } = data;

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const renderSortIndicator = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className="ml-1 h-3 w-3 text-muted-foreground" />;
    }
    return (
      <ArrowUpDown 
        className={`ml-1 h-3 w-3 ${
          sortDirection === "asc" ? "rotate-180" : ""
        }`} 
      />
    );
  };

  const getRankingBadge = (ranking: number) => {
    if (ranking === 1) {
      return <Badge className="bg-yellow-100 text-yellow-800"><Trophy className="w-3 h-3 mr-1" />1st</Badge>;
    } else if (ranking === 2) {
      return <Badge className="bg-gray-100 text-gray-800">2nd</Badge>;
    } else if (ranking === 3) {
      return <Badge className="bg-orange-100 text-orange-800">3rd</Badge>;
    } else {
      return <Badge variant="outline">{ranking}th</Badge>;
    }
  };

  if (analysisType === "single" && data.supplier && data.summary) {
    // Single supplier analysis view
    const { supplier, summary, productAnalysis } = data;

    return (
      <div className="space-y-6">
        {/* Supplier Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              <Link
                href={`/inventory/suppliers/${supplier.id}`}
                className="hover:text-primary transition-colors flex items-center gap-1"
              >
                {supplier.name}
                <ExternalLink className="h-4 w-4" />
              </Link>
              - Cost Analysis
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Generated {format(new Date(generatedAt), "MMM dd, yyyy 'at' HH:mm")}
              {supplier.contactPerson && ` • Contact: ${supplier.contactPerson}`}
            </p>
          </CardHeader>
        </Card>

        {/* Summary Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value Received</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalValueReceived)}</div>
              <p className="text-xs text-muted-foreground">{summary.totalBatches} batches</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Cost</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.averageCostPerUnit)}</div>
              <p className="text-xs text-muted-foreground">per unit</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Inventory</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.currentInventoryValue)}</div>
              <p className="text-xs text-muted-foreground">{summary.currentInventoryQuantity} units</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Inventory Turnover</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.inventoryTurnover.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">turnover rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Product Analysis */}
        {productAnalysis && productAnalysis.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Product Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead className="text-right">Batches</TableHead>
                    <TableHead className="text-right">Total Received</TableHead>
                    <TableHead className="text-right">Average Cost</TableHead>
                    <TableHead className="text-right">Current Stock</TableHead>
                    <TableHead className="text-right">Turnover Rate</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {productAnalysis.map((product) => (
                    <TableRow key={product.productId}>
                      <TableCell className="font-medium">{product.productName}</TableCell>
                      <TableCell>{product.productSku}</TableCell>
                      <TableCell>{product.category}</TableCell>
                      <TableCell className="text-right">{product.batchCount}</TableCell>
                      <TableCell className="text-right">
                        {product.totalQuantityReceived} {product.unit}
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(product.averageCost)}
                      </TableCell>
                      <TableCell className="text-right">
                        {product.currentQuantity} {product.unit}
                      </TableCell>
                      <TableCell className="text-right">
                        <Badge 
                          variant={product.turnoverRate > 70 ? "default" : product.turnoverRate > 40 ? "secondary" : "destructive"}
                        >
                          {product.turnoverRate.toFixed(1)}%
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  if (analysisType === "comparison" && data.suppliers) {
    // Multi-supplier comparison view
    const { suppliers } = data;

    // Sort suppliers based on current sort settings
    const sortedSuppliers = [...suppliers].sort((a, b) => {
      if (!sortField) return a.ranking - b.ranking; // Default sort by ranking

      let aValue: any;
      let bValue: any;

      if (sortField.startsWith("metrics.")) {
        const metricField = sortField.replace("metrics.", "");
        aValue = a.metrics[metricField as keyof typeof a.metrics];
        bValue = b.metrics[metricField as keyof typeof b.metrics];
      } else if (sortField.startsWith("performance.")) {
        const perfField = sortField.replace("performance.", "");
        aValue = a.performance[perfField as keyof typeof a.performance];
        bValue = b.performance[perfField as keyof typeof b.performance];
      } else {
        aValue = a[sortField as keyof typeof a];
        bValue = b[sortField as keyof typeof b];
      }

      // Handle numeric values
      if (typeof aValue === "number" && typeof bValue === "number") {
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      // Handle string values
      const aString = String(aValue || "").toLowerCase();
      const bString = String(bValue || "").toLowerCase();

      return sortDirection === "asc"
        ? aString.localeCompare(bString)
        : bString.localeCompare(aString);
    });

    return (
      <div className="space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Supplier Cost Comparison
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Generated {format(new Date(generatedAt), "MMM dd, yyyy 'at' HH:mm")} • {suppliers.length} suppliers
            </p>
          </CardHeader>
        </Card>

        {/* Suppliers Comparison Table */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Performance Ranking</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Rank</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("supplierName")}
                      className="p-0 h-auto font-semibold"
                    >
                      Supplier {renderSortIndicator("supplierName")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("metrics.totalValueReceived")}
                      className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                    >
                      Total Value {renderSortIndicator("metrics.totalValueReceived")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("metrics.averageCostPerUnit")}
                      className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                    >
                      Avg Cost {renderSortIndicator("metrics.averageCostPerUnit")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("metrics.inventoryTurnover")}
                      className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                    >
                      Turnover {renderSortIndicator("metrics.inventoryTurnover")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("metrics.uniqueProducts")}
                      className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                    >
                      Products {renderSortIndicator("metrics.uniqueProducts")}
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort("compositeScore")}
                      className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                    >
                      Score {renderSortIndicator("compositeScore")}
                    </Button>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedSuppliers.map((supplier) => (
                  <TableRow key={supplier.supplierId}>
                    <TableCell>{getRankingBadge(supplier.ranking)}</TableCell>
                    <TableCell>
                      <div>
                        <Link
                          href={`/inventory/suppliers/${supplier.supplierId}`}
                          className="font-medium hover:text-primary transition-colors flex items-center gap-1 w-fit"
                        >
                          {supplier.supplierName}
                          <ExternalLink className="h-3 w-3" />
                        </Link>
                        {supplier.contactPerson && (
                          <div className="text-xs text-muted-foreground">{supplier.contactPerson}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(supplier.metrics.totalValueReceived)}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(supplier.metrics.averageCostPerUnit)}
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge 
                        variant={supplier.metrics.inventoryTurnover > 70 ? "default" : 
                                supplier.metrics.inventoryTurnover > 40 ? "secondary" : "destructive"}
                      >
                        {supplier.metrics.inventoryTurnover.toFixed(1)}%
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">{supplier.metrics.uniqueProducts}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant="outline">
                        {supplier.compositeScore.toFixed(2)}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Supplier Cost Analysis</CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">No data available for analysis.</p>
      </CardContent>
    </Card>
  );
}
