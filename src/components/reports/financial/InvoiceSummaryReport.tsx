"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import { 
  FileText, 
  DollarSign, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Calendar,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  ExternalLink
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { format, differenceInDays } from "date-fns";
import Link from "next/link";

interface InvoiceSummaryData {
  totalInvoices: number;
  pendingInvoices: number;
  approvedInvoices: number;
  paidInvoices: number;
  overdueInvoices: number;
  totalAmount: number;
  paidAmount: number;
  outstandingAmount: number;
  closestDueDate: string | null;
  daysUntilClosestDue: number | null;
  unpaidInvoices: number;
  partiallyPaidInvoices: number;
}

export function InvoiceSummaryReportComponent() {
  const [summaryData, setSummaryData] = useState<InvoiceSummaryData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchSummaryData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/invoices/summary');
      
      if (!response.ok) {
        throw new Error('Failed to fetch invoice summary');
      }

      const data = await response.json();
      setSummaryData(data);
    } catch (err) {
      console.error('Error fetching invoice summary:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch data');
      toast.error('Failed to load invoice summary');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSummaryData();
  }, []);

  const handleRefresh = () => {
    fetchSummaryData();
  };

  if (isLoading && !summaryData) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice Summary
              </span>
              <Skeleton className="h-9 w-24" />
            </CardTitle>
            <CardDescription>
              Overview of invoice status and payment information
            </CardDescription>
          </CardHeader>
        </Card>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-24 mb-1" />
                <Skeleton className="h-3 w-16" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            Error Loading Invoice Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!summaryData) {
    return null;
  }

  const paymentCompletionRate = summaryData.totalInvoices > 0 
    ? (summaryData.paidInvoices / summaryData.totalInvoices) * 100 
    : 0;

  const amountCollectionRate = summaryData.totalAmount > 0 
    ? (summaryData.paidAmount / summaryData.totalAmount) * 100 
    : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Invoice Summary
            </span>
            <div className="flex items-center gap-2">
              <Button 
                onClick={handleRefresh} 
                variant="outline" 
                size="sm"
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Link href="/invoices">
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View All Invoices
                </Button>
              </Link>
            </div>
          </CardTitle>
          <CardDescription>
            Overview of invoice status and payment information as of {format(new Date(), "PPP")}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {/* Total Invoices */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summaryData.totalInvoices}</div>
            <p className="text-xs text-muted-foreground">
              All invoices in system
            </p>
          </CardContent>
        </Card>

        {/* Total Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summaryData.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              Total invoice value
            </p>
          </CardContent>
        </Card>

        {/* Outstanding Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Outstanding Amount</CardTitle>
            <TrendingUp className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(summaryData.outstandingAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              Amount still due
            </p>
          </CardContent>
        </Card>

        {/* Paid Amount */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Amount</CardTitle>
            <TrendingDown className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {formatCurrency(summaryData.paidAmount)}
            </div>
            <p className="text-xs text-muted-foreground">
              {amountCollectionRate.toFixed(1)}% collected
            </p>
          </CardContent>
        </Card>

        {/* Unpaid Invoices */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unpaid Invoices</CardTitle>
            <Clock className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {summaryData.unpaidInvoices}
            </div>
            <p className="text-xs text-muted-foreground">
              Awaiting payment
            </p>
          </CardContent>
        </Card>

        {/* Overdue Invoices */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue Invoices</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {summaryData.overdueInvoices}
            </div>
            <p className="text-xs text-muted-foreground">
              Past due date
            </p>
          </CardContent>
        </Card>

        {/* Closest Due Date */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Next Due Date</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {summaryData.closestDueDate ? (
              <>
                <div className="text-2xl font-bold">
                  {summaryData.daysUntilClosestDue !== null && summaryData.daysUntilClosestDue >= 0 
                    ? `${summaryData.daysUntilClosestDue} days`
                    : 'Overdue'
                  }
                </div>
                <p className="text-xs text-muted-foreground">
                  {format(new Date(summaryData.closestDueDate), "MMM dd, yyyy")}
                </p>
              </>
            ) : (
              <>
                <div className="text-2xl font-bold text-muted-foreground">N/A</div>
                <p className="text-xs text-muted-foreground">No pending invoices</p>
              </>
            )}
          </CardContent>
        </Card>

        {/* Payment Completion Rate */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {paymentCompletionRate.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {summaryData.paidInvoices} of {summaryData.totalInvoices} paid
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status Breakdown */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Invoice Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="secondary">Pending</Badge>
                <span className="text-sm">Pending Approval</span>
              </span>
              <span className="font-medium">{summaryData.pendingInvoices}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="default">Approved</Badge>
                <span className="text-sm">Approved</span>
              </span>
              <span className="font-medium">{summaryData.approvedInvoices}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Payment Status Breakdown</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="destructive">Unpaid</Badge>
                <span className="text-sm">Not Paid</span>
              </span>
              <span className="font-medium">{summaryData.unpaidInvoices}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="outline" className="border-yellow-500 text-yellow-600">Partial</Badge>
                <span className="text-sm">Partially Paid</span>
              </span>
              <span className="font-medium">{summaryData.partiallyPaidInvoices}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="outline" className="border-green-500 text-green-600">Paid</Badge>
                <span className="text-sm">Fully Paid</span>
              </span>
              <span className="font-medium">{summaryData.paidInvoices}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Badge variant="destructive">Overdue</Badge>
                <span className="text-sm">Past Due</span>
              </span>
              <span className="font-medium">{summaryData.overdueInvoices}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
