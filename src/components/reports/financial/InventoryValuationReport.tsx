"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  DollarSign, 
  Package,
  Building2,
  Store,
  Warehouse,
  AlertTriangle,
  Clock,
  TrendingUp,
  TrendingDown,
  BarChart3
} from "lucide-react";
import { format, differenceInDays } from "date-fns";
import { cn, formatCurrency } from "@/lib/utils";
import { InventoryValuationReport } from "@/app/api/reports/financial/inventory-valuation/route";

export function InventoryValuationReportComponent() {
  const [reportData, setReportData] = useState<InventoryValuationReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [valuationMethod, setValuationMethod] = useState<"FIFO" | "LIFO" | "WEIGHTED_AVERAGE">("FIFO");

  const fetchReport = async (method: "FIFO" | "LIFO" | "WEIGHTED_AVERAGE") => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/reports/financial/inventory-valuation?method=${method}`);
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch inventory valuation report");
      }
      
      setReportData(result.data);
    } catch (err) {
      console.error("Error fetching inventory valuation report:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReport(valuationMethod);
  }, [valuationMethod]);

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case "LOW": return "text-green-600";
      case "MEDIUM": return "text-yellow-600";
      case "HIGH": return "text-orange-600";
      case "EXPIRED": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getRiskLevelBadgeVariant = (riskLevel: string): "default" | "secondary" | "destructive" => {
    switch (riskLevel) {
      case "LOW": return "default";
      case "MEDIUM": return "secondary";
      case "HIGH": 
      case "EXPIRED": return "destructive";
      default: return "secondary";
    }
  };

  if (isLoading) {
    return <InventoryValuationReportSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Inventory Valuation Report</CardTitle>
          <CardDescription>Error loading report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => fetchReport(valuationMethod)}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!reportData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Inventory Valuation Report</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Inventory Valuation Report
            <Select value={valuationMethod} onValueChange={setValuationMethod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="FIFO">FIFO</SelectItem>
                <SelectItem value="LIFO">LIFO</SelectItem>
                <SelectItem value="WEIGHTED_AVERAGE">Weighted Average</SelectItem>
              </SelectContent>
            </Select>
          </CardTitle>
          <CardDescription>
            Inventory valuation as of {reportData.reportDate} using {reportData.valuationMethod} method
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Inventory Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalInventoryValue)}</div>
            <div className="text-xs text-muted-foreground">
              {reportData.summary.totalProducts} products, {reportData.summary.totalBatches} batches
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Store Value</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalStoreValue)}</div>
            <div className="text-xs text-muted-foreground">
              {reportData.locationBreakdown.store.productCount} products
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warehouse Value</CardTitle>
            <Warehouse className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalWarehouseValue)}</div>
            <div className="text-xs text-muted-foreground">
              {reportData.locationBreakdown.warehouse.productCount} products
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Value/Product</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.averageValuePerProduct)}</div>
            <div className="text-xs text-muted-foreground">
              Per batch: {formatCurrency(reportData.summary.averageValuePerBatch)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Aging Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Inventory Aging Analysis
          </CardTitle>
          <CardDescription>
            Distribution of inventory value by age
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-green-100 text-green-800">Fresh (0-30 days)</Badge>
                <span className="text-sm">{reportData.agingAnalysis.fresh.threshold}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(reportData.agingAnalysis.fresh.value)}</div>
                <div className="text-xs text-muted-foreground">{reportData.agingAnalysis.fresh.percentage.toFixed(1)}%</div>
              </div>
            </div>
            <Progress value={reportData.agingAnalysis.fresh.percentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-blue-100 text-blue-800">Moderate (31-90 days)</Badge>
                <span className="text-sm">{reportData.agingAnalysis.moderate.threshold}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(reportData.agingAnalysis.moderate.value)}</div>
                <div className="text-xs text-muted-foreground">{reportData.agingAnalysis.moderate.percentage.toFixed(1)}%</div>
              </div>
            </div>
            <Progress value={reportData.agingAnalysis.moderate.percentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-yellow-100 text-yellow-800">Old (91-180 days)</Badge>
                <span className="text-sm">{reportData.agingAnalysis.old.threshold}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(reportData.agingAnalysis.old.value)}</div>
                <div className="text-xs text-muted-foreground">{reportData.agingAnalysis.old.percentage.toFixed(1)}%</div>
              </div>
            </div>
            <Progress value={reportData.agingAnalysis.old.percentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-orange-100 text-orange-800">Very Old (181-365 days)</Badge>
                <span className="text-sm">{reportData.agingAnalysis.veryOld.threshold}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(reportData.agingAnalysis.veryOld.value)}</div>
                <div className="text-xs text-muted-foreground">{reportData.agingAnalysis.veryOld.percentage.toFixed(1)}%</div>
              </div>
            </div>
            <Progress value={reportData.agingAnalysis.veryOld.percentage} className="h-2" />

            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Badge className="bg-red-100 text-red-800">Expired/Very Old</Badge>
                <span className="text-sm">{reportData.agingAnalysis.expired.threshold}</span>
              </div>
              <div className="text-right">
                <div className="font-medium">{formatCurrency(reportData.agingAnalysis.expired.value)}</div>
                <div className="text-xs text-muted-foreground">{reportData.agingAnalysis.expired.percentage.toFixed(1)}%</div>
              </div>
            </div>
            <Progress value={reportData.agingAnalysis.expired.percentage} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Category Valuation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Category Valuation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Products</TableHead>
                <TableHead className="text-right">Batches</TableHead>
                <TableHead className="text-right">Total Value</TableHead>
                <TableHead className="text-right">Percentage</TableHead>
                <TableHead className="text-right">Avg Value/Product</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.categoryValuation.map((category) => (
                <TableRow key={category.categoryId}>
                  <TableCell className="font-medium">{category.categoryName}</TableCell>
                  <TableCell className="text-right">{category.productCount}</TableCell>
                  <TableCell className="text-right">{category.batchCount}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.totalValue)}</TableCell>
                  <TableCell className="text-right">{category.percentage.toFixed(1)}%</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.averageValuePerProduct)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Supplier Valuation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Supplier Valuation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead className="text-right">Products</TableHead>
                <TableHead className="text-right">Batches</TableHead>
                <TableHead className="text-right">Total Value</TableHead>
                <TableHead className="text-right">Percentage</TableHead>
                <TableHead className="text-right">Avg Value/Product</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.supplierValuation.map((supplier) => (
                <TableRow key={supplier.supplierId}>
                  <TableCell className="font-medium">{supplier.supplierName}</TableCell>
                  <TableCell className="text-right">{supplier.productCount}</TableCell>
                  <TableCell className="text-right">{supplier.batchCount}</TableCell>
                  <TableCell className="text-right">{formatCurrency(supplier.totalValue)}</TableCell>
                  <TableCell className="text-right">{supplier.percentage.toFixed(1)}%</TableCell>
                  <TableCell className="text-right">{formatCurrency(supplier.averageValuePerProduct)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Top Value Products */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Highest Value Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Total Value</TableHead>
                <TableHead className="text-right">Quantity</TableHead>
                <TableHead className="text-right">Avg Unit Value</TableHead>
                <TableHead className="text-right">Batches</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.topValueProducts.map((product) => (
                <TableRow key={product.productId}>
                  <TableCell className="font-medium">{product.productName}</TableCell>
                  <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                  <TableCell>{product.category}</TableCell>
                  <TableCell className="text-right">{formatCurrency(product.totalValue)}</TableCell>
                  <TableCell className="text-right">{product.totalQuantity}</TableCell>
                  <TableCell className="text-right">{formatCurrency(product.averageUnitValue)}</TableCell>
                  <TableCell className="text-right">{product.batchCount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Expiry Analysis */}
      {reportData.expiryAnalysis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Expiry Risk Analysis
            </CardTitle>
            <CardDescription>
              Products approaching expiry or already expired
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead>Batch</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead className="text-right">Expiry Date</TableHead>
                  <TableHead className="text-right">Days to Expiry</TableHead>
                  <TableHead className="text-right">Quantity</TableHead>
                  <TableHead className="text-right">Value</TableHead>
                  <TableHead className="text-right">Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportData.expiryAnalysis.slice(0, 20).map((item) => (
                  <TableRow key={item.batchId}>
                    <TableCell className="font-medium">{item.productName}</TableCell>
                    <TableCell className="text-muted-foreground">{item.sku}</TableCell>
                    <TableCell>{item.batchNumber}</TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {item.location === "STORE" ? <Store className="h-3 w-3 mr-1" /> : <Warehouse className="h-3 w-3 mr-1" />}
                        {item.location}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      {item.expiryDate ? format(new Date(item.expiryDate), "MMM dd, yyyy") : "N/A"}
                    </TableCell>
                    <TableCell className="text-right">
                      {item.daysToExpiry !== null ? item.daysToExpiry : "N/A"}
                    </TableCell>
                    <TableCell className="text-right">{item.quantity}</TableCell>
                    <TableCell className="text-right">{formatCurrency(item.value)}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant={getRiskLevelBadgeVariant(item.riskLevel)}>
                        {item.riskLevel}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function InventoryValuationReportSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array(4).fill(0).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {Array(4).fill(0).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array(5).fill(0).map((_, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
