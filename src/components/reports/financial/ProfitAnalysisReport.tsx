"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  CalendarIcon, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  Package,
  Building2,
  BarChart3,
  PieChart,
  Target
} from "lucide-react";
import { format } from "date-fns";
import { cn, formatCurrency } from "@/lib/utils";
import { ProfitMarginAnalysis } from "@/app/api/reports/financial/profit-analysis/route";

interface ProfitAnalysisReportComponentProps {
  dateRange: { from: Date; to: Date };
  onDateRangeChange: (range: { from: Date; to: Date }) => void;
}

export function ProfitAnalysisReportComponent({ dateRange, onDateRangeChange }: ProfitAnalysisReportComponentProps) {
  const [reportData, setReportData] = useState<ProfitMarginAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReport = async (from: Date, to: Date) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(
        `/api/reports/financial/profit-analysis?startDate=${format(from, "yyyy-MM-dd")}&endDate=${format(to, "yyyy-MM-dd")}`
      );
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch profit analysis report");
      }
      
      setReportData(result.data);
    } catch (err) {
      console.error("Error fetching profit analysis report:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReport(dateRange.from, dateRange.to);
  }, [dateRange]);

  const getProfitMarginColor = (margin: number) => {
    if (margin >= 30) return "text-green-600";
    if (margin >= 15) return "text-blue-600";
    if (margin >= 5) return "text-yellow-600";
    if (margin >= 0) return "text-orange-600";
    return "text-red-600";
  };

  const getProfitMarginBadgeVariant = (margin: number): "default" | "secondary" | "destructive" => {
    if (margin >= 20) return "default";
    if (margin >= 10) return "secondary";
    return "destructive";
  };

  if (isLoading) {
    return <ProfitAnalysisReportSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profit Analysis Report</CardTitle>
          <CardDescription>Error loading report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => fetchReport(dateRange.from, dateRange.to)}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!reportData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Profit Analysis Report</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Profit Analysis Report
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" className="w-[300px] justify-start text-left font-normal">
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  initialFocus
                  mode="range"
                  defaultMonth={dateRange.from}
                  selected={dateRange}
                  onSelect={(range) => {
                    if (range?.from && range?.to) {
                      onDateRangeChange({ from: range.from, to: range.to });
                    }
                  }}
                  numberOfMonths={2}
                />
              </PopoverContent>
            </Popover>
          </CardTitle>
          <CardDescription>
            Comprehensive profit margin analysis from {reportData.period.start} to {reportData.period.end}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Overall Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.overallMetrics.totalRevenue)}</div>
            <div className="text-xs text-muted-foreground">
              Cost: {formatCurrency(reportData.overallMetrics.totalCost)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.overallMetrics.totalProfit)}</div>
            <div className={cn("text-xs font-medium", getProfitMarginColor(reportData.overallMetrics.overallProfitMargin))}>
              {reportData.overallMetrics.overallProfitMargin.toFixed(1)}% margin
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Profit/Transaction</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.overallMetrics.averageProfitPerTransaction)}</div>
            <div className="text-xs text-muted-foreground">
              Per item: {formatCurrency(reportData.overallMetrics.averageProfitPerItem)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Profit Margin Distribution */}
      {reportData.profitMarginDistribution && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <PieChart className="h-5 w-5" />
              Profit Margin Distribution
            </CardTitle>
            <CardDescription>
              Distribution of products by profit margin ranges
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className="bg-green-100 text-green-800">High (≥30%)</Badge>
                  <span className="text-sm">{reportData.profitMarginDistribution.highMargin.count} products</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">{reportData.profitMarginDistribution.highMargin.percentage.toFixed(1)}%</div>
                </div>
              </div>
              <Progress value={reportData.profitMarginDistribution.highMargin.percentage} className="h-2" />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className="bg-blue-100 text-blue-800">Medium (15-30%)</Badge>
                  <span className="text-sm">{reportData.profitMarginDistribution.mediumMargin.count} products</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">{reportData.profitMarginDistribution.mediumMargin.percentage.toFixed(1)}%</div>
                </div>
              </div>
              <Progress value={reportData.profitMarginDistribution.mediumMargin.percentage} className="h-2" />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className="bg-yellow-100 text-yellow-800">Low (5-15%)</Badge>
                  <span className="text-sm">{reportData.profitMarginDistribution.lowMargin.count} products</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">{reportData.profitMarginDistribution.lowMargin.percentage.toFixed(1)}%</div>
                </div>
              </div>
              <Progress value={reportData.profitMarginDistribution.lowMargin.percentage} className="h-2" />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className="bg-orange-100 text-orange-800">Very Low (&lt;5%)</Badge>
                  <span className="text-sm">{reportData.profitMarginDistribution.veryLowMargin.count} products</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">{reportData.profitMarginDistribution.veryLowMargin.percentage.toFixed(1)}%</div>
                </div>
              </div>
              <Progress value={reportData.profitMarginDistribution.veryLowMargin.percentage} className="h-2" />

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Badge className="bg-red-100 text-red-800">Negative (&lt;0%)</Badge>
                  <span className="text-sm">{reportData.profitMarginDistribution.negativemargin.count} products</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">{reportData.profitMarginDistribution.negativemargin.percentage.toFixed(1)}%</div>
                </div>
              </div>
              <Progress value={reportData.profitMarginDistribution.negativemargin.percentage} className="h-2" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Category Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Category Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Products</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead className="text-right">Cost</TableHead>
                <TableHead className="text-right">Profit</TableHead>
                <TableHead className="text-right">Margin</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.categoryAnalysis.map((category) => (
                <TableRow key={category.categoryId}>
                  <TableCell className="font-medium">{category.categoryName}</TableCell>
                  <TableCell className="text-right">{category.productCount}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.totalRevenue)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.totalCost)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.totalProfit)}</TableCell>
                  <TableCell className="text-right">
                    <Badge variant={getProfitMarginBadgeVariant(category.profitMargin)}>
                      {category.profitMargin.toFixed(1)}%
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Supplier Analysis */}
      {reportData.supplierAnalysis && reportData.supplierAnalysis.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Supplier Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Supplier</TableHead>
                  <TableHead className="text-right">Products</TableHead>
                  <TableHead className="text-right">Revenue</TableHead>
                  <TableHead className="text-right">Cost</TableHead>
                  <TableHead className="text-right">Profit</TableHead>
                  <TableHead className="text-right">Margin</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportData.supplierAnalysis.map((supplier) => (
                  <TableRow key={supplier.supplierId}>
                    <TableCell className="font-medium">{supplier.supplierName}</TableCell>
                    <TableCell className="text-right">{supplier.productCount}</TableCell>
                    <TableCell className="text-right">{formatCurrency(supplier.totalRevenue)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(supplier.totalCost)}</TableCell>
                    <TableCell className="text-right">{formatCurrency(supplier.totalProfit)}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant={getProfitMarginBadgeVariant(supplier.profitMargin)}>
                        {supplier.profitMargin.toFixed(1)}%
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Top Profitable Products */}
      {reportData.topProfitableProducts && reportData.topProfitableProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Most Profitable Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead className="text-right">Qty Sold</TableHead>
                  <TableHead className="text-right">Total Profit</TableHead>
                  <TableHead className="text-right">Margin</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportData.topProfitableProducts.map((product) => (
                  <TableRow key={product.productId}>
                    <TableCell className="font-medium">{product.productName}</TableCell>
                    <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                    <TableCell className="text-right">{product.quantitySold}</TableCell>
                    <TableCell className="text-right">{formatCurrency(product.totalProfit)}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant={getProfitMarginBadgeVariant(product.profitMargin)}>
                        {product.profitMargin.toFixed(1)}%
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Least Profitable Products */}
      {reportData.leastProfitableProducts && reportData.leastProfitableProducts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingDown className="h-5 w-5" />
              Least Profitable Products
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>SKU</TableHead>
                  <TableHead className="text-right">Qty Sold</TableHead>
                  <TableHead className="text-right">Total Profit</TableHead>
                  <TableHead className="text-right">Margin</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {reportData.leastProfitableProducts.map((product) => (
                  <TableRow key={product.productId}>
                    <TableCell className="font-medium">{product.productName}</TableCell>
                    <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                    <TableCell className="text-right">{product.quantitySold}</TableCell>
                    <TableCell className="text-right">{formatCurrency(product.totalProfit)}</TableCell>
                    <TableCell className="text-right">
                      <Badge variant={getProfitMarginBadgeVariant(product.profitMargin)}>
                        {product.profitMargin.toFixed(1)}%
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ProfitAnalysisReportSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array(3).fill(0).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {Array(3).fill(0).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array(5).fill(0).map((_, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
