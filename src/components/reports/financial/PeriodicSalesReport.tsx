"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  CalendarIcon, 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  ShoppingCart,
  Package,
  Users,
  BarChart3,
  ArrowUpIcon,
  ArrowDownIcon,
  MinusIcon
} from "lucide-react";
import { format } from "date-fns";
import { cn, formatCurrency } from "@/lib/utils";
import { PeriodicSalesReport } from "@/app/api/reports/financial/periodic/route";

interface PeriodicSalesReportComponentProps {
  selectedDate: Date;
  periodicType: "weekly" | "monthly";
  onDateChange: (date: Date) => void;
  onPeriodicTypeChange: (type: "weekly" | "monthly") => void;
}

export function PeriodicSalesReportComponent({ 
  selectedDate, 
  periodicType, 
  onDateChange, 
  onPeriodicTypeChange 
}: PeriodicSalesReportComponentProps) {
  const [reportData, setReportData] = useState<PeriodicSalesReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReport = async (date: Date, type: "weekly" | "monthly") => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await fetch(`/api/reports/financial/periodic?type=${type}&date=${format(date, "yyyy-MM-dd")}`);
      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || "Failed to fetch periodic sales report");
      }
      
      setReportData(result.data);
    } catch (err) {
      console.error("Error fetching periodic sales report:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchReport(selectedDate, periodicType);
  }, [selectedDate, periodicType]);

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      onDateChange(date);
    }
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <ArrowUpIcon className="h-4 w-4 text-green-600" />;
    if (change < 0) return <ArrowDownIcon className="h-4 w-4 text-red-600" />;
    return <MinusIcon className="h-4 w-4 text-gray-400" />;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return "text-green-600";
    if (change < 0) return "text-red-600";
    return "text-gray-400";
  };

  if (isLoading) {
    return <PeriodicSalesReportSkeleton />;
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Periodic Sales Report</CardTitle>
          <CardDescription>Error loading report</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={() => fetchReport(selectedDate, periodicType)}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!reportData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Periodic Sales Report</CardTitle>
          <CardDescription>No data available</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            {periodicType.charAt(0).toUpperCase() + periodicType.slice(1)} Sales Report
            <div className="flex items-center gap-2">
              <Select value={periodicType} onValueChange={onPeriodicTypeChange}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="weekly">Weekly</SelectItem>
                  <SelectItem value="monthly">Monthly</SelectItem>
                </SelectContent>
              </Select>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {format(selectedDate, "PPP")}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={handleDateSelect}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </CardTitle>
          <CardDescription>
            {reportData.period.label} ({reportData.period.start} to {reportData.period.end})
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalRevenue)}</div>
            <div className={cn("text-xs flex items-center gap-1", getChangeColor(reportData.periodComparison.revenueChange))}>
              {getChangeIcon(reportData.periodComparison.revenueChange)}
              {Math.abs(reportData.periodComparison.revenueChange).toFixed(1)}% vs previous {periodicType.slice(0, -2)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.totalProfit)}</div>
            <div className={cn("text-xs flex items-center gap-1", getChangeColor(reportData.periodComparison.profitChange))}>
              {getChangeIcon(reportData.periodComparison.profitChange)}
              {Math.abs(reportData.periodComparison.profitChange).toFixed(1)}% vs previous {periodicType.slice(0, -2)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Transactions</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reportData.summary.totalTransactions}</div>
            <div className={cn("text-xs flex items-center gap-1", getChangeColor(reportData.periodComparison.transactionChange))}>
              {getChangeIcon(reportData.periodComparison.transactionChange)}
              {Math.abs(reportData.periodComparison.transactionChange).toFixed(1)}% vs previous {periodicType.slice(0, -2)}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Daily Revenue</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(reportData.summary.averageDailyRevenue)}</div>
            <div className="text-xs text-muted-foreground">
              {reportData.summary.profitMargin.toFixed(1)}% profit margin
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Daily Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead className="text-right">Profit</TableHead>
                <TableHead className="text-right">Transactions</TableHead>
                <TableHead className="text-right">Avg Order Value</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.dailyBreakdown.map((day) => (
                <TableRow key={day.date}>
                  <TableCell className="font-medium">{format(new Date(day.date), "MMM dd, yyyy")}</TableCell>
                  <TableCell className="text-right">{formatCurrency(day.revenue)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(day.profit)}</TableCell>
                  <TableCell className="text-right">{day.transactions}</TableCell>
                  <TableCell className="text-right">{formatCurrency(day.averageOrderValue)}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Category Performance */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Category Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead className="text-right">Profit</TableHead>
                <TableHead className="text-right">Transactions</TableHead>
                <TableHead className="text-right">Profit Margin</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.categoryPerformance.map((category) => (
                <TableRow key={category.categoryId}>
                  <TableCell className="font-medium">{category.categoryName}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.revenue)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(category.profit)}</TableCell>
                  <TableCell className="text-right">{category.transactions}</TableCell>
                  <TableCell className="text-right">
                    <Badge variant={category.profitMargin > 20 ? "default" : category.profitMargin > 10 ? "secondary" : "destructive"}>
                      {category.profitMargin.toFixed(1)}%
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Top Performing Products */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Top Performing Products
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Product</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead className="text-right">Qty Sold</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead className="text-right">Profit</TableHead>
                <TableHead className="text-right">Margin</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.topPerformingProducts.slice(0, 10).map((product) => (
                <TableRow key={product.productId}>
                  <TableCell className="font-medium">{product.productName}</TableCell>
                  <TableCell className="text-muted-foreground">{product.sku}</TableCell>
                  <TableCell className="text-right">{product.quantitySold}</TableCell>
                  <TableCell className="text-right">{formatCurrency(product.revenue)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(product.profit)}</TableCell>
                  <TableCell className="text-right">
                    <Badge variant={product.profitMargin > 20 ? "default" : product.profitMargin > 10 ? "secondary" : "destructive"}>
                      {product.profitMargin.toFixed(1)}%
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Cashier Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Cashier Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Cashier</TableHead>
                <TableHead className="text-right">Transactions</TableHead>
                <TableHead className="text-right">Revenue</TableHead>
                <TableHead className="text-right">Avg Order Value</TableHead>
                <TableHead className="text-right">Working Days</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportData.cashierSummary.map((cashier) => (
                <TableRow key={cashier.cashierId}>
                  <TableCell className="font-medium">{cashier.cashierName}</TableCell>
                  <TableCell className="text-right">{cashier.totalTransactions}</TableCell>
                  <TableCell className="text-right">{formatCurrency(cashier.totalRevenue)}</TableCell>
                  <TableCell className="text-right">{formatCurrency(cashier.averageOrderValue)}</TableCell>
                  <TableCell className="text-right">{cashier.workingDays}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}

function PeriodicSalesReportSkeleton() {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </CardHeader>
      </Card>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array(4).fill(0).map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-32 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      {Array(4).fill(0).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-6 w-48" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array(5).fill(0).map((_, j) => (
                <Skeleton key={j} className="h-4 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
