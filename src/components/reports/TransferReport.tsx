"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";
import { 
  ArrowRightLeft, 
  Package, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  TrendingUp,
  Users,
  ArrowUpDown
} from "lucide-react";

interface TransferReportData {
  reportType: string;
  generatedAt: string;
  filters: {
    startDate?: string;
    endDate?: string;
    supplierId?: string;
    status?: string;
    productId?: string;
  };
  summary: {
    totalTransfers: number;
    completedTransfers: number;
    pendingTransfers: number;
    approvedTransfers: number;
    completionRate: number;
  };
  supplierBreakdown: Array<{
    supplierId: string | null;
    supplierName: string;
    contactPerson?: string;
    transferCount: number;
    totalQuantity: number;
    completedCount: number;
    pendingCount: number;
    approvedCount: number;
  }>;
  transfers: Array<{
    id: string;
    date: string;
    productName: string;
    productSku: string;
    category: string;
    unit: string;
    quantity: number;
    fromStore: boolean;
    toStore: boolean;
    status: string;
    notes?: string;
    requestedBy: {
      name: string;
      role: string;
    };
    approvedBy?: {
      name: string;
      role: string;
    };
    completedAt?: string;
    suppliers: Array<{
      id: string;
      name: string;
      contactPerson?: string;
      isPreferred: boolean;
      purchasePrice: number;
    }>;
  }>;
}

interface TransferReportProps {
  data: TransferReportData;
}

export function TransferReport({ data }: TransferReportProps) {
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  const { summary, supplierBreakdown, transfers, generatedAt } = data;

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const renderSortIndicator = (field: string) => {
    if (sortField !== field) {
      return <ArrowUpDown className="ml-1 h-3 w-3 text-muted-foreground" />;
    }
    return (
      <ArrowUpDown 
        className={`ml-1 h-3 w-3 ${
          sortDirection === "asc" ? "rotate-180" : ""
        }`} 
      />
    );
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case "APPROVED":
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Approved</Badge>;
      case "PENDING":
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getTransferDirection = (fromStore: boolean, toStore: boolean) => {
    if (fromStore && toStore) return "Store → Store";
    if (fromStore && !toStore) return "Store → Warehouse";
    if (!fromStore && toStore) return "Warehouse → Store";
    return "Warehouse → Warehouse";
  };

  // Sort transfers based on current sort settings
  const sortedTransfers = [...transfers].sort((a, b) => {
    if (!sortField) return 0;

    let aValue: any = a[sortField as keyof typeof a];
    let bValue: any = b[sortField as keyof typeof b];

    // Handle nested properties
    if (sortField === "requestedBy") {
      aValue = a.requestedBy.name;
      bValue = b.requestedBy.name;
    }

    // Handle numeric values
    if (typeof aValue === "number" && typeof bValue === "number") {
      return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
    }

    // Handle string values
    const aString = String(aValue || "").toLowerCase();
    const bString = String(bValue || "").toLowerCase();

    return sortDirection === "asc"
      ? aString.localeCompare(bString)
      : bString.localeCompare(aString);
  });

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transfers</CardTitle>
            <ArrowRightLeft className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalTransfers}</div>
            <p className="text-xs text-muted-foreground">
              Generated {format(new Date(generatedAt), "MMM dd, yyyy 'at' HH:mm")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.completedTransfers}</div>
            <p className="text-xs text-muted-foreground">
              {summary.completionRate.toFixed(1)}% completion rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{summary.pendingTransfers}</div>
            <p className="text-xs text-muted-foreground">Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <AlertCircle className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{summary.approvedTransfers}</div>
            <p className="text-xs text-muted-foreground">Ready for completion</p>
          </CardContent>
        </Card>
      </div>

      {/* Supplier Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Supplier Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Contact Person</TableHead>
                <TableHead className="text-right">Total Transfers</TableHead>
                <TableHead className="text-right">Total Quantity</TableHead>
                <TableHead className="text-right">Completed</TableHead>
                <TableHead className="text-right">Pending</TableHead>
                <TableHead className="text-right">Approved</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {supplierBreakdown.map((supplier, index) => (
                <TableRow key={supplier.supplierId || index}>
                  <TableCell className="font-medium">{supplier.supplierName}</TableCell>
                  <TableCell>{supplier.contactPerson || "-"}</TableCell>
                  <TableCell className="text-right">{supplier.transferCount}</TableCell>
                  <TableCell className="text-right">{supplier.totalQuantity}</TableCell>
                  <TableCell className="text-right text-green-600">{supplier.completedCount}</TableCell>
                  <TableCell className="text-right text-yellow-600">{supplier.pendingCount}</TableCell>
                  <TableCell className="text-right text-blue-600">{supplier.approvedCount}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Detailed Transfers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Transfer Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("date")}
                    className="p-0 h-auto font-semibold"
                  >
                    Date {renderSortIndicator("date")}
                  </Button>
                </TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("productName")}
                    className="p-0 h-auto font-semibold"
                  >
                    Product {renderSortIndicator("productName")}
                  </Button>
                </TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Suppliers</TableHead>
                <TableHead className="text-right">
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("quantity")}
                    className="p-0 h-auto font-semibold flex items-center justify-end ml-auto"
                  >
                    Quantity {renderSortIndicator("quantity")}
                  </Button>
                </TableHead>
                <TableHead>Direction</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>
                  <Button
                    variant="ghost"
                    onClick={() => handleSort("requestedBy")}
                    className="p-0 h-auto font-semibold"
                  >
                    Requested By {renderSortIndicator("requestedBy")}
                  </Button>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedTransfers.map((transfer) => (
                <TableRow key={transfer.id}>
                  <TableCell>
                    {format(new Date(transfer.date), "MMM dd, yyyy")}
                  </TableCell>
                  <TableCell className="font-medium">{transfer.productName}</TableCell>
                  <TableCell>{transfer.productSku}</TableCell>
                  <TableCell>{transfer.category}</TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {transfer.suppliers.length === 0 ? (
                        <Badge variant="outline">No Supplier</Badge>
                      ) : (
                        transfer.suppliers.map((supplier) => (
                          <div key={supplier.id} className="flex items-center gap-1">
                            <Badge
                              variant={supplier.isPreferred ? "default" : "secondary"}
                              className="text-xs"
                            >
                              {supplier.name}
                            </Badge>
                            {supplier.isPreferred && (
                              <span className="text-xs text-muted-foreground">(Preferred)</span>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    {transfer.quantity} {transfer.unit}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {getTransferDirection(transfer.fromStore, transfer.toStore)}
                    </Badge>
                  </TableCell>
                  <TableCell>{getStatusBadge(transfer.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{transfer.requestedBy.name}</div>
                      <div className="text-xs text-muted-foreground">{transfer.requestedBy.role}</div>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
