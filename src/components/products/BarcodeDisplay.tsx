"use client";

import { useEffect, useRef } from "react";
import JsBarcode from "jsbarcode";

interface BarcodeDisplayProps {
  value: string;
  width?: number;
  height?: number;
  displayValue?: boolean;
  fontSize?: number;
  margin?: number;
}

export function BarcodeDisplay({
  value,
  width = 2,
  height = 50,
  displayValue = true,
  fontSize = 12,
  margin = 5,
}: BarcodeDisplayProps) {
  const barcodeRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (barcodeRef.current && value) {
      try {
        JsBarcode(barcodeRef.current, value, {
          format: "EAN13",
          width,
          height,
          displayValue,
          fontSize,
          margin,
          valid: (valid) => {
            if (!valid) {
              console.warn(`Invalid barcode value: ${value}`);
            }
          },
        });
      } catch (error) {
        console.error("Error generating barcode:", error);
      }
    }
  }, [value, width, height, displayValue, fontSize, margin]);

  if (!value) {
    return null;
  }

  return <svg ref={barcodeRef} className="w-full"></svg>;
}
