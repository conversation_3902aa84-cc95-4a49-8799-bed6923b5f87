"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  AlertCircle,
  FileSpreadsheet,
  CheckCircle,
  X,
  FileText,
} from "lucide-react";

export function ImportExportButtons() {
  const [importDialogOpen, setImportDialogOpen] = useState(false);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [importFile, setImportFile] = useState<File | null>(null);
  const [importMode, setImportMode] = useState("upsert");
  const [exportFormat, setExportFormat] = useState("csv");
  const [isLoading, setIsLoading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<{
    success?: boolean;
    message?: string;
    error?: string;
    details?: any[];
  }>({});

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setImportFile(e.target.files[0]);
      // Reset result when a new file is selected
      setResult({});
    }
  };

  // Handle import submission
  const handleImport = async () => {
    if (!importFile) return;

    setIsLoading(true);
    setProgress(10);
    setResult({});

    try {
      const formData = new FormData();
      formData.append("file", importFile);

      setProgress(30);

      const response = await fetch(`/api/products/import?mode=${importMode}`, {
        method: "POST",
        body: formData,
      });

      setProgress(90);

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to import products");
      }

      setProgress(100);
      setResult({
        success: true,
        message: data.message,
        details: data.details,
      });
    } catch (error) {
      setResult({
        success: false,
        error: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle export
  const handleExport = () => {
    // Use window.open to trigger file download
    window.open(`/api/products/export?format=${exportFormat}`, "_blank");
    setExportDialogOpen(false);
  };

  // Template format state (only xlsx or csv, no pdf for templates)
  const [templateFormat, setTemplateFormat] = useState("xlsx");

  // Download template
  const handleDownloadTemplate = () => {
    window.open(`/api/products/template?format=${templateFormat}`, "_blank");
  };

  // Reset import form
  const resetImportForm = () => {
    setImportFile(null);
    setResult({});
    setProgress(0);

    // Reset file input by recreating it
    const fileInput = document.getElementById("file-input") as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  return (
    <div className="flex gap-2">
      {/* Export Button */}
      <Dialog open={exportDialogOpen} onOpenChange={setExportDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Products</DialogTitle>
            <DialogDescription>
              Export your product data to a file format of your choice.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="format">File Format</Label>
              <Select value={exportFormat} onValueChange={setExportFormat}>
                <SelectTrigger id="format">
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV (Comma Separated Values)</SelectItem>
                  <SelectItem value="xlsx">Excel Spreadsheet (XLSX)</SelectItem>
                  <SelectItem value="pdf">PDF Document</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setExportDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport}>
              <Download className="mr-2 h-4 w-4" />
              Export Products
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Button */}
      <Dialog
        open={importDialogOpen}
        onOpenChange={(open) => {
          setImportDialogOpen(open);
          if (!open) {
            resetImportForm();
          }
        }}
      >
        <DialogTrigger asChild>
          <Button variant="outline" className="gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Import Products</DialogTitle>
            <DialogDescription>
              Upload a CSV or Excel file to import products.
              <Dialog>
                <DialogTrigger asChild>
                  <Button variant="link" className="h-auto p-0 text-xs">
                    Download template
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px]">
                  <DialogHeader>
                    <DialogTitle>Download Template</DialogTitle>
                    <DialogDescription>
                      Select a format to download the product import template.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid gap-2">
                      <Label htmlFor="template-format">File Format</Label>
                      <Select value={templateFormat} onValueChange={setTemplateFormat}>
                        <SelectTrigger id="template-format">
                          <SelectValue placeholder="Select format" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="csv">CSV (Comma Separated Values)</SelectItem>
                          <SelectItem value="xlsx">Excel Spreadsheet (XLSX)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleDownloadTemplate}>
                      <Download className="mr-2 h-4 w-4" />
                      Download Template
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            {!result.success && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="file">File</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="file-input"
                      type="file"
                      accept=".csv,.xlsx,.xls"
                      onChange={handleFileChange}
                      disabled={isLoading}
                    />
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm" title="Download template">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[425px]">
                        <DialogHeader>
                          <DialogTitle>Download Template</DialogTitle>
                          <DialogDescription>
                            Select a format to download the product import template.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="grid gap-4 py-4">
                          <div className="grid gap-2">
                            <Label htmlFor="template-format-2">File Format</Label>
                            <Select value={templateFormat} onValueChange={setTemplateFormat}>
                              <SelectTrigger id="template-format-2">
                                <SelectValue placeholder="Select format" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="csv">CSV (Comma Separated Values)</SelectItem>
                                <SelectItem value="xlsx">Excel Spreadsheet (XLSX)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <DialogFooter>
                          <Button onClick={handleDownloadTemplate}>
                            <Download className="mr-2 h-4 w-4" />
                            Download Template
                          </Button>
                        </DialogFooter>
                      </DialogContent>
                    </Dialog>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Supported formats: CSV, Excel (.xlsx, .xls)
                  </p>
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="mode">Import Mode</Label>
                  <Select value={importMode} onValueChange={setImportMode} disabled={isLoading}>
                    <SelectTrigger id="mode">
                      <SelectValue placeholder="Select mode" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="upsert">Update existing & create new (Upsert)</SelectItem>
                      <SelectItem value="create">Create new only</SelectItem>
                      <SelectItem value="update">Update existing only</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-muted-foreground">
                    {importMode === "upsert" &&
                      "Products will be updated if they exist (by SKU), or created if they don't."}
                    {importMode === "create" &&
                      "Only new products will be created. Existing SKUs will be skipped."}
                    {importMode === "update" &&
                      "Only existing products will be updated. New SKUs will be skipped."}
                  </p>
                </div>
              </>
            )}

            {isLoading && (
              <div className="space-y-2">
                <Progress value={progress} />
                <p className="text-sm text-center text-muted-foreground">
                  {progress < 30 && "Preparing import..."}
                  {progress >= 30 && progress < 90 && "Processing file..."}
                  {progress >= 90 && "Finalizing import..."}
                </p>
              </div>
            )}

            {result.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{result.error}</AlertDescription>
              </Alert>
            )}

            {result.success && (
              <div className="space-y-4">
                <Alert variant="default" className="border-green-500 bg-green-50 text-green-800">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertDescription>{result.message}</AlertDescription>
                </Alert>

                {result.details && result.details.length > 0 && (
                  <div className="max-h-[200px] overflow-y-auto border rounded-md p-2">
                    <p className="text-sm font-medium mb-2">Import Details:</p>
                    <ul className="space-y-1 text-sm">
                      {result.details.slice(0, 10).map((detail, index) => (
                        <li key={index} className="flex items-center gap-2">
                          {detail.status === "created" && (
                            <span className="text-green-500 flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" /> Created:
                            </span>
                          )}
                          {detail.status === "updated" && (
                            <span className="text-blue-500 flex items-center gap-1">
                              <CheckCircle className="h-3 w-3" /> Updated:
                            </span>
                          )}
                          {detail.status === "error" && (
                            <span className="text-red-500 flex items-center gap-1">
                              <X className="h-3 w-3" /> Error:
                            </span>
                          )}
                          <span>{detail.sku}</span>
                          {detail.error && <span className="text-red-500">- {detail.error}</span>}
                        </li>
                      ))}
                      {result.details.length > 10 && (
                        <li className="text-muted-foreground">
                          ...and {result.details.length - 10} more
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>

          <DialogFooter>
            {!result.success ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => setImportDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button onClick={handleImport} disabled={!importFile || isLoading}>
                  <Upload className="mr-2 h-4 w-4" />
                  Import Products
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" onClick={resetImportForm}>
                  Import Another File
                </Button>
                <Button onClick={() => setImportDialogOpen(false)}>Close</Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
