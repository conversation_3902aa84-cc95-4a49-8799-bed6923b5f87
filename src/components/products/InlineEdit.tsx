"use client";

import { useState, useRef, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Loader2, ChevronDown } from "lucide-react";

type FieldType = "text" | "dropdown";

interface InlineEditProps {
  type: FieldType;
  value: string;
  options?: { id: string; name: string; abbreviation?: string }[];
  onSave: (value: string) => Promise<void>;
  displayValue?: string;
  placeholder?: string;
}

export function InlineEdit({
  type,
  value,
  options = [],
  onSave,
  displayValue,
  placeholder = "Enter value...",
}: InlineEditProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<"bottom" | "top">("bottom");
  const inputRef = useRef<HTMLInputElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Set up click outside listener for text fields
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
        if (isEditing && !isSaving && type === "text") {
          if (editValue !== value) {
            // For text fields, save on blur if value has changed
            handleSave();
          } else {
            // Otherwise just cancel editing
            setIsEditing(false);
            setEditValue(value); // Reset to original value
            setError(null);
          }
        }
      }

      // Close dropdown when clicking outside
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isEditing, isSaving, value, editValue, type]);

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current && type === "text") {
      inputRef.current.focus();
    }
  }, [isEditing, type]);

  // Check dropdown position when it opens
  useEffect(() => {
    if (isDropdownOpen && dropdownRef.current) {
      const updatePosition = () => {
        const rect = dropdownRef.current?.getBoundingClientRect();
        if (rect) {
          const spaceBelow = window.innerHeight - rect.bottom;
          setDropdownPosition(spaceBelow < 200 ? "top" : "bottom");
        }
      };

      // Update position initially and on scroll/resize
      updatePosition();
      window.addEventListener("scroll", updatePosition);
      window.addEventListener("resize", updatePosition);

      return () => {
        window.removeEventListener("scroll", updatePosition);
        window.removeEventListener("resize", updatePosition);
      };
    }
  }, [isDropdownOpen]);

  const handleEdit = () => {
    if (type === "text") {
      setIsEditing(true);
      setEditValue(value);
      setError(null);
    } else if (type === "dropdown") {
      setIsEditing(true);
      setIsDropdownOpen(!isDropdownOpen);
    }
  };

  const handleSave = async () => {
    // Don't save if value hasn't changed
    if (editValue === value) {
      setIsEditing(false);
      setIsDropdownOpen(false);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      await onSave(editValue);
      setIsEditing(false);
      setIsDropdownOpen(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to save");
    } finally {
      setIsSaving(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      setIsEditing(false);
      setEditValue(value); // Reset to original value
      setError(null);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditValue(e.target.value);
  };

  const handleOptionSelect = async (optionId: string) => {
    if (optionId !== value) {
      setEditValue(optionId);
      setIsSaving(true);
      setError(null);

      try {
        await onSave(optionId);
        setIsEditing(false);
        setIsDropdownOpen(false);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to save");
      } finally {
        setIsSaving(false);
      }
    } else {
      setIsDropdownOpen(false);
    }
  };

  // Display value (for dropdowns where we need to show a different value than the ID)
  const displayText = displayValue || value;

  // Find the selected option for dropdown display
  const selectedOption = type === "dropdown" ? options.find((option) => option.id === value) : null;

  return (
    <div ref={wrapperRef} className="relative group">
      {isEditing && type === "text" ? (
        <div className="flex items-center">
          <div className="flex-1">
            <Input
              ref={inputRef}
              type="text"
              value={editValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onBlur={() => {
                if (editValue !== value) {
                  handleSave();
                }
              }}
              placeholder={placeholder}
              className="w-full py-1 px-2 text-sm"
              disabled={isSaving}
            />
            {error && <div className="text-xs text-red-500 mt-1">{error}</div>}
          </div>

          {isSaving && (
            <div className="flex items-center ml-2">
              <Loader2 className="h-4 w-4 animate-spin text-primary" />
            </div>
          )}
        </div>
      ) : (
        <div className="relative">
          <div
            onClick={handleEdit}
            className="cursor-pointer py-1 px-2 rounded hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors flex items-center justify-between"
          >
            <span className="inline-block min-w-[40px]">
              {type === "dropdown"
                ? selectedOption
                  ? `${selectedOption.name}${selectedOption.abbreviation ? ` (${selectedOption.abbreviation})` : ""}`
                  : displayText || <span className="text-gray-400 italic">Empty</span>
                : displayText || <span className="text-gray-400 italic">Empty</span>}
            </span>
            {type === "dropdown" && <ChevronDown className="h-4 w-4 ml-1 opacity-50" />}
            {isSaving && (
              <Loader2 className="inline-block ml-2 h-3 w-3 animate-spin text-primary" />
            )}
          </div>

          {/* Custom dropdown */}
          {type === "dropdown" && isDropdownOpen && (
            <div
              ref={dropdownRef}
              className={`absolute z-50 w-full bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 max-h-60 overflow-y-auto ${
                dropdownPosition === "bottom" ? "mt-1 top-full" : "mb-1 bottom-full"
              }`}
            >
              {options.map((option) => (
                <div
                  key={option.id}
                  className={`px-2 py-1.5 text-sm cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ${option.id === value ? "bg-gray-100 dark:bg-gray-700" : ""}`}
                  onClick={() => handleOptionSelect(option.id)}
                >
                  {option.name} {option.abbreviation ? `(${option.abbreviation})` : ""}
                </div>
              ))}
              {options.length === 0 && (
                <div className="px-2 py-1.5 text-sm text-gray-500">No options available</div>
              )}
            </div>
          )}

          {error && <div className="text-xs text-red-500 mt-1">{error}</div>}
        </div>
      )}
    </div>
  );
}
