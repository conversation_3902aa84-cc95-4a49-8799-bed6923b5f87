"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ShoppingCart } from "lucide-react";
import { usePOSCart } from "@/contexts/POSCartContext";

interface OrderSummaryProps {
  onProceedToPayment: () => void;
  disabled?: boolean;
}

export function OrderSummary({ onProceedToPayment, disabled = false }: OrderSummaryProps) {
  const { cartItems, subtotal, totalDiscount, total, itemCount } = usePOSCart();

  const canProceed = cartItems.length > 0 && !disabled;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <ShoppingCart className="h-5 w-5" />
          <span>Order Summary</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Order Details */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Items:</span>
              <span className="font-medium">{itemCount} unique items</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Subtotal:</span>
              <span className="font-medium">Rp {Number(subtotal).toLocaleString("id-ID")}</span>
            </div>
            <div className="flex justify-between text-green-600">
              <span className="text-sm">Discount:</span>
              <span className="font-medium">
                -Rp {Number(totalDiscount).toLocaleString("id-ID")}
              </span>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between text-lg font-bold">
                <span>Total:</span>
                <span>Rp {Number(total).toLocaleString("id-ID")}</span>
              </div>
            </div>
          </div>

          {/* Proceed Button */}
          <Button onClick={onProceedToPayment} disabled={!canProceed} className="w-full" size="lg">
            {cartItems.length === 0
              ? "Add items to proceed"
              : disabled
                ? "Cannot proceed - check drawer status"
                : "Proceed to Payment"}
          </Button>

          {disabled && (
            <p className="text-sm text-red-600 text-center">
              Please check your drawer status before proceeding
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
