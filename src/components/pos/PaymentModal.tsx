"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CreditCard, DollarSign, Smartphone } from "lucide-react";
import { usePOSCart } from "@/contexts/POSCartContext";
import { usePOS } from "@/contexts/POSContext";
import { toast } from "sonner";

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedCustomer: any;
}

export function PaymentModal({ isOpen, onClose, selectedCustomer }: PaymentModalProps) {
  const { cartItems, total, subtotal, totalDiscount, clearCart } = usePOSCart();
  const { focusSearchInput } = usePOS();
  const [paymentMethod, setPaymentMethod] = useState<"CASH" | "DEBIT" | "QRIS">("CASH");
  const [paymentStatus, setPaymentStatus] = useState<"PAID" | "PARTIAL" | "PENDING">("PAID");
  const [amountReceived, setAmountReceived] = useState<string>("");
  const [notes, setNotes] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const amountInputRef = useRef<HTMLInputElement>(null);

  const amountReceivedNum = parseFloat(amountReceived) || 0;
  const change = amountReceivedNum - total;
  const isValidCashPayment = paymentMethod !== "CASH" || amountReceivedNum >= total;

  // Auto-focus amount input when cash payment is selected and modal opens
  useEffect(() => {
    if (isOpen && paymentMethod === "CASH") {
      console.log(
        "PaymentModal: Modal opened with CASH payment method, attempting to focus amount input"
      );

      // Use a more robust approach to ensure the input field is rendered and focusable
      const attemptFocus = (attempts = 0) => {
        if (attempts > 10) {
          console.log("PaymentModal: Failed to focus after 10 attempts, giving up");
          return;
        }

        if (amountInputRef.current) {
          try {
            amountInputRef.current.focus();
            console.log(
              `PaymentModal: Successfully focused cash amount input field (attempt ${attempts + 1})`
            );
          } catch (error) {
            console.error("PaymentModal: Error focusing input field:", error);
          }
        } else {
          console.log(
            `PaymentModal: Input ref not available yet, retrying... (attempt ${attempts + 1})`
          );
          setTimeout(() => attemptFocus(attempts + 1), 100);
        }
      };

      // Start attempting to focus after a delay to ensure modal is rendered
      const focusTimeout = setTimeout(() => attemptFocus(), 400);

      return () => clearTimeout(focusTimeout);
    }
  }, [isOpen, paymentMethod]);

  // Additional effect to focus when the input ref becomes available
  useEffect(() => {
    if (isOpen && paymentMethod === "CASH" && amountInputRef.current) {
      console.log("PaymentModal: Input ref became available, focusing immediately");
      try {
        amountInputRef.current.focus();
        console.log("PaymentModal: Successfully focused via ref availability effect");
      } catch (error) {
        console.error("PaymentModal: Error in ref availability focus:", error);
      }
    }
  }, [isOpen, paymentMethod, amountInputRef.current]);

  // Handle keyboard events for form submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isProcessing) {
      e.preventDefault();
      if (isValidCashPayment) {
        handleCompletePayment();
      }
    }
  };

  const handlePaymentMethodChange = (method: "CASH" | "DEBIT" | "QRIS") => {
    setPaymentMethod(method);
    if (method !== "CASH") {
      setAmountReceived(total.toString());
    } else {
      setAmountReceived("");
      // Auto-focus amount input when switching to CASH payment
      setTimeout(() => {
        if (amountInputRef.current) {
          amountInputRef.current.focus();
          console.log("PaymentModal: Auto-focused cash amount input after payment method change");
        }
      }, 100); // Short delay to ensure the input field is rendered
    }
  };

  const handleCompletePayment = async () => {
    if (!isValidCashPayment) {
      toast.error("Amount received must be greater than or equal to total");
      return;
    }

    if (cartItems.length === 0) {
      toast.error("Cart is empty");
      return;
    }

    setIsProcessing(true);

    // Trigger focus restoration immediately when payment starts
    // This ensures focus is ready when user returns from receipt tab
    console.log("PaymentModal: Payment started, preparing focus restoration");
    setTimeout(() => {
      focusSearchInput(100);
    }, 100);
    try {
      // Prepare transaction data
      const transactionData = {
        customerId: selectedCustomer?.id,
        subtotal: subtotal,
        discount: totalDiscount,
        tax: 0, // No tax for now
        total: total,
        items: cartItems.map((item) => ({
          productId: item.productId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount + item.autoDiscount,
          subtotal: item.subtotal,
        })),
        paymentMethod,
        paymentStatus,
        cashReceived: paymentMethod === "CASH" ? amountReceivedNum : undefined,
        changeAmount: paymentMethod === "CASH" ? change : undefined,
        notes: notes.trim() || "",
      };

      console.log("Sending transaction data:", JSON.stringify(transactionData, null, 2));

      const response = await fetch("/api/transactions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(transactionData),
      });

      if (response.ok) {
        const result = await response.json();

        // Clear cart and close modal
        clearCart();
        onClose();

        // Reset form
        setPaymentMethod("CASH");
        setPaymentStatus("PAID");
        setAmountReceived("");
        setNotes("");

        toast.success("Transaction completed successfully");

        // Dispatch custom event to notify other components about transaction completion
        window.dispatchEvent(
          new CustomEvent("transactionCompleted", {
            detail: {
              transactionId: result.transaction.id,
              paymentMethod: paymentMethod,
              total: total,
            },
          })
        );

        // Open receipt in new tab (no auto-print to preserve focus)
        const receiptUrl = `/receipts/${result.transaction.id}`;

        console.log("PaymentModal: Opening receipt tab and setting up focus restoration");

        try {
          const receiptWindow = window.open(receiptUrl, "_blank");

          if (!receiptWindow) {
            // Pop-up blocked, restore focus immediately
            console.log("PaymentModal: Pop-up blocked, restoring focus immediately");
            setTimeout(() => {
              focusSearchInput(300);
            }, 100);
          } else {
            // Receipt window opened successfully
            console.log(
              "PaymentModal: Receipt window opened, focus will be restored when user returns"
            );
            // Additional focus restoration as backup
            setTimeout(() => {
              focusSearchInput(200);
            }, 500);
          }
        } catch (error) {
          console.error("Error opening receipt window:", error);
          // Restore focus on error
          setTimeout(() => {
            focusSearchInput(300);
          }, 100);
        }
      } else {
        const errorData = await response.json();
        console.error("Transaction API error:", errorData);

        // Show more detailed error message
        let errorMessage = errorData.error || "Failed to process transaction";
        if (errorData.details) {
          console.error("Validation details:", errorData.details);
          // If it's a validation error, show a more helpful message
          if (errorMessage === "Validation failed") {
            errorMessage = "Transaction validation failed. Please check all required fields.";
          }
        }

        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      toast.error("Failed to process transaction");
    } finally {
      setIsProcessing(false);
    }
  };

  const resetForm = () => {
    setPaymentMethod("CASH");
    setPaymentStatus("PAID");
    setAmountReceived("");
    setNotes("");
  };

  const handleClose = () => {
    resetForm();
    onClose();
    // Restore focus when modal is closed manually
    setTimeout(() => {
      focusSearchInput(150);
    }, 100);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Payment</DialogTitle>
        </DialogHeader>

        <form
          className="space-y-6"
          onSubmit={(e) => {
            e.preventDefault();
            if (isValidCashPayment && !isProcessing) {
              handleCompletePayment();
            }
          }}
        >
          {/* Order Summary */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Order Summary</h3>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Items:</span>
                <span>{cartItems.length}</span>
              </div>
              <div className="flex justify-between font-bold text-xl">
                <span>Total:</span>
                <span>Rp {Number(total).toLocaleString("id-ID")}</span>
              </div>
              {selectedCustomer && (
                <div className="flex justify-between">
                  <span>Customer:</span>
                  <span>{selectedCustomer.name}</span>
                </div>
              )}
            </div>
          </div>

          {/* Payment Method */}
          <div>
            <Label>Payment Method</Label>
            <Select value={paymentMethod} onValueChange={handlePaymentMethodChange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="CASH">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-4 w-4" />
                    <span>Cash</span>
                  </div>
                </SelectItem>
                <SelectItem value="DEBIT">
                  <div className="flex items-center space-x-2">
                    <CreditCard className="h-4 w-4" />
                    <span>Debit</span>
                  </div>
                </SelectItem>
                <SelectItem value="QRIS">
                  <div className="flex items-center space-x-2">
                    <Smartphone className="h-4 w-4" />
                    <span>QRIS</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Amount Received (Cash only) */}
          {paymentMethod === "CASH" && (
            <div>
              <Label>Amount Received</Label>
              <Input
                ref={amountInputRef}
                type="number"
                step="0.01"
                min="0"
                value={amountReceived}
                onChange={(e) => setAmountReceived(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Enter amount received"
                autoComplete="off"
                autoFocus={paymentMethod === "CASH"}
              />
              {amountReceivedNum > 0 && (
                <div className="mt-2 text-lg font-semibold">
                  {change >= 0 ? (
                    <span className="text-green-600">
                      Change: Rp {Number(change).toLocaleString("id-ID")}
                    </span>
                  ) : (
                    <span className="text-red-600">Insufficient amount</span>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Payment Status */}
          <div>
            <Label>Payment Status</Label>
            <Select
              value={paymentStatus}
              onValueChange={(value: "PAID" | "PARTIAL" | "PENDING") => setPaymentStatus(value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PAID">Paid</SelectItem>
                <SelectItem value="PARTIAL">Partial</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div>
            <Label>Notes (optional)</Label>
            <Textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Add any notes about this transaction..."
              rows={3}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button type="submit" disabled={!isValidCashPayment || isProcessing} className="flex-1">
              {isProcessing ? "Processing..." : "Complete Payment"}
            </Button>
            <Button type="button" variant="outline" onClick={handleClose} disabled={isProcessing}>
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
