"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  MoreVertical,
  AlertTriangle 
} from "lucide-react";

const statusUpdateSchema = z.object({
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED']),
  notes: z.string().optional(),
});

type StatusUpdateData = z.infer<typeof statusUpdateSchema>;

interface InvoiceStatusActionsProps {
  invoiceId: string;
  currentStatus: string;
  userRole: string;
  paidAmount: number;
  hasPayments: boolean;
  onStatusUpdated: () => void;
}

const STATUS_TRANSITIONS = {
  PENDING: ['APPROVED', 'REJECTED'],
  APPROVED: ['CANCELLED'],
  REJECTED: ['PENDING'],
  CANCELLED: [],
};

const STATUS_LABELS = {
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  CANCELLED: 'Cancelled',
};

const STATUS_ICONS = {
  PENDING: <Clock className="h-4 w-4" />,
  APPROVED: <CheckCircle className="h-4 w-4" />,
  REJECTED: <XCircle className="h-4 w-4" />,
  CANCELLED: <AlertTriangle className="h-4 w-4" />,
};

export function InvoiceStatusActions({
  invoiceId,
  currentStatus,
  userRole,
  paidAmount,
  hasPayments,
  onStatusUpdated,
}: InvoiceStatusActionsProps) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<StatusUpdateData>({
    resolver: zodResolver(statusUpdateSchema),
    defaultValues: {
      notes: '',
    },
  });

  // Check if user can perform status transitions
  const canUpdateStatus = ['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(userRole);
  let availableTransitions = STATUS_TRANSITIONS[currentStatus as keyof typeof STATUS_TRANSITIONS] || [];

  // Filter out Cancel option if invoice has payments
  if (paidAmount > 0 || hasPayments) {
    availableTransitions = availableTransitions.filter(status => status !== 'CANCELLED');
  }

  const handleStatusChange = (newStatus: string) => {
    setSelectedStatus(newStatus);
    form.setValue('status', newStatus as any);
    setError(null); // Clear any previous errors
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
    setError(null);
    form.reset();
  };

  const onSubmit = async (data: StatusUpdateData) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Submitting status update:', data);

      const response = await fetch(`/api/invoices/${invoiceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Status update failed:', errorData);
        throw new Error(errorData.error || 'Failed to update invoice status');
      }

      const result = await response.json();
      console.log('Status update successful:', result);

      // Close dialog first, then update parent
      setDialogOpen(false);
      setError(null);
      form.reset();

      // Call parent update after a small delay to ensure dialog is closed
      setTimeout(() => {
        onStatusUpdated();
      }, 100);

    } catch (error) {
      console.error('Error updating invoice status:', error);
      setError(error instanceof Error ? error.message : 'Failed to update invoice status');
    } finally {
      setLoading(false);
    }
  };

  if (!canUpdateStatus || availableTransitions.length === 0) {
    return null;
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'text-green-600';
      case 'REJECTED':
        return 'text-red-600';
      case 'CANCELLED':
        return 'text-gray-600';
      default:
        return 'text-yellow-600';
    }
  };

  const getActionLabel = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'Approve Invoice';
      case 'REJECTED':
        return 'Reject Invoice';
      case 'CANCELLED':
        return 'Cancel Invoice';
      case 'PENDING':
        return 'Mark as Pending';
      default:
        return `Change to ${STATUS_LABELS[status as keyof typeof STATUS_LABELS]}`;
    }
  };

  const getActionDescription = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'This will approve the invoice and allow payments to be recorded.';
      case 'REJECTED':
        return 'This will reject the invoice. You can provide a reason below.';
      case 'CANCELLED':
        return 'This will cancel the invoice. This action should be used carefully.';
      case 'PENDING':
        return 'This will reset the invoice status to pending for review.';
      default:
        return `This will change the invoice status to ${STATUS_LABELS[status as keyof typeof STATUS_LABELS]}.`;
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm">
            <MoreVertical className="h-4 w-4" />
            Actions
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {availableTransitions.map((status) => (
            <DropdownMenuItem
              key={status}
              onClick={() => handleStatusChange(status)}
              className={getStatusColor(status)}
            >
              {STATUS_ICONS[status as keyof typeof STATUS_ICONS]}
              <span className="ml-2">{getActionLabel(status)}</span>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={dialogOpen} onOpenChange={(open) => {
        if (!open && !loading) {
          handleDialogClose();
        }
      }}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {STATUS_ICONS[selectedStatus as keyof typeof STATUS_ICONS]}
              {getActionLabel(selectedStatus)}
            </DialogTitle>
            <DialogDescription>
              {getActionDescription(selectedStatus)}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {/* Notes */}
              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {selectedStatus === 'REJECTED' ? 'Reason for Rejection' : 'Notes (Optional)'}
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder={
                          selectedStatus === 'REJECTED'
                            ? 'Please provide a reason for rejecting this invoice...'
                            : 'Additional notes about this status change...'
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {error && (
                <div className="p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
                  {error}
                </div>
              )}

              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleDialogClose}
                  disabled={loading}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={loading}
                  className={
                    selectedStatus === 'APPROVED'
                      ? 'bg-green-600 hover:bg-green-700'
                      : selectedStatus === 'REJECTED'
                      ? 'bg-red-600 hover:bg-red-700'
                      : ''
                  }
                >
                  {loading ? 'Updating...' : getActionLabel(selectedStatus)}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
}
