"use client";

import React, { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, CreditCard, Upload, X, Eye, FileText } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/lib/invoice-utils";
import { validateFile } from "@/lib/file-upload";

const paymentFormSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  paymentDate: z.date(),
  paymentMethod: z.string().min(1, "Payment method is required"),
  paymentReference: z.string().optional(),
  notes: z.string().optional(),
  proofImage: z.any().optional(), // File will be handled separately
});

type PaymentFormData = z.infer<typeof paymentFormSchema>;

interface Installment {
  id: string;
  installmentNumber: number;
  dueDate: string;
  amount: number;
  description: string | null;
  status: string;
  paidAmount: number;
  paidAt: string | null;
}

interface PaymentRecordingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  invoiceId: string;
  invoiceNumber: string;
  totalAmount: number;
  paidAmount: number;
  installments?: Installment[];
  onPaymentRecorded: () => void;
}

const PAYMENT_METHODS = [
  { value: "BANK_TRANSFER", label: "Bank Transfer" },
  { value: "CASH", label: "Cash" },
  { value: "CHECK", label: "Check" },
  { value: "CREDIT_CARD", label: "Credit Card" },
  { value: "DEBIT_CARD", label: "Debit Card" },
  { value: "DIGITAL_WALLET", label: "Digital Wallet" },
  { value: "OTHER", label: "Other" },
];

// Client-side utility functions
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith('image/');
}

function isPdfFile(mimeType: string): boolean {
  return mimeType === 'application/pdf';
}

export function PaymentRecordingDialog({
  open,
  onOpenChange,
  invoiceId,
  invoiceNumber,
  totalAmount,
  paidAmount,
  installments,
  onPaymentRecorded,
}: PaymentRecordingDialogProps) {
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [filePreview, setFilePreview] = useState<string | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const remainingAmount = totalAmount - paidAmount;

  // Memoize calculated values to prevent infinite re-renders
  const nextInstallment = useMemo(() => {
    return installments?.find(inst => inst.status === 'PENDING' || inst.paidAmount < inst.amount);
  }, [installments]);

  const suggestedAmount = useMemo(() => {
    return nextInstallment ? (nextInstallment.amount - nextInstallment.paidAmount) : remainingAmount;
  }, [nextInstallment, remainingAmount]);

  const suggestedDueDate = useMemo(() => {
    return nextInstallment ? new Date(nextInstallment.dueDate) : new Date();
  }, [nextInstallment]);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amount: 0, // Will be set by useEffect
      paymentDate: new Date(),
      paymentMethod: "",
      paymentReference: "",
      notes: "",
      proofImage: undefined,
    },
  });

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      setFileError(validation.error || 'Invalid file');
      return;
    }

    setFileError(null);
    setSelectedFile(file);

    // Create preview for images
    if (isImageFile(file.type)) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setFilePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    } else {
      setFilePreview(null);
    }
  };

  // Remove selected file
  const handleFileRemove = () => {
    setSelectedFile(null);
    setFilePreview(null);
    setFileError(null);
    // Reset file input
    const fileInput = document.getElementById('proof-image-input') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = '';
    }
  };

  // Reset file state when dialog closes
  React.useEffect(() => {
    if (!open) {
      handleFileRemove();
    }
  }, [open]);

  // Update form when dialog opens with installment data
  React.useEffect(() => {
    if (open) {
      form.setValue('amount', suggestedAmount);
      form.setValue('paymentDate', suggestedDueDate);
    }
  }, [open, suggestedAmount, suggestedDueDate]); // Removed form from dependencies

  const onSubmit = async (data: PaymentFormData) => {
    try {
      setLoading(true);
      setUploadProgress(0);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('amount', data.amount.toString());
      formData.append('paymentDate', data.paymentDate.toISOString());
      formData.append('paymentMethod', data.paymentMethod);
      if (data.paymentReference) {
        formData.append('paymentReference', data.paymentReference);
      }
      if (data.notes) {
        formData.append('notes', data.notes);
      }
      if (selectedFile) {
        formData.append('proofImage', selectedFile);
      }

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      const response = await fetch(`/api/invoices/${invoiceId}/payments`, {
        method: "POST",
        body: formData, // No Content-Type header for FormData
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to record payment");
      }

      // Reset form and close dialog
      form.reset();
      handleFileRemove();
      onOpenChange(false);
      onPaymentRecorded();
    } catch (error) {
      console.error("Error recording payment:", error);
      setFileError(error instanceof Error ? error.message : 'Failed to record payment');
    } finally {
      setLoading(false);
      setUploadProgress(0);
    }
  };

  const watchedAmount = form.watch("amount");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Record Payment
          </DialogTitle>
          <DialogDescription>
            Record a payment for invoice {invoiceNumber}
          </DialogDescription>
        </DialogHeader>

        {/* Payment Summary */}
        <div className="bg-muted p-4 rounded-lg space-y-2">
          <div className="flex justify-between text-sm">
            <span>Total Invoice Amount:</span>
            <span className="font-medium">{formatCurrency(totalAmount)}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Already Paid:</span>
            <span className="font-medium text-green-600">{formatCurrency(paidAmount)}</span>
          </div>
          <div className="flex justify-between text-sm border-t pt-2">
            <span>Remaining Balance:</span>
            <span className="font-semibold text-red-600">{formatCurrency(remainingAmount)}</span>
          </div>

          {/* Installment Information */}
          {nextInstallment && (
            <div className="border-t pt-2 mt-2">
              <div className="text-sm font-medium text-blue-600 mb-1">
                Next Installment: #{nextInstallment.installmentNumber}
              </div>
              <div className="flex justify-between text-sm">
                <span>Installment Amount:</span>
                <span className="font-medium">{formatCurrency(nextInstallment.amount)}</span>
              </div>
              {nextInstallment.paidAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span>Already Paid:</span>
                  <span className="font-medium text-green-600">{formatCurrency(nextInstallment.paidAmount)}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Due Date:</span>
                <span className={`font-medium ${new Date(nextInstallment.dueDate) < new Date() ? 'text-red-600' : ''}`}>
                  {new Date(nextInstallment.dueDate).toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between text-sm font-medium">
                <span>Suggested Payment:</span>
                <span className="text-blue-600">{formatCurrency(suggestedAmount)}</span>
              </div>
            </div>
          )}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              {/* Payment Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="0"
                        {...field}
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                      />
                    </FormControl>
                    <FormMessage />
                    {watchedAmount > remainingAmount && (
                      <p className="text-sm text-red-600">
                        Amount exceeds remaining balance
                      </p>
                    )}
                  </FormItem>
                )}
              />

              {/* Payment Date */}
              <FormField
                control={form.control}
                name="paymentDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Method</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {PAYMENT_METHODS.map((method) => (
                        <SelectItem key={method.value} value={method.value}>
                          {method.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Reference */}
            <FormField
              control={form.control}
              name="paymentReference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Reference (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Transaction ID, check number, etc."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes about this payment..."
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Proof Upload */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Payment Proof (Optional)</label>
              <p className="text-xs text-muted-foreground">
                Upload an image or PDF as proof of payment. Supported formats: JPG, PNG, PDF (max 5MB)
              </p>

              {!selectedFile ? (
                <div className="relative border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">
                      Click to upload or drag and drop
                    </p>
                    <p className="text-xs text-muted-foreground">
                      JPG, PNG, PDF up to 5MB
                    </p>
                  </div>
                  <input
                    id="proof-image-input"
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={handleFileSelect}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                  />
                </div>
              ) : (
                <div className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {isImageFile(selectedFile.type) ? (
                        <div className="relative">
                          {filePreview ? (
                            <img
                              src={filePreview}
                              alt="Payment proof preview"
                              className="w-12 h-12 object-cover rounded"
                            />
                          ) : (
                            <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                              <Upload className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="w-12 h-12 bg-red-100 rounded flex items-center justify-center">
                          <FileText className="h-6 w-6 text-red-600" />
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium">{selectedFile.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {formatFileSize(selectedFile.size)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {isImageFile(selectedFile.type) && filePreview && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            // Open preview in new window
                            const newWindow = window.open();
                            if (newWindow) {
                              newWindow.document.write(`
                                <html>
                                  <head><title>Payment Proof Preview</title></head>
                                  <body style="margin:0;display:flex;justify-content:center;align-items:center;min-height:100vh;background:#f0f0f0;">
                                    <img src="${filePreview}" style="max-width:100%;max-height:100%;object-fit:contain;" />
                                  </body>
                                </html>
                              `);
                            }
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleFileRemove}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {loading && uploadProgress > 0 && (
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Uploading...</span>
                        <span>{uploadProgress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${uploadProgress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}

              {fileError && (
                <p className="text-sm text-red-600">{fileError}</p>
              )}
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading || watchedAmount <= 0 || watchedAmount > remainingAmount}
              >
                {loading ? "Recording..." : "Record Payment"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
