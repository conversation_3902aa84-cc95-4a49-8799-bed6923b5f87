"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { 
  ShoppingCart, 
  AlertTriangle, 
  Clock, 
  TrendingUp,
  Package,
  DollarSign,
  RefreshCw,
  Loader2,
  Plus,
  Eye
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface POSuggestion {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  currentStock: number;
  reorderPoint: number;
  daysUntilStockout: number;
  urgencyLevel: 'critical' | 'high' | 'medium' | 'low';
  recommendedSupplier: {
    supplierId: string;
    supplierName: string;
    score: number;
    purchasePrice: number;
    minimumOrderQuantity?: number;
    isPreferred: boolean;
  };
  suggestedQuantity: number;
  estimatedCost: number;
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'po_created';
}

interface POSuggestionSummary {
  totalSuggestions: number;
  criticalCount: number;
  highPriorityCount: number;
  totalEstimatedCost: number;
  productsAtRisk: number;
  averageUrgency: number;
  topSuppliers: Array<{
    supplierId: string;
    supplierName: string;
    productCount: number;
    totalValue: number;
  }>;
}

interface POSuggestionsDashboardProps {
  categoryId?: string;
  onViewDetails?: (suggestion: POSuggestion) => void;
  onCreatePO?: (suggestion: POSuggestion) => void;
  onBulkCreatePO?: (suggestions: POSuggestion[]) => void;
}

export default function POSuggestionsDashboard({ 
  categoryId, 
  onViewDetails, 
  onCreatePO, 
  onBulkCreatePO 
}: POSuggestionsDashboardProps) {
  const [suggestions, setSuggestions] = useState<POSuggestion[]>([]);
  const [summary, setSummary] = useState<POSuggestionSummary | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedSuggestions, setSelectedSuggestions] = useState<Set<string>>(new Set());
  const [urgencyFilter, setUrgencyFilter] = useState<'all' | 'critical' | 'high' | 'medium' | 'low'>('all');
  const [groupBySupplier, setGroupBySupplier] = useState(true);

  // Fetch suggestions on component mount and when filters change
  useEffect(() => {
    fetchSuggestions();
  }, [categoryId, urgencyFilter]);

  const fetchSuggestions = async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      if (categoryId) params.append('categoryId', categoryId);
      if (urgencyFilter !== 'all') params.append('urgency', urgencyFilter);
      params.append('limit', '50');

      const response = await fetch(`/api/po-suggestions?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch PO suggestions');
      }

      const data = await response.json();
      setSuggestions(data.suggestions || []);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error fetching PO suggestions:', error);
      setError('Failed to load PO suggestions');
      toast.error('Failed to load PO suggestions');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchSuggestions();
  };

  const getUrgencyBadgeVariant = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <Clock className="h-4 w-4" />;
      case 'medium': return <Package className="h-4 w-4" />;
      case 'low': return <TrendingUp className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const handleSuggestionSelect = (suggestionId: string, selected: boolean) => {
    const newSelected = new Set(selectedSuggestions);
    if (selected) {
      newSelected.add(suggestionId);
    } else {
      newSelected.delete(suggestionId);
    }
    setSelectedSuggestions(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedSuggestions.size === suggestions.length) {
      setSelectedSuggestions(new Set());
    } else {
      setSelectedSuggestions(new Set(suggestions.map(s => s.id)));
    }
  };

  const handleBulkCreate = () => {
    const selectedSuggestionObjects = suggestions.filter(s => selectedSuggestions.has(s.id));
    onBulkCreatePO?.(selectedSuggestionObjects);
  };

  const handleSupplierGroupCreate = (supplierSuggestions: POSuggestion[]) => {
    const selectedFromSupplier = supplierSuggestions.filter(s => selectedSuggestions.has(s.id));
    if (selectedFromSupplier.length > 0) {
      onBulkCreatePO?.(selectedFromSupplier);
    }
  };

  // Group suggestions by supplier
  const groupedSuggestions = groupBySupplier ?
    suggestions.reduce((groups, suggestion) => {
      const supplierId = suggestion.recommendedSupplier.supplierId;
      if (!groups[supplierId]) {
        groups[supplierId] = {
          supplier: suggestion.recommendedSupplier,
          suggestions: []
        };
      }
      groups[supplierId].suggestions.push(suggestion);
      return groups;
    }, {} as Record<string, { supplier: POSuggestion['recommendedSupplier'], suggestions: POSuggestion[] }>) :
    null;

  return (
    <div className="space-y-6">
      {/* Action Bar */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {selectedSuggestions.size > 0 && (
            <Button onClick={handleBulkCreate}>
              <Plus className="h-4 w-4 mr-2" />
              Create {selectedSuggestions.size} POs
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleRefresh} disabled={loading}>
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Suggestions</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalSuggestions}</div>
              <p className="text-xs text-muted-foreground">
                {summary.productsAtRisk} products at risk
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Critical & High Priority</CardTitle>
              <AlertTriangle className="h-4 w-4 text-destructive" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-destructive">
                {summary.criticalCount + summary.highPriorityCount}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.criticalCount} critical, {summary.highPriorityCount} high
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estimated Cost</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalEstimatedCost)}</div>
              <p className="text-xs text-muted-foreground">
                Total investment required
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Supplier</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {summary.topSuppliers[0]?.productCount || 0}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.topSuppliers[0]?.supplierName || 'No suppliers'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Filter by urgency:</span>
          {['all', 'critical', 'high', 'medium', 'low'].map((level) => (
            <Button
              key={level}
              variant={urgencyFilter === level ? 'default' : 'outline'}
              size="sm"
              onClick={() => setUrgencyFilter(level as any)}
            >
              {level === 'all' ? 'All' : level.charAt(0).toUpperCase() + level.slice(1)}
            </Button>
          ))}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Group by supplier:</span>
          <Button
            variant={groupBySupplier ? 'default' : 'outline'}
            size="sm"
            onClick={() => setGroupBySupplier(!groupBySupplier)}
          >
            {groupBySupplier ? 'Grouped' : 'List View'}
          </Button>
        </div>
      </div>

      {/* Error State */}
      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading PO suggestions...</span>
        </div>
      )}

      {/* Suggestions List */}
      {!loading && suggestions.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Suggestions ({suggestions.length})</CardTitle>
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                {selectedSuggestions.size === suggestions.length ? 'Deselect All' : 'Select All'}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {groupBySupplier && groupedSuggestions ? (
              // Grouped by Supplier View
              <div className="space-y-6">
                {Object.entries(groupedSuggestions).map(([supplierId, group]) => {
                  const selectedFromGroup = group.suggestions.filter(s => selectedSuggestions.has(s.id));
                  const totalGroupCost = selectedFromGroup.reduce((sum, s) => sum + s.estimatedCost, 0);

                  return (
                    <div key={supplierId} className="border rounded-lg p-4 bg-muted/20">
                      {/* Supplier Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <div>
                            <h3 className="font-semibold text-lg">{group.supplier.supplierName}</h3>
                            <p className="text-sm text-muted-foreground">
                              {group.suggestions.length} product{group.suggestions.length !== 1 ? 's' : ''} •
                              Score: {group.supplier.score}/100
                              {group.supplier.isPreferred && (
                                <Badge variant="secondary" className="ml-2">Preferred</Badge>
                              )}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {selectedFromGroup.length > 0 && (
                            <div className="text-right mr-4">
                              <div className="text-sm text-muted-foreground">Selected Total:</div>
                              <div className="font-semibold">{formatCurrency(totalGroupCost)}</div>
                            </div>
                          )}
                          <Button
                            onClick={() => handleSupplierGroupCreate(group.suggestions)}
                            disabled={selectedFromGroup.length === 0}
                            size="sm"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Create PO ({selectedFromGroup.length})
                          </Button>
                        </div>
                      </div>

                      {/* Products in this supplier group */}
                      <div className="space-y-3">
                        {group.suggestions.map((suggestion) => (
                          <div
                            key={suggestion.id}
                            className={`border rounded-lg p-3 bg-background ${
                              selectedSuggestions.has(suggestion.id) ? 'border-primary bg-primary/5' : ''
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3">
                                <input
                                  type="checkbox"
                                  checked={selectedSuggestions.has(suggestion.id)}
                                  onChange={(e) => handleSuggestionSelect(suggestion.id, e.target.checked)}
                                  className="rounded"
                                />
                                <div>
                                  <h4 className="font-medium">{suggestion.productName}</h4>
                                  <p className="text-sm text-muted-foreground">SKU: {suggestion.productSku}</p>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={getUrgencyBadgeVariant(suggestion.urgencyLevel)}>
                                  {getUrgencyIcon(suggestion.urgencyLevel)}
                                  {suggestion.urgencyLevel}
                                </Badge>
                                <span className="text-sm text-muted-foreground">
                                  {suggestion.daysUntilStockout} days left
                                </span>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mt-3">
                              <div>
                                <span className="text-muted-foreground">Current Stock:</span>
                                <div className="font-medium">{suggestion.currentStock}</div>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Suggested Qty:</span>
                                <div className="font-medium">{suggestion.suggestedQuantity}</div>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Unit Price:</span>
                                <div className="font-medium">{formatCurrency(suggestion.recommendedSupplier.purchasePrice)}</div>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Estimated Cost:</span>
                                <div className="font-medium">{formatCurrency(suggestion.estimatedCost)}</div>
                              </div>
                            </div>

                            <div className="flex items-center justify-end gap-2 mt-3">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => onViewDetails?.(suggestion)}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                Details
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              // List View (Original)
              <div className="space-y-4">
                {suggestions.map((suggestion) => (
                  <div
                    key={suggestion.id}
                    className={`border rounded-lg p-4 ${
                      selectedSuggestions.has(suggestion.id) ? 'border-primary bg-primary/5' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <input
                          type="checkbox"
                          checked={selectedSuggestions.has(suggestion.id)}
                          onChange={(e) => handleSuggestionSelect(suggestion.id, e.target.checked)}
                          className="rounded"
                        />
                        <div>
                          <h4 className="font-semibold">{suggestion.productName}</h4>
                          <p className="text-sm text-muted-foreground">SKU: {suggestion.productSku}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getUrgencyBadgeVariant(suggestion.urgencyLevel)}>
                          {getUrgencyIcon(suggestion.urgencyLevel)}
                          {suggestion.urgencyLevel}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          {suggestion.daysUntilStockout} days left
                        </span>
                      </div>
                    </div>

                    <Separator className="my-3" />

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Current Stock:</span>
                        <div className="font-medium">{suggestion.currentStock}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Suggested Qty:</span>
                        <div className="font-medium">{suggestion.suggestedQuantity}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Supplier:</span>
                        <div className="font-medium">{suggestion.recommendedSupplier.supplierName}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Estimated Cost:</span>
                        <div className="font-medium">{formatCurrency(suggestion.estimatedCost)}</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-end gap-2 mt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewDetails?.(suggestion)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Details
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => onCreatePO?.(suggestion)}
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Create PO
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!loading && suggestions.length === 0 && !error && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-8">
            <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No PO Suggestions</h3>
            <p className="text-muted-foreground text-center">
              All products have sufficient stock levels. Check back later or refresh to see new suggestions.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
