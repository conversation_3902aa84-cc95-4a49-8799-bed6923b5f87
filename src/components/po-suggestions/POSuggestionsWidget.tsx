"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  <PERSON>Cart, 
  AlertTriangle, 
  TrendingUp,
  ArrowRight,
  Loader2,
  RefreshCw
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import Link from "next/link";

interface POSuggestionSummary {
  totalSuggestions: number;
  criticalCount: number;
  highPriorityCount: number;
  totalEstimatedCost: number;
  productsAtRisk: number;
}

interface TopSuggestion {
  id: string;
  productName: string;
  urgencyLevel: 'critical' | 'high' | 'medium' | 'low';
  daysUntilStockout: number;
  estimatedCost: number;
  supplierName: string;
}

interface POSuggestionsWidgetProps {
  className?: string;
  maxItems?: number;
}

export default function POSuggestionsWidget({ 
  className = "", 
  maxItems = 5 
}: POSuggestionsWidgetProps) {
  const [summary, setSummary] = useState<POSuggestionSummary | null>(null);
  const [topSuggestions, setTopSuggestions] = useState<TopSuggestion[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchSummary();
  }, []);

  const fetchSummary = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/po-suggestions?limit=${maxItems}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch PO suggestions');
      }

      const data = await response.json();
      setSummary(data.summary);
      
      // Extract top suggestions for display
      const suggestions = data.suggestions || [];
      const topItems = suggestions.slice(0, maxItems).map((s: any) => ({
        id: s.id,
        productName: s.productName,
        urgencyLevel: s.urgencyLevel,
        daysUntilStockout: s.daysUntilStockout,
        estimatedCost: s.estimatedCost,
        supplierName: s.recommendedSupplier.supplierName,
      }));
      
      setTopSuggestions(topItems);
    } catch (error) {
      console.error('Error fetching PO suggestions summary:', error);
      setError('Failed to load PO suggestions');
    } finally {
      setLoading(false);
    }
  };

  const getUrgencyBadgeVariant = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency) {
      case 'critical': return <AlertTriangle className="h-3 w-3" />;
      case 'high': return <AlertTriangle className="h-3 w-3" />;
      default: return <TrendingUp className="h-3 w-3" />;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            PO Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading suggestions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            PO Suggestions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <AlertTriangle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">{error}</p>
            <Button variant="outline" size="sm" onClick={fetchSummary} className="mt-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!summary || summary.totalSuggestions === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            PO Suggestions
          </CardTitle>
          <CardDescription>
            Automatic purchase order recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ShoppingCart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">
              No purchase order suggestions at this time
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              All products have sufficient stock levels
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              PO Suggestions
            </CardTitle>
            <CardDescription>
              {summary.totalSuggestions} products need reordering
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={fetchSummary}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold text-destructive">
                {summary.criticalCount + summary.highPriorityCount}
              </div>
              <div className="text-xs text-muted-foreground">
                Urgent Orders
              </div>
            </div>
            <div className="text-center p-3 bg-muted rounded-lg">
              <div className="text-2xl font-bold">
                {formatCurrency(summary.totalEstimatedCost)}
              </div>
              <div className="text-xs text-muted-foreground">
                Total Value
              </div>
            </div>
          </div>

          {/* Top Suggestions */}
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Most Urgent</h4>
            {topSuggestions.map((suggestion) => (
              <div
                key={suggestion.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <Badge 
                      variant={getUrgencyBadgeVariant(suggestion.urgencyLevel)}
                      className="text-xs"
                    >
                      {getUrgencyIcon(suggestion.urgencyLevel)}
                      {suggestion.urgencyLevel}
                    </Badge>
                    <span className="text-xs text-muted-foreground">
                      {suggestion.daysUntilStockout}d left
                    </span>
                  </div>
                  <div className="font-medium text-sm truncate">
                    {suggestion.productName}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {suggestion.supplierName} • {formatCurrency(suggestion.estimatedCost)}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-2">
            <Link href="/inventory/po-suggestions" className="flex-1">
              <Button variant="outline" className="w-full">
                View All
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
            <Link href="/inventory/po-suggestions?urgency=critical">
              <Button size="sm" variant="destructive">
                {summary.criticalCount} Critical
              </Button>
            </Link>
          </div>

          {/* Quick Stats */}
          <div className="text-xs text-muted-foreground text-center pt-2 border-t">
            {summary.productsAtRisk} products at risk of stockout
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
