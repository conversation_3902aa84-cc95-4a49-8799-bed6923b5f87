"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { 
  Package, 
  TrendingUp, 
  Clock, 
  DollarSign, 
  Star,
  AlertTriangle,
  Calendar,
  Truck,
  ShoppingCart,
  Info
} from "lucide-react";
import { formatCurrency, formatDate } from "@/lib/utils";
import { toast } from "sonner";

interface POSuggestion {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  currentStock: number;
  reorderPoint: number;
  daysUntilStockout: number;
  urgencyLevel: 'critical' | 'high' | 'medium' | 'low';
  recommendedSupplier: {
    supplierId: string;
    supplierName: string;
    productSupplierId: string;
    score: number;
    reasoning: string[];
    purchasePrice: number;
    leadTimeDays?: number;
    minimumOrderQuantity?: number;
    isPreferred: boolean;
  };
  suggestedQuantity: number;
  estimatedCost: number;
  demandForecast: {
    predictedDemand: number;
    confidenceLevel: number;
    seasonalFactor: number;
  };
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'po_created';
  metadata: {
    triggerReason: string;
    stockoutDate: string;
    safetyStockDays: number;
    leadTimeBuffer: number;
  };
}

interface POSuggestionDetailsProps {
  suggestion: POSuggestion;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreatePO: (suggestion: POSuggestion, adjustedQuantity?: number, notes?: string) => void;
}

export default function POSuggestionDetails({ 
  suggestion, 
  open, 
  onOpenChange, 
  onCreatePO 
}: POSuggestionDetailsProps) {
  const [adjustedQuantity, setAdjustedQuantity] = useState(suggestion.suggestedQuantity);
  const [notes, setNotes] = useState('');
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState('');

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'text-red-600';
      case 'high': return 'text-orange-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const getUrgencyBadgeVariant = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'destructive';
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'outline';
      default: return 'outline';
    }
  };

  const handleCreatePO = () => {
    if (adjustedQuantity <= 0) {
      toast.error('Quantity must be greater than 0');
      return;
    }

    onCreatePO(suggestion, adjustedQuantity, notes);
    onOpenChange(false);
  };

  const adjustedCost = adjustedQuantity * suggestion.recommendedSupplier.purchasePrice;
  const costDifference = adjustedCost - suggestion.estimatedCost;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            PO Suggestion Details
          </DialogTitle>
          <DialogDescription>
            Review and adjust the purchase order suggestion for {suggestion.productName}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Product Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Product Name</Label>
                  <div className="font-medium">{suggestion.productName}</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">SKU</Label>
                  <div className="font-medium">{suggestion.productSku}</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Current Stock</Label>
                  <div className="font-medium">{suggestion.currentStock}</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Reorder Point</Label>
                  <div className="font-medium">{suggestion.reorderPoint}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Urgency & Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Urgency & Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant={getUrgencyBadgeVariant(suggestion.urgencyLevel)}>
                      {suggestion.urgencyLevel.toUpperCase()}
                    </Badge>
                    <span className={`font-medium ${getUrgencyColor(suggestion.urgencyLevel)}`}>
                      {suggestion.daysUntilStockout} days until stockout
                    </span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Stockout date: {formatDate(suggestion.metadata.stockoutDate)}
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Stock depletion progress</span>
                    <span>{Math.max(0, 100 - (suggestion.daysUntilStockout / 30) * 100).toFixed(0)}%</span>
                  </div>
                  <Progress 
                    value={Math.max(0, 100 - (suggestion.daysUntilStockout / 30) * 100)} 
                    className="h-2"
                  />
                </div>

                <div className="text-sm text-muted-foreground">
                  <strong>Trigger Reason:</strong> {suggestion.metadata.triggerReason}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Supplier Recommendation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Recommended Supplier
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div>
                      <h4 className="font-semibold">{suggestion.recommendedSupplier.supplierName}</h4>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <span>Score: {suggestion.recommendedSupplier.score}/100</span>
                        {suggestion.recommendedSupplier.isPreferred && (
                          <Badge variant="secondary">
                            <Star className="h-3 w-3 mr-1" />
                            Preferred
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{formatCurrency(suggestion.recommendedSupplier.purchasePrice)}</div>
                    <div className="text-sm text-muted-foreground">per unit</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  {suggestion.recommendedSupplier.minimumOrderQuantity && (
                    <div>
                      <span className="text-muted-foreground">MOQ:</span>
                      <div className="font-medium">{suggestion.recommendedSupplier.minimumOrderQuantity}</div>
                    </div>
                  )}
                  {suggestion.recommendedSupplier.leadTimeDays && (
                    <div>
                      <span className="text-muted-foreground">Lead Time:</span>
                      <div className="font-medium">{suggestion.recommendedSupplier.leadTimeDays} days</div>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium">Selection Reasoning:</Label>
                  <ul className="mt-2 space-y-1">
                    {suggestion.recommendedSupplier.reasoning.map((reason, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <Info className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                        {reason}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demand Forecast */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Demand Forecast
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Predicted Demand (30 days)</Label>
                  <div className="font-medium">{suggestion.demandForecast.predictedDemand}</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Confidence Level</Label>
                  <div className="font-medium">{suggestion.demandForecast.confidenceLevel}%</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">Seasonal Factor</Label>
                  <div className="font-medium">{suggestion.demandForecast.seasonalFactor.toFixed(2)}x</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Adjustment */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Order Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="quantity">Quantity</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={adjustedQuantity}
                      onChange={(e) => setAdjustedQuantity(parseInt(e.target.value) || 0)}
                      min="1"
                    />
                    <div className="text-sm text-muted-foreground mt-1">
                      Suggested: {suggestion.suggestedQuantity}
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="delivery-date">Expected Delivery Date</Label>
                    <Input
                      id="delivery-date"
                      type="date"
                      value={expectedDeliveryDate}
                      onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Notes (Optional)</Label>
                  <Textarea
                    id="notes"
                    placeholder="Add any special instructions or notes for this purchase order..."
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                  />
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Unit Price:</span>
                    <span>{formatCurrency(suggestion.recommendedSupplier.purchasePrice)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Quantity:</span>
                    <span>{adjustedQuantity}</span>
                  </div>
                  <div className="flex justify-between font-semibold">
                    <span>Total Cost:</span>
                    <span>{formatCurrency(adjustedCost)}</span>
                  </div>
                  {costDifference !== 0 && (
                    <div className={`flex justify-between text-sm ${costDifference > 0 ? 'text-red-600' : 'text-green-600'}`}>
                      <span>Difference from suggested:</span>
                      <span>{costDifference > 0 ? '+' : ''}{formatCurrency(costDifference)}</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreatePO}>
            <ShoppingCart className="h-4 w-4 mr-2" />
            Create Purchase Order
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
