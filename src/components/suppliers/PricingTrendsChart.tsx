"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  AlertTriangle, 
  RefreshCw,
  DollarSign,
  Target,
  Award
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { SupplierPricingAnalysis } from "@/lib/pricing-analytics";

interface PricingTrendsChartProps {
  supplierId: string;
}

export function PricingTrendsChart({ supplierId }: PricingTrendsChartProps) {
  const [pricingData, setPricingData] = useState<SupplierPricingAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("6months");

  const fetchPricingTrends = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierId}/pricing-trends?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch pricing trends");
      }

      const data = await response.json();
      setPricingData(data.pricingAnalysis);
    } catch (error) {
      console.error("Error fetching pricing trends:", error);
      setError("Failed to load pricing trends. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPricingTrends();
  }, [supplierId, timeRange]);

  const getCompetitivenessColor = (level: string) => {
    switch (level) {
      case 'excellent': return 'bg-green-100 text-green-800';
      case 'good': return 'bg-blue-100 text-blue-800';
      case 'average': return 'bg-yellow-100 text-yellow-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTrendIcon = (change: number) => {
    if (change > 1) return <TrendingUp className="h-4 w-4 text-red-600" />;
    if (change < -1) return <TrendingDown className="h-4 w-4 text-green-600" />;
    return <Minus className="h-4 w-4 text-gray-600" />;
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pricing Competitiveness</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading pricing trends...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pricing Competitiveness</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={fetchPricingTrends} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!pricingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Pricing Competitiveness</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <DollarSign className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No pricing data available for this supplier.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { metrics, recommendations, competitivenessLevel } = pricingData;

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      {/* Header with Time Range Selector */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-semibold">Pricing Competitiveness</h3>
          <p className="text-sm text-muted-foreground">{pricingData.timeRange}</p>
        </div>
        <div className="flex-shrink-0">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="1year">Last Year</SelectItem>
              <SelectItem value="2years">Last 2 Years</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Competitiveness Score</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(metrics.competitivenessScore)}`}>
              {metrics.competitivenessScore.toFixed(1)}/100
            </div>
            <Badge className={getCompetitivenessColor(competitivenessLevel)}>
              {competitivenessLevel.toUpperCase()}
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Market Position</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">#{metrics.priceRank}</div>
            <p className="text-xs text-muted-foreground">
              of {metrics.totalSuppliers} suppliers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Price</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.currentAveragePrice)}</div>
            <p className="text-xs text-muted-foreground">
              Market: {formatCurrency(metrics.marketAveragePrice)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Price Volatility</CardTitle>
            {getTrendIcon(metrics.averagePriceChange)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.priceVolatility.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Avg change: {metrics.averagePriceChange.toFixed(1)}%
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Product Price Analysis */}
      {metrics.priceTrends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Product Price Analysis</CardTitle>
            <CardDescription>
              Price competitiveness by product compared to market average
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="w-full overflow-x-auto">
              <div className="min-w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[180px]">Product</TableHead>
                      <TableHead className="min-w-[100px]">SKU</TableHead>
                      <TableHead className="min-w-[120px] text-right">Current Price</TableHead>
                      <TableHead className="min-w-[120px] text-right">Market Average</TableHead>
                      <TableHead className="min-w-[130px] text-right">Competitiveness</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.priceTrends.map((product) => (
                      <TableRow key={product.productId}>
                        <TableCell className="font-medium min-w-[180px]">
                          <span className="truncate block">{product.productName}</span>
                        </TableCell>
                        <TableCell className="min-w-[100px]">
                          <span className="truncate block">{product.productSku}</span>
                        </TableCell>
                        <TableCell className="text-right">{formatCurrency(product.currentPrice)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.marketAverage)}</TableCell>
                        <TableCell className="text-right">
                          <Badge
                            variant={product.competitivenessScore > 80 ? "default" :
                                    product.competitivenessScore > 60 ? "secondary" : "destructive"}
                          >
                            {product.competitivenessScore.toFixed(1)}%
                          </Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Monthly Price Trends */}
      {metrics.monthlyPriceTrends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Monthly Price Trends</CardTitle>
            <CardDescription>
              Price evolution over time compared to market
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.monthlyPriceTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{trend.month}</span>
                    {getTrendIcon(trend.changePercentage)}
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(trend.averagePrice)}</div>
                      <div className="text-sm text-muted-foreground">Supplier</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(trend.marketAverage)}</div>
                      <div className="text-sm text-muted-foreground">Market</div>
                    </div>
                    {index > 0 && (
                      <div className="text-right">
                        <div className={`font-semibold ${trend.changePercentage > 0 ? 'text-red-600' : 'text-green-600'}`}>
                          {trend.changePercentage > 0 ? '+' : ''}{trend.changePercentage.toFixed(1)}%
                        </div>
                        <div className="text-sm text-muted-foreground">Change</div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Pricing Recommendations</CardTitle>
            <CardDescription>
              Suggested actions to improve pricing competitiveness
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recommendations.map((recommendation, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{recommendation}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
