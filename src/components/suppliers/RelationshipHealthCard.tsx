"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  AlertTriangle, 
  RefreshCw,
  Heart,
  Shield,
  Users,
  Zap,
  CheckCircle,
  XCircle
} from "lucide-react";
import type { SupplierRelationshipAnalysis } from "@/lib/relationship-health";

interface RelationshipHealthCardProps {
  supplierId: string;
}

export function RelationshipHealthCard({ supplierId }: RelationshipHealthCardProps) {
  const [healthData, setHealthData] = useState<SupplierRelationshipAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("6months");

  const fetchRelationshipHealth = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierId}/relationship-health?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch relationship health");
      }

      const data = await response.json();
      setHealthData(data.relationshipAnalysis);
    } catch (error) {
      console.error("Error fetching relationship health:", error);
      setError("Failed to load relationship health. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRelationshipHealth();
  }, [supplierId, timeRange]);

  const getRiskBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'default';
      case 'medium': return 'secondary';
      case 'high': return 'destructive';
      default: return 'outline';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'medium': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'low': return <CheckCircle className="h-4 w-4 text-green-600" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Relationship Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading relationship health...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Relationship Health</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={fetchRelationshipHealth} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!healthData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Relationship Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Heart className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No relationship data available for this supplier.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { relationshipHealth, comparisonToAverage } = healthData;

  return (
    <div className="space-y-6">
      {/* Header with Time Range Selector */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Relationship Health</h3>
          <p className="text-sm text-muted-foreground">{healthData.timeRange}</p>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="3months">Last 3 Months</SelectItem>
            <SelectItem value="6months">Last 6 Months</SelectItem>
            <SelectItem value="1year">Last Year</SelectItem>
            <SelectItem value="2years">Last 2 Years</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overall Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5" />
            Overall Relationship Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className={`text-4xl font-bold ${getScoreColor(relationshipHealth.overallScore)}`}>
                {relationshipHealth.overallScore}/100
              </div>
              {getTrendIcon(relationshipHealth.healthTrend)}
            </div>
            <Badge variant={getRiskBadgeVariant(relationshipHealth.riskLevel)}>
              {relationshipHealth.riskLevel.toUpperCase()} RISK
            </Badge>
          </div>
          <Progress value={relationshipHealth.overallScore} className="mb-4" />
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Industry Average: {comparisonToAverage.industryAverage}</span>
            <span>{comparisonToAverage.percentile}th Percentile</span>
          </div>
        </CardContent>
      </Card>

      {/* Health Dimensions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Communication</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(relationshipHealth.communicationScore)}`}>
              {relationshipHealth.communicationScore}
            </div>
            <Progress value={relationshipHealth.communicationScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reliability</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(relationshipHealth.reliabilityScore)}`}>
              {relationshipHealth.reliabilityScore}
            </div>
            <Progress value={relationshipHealth.reliabilityScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Flexibility</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(relationshipHealth.flexibilityScore)}`}>
              {relationshipHealth.flexibilityScore}
            </div>
            <Progress value={relationshipHealth.flexibilityScore} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Contract Compliance</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getScoreColor(relationshipHealth.keyMetrics.contractCompliance)}`}>
              {relationshipHealth.keyMetrics.contractCompliance}
            </div>
            <Progress value={relationshipHealth.keyMetrics.contractCompliance} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Risk Factors */}
      {relationshipHealth.riskFactors.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Risk Factors</CardTitle>
            <CardDescription>
              Areas requiring attention to maintain healthy supplier relationship
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {relationshipHealth.riskFactors.map((risk, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  {getSeverityIcon(risk.severity)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{risk.factor}</h4>
                      <Badge variant={risk.severity === 'high' ? 'destructive' : risk.severity === 'medium' ? 'secondary' : 'default'}>
                        {risk.severity.toUpperCase()}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{risk.description}</p>
                    <div className="mt-2">
                      <div className="flex items-center justify-between text-xs">
                        <span>Impact</span>
                        <span>{risk.impact}%</span>
                      </div>
                      <Progress value={risk.impact} className="mt-1 h-2" />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Strength Areas */}
      {relationshipHealth.strengthAreas.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Strength Areas</CardTitle>
            <CardDescription>
              Key areas where the supplier excels in the relationship
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {relationshipHealth.strengthAreas.map((strength, index) => (
                <div key={index} className="flex items-center gap-3 p-3 border rounded-lg bg-green-50">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{strength.area}</h4>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        {strength.score}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">{strength.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {relationshipHealth.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Relationship Recommendations</CardTitle>
            <CardDescription>
              Suggested actions to improve supplier relationship health
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {relationshipHealth.recommendations.map((recommendation, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{recommendation}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
