"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import {
  Loader2,
  Plus,
  Edit,
  Trash,
  Star,
  Package,
  DollarSign,
  Clock,
  Alert<PERSON>riangle,
  <PERSON>
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface ProductSupplier {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  purchasePrice: number;
  category?: {
    id: string;
    name: string;
  } | null;
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
  supplierInfo: {
    id: string;
    productId: string;
    supplierId: string;
    supplierProductCode?: string | null;
    supplierProductName?: string | null;
    purchasePrice: number;
    minimumOrderQuantity?: number | null;
    leadTimeDays?: number | null;
    isPreferred: boolean;
    isActive: boolean;
    notes?: string | null;
    createdAt: string;
    updatedAt: string;
  };
}

interface SupplierProductsTabProps {
  supplierId: string;
}

// Form schema for editing product-supplier relationship
const productSupplierSchema = z.object({
  supplierProductCode: z.string().optional(),
  supplierProductName: z.string().optional(),
  purchasePrice: z.number().positive({ message: "Purchase price must be positive" }),
  minimumOrderQuantity: z.number().min(0).optional(),
  leadTimeDays: z.number().int().min(0).optional(),
  isPreferred: z.boolean().default(false),
  isActive: z.boolean().default(true),
  notes: z.string().optional(),
});

type ProductSupplierFormValues = z.infer<typeof productSupplierSchema>;

export function SupplierProductsTab({ supplierId }: SupplierProductsTabProps) {
  const [products, setProducts] = useState<ProductSupplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductSupplier | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ProductSupplierFormValues>({
    resolver: zodResolver(productSupplierSchema),
    defaultValues: {
      supplierProductCode: "",
      supplierProductName: "",
      purchasePrice: 0,
      minimumOrderQuantity: 0,
      leadTimeDays: 0,
      isPreferred: false,
      isActive: true,
      notes: "",
    },
  });

  // Fetch supplier products
  const fetchProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      if (searchTerm) queryParams.append("search", searchTerm);

      const response = await fetch(`/api/suppliers/${supplierId}/products?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch supplier products");
      }

      const data = await response.json();
      setProducts(data.products || []);
    } catch (error) {
      console.error("Error fetching supplier products:", error);
      setError("Failed to load supplier products. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, [supplierId, searchTerm]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchProducts();
  };

  // Open edit dialog
  const handleEditProduct = (productSupplier: ProductSupplier) => {
    setEditingProduct(productSupplier);
    form.reset({
      supplierProductCode: productSupplier.supplierInfo?.supplierProductCode || "",
      supplierProductName: productSupplier.supplierInfo?.supplierProductName || "",
      purchasePrice: productSupplier.supplierInfo?.purchasePrice || 0,
      minimumOrderQuantity: productSupplier.supplierInfo?.minimumOrderQuantity || 0,
      leadTimeDays: productSupplier.supplierInfo?.leadTimeDays || 0,
      isPreferred: productSupplier.supplierInfo?.isPreferred || false,
      isActive: productSupplier.supplierInfo?.isActive || true,
      notes: productSupplier.supplierInfo?.notes || "",
    });
    setIsEditDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: ProductSupplierFormValues) => {
    if (!editingProduct) return;

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/products/${editingProduct.id}/suppliers/${editingProduct.supplierInfo.supplierId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update product supplier");
      }

      toast.success("Product supplier updated successfully");
      await fetchProducts();
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error("Error updating product supplier:", error);
      setError((error as Error).message);
      toast.error("Failed to update product supplier");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter products based on search term
  const filteredProducts = products.filter((productSupplier) => {
    if (!productSupplier) return false;

    const searchLower = searchTerm.toLowerCase();
    const productName = productSupplier.name?.toLowerCase() || '';
    const productSku = productSupplier.sku?.toLowerCase() || '';
    const supplierCode = productSupplier.supplierInfo?.supplierProductCode?.toLowerCase() || '';
    const supplierName = productSupplier.supplierInfo?.supplierProductName?.toLowerCase() || '';

    return productName.includes(searchLower) ||
           productSku.includes(searchLower) ||
           supplierCode.includes(searchLower) ||
           supplierName.includes(searchLower);
  });

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Supplier Products</CardTitle>
          <CardDescription>
            Manage products supplied by this supplier, including pricing and preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-4">
            <form onSubmit={handleSearch} className="flex gap-2">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button type="submit">Search</Button>
            </form>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading products...</span>
            </div>
          ) : filteredProducts.length === 0 ? (
            <div className="text-center p-8 border rounded-lg bg-muted/20">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No products found</h3>
              <p className="text-muted-foreground">
                {searchTerm ? "No products match your search criteria." : "This supplier has no products assigned."}
              </p>
            </div>
          ) : (
            <div className="w-full overflow-x-auto">
              <div className="rounded-md border min-w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Product</TableHead>
                      <TableHead className="min-w-[150px]">Supplier Code</TableHead>
                      <TableHead className="min-w-[120px]">Purchase Price</TableHead>
                      <TableHead className="min-w-[100px]">MOQ</TableHead>
                      <TableHead className="min-w-[100px]">Lead Time</TableHead>
                      <TableHead className="min-w-[150px]">Status</TableHead>
                      <TableHead className="min-w-[80px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredProducts.map((productSupplier) => (
                      <TableRow key={productSupplier.id}>
                        <TableCell className="min-w-[200px]">
                          <div className="space-y-1">
                            <div className="font-medium truncate">{productSupplier.name}</div>
                            <div className="text-sm text-muted-foreground truncate">
                              SKU: {productSupplier.sku}
                            </div>
                            {productSupplier.category && (
                              <Badge variant="outline" className="text-xs">
                                {productSupplier.category.name}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[150px]">
                          <div className="space-y-1">
                            {productSupplier.supplierInfo?.supplierProductCode && (
                              <div className="text-sm font-mono truncate">
                                {productSupplier.supplierInfo.supplierProductCode}
                              </div>
                            )}
                            {productSupplier.supplierInfo?.supplierProductName && (
                              <div className="text-sm text-muted-foreground truncate">
                                {productSupplier.supplierInfo.supplierProductName}
                              </div>
                            )}
                            {!productSupplier.supplierInfo?.supplierProductCode && !productSupplier.supplierInfo?.supplierProductName && (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[120px]">
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                            <span className="font-medium truncate">
                              {formatCurrency(productSupplier.supplierInfo?.purchasePrice || 0)}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[100px]">
                          {productSupplier.supplierInfo?.minimumOrderQuantity ? (
                            <span className="truncate block">
                              {productSupplier.supplierInfo.minimumOrderQuantity} {productSupplier.unit?.name || ''}
                            </span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="min-w-[100px]">
                          {productSupplier.supplierInfo?.leadTimeDays ? (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <span className="truncate">{productSupplier.supplierInfo.leadTimeDays} days</span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="min-w-[150px]">
                          <div className="flex flex-col gap-1">
                            {productSupplier.supplierInfo?.isPreferred && (
                              <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 text-xs">
                                <Star className="h-3 w-3 mr-1 flex-shrink-0" />
                                Preferred
                              </Badge>
                            )}
                            <Badge
                              variant={productSupplier.supplierInfo?.isActive ? "secondary" : "outline"}
                              className={`text-xs ${productSupplier.supplierInfo?.isActive ? "bg-green-100 text-green-800" : ""}`}
                            >
                              {productSupplier.supplierInfo?.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell className="min-w-[80px]">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleEditProduct(productSupplier)}
                            title="Edit Product Supplier"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Product Supplier Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Product Supplier</DialogTitle>
            <DialogDescription>
              Update supplier-specific information for {editingProduct?.name}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="supplierProductCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier Product Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter supplier's product code"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="supplierProductName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Supplier Product Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter supplier's product name"
                          {...field}
                          value={field.value || ""}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="purchasePrice"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purchase Price *</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0.00"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minimumOrderQuantity"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Order Quantity</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="leadTimeDays"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Lead Time (Days)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="0"
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex gap-4">
                <FormField
                  control={form.control}
                  name="isPreferred"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Preferred Supplier</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          Mark as preferred supplier for this product
                        </p>
                      </div>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="isActive"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Active</FormLabel>
                        <p className="text-sm text-muted-foreground">
                          Enable this supplier for this product
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="notes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Notes</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter any additional notes..."
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
