"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  BarChart3
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { SupplierQualityAnalysis } from "@/lib/supplier-quality-metrics";

interface SupplierQualityMetricsProps {
  supplierId: string;
}

export function SupplierQualityMetrics({ supplierId }: SupplierQualityMetricsProps) {
  const [qualityData, setQualityData] = useState<SupplierQualityAnalysis | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("3months");

  const fetchQualityMetrics = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierId}/quality-metrics?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch quality metrics");
      }

      const data = await response.json();
      setQualityData(data.qualityAnalysis);
    } catch (error) {
      console.error("Error fetching quality metrics:", error);
      setError("Failed to load quality metrics. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualityMetrics();
  }, [supplierId, timeRange]);

  const getRiskBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'default';
      case 'medium': return 'secondary';
      case 'high': return 'destructive';
      default: return 'outline';
    }
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'stable': return <Minus className="h-4 w-4 text-gray-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Quality Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading quality metrics...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Quality Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button onClick={fetchQualityMetrics} className="mt-4">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!qualityData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Quality Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground">No quality data available for this supplier.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { metrics, recommendations, riskLevel } = qualityData;

  return (
    <div className="space-y-6">
      {/* Header with Time Range Selector */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Quality Metrics</h3>
          <p className="text-sm text-muted-foreground">{qualityData.timeRange}</p>
        </div>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1month">Last Month</SelectItem>
            <SelectItem value="3months">Last 3 Months</SelectItem>
            <SelectItem value="6months">Last 6 Months</SelectItem>
            <SelectItem value="1year">Last Year</SelectItem>
            <SelectItem value="all">All Time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
            {getTrendIcon(metrics.qualityTrend)}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getQualityScoreColor(metrics.qualityScore)}`}>
              {metrics.qualityScore}/100
            </div>
            <p className="text-xs text-muted-foreground">
              Overall quality rating
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Return Rate</CardTitle>
            <Badge variant={metrics.returnRate > 5 ? "destructive" : "default"}>
              {metrics.returnRate.toFixed(2)}%
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalItemsReturned}</div>
            <p className="text-xs text-muted-foreground">
              of {metrics.totalItemsSold} items sold
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Defect Rate</CardTitle>
            <Badge variant={metrics.defectRate > 2 ? "destructive" : "default"}>
              {metrics.defectRate.toFixed(2)}%
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(metrics.returnValue)}</div>
            <p className="text-xs text-muted-foreground">
              Total return value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Level</CardTitle>
            <Badge variant={getRiskBadgeVariant(riskLevel)}>
              {riskLevel.toUpperCase()}
            </Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.customerSatisfactionScore.toFixed(1)}</div>
            <p className="text-xs text-muted-foreground">
              Customer satisfaction score
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Returns by Reason */}
      {metrics.returnsByReason.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Returns by Reason</CardTitle>
            <CardDescription>
              Breakdown of return reasons for quality analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="w-full overflow-x-auto">
              <div className="min-w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[150px]">Reason</TableHead>
                      <TableHead className="min-w-[80px] text-right">Count</TableHead>
                      <TableHead className="min-w-[100px] text-right">Percentage</TableHead>
                      <TableHead className="min-w-[120px] text-right">Value</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.returnsByReason.map((reason, index) => (
                      <TableRow key={index}>
                        <TableCell className="font-medium min-w-[150px]">
                          <span className="truncate block">{reason.reason}</span>
                        </TableCell>
                        <TableCell className="text-right">{reason.count}</TableCell>
                        <TableCell className="text-right">{reason.percentage.toFixed(1)}%</TableCell>
                        <TableCell className="text-right">{formatCurrency(reason.value)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Quality Recommendations</CardTitle>
            <CardDescription>
              Suggested actions to improve supplier quality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recommendations.map((recommendation, index) => (
                <Alert key={index}>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>{recommendation}</AlertDescription>
                </Alert>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Monthly Trends */}
      {metrics.monthlyTrends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Quality Trends</CardTitle>
            <CardDescription>
              Monthly return rate and value trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.monthlyTrends.map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="font-medium">{trend.month}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="font-semibold">{trend.returnRate.toFixed(2)}%</div>
                      <div className="text-sm text-muted-foreground">Return Rate</div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{formatCurrency(trend.returnValue)}</div>
                      <div className="text-sm text-muted-foreground">Return Value</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
