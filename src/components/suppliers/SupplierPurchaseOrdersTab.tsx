"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  Plus,
  Eye,
  FileText,
  Calendar,
  DollarSign,
  Package,
  AlertTriangle,
  Search,
  Filter
} from "lucide-react";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";
import { format } from "date-fns";

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: string;
  subtotal: number;
  tax: number;
  total: number;
  expectedDeliveryDate?: string | null;
  receivedAt?: string | null;
  approvedAt?: string | null;
  createdBy: {
    name: string;
  };
  _count: {
    items: number;
  };
}

interface SupplierPurchaseOrdersTabProps {
  supplierId: string;
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PENDING_APPROVAL: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  ORDERED: "bg-purple-100 text-purple-800",
  RECEIVED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Pending Approval",
  APPROVED: "Approved",
  ORDERED: "Ordered",
  RECEIVED: "Received",
  CANCELLED: "Cancelled",
};

export function SupplierPurchaseOrdersTab({ supplierId }: SupplierPurchaseOrdersTabProps) {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch purchase orders
  const fetchPurchaseOrders = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      queryParams.append("page", page.toString());
      queryParams.append("limit", "10");
      if (searchTerm) queryParams.append("search", searchTerm);
      if (statusFilter !== "all") queryParams.append("status", statusFilter);

      const response = await fetch(`/api/suppliers/${supplierId}/purchase-orders?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch purchase orders");
      }

      const data = await response.json();
      setPurchaseOrders(data.purchaseOrders || []);
      setTotalPages(data.pagination?.pages || 1);
    } catch (error) {
      console.error("Error fetching purchase orders:", error);
      setError("Failed to load purchase orders. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPurchaseOrders();
  }, [supplierId, page, searchTerm, statusFilter]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchPurchaseOrders();
  };

  // Handle status filter change
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Purchase Orders</CardTitle>
              <CardDescription>
                View and manage purchase orders for this supplier
              </CardDescription>
            </div>
            <Button asChild>
              <Link href={`/inventory/purchase-orders/new?supplier=${supplierId}`}>
                <Plus className="h-4 w-4 mr-2" />
                New Purchase Order
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="mb-4 flex gap-4">
            <form onSubmit={handleSearch} className="flex gap-2 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search purchase orders..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
              <Button type="submit">Search</Button>
            </form>

            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-48">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="ORDERED">Ordered</SelectItem>
                <SelectItem value="RECEIVED">Received</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading purchase orders...</span>
            </div>
          ) : purchaseOrders.length === 0 ? (
            <div className="text-center p-8 border rounded-lg bg-muted/20">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No purchase orders found</h3>
              <p className="text-muted-foreground mb-4">
                {searchTerm || statusFilter !== "all" 
                  ? "No purchase orders match your search criteria." 
                  : "No purchase orders have been created for this supplier yet."}
              </p>
              <Button asChild>
                <Link href={`/inventory/purchase-orders/new?supplier=${supplierId}`}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Purchase Order
                </Link>
              </Button>
            </div>
          ) : (
            <>
              <div className="w-full overflow-x-auto">
                <div className="rounded-md border min-w-full">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="min-w-[120px]">Order ID</TableHead>
                        <TableHead className="min-w-[120px]">Date</TableHead>
                        <TableHead className="min-w-[140px]">Status</TableHead>
                        <TableHead className="min-w-[80px]">Items</TableHead>
                        <TableHead className="min-w-[120px]">Total</TableHead>
                        <TableHead className="min-w-[140px]">Expected Delivery</TableHead>
                        <TableHead className="min-w-[120px]">Created By</TableHead>
                        <TableHead className="min-w-[80px]">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {purchaseOrders.map((po) => (
                        <TableRow key={po.id}>
                          <TableCell className="min-w-[120px]">
                            <div className="font-mono text-sm truncate">
                              PO-{po.id.slice(-8).toUpperCase()}
                            </div>
                          </TableCell>
                          <TableCell className="min-w-[120px]">
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <span className="text-sm truncate">
                                {format(new Date(po.orderDate), "MMM dd, yyyy")}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="min-w-[140px]">
                            <Badge
                              variant="secondary"
                              className={`text-xs ${statusColors[po.status as keyof typeof statusColors]}`}
                            >
                              {statusLabels[po.status as keyof typeof statusLabels]}
                            </Badge>
                          </TableCell>
                          <TableCell className="min-w-[80px]">
                            <div className="flex items-center gap-1">
                              <Package className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <span className="truncate">{po._count.items}</span>
                            </div>
                          </TableCell>
                          <TableCell className="min-w-[120px]">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                              <span className="font-medium truncate">
                                {formatCurrency(po.total)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="min-w-[140px]">
                            {po.expectedDeliveryDate ? (
                              <span className="text-sm truncate block">
                                {format(new Date(po.expectedDeliveryDate), "MMM dd, yyyy")}
                              </span>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                          <TableCell className="min-w-[120px]">
                            <span className="text-sm truncate block">{po.createdBy.name}</span>
                          </TableCell>
                          <TableCell className="min-w-[80px]">
                            <Button
                              variant="ghost"
                              size="icon"
                              asChild
                              title="View Purchase Order"
                            >
                              <Link href={`/inventory/purchase-orders/${po.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page - 1)}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-3 text-sm">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(page + 1)}
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
