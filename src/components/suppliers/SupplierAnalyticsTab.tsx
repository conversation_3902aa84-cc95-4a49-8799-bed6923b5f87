"use client";

import { useState, useEffect } from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Loader2,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Package,
  Clock,
  Calendar,
  AlertTriangle,
  Star,
  Target
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { format, subDays, subMonths } from "date-fns";
import { SupplierQualityMetrics } from "./SupplierQualityMetrics";
import { PricingTrendsChart } from "./PricingTrendsChart";
import { RelationshipHealthCard } from "./RelationshipHealthCard";

interface SupplierAnalytics {
  totalSpent: number;
  totalOrders: number;
  averageOrderValue: number;
  averageDeliveryTime: number;
  onTimeDeliveryRate: number;
  totalProducts: number;
  preferredProducts: number;
  lastOrderDate: string | null;
  monthlySpending: Array<{
    month: string;
    amount: number;
  }>;
  topProducts: Array<{
    productId: string;
    productName: string;
    totalQuantity: number;
    totalSpent: number;
    averagePrice: number;
    lastOrderDate: string;
  }>;
  performanceMetrics: {
    qualityScore: number;
    reliabilityScore: number;
    costEffectivenessScore: number;
    overallScore: number;
  };
}

interface SupplierAnalyticsTabProps {
  supplierId: string;
}

export function SupplierAnalyticsTab({ supplierId }: SupplierAnalyticsTabProps) {
  const [analytics, setAnalytics] = useState<SupplierAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("12months");

  // Fetch analytics data
  const fetchAnalytics = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierId}/analytics?timeRange=${timeRange}`);

      if (!response.ok) {
        throw new Error("Failed to fetch supplier analytics");
      }

      const data = await response.json();
      setAnalytics(data.analytics);
    } catch (error) {
      console.error("Error fetching supplier analytics:", error);
      setError("Failed to load supplier analytics. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [supplierId, timeRange]);

  // Get performance color based on score
  const getPerformanceColor = (score: number) => {
    if (score >= 80) return "bg-green-100 text-green-800";
    if (score >= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-red-100 text-red-800";
  };

  // Get trend icon based on value
  const getTrendIcon = (value: number, isPositive: boolean = true) => {
    const isGood = isPositive ? value > 0 : value < 0;
    return isGood ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading analytics...</span>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error || "Failed to load analytics data"}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6 w-full max-w-full overflow-hidden">
      {/* Time Range Selector */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="min-w-0 flex-1">
          <h3 className="text-lg font-medium">Supplier Performance Analytics</h3>
          <p className="text-sm text-muted-foreground">
            Performance metrics and insights for the selected time period
          </p>
        </div>
        <div className="flex-shrink-0">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="3months">Last 3 Months</SelectItem>
              <SelectItem value="6months">Last 6 Months</SelectItem>
              <SelectItem value="12months">Last 12 Months</SelectItem>
              <SelectItem value="all">All Time</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalSpent)}</div>
            <p className="text-xs text-muted-foreground">
              Across {analytics.totalOrders} orders
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.averageOrderValue)}</div>
            <p className="text-xs text-muted-foreground">
              Per purchase order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Delivery Performance</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.onTimeDeliveryRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              On-time delivery rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Product Portfolio</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              {analytics.preferredProducts} preferred
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Performance Scores */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Scores</CardTitle>
          <CardDescription>
            Overall supplier performance evaluation based on various metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
            <div className="text-center">
              <div className="text-3xl font-bold mb-2">
                {analytics.performanceMetrics.overallScore}
              </div>
              <Badge 
                variant="secondary" 
                className={getPerformanceColor(analytics.performanceMetrics.overallScore)}
              >
                Overall Score
              </Badge>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-semibold mb-2">
                {analytics.performanceMetrics.qualityScore}
              </div>
              <Badge 
                variant="outline" 
                className={getPerformanceColor(analytics.performanceMetrics.qualityScore)}
              >
                Quality
              </Badge>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-semibold mb-2">
                {analytics.performanceMetrics.reliabilityScore}
              </div>
              <Badge 
                variant="outline" 
                className={getPerformanceColor(analytics.performanceMetrics.reliabilityScore)}
              >
                Reliability
              </Badge>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-semibold mb-2">
                {analytics.performanceMetrics.costEffectivenessScore}
              </div>
              <Badge 
                variant="outline" 
                className={getPerformanceColor(analytics.performanceMetrics.costEffectivenessScore)}
              >
                Cost Effectiveness
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Top Products */}
      <Card>
        <CardHeader>
          <CardTitle>Top Products by Spending</CardTitle>
          <CardDescription>
            Most frequently ordered products from this supplier
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analytics.topProducts.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No product data available for the selected time range.</p>
            </div>
          ) : (
            <div className="w-full overflow-x-auto">
              <div className="rounded-md border min-w-full">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Product</TableHead>
                      <TableHead className="min-w-[100px] text-right">Quantity</TableHead>
                      <TableHead className="min-w-[120px] text-right">Total Spent</TableHead>
                      <TableHead className="min-w-[100px] text-right">Avg Price</TableHead>
                      <TableHead className="min-w-[120px] text-right">Last Order</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analytics.topProducts.map((product, index) => (
                      <TableRow key={product.productId}>
                        <TableCell className="min-w-[200px]">
                          <div className="flex items-center gap-2">
                            {index < 3 && (
                              <Star className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                            )}
                            <span className="font-medium truncate">{product.productName}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">{product.totalQuantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.totalSpent)}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.averagePrice)}</TableCell>
                        <TableCell className="text-right">
                          {format(new Date(product.lastOrderDate), "MMM dd, yyyy")}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Monthly Spending Trend */}
      <Card>
        <CardHeader>
          <CardTitle>Monthly Spending Trend</CardTitle>
          <CardDescription>
            Spending pattern over the selected time period
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analytics.monthlySpending.length === 0 ? (
            <div className="text-center py-8">
              <TrendingUp className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No spending data available for the selected time range.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {analytics.monthlySpending.map((month, index) => {
                const prevMonth = index > 0 ? analytics.monthlySpending[index - 1] : null;
                const change = prevMonth ? ((month.amount - prevMonth.amount) / prevMonth.amount) * 100 : 0;
                
                return (
                  <div key={month.month} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="font-medium">{month.month}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="font-semibold">{formatCurrency(month.amount)}</span>
                      {prevMonth && (
                        <div className="flex items-center gap-1">
                          {getTrendIcon(change)}
                          <span className={`text-sm ${change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {Math.abs(change).toFixed(1)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Quality Metrics Section */}
      <SupplierQualityMetrics supplierId={supplierId} />

      {/* Pricing Trends Section */}
      <PricingTrendsChart supplierId={supplierId} />

      {/* Relationship Health Section */}
      <RelationshipHealthCard supplierId={supplierId} />
    </div>
  );
}
