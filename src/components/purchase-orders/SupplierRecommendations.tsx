"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown, 
  Star, 
  Clock, 
  Package, 
  AlertTriangle,
  CheckCircle,
  Info,
  Loader2,
  RefreshCw
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface POItem {
  productId: string;
  quantity: number;
}

interface SupplierScore {
  supplierId: string;
  supplierName: string;
  productSupplierId: string;
  totalScore: number;
  breakdown: {
    priceScore: number;
    qualityScore: number;
    deliveryScore: number;
    moqScore: number;
    preferredScore: number;
  };
  reasoning: string[];
  purchasePrice: number;
  minimumOrderQuantity?: number;
  leadTimeDays?: number;
  isPreferred: boolean;
}

interface SupplierRecommendation {
  productId: string;
  productName: string;
  requestedQuantity: number;
  recommendedSupplier: SupplierScore;
  alternativeSuppliers: SupplierScore[];
  recommendation: {
    confidence: 'high' | 'medium' | 'low';
    reasoning: string[];
    warnings: string[];
  };
}

interface SupplierGroup {
  supplier: { id: string; name: string };
  items: Array<{
    productId: string;
    productName: string;
    quantity: number;
    recommendation: SupplierRecommendation;
  }>;
  totalScore: number;
  totalValue: number;
  confidence: 'high' | 'medium' | 'low';
  warnings: string[];
}

interface SupplierRecommendationsProps {
  items: POItem[];
  onSupplierSelect?: (supplierId: string, supplierName: string) => void;
  onRecommendationApply?: (recommendations: SupplierRecommendation[]) => void;
}

export default function SupplierRecommendations({ 
  items, 
  onSupplierSelect, 
  onRecommendationApply 
}: SupplierRecommendationsProps) {
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<SupplierRecommendation[]>([]);
  const [supplierGroups, setSupplierGroups] = useState<SupplierGroup[]>([]);
  const [poSplittingRecommendations, setPOSplittingRecommendations] = useState<any>(null);
  const [summary, setSummary] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch recommendations when items change
  useEffect(() => {
    if (items.length > 0) {
      fetchRecommendations();
    } else {
      // Clear recommendations when no items
      setRecommendations([]);
      setSupplierGroups([]);
      setPOSplittingRecommendations(null);
      setSummary(null);
    }
  }, [items]);

  const fetchRecommendations = async () => {
    if (items.length === 0) return;

    // Validate items before sending
    const validItems = items.filter(item =>
      item.productId &&
      typeof item.productId === 'string' &&
      item.productId.trim() !== '' &&
      item.quantity &&
      typeof item.quantity === 'number' &&
      item.quantity > 0
    );

    if (validItems.length === 0) {
      setError('No valid items to analyze');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching recommendations for items:', validItems);

      const response = await fetch('/api/purchase-orders/supplier-recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ items: validItems }),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('API Error:', errorData);
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch supplier recommendations`);
      }

      const data = await response.json();
      console.log('Recommendations data:', data);

      setRecommendations(data.itemRecommendations || []);
      setSupplierGroups(data.supplierGroups || []);
      setPOSplittingRecommendations(data.poSplittingRecommendations);
      setSummary(data.summary);
    } catch (error) {
      console.error('Error fetching recommendations:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to load supplier recommendations';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceBadgeVariant = (confidence: string) => {
    switch (confidence) {
      case 'high': return 'default';
      case 'medium': return 'secondary';
      case 'low': return 'destructive';
      default: return 'outline';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleSupplierSelect = (supplierId: string, supplierName: string) => {
    onSupplierSelect?.(supplierId, supplierName);
  };

  const handleApplyRecommendations = () => {
    onRecommendationApply?.(recommendations);
  };

  if (items.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Supplier Recommendations
          </CardTitle>
          <CardDescription>
            Add products to see intelligent supplier recommendations
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5" />
                Supplier Recommendations
              </CardTitle>
              <CardDescription>
                AI-powered supplier selection based on price, quality, delivery, and MOQ
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchRecommendations}
              disabled={loading}
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Analyzing suppliers...</span>
            </div>
          ) : summary ? (
            <div className="space-y-6">
              {/* Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold">{summary.uniqueSuppliers}</div>
                  <div className="text-sm text-muted-foreground">Suppliers</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{summary.recommendationsFound}</div>
                  <div className="text-sm text-muted-foreground">Products</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold capitalize">{summary.averageConfidence}</div>
                  <div className="text-sm text-muted-foreground">Confidence</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{formatCurrency(summary.totalEstimatedValue)}</div>
                  <div className="text-sm text-muted-foreground">Est. Value</div>
                </div>
              </div>

              <Separator />

              {/* PO Splitting Recommendations */}
              {poSplittingRecommendations && (
                <div>
                  <h3 className="text-lg font-semibold mb-3">Purchase Order Strategy</h3>
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">
                          Recommended: {poSplittingRecommendations.strategy === 'single' ? 'Single Supplier' : 'Split Across Suppliers'}
                        </div>
                        <ul className="list-disc list-inside space-y-1">
                          {poSplittingRecommendations.reasoning.map((reason: string, index: number) => (
                            <li key={index} className="text-sm">{reason}</li>
                          ))}
                        </ul>
                      </div>
                    </AlertDescription>
                  </Alert>
                </div>
              )}

              {/* Supplier Groups */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Recommended Suppliers</h3>
                <div className="space-y-4">
                  {supplierGroups.map((group, index) => (
                    <Card key={group.supplier.id} className={index === 0 ? "border-primary" : ""}>
                      <CardContent className="pt-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <div>
                              <h4 className="font-semibold">{group.supplier.name}</h4>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>{group.items.length} products</span>
                                <span>•</span>
                                <span className={getScoreColor(group.totalScore)}>
                                  Score: {group.totalScore}/100
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge variant={getConfidenceBadgeVariant(group.confidence)}>
                              {group.confidence} confidence
                            </Badge>
                            {index === 0 && (
                              <Badge variant="outline">
                                <Star className="h-3 w-3 mr-1" />
                                Recommended
                              </Badge>
                            )}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-3">
                          <div>
                            <div className="text-sm text-muted-foreground">Total Value</div>
                            <div className="font-medium">{formatCurrency(group.totalValue)}</div>
                          </div>
                          <div>
                            <div className="text-sm text-muted-foreground">Products</div>
                            <div className="font-medium">{group.items.length}</div>
                          </div>
                        </div>

                        {group.warnings.length > 0 && (
                          <Alert className="mb-3">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription>
                              <ul className="list-disc list-inside space-y-1">
                                {group.warnings.map((warning, idx) => (
                                  <li key={idx} className="text-sm">{warning}</li>
                                ))}
                              </ul>
                            </AlertDescription>
                          </Alert>
                        )}

                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            onClick={() => handleSupplierSelect(group.supplier.id, group.supplier.name)}
                          >
                            Select Supplier
                          </Button>
                          {index === 0 && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleApplyRecommendations}
                            >
                              Apply All Recommendations
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          ) : null}
        </CardContent>
      </Card>
    </div>
  );
}
