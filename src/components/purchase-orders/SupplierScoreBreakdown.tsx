"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  TrendingUp, 
  TrendingDown, 
  Star, 
  Clock, 
  Package, 
  DollarSign,
  Award,
  Info,
  ChevronRight
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";

interface SupplierScore {
  supplierId: string;
  supplierName: string;
  productSupplierId: string;
  totalScore: number;
  breakdown: {
    priceScore: number;
    qualityScore: number;
    deliveryScore: number;
    moqScore: number;
    preferredScore: number;
  };
  reasoning: string[];
  purchasePrice: number;
  minimumOrderQuantity?: number;
  leadTimeDays?: number;
  isPreferred: boolean;
  qualityMetrics?: {
    returnRate: number;
    defectRate: number;
    qualityScore: number;
    riskLevel: string;
  };
  deliveryMetrics?: {
    averageDeliveryDays: number;
    onTimeDeliveryRate: number;
    totalDeliveries: number;
  };
}

interface SupplierScoreBreakdownProps {
  supplier: SupplierScore;
  productName: string;
  requestedQuantity: number;
  rank?: number;
}

export default function SupplierScoreBreakdown({ 
  supplier, 
  productName, 
  requestedQuantity,
  rank 
}: SupplierScoreBreakdownProps) {
  const [detailsOpen, setDetailsOpen] = useState(false);

  const getScoreColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreIcon = (score: number) => {
    if (score >= 85) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (score >= 70) return <TrendingUp className="h-4 w-4 text-yellow-600" />;
    return <TrendingDown className="h-4 w-4 text-red-600" />;
  };

  const scoreCategories = [
    {
      name: 'Price Competitiveness',
      score: supplier.breakdown.priceScore,
      weight: 30,
      icon: <DollarSign className="h-4 w-4" />,
      description: 'How competitive the price is compared to other suppliers',
    },
    {
      name: 'Quality Performance',
      score: supplier.breakdown.qualityScore,
      weight: 25,
      icon: <Award className="h-4 w-4" />,
      description: 'Based on return rates, defect rates, and quality history',
    },
    {
      name: 'Delivery Reliability',
      score: supplier.breakdown.deliveryScore,
      weight: 20,
      icon: <Clock className="h-4 w-4" />,
      description: 'On-time delivery rate and average delivery speed',
    },
    {
      name: 'MOQ Compatibility',
      score: supplier.breakdown.moqScore,
      weight: 15,
      icon: <Package className="h-4 w-4" />,
      description: 'How well the order quantity matches minimum requirements',
    },
    {
      name: 'Preferred Status',
      score: supplier.breakdown.preferredScore,
      weight: 10,
      icon: <Star className="h-4 w-4" />,
      description: 'Bonus for being designated as preferred supplier',
    },
  ];

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {rank && (
              <div className="flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary font-bold">
                {rank}
              </div>
            )}
            <div>
              <CardTitle className="text-lg">{supplier.supplierName}</CardTitle>
              <CardDescription>
                {productName} • Qty: {requestedQuantity}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className="text-right">
              <div className={`text-2xl font-bold ${getScoreColor(supplier.totalScore)}`}>
                {supplier.totalScore}
              </div>
              <div className="text-sm text-muted-foreground">/ 100</div>
            </div>
            {getScoreIcon(supplier.totalScore)}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
          <div>
            <div className="text-muted-foreground">Price</div>
            <div className="font-medium">{formatCurrency(supplier.purchasePrice)}</div>
          </div>
          {supplier.minimumOrderQuantity && (
            <div>
              <div className="text-muted-foreground">MOQ</div>
              <div className="font-medium">{supplier.minimumOrderQuantity}</div>
            </div>
          )}
          {supplier.leadTimeDays && (
            <div>
              <div className="text-muted-foreground">Lead Time</div>
              <div className="font-medium">{supplier.leadTimeDays} days</div>
            </div>
          )}
        </div>

        {/* Badges */}
        <div className="flex flex-wrap gap-2">
          {supplier.isPreferred && (
            <Badge variant="secondary">
              <Star className="h-3 w-3 mr-1" />
              Preferred
            </Badge>
          )}
          {supplier.qualityMetrics && (
            <Badge variant={supplier.qualityMetrics.riskLevel === 'low' ? 'default' : 'destructive'}>
              {supplier.qualityMetrics.riskLevel} risk
            </Badge>
          )}
        </div>

        {/* Score Breakdown */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Score Breakdown</span>
            <Dialog open={detailsOpen} onOpenChange={setDetailsOpen}>
              <DialogTrigger asChild>
                <Button variant="ghost" size="sm">
                  Details <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>{supplier.supplierName} - Detailed Analysis</DialogTitle>
                  <DialogDescription>
                    Comprehensive scoring breakdown for {productName}
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-6">
                  {/* Detailed Score Categories */}
                  <div className="space-y-4">
                    {scoreCategories.map((category) => (
                      <div key={category.name} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {category.icon}
                            <span className="font-medium">{category.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {category.weight}%
                            </Badge>
                          </div>
                          <span className={`font-bold ${getScoreColor(category.score)}`}>
                            {category.score}/100
                          </span>
                        </div>
                        <Progress value={category.score} className="h-2" />
                        <p className="text-sm text-muted-foreground">{category.description}</p>
                      </div>
                    ))}
                  </div>

                  <Separator />

                  {/* Reasoning */}
                  <div>
                    <h4 className="font-medium mb-2">Key Factors</h4>
                    <ul className="space-y-1">
                      {supplier.reasoning.map((reason, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <Info className="h-3 w-3 mt-0.5 text-muted-foreground flex-shrink-0" />
                          {reason}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Performance Metrics */}
                  {(supplier.qualityMetrics || supplier.deliveryMetrics) && (
                    <>
                      <Separator />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {supplier.qualityMetrics && (
                          <div>
                            <h4 className="font-medium mb-3">Quality Metrics</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span>Return Rate:</span>
                                <span>{supplier.qualityMetrics.returnRate.toFixed(1)}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Defect Rate:</span>
                                <span>{supplier.qualityMetrics.defectRate.toFixed(1)}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Quality Score:</span>
                                <span className={getScoreColor(supplier.qualityMetrics.qualityScore)}>
                                  {supplier.qualityMetrics.qualityScore}/100
                                </span>
                              </div>
                            </div>
                          </div>
                        )}

                        {supplier.deliveryMetrics && (
                          <div>
                            <h4 className="font-medium mb-3">Delivery Metrics</h4>
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span>Avg Delivery:</span>
                                <span>{supplier.deliveryMetrics.averageDeliveryDays} days</span>
                              </div>
                              <div className="flex justify-between">
                                <span>On-Time Rate:</span>
                                <span>{supplier.deliveryMetrics.onTimeDeliveryRate}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Total Orders:</span>
                                <span>{supplier.deliveryMetrics.totalDeliveries}</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </>
                  )}
                </div>
              </DialogContent>
            </Dialog>
          </div>
          
          {/* Quick Score Bars */}
          <div className="space-y-2">
            {scoreCategories.slice(0, 3).map((category) => (
              <div key={category.name} className="flex items-center gap-3">
                <div className="w-20 text-xs text-muted-foreground truncate">
                  {category.name.split(' ')[0]}
                </div>
                <Progress value={category.score} className="flex-1 h-1.5" />
                <div className="w-8 text-xs text-right">{category.score}</div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
