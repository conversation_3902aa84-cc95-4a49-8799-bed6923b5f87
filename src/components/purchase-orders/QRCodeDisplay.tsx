"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Download, 
  Printer, 
  QrCode, 
  Package, 
  Calendar, 
  Building2,
  Hash,
  DollarSign,
  Info,
  Loader2
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface QRCodeData {
  type: string;
  version: string;
  generatedAt: string;
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    total: number;
    supplier: {
      name: string;
      contactPerson: string | null;
      phone: string | null;
    };
  };
  batches: Array<{
    id: string;
    batchNumber: string | null;
    receivedDate: string;
    expiryDate: string | null;
    quantity: number;
    remainingQuantity: number;
    purchasePrice: number;
    product: {
      id: string;
      name: string;
      sku: string;
      barcode: string | null;
    };
    supplier: {
      name: string;
    };
    notes: string | null;
  }>;
  summary: {
    totalBatches: number;
    totalProducts: number;
    totalValue: number;
  };
  humanReadable: {
    poNumber: string;
    supplier: string;
    receivedDate: string;
    batchCount: number;
    productList: string[];
  };
}

interface QRCodeDisplayProps {
  purchaseOrderId: string;
  onClose?: () => void;
}

export function QRCodeDisplay({ purchaseOrderId, onClose }: QRCodeDisplayProps) {
  const [qrCodeImage, setQRCodeImage] = useState<string | null>(null);
  const [qrData, setQRData] = useState<QRCodeData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const qrRef = useRef<HTMLDivElement>(null);

  const generateQRCode = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}/qr-code?format=image`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate QR code');
      }

      const data = await response.json();
      setQRCodeImage(data.qrCodeImage);
      setQRData(data.data);
      
      toast.success('QR code generated successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate QR code';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeImage || !qrData) return;

    const link = document.createElement('a');
    link.href = qrCodeImage;
    link.download = `PO-${qrData.humanReadable.poNumber}-QR-Code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('QR code downloaded successfully');
  };

  const printQRCode = () => {
    if (!qrCodeImage || !qrData) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast.error('Unable to open print window. Please check your popup blocker settings.');
      return;
    }

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>QR Code - ${qrData.humanReadable.poNumber}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              text-align: center;
            }
            .qr-container {
              display: inline-block;
              border: 2px solid #000;
              padding: 20px;
              margin: 20px;
              background: white;
            }
            .qr-image {
              display: block;
              margin: 0 auto 15px;
            }
            .info {
              text-align: left;
              font-size: 12px;
              line-height: 1.4;
              max-width: 300px;
            }
            .title {
              font-size: 16px;
              font-weight: bold;
              margin-bottom: 10px;
              text-align: center;
            }
            @media print {
              body { margin: 0; }
              .qr-container { border: 1px solid #000; }
            }
          </style>
        </head>
        <body>
          <div class="qr-container">
            <div class="title">Purchase Order QR Code</div>
            <img src="${qrCodeImage}" alt="QR Code" class="qr-image" width="200" height="200" />
            <div class="info">
              <strong>PO:</strong> ${qrData.humanReadable.poNumber}<br>
              <strong>Supplier:</strong> ${qrData.humanReadable.supplier}<br>
              <strong>Received:</strong> ${qrData.humanReadable.receivedDate}<br>
              <strong>Batches:</strong> ${qrData.humanReadable.batchCount}<br>
              <strong>Products:</strong> ${qrData.summary.totalProducts}<br>
              <strong>Generated:</strong> ${new Date(qrData.generatedAt).toLocaleString()}
            </div>
          </div>
          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
          </script>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    
    toast.success('QR code sent to printer');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <QrCode className="h-6 w-6" />
          <h2 className="text-xl font-semibold">Purchase Order QR Code</h2>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Generate Button */}
      {!qrCodeImage && !isLoading && (
        <Card>
          <CardHeader>
            <CardTitle>Generate QR Code</CardTitle>
            <CardDescription>
              Create a QR code containing all batch information for this Purchase Order.
              The QR code can be printed as stickers for physical inventory management.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={generateQRCode} className="w-full">
              <QrCode className="h-4 w-4 mr-2" />
              Generate QR Code
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Generating QR code...
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <Info className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* QR Code Display */}
      {qrCodeImage && qrData && (
        <div className="space-y-6">
          {/* QR Code Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div ref={qrRef} className="flex flex-col items-center space-y-4">
                <div className="bg-white p-4 rounded-lg border-2 border-gray-200">
                  <img 
                    src={qrCodeImage} 
                    alt="Purchase Order QR Code" 
                    className="w-64 h-64"
                  />
                </div>
                
                {/* Action Buttons */}
                <div className="flex gap-2">
                  <Button onClick={downloadQRCode} variant="outline">
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                  <Button onClick={printQRCode} variant="outline">
                    <Printer className="h-4 w-4 mr-2" />
                    Print
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Human Readable Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                QR Code Information
              </CardTitle>
              <CardDescription>
                Human-readable backup information in case QR scanning fails
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* PO Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">PO Number:</span>
                    <span className="font-mono">{qrData.humanReadable.poNumber}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Supplier:</span>
                    <span>{qrData.humanReadable.supplier}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Received:</span>
                    <span>{qrData.humanReadable.receivedDate}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Batches:</span>
                    <Badge variant="secondary">{qrData.summary.totalBatches}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Products:</span>
                    <Badge variant="secondary">{qrData.summary.totalProducts}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Value:</span>
                    <span>{formatCurrency(qrData.summary.totalValue)}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Product List Preview */}
              <div>
                <h4 className="font-medium mb-2">Products (showing first 5):</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {qrData.humanReadable.productList.map((product, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center">
                        {index + 1}
                      </span>
                      {product}
                    </li>
                  ))}
                  {qrData.summary.totalProducts > 5 && (
                    <li className="text-xs text-muted-foreground italic">
                      ... and {qrData.summary.totalProducts - 5} more products
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
