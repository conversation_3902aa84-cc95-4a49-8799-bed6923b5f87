"use client";

import { useState, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  Download,
  Printer,
  QrCode,
  Package,
  Calendar,
  Building2,
  Hash,
  DollarSign,
  Info,
  Loader2,
  Grid3X3
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import { toast } from "sonner";

interface QRCodeData {
  type: string;
  version: string;
  generatedAt: string;
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    total: number;
    supplier: {
      name: string;
      contactPerson: string | null;
      phone: string | null;
    };
  };
  batches: Array<{
    id: string;
    batchNumber: string | null;
    receivedDate: string;
    expiryDate: string | null;
    quantity: number;
    remainingQuantity: number;
    purchasePrice: number;
    product: {
      id: string;
      name: string;
      sku: string;
      barcode: string | null;
    };
    supplier: {
      name: string;
    };
    notes: string | null;
  }>;
  summary: {
    totalBatches: number;
    totalProducts: number;
    totalValue: number;
  };
  humanReadable: {
    poNumber: string;
    supplier: string;
    receivedDate: string;
    batchCount: number;
    productList: string[];
  };
}

interface QRCodeDisplayProps {
  purchaseOrderId: string;
  onClose?: () => void;
}

export function QRCodeDisplay({ purchaseOrderId, onClose }: QRCodeDisplayProps) {
  const [qrCodeImage, setQRCodeImage] = useState<string | null>(null);
  const [qrData, setQRData] = useState<QRCodeData | null>(null);
  const [qrContent, setQRContent] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [printQuantity, setPrintQuantity] = useState<number>(1);
  const qrRef = useRef<HTMLDivElement>(null);

  // Print quantity options with grid configurations
  const printOptions = [
    { value: 1, label: "1 copy", grid: "1x1", size: 400 },
    { value: 4, label: "4 copies", grid: "2x2", size: 180 },
    { value: 9, label: "9 copies", grid: "3x3", size: 120 },
    { value: 16, label: "16 copies", grid: "4x4", size: 90 },
    { value: 25, label: "25 copies", grid: "5x5", size: 72 },
  ];

  const generateQRCode = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}/qr-code?format=image`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate QR code');
      }

      const data = await response.json();
      setQRCodeImage(data.qrCodeImage);
      setQRData(data.data);
      setQRContent(data.qrContent);

      toast.success('QR code generated successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate QR code';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeImage || !qrData) return;

    const link = document.createElement('a');
    link.href = qrCodeImage;
    link.download = `PO-${qrData.humanReadable.poNumber}-QR-Code.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    toast.success('QR code downloaded successfully');
  };

  const printQRCode = () => {
    if (!qrCodeImage || !qrData) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast.error('Unable to open print window. Please check your popup blocker settings.');
      return;
    }

    const selectedOption = printOptions.find(opt => opt.value === printQuantity);
    if (!selectedOption) return;

    const gridSize = Math.sqrt(printQuantity);
    const qrSize = selectedOption.size;

    // Generate grid of QR codes
    const qrCodeGrid = Array(printQuantity).fill(0).map((_, index) => `
      <div class="qr-item">
        <img src="${qrCodeImage}" alt="QR Code ${index + 1}" width="${qrSize}" height="${qrSize}" />
        <div class="qr-label">${qrData.humanReadable.poNumber}</div>
      </div>
    `).join('');

    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>QR Codes - ${qrData.humanReadable.poNumber} (${printQuantity} copies)</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            body {
              font-family: Arial, sans-serif;
              padding: 10mm;
              background: white;
            }
            .page-title {
              text-align: center;
              font-size: 14px;
              font-weight: bold;
              margin-bottom: 10mm;
              color: #333;
            }
            .qr-grid {
              display: grid;
              grid-template-columns: repeat(${gridSize}, 1fr);
              gap: ${printQuantity === 1 ? '0' : '5mm'};
              justify-items: center;
              align-items: center;
              width: 100%;
              max-width: 190mm;
              margin: 0 auto;
            }
            .qr-item {
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border: ${printQuantity === 1 ? '2px' : '1px'} solid #000;
              padding: ${printQuantity === 1 ? '10mm' : '3mm'};
              background: white;
              page-break-inside: avoid;
              width: 100%;
              aspect-ratio: 1;
            }
            .qr-item img {
              display: block;
              margin-bottom: ${printQuantity === 1 ? '5mm' : '2mm'};
            }
            .qr-label {
              font-size: ${printQuantity === 1 ? '12px' : printQuantity <= 4 ? '10px' : '8px'};
              font-weight: bold;
              text-align: center;
              color: #000;
              white-space: nowrap;
            }
            .print-info {
              margin-top: 10mm;
              text-align: center;
              font-size: 10px;
              color: #666;
            }
            @media print {
              body {
                margin: 0;
                padding: 5mm;
              }
              .page-title {
                margin-bottom: 5mm;
              }
              .print-info {
                margin-top: 5mm;
              }
            }
          </style>
        </head>
        <body>
          <div class="page-title">
            Purchase Order QR Codes - ${qrData.humanReadable.poNumber}
            <br>
            <span style="font-size: 12px; font-weight: normal;">
              ${qrData.humanReadable.supplier} | ${printQuantity} ${printQuantity === 1 ? 'copy' : 'copies'} | ${selectedOption.grid}
            </span>
          </div>

          <div class="qr-grid">
            ${qrCodeGrid}
          </div>

          <div class="print-info">
            Generated: ${new Date().toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })} | NPOS Inventory System
          </div>

          <script>
            window.onload = function() {
              setTimeout(function() {
                window.print();
              }, 500);
            };
          </script>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    toast.success(`${printQuantity} QR code${printQuantity === 1 ? '' : 's'} sent to printer`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <QrCode className="h-6 w-6" />
          <h2 className="text-xl font-semibold">Purchase Order QR Code</h2>
        </div>
        {onClose && (
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        )}
      </div>

      {/* Generate Button */}
      {!qrCodeImage && !isLoading && (
        <Card>
          <CardHeader>
            <CardTitle>Generate QR Code</CardTitle>
            <CardDescription>
              Create a QR code containing all batch information for this Purchase Order.
              The QR code can be printed as stickers for physical inventory management.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={generateQRCode} className="w-full">
              <QrCode className="h-4 w-4 mr-2" />
              Generate QR Code
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            Generating QR code...
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && (
        <Alert variant="destructive">
          <Info className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* QR Code Display */}
      {qrCodeImage && qrData && (
        <div className="space-y-6">
          {/* QR Code Image */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div ref={qrRef} className="flex flex-col items-center space-y-4">
                <div className="bg-white p-4 rounded-lg border-2 border-gray-200">
                  <img 
                    src={qrCodeImage} 
                    alt="Purchase Order QR Code" 
                    className="w-64 h-64"
                  />
                </div>
                
                {/* Print Quantity Selector */}
                <div className="flex flex-col items-center space-y-4">
                  <div className="flex items-center space-x-3">
                    <Label htmlFor="print-quantity" className="text-sm font-medium">
                      Print Quantity:
                    </Label>
                    <Select value={printQuantity.toString()} onValueChange={(value) => setPrintQuantity(parseInt(value))}>
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {printOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value.toString()}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Badge variant="outline" className="text-xs">
                      {printOptions.find(opt => opt.value === printQuantity)?.grid}
                    </Badge>
                  </div>

                  {/* Print Layout Preview */}
                  {printQuantity > 1 && (
                    <div className="flex flex-col items-center space-y-2">
                      <Label className="text-xs text-muted-foreground">Print Layout Preview:</Label>
                      <div
                        className="grid gap-1 p-2 border rounded bg-gray-50"
                        style={{
                          gridTemplateColumns: `repeat(${Math.sqrt(printQuantity)}, 1fr)`,
                          width: 'fit-content'
                        }}
                      >
                        {Array(printQuantity).fill(0).map((_, index) => (
                          <div
                            key={index}
                            className="w-6 h-6 bg-gray-300 border border-gray-400 rounded-sm flex items-center justify-center"
                          >
                            <Grid3X3 className="w-3 h-3 text-gray-600" />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Button onClick={downloadQRCode} variant="outline">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button onClick={printQRCode} variant="outline">
                      <Printer className="h-4 w-4 mr-2" />
                      Print {printQuantity > 1 ? `${printQuantity} copies` : ''}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* QR Code Content Preview */}
          {qrContent && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <QrCode className="h-5 w-5" />
                  Scanned Content Preview
                </CardTitle>
                <CardDescription>
                  This is what warehouse staff will see when scanning the QR code
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-50 p-4 rounded-lg border">
                  <pre className="text-sm font-mono whitespace-pre-wrap text-gray-800 leading-relaxed">
                    {qrContent}
                  </pre>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Human Readable Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                Additional Information
              </CardTitle>
              <CardDescription>
                Detailed batch information for reference
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* PO Summary */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">PO Number:</span>
                    <span className="font-mono">{qrData.humanReadable.poNumber}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Supplier:</span>
                    <span>{qrData.humanReadable.supplier}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Received:</span>
                    <span>{qrData.humanReadable.receivedDate}</span>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Batches:</span>
                    <Badge variant="secondary">{qrData.summary.totalBatches}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Products:</span>
                    <Badge variant="secondary">{qrData.summary.totalProducts}</Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Total Value:</span>
                    <span>{formatCurrency(qrData.summary.totalValue)}</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Product List Preview */}
              <div>
                <h4 className="font-medium mb-2">Products (showing first 5):</h4>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  {qrData.humanReadable.productList.map((product, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <span className="w-4 h-4 rounded-full bg-blue-100 text-blue-600 text-xs flex items-center justify-center">
                        {index + 1}
                      </span>
                      {product}
                    </li>
                  ))}
                  {qrData.summary.totalProducts > 5 && (
                    <li className="text-xs text-muted-foreground italic">
                      ... and {qrData.summary.totalProducts - 5} more products
                    </li>
                  )}
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
