"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertTriangle, CheckCircle, Clock, AlertCircle } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface POPerformanceCardProps {
  performanceScore: number | null;
  qualityScore: number | null;
  supplierScore: number | null;
  hasDelays: boolean;
  delayReason?: string | null;
  lastTransitionAt: Date | null;
  timeInCurrentStatus: number | null;
  batchMetrics: {
    totalBatches: number;
    activeBatches: number;
    expiringBatches: number;
    expiredBatches: number;
  };
}

export function POPerformanceCard({
  performanceScore,
  qualityScore,
  supplierScore,
  hasDelays,
  delayReason,
  lastTransitionAt,
  timeInCurrentStatus,
  batchMetrics,
}: POPerformanceCardProps) {
  const getScoreColor = (score: number | null) => {
    if (score === null) return "bg-gray-200";
    if (score >= 90) return "bg-green-500";
    if (score >= 70) return "bg-yellow-500";
    return "bg-red-500";
  };

  const getScoreLabel = (score: number | null) => {
    if (score === null) return "Not Available";
    if (score >= 90) return "Excellent";
    if (score >= 70) return "Good";
    return "Needs Attention";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          Performance Metrics
          {hasDelays && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Delays Detected: {delayReason}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Status Duration */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Time in Current Status
            </span>
            <span>
              {lastTransitionAt && (
                <Badge variant="outline">{formatDistanceToNow(lastTransitionAt)}</Badge>
              )}
            </span>
          </div>
        </div>

        {/* Performance Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Performance Score</span>
            <span className="font-medium">{performanceScore ?? "N/A"}</span>
          </div>
          <Progress value={performanceScore ?? 0} className={getScoreColor(performanceScore)} />
          <span className="text-xs text-muted-foreground">{getScoreLabel(performanceScore)}</span>
        </div>

        {/* Quality Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Quality Score</span>
            <span className="font-medium">{qualityScore ?? "N/A"}</span>
          </div>
          <Progress value={qualityScore ?? 0} className={getScoreColor(qualityScore)} />
          <span className="text-xs text-muted-foreground">{getScoreLabel(qualityScore)}</span>
        </div>

        {/* Supplier Score */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span>Supplier Performance</span>
            <span className="font-medium">{supplierScore ?? "N/A"}</span>
          </div>
          <Progress value={supplierScore ?? 0} className={getScoreColor(supplierScore)} />
          <span className="text-xs text-muted-foreground">{getScoreLabel(supplierScore)}</span>
        </div>

        {/* Batch Statistics */}
        <div className="grid grid-cols-2 gap-4 pt-4">
          <div className="space-y-1">
            <span className="text-sm text-muted-foreground">Active Batches</span>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span className="text-2xl font-semibold">{batchMetrics.activeBatches}</span>
              <span className="text-sm text-muted-foreground">of {batchMetrics.totalBatches}</span>
            </div>
          </div>
          <div className="space-y-1">
            <span className="text-sm text-muted-foreground">Expiring Soon</span>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-yellow-500" />
              <span className="text-2xl font-semibold">{batchMetrics.expiringBatches}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
