"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { POStatus, POStatusChangeReason, POPriority } from "@prisma/client";
import { getValidTransitions, PO_STATUS_CHANGE_REASONS } from "@/lib/po-status-management";
import { toast } from "sonner";
import { Loader2, <PERSON>R<PERSON>, CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface POStatusTransitionProps {
  purchaseOrderId: string;
  currentStatus: POStatus;
  userRole: string;
  onStatusChanged?: (newStatus: POStatus) => void;
  trigger?: React.ReactNode;
}

export function POStatusTransition({
  purchaseOrderId,
  currentStatus,
  userRole,
  onStatusChanged,
  trigger,
}: POStatusTransitionProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState<POStatus | "">("");
  const [reason, setReason] = useState<POStatusChangeReason | "">("");
  const [notes, setNotes] = useState("");
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState<Date | undefined>(undefined);
  const [holdReason, setHoldReason] = useState("");
  const [holdUntil, setHoldUntil] = useState<Date | undefined>(undefined);
  const [validationError, setValidationError] = useState<string | null>(null);

  // Inline calendar states
  const [showHoldUntilCalendar, setShowHoldUntilCalendar] = useState(false);
  const [showExpectedDeliveryCalendar, setShowExpectedDeliveryCalendar] = useState(false);

  const validTransitions = getValidTransitions(currentStatus, userRole);

  // Reset form when dialog opens
  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (isOpen) {
      // Reset form state when opening
      setSelectedStatus("");
      setReason("");
      setNotes("");
      setExpectedDeliveryDate(undefined);
      setHoldReason("");
      setHoldUntil(undefined);
      setValidationError(null);
      setShowHoldUntilCalendar(false);
      setShowExpectedDeliveryCalendar(false);
    }
  };

  const handleSubmit = async () => {
    // Clear previous validation errors
    setValidationError(null);

    // Validate required fields
    if (!selectedStatus) {
      setValidationError("Please select a new status");
      return;
    }

    if (!reason) {
      setValidationError("Please select a reason for the status change");
      return;
    }

    try {
      setLoading(true);

      const requestData: any = {
        toStatus: selectedStatus,
        reason,
        notes: notes || undefined,
      };

      if (expectedDeliveryDate) {
        requestData.expectedDeliveryDate = expectedDeliveryDate.toISOString();
      }

      if (selectedStatus === 'ON_HOLD') {
        requestData.holdReason = holdReason || undefined;
        if (holdUntil) {
          requestData.holdUntil = holdUntil.toISOString();
        }
      }

      const response = await fetch(`/api/purchase-orders/${purchaseOrderId}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to change status");
      }

      const updatedPO = await response.json();
      
      toast.success(`Status changed to ${selectedStatus}`);
      onStatusChanged?.(selectedStatus);
      handleOpenChange(false);
      
      // Reset form
      setSelectedStatus("");
      setReason("");
      setNotes("");
      setExpectedDeliveryDate(undefined);
      setHoldReason("");
      setHoldUntil(undefined);
      setValidationError(null);
      setShowHoldUntilCalendar(false);
      setShowExpectedDeliveryCalendar(false);
    } catch (error) {
      console.error("Error changing status:", error);
      toast.error(error instanceof Error ? error.message : "Failed to change status");
    } finally {
      setLoading(false);
    }
  };

  const selectedTransition = validTransitions.find(t => t.to === selectedStatus);



  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <ArrowRight className="h-4 w-4 mr-2" />
            Change Status
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Change Purchase Order Status</DialogTitle>
          <DialogDescription>
            Change the status of this purchase order. Current status: <strong>{currentStatus}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Validation Error Alert */}
          {validationError && (
            <Alert variant="destructive">
              <AlertDescription>{validationError}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="status">New Status</Label>
            <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value as POStatus)}>
              <SelectTrigger>
                <SelectValue placeholder="Select new status" />
              </SelectTrigger>
              <SelectContent>
                {validTransitions.length === 0 ? (
                  <SelectItem value="no-transitions" disabled>
                    No status changes available
                  </SelectItem>
                ) : (
                  validTransitions.map((transition) => (
                    <SelectItem key={transition.to} value={transition.to}>
                      <div className="flex flex-col">
                        <span>{transition.label}</span>
                        <span className="text-xs text-muted-foreground">
                          {transition.description}
                        </span>
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="reason">Reason</Label>
            <Select value={reason} onValueChange={(value) => setReason(value as POStatusChangeReason)}>
              <SelectTrigger>
                <SelectValue placeholder="Select reason for change" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(PO_STATUS_CHANGE_REASONS).map(([key, label]) => (
                  <SelectItem key={key} value={key}>
                    {label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedStatus === 'ON_HOLD' && (
            <>
              <div className="space-y-2">
                <Label htmlFor="holdReason">Hold Reason</Label>
                <Input
                  id="holdReason"
                  value={holdReason}
                  onChange={(e) => setHoldReason(e.target.value)}
                  placeholder="Reason for putting on hold"
                />
              </div>

              <div className="space-y-2">
                <Label>Hold Until (Optional)</Label>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !holdUntil && "text-muted-foreground"
                  )}
                  onClick={() => setShowHoldUntilCalendar(!showHoldUntilCalendar)}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {holdUntil ? format(holdUntil, "PPP") : "Pick a date"}
                </Button>

                {showHoldUntilCalendar && (
                  <div className="border rounded-md p-3 bg-popover">
                    <Calendar
                      mode="single"
                      selected={holdUntil}
                      onSelect={(date) => {
                        setHoldUntil(date);
                        setShowHoldUntilCalendar(false);
                      }}
                      initialFocus
                      disabled={(date) => date < new Date()}
                    />
                  </div>
                )}
              </div>
            </>
          )}

          {(selectedStatus === 'ORDERED' || selectedStatus === 'EXPEDITED') && (
            <div className="space-y-2">
              <Label>Expected Delivery Date (Optional)</Label>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !expectedDeliveryDate && "text-muted-foreground"
                )}
                onClick={() => setShowExpectedDeliveryCalendar(!showExpectedDeliveryCalendar)}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {expectedDeliveryDate ? format(expectedDeliveryDate, "PPP") : "Pick a date"}
              </Button>

              {showExpectedDeliveryCalendar && (
                <div className="border rounded-md p-3 bg-popover">
                  <Calendar
                    mode="single"
                    selected={expectedDeliveryDate}
                    onSelect={(date) => {
                      setExpectedDeliveryDate(date);
                      setShowExpectedDeliveryCalendar(false);
                    }}
                    initialFocus
                    disabled={(date) => date < new Date()}
                  />
                </div>
              )}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes about this status change"
              rows={3}
            />
          </div>

          {selectedTransition && (
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Action:</strong> {selectedTransition.description}
              </p>
              {selectedTransition.requiresApproval && (
                <p className="text-sm text-orange-600 mt-1">
                  ⚠️ This action requires approval
                </p>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => handleOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={loading || !selectedStatus || !reason}
          >
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Change Status
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
