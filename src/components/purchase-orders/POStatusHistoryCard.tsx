"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow, format } from "date-fns";
import { POStatus } from "@prisma/client";
import { PO_STATUS_INFO, PO_STATUS_CHANGE_REASONS } from "@/lib/po-status-management";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AlertTriangle, TrendingDown, TrendingUp, ArrowRight } from "lucide-react";

interface StatusHistoryEntry {
  id: string;
  fromStatus: POStatus;
  toStatus: POStatus;
  reason: string;
  notes?: string | null;
  createdAt: Date;
  createdBy: {
    name: string;
  };
  metadata?: any;
  performanceData?: {
    timeInStatus: number;
    performanceScore: number;
    qualityScore: number;
    supplierScore: number;
    batchMetrics: {
      totalBatches: number;
      activeBatches: number;
      expiringBatches: number;
      expiredBatches: number;
    };
  };
}

interface POStatusHistoryCardProps {
  history?: StatusHistoryEntry[] | null;
}

export function POStatusHistoryCard({ history }: POStatusHistoryCardProps) {
  // Ensure history is an array and handle edge cases
  const historyArray = Array.isArray(history) ? history : [];

  const getPerformanceIndicator = (current: number, previous: number | undefined) => {
    if (previous === undefined) return null;
    const difference = current - previous;
    if (difference > 0) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <TrendingUp className="h-4 w-4 text-green-500" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Improved by {difference.toFixed(1)} points</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    if (difference < 0) {
      return (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger>
              <TrendingDown className="h-4 w-4 text-red-500" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Decreased by {Math.abs(difference).toFixed(1)} points</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">Status History</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {historyArray.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No status changes recorded yet.</p>
            <p className="text-sm mt-1">Status history will appear here when the purchase order status changes.</p>
          </div>
        ) : (
          historyArray.map((entry, index) => {
            const previousEntry = historyArray[index + 1];
          const statusInfo = PO_STATUS_INFO[entry.toStatus];

          return (
            <div key={entry.id} className="relative pl-6 pb-6 border-l-2 border-gray-200 last:pb-0">
              {/* Status Change Marker */}
              <div className="absolute -left-[9px] top-0 w-4 h-4 rounded-full bg-white border-2 border-gray-200" />

              {/* Main Content */}
              <div className="space-y-3">
                {/* Status Change Header */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={statusInfo.color} variant="secondary">
                      {statusInfo.label}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      {format(entry.createdAt, "MMM d, yyyy HH:mm")}
                    </span>
                    <span className="text-sm text-muted-foreground">by {entry.createdBy.name}</span>
                  </div>

                  {/* Status Transition */}
                  {entry.fromStatus && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <span>{PO_STATUS_INFO[entry.fromStatus]?.label || entry.fromStatus}</span>
                      <ArrowRight className="h-3 w-3" />
                      <span>{statusInfo.label}</span>
                    </div>
                  )}

                  {/* Reason */}
                  {entry.reason && (
                    <div className="text-sm">
                      <span className="font-medium text-muted-foreground">Reason: </span>
                      <span className="text-foreground">
                        {PO_STATUS_CHANGE_REASONS[entry.reason] || entry.reason}
                      </span>
                    </div>
                  )}
                </div>

                {/* Performance Metrics */}
                {entry.performanceData && (
                  <div className="grid grid-cols-3 gap-4 mt-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Performance:</span>
                      <div className="flex items-center gap-1">
                        <span>{entry.performanceData.performanceScore}</span>
                        {getPerformanceIndicator(
                          entry.performanceData.performanceScore,
                          previousEntry?.performanceData?.performanceScore
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Quality:</span>
                      <div className="flex items-center gap-1">
                        <span>{entry.performanceData.qualityScore}</span>
                        {getPerformanceIndicator(
                          entry.performanceData.qualityScore,
                          previousEntry?.performanceData?.qualityScore
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Supplier:</span>
                      <div className="flex items-center gap-1">
                        <span>{entry.performanceData.supplierScore}</span>
                        {getPerformanceIndicator(
                          entry.performanceData.supplierScore,
                          previousEntry?.performanceData?.supplierScore
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Batch Information */}
                {entry.performanceData?.batchMetrics && (
                  <div className="text-sm text-muted-foreground mt-1">
                    <span>
                      {entry.performanceData.batchMetrics.activeBatches} active batches
                      {entry.performanceData.batchMetrics.expiringBatches > 0 && (
                        <span className="ml-2 flex items-center gap-1">
                          <AlertTriangle className="h-4 w-4 text-yellow-500" />
                          {entry.performanceData.batchMetrics.expiringBatches} expiring soon
                        </span>
                      )}
                    </span>
                  </div>
                )}

                {/* Notes */}
                {entry.notes && <p className="text-sm text-muted-foreground mt-1">{entry.notes}</p>}
              </div>
            </div>
          );
        })
        )}
      </CardContent>
    </Card>
  );
}
