"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Settings, Eye, Loader2 } from "lucide-react";
import Link from "next/link";

interface NotificationStats {
  total: number;
  unread: number;
  recentNotifications: Array<{
    id: string;
    title: string;
    type: string;
    createdAt: string;
    isRead: boolean;
  }>;
}

export function NotificationQuickAccess() {
  const [stats, setStats] = useState<NotificationStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchNotificationStats();
  }, []);

  const fetchNotificationStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications?limit=3');
      
      if (!response.ok) {
        throw new Error('Failed to fetch notification stats');
      }

      const data = await response.json();
      
      setStats({
        total: data.pagination?.total || 0,
        unread: data.notifications?.filter((n: any) => !n.isRead).length || 0,
        recentNotifications: data.notifications?.slice(0, 3) || [],
      });
    } catch (error) {
      console.error('Error fetching notification stats:', error);
      // Set empty stats on error
      setStats({
        total: 0,
        unread: 0,
        recentNotifications: [],
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notifications
        </CardTitle>
        <CardDescription>
          Recent notifications and quick access
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stats */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{stats?.total || 0}</div>
              <div className="text-xs text-muted-foreground">Total</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-primary">{stats?.unread || 0}</div>
              <div className="text-xs text-muted-foreground">Unread</div>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" asChild>
              <Link href="/settings/notifications">
                <Settings className="h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="sm" asChild>
              <Link href="/notifications">
                <Eye className="h-4 w-4" />
              </Link>
            </Button>
          </div>
        </div>

        {/* Recent Notifications */}
        {stats?.recentNotifications && stats.recentNotifications.length > 0 ? (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recent</h4>
            {stats.recentNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`flex items-start gap-2 p-2 rounded-md text-sm ${
                  !notification.isRead ? 'bg-accent/20' : 'bg-muted/50'
                }`}
              >
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium truncate">{notification.title}</span>
                    {!notification.isRead && (
                      <Badge variant="secondary" className="text-xs">New</Badge>
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(notification.createdAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-4">
            <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-sm text-muted-foreground">No recent notifications</p>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2 pt-2 border-t">
          <Button variant="outline" size="sm" asChild className="flex-1">
            <Link href="/notifications">
              View All
            </Link>
          </Button>
          <Button variant="outline" size="sm" asChild className="flex-1">
            <Link href="/settings/notifications">
              Preferences
            </Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
