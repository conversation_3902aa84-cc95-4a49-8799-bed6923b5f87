"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>2, <PERSON>tings, CheckCircle, AlertTriangle, Info, RefreshCw } from "lucide-react";
import { toast } from "sonner";

interface InitializationStats {
  templatesCreated: number;
  preferencesCreated: number;
  usersProcessed: number;
}

export function NotificationSystemInit() {
  const [initializing, setInitializing] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const [stats, setStats] = useState<InitializationStats | null>(null);
  const [checkingStatus, setCheckingStatus] = useState(false);
  const [systemStatus, setSystemStatus] = useState<any>(null);

  const initializeSystem = async () => {
    try {
      setInitializing(true);
      console.log('🚀 Initializing notification system...');

      const response = await fetch('/api/notifications/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      console.log('📡 Init API response status:', response.status);
      console.log('📡 Init API response headers:', Object.fromEntries(response.headers));

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
          console.error('❌ Init API error response:', errorData);
        } catch (parseError) {
          console.error('❌ Failed to parse error response:', parseError);
          errorData = {
            error: 'Failed to parse server response',
            details: `HTTP ${response.status}: ${response.statusText}`
          };
        }

        let errorMessage = 'Failed to initialize notification system';

        if (response.status === 403) {
          errorMessage = errorData.details || 'You are not authorized to initialize the notification system. Only super admins can perform this action.';
        } else if (response.status === 500) {
          if (errorData.details) {
            errorMessage = `Server error: ${errorData.details}`;
            if (errorData.suggestion) {
              errorMessage += `\n\nSuggestion: ${errorData.suggestion}`;
            }
          } else {
            errorMessage = `Server error: ${errorData.error || 'Internal server error'}`;
          }
        } else if (response.status === 401) {
          errorMessage = 'Authentication failed. Please log in and try again.';
        } else {
          errorMessage = errorData.details || errorData.error || `HTTP ${response.status}: ${response.statusText}`;
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('✅ Notification system initialized:', data);

      setStats(data.stats);
      setInitialized(true);

      let successMessage = 'Notification system initialized successfully!';
      if (data.stats) {
        successMessage += ` Created ${data.stats.templatesCreated} templates and ${data.stats.preferencesCreated} preferences.`;
      }

      toast.success(successMessage);

      // Show warnings if any
      if (data.warnings) {
        if (data.warnings.templateErrors?.length > 0) {
          toast.warning(`${data.warnings.templateErrors.length} template creation errors occurred. Check console for details.`);
        }
        if (data.warnings.preferenceErrors?.length > 0) {
          toast.warning(`${data.warnings.preferenceErrors.length} preference creation errors occurred. Check console for details.`);
        }
      }

    } catch (error) {
      console.error('❌ Error initializing notification system:', error);

      // Show detailed error message
      const errorMessage = error.message || 'Failed to initialize notification system';
      toast.error(errorMessage, {
        duration: 10000, // Show error longer
      });

    } finally {
      setInitializing(false);
    }
  };

  const checkSystemStatus = async () => {
    try {
      setCheckingStatus(true);
      console.log('🔍 Checking notification system status...');

      const response = await fetch('/api/notifications/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      console.log('📡 Status API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ Status API error:', errorData);

        if (response.status === 403 || response.status === 401) {
          throw new Error('You need to be logged in to check system status.');
        } else {
          throw new Error(errorData.details || errorData.error || 'Failed to check system status');
        }
      }

      const data = await response.json();
      console.log('✅ System status received:', data);

      setSystemStatus(data);

      if (data.status?.system?.initialized) {
        toast.success('Notification system is already initialized and ready to use!');
        setInitialized(true);
      } else {
        toast.info('System status checked. See details below.');
      }

    } catch (error) {
      console.error('❌ Error checking system status:', error);
      toast.error(error.message || 'Failed to check system status');
    } finally {
      setCheckingStatus(false);
    }
  };

  if (initialized && stats) {
    return (
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Notification system initialized successfully!</strong>
          <br />
          Created {stats.templatesCreated} templates and {stats.preferencesCreated} preferences for {stats.usersProcessed} users.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Initialize Notification System
        </CardTitle>
        <CardDescription>
          Set up default notification templates and user preferences
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            If you're seeing errors loading notification preferences, the notification system may need to be initialized.
            This will create default notification templates and preferences for all users.
          </AlertDescription>
        </Alert>

        {/* System Status Display */}
        {systemStatus && (
          <div className="space-y-3">
            <div className="text-sm font-medium">System Status:</div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${systemStatus.status?.database?.connected ? 'bg-green-500' : 'bg-red-500'}`} />
                Database: {systemStatus.status?.database?.connected ? 'Connected' : 'Disconnected'}
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${systemStatus.status?.database?.tablesExist ? 'bg-green-500' : 'bg-red-500'}`} />
                Tables: {systemStatus.status?.database?.tablesExist ? 'Exist' : 'Missing'}
              </div>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 rounded-full bg-green-500" />
                Templates: Hardcoded
              </div>
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${systemStatus.status?.system?.hasPreferences ? 'bg-green-500' : 'bg-yellow-500'}`} />
                Preferences: {systemStatus.status?.database?.preferenceCount || 0}
              </div>
            </div>

            {systemStatus.recommendations && systemStatus.recommendations.length > 0 && (
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  <div className="font-medium mb-1">Recommendations:</div>
                  <ul className="text-xs space-y-1">
                    {systemStatus.recommendations.map((rec: string, index: number) => (
                      <li key={index}>• {rec}</li>
                    ))}
                  </ul>
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        <div className="flex gap-2">
          <Button
            onClick={checkSystemStatus}
            disabled={checkingStatus}
            variant="outline"
            className="flex-1"
          >
            {checkingStatus ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Check Status
              </>
            )}
          </Button>

          <Button
            onClick={initializeSystem}
            disabled={initializing || checkingStatus}
            className="flex-1"
          >
            {initializing ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Initializing...
              </>
            ) : (
              <>
                <Settings className="h-4 w-4 mr-2" />
                Initialize System
              </>
            )}
          </Button>
        </div>

        <p className="text-xs text-muted-foreground">
          Note: Only super administrators can initialize the notification system.
        </p>
      </CardContent>
    </Card>
  );
}
