"use client";

import { useState, useEffect, useRef } from "react";
import { Bell, Check, Package, ExternalLink, Settings, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/custom/badge";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useClientAuth } from "@/hooks/use-client-auth";
import Link from "next/link";

type Notification = {
  id: string;
  title: string;
  message: string;
  type:
    | "SYSTEM"
    | "MESSAGE"
    | "ALERT"
    | "INFO"
    | "PURCHASE_ORDER_APPROVAL"
    | "PURCHASE_ORDER_APPROVED"
    | "PURCHASE_ORDER_REJECTED"
    | "PURCHASE_ORDER_RECEIVED";
  isRead: boolean;
  createdAt: string;
  purchaseOrderId?: string;
  actionUrl?: string;
  metadata?: any;
  purchaseOrder?: {
    id: string;
    status: string;
    total: number;
    supplier: {
      name: string;
    };
  };
};

export function NotificationDropdown() {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showClearDialog, setShowClearDialog] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user } = useClientAuth();

  // Fetch notifications
  const fetchNotifications = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/notifications?limit=10&unread=false");

      if (!response.ok) {
        const errorData = await response.json();
        console.error("Notification API error:", errorData);
        throw new Error(errorData.error || "Failed to fetch notifications");
      }

      const data = await response.json();

      // Ensure notifications array exists and has proper structure
      const notifications = Array.isArray(data.notifications) ? data.notifications : [];
      setNotifications(notifications);

      // Count unread notifications
      const unread = notifications.filter((n: Notification) => !n.isRead).length;
      setUnreadCount(unread);
    } catch (err: any) {
      console.error("Error fetching notifications:", err);
      setError(err.message || "An error occurred while fetching notifications");
      // Set empty state on error to prevent app crash
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  // Mark a notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ isRead: true }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to mark notification as read");
      }

      // Update local state
      setNotifications((prev) => prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)));
      setUnreadCount((prev) => Math.max(0, prev - 1));
    } catch (err: any) {
      console.error("Error marking notification as read:", err);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await fetch("/api/notifications/mark-all-read", {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to mark all notifications as read");
      }

      // Update local state
      setNotifications((prev) => prev.map((n) => ({ ...n, isRead: true })));
      setUnreadCount(0);
    } catch (err: any) {
      console.error("Error marking all notifications as read:", err);
    }
  };

  // Show clear all confirmation dialog
  const showClearAllDialog = () => {
    setShowClearDialog(true);
  };

  // Clear all notifications (delete them permanently)
  const clearAllNotifications = async () => {
    setShowClearDialog(false);

    try {
      const response = await fetch("/api/notifications/clear-all", {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to clear all notifications");
      }

      const result = await response.json();

      // Clear the local state to remove them from view
      setNotifications([]);
      setUnreadCount(0);

      // Show success message
      console.log(`All notifications cleared successfully: ${result.message}`);
    } catch (err: any) {
      console.error("Error clearing all notifications:", err);
      alert("Failed to clear notifications. Please try again.");
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Fetch notifications on mount and when user changes
  useEffect(() => {
    if (user) {
      fetchNotifications();
    }
  }, [user]);

  // Fetch notifications periodically (every 30 seconds)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      fetchNotifications();
    }, 30000);

    return () => clearInterval(interval);
  }, [user]);

  // Get notification badge color based on type
  const getNotificationBadgeColor = (type: string) => {
    switch (type) {
      case "ALERT":
        return "destructive";
      case "MESSAGE":
        return "secondary";
      case "INFO":
        return "success";
      case "PURCHASE_ORDER_APPROVAL":
        return "warning";
      case "PURCHASE_ORDER_APPROVED":
        return "success";
      case "PURCHASE_ORDER_REJECTED":
        return "destructive";
      case "PURCHASE_ORDER_RECEIVED":
        return "secondary";
      default:
        return "default";
    }
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "PURCHASE_ORDER_APPROVAL":
      case "PURCHASE_ORDER_APPROVED":
      case "PURCHASE_ORDER_REJECTED":
      case "PURCHASE_ORDER_RECEIVED":
        return <Package className="h-3 w-3" />;
      default:
        return null;
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        className="relative rounded-full p-2 text-muted-foreground hover:bg-accent hover:text-foreground"
        onClick={() => setIsOpen(!isOpen)}
        aria-label="Notifications"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute right-1 top-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-[11px] font-medium text-white">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 z-50 mt-2 w-80 rounded-md border bg-background shadow-lg">
          <div className="flex items-center justify-between border-b p-3">
            <h3 className="font-medium">Notifications</h3>
            <div className="flex items-center gap-2">
              {unreadCount > 0 && (
                <Button variant="ghost" size="sm" onClick={markAllAsRead} className="h-8 text-xs">
                  Mark all as read
                </Button>
              )}
              {notifications.length > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={showClearAllDialog}
                  className="h-8 text-xs text-muted-foreground hover:text-destructive"
                  title="Clear all notifications"
                >
                  <Trash2 className="h-3 w-3" />
                  Clear All
                </Button>
              )}
            </div>
          </div>

          <div className="max-h-[400px] overflow-y-auto">
            {loading && (
              <div className="flex items-center justify-center p-4">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            )}

            {error && <div className="p-4 text-sm text-destructive">{error}</div>}

            {!loading && !error && notifications.length === 0 && (
              <div className="p-4 text-center text-sm text-muted-foreground">No notifications</div>
            )}

            {!loading &&
              !error &&
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`border-b p-3 ${!notification.isRead ? "bg-accent/20" : ""}`}
                >
                  <div className="mb-1 flex items-center justify-between">
                    <div className="flex items-center gap-1">
                      {getNotificationIcon(notification.type)}
                      <Badge variant={getNotificationBadgeColor(notification.type)}>
                        {notification.type.replace("PURCHASE_ORDER_", "PO ").replace("_", " ")}
                      </Badge>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {format(new Date(notification.createdAt), "MMM d, h:mm a")}
                    </span>
                  </div>
                  <h4 className="mb-1 text-sm font-medium">{notification.title}</h4>
                  <p className="text-xs text-muted-foreground">{notification.message}</p>

                  {/* Purchase Order specific information */}
                  {notification.purchaseOrder && (
                    <div className="mt-2 rounded bg-muted/50 p-2">
                      <div className="flex items-center justify-between text-xs">
                        <span className="font-medium">
                          #{notification.purchaseOrder.id.slice(-8).toUpperCase()}
                        </span>
                        <span className="text-muted-foreground">
                          Rp {Number(notification.purchaseOrder.total).toLocaleString("id-ID")}
                        </span>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {notification.purchaseOrder.supplier.name}
                      </div>
                    </div>
                  )}

                  <div className="mt-2 flex gap-2">
                    {notification.actionUrl && (
                      <Button
                        variant="outline"
                        size="sm"
                        asChild
                        className="h-7 flex-1 text-xs"
                        onClick={() => {
                          markAsRead(notification.id);
                          setIsOpen(false);
                        }}
                      >
                        <Link href={notification.actionUrl}>
                          <ExternalLink className="mr-1 h-3 w-3" />
                          View
                        </Link>
                      </Button>
                    )}
                    {!notification.isRead && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => markAsRead(notification.id)}
                        className="h-7 text-xs"
                      >
                        <Check className="mr-1 h-3 w-3" />
                        Mark Read
                      </Button>
                    )}
                  </div>
                </div>
              ))}
          </div>

          {/* Footer with links */}
          <div className="border-t p-3">
            <div className="flex items-center justify-between text-xs">
              <Link
                href="/notifications"
                className="text-muted-foreground hover:text-foreground"
                onClick={() => setIsOpen(false)}
              >
                View all notifications
              </Link>
              <Link
                href="/settings/notifications"
                className="flex items-center gap-1 text-muted-foreground hover:text-foreground"
                onClick={() => setIsOpen(false)}
              >
                <Settings className="h-3 w-3" />
                Preferences
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Clear All Confirmation Dialog */}
      <Dialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Clear All Notifications</DialogTitle>
            <DialogDescription>
              Are you sure you want to clear all {notifications.length} notifications?
              This action cannot be undone and will permanently delete all your notifications.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowClearDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={clearAllNotifications}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
