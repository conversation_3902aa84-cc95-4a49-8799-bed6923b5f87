"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import {
  Loader2,
  Bell,
  BellOff,
  Mail,
  Smartphone,
  Monitor,
  MessageSquare,
  Settings,
  Save,
  RotateCcw,
  AlertTriangle
} from "lucide-react";
import { NotificationSystemInit } from "./NotificationSystemInit";

interface NotificationPreference {
  eventType: string;
  enabled: boolean;
  deliveryMethods: string[];
  frequency: string;
  quietHours?: {
    enabled: boolean;
    startTime: string;
    endTime: string;
    timezone: string;
  };
}

interface UserPreferencesProfile {
  userId: string;
  preferences: NotificationPreference[];
  globalSettings: {
    enabled: boolean;
    defaultDeliveryMethods: string[];
    quietHours?: {
      enabled: boolean;
      startTime: string;
      endTime: string;
      timezone: string;
    };
  };
}

// UI Configuration for Notification Delivery Methods
// Note: Only functional delivery methods are shown in the UI
// Backend supports EMAIL, SMS, and PUSH but these are hidden until service integrations are implemented
const DELIVERY_METHOD_ICONS = {
  IN_APP: Monitor,
  TOAST: Bell,
  // EMAIL: Mail,     // Hidden - no service integration
  // SMS: Smartphone, // Hidden - no service integration
  // PUSH: MessageSquare, // Hidden - no service integration
};

const DELIVERY_METHOD_LABELS = {
  IN_APP: "In-App",
  TOAST: "Toast",
  // EMAIL: "Email",  // Hidden - no service integration
  // SMS: "SMS",      // Hidden - no service integration
  // PUSH: "Push",    // Hidden - no service integration
};

const EVENT_TYPE_LABELS = {
  'po.status.changed': 'Purchase Order Status Changes',
  'po.approved': 'Purchase Order Approved',
  'po.rejected': 'Purchase Order Rejected',
  'po.overdue': 'Purchase Order Overdue',
  'inventory.low_stock': 'Low Stock Alerts',
  'inventory.out_of_stock': 'Out of Stock Alerts',
  'inventory.batch.expiring': 'Batch Expiring Soon',
  'inventory.adjustment.pending': 'Stock Adjustment Pending Approval',
  'inventory.adjustment.approved': 'Stock Adjustment Approved',
  'inventory.adjustment.rejected': 'Stock Adjustment Rejected',
  'cash.audit.alert': 'Cash Audit Alerts',
  'cash_audit.discrepancy_detected': 'Cash Reconciliation Discrepancy Alerts',
  'invoice.approved': 'Invoice Approval Notifications',
  'invoice.payment_made': 'Invoice Payment Confirmations',
  'revenue.target.achieved': 'Revenue Target Achieved',
  'revenue.target.missed': 'Revenue Target Missed',
  'system.maintenance': 'System Maintenance',
  'user.action.required': 'Action Required',
};

const EVENT_TYPE_CATEGORIES = {
  'Purchase Orders': [
    'po.status.changed',
    'po.approved',
    'po.rejected',
    'po.overdue'
  ],
  'Inventory': [
    'inventory.low_stock',
    'inventory.out_of_stock',
    'inventory.batch.expiring',
    'inventory.adjustment.pending',
    'inventory.adjustment.approved',
    'inventory.adjustment.rejected'
  ],
  'Financial': [
    'cash.audit.alert',
    'cash_audit.discrepancy_detected',
    'invoice.approved',
    'invoice.payment_made',
    'revenue.target.achieved',
    'revenue.target.missed'
  ],
  'System': [
    'system.maintenance',
    'user.action.required'
  ],
};

// Helper function to filter delivery methods to only show functional ones in UI
// This prevents errors when backend preferences include non-functional methods (EMAIL, SMS, PUSH)
// Backend infrastructure is preserved for future service integrations
const filterFunctionalDeliveryMethods = (methods: string[]): string[] => {
  const functionalMethods = Object.keys(DELIVERY_METHOD_LABELS);
  return methods.filter(method => functionalMethods.includes(method));
};

export function NotificationPreferences() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [preferences, setPreferences] = useState<UserPreferencesProfile | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    fetchPreferences();
  }, []);

  const fetchPreferences = async () => {
    try {
      setLoading(true);
      console.log('🔍 Fetching notification preferences...');

      const response = await fetch('/api/notifications/preferences', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
      });

      console.log('📡 Preferences API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ Preferences API error:', errorData);

        if (response.status === 403) {
          throw new Error('You are not authorized to access notification preferences. Please log in again.');
        } else if (response.status === 500) {
          throw new Error('Server error: ' + (errorData.error || 'Internal server error'));
        } else {
          throw new Error(errorData.error || 'Failed to fetch preferences');
        }
      }

      const data = await response.json();
      console.log('✅ Preferences data received:', data);

      if (data.preferences) {
        setPreferences(data.preferences);

        // Check if preferences are empty and show helpful message
        if (data.preferences.preferences && data.preferences.preferences.length === 0) {
          console.warn('⚠️ User has no notification preferences set up');
          toast.info('No notification preferences found. You may need to initialize the notification system.');
        }
      } else {
        console.warn('⚠️ No preferences data in response, creating default structure');
        // Create a default preferences structure if none exists
        // Only include functional delivery methods in UI defaults
        setPreferences({
          userId: 'current-user',
          preferences: [],
          globalSettings: {
            enabled: true,
            defaultDeliveryMethods: Object.keys(DELIVERY_METHOD_LABELS), // Only functional methods
          }
        });
      }
    } catch (error) {
      console.error('❌ Error fetching preferences:', error);
      toast.error(error.message || 'Failed to load notification preferences');
    } finally {
      setLoading(false);
    }
  };

  const updatePreference = (eventType: string, updates: Partial<NotificationPreference>) => {
    if (!preferences) return;

    const updatedPreferences = {
      ...preferences,
      preferences: preferences.preferences.map(pref =>
        pref.eventType === eventType
          ? { ...pref, ...updates }
          : pref
      ),
    };

    setPreferences(updatedPreferences);
    setHasChanges(true);
  };

  const toggleDeliveryMethod = (eventType: string, method: string) => {
    const preference = preferences?.preferences.find(p => p.eventType === eventType);
    if (!preference) return;

    // Only allow toggling of functional delivery methods
    const functionalMethods = Object.keys(DELIVERY_METHOD_LABELS);
    if (!functionalMethods.includes(method)) {
      console.warn(`Attempted to toggle non-functional delivery method: ${method}`);
      return;
    }

    const currentMethods = preference.deliveryMethods;
    const newMethods = currentMethods.includes(method)
      ? currentMethods.filter(m => m !== method)
      : [...currentMethods, method];

    updatePreference(eventType, { deliveryMethods: newMethods });
  };

  const savePreferences = async () => {
    if (!preferences || !hasChanges) return;

    try {
      setSaving(true);
      console.log('💾 Saving notification preferences...');

      const response = await fetch('/api/notifications/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({ preferences: preferences.preferences }),
      });

      console.log('📡 Save preferences API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ Save preferences API error:', errorData);

        if (response.status === 403) {
          throw new Error('You are not authorized to save preferences. Please log in again.');
        } else {
          throw new Error(errorData.error || 'Failed to save preferences');
        }
      }

      const data = await response.json();
      console.log('✅ Preferences saved successfully:', data);

      setHasChanges(false);
      toast.success('Notification preferences saved successfully');
    } catch (error) {
      console.error('❌ Error saving preferences:', error);
      toast.error(error.message || 'Failed to save notification preferences');
    } finally {
      setSaving(false);
    }
  };

  const resetPreferences = () => {
    fetchPreferences();
    setHasChanges(false);
  };

  const toggleAllNotifications = async (enabled: boolean) => {
    try {
      setSaving(true);
      console.log(`🔄 ${enabled ? 'Enabling' : 'Disabling'} all notifications...`);

      const response = await fetch('/api/notifications/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({ action: enabled ? 'enable-all' : 'disable-all' }),
      });

      console.log('📡 Toggle all API response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        console.error('❌ Toggle all API error:', errorData);

        if (response.status === 403) {
          throw new Error('You are not authorized to update preferences. Please log in again.');
        } else {
          throw new Error(errorData.error || 'Failed to update preferences');
        }
      }

      const data = await response.json();
      console.log('✅ Toggle all successful:', data);

      await fetchPreferences();
      setHasChanges(false);
      toast.success(`All notifications ${enabled ? 'enabled' : 'disabled'} successfully`);
    } catch (error) {
      console.error('❌ Error updating preferences:', error);
      toast.error(error.message || 'Failed to update notification preferences');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading notification preferences...</span>
      </div>
    );
  }

  if (!preferences) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              Notification Preferences Not Available
            </CardTitle>
            <CardDescription>
              Unable to load notification preferences. This might be because the notification system hasn't been initialized yet.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={fetchPreferences} variant="outline" className="w-full">
              Try Loading Again
            </Button>
          </CardContent>
        </Card>

        <NotificationSystemInit />
      </div>
    );
  }

  // Check if preferences are empty
  const hasNoPreferences = !preferences.preferences || preferences.preferences.length === 0;

  if (hasNoPreferences) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
              No Notification Preferences Found
            </CardTitle>
            <CardDescription>
              You don't have any notification preferences set up yet. This usually means the notification system needs to be initialized.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={fetchPreferences} variant="outline" className="w-full">
              Refresh Preferences
            </Button>
          </CardContent>
        </Card>

        <NotificationSystemInit />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Notification Preferences</h2>
          <p className="text-muted-foreground">
            Customize how and when you receive notifications
          </p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={resetPreferences}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          )}
          <Button
            onClick={savePreferences}
            disabled={!hasChanges || saving}
          >
            {saving ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Global Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Global Settings
          </CardTitle>
          <CardDescription>
            Quick controls for all notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-base font-medium">Enable All Notifications</Label>
              <p className="text-sm text-muted-foreground">
                Turn all notifications on or off at once
              </p>
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => toggleAllNotifications(false)}
                disabled={saving}
              >
                <BellOff className="h-4 w-4 mr-2" />
                Disable All
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => toggleAllNotifications(true)}
                disabled={saving}
              >
                <Bell className="h-4 w-4 mr-2" />
                Enable All
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Categories */}
      {Object.entries(EVENT_TYPE_CATEGORIES).map(([category, eventTypes]) => (
        <Card key={category}>
          <CardHeader>
            <CardTitle>{category}</CardTitle>
            <CardDescription>
              Configure notifications for {category.toLowerCase()} events
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {eventTypes.map((eventType) => {
              const preference = preferences.preferences.find(p => p.eventType === eventType);
              if (!preference) return null;

              return (
                <div key={eventType} className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Switch
                        checked={preference.enabled}
                        onCheckedChange={(enabled) => 
                          updatePreference(eventType, { enabled })
                        }
                      />
                      <div>
                        <Label className="text-base font-medium">
                          {EVENT_TYPE_LABELS[eventType] || eventType}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {preference.enabled ? 'Notifications enabled' : 'Notifications disabled'}
                        </p>
                      </div>
                    </div>
                    <Badge variant={preference.enabled ? "default" : "secondary"}>
                      {preference.enabled ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>

                  {preference.enabled && (
                    <div className="ml-8 space-y-4">
                      {/* Delivery Methods */}
                      <div>
                        <Label className="text-sm font-medium">Delivery Methods</Label>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {Object.entries(DELIVERY_METHOD_LABELS).map(([method, label]) => {
                            const Icon = DELIVERY_METHOD_ICONS[method];
                            // Filter user's delivery methods to only show functional ones
                            const functionalMethods = filterFunctionalDeliveryMethods(preference.deliveryMethods);
                            const isSelected = functionalMethods.includes(method);

                            return (
                              <div key={method} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`${eventType}-${method}`}
                                  checked={isSelected}
                                  onCheckedChange={() => toggleDeliveryMethod(eventType, method)}
                                />
                                <Label
                                  htmlFor={`${eventType}-${method}`}
                                  className="flex items-center gap-1 text-sm cursor-pointer"
                                >
                                  <Icon className="h-3 w-3" />
                                  {label}
                                </Label>
                              </div>
                            );
                          })}
                        </div>
                        {/* Info message about hidden delivery methods */}
                        <p className="text-xs text-muted-foreground mt-2">
                          Additional delivery methods (Email, SMS, Push) are available in the backend but hidden until service integrations are implemented.
                        </p>
                      </div>

                      {/* Frequency */}
                      <div>
                        <Label className="text-sm font-medium">Frequency</Label>
                        <Select
                          value={preference.frequency}
                          onValueChange={(frequency) => 
                            updatePreference(eventType, { frequency })
                          }
                        >
                          <SelectTrigger className="w-48 mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="IMMEDIATE">Immediate</SelectItem>
                            <SelectItem value="HOURLY">Hourly</SelectItem>
                            <SelectItem value="DAILY">Daily</SelectItem>
                            <SelectItem value="WEEKLY">Weekly</SelectItem>
                            <SelectItem value="NEVER">Never</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  )}

                  {eventType !== eventTypes[eventTypes.length - 1] && (
                    <Separator className="my-4" />
                  )}
                </div>
              );
            })}
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
