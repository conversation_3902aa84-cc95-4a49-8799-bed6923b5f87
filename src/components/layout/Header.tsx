"use client";

import { useState } from "react";
import { usePathname } from "next/navigation";
import { Search, ChevronDown, User, Setting<PERSON>, LogOut, Bell } from "lucide-react";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { useClientAuth } from "@/hooks/use-client-auth";
import { useStoreInfo } from "@/contexts/store-info-context";
import { NotificationDropdown } from "@/components/notifications/NotificationDropdown";
import { HorizontalNavigation } from "./HorizontalNavigation";

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const pathname = usePathname();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const { user, loading, logout } = useClientAuth();
  const { storeInfo } = useStoreInfo();

  // Generate breadcrumbs from pathname
  const generateBreadcrumbs = () => {
    if (pathname === "/") return [];

    const paths = pathname.split("/").filter(Boolean);
    return paths.map((path, index) => {
      const href = `/${paths.slice(0, index + 1).join("/")}`;
      return {
        href,
        label: path.charAt(0).toUpperCase() + path.slice(1),
      };
    });
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      setShowUserMenu(false); // Close the menu
      await logout();
      // No need to redirect as logout function already handles it
    } catch (error) {
      console.error("Logout failed:", error);
    }
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <div className="border-b bg-background">
      {/* Top header bar */}
      <header className={cn("flex h-16 items-center px-4", className)}>
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Store logo/name */}
            <Link href="/" className="flex items-center gap-2 font-semibold text-primary">
              <span className="flex h-8 w-8 items-center justify-center rounded bg-primary/10 text-primary">
                {storeInfo?.storeName ? (
                  storeInfo.storeName.charAt(0).toUpperCase()
                ) : (
                  "N"
                )}
              </span>
              <span className="hidden md:inline-block">
                {storeInfo?.storeName || "Next POS"}
              </span>
            </Link>

            {/* Breadcrumbs */}
            <div className="hidden items-center gap-1 lg:flex">
              {breadcrumbs.map((crumb, index) => (
                <div key={crumb.href} className="flex items-center">
                  {index > 0 && <span className="mx-1 text-muted-foreground">/</span>}
                  <a
                    href={crumb.href}
                    className={cn(
                      "text-sm hover:text-foreground",
                      index === breadcrumbs.length - 1
                        ? "font-medium text-foreground"
                        : "text-muted-foreground"
                    )}
                  >
                    {crumb.label}
                  </a>
                </div>
              ))}
            </div>
          </div>

          {/* Search */}
          <div className="relative mx-4 hidden flex-1 md:block lg:max-w-sm">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <input
              type="search"
              placeholder="Search..."
              className="w-full rounded-md border border-input bg-background py-2 pl-8 pr-4 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring"
            />
            <div className="absolute right-2.5 top-2 text-xs text-muted-foreground">
              <span className="rounded border border-input bg-background px-1.5 py-0.5">⌘</span>
              <span className="ml-0.5 rounded border border-input bg-background px-1.5 py-0.5">
                F
              </span>
            </div>
          </div>

          {/* Right side actions */}
          <div className="flex items-center gap-3">
            <NotificationDropdown />

            {/* User profile */}
            <div className="relative ml-2">
              {loading ? (
                <div className="h-8 w-8 animate-pulse rounded-full bg-primary/10"></div>
              ) : user ? (
                <>
                  <button
                    className="flex items-center gap-2 rounded-full p-1 text-sm font-medium hover:bg-accent"
                    onClick={() => setShowUserMenu(!showUserMenu)}
                  >
                    <div className="flex h-8 w-8 items-center justify-center overflow-hidden rounded-full bg-primary/10">
                      <span className="text-xs font-medium text-primary">
                        {user.name
                          .split(" ")
                          .map((n: string) => n[0])
                          .join("")
                          .toUpperCase()}
                      </span>
                    </div>
                    <span className="hidden md:inline-block">{user.name}</span>
                    <ChevronDown className="h-4 w-4 text-muted-foreground" />
                  </button>

                  {/* User dropdown menu */}
                  {showUserMenu && (
                    <div className="absolute right-0 top-full z-50 mt-1 w-48 rounded-md border bg-card p-1 shadow-md">
                      <div className="border-b px-2 py-1.5">
                        <div className="font-medium">{user.name}</div>
                        <div className="text-xs text-muted-foreground">{user.email}</div>
                        <div className="mt-1 text-xs font-medium">
                          <span className="rounded-sm bg-primary/10 px-1.5 py-0.5 text-primary">
                            {user.role.replace("_", " ")}
                          </span>
                        </div>
                      </div>
                      <div className="py-1">
                        <button className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent">
                          <User className="h-4 w-4" />
                          <span>Profile</span>
                        </button>
                        <Link
                          href="/settings"
                          className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <Settings className="h-4 w-4" />
                          <span>Settings</span>
                        </Link>
                        <Link
                          href="/settings/notifications"
                          className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm hover:bg-accent"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <Bell className="h-4 w-4" />
                          <span>Notification Preferences</span>
                        </Link>
                      </div>
                      <div className="border-t py-1">
                        <button
                          className="flex w-full items-center gap-2 rounded-sm px-2 py-1.5 text-sm text-destructive hover:bg-destructive/10"
                          onClick={handleLogout}
                        >
                          <LogOut className="h-4 w-4" />
                          <span>Logout</span>
                        </button>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <button
                  onClick={() => (window.location.href = "/login")}
                  className="flex items-center gap-2 rounded-md border px-3 py-1.5 text-sm font-medium hover:bg-accent"
                >
                  <LogOut className="h-4 w-4" />
                  <span>Login</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Navigation bar */}
      <div className="border-b bg-background px-4 py-2">
        <HorizontalNavigation />
      </div>
    </div>
  );
}
