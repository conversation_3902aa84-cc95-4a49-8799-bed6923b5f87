"use client";

import { useState, useEffect } from "react";
import { Header } from "./Header";
import { cn } from "@/lib/utils";
import { ChatButton } from "@/components/chat/ChatButton";
import { useSettings } from "@/contexts/settings-context";

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  const [isMounted, setIsMounted] = useState(false);
  const { isChatEnabled } = useSettings();

  // Prevent hydration errors by only rendering on client
  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <div className="flex h-screen flex-col overflow-hidden bg-background">
      {/* Header with horizontal navigation */}
      <Header />

      {/* Main content */}
      <main className="flex-1 overflow-auto p-4 md:p-6 pb-8">{children}</main>

      {/* Chat button - only shown if enabled in settings */}
      {isChatEnabled && <ChatButton />}
    </div>
  );
}
