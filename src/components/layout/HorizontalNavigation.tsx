"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { useClientAuth } from "@/hooks/use-client-auth";
import { useSettings } from "@/contexts/settings-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Settings,
  HelpCircle,
  Database,
  UserPlus,
  ClipboardList,
  LayoutDashboard,
  MessageSquare,
  Package,
  ShoppingCart,
  Tags,
  Ruler,
  Truck,
  Boxes,
  Receipt,
  Users,
  DollarSign,
  AlertTriangle,
  Monitor,
  Calculator,
  BarChart3,
  Target,
  RotateCcw,
  FileText,
  Bell,
  Brain,
  Lightbulb,
  Beaker,
  ChevronDown,
  ChevronRight,
  CreditCard,
  Shield,
  TrendingUp,
  AlertCircle,
  CheckCircle2,
  Activity,
  PieChart,
  LineChart,
  BarChart2,
  Zap,
  Clock,
} from "lucide-react";

interface NavItemProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  description?: string;
  active?: boolean;
}

interface NavSectionProps {
  title: string;
  items: NavItemProps[];
}

interface NestedNavSectionProps {
  title: string;
  icon: React.ReactNode;
  items: NavItemProps[];
}

const NavItem = ({ href, icon, label, description, active }: NavItemProps) => {
  return (
    <DropdownMenuItem asChild>
      <Link
        href={href}
        className={cn(
          "flex items-center gap-2 w-full cursor-pointer",
          active && "bg-accent text-accent-foreground"
        )}
      >
        {icon}
        <div className="flex flex-col">
          <span className="text-sm font-medium">{label}</span>
          {description && (
            <span className="text-xs text-muted-foreground">{description}</span>
          )}
        </div>
      </Link>
    </DropdownMenuItem>
  );
};

const NavSection = ({ title, items }: NavSectionProps) => {
  return (
    <>
      <DropdownMenuLabel className="text-xs font-medium text-muted-foreground">
        {title}
      </DropdownMenuLabel>
      {items.map((item) => (
        <NavItem key={item.href} {...item} />
      ))}
      <DropdownMenuSeparator />
    </>
  );
};

const NestedNavSection = ({ title, icon, items }: NestedNavSectionProps) => {
  return (
    <DropdownMenuSub>
      <DropdownMenuSubTrigger className="flex items-center gap-2">
        {icon}
        <span>{title}</span>
      </DropdownMenuSubTrigger>
      <DropdownMenuSubContent className="w-64">
        {items.map((item) => (
          <NavItem key={item.href} {...item} />
        ))}
      </DropdownMenuSubContent>
    </DropdownMenuSub>
  );
};

export function HorizontalNavigation() {
  const pathname = usePathname();
  const { user } = useClientAuth();
  const { isChatEnabled } = useSettings();
  
  const isDeveloper = user?.role === "DEVELOPER";
  const isSuperAdmin = user?.role === "SUPER_ADMIN";
  const isAdmin = isDeveloper || isSuperAdmin || user?.role === "FINANCE_ADMIN";
  const isCashier = user?.role === "CASHIER";

  // Main menu items
  const mainItems: NavItemProps[] = [
    ...(!isCashier ? [{
      href: "/dashboard",
      icon: <LayoutDashboard className="h-4 w-4" />,
      label: "Dashboard",
      description: "Overview and key metrics",
      active: pathname === "/dashboard",
    }] : []),
    ...(isCashier ? [{
      href: "/pos",
      icon: <ShoppingCart className="h-4 w-4" />,
      label: "Point of Sale",
      description: "Process sales transactions",
      active: pathname === "/pos",
    }] : []),
    {
      href: "/transactions",
      icon: <Receipt className="h-4 w-4" />,
      label: "Transactions",
      description: "View transaction history",
      active: pathname.startsWith("/transactions"),
    },
    {
      href: "/customers",
      icon: <Users className="h-4 w-4" />,
      label: "Customers",
      description: "Manage customer information",
      active: pathname.startsWith("/customers"),
    },
    {
      href: "/notifications",
      icon: <Bell className="h-4 w-4" />,
      label: "Notifications",
      description: "View system notifications",
      active: pathname.startsWith("/notifications"),
    },
  ];

  // Inventory menu items (only for non-developer and non-cashier roles)
  const inventoryItems = !isDeveloper && !isCashier ? {
    products: {
      title: "Products",
      icon: <Package className="h-4 w-4" />,
      items: [
        {
          href: "/inventory/products",
          icon: <Package className="h-4 w-4" />,
          label: "Product List",
          description: "Manage product catalog",
          active: pathname.startsWith("/inventory/products"),
        },
        {
          href: "/inventory/categories",
          icon: <Tags className="h-4 w-4" />,
          label: "Categories",
          description: "Organize product categories",
          active: pathname.startsWith("/inventory/categories"),
        },
        {
          href: "/inventory/units",
          icon: <Ruler className="h-4 w-4" />,
          label: "Units",
          description: "Manage measurement units",
          active: pathname.startsWith("/inventory/units"),
        },
      ],
    },
    suppliers: {
      title: "Suppliers",
      icon: <Truck className="h-4 w-4" />,
      items: [
        {
          href: "/inventory/suppliers",
          icon: <Truck className="h-4 w-4" />,
          label: "Supplier List",
          description: "Manage supplier information",
          active: pathname.startsWith("/inventory/suppliers"),
        },
        {
          href: "/inventory/supplier-cost-analysis",
          icon: <ClipboardList className="h-4 w-4" />,
          label: "Supplier Cost Analysis",
          description: "Analyze supplier performance",
          active: pathname.startsWith("/inventory/supplier-cost-analysis"),
        },
      ],
    },
    purchaseOrders: {
      title: "Purchase Orders",
      icon: <FileText className="h-4 w-4" />,
      items: [
        {
          href: "/inventory/purchase-orders",
          icon: <FileText className="h-4 w-4" />,
          label: "Purchase Orders",
          description: "Manage purchase orders",
          active: pathname.startsWith("/inventory/purchase-orders") &&
                  !pathname.includes("/templates") &&
                  !pathname.includes("/reports") &&
                  !pathname.includes("/po-suggestions"),
        },
        {
          href: "/inventory/po-suggestions",
          icon: <Lightbulb className="h-4 w-4" />,
          label: "PO Suggestions",
          description: "AI-powered purchase suggestions",
          active: pathname.startsWith("/inventory/po-suggestions"),
        },
        {
          href: "/inventory/purchase-order-templates",
          icon: <FileText className="h-4 w-4" />,
          label: "PO Templates",
          description: "Manage PO templates",
          active: pathname.startsWith("/inventory/purchase-order-templates"),
        },
        {
          href: "/inventory/purchase-orders/reports",
          icon: <ClipboardList className="h-4 w-4" />,
          label: "PO Reports",
          description: "Purchase order analytics",
          active: pathname.startsWith("/inventory/purchase-orders/reports"),
        },
      ],
    },
    stockManagement: {
      title: "Stock Management",
      icon: <Boxes className="h-4 w-4" />,
      items: [
        {
          href: "/inventory/stock",
          icon: <Boxes className="h-4 w-4" />,
          label: "Stock Management",
          description: "Manage inventory levels",
          active: pathname === "/inventory/stock" ||
                  pathname.startsWith("/inventory/stock/edit") ||
                  pathname === "/inventory/stock/new" ||
                  pathname.startsWith("/inventory/stock/transfer"),
        },
        {
          href: "/inventory/batches",
          icon: <Package className="h-4 w-4" />,
          label: "Batch Tracking",
          description: "Track product batches",
          active: pathname.startsWith("/inventory/batches"),
        },
        {
          href: "/inventory/returns",
          icon: <RotateCcw className="h-4 w-4" />,
          label: "Returns & Exchanges",
          description: "Process returns and exchanges",
          active: pathname.startsWith("/inventory/returns"),
        },
      ],
    },
    reports: {
      title: "Reports",
      icon: <ClipboardList className="h-4 w-4" />,
      items: [
        {
          href: "/inventory/reports",
          icon: <ClipboardList className="h-4 w-4" />,
          label: "Inventory Reports",
          description: "Inventory analytics and reports",
          active: pathname.startsWith("/inventory/reports"),
        },
        {
          href: "/inventory/transfer-reports",
          icon: <ClipboardList className="h-4 w-4" />,
          label: "Transfer Reports",
          description: "Stock transfer analytics",
          active: pathname.startsWith("/inventory/transfer-reports"),
        },
      ],
    },
  } : null;

  // Finance menu items (for super admin and finance admin)
  const financeItems = !isDeveloper && (isSuperAdmin || user?.role === "FINANCE_ADMIN") ? {
    invoices: [
      {
        href: "/invoices",
        icon: <FileText className="h-4 w-4" />,
        label: "Invoice Management",
        description: "Create and manage invoices",
        active: pathname.startsWith("/invoices"),
      },
    ],
    cashManagement: [
      {
        href: "/admin/cash-audit",
        icon: <Calculator className="h-4 w-4" />,
        label: "Cash Audit",
        description: "Cash reconciliation and audit",
        active: pathname.startsWith("/admin/cash-audit"),
      },
      {
        href: "/admin/drawer-sessions",
        icon: <CreditCard className="h-4 w-4" />,
        label: "Drawer Sessions",
        description: "View cash drawer sessions",
        active: pathname.startsWith("/admin/drawer-sessions"),
      },
    ],
    analytics: [
      {
        href: "/admin/revenue-targets",
        icon: <Target className="h-4 w-4" />,
        label: "Revenue Targets",
        description: "Set and track revenue goals",
        active: pathname.startsWith("/admin/revenue-targets"),
      },
      {
        href: "/admin/analytics",
        icon: <BarChart3 className="h-4 w-4" />,
        label: "Analytics",
        description: "Business analytics dashboard",
        active: pathname === "/admin/analytics",
      },
      {
        href: "/admin/analytics/advanced",
        icon: <Brain className="h-4 w-4" />,
        label: "Advanced Analytics",
        description: "Advanced business insights",
        active: pathname.startsWith("/admin/analytics/advanced"),
      },
    ],
    financialReports: [
      {
        href: "/reports/financial",
        icon: <FileText className="h-4 w-4" />,
        label: "Financial Reports",
        description: "Comprehensive financial reporting",
        active: pathname.startsWith("/reports/financial"),
      },
    ],
  } : null;

  // Quality Management menu items (for super admin, warehouse admin, and finance admin)
  const qualityItems = !isDeveloper && !isCashier && (isSuperAdmin || user?.role === "WAREHOUSE_ADMIN" || user?.role === "FINANCE_ADMIN") ? {
    qualityIssues: [
      {
        href: "/quality/issues",
        icon: <AlertCircle className="h-4 w-4" />,
        label: "Quality Issues",
        description: "Track and manage quality problems",
        active: pathname.startsWith("/quality/issues"),
      },
      {
        href: "/quality/improvements",
        icon: <TrendingUp className="h-4 w-4" />,
        label: "Quality Improvements",
        description: "Manage improvement plans",
        active: pathname.startsWith("/quality/improvements"),
      },
    ],
    configuration: isSuperAdmin || user?.role === "WAREHOUSE_ADMIN" ? [
      {
        href: "/quality/thresholds",
        icon: <Shield className="h-4 w-4" />,
        label: "Quality Thresholds",
        description: "Configure quality monitoring",
        active: pathname.startsWith("/quality/thresholds"),
      },
      {
        href: "/quality/alerts",
        icon: <Bell className="h-4 w-4" />,
        label: "Quality Alerts",
        description: "Manage alert system",
        active: pathname.startsWith("/quality/alerts"),
      },
      {
        href: "/quality/scheduler",
        icon: <Clock className="h-4 w-4" />,
        label: "Quality Scheduler",
        description: "Automated monitoring jobs",
        active: pathname.startsWith("/quality/scheduler"),
      },
    ] : [],
    analytics: [
      {
        href: "/analytics/supplier-accountability",
        icon: <PieChart className="h-4 w-4" />,
        label: "Supplier Accountability",
        description: "Supplier performance reports",
        active: pathname.startsWith("/analytics/supplier-accountability"),
      },
      {
        href: "/analytics/defect-tracking",
        icon: <AlertTriangle className="h-4 w-4" />,
        label: "Defect Tracking",
        description: "Defect analysis dashboard",
        active: pathname.startsWith("/analytics/defect-tracking"),
      },
      {
        href: "/analytics/quality-trends",
        icon: <LineChart className="h-4 w-4" />,
        label: "Quality Trends",
        description: "Quality trend analysis",
        active: pathname.startsWith("/analytics/quality-trends"),
      },
      {
        href: "/analytics/return-dashboard",
        icon: <BarChart2 className="h-4 w-4" />,
        label: "Return Dashboard",
        description: "Comprehensive return analytics",
        active: pathname.startsWith("/analytics/return-dashboard"),
      },
      {
        href: "/analytics/quality-improvements",
        icon: <CheckCircle2 className="h-4 w-4" />,
        label: "Improvement Analytics",
        description: "Track improvement effectiveness",
        active: pathname.startsWith("/analytics/quality-improvements"),
      },
    ],
  } : null;

  // Admin menu items (for super admin and finance admin, but not developer)
  const adminItems = !isDeveloper && (isSuperAdmin || user?.role === "FINANCE_ADMIN") ? {
    userManagement: isSuperAdmin ? [
      {
        href: "/admin/users",
        icon: <UserPlus className="h-4 w-4" />,
        label: "User Management",
        description: "Manage system users",
        active: pathname === "/admin/users",
      },
    ] : [],
    systemManagement: [
      {
        href: "/admin/activity-logs",
        icon: <ClipboardList className="h-4 w-4" />,
        label: "Activity Logs",
        description: "View system activity logs",
        active: pathname === "/admin/activity-logs",
      },
      {
        href: "/admin/terminals",
        icon: <Monitor className="h-4 w-4" />,
        label: "Terminals",
        description: "Manage POS terminals",
        active: pathname.startsWith("/admin/terminals"),
      },
      {
        href: "/admin/backup",
        icon: <Database className="h-4 w-4" />,
        label: "Backup & Restore",
        description: "System backup and restore",
        active: pathname === "/admin/backup",
      },
    ],
    communication: isSuperAdmin && isChatEnabled ? [
      {
        href: "/admin/chat",
        icon: <MessageSquare className="h-4 w-4" />,
        label: "Chat Management",
        description: "Manage chat system",
        active: pathname === "/admin/chat",
      },
    ] : [],
  } : null;

  // Development menu items (only for developer role)
  const developmentItems = isDeveloper ? [
    {
      href: "/tests",
      icon: <Beaker className="h-4 w-4" />,
      label: "API Tests",
      description: "Test API endpoints",
      active: pathname.startsWith("/tests"),
    },
  ] : null;

  // Settings menu items (not for cashier)
  const settingsItems = !isCashier ? {
    applicationSettings: [
      {
        href: "/settings",
        icon: <Settings className="h-4 w-4" />,
        label: "Settings",
        description: "Application preferences",
        active: pathname === "/settings",
      },
      {
        href: "/settings/notifications",
        icon: <Bell className="h-4 w-4" />,
        label: "Notification Preferences",
        description: "Configure notifications",
        active: pathname === "/settings/notifications",
      },
    ],
    support: [
      {
        href: "/help",
        icon: <HelpCircle className="h-4 w-4" />,
        label: "Help",
        description: "Get help and support",
        active: pathname === "/help",
      },
    ],
  } : null;

  return (
    <div className="flex items-center space-x-1 overflow-x-auto">
      {/* Main Menu */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
            Main
            <ChevronDown className="ml-1 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-80" align="start">
          <NavSection title="Core Operations" items={mainItems} />
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Inventory Menu - Only show for non-developer and non-cashier roles */}
      {inventoryItems && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
              Inventory
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64" align="start">
            <NestedNavSection
              title={inventoryItems.products.title}
              icon={inventoryItems.products.icon}
              items={inventoryItems.products.items}
            />
            <NestedNavSection
              title={inventoryItems.suppliers.title}
              icon={inventoryItems.suppliers.icon}
              items={inventoryItems.suppliers.items}
            />
            <NestedNavSection
              title={inventoryItems.purchaseOrders.title}
              icon={inventoryItems.purchaseOrders.icon}
              items={inventoryItems.purchaseOrders.items}
            />
            <NestedNavSection
              title={inventoryItems.stockManagement.title}
              icon={inventoryItems.stockManagement.icon}
              items={inventoryItems.stockManagement.items}
            />
            <NestedNavSection
              title={inventoryItems.reports.title}
              icon={inventoryItems.reports.icon}
              items={inventoryItems.reports.items}
            />
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Finance Menu - Only show for super admin and finance admin */}
      {financeItems && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
              Finance
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="start">
            <NavSection title="Invoices" items={financeItems.invoices} />
            <NavSection title="Cash Management" items={financeItems.cashManagement} />
            <NavSection title="Financial Reports" items={financeItems.financialReports} />
            <NavSection title="Analytics & Reports" items={financeItems.analytics} />
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Quality Menu - Only show for super admin, warehouse admin, and finance admin */}
      {qualityItems && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
              Quality
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="start">
            <NavSection title="Quality Management" items={qualityItems.qualityIssues} />
            {qualityItems.configuration.length > 0 && (
              <NavSection title="Configuration" items={qualityItems.configuration} />
            )}
            <NavSection title="Quality Analytics" items={qualityItems.analytics} />
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Admin Menu - Only show for super admin and finance admin, but not developer */}
      {adminItems && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
              Admin
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="start">
            {adminItems.userManagement.length > 0 && (
              <NavSection title="User Management" items={adminItems.userManagement} />
            )}
            <NavSection title="System Management" items={adminItems.systemManagement} />
            {adminItems.communication.length > 0 && (
              <NavSection title="Communication" items={adminItems.communication} />
            )}
            {developmentItems && (
              <NavSection title="Development" items={developmentItems} />
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Settings Menu - Not for cashier */}
      {settingsItems && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-9 px-3 whitespace-nowrap">
              Settings
              <ChevronDown className="ml-1 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80" align="start">
            <NavSection title="Application Settings" items={settingsItems.applicationSettings} />
            <NavSection title="Support" items={settingsItems.support} />
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </div>
  );
}
