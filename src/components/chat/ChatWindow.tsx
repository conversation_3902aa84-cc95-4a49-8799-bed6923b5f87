"use client";

import { useState, useEffect, useRef } from "react";
import { Send, X, Minimize, Maximize, User, Star } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useClientAuth } from "@/hooks/use-client-auth";
import { format } from "date-fns";

type Message = {
  id: string;
  content: string;
  senderId: string;
  createdAt: string;
  sender: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
};

type Conversation = {
  id: string;
  title: string;
  participants: {
    id: string;
    name: string;
    email: string;
    role: string;
  }[];
  messages: Message[];
  isStarred?: boolean;
  pagination?: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
  createdAt: string;
  updatedAt: string;
  lastActivity?: string;
};

interface ChatWindowProps {
  conversationId: string;
  onClose: () => void;
}

export function ChatWindow({ conversationId, onClose }: ChatWindowProps) {
  const [conversation, setConversation] = useState<Conversation | null>(null);
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [minimized, setMinimized] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useClientAuth();

  // Fetch conversation and messages
  const fetchConversation = async (isInitialLoad = false) => {
    if (!user) return;

    try {
      if (isInitialLoad) {
        setLoading(true);
      }
      setError(null);

      const response = await fetch(`/api/conversations/${conversationId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch conversation");
      }

      const data = await response.json();

      // Only update if there are new messages or this is the initial load
      if (
        isInitialLoad ||
        !conversation ||
        data.conversation.messages.length !== conversation.messages.length
      ) {
        // If we have temporary messages, merge them with server messages intelligently
        if (conversation && conversation.messages.some((msg) => msg.id.startsWith("temp-"))) {
          const tempMessages = conversation.messages.filter((msg) => msg.id.startsWith("temp-"));
          const serverMessages = data.conversation.messages;

          // Remove temp messages that have been replaced by server messages
          const filteredTempMessages = tempMessages.filter((tempMsg) => {
            return !serverMessages.some(
              (serverMsg) =>
                serverMsg.content === tempMsg.content &&
                serverMsg.senderId === tempMsg.senderId &&
                Math.abs(
                  new Date(serverMsg.createdAt).getTime() - new Date(tempMsg.createdAt).getTime()
                ) < 10000 // Within 10 seconds
            );
          });

          // Combine server messages with remaining temp messages
          const allMessages = [...serverMessages, ...filteredTempMessages].sort(
            (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );

          setConversation({
            ...data.conversation,
            messages: allMessages,
          });
        } else {
          setConversation(data.conversation);
        }
      }
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching the conversation");
      console.error("Error fetching conversation:", err);
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      }
    }
  };

  // Send a message
  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || !user || !conversation) return;

    // Store the message content before clearing the input
    const messageContent = message.trim();

    // Clear the input immediately for better UX
    setMessage("");

    // Create a temporary message to show immediately in the UI
    const tempMessage = {
      id: `temp-${Date.now()}`,
      content: messageContent,
      senderId: user.id,
      createdAt: new Date().toISOString(),
      sender: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
      },
    };

    // Update the UI immediately with the temporary message
    setConversation({
      ...conversation,
      messages: [...conversation.messages, tempMessage],
    });

    try {
      // Send the message to the server
      const response = await fetch(`/api/conversations/${conversationId}/messages`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ content: messageContent }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send message");
      }

      // The actual message will be fetched in the next polling cycle
      // Trigger an immediate fetch after a short delay to get the server message
      setTimeout(() => {
        fetchConversation(false);
      }, 500);
    } catch (err: any) {
      console.error("Error sending message:", err);
      // If there's an error, remove the temporary message
      setConversation({
        ...conversation,
        messages: conversation.messages.filter((msg) => msg.id !== tempMessage.id),
      });
    }
  };

  // Scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [conversation?.messages]);

  // Fetch conversation on mount and when conversationId changes
  useEffect(() => {
    if (user && conversationId) {
      fetchConversation(true); // true indicates initial load
    }
  }, [user, conversationId]);

  // Poll for new messages every 2 seconds
  useEffect(() => {
    if (!user || !conversationId) return;

    const interval = setInterval(() => {
      fetchConversation(false); // false indicates polling update
    }, 2000); // Reduced to 2 seconds for more responsive updates

    return () => clearInterval(interval);
  }, [user, conversationId]);

  // Toggle star status
  const toggleStar = async () => {
    if (!user || !conversationId || !conversation) return;

    try {
      const method = conversation.isStarred ? "DELETE" : "POST";
      const response = await fetch(`/api/conversations/${conversationId}/star`, {
        method,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `Failed to ${conversation.isStarred ? "unstar" : "star"} conversation`
        );
      }

      // Update local state
      setConversation({
        ...conversation,
        isStarred: !conversation.isStarred,
      });
    } catch (err: any) {
      console.error(
        `Error ${conversation.isStarred ? "unstarring" : "starring"} conversation:`,
        err
      );
    }
  };

  // Get the other participant's name for the chat title
  const getChatTitle = () => {
    if (!conversation) return "Chat";

    if (conversation.title) return conversation.title;

    const otherParticipants = conversation.participants.filter((p) => p.id !== user?.id);

    if (otherParticipants.length === 0) return "Chat";

    if (otherParticipants.length === 1) return otherParticipants[0].name;

    return `${otherParticipants[0].name} and ${otherParticipants.length - 1} others`;
  };

  return (
    <div
      className={`fixed bottom-0 right-4 z-50 flex flex-col rounded-t-lg border bg-background shadow-lg ${
        minimized ? "h-12" : "h-96"
      } w-80 transition-all duration-200`}
    >
      {/* Chat header */}
      <div className="flex items-center justify-between border-b p-2">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-medium">{getChatTitle()}</h3>
          {conversation && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={toggleStar}
              title={conversation.isStarred ? "Unstar conversation" : "Star conversation"}
            >
              <Star
                className={`h-4 w-4 ${conversation.isStarred ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"}`}
              />
            </Button>
          )}
        </div>
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6"
            onClick={() => setMinimized(!minimized)}
          >
            {minimized ? <Maximize className="h-4 w-4" /> : <Minimize className="h-4 w-4" />}
          </Button>
          <Button variant="ghost" size="icon" className="h-6 w-6" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Chat body */}
      {!minimized && (
        <>
          <div className="flex-1 overflow-y-auto p-3">
            {loading && (
              <div className="flex h-full items-center justify-center">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            )}

            {error && (
              <div className="flex h-full items-center justify-center text-sm text-destructive">
                {error}
              </div>
            )}

            {!loading &&
              !error &&
              (!conversation?.messages || conversation.messages.length === 0) && (
                <div className="flex h-full items-center justify-center text-sm text-muted-foreground">
                  No messages yet. Start the conversation!
                </div>
              )}

            {!loading &&
              !error &&
              conversation?.messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`mb-2 flex ${
                    msg.senderId === user?.id ? "justify-end" : "justify-start"
                  }`}
                >
                  <div
                    className={`max-w-[80%] rounded-lg p-2 ${
                      msg.senderId === user?.id ? "bg-primary text-primary-foreground" : "bg-accent"
                    }`}
                  >
                    {msg.senderId !== user?.id && (
                      <div className="mb-1 text-xs font-medium">
                        {msg.sender ? msg.sender.name : "Unknown User"}
                      </div>
                    )}
                    <div className="text-sm">{msg.content}</div>
                    <div className="mt-1 text-right text-xs opacity-70">
                      {format(new Date(msg.createdAt), "h:mm a")}
                    </div>
                  </div>
                </div>
              ))}
            <div ref={messagesEndRef} />
          </div>

          {/* Chat input */}
          <form onSubmit={sendMessage} className="border-t p-2">
            <div className="flex items-center gap-2">
              <Input
                type="text"
                placeholder="Type a message..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                className="h-9"
              />
              <Button type="submit" size="icon" className="h-9 w-9" disabled={!message.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </>
      )}
    </div>
  );
}
