"use client";

import { useState, useEffect } from "react";
import { MessageSquare, X, User, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useClientAuth } from "@/hooks/use-client-auth";
import { ChatWindow } from "./ChatWindow";
import { NewChatDialog } from "./NewChatDialog";

type Conversation = {
  id: string;
  title: string;
  participants: {
    id: string;
    name: string;
    email: string;
    role: string;
  }[];
  latestMessage: {
    id: string;
    content: string;
    createdAt: string;
  } | null;
  updatedAt: string;
};

export function ChatButton() {
  const [isOpen, setIsOpen] = useState(false);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showNewChatDialog, setShowNewChatDialog] = useState(false);
  const { user } = useClientAuth();

  // Fetch conversations
  const fetchConversations = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/conversations");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch conversations");
      }

      const data = await response.json();
      setConversations(data.conversations);
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching conversations");
      console.error("Error fetching conversations:", err);
    } finally {
      setLoading(false);
    }
  };

  // Handle creating a new conversation
  const handleNewConversation = (newConversation: Conversation) => {
    setConversations((prev) => [newConversation, ...prev]);
    setActiveConversation(newConversation.id);
    setShowNewChatDialog(false);
  };

  // Fetch conversations on mount and when user changes
  useEffect(() => {
    if (user && isOpen) {
      fetchConversations();
    }
  }, [user, isOpen]);

  // Get the other participant's name for the conversation title
  const getConversationTitle = (conversation: Conversation) => {
    if (conversation.title) return conversation.title;
    
    const otherParticipants = conversation.participants.filter(
      (p) => p.id !== user?.id
    );
    
    if (otherParticipants.length === 0) return "Chat";
    
    if (otherParticipants.length === 1) return otherParticipants[0].name;
    
    return `${otherParticipants[0].name} and ${otherParticipants.length - 1} others`;
  };

  // Get the latest message preview
  const getLatestMessagePreview = (conversation: Conversation) => {
    if (!conversation.latestMessage) return "No messages yet";
    
    return conversation.latestMessage.content.length > 30
      ? `${conversation.latestMessage.content.substring(0, 30)}...`
      : conversation.latestMessage.content;
  };

  return (
    <>
      {/* Chat button */}
      <button
        className="fixed bottom-4 right-4 z-40 flex h-12 w-12 items-center justify-center rounded-full bg-primary text-primary-foreground shadow-lg"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <MessageSquare className="h-6 w-6" />}
      </button>

      {/* Chat panel */}
      {isOpen && !activeConversation && (
        <div className="fixed bottom-20 right-4 z-40 w-80 rounded-lg border bg-background shadow-lg">
          <div className="flex items-center justify-between border-b p-3">
            <h3 className="font-medium">Messages</h3>
            <Button
              variant="outline"
              size="sm"
              className="h-8"
              onClick={() => setShowNewChatDialog(true)}
            >
              <Plus className="mr-1 h-4 w-4" /> New Chat
            </Button>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {loading && (
              <div className="flex items-center justify-center p-4">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            )}

            {error && (
              <div className="p-4 text-sm text-destructive">{error}</div>
            )}

            {!loading && !error && conversations.length === 0 && (
              <div className="p-4 text-center text-sm text-muted-foreground">
                No conversations yet. Start a new chat!
              </div>
            )}

            {!loading &&
              !error &&
              conversations.map((conversation) => (
                <button
                  key={conversation.id}
                  className="w-full border-b p-3 text-left hover:bg-accent"
                  onClick={() => setActiveConversation(conversation.id)}
                >
                  <div className="mb-1 font-medium">
                    {getConversationTitle(conversation)}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {getLatestMessagePreview(conversation)}
                  </div>
                </button>
              ))}
          </div>
        </div>
      )}

      {/* Active chat window */}
      {activeConversation && (
        <ChatWindow
          conversationId={activeConversation}
          onClose={() => setActiveConversation(null)}
        />
      )}

      {/* New chat dialog */}
      {showNewChatDialog && (
        <NewChatDialog
          onClose={() => setShowNewChatDialog(false)}
          onConversationCreated={handleNewConversation}
        />
      )}
    </>
  );
}
