"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  AlertTriangle, 
  CheckCircle, 
  Calendar,
  Package,
  DollarSign,
  BarChart3,
  RefreshCw
} from "lucide-react";
import { formatCurrency } from "@/lib/utils";
import type { SupplierDemandAnalysis, DemandForecast } from "@/lib/demand-forecasting";

interface DemandForecastingDashboardProps {
  supplierId: string;
  className?: string;
}

export function DemandForecastingDashboard({ supplierId, className }: DemandForecastingDashboardProps) {
  const [forecastData, setForecastData] = useState<SupplierDemandAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [forecastPeriod, setForecastPeriod] = useState<'30days' | '60days' | '90days'>('60days');

  const fetchForecastData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(
        `/api/analytics/demand-forecasting?supplierId=${supplierId}&forecastPeriod=${forecastPeriod}`
      );
      
      if (!response.ok) {
        throw new Error(`Failed to fetch forecast data: ${response.statusText}`);
      }
      
      const result = await response.json();
      setForecastData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (supplierId) {
      fetchForecastData();
    }
  }, [supplierId, forecastPeriod]);

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getConfidenceBadge = (confidence: number) => {
    if (confidence >= 80) return <Badge variant="default" className="bg-green-100 text-green-800">High ({confidence}%)</Badge>;
    if (confidence >= 60) return <Badge variant="secondary">Medium ({confidence}%)</Badge>;
    return <Badge variant="destructive">Low ({confidence}%)</Badge>;
  };

  const getRiskBadge = (riskFactors: string[]) => {
    if (riskFactors.length === 0) return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
    if (riskFactors.length <= 2) return <Badge variant="secondary">Medium Risk</Badge>;
    return <Badge variant="destructive">High Risk</Badge>;
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Generating demand forecasts...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Error loading demand forecast: {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchForecastData}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!forecastData) {
    return (
      <Alert className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No forecast data available for this supplier.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Controls */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Demand Forecasting</h3>
          <p className="text-sm text-muted-foreground">
            AI-powered demand predictions for {forecastData.supplierName}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={forecastPeriod} onValueChange={(value: any) => setForecastPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="30days">30 Days</SelectItem>
              <SelectItem value="60days">60 Days</SelectItem>
              <SelectItem value="90days">90 Days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={fetchForecastData}>
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{forecastData.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Products with forecasts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Predicted Demand</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {forecastData.aggregateMetrics.totalPredictedDemand.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Units over {forecastPeriod}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recommended Orders</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {forecastData.aggregateMetrics.totalRecommendedOrderValue.toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Units to order
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Confidence</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(forecastData.aggregateMetrics.averageConfidenceLevel)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Forecast reliability
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recommendations */}
      {forecastData.recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Strategic Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {forecastData.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                  <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'secondary' : 'default'}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <p className="font-medium">{rec.action}</p>
                    <p className="text-sm text-muted-foreground">{rec.impact}</p>
                    <p className="text-xs text-muted-foreground mt-1">Timeline: {rec.timeframe}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product Forecasts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Product Demand Forecasts</CardTitle>
          <p className="text-sm text-muted-foreground">
            Detailed forecasts for each product from this supplier
          </p>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Current Stock</TableHead>
                  <TableHead>Predicted Demand</TableHead>
                  <TableHead>Recommended Order</TableHead>
                  <TableHead>Optimal Order Date</TableHead>
                  <TableHead>Trend</TableHead>
                  <TableHead>Confidence</TableHead>
                  <TableHead>Risk Level</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {forecastData.forecasts.slice(0, 10).map((forecast) => (
                  <TableRow key={forecast.productId}>
                    <TableCell className="font-medium">
                      {forecast.productName}
                    </TableCell>
                    <TableCell>{forecast.currentStock.toLocaleString()}</TableCell>
                    <TableCell className="font-medium">
                      {forecast.predictedDemand.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      {forecast.recommendedOrderQuantity > 0 ? (
                        <span className="font-medium text-blue-600">
                          {forecast.recommendedOrderQuantity.toLocaleString()}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">No order needed</span>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3 text-muted-foreground" />
                        <span className="text-sm">
                          {new Date(forecast.optimalOrderDate).toLocaleDateString()}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        {getTrendIcon(forecast.trendDirection)}
                        <span className="text-sm capitalize">
                          {forecast.trendDirection}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getConfidenceBadge(forecast.confidenceLevel)}
                    </TableCell>
                    <TableCell>
                      {getRiskBadge(forecast.riskFactors)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
          
          {forecastData.forecasts.length > 10 && (
            <div className="mt-4 text-center">
              <p className="text-sm text-muted-foreground">
                Showing top 10 products. Total: {forecastData.forecasts.length} products
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
