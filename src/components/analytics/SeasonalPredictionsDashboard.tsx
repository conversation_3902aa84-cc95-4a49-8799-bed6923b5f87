"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  TrendingUp,
  TrendingDown,
  Minus,
  RefreshCw,
  Calendar,
  Shield,
  DollarSign,
  Truck
} from "lucide-react";
import type { SeasonalAnalysisReport, SeasonalPrediction } from "@/lib/seasonal-supplier-predictions";

interface SeasonalPredictionsDashboardProps {
  supplierId: string;
  className?: string;
}

export function SeasonalPredictionsDashboard({ supplierId, className }: SeasonalPredictionsDashboardProps) {
  const [predictionData, setPredictionData] = useState<SeasonalAnalysisReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchPredictionData = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/analytics/seasonal-predictions?supplierId=${supplierId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch prediction data: ${response.statusText}`);
      }
      
      const result = await response.json();
      setPredictionData(result.data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (supplierId) {
      fetchPredictionData();
    }
  }, [supplierId]);

  const getRiskBadge = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high': return <Badge variant="destructive">High Risk</Badge>;
      case 'medium': return <Badge variant="secondary">Medium Risk</Badge>;
      case 'low': return <Badge variant="default" className="bg-green-100 text-green-800">Low Risk</Badge>;
      default: return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Minus className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeasonIcon = (season: string) => {
    switch (season) {
      case 'Q1': return '🌱';
      case 'Q2': return '🌸';
      case 'Q3': return '☀️';
      case 'Q4': return '🍂';
      case 'Ramadan': return '🌙';
      case 'Christmas': return '🎄';
      default: return '📅';
    }
  };

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-center p-8">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Analyzing seasonal patterns...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Error loading seasonal predictions: {error}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={fetchPredictionData}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  if (!predictionData) {
    return (
      <Alert className={className}>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No seasonal prediction data available for this supplier.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold">Seasonal Performance Predictions</h3>
          <p className="text-sm text-muted-foreground">
            AI-powered seasonal analysis for {predictionData.supplierName}
          </p>
        </div>
        <Button variant="outline" size="sm" onClick={fetchPredictionData}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      </div>

      {/* Overall Risk Assessment */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Overall Risk Assessment
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="font-medium">Risk Level:</span>
              {getRiskBadge(predictionData.overallRiskAssessment.riskLevel)}
            </div>
            
            {predictionData.overallRiskAssessment.primaryConcerns.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Primary Concerns:</h4>
                <ul className="space-y-1">
                  {predictionData.overallRiskAssessment.primaryConcerns.map((concern, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                      <AlertTriangle className="h-3 w-3 text-amber-500 mt-0.5 flex-shrink-0" />
                      {concern}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {predictionData.overallRiskAssessment.mitigationStrategies.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Mitigation Strategies:</h4>
                <ul className="space-y-1">
                  {predictionData.overallRiskAssessment.mitigationStrategies.map((strategy, index) => (
                    <li key={index} className="text-sm text-muted-foreground flex items-start gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                      {strategy}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Strategic Recommendations */}
      {predictionData.strategicRecommendations.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Strategic Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {predictionData.strategicRecommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-3 p-3 bg-muted/50 rounded-lg">
                  <Badge variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'secondary' : 'default'}>
                    {rec.priority}
                  </Badge>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge variant="outline" className="text-xs">
                        {rec.category}
                      </Badge>
                    </div>
                    <p className="font-medium">{rec.action}</p>
                    <p className="text-sm text-muted-foreground">{rec.expectedImpact}</p>
                    <p className="text-xs text-muted-foreground mt-1">Timeline: {rec.timeframe}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Seasonal Predictions Table */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Seasonal Performance Predictions</CardTitle>
          <p className="text-sm text-muted-foreground">
            Predicted performance metrics for each season
          </p>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Season</TableHead>
                  <TableHead>Quality Score</TableHead>
                  <TableHead>Delivery Performance</TableHead>
                  <TableHead>Price Volatility</TableHead>
                  <TableHead>Capacity Risk</TableHead>
                  <TableHead>Data Quality</TableHead>
                  <TableHead>Key Risks</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {predictionData.upcomingSeasons.map((season) => (
                  <TableRow key={season.season}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{getSeasonIcon(season.season)}</span>
                        <div>
                          <div className="font-medium">{season.season}</div>
                          <div className="text-xs text-muted-foreground">
                            {season.seasonPeriod.description}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{season.predictions.qualityScore.predicted}/100</span>
                          {getTrendIcon(season.predictions.qualityScore.trend)}
                        </div>
                        <div className="text-xs">
                          {getRiskBadge(season.predictions.qualityScore.riskLevel)}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Confidence: {season.predictions.qualityScore.confidence}%
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <Truck className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">{season.predictions.deliveryPerformance.onTimeDeliveryRate}%</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Avg delay: {season.predictions.deliveryPerformance.predictedDelayDays} days
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Confidence: {season.predictions.deliveryPerformance.confidence}%
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-3 w-3 text-muted-foreground" />
                          <span className="text-sm">
                            {season.predictions.pricingTrends.expectedPriceChange > 0 ? '+' : ''}
                            {season.predictions.pricingTrends.expectedPriceChange.toFixed(1)}%
                          </span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Volatility: {season.predictions.pricingTrends.volatility}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Confidence: {season.predictions.pricingTrends.confidence}%
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        {getRiskBadge(season.predictions.capacityConstraints.riskOfShortages)}
                        <div className="text-xs text-muted-foreground">
                          Capacity: {season.predictions.capacityConstraints.expectedCapacityUtilization}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Confidence: {season.predictions.capacityConstraints.confidence}%
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        <Badge variant={
                          season.historicalData.dataQuality === 'excellent' ? 'default' :
                          season.historicalData.dataQuality === 'good' ? 'secondary' :
                          season.historicalData.dataQuality === 'fair' ? 'outline' : 'destructive'
                        }>
                          {season.historicalData.dataQuality}
                        </Badge>
                        <div className="text-xs text-muted-foreground">
                          {season.historicalData.yearsAnalyzed} years data
                        </div>
                      </div>
                    </TableCell>
                    
                    <TableCell>
                      <div className="space-y-1">
                        {season.riskFactors.slice(0, 2).map((risk, index) => (
                          <div key={index} className="text-xs text-muted-foreground flex items-start gap-1">
                            <AlertTriangle className="h-3 w-3 text-amber-500 mt-0.5 flex-shrink-0" />
                            <span className="line-clamp-2">{risk}</span>
                          </div>
                        ))}
                        {season.riskFactors.length > 2 && (
                          <div className="text-xs text-muted-foreground">
                            +{season.riskFactors.length - 2} more risks
                          </div>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
