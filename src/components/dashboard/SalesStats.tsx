"use client";

import { useState, useEffect } from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatCurrency } from "@/lib/utils";
import { TrendingUp, TrendingDown, CreditCard, ShoppingCart } from "lucide-react";

interface SalesStats {
  totalRevenue: number;
  totalSales: number;
  revenueChange: number;
  salesChange: number;
}

export function TotalRevenueCard({ stats }: { stats: SalesStats }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
        <CreditCard className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
        <div
          className={`flex items-center pt-1 text-xs ${stats.revenueChange >= 0 ? "text-green-500" : "text-red-500"}`}
        >
          {stats.revenueChange >= 0 ? (
            <TrendingUp className="mr-1 h-3 w-3" />
          ) : (
            <TrendingDown className="mr-1 h-3 w-3" />
          )}
          <span>
            {stats.revenueChange >= 0 ? "+" : ""}
            {(stats.revenueChange ?? 0).toFixed(1)}%
          </span>
        </div>
      </CardContent>
    </Card>
  );
}

export function TotalSalesCard({ stats }: { stats: SalesStats }) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Total Sales</CardTitle>
        <ShoppingCart className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.totalSales}</div>
        <div
          className={`flex items-center pt-1 text-xs ${stats.salesChange >= 0 ? "text-green-500" : "text-red-500"}`}
        >
          {stats.salesChange >= 0 ? (
            <TrendingUp className="mr-1 h-3 w-3" />
          ) : (
            <TrendingDown className="mr-1 h-3 w-3" />
          )}
          <span>
            {stats.salesChange >= 0 ? "+" : ""}
            {(stats.salesChange ?? 0).toFixed(1)}%
          </span>
        </div>
      </CardContent>
    </Card>
  );
}

export function useSalesStats(dateRange?: string | null) {
  const [stats, setStats] = useState<SalesStats>({
    totalRevenue: 0,
    totalSales: 0,
    revenueChange: 0,
    salesChange: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSalesStats = async () => {
      try {
        setIsLoading(true);
        // Fetch total revenue and sales count
        const url = dateRange
          ? `/api/transactions/stats?date=${dateRange}`
          : "/api/transactions/stats";

        const response = await fetch(url);

        if (!response.ok) {
          throw new Error("Failed to fetch sales statistics");
        }

        const data = await response.json();

        setStats({
          totalRevenue: data.totalRevenue || 0,
          totalSales: data.totalSales || 0,
          revenueChange: data.revenueChange || 0,
          salesChange: data.salesChange || 0,
        });
      } catch (err) {
        console.error("Error fetching sales statistics:", err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");

        // Set fallback data
        setStats({
          totalRevenue: 0,
          totalSales: 0,
          revenueChange: 0,
          salesChange: 0,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchSalesStats();
  }, [dateRange]);

  return { stats, isLoading, error };
}
