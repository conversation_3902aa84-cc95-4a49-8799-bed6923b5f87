"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Package, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function ProductCount() {
  const [productCount, setProductCount] = useState<{ total: number; active: number } | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch product count
  useEffect(() => {
    const fetchProductCount = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch("/api/products/count");

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to fetch product count");
        }

        const data = await response.json();
        setProductCount(data);
      } catch (err: any) {
        setError(err.message || "An error occurred while fetching product count");
        console.error("Error fetching product count:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchProductCount();
  }, []);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Total Products</CardTitle>
        <Package className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {error ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : loading ? (
          <div className="text-sm text-muted-foreground">Loading...</div>
        ) : (
          <>
            <div className="text-2xl font-bold">{productCount?.total || 0}</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              <span>{productCount?.active || 0} active products</span>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
