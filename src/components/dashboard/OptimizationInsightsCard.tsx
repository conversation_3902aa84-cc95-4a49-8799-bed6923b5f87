"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  Target, 
  TrendingUp, 
  AlertTriangle, 
  DollarSign, 
  ArrowRight,
  Lightbulb,
  Package,
  Clock
} from "lucide-react";
import Link from "next/link";

interface OptimizationSummary {
  totalRecommendations: number;
  highPriorityCount: number;
  estimatedSavings: number;
  topRisks: string[];
  lastGenerated: string;
}

export function OptimizationInsightsCard() {
  const [summary, setSummary] = useState<OptimizationSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadOptimizationSummary();
  }, []);

  const loadOptimizationSummary = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/inventory/optimization-recommendations", {
        method: "HEAD", // Use HEAD method for summary endpoint
      });

      if (!response.ok) {
        if (response.status === 403) {
          // User doesn't have permission - don't show error, just don't render
          setLoading(false);
          return;
        }
        throw new Error("Failed to load optimization summary");
      }

      const data = await response.json();
      setSummary(data);
    } catch (error) {
      console.error("Error loading optimization summary:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M IDR`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K IDR`;
    }
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  // Don't render if user doesn't have permission or there's an error
  if (error || (!loading && !summary)) {
    return null;
  }

  if (loading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Inventory Optimization</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent className="space-y-3">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-8 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!summary) return null;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Inventory Optimization</CardTitle>
        <Target className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <Lightbulb className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-muted-foreground">Recommendations</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-2xl font-bold">{summary.totalRecommendations}</span>
                {summary.highPriorityCount > 0 && (
                  <Badge variant="destructive" className="text-xs">
                    {summary.highPriorityCount} urgent
                  </Badge>
                )}
              </div>
            </div>

            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <DollarSign className="h-4 w-4 text-green-500" />
                <span className="text-sm text-muted-foreground">Potential Savings</span>
              </div>
              <div className="text-2xl font-bold text-green-600">
                {formatCurrency(summary.estimatedSavings)}
              </div>
            </div>
          </div>

          {/* Top Risks */}
          {summary.topRisks.length > 0 && (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span className="text-sm font-medium">Top Risks</span>
              </div>
              <div className="space-y-1">
                {summary.topRisks.slice(0, 2).map((risk, index) => (
                  <div key={index} className="text-xs text-muted-foreground bg-orange-50 p-2 rounded">
                    {risk}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              <Button size="sm" variant="outline" asChild>
                <Link href="/inventory/optimization?priority=high">
                  <AlertTriangle className="h-3 w-3 mr-1" />
                  High Priority
                </Link>
              </Button>
              <Button size="sm" variant="outline" asChild>
                <Link href="/inventory/optimization?type=reorder">
                  <Package className="h-3 w-3 mr-1" />
                  Reorders
                </Link>
              </Button>
            </div>
            
            <Button size="sm" className="w-full" asChild>
              <Link href="/inventory/optimization">
                View All Recommendations
                <ArrowRight className="h-3 w-3 ml-1" />
              </Link>
            </Button>
          </div>

          {/* Last Updated */}
          <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Last updated: {new Date(summary.lastGenerated).toLocaleTimeString()}
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={loadOptimizationSummary}
              className="h-6 px-2 text-xs"
            >
              Refresh
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
