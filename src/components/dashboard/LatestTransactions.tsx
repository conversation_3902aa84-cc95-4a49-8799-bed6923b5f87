"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency, formatDate } from "@/lib/utils";
import { Receipt, ArrowRight } from "lucide-react";
import Link from "next/link";

interface Transaction {
  id: string;
  createdAt: string;
  total: number;
  paymentMethod: string;
  paymentStatus: string;
  status: string;
  customer?: {
    name: string;
  };
}

export function LatestTransactions() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/transactions?limit=5&page=1");

        if (!response.ok) {
          throw new Error("Failed to fetch transactions");
        }

        const data = await response.json();
        setTransactions(data.transactions || []);
      } catch (err) {
        console.error("Error fetching transactions:", err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  return (
    <Card className="col-span-1">
      <CardHeader className="flex flex-row items-center justify-between pb-3 border-b">
        <CardTitle className="text-sm font-medium">Latest Transactions</CardTitle>
        <Button variant="ghost" size="sm" className="gap-1" asChild>
          <Link href="/transactions">
            View All
            <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </Button>
      </CardHeader>
      <CardContent className="pt-4">
        {isLoading ? (
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-[200px]" />
                  <Skeleton className="h-4 w-[160px]" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="py-6 text-center text-sm text-muted-foreground">
            <p>Failed to load transactions</p>
            <p className="text-xs text-red-500">{error}</p>
          </div>
        ) : transactions.length === 0 ? (
          <div className="py-6 text-center text-sm text-muted-foreground">
            <p>No transactions found</p>
          </div>
        ) : (
          <div className="space-y-4">
            {transactions.map((transaction) => (
              <div key={transaction.id} className="flex items-start justify-between border-b pb-3">
                <div className="flex items-start gap-3">
                  <div className="flex h-9 w-9 items-center justify-center rounded-full bg-gray-100">
                    <Receipt className="h-4 w-4 text-gray-500" />
                  </div>
                  <div>
                    <div className="font-medium">
                      {transaction.customer?.name || "Walk-in Customer"}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatDate(transaction.createdAt)}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{formatCurrency(transaction.total)}</div>
                  <div
                    className={`text-xs px-2 py-0.5 rounded-full inline-block ${
                      transaction.paymentStatus === "PAID"
                        ? "bg-green-100 text-green-800"
                        : transaction.paymentStatus === "PARTIAL"
                          ? "bg-yellow-100 text-yellow-800"
                          : transaction.paymentStatus === "CANCELLED"
                            ? "bg-red-100 text-red-800"
                            : "bg-blue-100 text-blue-800"
                    }`}
                  >
                    {transaction.paymentStatus}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
