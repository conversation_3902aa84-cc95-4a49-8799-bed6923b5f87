"use client";

import { useEffect, useState } from "react";

export type User = {
  id: string;
  name: string;
  email: string;
  role: string;
};

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);

        // Use the client-side API route instead of the server-side auth() function
        const response = await fetch("/api/auth/session", {
          credentials: "include", // Important: include cookies in the request
        });

        const data = await response.json();

        if (data.user) {
          setUser({
            id: data.user.id as string,
            name: data.user.name as string,
            email: data.user.email as string,
            role: data.user.role as string,
          });
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error("Error fetching user:", error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, []);

  return { user, loading };
}
