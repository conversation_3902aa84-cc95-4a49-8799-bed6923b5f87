"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

interface UseClientAuthReturn {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string; user?: User }>;
  logout: () => Promise<void>;
}

export function useClientAuth(): UseClientAuthReturn {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Function to fetch the current session
  const fetchSession = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/auth/session", {
        credentials: "include", // Important: include cookies in the request
      });
      const data = await response.json();

      console.log("Session data:", data);

      if (data.user) {
        setUser(data.user);
      } else {
        setUser(null);
      }
    } catch (error) {
      console.error("Error fetching session:", error);
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  // Fetch the session on component mount
  useEffect(() => {
    fetchSession();
  }, []);

  // Login function
  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string; user?: User }> => {
    try {
      console.log("Sending login request for:", email);

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
        credentials: "include", // Important: include cookies in the request
      });

      const data = await response.json();
      console.log("Login response:", data);

      if (!response.ok) {
        console.error("Login failed:", data.error);
        // Return a more user-friendly error message
        return {
          success: false,
          error: data.error || "Authentication failed. Please check your credentials and try again."
        };
      }

      // Update the user state
      setUser(data.user);

      // Manually fetch the session to ensure we have the latest data
      console.log("Login successful, fetching session to update state");
      await fetchSession();

      console.log("Session updated, user state:", data.user);
      return { success: true, user: data.user };
    } catch (error: any) {
      console.error("Login error:", error);
      return { success: false, error: error.message || "An error occurred" };
    }
  };

  // Logout function
  const logout = async (): Promise<void> => {
    try {
      console.log("Logging out...");

      const response = await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include", // Important: include cookies in the request
      });

      const data = await response.json();
      console.log("Logout response:", data);

      // Clear user state
      setUser(null);

      // Redirect to login page
      window.location.href = "/login";
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return { user, loading, login, logout };
}
