"use client";

import React, { createContext, useContext, useRef } from "react";

interface POSContextType {
  searchInputRef: React.RefObject<HTMLInputElement>;
  focusSearchInput: (delay?: number) => void;
}

const POSContext = createContext<POSContextType | undefined>(undefined);

export function POSProvider({ children }: { children: React.ReactNode }) {
  const searchInputRef = useRef<HTMLInputElement>(null);

  const focusSearchInput = (delay: number = 150) => {
    setTimeout(() => {
      const element = searchInputRef.current;
      if (!element) {
        console.log("POSContext: Search input ref not available");
        return;
      }

      // Check if element is in DOM and visible
      const isInDOM = document.contains(element);
      const isVisible = element.offsetParent !== null;
      const isEnabled = !element.disabled;

      console.log("POSContext: Focus attempt", {
        isInDOM,
        isVisible,
        isEnabled,
        delay,
      });

      if (isInDOM && isVisible && isEnabled) {
        try {
          element.focus();

          // Verify focus was successful
          setTimeout(() => {
            if (document.activeElement === element) {
              console.log("POSContext: Focus successful");
            } else {
              console.log("POSContext: Focus failed, retrying...");
              // Retry once
              setTimeout(() => {
                element.focus();
              }, 100);
            }
          }, 50);
        } catch (error) {
          console.error("POSContext: Focus error:", error);
        }
      } else {
        console.log("POSContext: Element not ready for focus, retrying in 200ms");
        // Retry after a longer delay
        setTimeout(() => focusSearchInput(100), 200);
      }
    }, delay);
  };

  const value: POSContextType = {
    searchInputRef,
    focusSearchInput,
  };

  return <POSContext.Provider value={value}>{children}</POSContext.Provider>;
}

export function usePOS() {
  const context = useContext(POSContext);
  if (context === undefined) {
    throw new Error("usePOS must be used within a POSProvider");
  }
  return context;
}
