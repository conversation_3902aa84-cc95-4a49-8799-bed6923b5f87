"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from "react";
import { toast } from "sonner";

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  unitPrice: number;
  quantity: number;
  unit: string;
  discount: number;
  autoDiscount: number;
  subtotal: number;
  selectedPriceType: "basePrice";
  // Temporary discount information
  originalPrice?: number; // Store original base price if using temporary discount
  hasTemporaryDiscount?: boolean;
}

export interface Product {
  id: string;
  name: string;
  sku: string;
  barcode?: string;
  basePrice: number;
  discountValue?: number;
  discountType?: "FIXED" | "PERCENTAGE";
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
  storeStock?: {
    id: string;
    quantity: number;
  };
  temporaryPrice?: {
    id: string;
    value: number;
    type: "FIXED" | "PERCENTAGE";
    startDate: string;
    endDate: string;
  };
}

interface POSCartContextType {
  cartItems: CartItem[];
  addToCart: (product: Product) => void;
  updateCartItem: (itemId: string, updates: Partial<CartItem>) => void;
  removeFromCart: (itemId: string) => void;
  clearCart: () => void;
  subtotal: number;
  totalDiscount: number;
  total: number;
  itemCount: number;
}

const POSCartContext = createContext<POSCartContextType | undefined>(undefined);

export function POSCartProvider({ children }: { children: React.ReactNode }) {
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const addToCartInProgress = useRef<Set<string>>(new Set());
  const addToCartCallCount = useRef<Map<string, number>>(new Map());
  const lastAddToCartCall = useRef<Map<string, number>>(new Map());
  const setCartItemsInProgress = useRef<Set<string>>(new Set());

  console.log("=== CART CONTEXT PROVIDER RENDER ===");
  console.log("Current cart items in provider:", cartItems);
  console.log("Is loaded:", isLoaded);

  // Load cart from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      const savedCart = localStorage.getItem("pos_cart_items");
      if (savedCart) {
        try {
          const parsedCart = JSON.parse(savedCart);
          console.log("Loading cart from localStorage:", parsedCart);
          setCartItems(parsedCart);
        } catch (e) {
          console.error("Error parsing saved cart:", e);
          localStorage.removeItem("pos_cart_items");
        }
      }
      setIsLoaded(true);
    }
  }, []);

  // Save cart to localStorage whenever it changes (but only after initial load)
  // REMOVED: This useEffect was causing re-render loops that duplicated cart updates
  // Instead, we'll save to localStorage directly in the cart modification functions

  // Calculate effective price considering temporary discounts
  const calculateEffectivePrice = (
    product: Product
  ): { effectivePrice: number; hasTemporaryDiscount: boolean } => {
    // Check if temporary discount is active
    if (product.temporaryPrice) {
      const now = new Date();
      const startDate = new Date(product.temporaryPrice.startDate);
      const endDate = new Date(product.temporaryPrice.endDate);

      if (now >= startDate && now <= endDate) {
        // Temporary discount is active
        if (product.temporaryPrice.type === "FIXED") {
          return {
            effectivePrice: Math.round(Number(product.temporaryPrice.value)),
            hasTemporaryDiscount: true,
          };
        } else if (product.temporaryPrice.type === "PERCENTAGE") {
          const discountedPrice =
            Number(product.basePrice) -
            (Number(product.basePrice) * Number(product.temporaryPrice.value)) / 100;
          return {
            effectivePrice: Math.round(discountedPrice),
            hasTemporaryDiscount: true,
          };
        }
      }
    }

    // No active temporary discount, use base price
    return {
      effectivePrice: Math.round(Number(product.basePrice)),
      hasTemporaryDiscount: false,
    };
  };

  // Calculate automatic discount amount (based on effective price)
  const calculateAutoDiscountAmount = (product: Product, effectivePrice: number): number => {
    if (!product.discountValue || !product.discountType) return 0;

    if (product.discountType === "FIXED") {
      return Math.round(Number(product.discountValue));
    } else if (product.discountType === "PERCENTAGE") {
      return Math.round((effectivePrice * Number(product.discountValue)) / 100);
    }
    return 0;
  };

  // Add product to cart
  const addToCart = useCallback((product: Product) => {
    const timestamp = new Date().toISOString();
    const callId = `${product.id}-${timestamp}`;

    // Track call count for this product
    const currentCount = addToCartCallCount.current.get(product.id) || 0;
    addToCartCallCount.current.set(product.id, currentCount + 1);

    console.log("🚨🚨🚨 ADDTOCART FUNCTION CALLED 🚨🚨🚨");
    console.log("=== CART CONTEXT DEBUG ===", timestamp);
    console.log("Call ID:", callId);
    console.log("Adding product to cart:", product.id, product.name);
    console.log("Call count for this product:", addToCartCallCount.current.get(product.id));
    console.log("Current addToCartInProgress set:", Array.from(addToCartInProgress.current));
    console.log("🔥 STACK TRACE:", new Error().stack);

    // Enhanced duplicate prevention with timing
    const now = Date.now();
    const lastCallTime = lastAddToCartCall.current.get(product.id) || 0;
    const timeSinceLastCall = now - lastCallTime;

    console.log("Time since last call for this product:", timeSinceLastCall, "ms");

    // Prevent duplicate calls within 500ms
    if (timeSinceLastCall < 500) {
      console.log(
        "❌ Duplicate call detected within 500ms for product:",
        product.id,
        "- IGNORING DUPLICATE CALL"
      );
      return;
    }

    // Check if already in progress
    if (addToCartInProgress.current.has(product.id)) {
      console.log(
        "❌ AddToCart already in progress for product:",
        product.id,
        "- IGNORING DUPLICATE CALL"
      );
      return;
    }

    // Record this call time
    lastAddToCartCall.current.set(product.id, now);

    // Mark this product as being processed immediately
    addToCartInProgress.current.add(product.id);
    console.log("✅ Marked product as in progress:", product.id);

    // Check if product has stock (allow if no stock info available)
    if (product.storeStock && product.storeStock.quantity <= 0) {
      console.log("❌ Product out of stock, exiting");
      addToCartInProgress.current.delete(product.id);
      toast.error("This product is out of stock");
      return;
    }

    try {
      // Create a unique operation ID for this specific add operation
      const operationId = `${product.id}-${Date.now()}`;

      setCartItems((currentCartItems) => {
        console.log("📦 Current cart items in context setter:", currentCartItems.length, "items");
        console.log(
          "📦 Current cart items IDs:",
          currentCartItems.map((item) => item.id)
        );
        console.log("🔄 Operation ID:", operationId);
        console.log("🔄 setCartItemsInProgress:", Array.from(setCartItemsInProgress.current));

        // Prevent duplicate setCartItems operations (React Strict Mode protection)
        if (setCartItemsInProgress.current.has(operationId)) {
          console.log("❌ setCartItems operation already in progress for:", operationId);
          return currentCartItems; // Return unchanged
        }

        // Mark this operation as in progress
        setCartItemsInProgress.current.add(operationId);
        console.log("✅ Marked setCartItems operation as in progress:", operationId);

        // Check if product already exists in cart
        const existingItemIndex = currentCartItems.findIndex(
          (item) => item.productId === product.id
        );

        if (existingItemIndex !== -1) {
          // Update quantity if product already in cart
          console.log("🔄 Updating existing product in cart");
          const updatedItems = [...currentCartItems];
          const item = updatedItems[existingItemIndex];

          // Check if adding more would exceed available stock (only if stock info available)
          if (product.storeStock && item.quantity + 1 > product.storeStock.quantity) {
            console.log("❌ Stock limit exceeded");
            toast.error(`Cannot add more. Only ${product.storeStock.quantity} in stock.`);
            // Clear progress flag on error
            addToCartInProgress.current.delete(product.id);
            return currentCartItems; // Return unchanged cart
          }

          // Update the quantity and recalculate subtotal
          console.log("🔢 BEFORE QUANTITY UPDATE:");
          console.log("  - item.quantity BEFORE:", item.quantity);

          const previousQuantity = item.quantity;
          console.log("  - previousQuantity:", previousQuantity);

          const newQuantity = previousQuantity + 1;
          console.log("  - newQuantity calculation:", previousQuantity, "+ 1 =", newQuantity);

          item.quantity = newQuantity;
          console.log("  - item.quantity AFTER assignment:", item.quantity);

          item.subtotal = Math.round(Number(newQuantity) * Number(item.unitPrice));

          console.log("🔢 AFTER QUANTITY UPDATE:");
          console.log("  - Final item.quantity:", item.quantity);
          console.log("=== QUANTITY UPDATE DETAILS ===");
          console.log("Previous quantity:", previousQuantity);
          console.log("New quantity:", newQuantity);
          console.log("Increment:", newQuantity - previousQuantity);
          console.log("Expected increment: 1");

          // Recalculate automatic discount based on new quantity
          const discountPerUnit = calculateAutoDiscountAmount(product, item.unitPrice);
          item.autoDiscount = Math.round(Number(item.quantity) * discountPerUnit);

          console.log("✅ Updated existing item in context:", item);
          toast.success(`Added another ${product.name} to cart`);

          // Save to localStorage immediately
          if (typeof window !== "undefined") {
            localStorage.setItem("pos_cart_items", JSON.stringify(updatedItems));
            console.log("💾 Saved updated cart to localStorage");
          }

          // Clear the progress flag after successful update
          setTimeout(() => {
            addToCartInProgress.current.delete(product.id);
            addToCartCallCount.current.delete(product.id);
            lastAddToCartCall.current.delete(product.id);
            setCartItemsInProgress.current.delete(operationId);
            console.log("🧹 Cleared progress flag and timing for product:", product.id);
            console.log("🧹 Cleared setCartItems operation:", operationId);
          }, 500);

          return updatedItems;
        } else {
          // Add new item to cart
          console.log("➕ Adding new item to cart in context");

          // Calculate effective price considering temporary discounts
          const { effectivePrice, hasTemporaryDiscount } = calculateEffectivePrice(product);
          const autoDiscount = calculateAutoDiscountAmount(product, effectivePrice);

          const newItem: CartItem = {
            id: crypto.randomUUID(),
            productId: product.id,
            name: product.name,
            unitPrice: effectivePrice,
            quantity: 1,
            unit: product.unit?.abbreviation || "pcs",
            discount: 0, // Manual discount starts at 0
            autoDiscount: Math.round(Number(autoDiscount)),
            subtotal: effectivePrice,
            selectedPriceType: "basePrice",
            // Store temporary discount information
            originalPrice: hasTemporaryDiscount ? Math.round(Number(product.basePrice)) : undefined,
            hasTemporaryDiscount: hasTemporaryDiscount,
          };

          console.log("✅ New cart item created:", newItem);

          const newCartItems = [...currentCartItems, newItem];
          console.log("📦 New cart after adding:", newCartItems.length, "items");

          toast.success(`Added ${product.name} to cart`);

          // Save to localStorage immediately
          if (typeof window !== "undefined") {
            localStorage.setItem("pos_cart_items", JSON.stringify(newCartItems));
            console.log("💾 Saved new cart to localStorage");
          }

          // Clear the progress flag after successful addition
          setTimeout(() => {
            addToCartInProgress.current.delete(product.id);
            addToCartCallCount.current.delete(product.id);
            lastAddToCartCall.current.delete(product.id);
            setCartItemsInProgress.current.delete(operationId);
            console.log("🧹 Cleared progress flag and timing for new product:", product.id);
            console.log("🧹 Cleared setCartItems operation:", operationId);
          }, 500);

          return newCartItems;
        }
      });
    } catch (error) {
      console.error("❌ Error in addToCart:", error);
      // Ensure progress flag and timing are cleared on error
      addToCartInProgress.current.delete(product.id);
      lastAddToCartCall.current.delete(product.id);
      // Note: operationId is not available in this scope, but that's OK since the operation failed
      toast.error("Failed to add product to cart");
    }
  }, []);

  // Update cart item
  const updateCartItem = useCallback((itemId: string, updates: Partial<CartItem>) => {
    setCartItems((items) => {
      const updatedItems = items.map((item) =>
        item.id === itemId
          ? {
              ...item,
              ...updates,
              subtotal: Math.round(
                Number(updates.quantity || item.quantity) *
                  Number(updates.unitPrice || item.unitPrice)
              ),
            }
          : item
      );

      // Save to localStorage immediately
      if (typeof window !== "undefined") {
        localStorage.setItem("pos_cart_items", JSON.stringify(updatedItems));
      }

      return updatedItems;
    });
  }, []);

  // Remove item from cart
  const removeFromCart = useCallback((itemId: string) => {
    setCartItems((items) => {
      const updatedItems = items.filter((item) => item.id !== itemId);

      // Save to localStorage immediately
      if (typeof window !== "undefined") {
        localStorage.setItem("pos_cart_items", JSON.stringify(updatedItems));
      }

      return updatedItems;
    });
  }, []);

  // Clear entire cart
  const clearCart = useCallback(() => {
    setCartItems([]);
    if (typeof window !== "undefined") {
      localStorage.removeItem("pos_cart_items");
    }
  }, []);

  // Calculate totals with memoization
  const calculations = useMemo(() => {
    const subtotal = Math.round(cartItems.reduce((sum, item) => sum + Number(item.subtotal), 0));
    const totalManualDiscount = Math.round(
      cartItems.reduce((sum, item) => sum + Number(item.discount), 0)
    );
    const totalAutoDiscount = Math.round(
      cartItems.reduce((sum, item) => sum + Number(item.autoDiscount), 0)
    );
    const totalDiscount = totalManualDiscount + totalAutoDiscount;
    const total = subtotal - totalDiscount;
    const itemCount = cartItems.length;

    console.log("Cart calculations:", { subtotal, totalDiscount, total, itemCount });

    return {
      subtotal,
      totalDiscount,
      total,
      itemCount,
    };
  }, [cartItems]);

  const value: POSCartContextType = {
    cartItems,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    ...calculations,
  };

  return <POSCartContext.Provider value={value}>{children}</POSCartContext.Provider>;
}

export function usePOSCart() {
  const context = useContext(POSCartContext);
  if (context === undefined) {
    throw new Error("usePOSCart must be used within a POSCartProvider");
  }
  return context;
}
