"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { 
  CalendarIcon, 
  Download, 
  FileText, 
  TrendingUp, 
  DollarSign,
  BarChart3,
  Package,
  Loader2
} from "lucide-react";
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { cn } from "@/lib/utils";

// Import report components
import { DailySalesReportComponent } from "@/components/reports/financial/DailySalesReport";
import { PeriodicSalesReportComponent } from "@/components/reports/financial/PeriodicSalesReport";
import { ProfitAnalysisReportComponent } from "@/components/reports/financial/ProfitAnalysisReport";
import { InventoryValuationReportComponent } from "@/components/reports/financial/InventoryValuationReport";
import { InvoiceSummaryReportComponent } from "@/components/reports/financial/InvoiceSummaryReport";

export default function FinancialReportsPage() {
  const [activeTab, setActiveTab] = useState("daily-sales");
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });
  const [periodicType, setPeriodicType] = useState<"weekly" | "monthly">("weekly");
  const [isExporting, setIsExporting] = useState(false);

  // Quick date range presets
  const datePresets = [
    {
      label: "Today",
      value: "today",
      getRange: () => ({ from: new Date(), to: new Date() }),
    },
    {
      label: "Yesterday",
      value: "yesterday",
      getRange: () => {
        const yesterday = subDays(new Date(), 1);
        return { from: yesterday, to: yesterday };
      },
    },
    {
      label: "Last 7 days",
      value: "7d",
      getRange: () => ({ from: subDays(new Date(), 7), to: new Date() }),
    },
    {
      label: "Last 30 days",
      value: "30d",
      getRange: () => ({ from: subDays(new Date(), 30), to: new Date() }),
    },
    {
      label: "This week",
      value: "week",
      getRange: () => ({ from: startOfWeek(new Date(), { weekStartsOn: 1 }), to: endOfWeek(new Date(), { weekStartsOn: 1 }) }),
    },
    {
      label: "This month",
      value: "month",
      getRange: () => ({ from: startOfMonth(new Date()), to: endOfMonth(new Date()) }),
    },
  ];

  const handleDatePreset = (preset: string) => {
    const presetConfig = datePresets.find(p => p.value === preset);
    if (presetConfig) {
      const range = presetConfig.getRange();
      setDateRange(range);
      if (preset === "today" || preset === "yesterday") {
        setSelectedDate(range.from);
      }
    }
  };

  const handleExport = async (reportType: string, exportFormat: "csv" | "xlsx") => {
    setIsExporting(true);
    try {
      let reportData;
      let filename;

      // Fetch the current report data based on active tab
      switch (reportType) {
        case "daily-sales":
          const dailyResponse = await fetch(`/api/reports/financial/daily-sales?date=${format(selectedDate, "yyyy-MM-dd")}`);
          const dailyResult = await dailyResponse.json();
          reportData = dailyResult.data;
          filename = `daily-sales-${format(selectedDate, "yyyy-MM-dd")}`;
          break;

        case "periodic-sales":
          const periodicResponse = await fetch(`/api/reports/financial/periodic?type=${periodicType}&date=${format(selectedDate, "yyyy-MM-dd")}`);
          const periodicResult = await periodicResponse.json();
          reportData = periodicResult.data;
          filename = `${periodicType}-sales-${format(selectedDate, "yyyy-MM-dd")}`;
          break;

        case "profit-analysis":
          const profitResponse = await fetch(`/api/reports/financial/profit-analysis?startDate=${format(dateRange.from, "yyyy-MM-dd")}&endDate=${format(dateRange.to, "yyyy-MM-dd")}`);
          const profitResult = await profitResponse.json();
          reportData = profitResult.data;
          filename = `profit-analysis-${format(dateRange.from, "yyyy-MM-dd")}-to-${format(dateRange.to, "yyyy-MM-dd")}`;
          break;

        case "inventory-valuation":
          const inventoryResponse = await fetch(`/api/reports/financial/inventory-valuation`);
          const inventoryResult = await inventoryResponse.json();
          reportData = inventoryResult.data;
          filename = `inventory-valuation-${format(new Date(), "yyyy-MM-dd")}`;
          break;

        case "invoice-summary":
          const invoiceResponse = await fetch(`/api/invoices/summary`);
          const invoiceResult = await invoiceResponse.json();
          reportData = invoiceResult;
          filename = `invoice-summary-${format(new Date(), "yyyy-MM-dd")}`;
          break;

        default:
          throw new Error("Unknown report type");
      }

      // Export the data
      const exportResponse = await fetch("/api/reports/financial/export", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include", // Include cookies for authentication
        body: JSON.stringify({
          reportType,
          reportData,
          format: exportFormat,
          filename,
        }),
      });

      if (!exportResponse.ok) {
        throw new Error("Export failed");
      }

      // Download the file
      const blob = await exportResponse.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `${filename}.${exportFormat}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Report exported successfully as ${exportFormat.toUpperCase()}`);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Failed to export report");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader
          title="Financial Reports"
          description="Comprehensive financial reporting and analysis"
        />

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Report Controls
            </CardTitle>
            <CardDescription>
              Select date ranges and export options for financial reports
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4 items-center">
              {/* Date Presets */}
              <div className="flex flex-wrap gap-2">
                {datePresets.map((preset) => (
                  <Button
                    key={preset.value}
                    variant="outline"
                    size="sm"
                    onClick={() => handleDatePreset(preset.value)}
                  >
                    {preset.label}
                  </Button>
                ))}
              </div>

              {/* Custom Date Picker */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-[240px] justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={(range) => {
                      if (range?.from && range?.to) {
                        setDateRange({ from: range.from, to: range.to });
                      }
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>

              {/* Export Actions */}
              <div className="flex gap-2 ml-auto">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport(activeTab, "csv")}
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Download className="mr-2 h-4 w-4" />
                  )}
                  Export CSV
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport(activeTab, "xlsx")}
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <FileText className="mr-2 h-4 w-4" />
                  )}
                  Export Excel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="daily-sales" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Daily Sales
            </TabsTrigger>
            <TabsTrigger value="periodic-sales" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Periodic Sales
            </TabsTrigger>
            <TabsTrigger value="profit-analysis" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Profit Analysis
            </TabsTrigger>
            <TabsTrigger value="inventory-valuation" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Inventory Valuation
            </TabsTrigger>
            <TabsTrigger value="invoice-summary" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Invoice Summary
            </TabsTrigger>
          </TabsList>

          <TabsContent value="daily-sales">
            <DailySalesReportComponent 
              selectedDate={selectedDate}
              onDateChange={setSelectedDate}
            />
          </TabsContent>

          <TabsContent value="periodic-sales">
            <PeriodicSalesReportComponent 
              selectedDate={selectedDate}
              periodicType={periodicType}
              onDateChange={setSelectedDate}
              onPeriodicTypeChange={setPeriodicType}
            />
          </TabsContent>

          <TabsContent value="profit-analysis">
            <ProfitAnalysisReportComponent 
              dateRange={dateRange}
              onDateRangeChange={setDateRange}
            />
          </TabsContent>

          <TabsContent value="inventory-valuation">
            <InventoryValuationReportComponent />
          </TabsContent>

          <TabsContent value="invoice-summary">
            <InvoiceSummaryReportComponent />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
