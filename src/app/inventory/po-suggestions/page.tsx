"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import POSuggestionsDashboard from "@/components/po-suggestions/POSuggestionsDashboard";
import POSuggestionDetails from "@/components/po-suggestions/POSuggestionDetails";
import { Button } from "@/components/ui/button";
import { RefreshCw, Lightbulb } from "lucide-react";
import { toast } from "sonner";

interface POSuggestion {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  currentStock: number;
  reorderPoint: number;
  daysUntilStockout: number;
  urgencyLevel: 'critical' | 'high' | 'medium' | 'low';
  recommendedSupplier: {
    supplierId: string;
    supplierName: string;
    productSupplierId: string;
    score: number;
    reasoning: string[];
    purchasePrice: number;
    leadTimeDays?: number;
    minimumOrderQuantity?: number;
    isPreferred: boolean;
  };
  suggestedQuantity: number;
  estimatedCost: number;
  demandForecast: {
    predictedDemand: number;
    confidenceLevel: number;
    seasonalFactor: number;
  };
  createdAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'po_created';
  metadata: {
    triggerReason: string;
    stockoutDate: string;
    safetyStockDays: number;
    leadTimeBuffer: number;
  };
}

export default function POSuggestionsPage() {
  const router = useRouter();
  const [selectedSuggestion, setSelectedSuggestion] = useState<POSuggestion | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleViewDetails = (suggestion: POSuggestion) => {
    setSelectedSuggestion(suggestion);
    setDetailsOpen(true);
  };

  const handleCreatePO = async (suggestion: POSuggestion, adjustedQuantity?: number, notes?: string) => {
    try {
      console.log('🔄 Starting single PO creation for suggestion:', suggestion.id);

      const requestBody = {
        suggestionId: suggestion.id,
        adjustedQuantity,
        notes,
      };

      console.log('📤 Request payload:', requestBody);

      // Create PO from suggestion
      const response = await fetch('/api/po-suggestions/create-po', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ API Error Response:', errorData);

        // Provide specific error messages
        let userMessage = 'Failed to create purchase order';
        if (errorData.errorType === 'NO_SUPPLIERS_ERROR') {
          userMessage = 'No active suppliers found. Please add suppliers before creating purchase orders.';
        } else if (errorData.errorType === 'DECIMAL_CONSTRAINT_ERROR') {
          userMessage = 'Order values are too large. Please reduce quantities or contact support for high-value orders.';
        } else if (errorData.errorType === 'QUANTITY_PRICE_CONSTRAINT_ERROR') {
          userMessage = 'Requested quantity is too high for this product\'s unit price. Please reduce the quantity or split into multiple orders.';
        } else if (errorData.message) {
          userMessage = errorData.message;
        }

        throw new Error(userMessage);
      }

      const data = await response.json();
      console.log('✅ Success response:', data);

      toast.success('Purchase order created successfully!');

      // Navigate to the created PO
      if (data.purchaseOrder?.id) {
        router.push(`/inventory/purchase-orders/${data.purchaseOrder.id}`);
      } else {
        // Fallback to PO list
        router.push('/inventory/purchase-orders');
      }
    } catch (error) {
      console.error('💥 Error creating PO:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create purchase order';
      toast.error(errorMessage);
    }
  };

  const handleBulkCreatePO = async (suggestions: POSuggestion[]) => {
    try {
      console.log('🔄 Starting bulk PO creation for', suggestions.length, 'suggestions');

      // Create bulk POs from suggestions
      const requestBody = {
        suggestions: suggestions.map(s => ({
          suggestionId: s.id,
          adjustedQuantity: s.suggestedQuantity,
        })),
        groupBySupplierId: true,
      };

      console.log('📤 Request payload:', requestBody);

      const response = await fetch('/api/po-suggestions/create-po', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ API Error Response:', errorData);

        // Provide specific error messages based on error type
        let userMessage = 'Failed to create purchase orders';
        if (errorData.errorType === 'NO_SUPPLIERS_ERROR') {
          userMessage = 'No active suppliers found. Please add suppliers before creating purchase orders.';
        } else if (errorData.errorType === 'VALIDATION_ERROR') {
          userMessage = 'Invalid data provided. Please check your selections and try again.';
        } else if (errorData.errorType === 'MISSING_PRODUCT_DATA') {
          userMessage = 'Missing product information. Please ensure all products have valid supplier relationships.';
        } else if (errorData.errorType === 'INVALID_PRICING_DATA') {
          userMessage = 'Invalid pricing or quantity data. Please check the product pricing configuration.';
        } else if (errorData.errorType === 'DECIMAL_CONSTRAINT_ERROR') {
          userMessage = 'Order values are too large. Please reduce quantities or contact support for high-value orders.';
        } else if (errorData.errorType === 'QUANTITY_PRICE_CONSTRAINT_ERROR') {
          userMessage = 'Requested quantity is too high for this product\'s unit price. Please reduce the quantity or split into multiple orders.';
        } else if (errorData.errorType === 'DATABASE_ERROR') {
          userMessage = 'Database error occurred. Please try again or contact support.';
        } else if (errorData.message) {
          userMessage = errorData.message;
        }

        throw new Error(userMessage);
      }

      const data = await response.json();
      console.log('✅ Success response:', data);

      toast.success(`${data.summary?.totalPOs || suggestions.length} purchase order(s) created successfully!`);

      // Navigate to PO list
      router.push('/inventory/purchase-orders');
    } catch (error) {
      console.error('💥 Error creating bulk POs:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create purchase orders';
      toast.error(errorMessage);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
    toast.success('Refreshing PO suggestions...');
  };

  return (
    <MainLayout>
      <PageHeader
        title="PO Suggestions"
        description="Automatic purchase order recommendations based on inventory levels"
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        }
      />

      <POSuggestionsDashboard
        key={refreshKey}
        onViewDetails={handleViewDetails}
        onCreatePO={handleCreatePO}
        onBulkCreatePO={handleBulkCreatePO}
      />

      {selectedSuggestion && (
        <POSuggestionDetails
          suggestion={selectedSuggestion}
          open={detailsOpen}
          onOpenChange={setDetailsOpen}
          onCreatePO={handleCreatePO}
        />
      )}
    </MainLayout>
  );
}
