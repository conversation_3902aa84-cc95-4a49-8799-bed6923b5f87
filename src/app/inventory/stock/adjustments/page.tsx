"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Plus, Search, Filter, ArrowUpDown } from "lucide-react";
import { toast } from "sonner";
import CustomPagination from "@/components/ui/custom-pagination";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";

// Define adjustment reasons
const adjustmentReasons = [
  { value: "INVENTORY_COUNT", label: "Inventory Count" },
  { value: "DAMAGED", label: "Damaged" },
  { value: "EXPIRED", label: "Expired" },
  { value: "THEFT", label: "Theft" },
  { value: "LOSS", label: "Loss" },
  { value: "RETURN", label: "Return" },
  { value: "CORRECTION", label: "Correction" },
  { value: "OTHER", label: "Other" },
];

// Define form schema
const adjustmentFormSchema = z.object({
  productId: z.string({
    required_error: "Please select a product",
  }),
  locationType: z.enum(["STORE", "WAREHOUSE"], {
    required_error: "Please select a location type",
  }),
  adjustmentQuantity: z.number({
    required_error: "Please enter an adjustment quantity",
  }),
  reason: z.enum(
    ["INVENTORY_COUNT", "DAMAGED", "EXPIRED", "THEFT", "LOSS", "RETURN", "CORRECTION", "OTHER"],
    {
      required_error: "Please select a reason",
    }
  ),
  notes: z.string().optional(),
});

interface Product {
  id: string;
  name: string;
  sku: string;
  category?: {
    id: string;
    name: string;
  } | null;
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  } | null;
}

interface StockAdjustment {
  id: string;
  date: string;
  productId: string;
  previousQuantity: number;
  newQuantity: number;
  adjustmentQuantity: number;
  reason: string;
  notes?: string;
  status?: string;
  approvedBy?: {
    id: string;
    name: string;
  } | null;
  approvedAt?: string | null;
  rejectedAt?: string | null;
  rejectionReason?: string | null;
  product: Product;
  user: {
    id: string;
    name: string;
    role: string;
  };
}

export default function StockAdjustmentsPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [adjustments, setAdjustments] = useState<StockAdjustment[]>([]);
  const [filteredAdjustments, setFilteredAdjustments] = useState<StockAdjustment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [reasonFilter, setReasonFilter] = useState("all");
  const [statusFilter, setStatusFilter] = useState("all");
  const [products, setProducts] = useState<Product[]>([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "ascending" | "descending";
  }>({ key: "date", direction: "descending" });

  // Check authentication and authorization
  useEffect(() => {
    if (!authLoading && !user) {
      router.push("/login");
      return;
    }

    if (!authLoading && user && !["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(user.role)) {
      toast.error("Access denied. Only SUPER_ADMIN and WAREHOUSE_ADMIN can access stock adjustments.");
      router.push("/dashboard");
      return;
    }
  }, [user, authLoading, router]);

  // Initialize form
  const form = useForm<z.infer<typeof adjustmentFormSchema>>({
    resolver: zodResolver(adjustmentFormSchema),
    defaultValues: {
      locationType: "STORE",
      adjustmentQuantity: 0,
      notes: "",
    },
  });

  // Check if user has access
  const hasAccess = user && ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(user.role);
  const isSuperAdmin = user?.role === "SUPER_ADMIN";

  // Fetch adjustments data
  useEffect(() => {
    const fetchAdjustments = async () => {
      setLoading(true);
      try {
        const response = await fetch(
          `/api/inventory/adjustments?page=${pagination.page}&limit=${pagination.limit}`
        );

        if (!response.ok) {
          let errorMessage = "Failed to fetch adjustments";
          try {
            const errorData = await response.json();
            errorMessage = errorData.error || errorData.message || errorMessage;
          } catch (e) {
            // If we can't parse the error response, use the status text
            errorMessage = `${response.status}: ${response.statusText}`;
          }
          throw new Error(errorMessage);
        }

        const data = await response.json();

        setAdjustments(data.adjustments || []);
        setFilteredAdjustments(data.adjustments || []);
        setPagination(data.pagination || { total: 0, page: 1, limit: 100, pages: 0 });
      } catch (error) {
        console.error("Error fetching adjustments:", error);
        const errorMessage = error instanceof Error ? error.message : "Failed to load adjustments";
        toast.error(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchAdjustments();
  }, [pagination.page, pagination.limit]);

  // Fetch products for the form
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch("/api/products?limit=500&active=true");
        if (!response.ok) throw new Error("Failed to fetch products");
        const data = await response.json();
        setProducts(data.products);
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to load products");
      }
    };

    fetchProducts();
  }, []);

  // Handle search and filtering
  useEffect(() => {
    if (searchTerm || (reasonFilter && reasonFilter !== "all") || (statusFilter && statusFilter !== "all")) {
      const filtered = adjustments.filter((adjustment) => {
        const matchesSearch = searchTerm
          ? adjustment.product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            adjustment.product.sku.toLowerCase().includes(searchTerm.toLowerCase())
          : true;

        const matchesReason =
          reasonFilter && reasonFilter !== "all" ? adjustment.reason === reasonFilter : true;

        const matchesStatus =
          statusFilter && statusFilter !== "all" ? adjustment.status === statusFilter : true;

        return matchesSearch && matchesReason && matchesStatus;
      });

      setFilteredAdjustments(filtered);
    } else {
      setFilteredAdjustments(adjustments);
    }
  }, [searchTerm, reasonFilter, statusFilter, adjustments]);

  // Handle sorting
  const handleSort = (key: string) => {
    let direction: "ascending" | "descending" = "ascending";

    if (sortConfig.key === key) {
      direction = sortConfig.direction === "ascending" ? "descending" : "ascending";
    }

    setSortConfig({ key, direction });

    const sortedData = [...filteredAdjustments].sort((a, b) => {
      let aValue, bValue;

      // Handle nested properties
      if (key.includes(".")) {
        const keys = key.split(".");
        aValue = keys.reduce((obj, k) => obj?.[k], a);
        bValue = keys.reduce((obj, k) => obj?.[k], b);
      } else {
        aValue = a[key];
        bValue = b[key];
      }

      // Handle date comparison
      if (key === "date") {
        return direction === "ascending"
          ? new Date(aValue).getTime() - new Date(bValue).getTime()
          : new Date(bValue).getTime() - new Date(aValue).getTime();
      }

      // Handle string comparison
      if (typeof aValue === "string") {
        return direction === "ascending"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      // Handle number comparison
      return direction === "ascending" ? aValue - bValue : bValue - aValue;
    });

    setFilteredAdjustments(sortedData);
  };

  // Handle form submission
  const onSubmit = async (values: z.infer<typeof adjustmentFormSchema>) => {
    try {
      console.log("🚀 [FRONTEND] Starting stock adjustment submission...");
      console.log("📋 [FRONTEND] Form values:", JSON.stringify(values, null, 2));
      console.log("👤 [FRONTEND] Current user:", {
        id: user?.id,
        role: user?.role,
        email: user?.email,
        isSuperAdmin
      });

      // Prepare request data
      const requestData = {
        ...values,
        adjustmentQuantity: Number(values.adjustmentQuantity)
      };
      console.log("📦 [FRONTEND] Request data:", JSON.stringify(requestData, null, 2));

      // Get cookies for debugging
      const cookies = document.cookie;
      console.log("🍪 [FRONTEND] Cookies:", cookies);

      console.log("📡 [FRONTEND] Making POST request to /api/inventory/adjustments...");

      const response = await fetch("/api/inventory/adjustments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      console.log("📊 [FRONTEND] Response status:", response.status);
      console.log("📊 [FRONTEND] Response headers:", Object.fromEntries(response.headers.entries()));
      console.log("📊 [FRONTEND] Response ok:", response.ok);

      // Get response text first to avoid consuming the stream
      const responseText = await response.text();
      console.log("📊 [FRONTEND] Response body (raw):", responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
        console.log("📊 [FRONTEND] Response body (parsed):", responseData);
      } catch (parseError) {
        console.error("❌ [FRONTEND] Failed to parse response as JSON:", parseError);
        console.log("📊 [FRONTEND] Response was not valid JSON");
      }

      if (!response.ok) {
        const errorMessage = responseData?.error || responseData?.message || `HTTP ${response.status}: ${response.statusText}`;
        console.error("❌ [FRONTEND] Request failed:", errorMessage);
        throw new Error(errorMessage);
      }

      console.log("✅ [FRONTEND] Request successful!");

      const successMessage = isSuperAdmin
        ? "Stock adjustment created and applied successfully"
        : "Stock adjustment created and submitted for approval";

      console.log("🎉 [FRONTEND] Showing success message:", successMessage);
      toast.success(successMessage);
      form.reset();
      setIsDialogOpen(false);

      console.log("🔄 [FRONTEND] Refreshing adjustments list...");
      // Refresh the adjustments list
      const refreshResponse = await fetch(
        `/api/inventory/adjustments?page=${pagination.page}&limit=${pagination.limit}`
      );

      if (refreshResponse.ok) {
        const refreshData = await refreshResponse.json();
        console.log("✅ [FRONTEND] Adjustments list refreshed successfully");
        setAdjustments(refreshData.adjustments);
        setFilteredAdjustments(refreshData.adjustments);
      } else {
        console.warn("⚠️ [FRONTEND] Failed to refresh adjustments list");
      }

    } catch (error) {
      console.error("💥 [FRONTEND] Error creating adjustment:", error);
      console.error("💥 [FRONTEND] Error stack:", error instanceof Error ? error.stack : 'No stack trace');

      const errorMessage = error instanceof Error ? error.message : "Failed to create stock adjustment";
      console.error("💥 [FRONTEND] Final error message:", errorMessage);

      toast.error(errorMessage);
    } finally {
      console.log("🏁 [FRONTEND] Form submission completed");
    }
  };

  // Format reason for display
  const formatReason = (reason: string) => {
    const reasonObj = adjustmentReasons.find((r) => r.value === reason);
    return reasonObj ? reasonObj.label : reason;
  };

  // Format status for display
  const formatStatus = (status: string) => {
    switch (status) {
      case "PENDING_APPROVAL":
        return "Pending Approval";
      case "APPROVED":
        return "Approved";
      case "REJECTED":
        return "Rejected";
      case "APPLIED":
        return "Applied";
      default:
        return status;
    }
  };

  // Get status badge variant
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "PENDING_APPROVAL":
        return "secondary";
      case "APPROVED":
      case "APPLIED":
        return "default";
      case "REJECTED":
        return "destructive";
      default:
        return "outline";
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Handle approval/rejection
  const handleApproval = async (adjustmentId: string, action: "approve" | "reject", rejectionReason?: string) => {
    try {
      console.log(`🔄 [FRONTEND] Starting ${action} for adjustment:`, adjustmentId);
      console.log(`👤 [FRONTEND] Current user:`, {
        id: user?.id,
        role: user?.role,
        email: user?.email
      });
      console.log(`📦 [FRONTEND] Request data:`, { action, rejectionReason });

      // Get cookies for debugging
      const cookies = document.cookie;
      console.log("🍪 [FRONTEND] Cookies:", cookies);

      const response = await fetch(`/api/inventory/adjustments/${adjustmentId}/approve`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ action, rejectionReason }),
      });

      console.log(`📊 [FRONTEND] Response status:`, response.status);
      console.log(`📊 [FRONTEND] Response ok:`, response.ok);

      if (!response.ok) {
        const errorData = await response.json();
        console.error(`❌ [FRONTEND] ${action} failed:`, errorData);
        throw new Error(errorData.error || `Failed to ${action} adjustment`);
      }

      const responseData = await response.json();
      console.log(`✅ [FRONTEND] ${action} successful:`, responseData);

      toast.success(`Adjustment ${action === "approve" ? "approved" : "rejected"} successfully`);

      console.log(`🔄 [FRONTEND] Refreshing adjustments list...`);
      // Refresh the adjustments list
      const refreshResponse = await fetch(
        `/api/inventory/adjustments?page=${pagination.page}&limit=${pagination.limit}`
      );
      const refreshData = await refreshResponse.json();
      setAdjustments(refreshData.adjustments);
      setFilteredAdjustments(refreshData.adjustments);
      console.log(`✅ [FRONTEND] Adjustments list refreshed`);
    } catch (error) {
      console.error(`💥 [FRONTEND] Error ${action}ing adjustment:`, error);
      console.error(`💥 [FRONTEND] Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
      toast.error(error instanceof Error ? error.message : `Failed to ${action} adjustment`);
    }
  };

  // Show loading or access denied
  if (authLoading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (!hasAccess) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center py-8">
          <h2 className="text-2xl font-bold text-destructive mb-4">Access Denied</h2>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access stock adjustments.
          </p>
          <p className="text-sm text-muted-foreground mb-6">
            Only SUPER_ADMIN and WAREHOUSE_ADMIN roles can access this page.
          </p>
          <Button onClick={() => router.push("/dashboard")}>
            Return to Dashboard
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Stock Adjustments"
        actions={
          <Button variant="outline" onClick={() => router.push("/inventory/stock")}>
            Back to Stock
          </Button>
        }
      />
      <div className="space-y-6">
        <div className="flex justify-end">
          <div className="flex space-x-2">
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" /> New Adjustment
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                  <DialogTitle>Create Stock Adjustment</DialogTitle>
                  <DialogDescription>
                    Adjust inventory levels for a product. Enter the details below.
                  </DialogDescription>
                  {!isSuperAdmin && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800">
                        <strong>Note:</strong> Your adjustment will require approval from a SUPER_ADMIN before being applied to inventory.
                      </p>
                    </div>
                  )}
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="productId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Product</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select a product" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {products.map((product) => (
                                <SelectItem key={product.id} value={product.id}>
                                  {product.name} ({product.sku})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="locationType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Location</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select location" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="STORE">Store</SelectItem>
                              <SelectItem value="WAREHOUSE">Warehouse</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="adjustmentQuantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Adjustment Quantity</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              onChange={(e) => {
                                const value = e.target.value;
                                if (value === "" || value === "-") {
                                  field.onChange(value === "" ? 0 : 0);
                                } else {
                                  const intValue = parseInt(value, 10);
                                  if (!isNaN(intValue)) {
                                    field.onChange(intValue);
                                  }
                                }
                              }}
                              value={field.value === 0 ? "" : field.value.toString()}
                              placeholder="Enter quantity (use - for decreases)"
                            />
                          </FormControl>
                          <FormDescription>
                            Use positive values to add stock, negative to remove
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="reason"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Reason</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select reason" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {adjustmentReasons.map((reason) => (
                                <SelectItem key={reason.value} value={reason.value}>
                                  {reason.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter additional details about this adjustment"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <DialogFooter>
                      <Button type="submit">Save Adjustment</Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Filters */}

        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search products..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-[200px]">
            <Select value={reasonFilter} onValueChange={setReasonFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Reasons" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Reasons</SelectItem>
                {adjustmentReasons.map((reason) => (
                  <SelectItem key={reason.value} value={reason.value}>
                    {reason.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-[200px]">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="PENDING_APPROVAL">Pending Approval</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
                <SelectItem value="APPLIED">Applied</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Adjustments List */}
        <Card>
          <CardHeader>
            <CardTitle>Adjustment History</CardTitle>
            <CardDescription>View and manage stock adjustments</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : filteredAdjustments.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="cursor-pointer" onClick={() => handleSort("date")}>
                        Date <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("product.name")}
                      >
                        Product <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("adjustmentQuantity")}
                      >
                        Adjustment <ArrowUpDown className="inline h-4 w-4 ml-1" />
                      </TableHead>
                      <TableHead>Previous</TableHead>
                      <TableHead>New</TableHead>
                      <TableHead>Reason</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>User</TableHead>
                      {isSuperAdmin && <TableHead>Actions</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAdjustments.map((adjustment) => (
                      <TableRow key={adjustment.id}>
                        <TableCell>{new Date(adjustment.date).toLocaleString()}</TableCell>
                        <TableCell className="font-medium">{adjustment.product.name}</TableCell>
                        <TableCell>{adjustment.product.sku}</TableCell>
                        <TableCell>
                          <span
                            className={
                              adjustment.adjustmentQuantity > 0 ? "text-green-600" : "text-red-600"
                            }
                          >
                            {adjustment.adjustmentQuantity > 0 ? "+" : ""}
                            {Number(adjustment.adjustmentQuantity).toFixed(2)}
                          </span>
                        </TableCell>
                        <TableCell>{Number(adjustment.previousQuantity).toFixed(2)}</TableCell>
                        <TableCell>{Number(adjustment.newQuantity).toFixed(2)}</TableCell>
                        <TableCell>{formatReason(adjustment.reason)}</TableCell>
                        <TableCell>
                          <Badge variant={getStatusBadgeVariant(adjustment.status || "APPLIED")}>
                            {formatStatus(adjustment.status || "APPLIED")}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span>{adjustment.user.name}</span>
                            {adjustment.approvedBy && (
                              <span className="text-xs text-muted-foreground">
                                Approved by {adjustment.approvedBy.name}
                              </span>
                            )}
                          </div>
                        </TableCell>
                        {isSuperAdmin && (
                          <TableCell>
                            {adjustment.status === "PENDING_APPROVAL" && (
                              <div className="flex space-x-2">
                                <Button
                                  size="sm"
                                  variant="default"
                                  onClick={() => handleApproval(adjustment.id, "approve")}
                                >
                                  Approve
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => {
                                    const reason = prompt("Enter rejection reason (optional):");
                                    handleApproval(adjustment.id, "reject", reason || undefined);
                                  }}
                                >
                                  Reject
                                </Button>
                              </div>
                            )}
                          </TableCell>
                        )}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
                <div className="mt-4">
                  <CustomPagination
                    currentPage={pagination.page}
                    totalPages={pagination.pages}
                    onPageChange={handlePageChange}
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">No adjustments found</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
