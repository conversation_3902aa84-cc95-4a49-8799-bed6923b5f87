"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Loader2, Plus, Search, AlertTriangle, ArrowUpDown, Package, Calendar, Building, ChevronDown, ChevronRight } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import CustomPagination from "@/components/ui/custom-pagination";

interface Product {
  id: string;
  name: string;
  sku: string;
  category?: {
    id: string;
    name: string;
  } | null;
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  } | null;
  productSuppliers?: {
    id: string;
    supplier: {
      id: string;
      name: string;
    };
  }[];
}

interface StockBatch {
  id: string;
  batchNumber: string;
  quantity: number;
  expiryDate?: Date | null;
  productSupplier?: {
    id: string;
    supplier: {
      id: string;
      name: string;
    };
  } | null;
}

interface StoreStock {
  id: string;
  productId: string;
  quantity: number;
  minThreshold: number;
  maxThreshold?: number | null;
  lastUpdated: string;
  product: Product;
  stockStatus?: string;
  percentRemaining?: number;
  batchCount?: number;
  batches?: StockBatch[];
  earliestExpiryDate?: Date | null;
}

interface WarehouseStock {
  id: string;
  productId: string;
  quantity: number;
  minThreshold: number;
  maxThreshold?: number | null;
  lastUpdated: string;
  product: Product;
  stockStatus?: string;
  percentRemaining?: number;
  batchCount?: number;
  batches?: StockBatch[];
  earliestExpiryDate?: Date | null;
}

export default function StockManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("store");
  const [storeStock, setStoreStock] = useState<StoreStock[]>([]);
  const [warehouseStock, setWarehouseStock] = useState<WarehouseStock[]>([]);
  const [lowStockItems, setLowStockItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [warehouseLoading, setWarehouseLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredStoreStock, setFilteredStoreStock] = useState<StoreStock[]>([]);
  const [filteredWarehouseStock, setFilteredWarehouseStock] = useState<WarehouseStock[]>([]);
  const [filteredLowStock, setFilteredLowStock] = useState<any[]>([]);
  const [categoryFilter, setCategoryFilter] = useState("");
  const [categories, setCategories] = useState<any[]>([]);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: "ascending" | "descending";
  }>({ key: "name", direction: "ascending" });
  const [storePagination, setStorePagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [warehousePagination, setWarehousePagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0,
  });
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Fetch store stock data
  useEffect(() => {
    const fetchStoreStockData = async () => {
      setLoading(true);
      try {
        // Fetch store stock
        const storeStockResponse = await fetch(
          `/api/inventory/store-stock?page=${storePagination.page}&limit=${storePagination.limit}`
        );
        if (!storeStockResponse.ok) throw new Error("Failed to fetch store stock");
        const storeStockData = await storeStockResponse.json();

        // Add stock status to each item
        const storeStockWithStatus = storeStockData.storeStock.map((item: StoreStock) => {
          const quantity = Number(item.quantity);
          const minThreshold = Number(item.minThreshold);
          let stockStatus = "NORMAL";
          let percentRemaining = 100;

          if (quantity <= 0) {
            stockStatus = "OUT_OF_STOCK";
            percentRemaining = 0;
          } else if (quantity <= minThreshold * 0.5) {
            stockStatus = "CRITICAL";
            percentRemaining = Math.round((quantity / minThreshold) * 100);
          } else if (quantity <= minThreshold) {
            stockStatus = "LOW";
            percentRemaining = Math.round((quantity / minThreshold) * 100);
          }

          return {
            ...item,
            stockStatus,
            percentRemaining,
          };
        });

        setStoreStock(storeStockWithStatus);
        setFilteredStoreStock(storeStockWithStatus);
        setStorePagination(storeStockData.pagination);

        // Fetch low stock items
        const lowStockResponse = await fetch("/api/inventory/low-stock");
        if (!lowStockResponse.ok) throw new Error("Failed to fetch low stock items");
        const lowStockData = await lowStockResponse.json();
        setLowStockItems(lowStockData.lowStockProducts);
        setFilteredLowStock(lowStockData.lowStockProducts);

        // Fetch categories for filtering
        const categoriesResponse = await fetch("/api/categories");
        if (!categoriesResponse.ok) throw new Error("Failed to fetch categories");
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories);
      } catch (error) {
        console.error("Error fetching store stock data:", error);
        toast.error("Failed to load store stock data");
      } finally {
        setLoading(false);
      }
    };

    fetchStoreStockData();
  }, [storePagination.page, storePagination.limit]);

  // Fetch warehouse stock data
  useEffect(() => {
    const fetchWarehouseStockData = async () => {
      setWarehouseLoading(true);
      try {
        // Fetch warehouse stock
        const warehouseStockResponse = await fetch(
          `/api/inventory/warehouse-stock?page=${warehousePagination.page}&limit=${warehousePagination.limit}`
        );
        if (!warehouseStockResponse.ok) throw new Error("Failed to fetch warehouse stock");
        const warehouseStockData = await warehouseStockResponse.json();

        // Add stock status to each item
        const warehouseStockWithStatus = warehouseStockData.warehouseStock.map(
          (item: WarehouseStock) => {
            const quantity = Number(item.quantity);
            const minThreshold = Number(item.minThreshold);
            let stockStatus = "NORMAL";
            let percentRemaining = 100;

            if (quantity <= 0) {
              stockStatus = "OUT_OF_STOCK";
              percentRemaining = 0;
            } else if (quantity <= minThreshold * 0.5) {
              stockStatus = "CRITICAL";
              percentRemaining = Math.round((quantity / minThreshold) * 100);
            } else if (quantity <= minThreshold) {
              stockStatus = "LOW";
              percentRemaining = Math.round((quantity / minThreshold) * 100);
            }

            return {
              ...item,
              stockStatus,
              percentRemaining,
            };
          }
        );

        setWarehouseStock(warehouseStockWithStatus);
        setFilteredWarehouseStock(warehouseStockWithStatus);
        setWarehousePagination(warehouseStockData.pagination);
      } catch (error) {
        console.error("Error fetching warehouse stock data:", error);
        toast.error("Failed to load warehouse stock data");
      } finally {
        setWarehouseLoading(false);
      }
    };

    // Only fetch warehouse data when the warehouse tab is active or on initial load
    if (activeTab === "warehouse" || warehouseStock.length === 0) {
      fetchWarehouseStockData();
    }
  }, [warehousePagination.page, warehousePagination.limit, activeTab, warehouseStock.length]);

  // Handle search and filtering
  useEffect(() => {
    if (searchTerm || categoryFilter) {
      // Filter function for stock items (storeStock and warehouseStock)
      const filterStock = (stock: any[]) => {
        return stock.filter((item) => {
          // Add null/undefined checks for item and item.product
          if (!item || !item.product) {
            return false;
          }

          const matchesSearch = searchTerm
            ? (item.product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
               item.product.sku?.toLowerCase().includes(searchTerm.toLowerCase()))
            : true;

          const matchesCategory = categoryFilter
            ? item.product.category?.id === categoryFilter
            : true;

          return matchesSearch && matchesCategory;
        });
      };

      // Filter function for low stock items (direct product structure)
      const filterLowStockItems = (items: any[]) => {
        return items.filter((item) => {
          // Add null/undefined checks for item
          if (!item) {
            return false;
          }

          const matchesSearch = searchTerm
            ? (item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
               item.sku?.toLowerCase().includes(searchTerm.toLowerCase()))
            : true;

          const matchesCategory = categoryFilter
            ? item.category?.id === categoryFilter
            : true;

          return matchesSearch && matchesCategory;
        });
      };

      setFilteredStoreStock(filterStock(storeStock));
      setFilteredWarehouseStock(filterStock(warehouseStock));
      setFilteredLowStock(filterLowStockItems(lowStockItems));
    } else {
      setFilteredStoreStock(storeStock);
      setFilteredWarehouseStock(warehouseStock);
      setFilteredLowStock(lowStockItems);
    }
  }, [searchTerm, categoryFilter, storeStock, warehouseStock, lowStockItems]);

  // Handle sorting
  const handleSort = (key: string, stockType: "store" | "warehouse" = "store") => {
    let direction: "ascending" | "descending" = "ascending";

    if (sortConfig.key === key) {
      direction = sortConfig.direction === "ascending" ? "descending" : "ascending";
    }

    setSortConfig({ key, direction });

    if (stockType === "store") {
      const sortedData = [...filteredStoreStock].sort((a, b) => {
        let aValue: any, bValue: any;

        // Handle nested properties
        if (key.includes(".")) {
          const keys = key.split(".");
          aValue = keys.reduce((obj: any, k: string) => obj?.[k], a);
          bValue = keys.reduce((obj: any, k: string) => obj?.[k], b);
        } else {
          aValue = a[key as keyof StoreStock];
          bValue = b[key as keyof StoreStock];
        }

        // Handle string comparison
        if (typeof aValue === "string") {
          return direction === "ascending"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        // Handle number comparison
        return direction === "ascending" ? aValue - bValue : bValue - aValue;
      });

      setFilteredStoreStock(sortedData);
    } else {
      const sortedData = [...filteredWarehouseStock].sort((a, b) => {
        let aValue: any, bValue: any;

        // Handle nested properties
        if (key.includes(".")) {
          const keys = key.split(".");
          aValue = keys.reduce((obj: any, k: string) => obj?.[k], a);
          bValue = keys.reduce((obj: any, k: string) => obj?.[k], b);
        } else {
          aValue = a[key as keyof WarehouseStock];
          bValue = b[key as keyof WarehouseStock];
        }

        // Handle string comparison
        if (typeof aValue === "string") {
          return direction === "ascending"
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }

        // Handle number comparison
        return direction === "ascending" ? aValue - bValue : bValue - aValue;
      });

      setFilteredWarehouseStock(sortedData);
    }
  };

  // Render stock status badge
  const renderStockStatus = (status: string | undefined) => {
    if (!status) return <Badge variant="outline">Unknown</Badge>;

    switch (status) {
      case "OUT_OF_STOCK":
        return <Badge variant="destructive">Out of Stock</Badge>;
      case "CRITICAL":
        return <Badge variant="destructive">Critical</Badge>;
      case "LOW":
        return <Badge className="bg-yellow-500 hover:bg-yellow-600">Low</Badge>;
      default:
        return <Badge variant="outline">Normal</Badge>;
    }
  };

  // Handle page change
  const handleStorePageChange = (page: number) => {
    setStorePagination((prev) => ({ ...prev, page }));
  };

  // Handle warehouse page change
  const handleWarehousePageChange = (page: number) => {
    setWarehousePagination((prev) => ({ ...prev, page }));
  };

  // Toggle row expansion
  const toggleRowExpansion = (rowId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(rowId)) {
      newExpandedRows.delete(rowId);
    } else {
      newExpandedRows.add(rowId);
    }
    setExpandedRows(newExpandedRows);
  };

  // Format expiry date
  const formatExpiryDate = (date: Date | string | null) => {
    if (!date) return "No expiry";
    const expiryDate = new Date(date);
    const now = new Date();
    const diffTime = expiryDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {
      return <span className="text-red-600 font-medium">Expired</span>;
    } else if (diffDays <= 7) {
      return <span className="text-orange-600 font-medium">Expires in {diffDays} days</span>;
    } else if (diffDays <= 30) {
      return <span className="text-yellow-600">Expires in {diffDays} days</span>;
    } else {
      return <span className="text-green-600">Expires in {diffDays} days</span>;
    }
  };

  return (
    <MainLayout>
      <PageHeader title="Stock Management" />
      <div className="space-y-6">
        <div className="flex justify-end">
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => router.push("/inventory/stock/adjustments")}>
              Stock Adjustments
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/inventory/stock/simple-transfers")}
            >
              Stock Transfers
            </Button>
            <Button onClick={() => router.push("/inventory/stock/new")}>
              <Plus className="mr-2 h-4 w-4" /> Add Stock
            </Button>
          </div>
        </div>

        {/* Filters */}

        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <div className="relative">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search products..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
          <div className="w-[200px]">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Stock Tabs */}
        <Tabs defaultValue="store" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="store">Store Stock</TabsTrigger>
            <TabsTrigger value="warehouse">Warehouse Stock</TabsTrigger>
            <TabsTrigger value="low-stock">
              Low Stock <AlertTriangle className="ml-2 h-4 w-4 text-amber-500" />
            </TabsTrigger>
          </TabsList>

          {/* Store Stock Tab */}
          <TabsContent value="store" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Store Inventory</CardTitle>
                <CardDescription>Manage your store inventory levels and thresholds</CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredStoreStock.length > 0 ? (
                  <>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-8"></TableHead>
                          <TableHead
                            className="cursor-pointer"
                            onClick={() => handleSort("product.name")}
                          >
                            Product <ArrowUpDown className="inline h-4 w-4 ml-1" />
                          </TableHead>
                          <TableHead>SKU</TableHead>
                          <TableHead>Category</TableHead>
                          <TableHead
                            className="cursor-pointer"
                            onClick={() => handleSort("quantity")}
                          >
                            Quantity <ArrowUpDown className="inline h-4 w-4 ml-1" />
                          </TableHead>
                          <TableHead>Batch Info</TableHead>
                          <TableHead>Supplier Info</TableHead>
                          <TableHead
                            className="cursor-pointer"
                            onClick={() => handleSort("minThreshold")}
                          >
                            Min Threshold <ArrowUpDown className="inline h-4 w-4 ml-1" />
                          </TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredStoreStock.map((item) => (
                          <React.Fragment key={item.id}>
                            <TableRow>
                              <TableCell>
                                {(item.batchCount || 0) > 0 && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => toggleRowExpansion(item.id)}
                                    className="p-0 h-6 w-6"
                                  >
                                    {expandedRows.has(item.id) ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                  </Button>
                                )}
                              </TableCell>
                              <TableCell className="font-medium">{item.product.name}</TableCell>
                              <TableCell>{item.product.sku}</TableCell>
                              <TableCell>{item.product.category?.name || "—"}</TableCell>
                              <TableCell>
                                {Math.round(Number(item.quantity))}{" "}
                                {item.product.unit?.abbreviation || ""}
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  <div className="flex items-center gap-2">
                                    <Package className="h-4 w-4 text-muted-foreground" />
                                    <span className="text-sm font-medium">
                                      {item.batchCount || 0} batches
                                    </span>
                                  </div>
                                  {item.earliestExpiryDate && (
                                    <div className="flex items-center gap-2">
                                      <Calendar className="h-4 w-4 text-muted-foreground" />
                                      <span className="text-xs">
                                        {formatExpiryDate(item.earliestExpiryDate)}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="space-y-1">
                                  {item.product.productSuppliers && item.product.productSuppliers.length > 0 ? (
                                    item.product.productSuppliers.slice(0, 2).map((ps, index) => (
                                      <div key={ps.id} className="flex items-center gap-1">
                                        <Building className="h-3 w-3 text-muted-foreground" />
                                        <span className="text-xs text-blue-600">{ps.supplier.name}</span>
                                      </div>
                                    ))
                                  ) : (
                                    <span className="text-xs text-muted-foreground">No suppliers</span>
                                  )}
                                  {item.product.productSuppliers && item.product.productSuppliers.length > 2 && (
                                    <span className="text-xs text-muted-foreground">
                                      +{item.product.productSuppliers.length - 2} more
                                    </span>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell>{Math.round(Number(item.minThreshold))}</TableCell>
                              <TableCell>{renderStockStatus(item.stockStatus)}</TableCell>
                              <TableCell className="text-right">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/inventory/stock/edit/${item.id}`)}
                                >
                                  Edit
                                </Button>
                              </TableCell>
                            </TableRow>
                            {expandedRows.has(item.id) && item.batches && item.batches.length > 0 && (
                              <TableRow>
                                <TableCell colSpan={9} className="bg-muted/50 p-4">
                                  <div className="space-y-2">
                                    <h4 className="font-medium text-sm">Batch Details</h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                      {item.batches.map((batch) => (
                                        <div key={batch.id} className="border rounded-lg p-3 bg-background">
                                          <div className="space-y-2">
                                            <div className="flex justify-between items-start">
                                              <span className="font-medium text-sm">#{batch.batchNumber}</span>
                                              <Badge variant="outline" className="text-xs">
                                                {batch.quantity} units
                                              </Badge>
                                            </div>
                                            {batch.expiryDate && (
                                              <div className="text-xs">
                                                {formatExpiryDate(batch.expiryDate)}
                                              </div>
                                            )}
                                            {batch.productSupplier && (
                                              <div className="flex items-center gap-1">
                                                <Building className="h-3 w-3 text-muted-foreground" />
                                                <span className="text-xs text-blue-600">
                                                  {batch.productSupplier.supplier.name}
                                                </span>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </React.Fragment>
                        ))}
                      </TableBody>
                    </Table>
                    <div className="mt-4">
                      <CustomPagination
                        currentPage={storePagination.page}
                        totalPages={storePagination.pages}
                        onPageChange={handleStorePageChange}
                      />
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No stock items found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Warehouse Stock Tab */}
          <TabsContent value="warehouse" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Warehouse Inventory</CardTitle>
                <CardDescription>
                  Manage your warehouse inventory levels and thresholds
                </CardDescription>
              </CardHeader>
              <CardContent>
                {warehouseLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredWarehouseStock.length > 0 ? (
                  <>
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-8"></TableHead>
                            <TableHead
                              className="cursor-pointer"
                              onClick={() => handleSort("product.name", "warehouse")}
                            >
                              Product <ArrowUpDown className="inline h-4 w-4 ml-1" />
                            </TableHead>
                            <TableHead>SKU</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead
                              className="cursor-pointer"
                              onClick={() => handleSort("quantity", "warehouse")}
                            >
                              Quantity <ArrowUpDown className="inline h-4 w-4 ml-1" />
                            </TableHead>
                            <TableHead>Batch Info</TableHead>
                            <TableHead>Supplier Info</TableHead>
                            <TableHead
                              className="cursor-pointer"
                              onClick={() => handleSort("minThreshold", "warehouse")}
                            >
                              Min Threshold <ArrowUpDown className="inline h-4 w-4 ml-1" />
                            </TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredWarehouseStock.map((item) => (
                            <React.Fragment key={`warehouse-${item.id}`}>
                              <TableRow>
                                <TableCell>
                                  {(item.batchCount || 0) > 0 && (
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      onClick={() => toggleRowExpansion(`warehouse-${item.id}`)}
                                      className="p-0 h-6 w-6"
                                    >
                                      {expandedRows.has(`warehouse-${item.id}`) ? (
                                        <ChevronDown className="h-4 w-4" />
                                      ) : (
                                        <ChevronRight className="h-4 w-4" />
                                      )}
                                    </Button>
                                  )}
                                </TableCell>
                                <TableCell className="font-medium">{item.product.name}</TableCell>
                                <TableCell>{item.product.sku}</TableCell>
                                <TableCell>{item.product.category?.name || "—"}</TableCell>
                                <TableCell>
                                  {Math.round(Number(item.quantity))}{" "}
                                  {item.product.unit?.abbreviation || ""}
                                </TableCell>
                                <TableCell>
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-2">
                                      <Package className="h-4 w-4 text-muted-foreground" />
                                      <span className="text-sm font-medium">
                                        {item.batchCount || 0} batches
                                      </span>
                                    </div>
                                    {item.earliestExpiryDate && (
                                      <div className="flex items-center gap-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <span className="text-xs">
                                          {formatExpiryDate(item.earliestExpiryDate)}
                                        </span>
                                      </div>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="space-y-1">
                                    {item.product.productSuppliers && item.product.productSuppliers.length > 0 ? (
                                      item.product.productSuppliers.slice(0, 2).map((ps, index) => (
                                        <div key={ps.id} className="flex items-center gap-1">
                                          <Building className="h-3 w-3 text-muted-foreground" />
                                          <span className="text-xs text-blue-600">{ps.supplier.name}</span>
                                        </div>
                                      ))
                                    ) : (
                                      <span className="text-xs text-muted-foreground">No suppliers</span>
                                    )}
                                    {item.product.productSuppliers && item.product.productSuppliers.length > 2 && (
                                      <span className="text-xs text-muted-foreground">
                                        +{item.product.productSuppliers.length - 2} more
                                      </span>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>{Math.round(Number(item.minThreshold))}</TableCell>
                                <TableCell>{renderStockStatus(item.stockStatus)}</TableCell>
                                <TableCell className="text-right">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() =>
                                      router.push(`/inventory/stock/edit/${item.id}?type=warehouse`)
                                    }
                                  >
                                    Edit
                                  </Button>
                                </TableCell>
                              </TableRow>
                              {expandedRows.has(`warehouse-${item.id}`) && item.batches && item.batches.length > 0 && (
                                <TableRow>
                                  <TableCell colSpan={9} className="bg-muted/50 p-4">
                                    <div className="space-y-2">
                                      <h4 className="font-medium text-sm">Batch Details</h4>
                                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                        {item.batches.map((batch) => (
                                          <div key={batch.id} className="border rounded-lg p-3 bg-background">
                                            <div className="space-y-2">
                                              <div className="flex justify-between items-start">
                                                <span className="font-medium text-sm">#{batch.batchNumber}</span>
                                                <Badge variant="outline" className="text-xs">
                                                  {batch.quantity} units
                                                </Badge>
                                              </div>
                                              {batch.expiryDate && (
                                                <div className="text-xs">
                                                  {formatExpiryDate(batch.expiryDate)}
                                                </div>
                                              )}
                                              {batch.productSupplier && (
                                                <div className="flex items-center gap-1">
                                                  <Building className="h-3 w-3 text-muted-foreground" />
                                                  <span className="text-xs text-blue-600">
                                                    {batch.productSupplier.supplier.name}
                                                  </span>
                                                </div>
                                              )}
                                            </div>
                                          </div>
                                        ))}
                                      </div>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              )}
                            </React.Fragment>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    <div className="mt-4">
                      <CustomPagination
                        currentPage={warehousePagination.page}
                        totalPages={warehousePagination.pages}
                        onPageChange={handleWarehousePageChange}
                      />
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No warehouse stock items found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Low Stock Tab */}
          <TabsContent value="low-stock" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Low Stock Items</CardTitle>
                <CardDescription>
                  Products that are below their minimum threshold levels
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  </div>
                ) : filteredLowStock.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>SKU</TableHead>
                        <TableHead>Category</TableHead>
                        <TableHead>Current Stock</TableHead>
                        <TableHead>Min Threshold</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLowStock.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">{item.name}</TableCell>
                          <TableCell>{item.sku}</TableCell>
                          <TableCell>{item.category?.name || "—"}</TableCell>
                          <TableCell>
                            {Number(item.storeStock?.quantity || 0).toFixed(2)}{" "}
                            {item.unit?.abbreviation || ""}
                          </TableCell>
                          <TableCell>
                            {Number(item.storeStock?.minThreshold || 0).toFixed(2)}
                          </TableCell>
                          <TableCell>{renderStockStatus(item.stockStatus)}</TableCell>
                          <TableCell className="text-right">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() =>
                                router.push(`/inventory/stock/edit/${item.storeStock?.id}`)
                              }
                            >
                              Edit
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No low stock items found</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
