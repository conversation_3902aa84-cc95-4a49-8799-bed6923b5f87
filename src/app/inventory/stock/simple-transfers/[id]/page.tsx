"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Loader2, ArrowLeft, CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useClientAuth } from "@/hooks/use-client-auth";
import { use } from "react";

interface SimpleTransfer {
  id: string;
  date: string;
  product: {
    id: string;
    name: string;
    sku: string;
    category?: {
      name: string;
    };
    unit?: {
      name: string;
    };
  };
  quantity: number;
  fromStore: boolean;
  toStore: boolean;
  status: string;
  notes?: string;
  requestedBy: {
    id: string;
    name: string;
    email: string;
  };
  approvedBy?: {
    id: string;
    name: string;
    email: string;
  };
  approvedAt?: string;
  completedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export default function SimpleTransferDetailPage({ params }: { params: { id: string } }) {
  // Unwrap params using React.use()
  const unwrappedParams = use(params);
  const transferId = unwrappedParams.id;

  const router = useRouter();
  const { user } = useClientAuth();
  const [transfer, setTransfer] = useState<SimpleTransfer | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showCancelDialog, setShowCancelDialog] = useState(false);
  const [showCompleteDialog, setShowCompleteDialog] = useState(false);
  const [notes, setNotes] = useState("");

  // Check if user has permission to approve transfers
  const canApprove = user?.role === "SUPER_ADMIN" || user?.role === "WAREHOUSE_ADMIN";

  // Fetch transfer details
  useEffect(() => {
    const fetchTransfer = async () => {
      try {
        setLoading(true);
        // This is a placeholder - the API endpoint doesn't exist yet
        const response = await fetch(`/api/inventory/simple-transfers/${transferId}`);

        if (!response.ok) {
          if (response.status === 404) {
            toast.error("Transfer not found");
            router.push("/inventory/stock/simple-transfers");
            return;
          }
          throw new Error("Failed to fetch transfer");
        }

        const data = await response.json();
        setTransfer(data.transfer);
      } catch (error) {
        console.error("Error fetching transfer:", error);
        toast.error("Failed to load transfer details");

        // For demo purposes, create a mock transfer
        setTransfer({
          id: transferId,
          date: new Date().toISOString(),
          product: {
            id: "prod123",
            name: "Sample Product",
            sku: "SKU123",
            category: {
              name: "Hardware",
            },
            unit: {
              name: "Piece",
            },
          },
          quantity: 10,
          fromStore: true,
          toStore: false,
          status: "PENDING",
          notes: "Sample transfer for demonstration",
          requestedBy: {
            id: "user123",
            name: "John Doe",
            email: "<EMAIL>",
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTransfer();
  }, [transferId, router]);

  // Handle approve action
  const handleApprove = async () => {
    try {
      setActionLoading(true);

      // This is a placeholder - the API endpoint doesn't exist yet
      const response = await fetch(`/api/inventory/simple-transfers/${transferId}/approve`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to approve transfer");
      }

      // Update local state
      if (transfer) {
        setTransfer({
          ...transfer,
          status: "APPROVED",
          approvedBy: {
            id: user?.id || "",
            name: user?.name || "",
            email: user?.email || "",
          },
          approvedAt: new Date().toISOString(),
          notes: notes || transfer.notes,
        });
      }

      toast.success("Transfer approved successfully");
      setShowApproveDialog(false);
      setNotes("");
    } catch (error) {
      console.error("Error approving transfer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to approve transfer");
    } finally {
      setActionLoading(false);
    }
  };

  // Test API endpoint
  const testApiEndpoint = async () => {
    try {
      console.log("Testing API endpoint for transfer:", transferId);

      // Test GET endpoint
      const testUrl = `/api/inventory/simple-transfers/${transferId}/test`;
      console.log("Test API URL:", testUrl);

      const testResponse = await fetch(testUrl);
      const testData = await testResponse.json();

      console.log("Test API response:", testData);
      toast.success("API test endpoint is working");

      // Test POST endpoint
      const testPostResponse = await fetch(testUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ test: true }),
      });

      const testPostData = await testPostResponse.json();
      console.log("Test POST API response:", testPostData);

      return true;
    } catch (error) {
      console.error("Error testing API endpoint:", error);
      toast.error("API test endpoint failed");
      return false;
    }
  };

  // Handle complete action
  const handleComplete = async () => {
    try {
      // Check if transfer is in APPROVED status
      if (transfer?.status !== "APPROVED") {
        toast.error("Transfer must be in APPROVED status before it can be completed");
        return;
      }

      // Test API endpoint first
      const testResult = await testApiEndpoint();
      if (!testResult) {
        toast.error("API endpoint test failed. Cannot proceed with completion.");
        return;
      }

      setActionLoading(true);
      console.log("Completing transfer:", transferId);

      // Construct the API URL
      const apiUrl = `/api/inventory/simple-transfers/${transferId}/complete`;
      console.log("API URL:", apiUrl);

      // Call the API endpoint to complete the transfer
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        let errorData;
        try {
          errorData = await response.json();
          console.error("Error response from API:", errorData);
        } catch (jsonError) {
          console.error("Failed to parse error response as JSON:", jsonError);
          console.error("Response status:", response.status, response.statusText);
          console.error(
            "Raw response:",
            await response.text().catch(() => "Could not read response text")
          );
          throw new Error(`Server error: ${response.status} ${response.statusText}`);
        }

        if (errorData && errorData.error) {
          throw new Error(errorData.error);
        } else if (errorData && errorData.message) {
          throw new Error(errorData.message);
        } else {
          throw new Error(`Failed to complete transfer (Status: ${response.status})`);
        }
      }

      // Update local state
      if (transfer) {
        setTransfer({
          ...transfer,
          status: "COMPLETED",
          completedAt: new Date().toISOString(),
          notes: notes || transfer.notes,
        });
      }

      toast.success("Transfer completed successfully");
      setShowCompleteDialog(false);
      setNotes("");
    } catch (error) {
      console.error("Error completing transfer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to complete transfer");
    } finally {
      setActionLoading(false);
    }
  };

  // Handle cancel action
  const handleCancel = async () => {
    try {
      setActionLoading(true);

      // This is a placeholder - the API endpoint doesn't exist yet
      const response = await fetch(`/api/inventory/simple-transfers/${transferId}/cancel`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ notes }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to cancel transfer");
      }

      // Update local state
      if (transfer) {
        setTransfer({
          ...transfer,
          status: "CANCELLED",
          notes: notes || transfer.notes,
        });
      }

      toast.success("Transfer cancelled successfully");
      setShowCancelDialog(false);
      setNotes("");
    } catch (error) {
      console.error("Error cancelling transfer:", error);
      toast.error(error instanceof Error ? error.message : "Failed to cancel transfer");
    } finally {
      setActionLoading(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Pending
          </Badge>
        );
      case "APPROVED":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800">
            Approved
          </Badge>
        );
      case "COMPLETED":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800">
            Completed
          </Badge>
        );
      case "CANCELLED":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800">
            Cancelled
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Transfer Details"
        description="View and manage stock transfer details"
        actions={
          <Button
            variant="outline"
            onClick={() => router.push("/inventory/stock/simple-transfers")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Transfers
          </Button>
        }
      />

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : transfer ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Transfer #{transfer.id.substring(0, 8)}</CardTitle>
                  <CardDescription>
                    Created on {new Date(transfer.date).toLocaleDateString()} at{" "}
                    {new Date(transfer.date).toLocaleTimeString()}
                  </CardDescription>
                </div>
                {getStatusBadge(transfer.status)}
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    Product Details
                  </h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Name:</span>
                      <span className="text-sm font-medium">{transfer.product.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">SKU:</span>
                      <span className="text-sm font-medium">{transfer.product.sku}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Category:</span>
                      <span className="text-sm font-medium">
                        {transfer.product.category?.name || "—"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Unit:</span>
                      <span className="text-sm font-medium">
                        {transfer.product.unit?.name || "—"}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    Transfer Details
                  </h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm">Quantity:</span>
                      <span className="text-sm font-medium">{transfer.quantity}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">From:</span>
                      <span className="text-sm font-medium">
                        {transfer.fromStore ? "Store" : "Warehouse"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">To:</span>
                      <span className="text-sm font-medium">
                        {transfer.toStore ? "Store" : "Warehouse"}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Status:</span>
                      <span className="text-sm font-medium">{transfer.status}</span>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="my-6" />

              <div className="space-y-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Additional Information
                </h3>

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Requested By:</span>
                    <span className="text-sm font-medium">{transfer.requestedBy.name}</span>
                  </div>

                  {transfer.approvedBy && (
                    <div className="flex justify-between">
                      <span className="text-sm">Approved By:</span>
                      <span className="text-sm font-medium">{transfer.approvedBy.name}</span>
                    </div>
                  )}

                  {transfer.approvedAt && (
                    <div className="flex justify-between">
                      <span className="text-sm">Approved At:</span>
                      <span className="text-sm font-medium">
                        {new Date(transfer.approvedAt).toLocaleDateString()}{" "}
                        {new Date(transfer.approvedAt).toLocaleTimeString()}
                      </span>
                    </div>
                  )}

                  {transfer.completedAt && (
                    <div className="flex justify-between">
                      <span className="text-sm">Completed At:</span>
                      <span className="text-sm font-medium">
                        {new Date(transfer.completedAt).toLocaleDateString()}{" "}
                        {new Date(transfer.completedAt).toLocaleTimeString()}
                      </span>
                    </div>
                  )}
                </div>

                {transfer.notes && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-1">Notes:</h4>
                    <p className="text-sm bg-muted p-3 rounded-md">{transfer.notes}</p>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-2">
              {transfer.status === "PENDING" && (
                <>
                  <Button variant="outline" onClick={() => setShowCancelDialog(true)}>
                    <XCircle className="mr-2 h-4 w-4" />
                    Cancel Transfer
                  </Button>

                  {canApprove && (
                    <Button onClick={() => setShowApproveDialog(true)}>
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Approve Transfer
                    </Button>
                  )}
                </>
              )}

              {transfer.status === "APPROVED" && canApprove && (
                <Button onClick={() => setShowCompleteDialog(true)}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Complete Transfer
                </Button>
              )}
            </CardFooter>
          </Card>
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-amber-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">Transfer Not Found</h3>
            <p className="text-muted-foreground mb-4">The requested transfer could not be found.</p>
            <Button onClick={() => router.push("/inventory/stock/simple-transfers")}>
              Go Back to Transfers
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Approve Transfer</DialogTitle>
            <DialogDescription>
              Are you sure you want to approve this transfer? This will allow the transfer to be
              completed.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Add any additional notes about this approval"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowApproveDialog(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleApprove} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Approve Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Dialog */}
      <Dialog open={showCompleteDialog} onOpenChange={setShowCompleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Complete Transfer</DialogTitle>
            <DialogDescription>
              Are you sure you want to mark this transfer as completed? This will update the
              inventory levels.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="complete-notes">Additional Notes (Optional)</Label>
              <Textarea
                id="complete-notes"
                placeholder="Add any additional notes about this completion"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCompleteDialog(false)}
              disabled={actionLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleComplete} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Complete Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Cancel Dialog */}
      <Dialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Cancel Transfer</DialogTitle>
            <DialogDescription>
              Are you sure you want to cancel this transfer? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="cancel-notes">Reason for Cancellation</Label>
              <Textarea
                id="cancel-notes"
                placeholder="Please provide a reason for cancelling this transfer"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowCancelDialog(false)}
              disabled={actionLoading}
            >
              Go Back
            </Button>
            <Button variant="destructive" onClick={handleCancel} disabled={actionLoading}>
              {actionLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Cancel Transfer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
