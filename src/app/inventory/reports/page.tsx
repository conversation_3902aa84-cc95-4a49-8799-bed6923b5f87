"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
// Using sonner for toast notifications
import { toast } from "sonner";
import { Loader2, FileText } from "lucide-react";
import { SummaryReport } from "@/components/reports/SummaryReport";
import { ValuationReport } from "@/components/reports/ValuationReport";
import { MovementReport } from "@/components/reports/MovementReport";
import { LowStockReport } from "@/components/reports/LowStockReport";
import { ReportFilters } from "@/components/reports/ReportFilters";
import { ExportReportButton } from "@/components/reports/ExportReportButton";

export default function InventoryReportsPage() {
  const [activeTab, setActiveTab] = useState("summary");
  const [isLoading, setIsLoading] = useState(false);
  const [reportData, setReportData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    categoryId: "",
    startDate: "",
    endDate: "",
  });

  // Fetch report data based on active tab and filters
  const fetchReportData = async () => {
    setIsLoading(true);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append("type", activeTab);

      if (filters.categoryId) {
        queryParams.append("categoryId", filters.categoryId);
      }

      if (activeTab === "movement") {
        if (filters.startDate) {
          queryParams.append("startDate", filters.startDate);
        }
        if (filters.endDate) {
          queryParams.append("endDate", filters.endDate);
        }
      }

      // Fetch report data
      const response = await fetch(`/api/inventory/reports?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Error fetching report: ${response.statusText}`);
      }

      const data = await response.json();
      setReportData(data);
      // Clear any previous errors
      setError(null);
      // Show success toast if this was a manual refresh
      if (isLoading) {
        toast.success("Report data refreshed successfully");
      }
    } catch (error) {
      console.error("Error fetching report data:", error);
      setError(`Failed to load report: ${(error as Error).message}`);
      // Show toast notification
      toast.error(`Failed to load report: ${(error as Error).message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when tab or filters change
  useEffect(() => {
    fetchReportData();
  }, [activeTab, filters]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: any) => {
    setFilters({ ...filters, ...newFilters });
  };

  // Handle manual refresh
  const handleRefresh = () => {
    toast.info("Refreshing report data...");
    fetchReportData();
  };

  return (
    <MainLayout>
      <PageHeader
        title="Inventory Reports"
        description="View and export inventory reports"
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <FileText className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
            <ExportReportButton
              reportType={activeTab}
              filters={filters}
              disabled={isLoading || !reportData}
            />
          </div>
        }
      />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4 relative">
          <strong className="font-bold">Error!</strong>
          <span className="block sm:inline"> {error}</span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setError(null)}
          >
            <span className="text-xl">&times;</span>
          </button>
        </div>
      )}

      <Tabs defaultValue="summary" value={activeTab} onValueChange={handleTabChange}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="valuation">Valuation</TabsTrigger>
            <TabsTrigger value="movement">Movement</TabsTrigger>
            <TabsTrigger value="low_stock">Low Stock</TabsTrigger>
          </TabsList>
        </div>

        <ReportFilters
          reportType={activeTab}
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        <div className="mt-4">
          <TabsContent value="summary">
            <SummaryReport data={reportData} isLoading={isLoading} />
          </TabsContent>

          <TabsContent value="valuation">
            <ValuationReport data={reportData} isLoading={isLoading} />
          </TabsContent>

          <TabsContent value="movement">
            <MovementReport data={reportData} isLoading={isLoading} />
          </TabsContent>

          <TabsContent value="low_stock">
            <LowStockReport data={reportData} isLoading={isLoading} />
          </TabsContent>
        </div>
      </Tabs>
    </MainLayout>
  );
}
