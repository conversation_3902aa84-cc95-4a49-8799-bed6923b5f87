"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Package,
  Clock,
  Target,
  Lightbulb,
  RefreshCw,
  Filter,
  CheckCircle,
  XCircle,
  Loader2
} from "lucide-react";

interface OptimizationRecommendation {
  id: string;
  type: 'reorder' | 'overstock' | 'expiry_risk' | 'slow_moving' | 'price_optimization' | 'supplier_switch';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: {
    financial: number;
    operational: string;
  };
  actionItems: string[];
  data: {
    productId?: string;
    productName?: string;
    supplierId?: string;
    supplierName?: string;
    currentStock?: number;
    recommendedAction?: string;
    timeframe?: string;
    confidence?: number;
  };
  metrics: {
    turnoverRate?: number;
    daysOfStock?: number;
    expiryRisk?: number;
    costSavings?: number;
  };
}

interface OptimizationAnalysis {
  summary: {
    totalRecommendations: number;
    highPriorityCount: number;
    estimatedSavings: number;
    riskMitigation: number;
  };
  recommendations: OptimizationRecommendation[];
  insights: {
    topRisks: string[];
    opportunities: string[];
    trends: string[];
  };
  filters: {
    categoryId?: string;
    timeRange: string;
    type?: string;
    priority?: string;
  };
  generatedAt: string;
}

export default function InventoryOptimizationPage() {
  const [analysis, setAnalysis] = useState<OptimizationAnalysis | null>(null);
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [actioningRecommendations, setActioningRecommendations] = useState<Set<string>>(new Set());
  const [processedRecommendations, setProcessedRecommendations] = useState<Map<string, { action: string; timestamp: string }>>(new Map());
  const [showProcessed, setShowProcessed] = useState(true);

  // Filter states
  const [timeRange, setTimeRange] = useState("90days");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [typeFilter, setTypeFilter] = useState("all");
  const [priorityFilter, setPriorityFilter] = useState("all");

  // Load categories for filtering
  useEffect(() => {
    loadCategories();
  }, []);

  // Load recommendations on mount and filter changes
  useEffect(() => {
    loadRecommendations();
  }, [timeRange, categoryFilter, typeFilter, priorityFilter]);

  const loadCategories = async () => {
    try {
      const response = await fetch("/api/categories");
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error("Error loading categories:", error);
    }
  };

  const loadRecommendations = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      params.append("timeRange", timeRange);
      
      if (categoryFilter !== "all") {
        params.append("categoryId", categoryFilter);
      }
      if (typeFilter !== "all") {
        params.append("type", typeFilter);
      }
      if (priorityFilter !== "all") {
        params.append("priority", priorityFilter);
      }

      const response = await fetch(`/api/inventory/optimization-recommendations?${params.toString()}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to load optimization recommendations");
      }

      const data = await response.json();
      setAnalysis(data);
      toast.success("Optimization recommendations loaded successfully");
    } catch (error) {
      console.error("Error loading recommendations:", error);
      toast.error(error.message || "Failed to load optimization recommendations");
    } finally {
      setLoading(false);
    }
  };

  const handleRecommendationAction = async (recommendationId: string, action: string) => {
    try {
      // Add to actioning set to show loading state
      setActioningRecommendations(prev => new Set(prev).add(recommendationId));

      const response = await fetch("/api/inventory/optimization-recommendations", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          recommendationId,
          action,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update recommendation");
      }

      // Mark as processed with timestamp
      setProcessedRecommendations(prev => new Map(prev).set(recommendationId, {
        action,
        timestamp: new Date().toISOString()
      }));

      toast.success(`Recommendation ${action} successfully`, {
        description: action === 'implemented'
          ? 'This recommendation has been marked as implemented and will be tracked for effectiveness.'
          : 'This recommendation has been dismissed and will not appear in future analyses.'
      });

      // Update the analysis to reflect the change
      if (analysis) {
        const updatedRecommendations = analysis.recommendations.map(rec => {
          if (rec.id === recommendationId) {
            return {
              ...rec,
              status: action as 'implemented' | 'dismissed'
            };
          }
          return rec;
        });

        setAnalysis({
          ...analysis,
          recommendations: updatedRecommendations,
          summary: {
            ...analysis.summary,
            totalRecommendations: action === 'dismissed'
              ? analysis.summary.totalRecommendations - 1
              : analysis.summary.totalRecommendations
          }
        });
      }

    } catch (error) {
      console.error("Error updating recommendation:", error);
      toast.error("Failed to update recommendation");
    } finally {
      // Remove from actioning set
      setActioningRecommendations(prev => {
        const newSet = new Set(prev);
        newSet.delete(recommendationId);
        return newSet;
      });
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "destructive";
      case "medium": return "default";
      case "low": return "secondary";
      default: return "outline";
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "reorder": return <Package className="h-4 w-4" />;
      case "overstock": return <TrendingDown className="h-4 w-4" />;
      case "expiry_risk": return <AlertTriangle className="h-4 w-4" />;
      case "slow_moving": return <Clock className="h-4 w-4" />;
      case "price_optimization": return <DollarSign className="h-4 w-4" />;
      case "supplier_switch": return <Target className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case "reorder": return "Reorder";
      case "overstock": return "Overstock";
      case "expiry_risk": return "Expiry Risk";
      case "slow_moving": return "Slow Moving";
      case "price_optimization": return "Price Optimization";
      case "supplier_switch": return "Supplier Review";
      default: return type;
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Inventory Optimization"
        description="AI-powered recommendations to optimize inventory management based on batch analytics"
      />
      
      <div className="space-y-6">
        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Analysis Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Time Range</label>
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30days">Last 30 Days</SelectItem>
                    <SelectItem value="90days">Last 90 Days</SelectItem>
                    <SelectItem value="6months">Last 6 Months</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Category</label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {categories.map((category: any) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Type</label>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="reorder">Reorder</SelectItem>
                    <SelectItem value="overstock">Overstock</SelectItem>
                    <SelectItem value="expiry_risk">Expiry Risk</SelectItem>
                    <SelectItem value="slow_moving">Slow Moving</SelectItem>
                    <SelectItem value="price_optimization">Price Optimization</SelectItem>
                    <SelectItem value="supplier_switch">Supplier Review</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Priority</label>
                <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priorities</SelectItem>
                    <SelectItem value="high">High Priority</SelectItem>
                    <SelectItem value="medium">Medium Priority</SelectItem>
                    <SelectItem value="low">Low Priority</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex justify-between items-center mt-4">
              <div className="flex items-center gap-4">
                <Button
                  onClick={loadRecommendations}
                  disabled={loading}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                  {loading ? "Analyzing..." : "Refresh Analysis"}
                </Button>

                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="showProcessed"
                    checked={showProcessed}
                    onChange={(e) => setShowProcessed(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="showProcessed" className="text-sm font-medium">
                    Show processed recommendations
                  </label>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Generating optimization recommendations...</span>
          </div>
        ) : analysis ? (
          <>
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Recommendations</CardTitle>
                  <Lightbulb className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analysis.summary.totalRecommendations}</div>
                  <p className="text-xs text-muted-foreground">
                    {analysis.summary.highPriorityCount} high priority
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Estimated Savings</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {formatCurrency(analysis.summary.estimatedSavings)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Potential cost optimization
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Risk Mitigation</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analysis.summary.riskMitigation}</div>
                  <p className="text-xs text-muted-foreground">
                    Risk items identified
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Analysis Date</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-sm font-bold">
                    {new Date(analysis.generatedAt).toLocaleDateString()}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {new Date(analysis.generatedAt).toLocaleTimeString()}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <Tabs defaultValue="recommendations" className="space-y-4">
              <TabsList>
                <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
                <TabsTrigger value="insights">Insights</TabsTrigger>
              </TabsList>

              <TabsContent value="recommendations" className="space-y-4">
                {analysis.recommendations.length > 0 ? (
                  <div className="space-y-4">
                    {/* Show processed recommendations summary when hidden */}
                    {!showProcessed && processedRecommendations.size > 0 && (
                      <Card className="bg-blue-50 border-blue-200">
                        <CardContent className="py-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium">
                                {processedRecommendations.size} recommendation(s) have been processed
                              </span>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setShowProcessed(true)}
                            >
                              Show All
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    )}

                    {analysis.recommendations
                      .filter((recommendation) => {
                        const isProcessed = processedRecommendations.has(recommendation.id);
                        return showProcessed || !isProcessed;
                      })
                      .map((recommendation) => {
                        const isProcessed = processedRecommendations.has(recommendation.id);
                        const processedInfo = processedRecommendations.get(recommendation.id);
                        const isActioning = actioningRecommendations.has(recommendation.id);

                        return (
                        <Card
                          key={recommendation.id}
                          className={`transition-all duration-300 ${
                            isProcessed
                              ? processedInfo?.action === 'implemented'
                                ? 'bg-green-50 border-green-200'
                                : 'bg-gray-50 border-gray-200 opacity-75'
                              : ''
                          }`}
                        >
                          <CardHeader>
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-3">
                                {getTypeIcon(recommendation.type)}
                                <div>
                                  <CardTitle className="text-lg flex items-center gap-2">
                                    {recommendation.title}
                                    {isProcessed && (
                                      <Badge
                                        variant={processedInfo?.action === 'implemented' ? 'default' : 'secondary'}
                                        className="text-xs"
                                      >
                                        {processedInfo?.action === 'implemented' ? '✓ Implemented' : '✗ Dismissed'}
                                      </Badge>
                                    )}
                                  </CardTitle>
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {recommendation.description}
                                  </p>
                                  {isProcessed && processedInfo && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {processedInfo.action === 'implemented' ? 'Implemented' : 'Dismissed'} on{' '}
                                      {new Date(processedInfo.timestamp).toLocaleString()}
                                    </p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Badge variant={getPriorityColor(recommendation.priority)}>
                                  {recommendation.priority.toUpperCase()}
                                </Badge>
                                <Badge variant="outline">
                                  {getTypeLabel(recommendation.type)}
                                </Badge>
                              </div>
                            </div>
                          </CardHeader>
                        <CardContent>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                              <h4 className="font-medium mb-2">Impact</h4>
                              <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                  <span className="text-sm">Financial Impact:</span>
                                  <span className="font-medium">
                                    {formatCurrency(recommendation.impact.financial)}
                                  </span>
                                </div>
                                <p className="text-sm text-muted-foreground">
                                  {recommendation.impact.operational}
                                </p>
                              </div>

                              {recommendation.data.confidence && (
                                <div className="mt-3">
                                  <div className="flex items-center justify-between text-sm">
                                    <span>Confidence:</span>
                                    <span>{recommendation.data.confidence}%</span>
                                  </div>
                                  <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                                    <div
                                      className="bg-blue-600 h-2 rounded-full"
                                      style={{ width: `${recommendation.data.confidence}%` }}
                                    ></div>
                                  </div>
                                </div>
                              )}
                            </div>

                            <div>
                              <h4 className="font-medium mb-2">Action Items</h4>
                              <ul className="space-y-1">
                                {recommendation.actionItems.map((item, index) => (
                                  <li key={index} className="text-sm flex items-start gap-2">
                                    <span className="text-muted-foreground">•</span>
                                    {item}
                                  </li>
                                ))}
                              </ul>

                              <div className="flex gap-2 mt-4">
                                {!isProcessed ? (
                                  <>
                                    <Button
                                      size="sm"
                                      onClick={() => handleRecommendationAction(recommendation.id, 'implemented')}
                                      disabled={isActioning}
                                      className="flex items-center gap-1"
                                    >
                                      {isActioning ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <CheckCircle className="h-3 w-3" />
                                      )}
                                      {isActioning ? 'Processing...' : 'Implemented'}
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleRecommendationAction(recommendation.id, 'dismissed')}
                                      disabled={isActioning}
                                      className="flex items-center gap-1"
                                    >
                                      {isActioning ? (
                                        <Loader2 className="h-3 w-3 animate-spin" />
                                      ) : (
                                        <XCircle className="h-3 w-3" />
                                      )}
                                      {isActioning ? 'Processing...' : 'Dismiss'}
                                    </Button>
                                  </>
                                ) : (
                                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                    {processedInfo?.action === 'implemented' ? (
                                      <>
                                        <CheckCircle className="h-4 w-4 text-green-600" />
                                        <span>This recommendation has been implemented</span>
                                      </>
                                    ) : (
                                      <>
                                        <XCircle className="h-4 w-4 text-gray-600" />
                                        <span>This recommendation has been dismissed</span>
                                      </>
                                    )}
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                      );
                    })}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="py-8 text-center">
                      <Lightbulb className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900">No recommendations found</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Your inventory appears to be well-optimized with the current filters.
                      </p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="insights" className="space-y-6">
                {/* Key Insights Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                        Top Risks
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {analysis.insights.topRisks.length > 0 ? (
                        <ul className="space-y-3">
                          {analysis.insights.topRisks.map((risk, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm">{risk}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-muted-foreground">No significant risks identified</p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <TrendingUp className="h-5 w-5 text-green-500" />
                        Opportunities
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {analysis.insights.opportunities.length > 0 ? (
                        <ul className="space-y-3">
                          {analysis.insights.opportunities.map((opportunity, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm">{opportunity}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-muted-foreground">No optimization opportunities found</p>
                      )}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Target className="h-5 w-5 text-blue-500" />
                        Trends
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      {analysis.insights.trends.length > 0 ? (
                        <ul className="space-y-3">
                          {analysis.insights.trends.map((trend, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                              <span className="text-sm">{trend}</span>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-muted-foreground">No trends identified</p>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Detailed Analytics */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Recommendation Type Distribution */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Recommendation Distribution</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {Object.entries(
                          analysis.recommendations.reduce((acc, rec) => {
                            acc[rec.type] = (acc[rec.type] || 0) + 1;
                            return acc;
                          }, {} as Record<string, number>)
                        ).map(([type, count]) => (
                          <div key={type} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              {getTypeIcon(type)}
                              <span className="text-sm font-medium">{getTypeLabel(type)}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-20 bg-gray-200 rounded-full h-2">
                                <div
                                  className="bg-blue-600 h-2 rounded-full"
                                  style={{
                                    width: `${(count / analysis.recommendations.length) * 100}%`
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm font-bold w-8 text-right">{count}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Priority Distribution */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Priority Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {Object.entries(
                          analysis.recommendations.reduce((acc, rec) => {
                            acc[rec.priority] = (acc[rec.priority] || 0) + 1;
                            return acc;
                          }, {} as Record<string, number>)
                        ).map(([priority, count]) => (
                          <div key={priority} className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant={getPriorityColor(priority)} className="w-16 justify-center">
                                {priority.toUpperCase()}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="w-20 bg-gray-200 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full ${
                                    priority === 'high' ? 'bg-red-600' :
                                    priority === 'medium' ? 'bg-yellow-600' : 'bg-green-600'
                                  }`}
                                  style={{
                                    width: `${(count / analysis.recommendations.length) * 100}%`
                                  }}
                                ></div>
                              </div>
                              <span className="text-sm font-bold w-8 text-right">{count}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Financial Impact Analysis */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <DollarSign className="h-5 w-5" />
                        Financial Impact Analysis
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                          <span className="text-sm font-medium">Total Potential Savings</span>
                          <span className="text-lg font-bold text-green-600">
                            {formatCurrency(analysis.summary.estimatedSavings)}
                          </span>
                        </div>

                        <div className="space-y-2">
                          <h4 className="text-sm font-medium">Top Value Recommendations</h4>
                          {analysis.recommendations
                            .sort((a, b) => b.impact.financial - a.impact.financial)
                            .slice(0, 3)
                            .map((rec, index) => (
                              <div key={rec.id} className="flex justify-between items-center text-sm">
                                <span className="truncate flex-1 mr-2">{rec.title}</span>
                                <span className="font-medium text-green-600">
                                  {formatCurrency(rec.impact.financial)}
                                </span>
                              </div>
                            ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Action Required Summary */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Clock className="h-5 w-5" />
                        Action Timeline
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Immediate Action Required</span>
                            <Badge variant="destructive">
                              {analysis.recommendations.filter(r => r.priority === 'high').length}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">This Week</span>
                            <Badge variant="default">
                              {analysis.recommendations.filter(r => r.priority === 'medium').length}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">This Month</span>
                            <Badge variant="secondary">
                              {analysis.recommendations.filter(r => r.priority === 'low').length}
                            </Badge>
                          </div>
                        </div>

                        <div className="pt-3 border-t">
                          <div className="text-xs text-muted-foreground">
                            Analysis generated on {new Date(analysis.generatedAt).toLocaleDateString()}
                            at {new Date(analysis.generatedAt).toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <Card>
            <CardContent className="py-8 text-center">
              <p className="text-muted-foreground">
                Click "Refresh Analysis" to generate optimization recommendations
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
