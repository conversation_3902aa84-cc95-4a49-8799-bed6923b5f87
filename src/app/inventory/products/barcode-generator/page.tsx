"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Printer, Download, Loader2 } from "lucide-react";
import Link from "next/link";
import { BarcodeGeneratorForm } from "@/components/products/BarcodeGeneratorForm";
import { BarcodePreview } from "@/components/products/BarcodePreview";

interface BarcodeGeneratorOptions {
  displayProductName: boolean;
  compactLayout: boolean;
  addPageBreaks: boolean;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  barcode: string | null;
}

export default function BarcodeGeneratorPage() {
  const [selectedProducts, setSelectedProducts] = useState<Product[]>([]);
  const [mode, setMode] = useState<"multiple" | "single">("multiple");
  const [copies, setCopies] = useState<number>(1);
  const [options, setOptions] = useState<BarcodeGeneratorOptions>({
    displayProductName: true,
    compactLayout: false,
    addPageBreaks: true,
  });
  const [showPreview, setShowPreview] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle form submission
  const handleGeneratePreview = () => {
    // Validate form
    if (selectedProducts.length === 0) {
      setError("Please select at least one product");
      return;
    }

    if (mode === "single" && copies <= 0) {
      setError("Please enter a valid number of copies");
      return;
    }

    // Clear any previous errors
    setError(null);

    // Show preview
    setShowPreview(true);
  };

  // Handle option changes
  const handleOptionChange = (option: keyof BarcodeGeneratorOptions, value: boolean) => {
    setOptions((prev) => ({ ...prev, [option]: value }));
  };

  // Handle mode change
  const handleModeChange = (value: "multiple" | "single") => {
    setMode(value);
    // Reset copies if switching to multiple mode
    if (value === "multiple") {
      setCopies(1);
    }
  };

  // Handle copies change
  const handleCopiesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    setCopies(isNaN(value) ? 0 : value);
  };

  // Handle product selection
  const handleProductSelection = (products: Product[]) => {
    // If in single mode, only allow one product
    if (mode === "single") {
      // If trying to add a new product, replace the current one
      if (products.length > selectedProducts.length) {
        // Find the new product that was added
        const newProduct = products.find((p) => !selectedProducts.some((sp) => sp.id === p.id));
        if (newProduct) {
          setSelectedProducts([newProduct]);
        }
      } else if (products.length === 0) {
        // Allow clearing selection
        setSelectedProducts([]);
      } else {
        // If removing products, check if we're keeping the right one
        setSelectedProducts(products.slice(0, 1));
      }
    } else {
      // In multiple mode, allow multiple products
      setSelectedProducts(products);
    }
  };

  // Handle back button in preview
  const handleBackToEditor = () => {
    setShowPreview(false);
  };

  return (
    <MainLayout>
      <PageHeader
        title="Barcode Generator"
        description="Generate and print barcodes for your products"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {showPreview ? (
        <BarcodePreview
          products={selectedProducts}
          mode={mode}
          copies={copies}
          options={options}
          onBack={handleBackToEditor}
        />
      ) : (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Product Selection</CardTitle>
            </CardHeader>
            <CardContent>
              <BarcodeGeneratorForm
                onProductSelection={handleProductSelection}
                selectedProducts={selectedProducts}
                singleProductMode={mode === "single"}
              />
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Generation Mode</CardTitle>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={mode}
                onValueChange={(value) => handleModeChange(value as "multiple" | "single")}
                className="space-y-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="multiple" id="multiple" />
                  <Label htmlFor="multiple">Generate for multiple products</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="single" id="single" />
                  <Label htmlFor="single">Generate multiple copies of a single product</Label>
                </div>
              </RadioGroup>

              {mode === "single" && (
                <div className="mt-4">
                  <Label htmlFor="copies">Number of barcode instances to generate:</Label>
                  <Input
                    id="copies"
                    type="number"
                    min="1"
                    value={copies}
                    onChange={handleCopiesChange}
                    className="max-w-xs mt-2"
                  />
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="displayProductName"
                    checked={options.displayProductName}
                    onCheckedChange={(checked) =>
                      handleOptionChange("displayProductName", checked === true)
                    }
                  />
                  <Label htmlFor="displayProductName">Display product name under barcode</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="compactLayout"
                    checked={options.compactLayout}
                    onCheckedChange={(checked) =>
                      handleOptionChange("compactLayout", checked === true)
                    }
                  />
                  <Label htmlFor="compactLayout">
                    Compact layout (smaller barcodes, fits more per page)
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="addPageBreaks"
                    checked={options.addPageBreaks}
                    onCheckedChange={(checked) =>
                      handleOptionChange("addPageBreaks", checked === true)
                    }
                  />
                  <Label htmlFor="addPageBreaks">Add page breaks for printer-friendly output</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-end">
            <Button onClick={handleGeneratePreview} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                "Generate Preview"
              )}
            </Button>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
