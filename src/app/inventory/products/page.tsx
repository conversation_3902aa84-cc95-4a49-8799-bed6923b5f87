"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { ProductSearch, ProductSearchFilters } from "@/components/products/ProductSearch";
import { ImportExportButtons } from "@/components/products/ImportExportButtons";
import { Pagination } from "@/components/custom/pagination";
import { Loader2, Plus, Eye, Edit, Power, Trash, DollarSign, Barcode } from "lucide-react";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";
import { InlineEdit } from "@/components/products/InlineEdit";
import { toast } from "sonner";

interface Product {
  id: string;
  name: string;
  sku: string;
  barcode?: string | null;
  basePrice: number;
  friendPrice?: number | null;
  familyPrice?: number | null;
  imageUrl?: string | null;
  active: boolean;
  category?: {
    id: string;
    name: string;
  } | null;
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
  storeStock?: {
    quantity: number;
    minThreshold: number;
  } | null;
  temporaryPrice?: {
    id: string;
    value: number;
    type: "FIXED" | "PERCENTAGE";
    startDate: string;
    endDate: string;
  } | null;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface Category {
  id: string;
  name: string;
}

interface Unit {
  id: string;
  name: string;
  abbreviation: string;
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ProductSearchFilters>({
    search: "",
    category: "all",
    active: "all",
    temporaryPrice: "all",
  });
  const [categories, setCategories] = useState<Category[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [loadingCategories, setLoadingCategories] = useState(false);
  const [loadingUnits, setLoadingUnits] = useState(false);

  // Fetch products with filters and pagination
  const fetchProducts = async () => {
    setLoading(true);
    setError(null);

    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (filters.search) queryParams.append("search", filters.search);
      if (filters.category && filters.category !== "all")
        queryParams.append("category", filters.category);
      if (filters.active !== "all") queryParams.append("active", filters.active);
      if (filters.temporaryPrice !== "all" && filters.temporaryPrice !== undefined)
        queryParams.append("temporaryPrice", filters.temporaryPrice);
      queryParams.append("page", pagination.page.toString());
      queryParams.append("limit", pagination.limit.toString());

      // Fetch products
      const response = await fetch(`/api/products?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch products");
      }

      const data = await response.json();
      setProducts(data.products || []);

      // Handle case where pagination info is not provided
      if (data.pagination) {
        setPagination(data.pagination);
      } else {
        // Create default pagination based on products length
        const totalItems = data.products?.length || 0;
        setPagination({
          total: totalItems,
          page: 1,
          limit: pagination.limit,
          pages: Math.ceil(totalItems / pagination.limit) || 1,
        });
      }
    } catch (error) {
      console.error("Error fetching products:", error);
      setError("Failed to load products. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    setLoadingCategories(true);
    try {
      const response = await fetch("/api/categories");
      if (!response.ok) {
        throw new Error("Failed to fetch categories");
      }
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (error) {
      console.error("Error fetching categories:", error);
    } finally {
      setLoadingCategories(false);
    }
  };

  // Fetch units
  const fetchUnits = async () => {
    setLoadingUnits(true);
    try {
      const response = await fetch("/api/units");
      if (!response.ok) {
        throw new Error("Failed to fetch units");
      }
      const data = await response.json();
      setUnits(data.units || []);
    } catch (error) {
      console.error("Error fetching units:", error);
    } finally {
      setLoadingUnits(false);
    }
  };

  // Update product field
  const updateProductField = async (productId: string, field: string, value: string) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ [field]: value }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update product");
      }

      // Update the product in the local state
      setProducts((prevProducts) =>
        prevProducts.map((product) => {
          if (product.id === productId) {
            // Handle special cases for category and unit
            if (field === "categoryId") {
              const category = categories.find((cat) => cat.id === value);
              return {
                ...product,
                category: category ? { id: category.id, name: category.name } : null,
              };
            } else if (field === "unitId") {
              const unit = units.find((u) => u.id === value);
              return {
                ...product,
                unit: unit
                  ? { id: unit.id, name: unit.name, abbreviation: unit.abbreviation }
                  : product.unit,
              };
            }
            // Handle regular fields
            return { ...product, [field]: value };
          }
          return product;
        })
      );

      toast.success(`Product ${field.replace(/([A-Z])/g, " $1").toLowerCase()} updated`);
    } catch (error) {
      console.error(`Error updating product ${field}:`, error);
      toast.error(
        `Failed to update product: ${error instanceof Error ? error.message : "Unknown error"}`
      );
      throw error;
    }
  };

  // Fetch products when filters or pagination changes
  useEffect(() => {
    fetchProducts();
  }, [filters, pagination.page, pagination.limit]);

  // Fetch categories and units on component mount
  useEffect(() => {
    fetchCategories();
    fetchUnits();
  }, []);

  // Handle filter changes
  const handleFilterChange = (newFilters: ProductSearchFilters) => {
    // Only update if filters have actually changed
    if (
      filters.search !== newFilters.search ||
      filters.category !== newFilters.category ||
      filters.active !== newFilters.active ||
      filters.temporaryPrice !== newFilters.temporaryPrice
    ) {
      setFilters(newFilters);
      // Reset to first page when filters change
      setPagination((prev) => ({ ...prev, page: 1 }));
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Handle product deletion
  const handleDeleteProduct = async (productId: string) => {
    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete product");
      }

      // Refresh products after deletion
      fetchProducts();
    } catch (error) {
      console.error("Error deleting product:", error);
      setError("Failed to delete product. Please try again.");
    }
  };

  // Handle product activation/deactivation
  const handleToggleActive = async (id: string, active: boolean) => {
    try {
      const response = await fetch(`/api/products/${id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ active }),
      });

      if (!response.ok) {
        throw new Error("Failed to update product status");
      }

      // Refresh products after update
      fetchProducts();
    } catch (error) {
      console.error("Error updating product status:", error);
      setError("Failed to update product status. Please try again.");
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Products"
        description="Manage your inventory products"
        actions={
          <div className="flex items-center gap-2">
            <ImportExportButtons />
            <Button variant="outline" asChild>
              <Link href="/inventory/products/discount-update">
                <DollarSign className="h-4 w-4 mr-2" />
                Discount Update
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/inventory/products/bulk-update">
                <DollarSign className="h-4 w-4 mr-2" />
                Bulk Price Update
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/inventory/products/barcode-generator">
                <Barcode className="h-4 w-4 mr-2" />
                Barcode Generator
              </Link>
            </Button>
            <Button asChild>
              <Link href="/inventory/products/new">
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Link>
            </Button>
          </div>
        }
      />

      <div className="mb-6">
        <ProductSearch onFilterChange={handleFilterChange} />
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading products...</span>
        </div>
      ) : products.length === 0 ? (
        <div className="text-center p-12 border rounded-lg bg-muted/20">
          <h3 className="text-lg font-medium mb-2">No products found</h3>
          <p className="text-muted-foreground mb-4">
            {filters.search ||
            filters.category ||
            filters.active !== "all" ||
            filters.temporaryPrice !== "all"
              ? "Try adjusting your filters or search terms."
              : "Get started by adding your first product."}
          </p>
          <Button asChild>
            <Link href="/inventory/products/new">
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </Link>
          </Button>
        </div>
      ) : (
        <div className="block w-full overflow-x-auto scrollbar-none">
          <table className="min-w-full caption-bottom text-sm rounded-md border">
            <thead className="[&_tr]:border-b">
              <tr className="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors">
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Name
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  SKU
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Category
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Unit
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Base Price
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Stock
                </th>
                <th className="text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap">
                  Status
                </th>
                <th className="text-foreground h-10 px-2 text-right align-middle font-medium whitespace-nowrap">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="[&_tr:last-child]:border-0">
              {products.map((product) => {
                // Get stock status
                const getStockStatus = () => {
                  if (!product.storeStock)
                    return { label: "No Stock", variant: "outline" as const };

                  const quantity = Number(product.storeStock.quantity);
                  const threshold = Number(product.storeStock.minThreshold);

                  if (quantity <= 0)
                    return { label: "Out of Stock", variant: "destructive" as const };
                  if (quantity < threshold)
                    return { label: "Low Stock", variant: "secondary" as const };
                  return { label: "In Stock", variant: "default" as const };
                };

                const stockStatus = getStockStatus();

                return (
                  <tr
                    key={product.id}
                    className="hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors"
                  >
                    <td className="p-2 align-middle whitespace-nowrap font-medium">
                      <InlineEdit
                        type="text"
                        value={product.name}
                        onSave={(value) => updateProductField(product.id, "name", value)}
                        placeholder="Enter product name"
                      />
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      <InlineEdit
                        type="text"
                        value={product.sku}
                        onSave={(value) => updateProductField(product.id, "sku", value)}
                        placeholder="Enter SKU"
                      />
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      <InlineEdit
                        type="dropdown"
                        value={product.category?.id || ""}
                        displayValue={product.category?.name || "Uncategorized"}
                        options={categories}
                        onSave={(value) => updateProductField(product.id, "categoryId", value)}
                        placeholder="Select category"
                      />
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      <InlineEdit
                        type="dropdown"
                        value={product.unit.id}
                        displayValue={`${product.unit.name} (${product.unit.abbreviation})`}
                        options={units}
                        onSave={(value) => updateProductField(product.id, "unitId", value)}
                        placeholder="Select unit"
                      />
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      {product.temporaryPrice ? (
                        <div>
                          <span className="line-through text-muted-foreground text-xs">
                            {formatCurrency(Number(product.basePrice))}
                          </span>
                          <br />
                          <span className="text-green-600 font-medium">
                            {product.temporaryPrice.type === "FIXED"
                              ? formatCurrency(Number(product.temporaryPrice.value))
                              : formatCurrency(
                                  Number(product.basePrice) -
                                    (Number(product.basePrice) *
                                      Number(product.temporaryPrice.value)) /
                                      100
                                )}
                          </span>
                        </div>
                      ) : (
                        formatCurrency(Number(product.basePrice))
                      )}
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      <Badge variant={stockStatus.variant}>{stockStatus.label}</Badge>
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap">
                      <Badge variant={product.active ? "default" : "secondary"}>
                        {product.active ? "Active" : "Inactive"}
                      </Badge>
                    </td>
                    <td className="p-2 align-middle whitespace-nowrap text-right">
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/inventory/products/${product.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/inventory/products/edit/${product.id}`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleToggleActive(product.id, !product.active)}
                        >
                          <Power className="h-4 w-4" />
                        </Button>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Trash className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                              <AlertDialogDescription>
                                This will permanently delete the product "{product.name}". This
                                action cannot be undone.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction onClick={() => handleDeleteProduct(product.id)}>
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          {pagination.pages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </div>
      )}
    </MainLayout>
  );
}
