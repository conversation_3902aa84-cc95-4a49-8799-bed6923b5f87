"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ArrowLeft, Calendar as CalendarIcon, Loader2 } from "lucide-react";
import Link from "next/link";
import { format, addDays, addWeeks, addMonths } from "date-fns";
import { cn } from "@/lib/utils";

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice: number;
  category?: {
    id: string;
    name: string;
  } | null;
  temporaryPrice?: {
    id: string;
    value: number;
    type: "FIXED" | "PERCENTAGE";
    startDate: string;
    endDate: string;
  } | null;
}

interface Category {
  id: string;
  name: string;
}

export default function DiscountUpdatePage() {
  // State for products and categories
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // State for search and filtering
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [selectedProductsDetails, setSelectedProductsDetails] = useState<Product[]>([]);

  // State for discount form
  const [discountType, setDiscountType] = useState<"FIXED" | "PERCENTAGE">("PERCENTAGE");
  const [discountValue, setDiscountValue] = useState<number>(10);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(addMonths(new Date(), 1));
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [updateMode, setUpdateMode] = useState<"PRODUCTS" | "CATEGORY">("PRODUCTS");

  // Fetch products and categories on component mount
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        // Fetch products
        const productsResponse = await fetch("/api/products?limit=500&active=true");
        const productsData = await productsResponse.json();

        // Fetch categories
        const categoriesResponse = await fetch("/api/categories");
        const categoriesData = await categoriesResponse.json();

        // Fetch temporary prices
        const temporaryPricesResponse = await fetch("/api/products/temporary-prices");
        const temporaryPricesData = await temporaryPricesResponse.json();

        // Map temporary prices to products
        const productsWithTemporaryPrices = productsData.products.map((product: Product) => {
          const temporaryPrice = temporaryPricesData.temporaryPrices?.find(
            (tp: any) => tp.productId === product.id
          );
          return {
            ...product,
            temporaryPrice: temporaryPrice || null,
          };
        });

        setProducts(productsWithTemporaryPrices);
        setFilteredProducts(productsWithTemporaryPrices.filter((p: Product) => !p.temporaryPrice));
        setCategories(categoriesData.categories);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load products and categories");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update filtered products when search term changes
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredProducts(products.filter(p => !p.temporaryPrice));
    } else {
      const lowerSearchTerm = searchTerm.toLowerCase();
      setFilteredProducts(
        products.filter(
          (product) =>
            !product.temporaryPrice &&
            (product.name.toLowerCase().includes(lowerSearchTerm) ||
              product.sku.toLowerCase().includes(lowerSearchTerm) ||
              product.category?.name.toLowerCase().includes(lowerSearchTerm))
        )
      );
    }
  }, [searchTerm, products]);

  // Update selected products details when selected products change
  useEffect(() => {
    const selectedDetails = products.filter((product) => selectedProducts.includes(product.id));
    setSelectedProductsDetails(selectedDetails);
  }, [selectedProducts, products]);

  // Handle selecting/deselecting a product
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId));
    }
  };

  // Handle selecting/deselecting all products
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(filteredProducts.map((product) => product.id));
    } else {
      setSelectedProducts([]);
    }
  };

  // Handle removing a product from selection
  const handleRemoveSelected = (productId: string) => {
    setSelectedProducts(selectedProducts.filter((id) => id !== productId));
  };

  // Handle clearing all selected products
  const handleClearSelected = () => {
    setSelectedProducts([]);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Handle quick date selection
  const handleQuickDateSelection = (option: "week" | "month" | "3months") => {
    const today = new Date();
    let newEndDate;

    switch (option) {
      case "week":
        newEndDate = addWeeks(today, 1);
        break;
      case "month":
        newEndDate = addMonths(today, 1);
        break;
      case "3months":
        newEndDate = addMonths(today, 3);
        break;
      default:
        newEndDate = addMonths(today, 1);
    }

    setStartDate(today);
    setEndDate(newEndDate);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Prepare request body based on update mode
      const requestBody = {
        type: discountType,
        value: discountValue,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
      };

      // Add either productIds or categoryId based on update mode
      if (updateMode === "PRODUCTS") {
        Object.assign(requestBody, { productIds: selectedProducts });
      } else {
        Object.assign(requestBody, { productIds: products
          .filter(p => p.category?.id === selectedCategory && !p.temporaryPrice)
          .map(p => p.id)
        });
      }

      const response = await fetch("/api/products/temporary-prices", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update products");
      }

      // Success message based on update mode
      if (updateMode === "PRODUCTS") {
        setSuccess(`Successfully applied temporary discount to ${selectedProducts.length} products`);
      } else {
        const category = categories.find((c) => c.id === selectedCategory);
        setSuccess(`Successfully applied temporary discount to products in category "${category?.name}"`);
      }

      // Refresh product list
      const productsResponse = await fetch("/api/products?limit=500&active=true");
      const productsData = await productsResponse.json();

      // Fetch temporary prices
      const temporaryPricesResponse = await fetch("/api/products/temporary-prices");
      const temporaryPricesData = await temporaryPricesResponse.json();

      // Map temporary prices to products
      const productsWithTemporaryPrices = productsData.products.map((product: Product) => {
        const temporaryPrice = temporaryPricesData.temporaryPrices?.find(
          (tp: any) => tp.productId === product.id
        );
        return {
          ...product,
          temporaryPrice: temporaryPrice || null,
        };
      });

      setProducts(productsWithTemporaryPrices);
      setFilteredProducts(productsWithTemporaryPrices.filter((p: Product) => !p.temporaryPrice));

      // Clear selection
      setSelectedProducts([]);
    } catch (error) {
      console.error("Error updating products:", error);
      setError((error as Error).message || "Failed to update products");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Temporary Discount Update"
        description="Apply temporary discounts to products for a specific time period"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
        }
      />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
        {/* Update Form */}
        <Card className="md:col-span-1">
          <CardContent className="p-6">
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Update Mode Selection */}
              <div className="space-y-2">
                <Label htmlFor="updateMode">Update Mode</Label>
                <Select value={updateMode} onValueChange={(value: any) => setUpdateMode(value)}>
                  <SelectTrigger id="updateMode">
                    <SelectValue placeholder="Select update mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PRODUCTS">Selected Products</SelectItem>
                    <SelectItem value="CATEGORY">By Category</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Category Selection (only shown in category mode) */}
              {updateMode === "CATEGORY" && (
                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <Select
                    value={selectedCategory || ""}
                    onValueChange={(value) => setSelectedCategory(value)}
                  >
                    <SelectTrigger id="category">
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="discountType">Discount Type</Label>
                <Select value={discountType} onValueChange={(value: any) => setDiscountType(value)}>
                  <SelectTrigger id="discountType">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PERCENTAGE">Percentage (%)</SelectItem>
                    <SelectItem value="FIXED">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="discountValue">
                  {discountType === "PERCENTAGE" ? "Percentage" : "Amount"}
                </Label>
                <Input
                  id="discountValue"
                  type="number"
                  value={discountValue}
                  onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                  min={0}
                  step={discountType === "PERCENTAGE" ? 0.1 : 1000}
                />
              </div>

              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="flex flex-col space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="startDate" className="text-xs">Start Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {format(startDate, "PPP")}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={startDate}
                            onSelect={(date) => date && setStartDate(date)}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    <div>
                      <Label htmlFor="endDate" className="text-xs">End Date</Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-full justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {format(endDate, "PPP")}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            selected={endDate}
                            onSelect={(date) => date && setEndDate(date)}
                            initialFocus
                            disabled={(date) => date < startDate}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                  </div>
                  <div className="flex flex-wrap gap-2 mt-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickDateSelection("week")}
                    >
                      1 Week
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickDateSelection("month")}
                    >
                      1 Month
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuickDateSelection("3months")}
                    >
                      3 Months
                    </Button>
                  </div>
                </div>
              </div>

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert variant="default" className="bg-green-50 text-green-800 border-green-200">
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <Button
                type="submit"
                className="w-full"
                disabled={
                  submitting ||
                  (updateMode === "PRODUCTS" && selectedProducts.length === 0) ||
                  (updateMode === "CATEGORY" && !selectedCategory) ||
                  discountValue <= 0 ||
                  startDate >= endDate
                }
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Applying Discount...
                  </>
                ) : updateMode === "PRODUCTS" ? (
                  `Apply Discount to ${selectedProducts.length} Products`
                ) : (
                  `Apply Discount to Category`
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Product Selection (only shown in products mode) */}
        {updateMode === "PRODUCTS" && (
          <Card className="md:col-span-2">
            <CardContent className="p-6">
              <div className="flex flex-col space-y-4">
                {/* Search and Selected Count */}
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="flex-1">
                    <Input
                      placeholder="Search products by name, SKU or category..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      {selectedProducts.length} selected
                    </span>
                    {selectedProducts.length > 0 && (
                      <Button variant="outline" size="sm" onClick={handleClearSelected}>
                        Clear
                      </Button>
                    )}
                  </div>
                </div>

                {/* Selected Products List */}
                {selectedProducts.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">Selected Products:</h4>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {selectedProductsDetails.map((product) => (
                        <div
                          key={`selected-${product.id}`}
                          className="flex items-center gap-1 bg-muted px-2 py-1 rounded-md text-sm"
                        >
                          <span>{product.name}</span>
                          <button
                            onClick={() => handleRemoveSelected(product.id)}
                            className="text-muted-foreground hover:text-destructive"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <Separator className="my-2" />

                {/* Product List with Checkboxes */}
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium">Available Products</h3>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="selectAll"
                      checked={
                        filteredProducts.length > 0 &&
                        filteredProducts.every((p) => selectedProducts.includes(p.id))
                      }
                      onCheckedChange={handleSelectAll}
                    />
                    <Label htmlFor="selectAll">Select All</Label>
                  </div>
                </div>

                {loading ? (
                  <div className="flex justify-center items-center p-12">
                    <Loader2 className="h-8 w-8 animate-spin text-primary" />
                    <span className="ml-2">Loading products...</span>
                  </div>
                ) : filteredProducts.length === 0 ? (
                  <div className="text-center p-12">
                    <p className="text-muted-foreground">No products found matching your search</p>
                  </div>
                ) : (
                  <div className="space-y-2 max-h-[400px] overflow-y-auto pr-2 mt-4">
                    {filteredProducts.map((product) => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 border rounded-md hover:bg-muted/50"
                      >
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            id={`product-${product.id}`}
                            checked={selectedProducts.includes(product.id)}
                            onCheckedChange={(checked) =>
                              handleSelectProduct(product.id, !!checked)
                            }
                          />
                          <div>
                            <p className="font-medium">{product.name}</p>
                            <p className="text-sm text-muted-foreground">SKU: {product.sku}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(Number(product.basePrice))}</p>
                          <p className="text-sm text-muted-foreground">
                            {product.category?.name || "Uncategorized"}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}