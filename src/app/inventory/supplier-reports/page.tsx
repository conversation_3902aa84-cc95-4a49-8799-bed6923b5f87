"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Plus, 
  Calendar, 
  FileText, 
  AlertTriangle, 
  RefreshCw,
  Clock,
  Mail,
  Play
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { format } from "date-fns";
import type { ScheduledReport } from "@/lib/scheduled-reports";

const createReportSchema = z.object({
  type: z.enum(['weekly', 'monthly', 'quarterly']),
  reportCategory: z.enum(['performance', 'cost', 'quality', 'relationship']),
  recipients: z.string().min(1, "At least one recipient is required"),
  supplierId: z.string().optional(),
});

type CreateReportFormValues = z.infer<typeof createReportSchema>;

export default function SupplierReportsPage() {
  const [reports, setReports] = useState<ScheduledReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const form = useForm<CreateReportFormValues>({
    resolver: zodResolver(createReportSchema),
    defaultValues: {
      type: 'monthly',
      reportCategory: 'performance',
      recipients: '',
      supplierId: '',
    },
  });

  const fetchReports = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/reports/scheduled');
      
      if (!response.ok) {
        throw new Error('Failed to fetch scheduled reports');
      }

      const data = await response.json();
      setReports(data.reports || []);
    } catch (error) {
      console.error('Error fetching reports:', error);
      setError('Failed to load scheduled reports. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const processDueReports = async () => {
    setIsProcessing(true);
    
    try {
      const response = await fetch('/api/reports/scheduled?action=process');
      
      if (!response.ok) {
        throw new Error('Failed to process reports');
      }

      const data = await response.json();
      toast.success(`Processed ${data.processed} reports successfully`);
      fetchReports(); // Refresh the list
    } catch (error) {
      console.error('Error processing reports:', error);
      toast.error('Failed to process reports');
    } finally {
      setIsProcessing(false);
    }
  };

  const onSubmit = async (data: CreateReportFormValues) => {
    try {
      const recipients = data.recipients.split(',').map(email => email.trim()).filter(Boolean);
      const parameters = data.supplierId ? { supplierId: data.supplierId } : {};

      const response = await fetch('/api/reports/scheduled', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: data.type,
          reportCategory: data.reportCategory,
          recipients,
          parameters,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create scheduled report');
      }

      toast.success('Scheduled report created successfully');
      setIsCreateDialogOpen(false);
      form.reset();
      fetchReports();
    } catch (error) {
      console.error('Error creating report:', error);
      toast.error('Failed to create scheduled report');
    }
  };

  useEffect(() => {
    fetchReports();
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'weekly': return 'bg-blue-100 text-blue-800';
      case 'monthly': return 'bg-green-100 text-green-800';
      case 'quarterly': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'performance': return 'bg-orange-100 text-orange-800';
      case 'cost': return 'bg-red-100 text-red-800';
      case 'quality': return 'bg-yellow-100 text-yellow-800';
      case 'relationship': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <PageHeader
          title="Supplier Reports"
          description="Manage automated supplier performance reports and analytics"
        />

        {/* Actions */}
        <div className="flex justify-between items-center">
          <div className="flex gap-2">
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Schedule Report
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Schedule New Report</DialogTitle>
                  <DialogDescription>
                    Create a new automated supplier report that will be generated and sent on a recurring basis.
                  </DialogDescription>
                </DialogHeader>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Frequency</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select frequency" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="quarterly">Quarterly</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="reportCategory"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Report Category</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="performance">Performance</SelectItem>
                              <SelectItem value="cost">Cost Analysis</SelectItem>
                              <SelectItem value="quality">Quality Metrics</SelectItem>
                              <SelectItem value="relationship">Relationship Health</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="recipients"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Recipients</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter email addresses separated by commas"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="supplierId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Specific Supplier (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Leave empty for all suppliers"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter>
                      <Button type="submit">Create Report</Button>
                    </DialogFooter>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          <Button 
            onClick={processDueReports} 
            disabled={isProcessing}
            variant="outline"
          >
            {isProcessing ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Play className="h-4 w-4 mr-2" />
            )}
            Process Due Reports
          </Button>
        </div>

        {/* Reports List */}
        <Card>
          <CardHeader>
            <CardTitle>Scheduled Reports</CardTitle>
            <CardDescription>
              Automated supplier reports that are generated and distributed on a recurring basis
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>Loading reports...</span>
              </div>
            ) : error ? (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            ) : reports.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">No scheduled reports found.</p>
                <p className="text-sm text-muted-foreground">Create your first automated report to get started.</p>
              </div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Recipients</TableHead>
                    <TableHead>Last Generated</TableHead>
                    <TableHead>Next Scheduled</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <Badge className={getTypeColor(report.type)}>
                          {report.type.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getCategoryColor(report.reportCategory)}>
                          {report.reportCategory.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <span>{report.recipients.length} recipient(s)</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {report.lastGenerated ? (
                          format(new Date(report.lastGenerated), "MMM dd, yyyy HH:mm")
                        ) : (
                          <span className="text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          {format(new Date(report.nextScheduled), "MMM dd, yyyy HH:mm")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={report.isActive ? "default" : "secondary"}>
                          {report.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
