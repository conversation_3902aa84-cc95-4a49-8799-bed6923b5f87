"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";
import {
  Search,
  Filter,
  Package,
  Truck,
  AlertTriangle,
  Clock,
  Eye,
  CheckCircle2,
  Loader2,
  FileText,
  Calendar,
} from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import Link from "next/link";

interface PartialReceiptItem {
  id: string;
  orderDate: string;
  status: string;
  total: number;
  subtotal: number;
  tax: number;
  notes?: string;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    receivedQuantity: number;
    unitPrice: number;
    product: {
      id: string;
      name: string;
      sku: string;
      unit: {
        name: string;
        abbreviation: string;
      };
    };
  }>;
  pendingItems: Array<{
    id: string;
    quantity: number;
    receivedQuantity: number;
    unitPrice: number;
    pendingQuantity: number;
    product: {
      id: string;
      name: string;
      sku: string;
      unit: {
        name: string;
        abbreviation: string;
      };
    };
  }>;
  lastReceiving?: {
    id: string;
    receivedAt: string;
    receivedBy: {
      id: string;
      name: string;
      email: string;
    };
  };
  daysSinceLastReceiving?: number;
  metrics: {
    totalItemsOrdered: number;
    totalItemsReceived: number;
    totalItemsPending: number;
    fulfillmentPercentage: number;
    pendingItemsCount: number;
    totalPendingValue: number;
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

interface Supplier {
  id: string;
  name: string;
}

export default function PartialReceiptsPage() {
  const [partialReceipts, setPartialReceipts] = useState<PartialReceiptItem[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Filter states
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState<string>("all");
  const [sortBy, setSortBy] = useState("orderDate");
  const [sortOrder, setSortOrder] = useState("desc");

  // Fetch suppliers for filter dropdown
  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
    }
  };

  // Fetch partial receipts with filters
  const fetchPartialReceipts = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });

      if (searchTerm) params.append("search", searchTerm);
      if (selectedSupplier && selectedSupplier !== "all")
        params.append("supplierId", selectedSupplier);

      const response = await fetch(`/api/inventory/partial-receipts?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch partial receipts");
      }

      const data = await response.json();
      setPartialReceipts(data.partialReceipts);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching partial receipts:", error);
      setError("Failed to load partial receipts");
      toast.error("Failed to load partial receipts");
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchSuppliers();
    fetchPartialReceipts();
  }, []);

  // Refetch when filters change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchPartialReceipts(1);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, selectedSupplier, sortBy, sortOrder]);

  const handlePageChange = (newPage: number) => {
    fetchPartialReceipts(newPage);
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedSupplier("all");
    setSortBy("orderDate");
    setSortOrder("desc");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "dd/MM/yyyy");
  };

  const getUrgencyBadge = (daysSinceLastReceiving: number | null) => {
    if (daysSinceLastReceiving === null) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          No receipts yet
        </Badge>
      );
    }

    if (daysSinceLastReceiving <= 3) {
      return (
        <Badge variant="default" className="flex items-center gap-1">
          <CheckCircle2 className="h-3 w-3" />
          Recent
        </Badge>
      );
    } else if (daysSinceLastReceiving <= 7) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          {daysSinceLastReceiving} days ago
        </Badge>
      );
    } else {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" />
          {daysSinceLastReceiving} days ago
        </Badge>
      );
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-500";
    if (percentage >= 50) return "bg-yellow-500";
    return "bg-red-500";
  };

  return (
    <MainLayout>
      <PageHeader
        title="Partial Receipt Management"
        description="Enhanced system for managing and tracking partial deliveries across multiple receipt sessions"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-orders">View All POs</Link>
            </Button>
          </div>
        }
      />

      <div className="space-y-6">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Partial Receipts</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{pagination.total}</div>
              <p className="text-xs text-muted-foreground">
                Purchase orders with partial deliveries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Value</CardTitle>
              <Truck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(
                  partialReceipts.reduce((sum, po) => sum + po.metrics.totalPendingValue, 0)
                )}
              </div>
              <p className="text-xs text-muted-foreground">Total value of pending items</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Urgent Follow-ups</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {
                  partialReceipts.filter(
                    (po) => po.daysSinceLastReceiving !== null && po.daysSinceLastReceiving > 7
                  ).length
                }
              </div>
              <p className="text-xs text-muted-foreground">POs with no activity for 7+ days</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg. Completion</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {partialReceipts.length > 0
                  ? Math.round(
                      partialReceipts.reduce(
                        (sum, po) => sum + po.metrics.fulfillmentPercentage,
                        0
                      ) / partialReceipts.length
                    )
                  : 0}
                %
              </div>
              <p className="text-xs text-muted-foreground">Average fulfillment rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Filters Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="PO number, supplier..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Supplier Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplier</label>
                <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                  <SelectTrigger>
                    <SelectValue placeholder="All suppliers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All suppliers</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort By */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort By</label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="orderDate">Order Date</SelectItem>
                    <SelectItem value="total">Total Value</SelectItem>
                    <SelectItem value="supplier">Supplier</SelectItem>
                    <SelectItem value="lastReceived">Last Received</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Order */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Order</label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">Descending</SelectItem>
                    <SelectItem value="asc">Ascending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Partial Receipts Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Partial Receipts
            </CardTitle>
            <CardDescription>
              {pagination.total} purchase orders with partial deliveries
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">{error}</p>
                <Button onClick={() => fetchPartialReceipts()} className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : partialReceipts.length > 0 ? (
              <>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>PO Number</TableHead>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Order Date</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Pending Items</TableHead>
                      <TableHead>Pending Value</TableHead>
                      <TableHead>Last Activity</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {partialReceipts.map((po) => (
                      <TableRow key={po.id}>
                        <TableCell className="font-medium">
                          #{po.id.slice(-8).toUpperCase()}
                        </TableCell>
                        <TableCell>{po.supplier.name}</TableCell>
                        <TableCell>{formatDate(po.orderDate)}</TableCell>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between text-sm">
                              <span>{po.metrics.fulfillmentPercentage}%</span>
                              <span className="text-muted-foreground">
                                {po.metrics.totalItemsReceived}/{po.metrics.totalItemsOrdered}
                              </span>
                            </div>
                            <Progress value={po.metrics.fulfillmentPercentage} className="h-2" />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <span className="font-medium">
                              {po.metrics.pendingItemsCount} items
                            </span>
                            <span className="text-sm text-muted-foreground">
                              {po.metrics.totalItemsPending} units
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>{formatCurrency(po.metrics.totalPendingValue)}</TableCell>
                        <TableCell>{getUrgencyBadge(po.daysSinceLastReceiving)}</TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/inventory/purchase-orders/${po.id}/receive`}>
                                <Package className="h-4 w-4 mr-1" />
                                Continue
                              </Link>
                            </Button>
                            <Button variant="outline" size="sm" asChild>
                              <Link href={`/inventory/purchase-orders/${po.id}`}>
                                <Eye className="h-4 w-4" />
                              </Link>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="flex justify-between items-center mt-4">
                    <p className="text-sm text-muted-foreground">
                      Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                      {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
                      {pagination.total} results
                    </p>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={!pagination.hasPrev}
                      >
                        Previous
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={!pagination.hasNext}
                      >
                        Next
                      </Button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-muted-foreground">No partial receipts found</p>
                <p className="text-sm text-muted-foreground mt-2">
                  All purchase orders are either fully received or not yet started
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
