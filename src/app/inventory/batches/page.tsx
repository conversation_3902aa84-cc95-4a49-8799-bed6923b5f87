"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Loader2,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  AlertTriangle,
  Package,
  Warehouse,
  Store,
  ArrowRight,
  Clock,
  ShoppingCart,
  FileText,
  Filter,
  BarChart3,
  ChevronDown,
  ChevronRight,
  GitBranch,
  MapPin
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import CustomPagination from "@/components/ui/custom-pagination";
import Link from "next/link";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface StockBatch {
  id: string;
  productId: string;
  productSupplierId: string;
  batchNumber: string | null;
  receivedDate: string;
  expiryDate: string | null;
  quantity: number;
  remainingQuantity: number;
  purchasePrice: number;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    sku: string;
    unit: {
      id: string;
      name: string;
      abbreviation: string;
    };
  };
  productSupplier: {
    id: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
    };
  };
  purchaseOrder?: {
    id: string;
    orderDate: string;
    status: string;
  };
  storeStock?: {
    id: string;
    quantity: number;
  };
  warehouseStock?: {
    id: string;
    quantity: number;
  };
}

interface BatchGroup {
  batchNumber: string;
  productId: string;
  productName: string;
  productSku: string;
  batches: StockBatch[];
  totalOriginalQuantity: number;
  totalRemainingQuantity: number;
  locations: string[];
  purchaseOrderId?: string;
  supplierName: string;
  supplierContactPerson?: string;
  earliestReceived: string;
  latestReceived: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export default function BatchesPage() {
  const router = useRouter();
  const [batches, setBatches] = useState<StockBatch[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [locationFilter, setLocationFilter] = useState<string>("all");
  const [supplierFilter, setSupplierFilter] = useState<string>("all");
  const [sortBy, setSortBy] = useState("receivedDate");
  const [sortOrder, setSortOrder] = useState("desc");
  const [viewMode, setViewMode] = useState<"grouped" | "flat">("grouped");
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set());
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 20,
    pages: 0,
  });

  // Fetch batches
  const fetchBatches = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy,
        sortOrder,
      });

      if (searchTerm) {
        params.append("search", searchTerm);
      }

      if (statusFilter !== "all") {
        params.append("status", statusFilter);
      }

      if (supplierFilter !== "all") {
        params.append("supplier", supplierFilter);
      }

      const response = await fetch(`/api/inventory/stock-batches?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch batches");
      }

      setBatches(data.batches);
      setPagination(data.pagination);
    } catch (err) {
      console.error("Error fetching batches:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch batches");
      toast.error("Failed to fetch batches");
    } finally {
      setLoading(false);
    }
  };

  // Group batches by batch number and product
  const batchGroups = useMemo(() => {
    const groups = new Map<string, BatchGroup>();

    batches.forEach(batch => {
      const key = `${batch.productId}-${batch.batchNumber || 'no-batch'}`;

      if (!groups.has(key)) {
        groups.set(key, {
          batchNumber: batch.batchNumber || 'No Batch Number',
          productId: batch.productId,
          productName: batch.product.name,
          productSku: batch.product.sku,
          batches: [],
          totalOriginalQuantity: 0,
          totalRemainingQuantity: 0,
          locations: [],
          purchaseOrderId: batch.purchaseOrder?.id,
          supplierName: batch.productSupplier.supplier.name,
          supplierContactPerson: batch.productSupplier.supplier.contactPerson || undefined,
          earliestReceived: batch.receivedDate,
          latestReceived: batch.receivedDate,
        });
      }

      const group = groups.get(key)!;
      group.batches.push(batch);

      // Always add remaining quantity (this represents current stock across all locations)
      group.totalRemainingQuantity += Number(batch.remainingQuantity);

      // Track locations
      if (batch.storeStock && !group.locations.includes('Store')) {
        group.locations.push('Store');
      }
      if (batch.warehouseStock && !group.locations.includes('Warehouse')) {
        group.locations.push('Warehouse');
      }

      // Update date range
      if (new Date(batch.receivedDate) < new Date(group.earliestReceived)) {
        group.earliestReceived = batch.receivedDate;
      }
      if (new Date(batch.receivedDate) > new Date(group.latestReceived)) {
        group.latestReceived = batch.receivedDate;
      }
    });

    // Calculate correct total original quantities for each group
    // The total original quantity should represent the original PO quantity, not the sum of split batches
    groups.forEach(group => {
      // Find the maximum quantity among batches with the same batch number
      // This represents the original quantity before any transfers
      const originalBatches = group.batches.filter(batch =>
        !batch.notes?.includes('Transferred from')
      );

      if (originalBatches.length > 0) {
        // Use the quantity from original batches (should be the same for all original batches with same batch number)
        group.totalOriginalQuantity = Math.max(...originalBatches.map(batch => Number(batch.quantity)));
      } else {
        // If we only have transferred batches, reconstruct the original quantity
        // by summing all the transferred quantities (this represents the original total)
        group.totalOriginalQuantity = group.batches.reduce((sum, batch) => sum + Number(batch.quantity), 0);
      }

      // Debug logging for development (remove in production)
      if (process.env.NODE_ENV === 'development') {
        console.log(`Batch Group: ${group.productName} - ${group.batchNumber}`, {
          totalBatches: group.batches.length,
          originalBatches: originalBatches.length,
          transferredBatches: group.batches.length - originalBatches.length,
          calculatedOriginalQuantity: group.totalOriginalQuantity,
          totalRemainingQuantity: group.totalRemainingQuantity,
          batchDetails: group.batches.map(b => ({
            id: b.id.slice(-8),
            quantity: Number(b.quantity),
            remaining: Number(b.remainingQuantity),
            isTransferred: b.notes?.includes('Transferred from'),
            location: b.warehouseStock ? 'Warehouse' : 'Store'
          }))
        });
      }
    });

    return Array.from(groups.values()).sort((a, b) => {
      // Sort by product name, then by batch number
      const productCompare = a.productName.localeCompare(b.productName);
      if (productCompare !== 0) return productCompare;
      return a.batchNumber.localeCompare(b.batchNumber);
    });
  }, [batches]);

  // Filter batches based on location and supplier filters
  const filteredBatches = useMemo(() => {
    let filtered = batches;

    // Apply location filter
    if (locationFilter !== "all") {
      filtered = filtered.filter(batch => {
        if (locationFilter === "warehouse") return batch.warehouseStock;
        if (locationFilter === "store") return batch.storeStock;
        return true;
      });
    }

    // Apply supplier filter
    if (supplierFilter !== "all") {
      filtered = filtered.filter(batch =>
        batch.productSupplier.supplier.id === supplierFilter
      );
    }

    return filtered;
  }, [batches, locationFilter, supplierFilter]);

  // Filter batch groups based on location and supplier filters
  const filteredBatchGroups = useMemo(() => {
    let filtered = batchGroups;

    // Apply location filter
    if (locationFilter !== "all") {
      filtered = filtered.filter(group => {
        if (locationFilter === "warehouse") return group.locations.includes('Warehouse');
        if (locationFilter === "store") return group.locations.includes('Store');
        return true;
      });
    }

    // Apply supplier filter
    if (supplierFilter !== "all") {
      filtered = filtered.filter(group =>
        group.batches.some(batch => batch.productSupplier.supplier.id === supplierFilter)
      );
    }

    return filtered;
  }, [batchGroups, locationFilter, supplierFilter]);

  // Initial load
  useEffect(() => {
    fetchBatches();
  }, [searchTerm, statusFilter, supplierFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchBatches(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    fetchBatches(page);
  };

  // Toggle group expansion
  const toggleGroupExpansion = (groupKey: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupKey)) {
      newExpanded.delete(groupKey);
    } else {
      newExpanded.add(groupKey);
    }
    setExpandedGroups(newExpanded);
  };

  // Expand all groups
  const expandAllGroups = () => {
    const allKeys = filteredBatchGroups.map(group =>
      `${group.productId}-${group.batchNumber}`
    );
    setExpandedGroups(new Set(allKeys));
  };

  // Collapse all groups
  const collapseAllGroups = () => {
    setExpandedGroups(new Set());
  };

  // Get location badge
  const getLocationBadge = (batch: StockBatch) => {
    const locations = [];
    if (batch.warehouseStock) {
      locations.push(
        <Badge key="warehouse" variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
          <Warehouse className="h-3 w-3 mr-1" />
          Warehouse
        </Badge>
      );
    }
    if (batch.storeStock) {
      locations.push(
        <Badge key="store" variant="secondary" className="bg-green-100 text-green-800 border-green-200">
          <Store className="h-3 w-3 mr-1" />
          Store
        </Badge>
      );
    }
    return locations;
  };

  // Get batch relationship indicator
  const getBatchRelationshipInfo = (batch: StockBatch, group: BatchGroup) => {
    const batchIndex = group.batches.findIndex(b => b.id === batch.id);
    const splitCount = group.batches.length;

    if (splitCount > 1) {
      return (
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <GitBranch className="h-3 w-3" />
          Split {batchIndex + 1} of {splitCount}
        </div>
      );
    }
    return null;
  };

  // Get status badge variant
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "EXPIRED":
        return <Badge variant="destructive">Expired</Badge>;
      case "RECALLED":
        return <Badge variant="destructive">Recalled</Badge>;
      case "SOLD_OUT":
        return <Badge variant="secondary">Sold Out</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check if batch is expiring soon (within 30 days)
  const isExpiringSoon = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    return expiry <= thirtyDaysFromNow && expiry > now;
  };

  // Check if batch is expired
  const isExpired = (expiryDate: string | null) => {
    if (!expiryDate) return false;
    return new Date(expiryDate) <= new Date();
  };

  // Get unique suppliers for filter dropdown
  const uniqueSuppliers = useMemo(() => {
    const suppliers = new Map();
    batches.forEach(batch => {
      const supplier = batch.productSupplier.supplier;
      if (!suppliers.has(supplier.id)) {
        suppliers.set(supplier.id, {
          id: supplier.id,
          name: supplier.name,
          contactPerson: supplier.contactPerson
        });
      }
    });
    return Array.from(suppliers.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [batches]);

  // Get summary statistics
  const summaryStats = useMemo(() => {
    const totalBatches = batches.length;
    const activeBatches = batches.filter(b => b.status === 'ACTIVE').length;
    const expiredBatches = batches.filter(b => b.status === 'EXPIRED').length;
    const soldOutBatches = batches.filter(b => b.status === 'SOLD_OUT').length;
    const totalSplits = batchGroups.reduce((sum, group) => sum + (group.batches.length - 1), 0);
    const uniqueProducts = new Set(batches.map(b => b.productId)).size;

    return {
      totalBatches,
      activeBatches,
      expiredBatches,
      soldOutBatches,
      totalSplits,
      uniqueProducts,
      batchGroups: batchGroups.length
    };
  }, [batches, batchGroups]);

  return (
    <TooltipProvider>
      <MainLayout>
        <PageHeader
          title="Batch Tracking"
          description="Manage and track inventory batches for enhanced traceability"
          actions={
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setViewMode(viewMode === 'grouped' ? 'flat' : 'grouped')}>
                {viewMode === 'grouped' ? <BarChart3 className="h-4 w-4 mr-2" /> : <GitBranch className="h-4 w-4 mr-2" />}
                {viewMode === 'grouped' ? 'Flat View' : 'Grouped View'}
              </Button>
              <Button asChild>
                <Link href="/inventory/batches/new">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Batch
                </Link>
              </Button>
            </div>
          }
        />

        <div className="space-y-6">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-blue-600">{summaryStats.totalBatches}</div>
                <p className="text-xs text-muted-foreground">Total Batches</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">{summaryStats.activeBatches}</div>
                <p className="text-xs text-muted-foreground">Active</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-red-600">{summaryStats.expiredBatches}</div>
                <p className="text-xs text-muted-foreground">Expired</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-gray-600">{summaryStats.soldOutBatches}</div>
                <p className="text-xs text-muted-foreground">Sold Out</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">{summaryStats.totalSplits}</div>
                <p className="text-xs text-muted-foreground">Total Splits</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-orange-600">{summaryStats.uniqueProducts}</div>
                <p className="text-xs text-muted-foreground">Products</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-indigo-600">{summaryStats.batchGroups}</div>
                <p className="text-xs text-muted-foreground">Batch Groups</p>
              </CardContent>
            </Card>
          </div>
          {/* Search and Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Search & Filter
              </CardTitle>
              <CardDescription>Find batches by product, supplier, batch number, or location</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSearch} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                  <div className="lg:col-span-2">
                    <label htmlFor="search" className="block text-sm font-medium mb-2">
                      Search
                    </label>
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        id="search"
                        placeholder="Search by product name, SKU, batch number, or supplier..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <label htmlFor="location" className="block text-sm font-medium mb-2">
                      Location
                    </label>
                    <Select value={locationFilter} onValueChange={setLocationFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All locations" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Locations</SelectItem>
                        <SelectItem value="warehouse">
                          <div className="flex items-center gap-2">
                            <Warehouse className="h-4 w-4 text-blue-600" />
                            Warehouse
                          </div>
                        </SelectItem>
                        <SelectItem value="store">
                          <div className="flex items-center gap-2">
                            <Store className="h-4 w-4 text-green-600" />
                            Store
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label htmlFor="supplier" className="block text-sm font-medium mb-2">
                      Supplier
                    </label>
                    <Select value={supplierFilter} onValueChange={setSupplierFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All suppliers" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Suppliers</SelectItem>
                        {uniqueSuppliers.map(supplier => (
                          <SelectItem key={supplier.id} value={supplier.id}>
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-purple-500 rounded-full" />
                              {supplier.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium mb-2">
                      Status
                    </label>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="ACTIVE">Active</SelectItem>
                        <SelectItem value="EXPIRED">Expired</SelectItem>
                        <SelectItem value="RECALLED">Recalled</SelectItem>
                        <SelectItem value="SOLD_OUT">Sold Out</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label htmlFor="sort" className="block text-sm font-medium mb-2">
                      Sort By
                    </label>
                    <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
                      const [field, order] = value.split('-');
                      setSortBy(field);
                      setSortOrder(order);
                    }}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="receivedDate-desc">Newest First</SelectItem>
                        <SelectItem value="receivedDate-asc">Oldest First</SelectItem>
                        <SelectItem value="expiryDate-asc">Expiry Date (Soon)</SelectItem>
                        <SelectItem value="expiryDate-desc">Expiry Date (Late)</SelectItem>
                        <SelectItem value="product.name-asc">Product A-Z</SelectItem>
                        <SelectItem value="product.name-desc">Product Z-A</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button type="submit">
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                  {viewMode === 'grouped' && (
                    <>
                      <Button type="button" variant="outline" onClick={expandAllGroups}>
                        <ChevronDown className="h-4 w-4 mr-2" />
                        Expand All
                      </Button>
                      <Button type="button" variant="outline" onClick={collapseAllGroups}>
                        <ChevronRight className="h-4 w-4 mr-2" />
                        Collapse All
                      </Button>
                    </>
                  )}
                </div>
              </form>
            </CardContent>
          </Card>

        {/* Batches Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Stock Batches
              {viewMode === 'grouped' && (
                <Badge variant="outline" className="ml-2">
                  <GitBranch className="h-3 w-3 mr-1" />
                  Grouped View
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {viewMode === 'grouped'
                ? `${filteredBatchGroups.length} batch groups with ${pagination.total} total batches`
                : `${pagination.total} total batches found`
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8">
                <p className="text-red-600">{error}</p>
                <Button onClick={() => fetchBatches()} className="mt-4">
                  Try Again
                </Button>
              </div>
            ) : (viewMode === 'grouped' ? filteredBatchGroups.length > 0 : filteredBatches.length > 0) ? (
              <>
                {viewMode === 'grouped' ? (
                  // Grouped View
                  <div className="space-y-4">
                    {filteredBatchGroups.map((group) => {
                      const groupKey = `${group.productId}-${group.batchNumber}`;
                      const isExpanded = expandedGroups.has(groupKey);

                      return (
                        <Collapsible
                          key={groupKey}
                          open={isExpanded}
                          onOpenChange={() => toggleGroupExpansion(groupKey)}
                        >
                          <div className="border rounded-lg">
                            {/* Group Header */}
                            <CollapsibleTrigger asChild>
                              <div className="flex items-center justify-between p-4 hover:bg-muted/50 cursor-pointer">
                                <div className="flex items-center gap-4">
                                  <div className="flex items-center gap-2">
                                    {isExpanded ? (
                                      <ChevronDown className="h-4 w-4" />
                                    ) : (
                                      <ChevronRight className="h-4 w-4" />
                                    )}
                                    <div>
                                      <div className="font-medium">{group.productName}</div>
                                      <div className="text-xs text-muted-foreground">
                                        Supplier: {group.supplierName}
                                      </div>
                                    </div>
                                    <Badge variant="outline" className="text-xs">
                                      {group.productSku}
                                    </Badge>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    <Badge variant="secondary" className="font-mono text-xs">
                                      {group.batchNumber}
                                    </Badge>

                                    {group.batches.length > 1 && (
                                      <Badge variant="outline" className="text-xs">
                                        <GitBranch className="h-3 w-3 mr-1" />
                                        {group.batches.length} splits
                                      </Badge>
                                    )}

                                    {/* Location badges */}
                                    <div className="flex gap-1">
                                      {group.locations.map(location => (
                                        <Badge
                                          key={location}
                                          variant="secondary"
                                          className={location === 'Warehouse'
                                            ? "bg-blue-100 text-blue-800 border-blue-200"
                                            : "bg-green-100 text-green-800 border-green-200"
                                          }
                                        >
                                          {location === 'Warehouse' ? (
                                            <Warehouse className="h-3 w-3 mr-1" />
                                          ) : (
                                            <Store className="h-3 w-3 mr-1" />
                                          )}
                                          {location}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                </div>

                                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                                  <div className="text-right">
                                    <div className="font-medium text-foreground">
                                      {group.totalRemainingQuantity}/{group.totalOriginalQuantity}
                                    </div>
                                    <div className="text-xs">remaining</div>
                                  </div>

                                  {group.purchaseOrderId && (
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          className="h-auto p-1"
                                          asChild
                                          onClick={(e) => e.stopPropagation()}
                                        >
                                          <Link href={`/inventory/purchase-orders/${group.purchaseOrderId}`}>
                                            <Badge variant="outline" className="text-xs cursor-pointer hover:bg-muted">
                                              <FileText className="h-3 w-3 mr-1" />
                                              PO
                                            </Badge>
                                          </Link>
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>
                                        Click to view Purchase Order: {group.purchaseOrderId}
                                      </TooltipContent>
                                    </Tooltip>
                                  )}
                                </div>
                              </div>
                            </CollapsibleTrigger>

                            {/* Group Content */}
                            <CollapsibleContent>
                              <div className="border-t">
                                <Table>
                                  <TableHeader>
                                    <TableRow className="bg-muted/30">
                                      <TableHead className="w-12"></TableHead>
                                      <TableHead>Location</TableHead>
                                      <TableHead>Quantity</TableHead>
                                      <TableHead>Received</TableHead>
                                      <TableHead>Expiry</TableHead>
                                      <TableHead>Status</TableHead>
                                      <TableHead>Actions</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {group.batches.map((batch, index) => (
                                      <TableRow key={batch.id} className="hover:bg-muted/30">
                                        <TableCell className="w-12">
                                          <div className="flex items-center justify-center">
                                            {getBatchRelationshipInfo(batch, group)}
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex items-center gap-2">
                                            {getLocationBadge(batch)}
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          <div>
                                            <div className="font-medium">
                                              {batch.remainingQuantity} / {batch.quantity}
                                            </div>
                                            <div className="text-sm text-muted-foreground">
                                              {batch.product.unit.abbreviation}
                                            </div>
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          <div className="text-sm">
                                            {new Date(batch.receivedDate).toLocaleDateString()}
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex items-center gap-2">
                                            {batch.expiryDate ? (
                                              <>
                                                <div className="text-sm">
                                                  {new Date(batch.expiryDate).toLocaleDateString()}
                                                </div>
                                                {isExpired(batch.expiryDate) && (
                                                  <AlertTriangle className="h-4 w-4 text-red-500" />
                                                )}
                                                {isExpiringSoon(batch.expiryDate) && !isExpired(batch.expiryDate) && (
                                                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                                )}
                                              </>
                                            ) : (
                                              <span className="text-sm text-muted-foreground">No expiry</span>
                                            )}
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          {getStatusBadge(batch.status)}
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex items-center gap-2">
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <Button variant="ghost" size="sm" asChild>
                                                  <Link href={`/inventory/batches/${batch.id}`}>
                                                    <Eye className="h-4 w-4" />
                                                  </Link>
                                                </Button>
                                              </TooltipTrigger>
                                              <TooltipContent>View Details</TooltipContent>
                                            </Tooltip>
                                            <Tooltip>
                                              <TooltipTrigger asChild>
                                                <Button variant="ghost" size="sm" asChild>
                                                  <Link href={`/inventory/batches/${batch.id}/edit`}>
                                                    <Edit className="h-4 w-4" />
                                                  </Link>
                                                </Button>
                                              </TooltipTrigger>
                                              <TooltipContent>Edit Batch</TooltipContent>
                                            </Tooltip>
                                          </div>
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>
                            </CollapsibleContent>
                          </div>
                        </Collapsible>
                      );
                    })}
                  </div>
                ) : (
                  // Flat View
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Product</TableHead>
                        <TableHead>Batch Number</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Supplier</TableHead>
                        <TableHead>Quantity</TableHead>
                        <TableHead>Received</TableHead>
                        <TableHead>Expiry</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBatches.map((batch) => (
                        <TableRow key={batch.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{batch.product.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {batch.product.sku}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-mono text-sm">
                              {batch.batchNumber || "N/A"}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getLocationBadge(batch)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {batch.productSupplier.supplier.name}
                              </div>
                              {batch.productSupplier.supplier.contactPerson && (
                                <div className="text-sm text-muted-foreground">
                                  {batch.productSupplier.supplier.contactPerson}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">
                                {batch.remainingQuantity} / {batch.quantity}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {batch.product.unit.abbreviation}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(batch.receivedDate).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {batch.expiryDate ? (
                                <>
                                  <div className="text-sm">
                                    {new Date(batch.expiryDate).toLocaleDateString()}
                                  </div>
                                  {isExpired(batch.expiryDate) && (
                                    <AlertTriangle className="h-4 w-4 text-red-500" />
                                  )}
                                  {isExpiringSoon(batch.expiryDate) && !isExpired(batch.expiryDate) && (
                                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                                  )}
                                </>
                              ) : (
                                <span className="text-sm text-muted-foreground">No expiry</span>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(batch.status)}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {batch.purchaseOrder && (
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button variant="ghost" size="sm" asChild>
                                      <Link href={`/inventory/purchase-orders/${batch.purchaseOrder.id}`}>
                                        <FileText className="h-4 w-4" />
                                      </Link>
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>View Purchase Order</TooltipContent>
                                </Tooltip>
                              )}
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" asChild>
                                    <Link href={`/inventory/batches/${batch.id}`}>
                                      <Eye className="h-4 w-4" />
                                    </Link>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>View Details</TooltipContent>
                              </Tooltip>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button variant="ghost" size="sm" asChild>
                                    <Link href={`/inventory/batches/${batch.id}/edit`}>
                                      <Edit className="h-4 w-4" />
                                    </Link>
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>Edit Batch</TooltipContent>
                              </Tooltip>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}

                {/* Pagination */}
                {pagination.pages > 1 && (
                  <div className="mt-6">
                    <CustomPagination
                      currentPage={pagination.page}
                      totalPages={pagination.pages}
                      onPageChange={handlePageChange}
                    />
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-8">
                <Package className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No batches found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Try adjusting your search criteria or add a new batch.
                </p>
                <div className="mt-6">
                  <Button asChild>
                    <Link href="/inventory/batches/new">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Batch
                    </Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
        </div>
      </MainLayout>
    </TooltipProvider>
  );
}
