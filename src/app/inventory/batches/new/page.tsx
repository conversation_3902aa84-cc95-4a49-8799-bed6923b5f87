"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, Loader2, ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import Link from "next/link";

interface Product {
  id: string;
  name: string;
  sku: string;
  unit: {
    id: string;
    name: string;
    abbreviation: string;
  };
}

interface ProductSupplier {
  id: string;
  supplier: {
    id: string;
    name: string;
    contactPerson: string | null;
  };
}

interface PendingReceiptItem {
  id: string;
  purchaseOrderId: string;
  productId: string;
  quantity: number;
  receivedQuantity: number;
  remainingQuantity: number;
  unitPrice: number;
  subtotal: number;
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    orderNumber: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
    };
  };
  product: {
    id: string;
    name: string;
    sku: string;
    unit: {
      id: string;
      name: string;
      abbreviation: string;
    };
  };
  productSupplier: {
    id: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
    };
  } | null;
}

interface FormData {
  productId: string;
  productSupplierId: string;
  purchaseOrderItemId: string;
  batchNumber: string;
  receivedDate: Date;
  expiryDate: Date | null;
  quantity: string;
  purchasePrice: string;
  notes: string;
}

export default function NewBatchPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [productSuppliers, setProductSuppliers] = useState<ProductSupplier[]>([]);
  const [pendingReceiptItems, setPendingReceiptItems] = useState<PendingReceiptItem[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(true);
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const [loadingPendingItems, setLoadingPendingItems] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    productId: "",
    productSupplierId: "",
    purchaseOrderItemId: "",
    batchNumber: "",
    receivedDate: new Date(),
    expiryDate: null,
    quantity: "",
    purchasePrice: "",
    notes: "",
  });

  // Check for productId in URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const productIdParam = urlParams.get("productId");
    if (productIdParam) {
      setFormData((prev) => ({ ...prev, productId: productIdParam }));
    }
  }, []);

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await fetch("/api/products?limit=1000");
        const data = await response.json();

        if (response.ok) {
          setProducts(data.products || []);
        } else {
          toast.error("Failed to fetch products");
        }
      } catch (error) {
        console.error("Error fetching products:", error);
        toast.error("Failed to fetch products");
      } finally {
        setLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  // Fetch pending receipt items for the selected product
  const fetchPendingReceiptItems = async (productId: string) => {
    try {
      setLoadingPendingItems(true);
      const response = await fetch(`/api/products/${productId}/pending-receipts`);
      const data = await response.json();

      if (response.ok) {
        setPendingReceiptItems(data.items || []);
      } else {
        console.error("Error fetching pending receipts:", data.error);
        setPendingReceiptItems([]);
        toast.error("Failed to load pending receipts");
      }
    } catch (error) {
      console.error("Error fetching pending receipts:", error);
      setPendingReceiptItems([]);
      toast.error("Failed to load pending receipts");
    } finally {
      setLoadingPendingItems(false);
    }
  };

  // Fetch product suppliers when product is selected
  useEffect(() => {
    if (formData.productId) {
      const fetchProductSuppliers = async () => {
        try {
          setLoadingSuppliers(true);
          const response = await fetch(`/api/products/${formData.productId}/suppliers`);
          const data = await response.json();

          if (response.ok) {
            setProductSuppliers(data.productSuppliers || []);
          } else {
            toast.error("Failed to fetch suppliers for this product");
            setProductSuppliers([]);
          }
        } catch (error) {
          console.error("Error fetching product suppliers:", error);
          toast.error("Failed to fetch suppliers");
          setProductSuppliers([]);
        } finally {
          setLoadingSuppliers(false);
        }
      };

      fetchProductSuppliers();
      fetchPendingReceiptItems(formData.productId);
    } else {
      setProductSuppliers([]);
      setPendingReceiptItems([]);
      setFormData((prev) => ({
        ...prev,
        productSupplierId: "",
        purchaseOrderItemId: "",
        quantity: "",
        purchasePrice: "",
      }));
    }
  }, [formData.productId]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.productId) {
      toast.error("Please select a product");
      return;
    }

    if (!formData.productSupplierId) {
      toast.error("Please select a supplier");
      return;
    }

    if (!formData.purchaseOrderItemId) {
      toast.error("Please select a purchase order item to receive");
      return;
    }

    if (!formData.quantity || parseFloat(formData.quantity) <= 0) {
      toast.error("Please enter a valid quantity");
      return;
    }

    if (!formData.purchasePrice || parseFloat(formData.purchasePrice) <= 0) {
      toast.error("Please enter a valid purchase price");
      return;
    }

    try {
      setLoading(true);

      const payload = {
        productId: formData.productId,
        productSupplierId: formData.productSupplierId,
        purchaseOrderItemId: formData.purchaseOrderItemId,
        batchNumber: formData.batchNumber || null,
        receivedDate: formData.receivedDate.toISOString(),
        expiryDate: formData.expiryDate?.toISOString() || null,
        quantity: parseFloat(formData.quantity),
        purchasePrice: parseFloat(formData.purchasePrice),
        notes: formData.notes || null,
      };

      const response = await fetch("/api/inventory/stock-batches", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Batch created successfully");
        router.push(`/inventory/batches/${data.batch.id}`);
      } else {
        throw new Error(data.error || "Failed to create batch");
      }
    } catch (error) {
      console.error("Error creating batch:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create batch");
    } finally {
      setLoading(false);
    }
  };

  const selectedProduct = products.find((p) => p.id === formData.productId);

  return (
    <MainLayout>
      <PageHeader
        title="Create New Batch"
        description="Add a new inventory batch for tracking and traceability"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/batches">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Batches
            </Link>
          </Button>
        }
      />

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Batch Information</CardTitle>
            <CardDescription>Enter the details for the new inventory batch</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Product Selection */}
              <div className="space-y-2">
                <Label htmlFor="product">Product *</Label>
                <Select
                  value={formData.productId}
                  onValueChange={(value) => setFormData((prev) => ({ ...prev, productId: value }))}
                  disabled={loadingProducts}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={loadingProducts ? "Loading products..." : "Select a product"}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        <div>
                          <div className="font-medium">{product.name}</div>
                          <div className="text-sm text-muted-foreground">{product.sku}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Supplier Selection */}
              <div className="space-y-2">
                <Label htmlFor="supplier">Supplier *</Label>
                <Select
                  value={formData.productSupplierId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, productSupplierId: value }))
                  }
                  disabled={!formData.productId || loadingSuppliers}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        !formData.productId
                          ? "Select a product first"
                          : loadingSuppliers
                            ? "Loading suppliers..."
                            : "Select a supplier"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {productSuppliers.map((ps) => (
                      <SelectItem key={ps.id} value={ps.id}>
                        <div>
                          <div className="font-medium">{ps.supplier.name}</div>
                          {ps.supplier.contactPerson && (
                            <div className="text-sm text-muted-foreground">
                              {ps.supplier.contactPerson}
                            </div>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.productId && productSuppliers.length === 0 && !loadingSuppliers && (
                  <p className="text-sm text-muted-foreground">
                    No suppliers found for this product. Please add a supplier first.
                  </p>
                )}
              </div>

              {/* Purchase Order Item Selection */}
              <div className="space-y-2">
                <Label htmlFor="poItem">Purchase Order Item *</Label>
                <Select
                  value={formData.purchaseOrderItemId}
                  onValueChange={(value) => {
                    const selectedItem = pendingReceiptItems.find((item) => item.id === value);
                    setFormData((prev) => ({
                      ...prev,
                      purchaseOrderItemId: value,
                      // Auto-fill purchase price from PO item
                      purchasePrice: selectedItem
                        ? selectedItem.unitPrice.toString()
                        : prev.purchasePrice,
                    }));
                  }}
                  disabled={!formData.productId || loadingPendingItems}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={
                        !formData.productId
                          ? "Select a product first"
                          : loadingPendingItems
                            ? "Loading purchase orders..."
                            : pendingReceiptItems.length === 0
                              ? "No pending receipts available"
                              : "Select a purchase order item"
                      }
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {pendingReceiptItems.map((item) => (
                      <SelectItem key={item.id} value={item.id}>
                        <div className="space-y-1">
                          <div className="font-medium">
                            {item.purchaseOrder.orderNumber} - {item.purchaseOrder.supplier.name}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Ordered: {item.quantity} | Received: {item.receivedQuantity} |
                            <span className="font-medium text-green-600">
                              {" "}
                              Remaining: {item.remainingQuantity}
                            </span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            Unit Price:{" "}
                            {new Intl.NumberFormat("id-ID", {
                              style: "currency",
                              currency: "IDR",
                            }).format(item.unitPrice)}{" "}
                            | Order Date:{" "}
                            {new Date(item.purchaseOrder.orderDate).toLocaleDateString()}
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {formData.productId && pendingReceiptItems.length === 0 && !loadingPendingItems && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-sm text-yellow-800">
                      <strong>No pending receipts found.</strong> Batches can only be created when
                      receiving goods from approved purchase orders. Please create a purchase order
                      for this product first.
                    </p>
                  </div>
                )}
                {formData.purchaseOrderItemId && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                    {(() => {
                      const selectedItem = pendingReceiptItems.find(
                        (item) => item.id === formData.purchaseOrderItemId
                      );
                      return selectedItem ? (
                        <div className="text-sm text-blue-800">
                          <p>
                            <strong>Selected PO:</strong> {selectedItem.purchaseOrder.orderNumber}
                          </p>
                          <p>
                            <strong>Supplier:</strong> {selectedItem.purchaseOrder.supplier.name}
                          </p>
                          <p>
                            <strong>Remaining to receive:</strong> {selectedItem.remainingQuantity}{" "}
                            {selectedItem.product.unit.abbreviation}
                          </p>
                          <p>
                            <strong>Unit Price:</strong>{" "}
                            {new Intl.NumberFormat("id-ID", {
                              style: "currency",
                              currency: "IDR",
                            }).format(selectedItem.unitPrice)}
                          </p>
                        </div>
                      ) : null;
                    })()}
                  </div>
                )}
              </div>

              {/* Batch Number */}
              <div className="space-y-2">
                <Label htmlFor="batchNumber">Batch Number</Label>
                <Input
                  id="batchNumber"
                  placeholder="Enter batch number (optional)"
                  value={formData.batchNumber}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, batchNumber: e.target.value }))
                  }
                />
              </div>

              {/* Quantity */}
              <div className="space-y-2">
                <Label htmlFor="quantity">
                  Quantity * {selectedProduct && `(${selectedProduct.unit.abbreviation})`}
                  {(() => {
                    const selectedItem = pendingReceiptItems.find(
                      (item) => item.id === formData.purchaseOrderItemId
                    );
                    return selectedItem ? (
                      <span className="text-sm text-muted-foreground ml-2">
                        (Max: {selectedItem.remainingQuantity})
                      </span>
                    ) : null;
                  })()}
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  step="0.01"
                  min="0"
                  max={(() => {
                    const selectedItem = pendingReceiptItems.find(
                      (item) => item.id === formData.purchaseOrderItemId
                    );
                    return selectedItem ? selectedItem.remainingQuantity : undefined;
                  })()}
                  placeholder="Enter quantity to receive"
                  value={formData.quantity}
                  onChange={(e) => setFormData((prev) => ({ ...prev, quantity: e.target.value }))}
                  required
                />
                {formData.quantity &&
                  formData.purchaseOrderItemId &&
                  (() => {
                    const selectedItem = pendingReceiptItems.find(
                      (item) => item.id === formData.purchaseOrderItemId
                    );
                    const quantity = parseFloat(formData.quantity);
                    if (selectedItem && quantity > selectedItem.remainingQuantity) {
                      return (
                        <p className="text-sm text-red-600">
                          Quantity cannot exceed remaining quantity (
                          {selectedItem.remainingQuantity})
                        </p>
                      );
                    }
                    return null;
                  })()}
              </div>

              {/* Purchase Price */}
              <div className="space-y-2">
                <Label htmlFor="purchasePrice">Purchase Price *</Label>
                <Input
                  id="purchasePrice"
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="Enter purchase price per unit"
                  value={formData.purchasePrice}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, purchasePrice: e.target.value }))
                  }
                  required
                />
              </div>

              {/* Received Date */}
              <div className="space-y-2">
                <Label>Received Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.receivedDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.receivedDate ? format(formData.receivedDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.receivedDate}
                      onSelect={(date) =>
                        date && setFormData((prev) => ({ ...prev, receivedDate: date }))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Expiry Date */}
              <div className="space-y-2">
                <Label>Expiry Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !formData.expiryDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formData.expiryDate
                        ? format(formData.expiryDate, "PPP")
                        : "Pick expiry date (optional)"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formData.expiryDate || undefined}
                      onSelect={(date) =>
                        setFormData((prev) => ({ ...prev, expiryDate: date || null }))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Notes */}
              <div className="space-y-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  placeholder="Enter any additional notes about this batch"
                  value={formData.notes}
                  onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
                  rows={3}
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" asChild>
                  <Link href="/inventory/batches">Cancel</Link>
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Create Batch
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
