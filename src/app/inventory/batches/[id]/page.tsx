"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { <PERSON>lt<PERSON>, Toolt<PERSON>Content, Tooltip<PERSON><PERSON>ider, TooltipTrigger } from "@/components/ui/tooltip";
import { 
  Loader2, 
  ArrowLeft, 
  Edit, 
  Trash2, 
  AlertTriangle, 
  Package, 
  Truck, 
  Calendar,
  DollarSign,
  Hash,
  FileText
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";
import Link from "next/link";

interface StockBatch {
  id: string;
  productId: string;
  productSupplierId: string;
  batchNumber: string | null;
  receivedDate: string;
  expiryDate: string | null;
  quantity: number;
  remainingQuantity: number;
  purchasePrice: number;
  status: "ACTIVE" | "EXPIRED" | "RECALLED" | "SOLD_OUT";
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  product: {
    id: string;
    name: string;
    sku: string;
    barcode: string | null;
    basePrice: number;
    unit: {
      id: string;
      name: string;
      abbreviation: string;
    };
  };
  productSupplier: {
    id: string;
    supplier: {
      id: string;
      name: string;
      contactPerson: string | null;
      phone: string | null;
      email: string | null;
      address: string | null;
    };
  };
  purchaseOrder?: {
    id: string;
    orderDate: string;
    status: string;
    total: number;
  };
  stockHistory: Array<{
    id: string;
    date: string;
    changeQuantity: number;
    source: string;
    notes: string | null;
  }>;
  transactionItems: Array<{
    id: string;
    quantity: number;
    unitPrice: number;
    transaction: {
      id: string;
      transactionDate: string;
      total: number;
    };
  }>;
}

export default function BatchDetailsPage() {
  const router = useRouter();
  const params = useParams();
  const batchId = params.id as string;
  
  const [batch, setBatch] = useState<StockBatch | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch batch details
  useEffect(() => {
    const fetchBatch = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/inventory/stock-batches/${batchId}`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || "Failed to fetch batch details");
        }

        setBatch(data.batch);
      } catch (err) {
        console.error("Error fetching batch:", err);
        setError(err instanceof Error ? err.message : "Failed to fetch batch details");
        toast.error("Failed to fetch batch details");
      } finally {
        setLoading(false);
      }
    };

    if (batchId) {
      fetchBatch();
    }
  }, [batchId]);

  // Handle delete batch
  const handleDelete = async () => {
    try {
      setDeleting(true);

      const response = await fetch(`/api/inventory/stock-batches/${batchId}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Batch deleted successfully");
        router.push("/inventory/batches");
      } else {
        throw new Error(data.error || "Failed to delete batch");
      }
    } catch (error) {
      console.error("Error deleting batch:", error);
      toast.error(error instanceof Error ? error.message : "Failed to delete batch");
    } finally {
      setDeleting(false);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
      case "EXPIRED":
        return <Badge variant="destructive">Expired</Badge>;
      case "RECALLED":
        return <Badge variant="destructive">Recalled</Badge>;
      case "SOLD_OUT":
        return <Badge variant="secondary">Sold Out</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Check if batch is expiring soon or expired
  const getExpiryStatus = (expiryDate: string | null) => {
    if (!expiryDate) return null;
    
    const expiry = new Date(expiryDate);
    const now = new Date();
    const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    
    if (expiry <= now) {
      return { status: "expired", color: "text-red-600", icon: AlertTriangle };
    } else if (expiry <= thirtyDaysFromNow) {
      return { status: "expiring", color: "text-yellow-600", icon: AlertTriangle };
    }
    
    return null;
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error || !batch) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <p className="text-red-600">{error || "Batch not found"}</p>
          <Button asChild className="mt-4">
            <Link href="/inventory/batches">Back to Batches</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  const expiryStatus = getExpiryStatus(batch.expiryDate);

  return (
    <MainLayout>
      <PageHeader
        title={`Batch Details`}
        description={`${batch.product.name} - ${batch.batchNumber || "No batch number"}`}
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/batches">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Batches
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href={`/inventory/batches/${batch.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Link>
            </Button>
            {batch.purchaseOrder ? (
              <TooltipProvider>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    <div>
                      <Button
                        variant="outline"
                        disabled={true}
                        className="bg-gray-100 text-gray-400 border-gray-300 cursor-not-allowed hover:bg-gray-100 hover:text-gray-400 hover:border-gray-300"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete (Disabled)
                      </Button>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="bottom" className="max-w-xs">
                    <div className="space-y-1">
                      <p className="font-medium">Cannot delete this batch</p>
                      <p className="text-xs">This batch was created from Purchase Order receiving and cannot be deleted to maintain data integrity with:</p>
                      <ul className="text-xs space-y-0.5 ml-2">
                        <li>• Purchase Order items and receiving history</li>
                        <li>• Inventory transactions and stock movements</li>
                        <li>• FIFO batch tracking logic</li>
                        <li>• Financial records tied to the PO</li>
                      </ul>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            ) : (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Batch</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this batch? This action cannot be undone.
                      {batch.transactionItems.length > 0 && (
                        <span className="block mt-2 text-yellow-600">
                          Note: This batch has transaction history and will be deactivated instead of deleted.
                        </span>
                      )}
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDelete}
                      disabled={deleting}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {deleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        }
      />

      <div className="space-y-6">
        {/* Batch Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Status</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-2">
                {getStatusBadge(batch.status)}
                {expiryStatus && (
                  <div className={`flex items-center gap-1 ${expiryStatus.color}`}>
                    <expiryStatus.icon className="h-4 w-4" />
                    <span className="text-sm">
                      {expiryStatus.status === "expired" ? "Expired" : "Expiring Soon"}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quantity</CardTitle>
              <Hash className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {batch.remainingQuantity} / {batch.quantity}
              </div>
              <p className="text-xs text-muted-foreground">
                {batch.product.unit.abbreviation} remaining
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Purchase Price</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(batch.purchasePrice)}
              </div>
              <p className="text-xs text-muted-foreground">
                per {batch.product.unit.abbreviation}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(batch.remainingQuantity * batch.purchasePrice)}
              </div>
              <p className="text-xs text-muted-foreground">
                remaining value
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Batch Information */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Product Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Product Name</label>
                <p className="text-lg font-medium">{batch.product.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">SKU</label>
                <p className="font-mono">{batch.product.sku}</p>
              </div>
              {batch.product.barcode && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Barcode</label>
                  <p className="font-mono">{batch.product.barcode}</p>
                </div>
              )}
              <div>
                <label className="text-sm font-medium text-muted-foreground">Unit</label>
                <p>{batch.product.unit.name} ({batch.product.unit.abbreviation})</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Base Price</label>
                <p>{formatCurrency(batch.product.basePrice)}</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Supplier Name</label>
                <p className="text-lg font-medium">{batch.productSupplier.supplier.name}</p>
              </div>
              {batch.productSupplier.supplier.contactPerson && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                  <p>{batch.productSupplier.supplier.contactPerson}</p>
                </div>
              )}
              {batch.productSupplier.supplier.phone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Phone</label>
                  <p>{batch.productSupplier.supplier.phone}</p>
                </div>
              )}
              {batch.productSupplier.supplier.email && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p>{batch.productSupplier.supplier.email}</p>
                </div>
              )}
              {batch.productSupplier.supplier.address && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Address</label>
                  <p>{batch.productSupplier.supplier.address}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Batch Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Batch Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Batch Number</label>
                <p className="font-mono text-lg">{batch.batchNumber || "N/A"}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Received Date</label>
                <p>{new Date(batch.receivedDate).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Expiry Date</label>
                <div className="flex items-center gap-2">
                  <p>{batch.expiryDate ? new Date(batch.expiryDate).toLocaleDateString() : "No expiry"}</p>
                  {expiryStatus && (
                    <expiryStatus.icon className={`h-4 w-4 ${expiryStatus.color}`} />
                  )}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <p>{new Date(batch.createdAt).toLocaleDateString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                <p>{new Date(batch.updatedAt).toLocaleDateString()}</p>
              </div>
            </div>
            
            {batch.notes && (
              <>
                <Separator className="my-4" />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Notes</label>
                  <p className="mt-1 whitespace-pre-wrap">{batch.notes}</p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Purchase Order Information */}
        {batch.purchaseOrder && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Purchase Order
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Order ID</label>
                  <p className="font-mono">{batch.purchaseOrder.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Order Date</label>
                  <p>{new Date(batch.purchaseOrder.orderDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <Badge variant="outline">{batch.purchaseOrder.status}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Transaction History */}
        {batch.transactionItems.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>Recent sales transactions using this batch</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Transaction ID</TableHead>
                    <TableHead>Quantity Sold</TableHead>
                    <TableHead>Unit Price</TableHead>
                    <TableHead>Total</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {batch.transactionItems.slice(0, 10).map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        {new Date(item.transaction.transactionDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell className="font-mono">{item.transaction.id}</TableCell>
                      <TableCell>{item.quantity} {batch.product.unit.abbreviation}</TableCell>
                      <TableCell>{formatCurrency(item.unitPrice)}</TableCell>
                      <TableCell>{formatCurrency(item.quantity * item.unitPrice)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Stock History */}
        {batch.stockHistory.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Stock History</CardTitle>
              <CardDescription>Recent stock movements for this batch</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Change</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {batch.stockHistory.slice(0, 10).map((history) => (
                    <TableRow key={history.id}>
                      <TableCell>
                        {new Date(history.date).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <span className={history.changeQuantity > 0 ? "text-green-600" : "text-red-600"}>
                          {history.changeQuantity > 0 ? "+" : ""}{history.changeQuantity} {batch.product.unit.abbreviation}
                        </span>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{history.source}</Badge>
                      </TableCell>
                      <TableCell>{history.notes || "—"}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}
