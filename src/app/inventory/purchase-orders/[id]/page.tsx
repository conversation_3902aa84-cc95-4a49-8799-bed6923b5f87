"use client";

import { useState, useEffect, use } from "react";
import { useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { ArrowLeft, Loader2, Check, X, Package, Edit, Printer, Send, User, Phone, Mail, Building, Tag, FileText, Plus, QrCode } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { notFound } from "next/navigation";
import { POStatusBadge } from "@/components/purchase-orders/POStatusBadge";
import { POPerformanceCard } from "@/components/purchase-orders/POPerformanceCard";
import { POStatusHistoryCard } from "@/components/purchase-orders/POStatusHistoryCard";
import { POStatusTransition } from "@/components/purchase-orders/POStatusTransition";
import { QRCodeDisplay } from "@/components/purchase-orders/QRCodeDisplay";
import { format } from "date-fns";
import { POStatus } from "@prisma/client";
import { useClientAuth } from "@/hooks/use-client-auth";

interface PurchaseOrderItem {
  id: string;
  product: {
    name: string;
    sku: string;
  };
  productSupplier?: {
    id: string;
    supplierProductCode?: string | null;
    supplierProductName?: string | null;
    supplier: {
      id: string;
      name: string;
      contactPerson?: string | null;
      phone?: string | null;
      email?: string | null;
    };
  } | null;
  supplierProductCode?: string | null;
  quantity: number;
  receivedQuantity?: number;
  unitPrice: number;
}

interface PurchaseOrder {
  id: string;
  status: POStatus;
  createdAt: string;
  lastTransitionAt: string | null;
  supplier: {
    name: string;
    contactPerson: string | null;
    phone: string | null;
    email: string | null;
  };
  items: PurchaseOrderItem[];
  notes: string | null;
  invoices?: Array<{
    id: string;
    invoiceNumber: string;
    status: string;
    total: number;
    createdAt: string;
  }>;
  performanceData?: {
    performanceScore: number;
    qualityScore: number;
    supplierScore: number;
    hasDelays: boolean;
    delayReason?: string;
    timeInStatus: number;
    batchMetrics: {
      totalBatches: number;
      activeBatches: number;
      expiringBatches: number;
      expiredBatches: number;
    };
  };
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PENDING_APPROVAL: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  ORDERED: "bg-purple-100 text-purple-800",
  PARTIALLY_RECEIVED: "bg-orange-100 text-orange-800",
  RECEIVED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Pending Approval",
  APPROVED: "Approved",
  ORDERED: "Ordered",
  PARTIALLY_RECEIVED: "Partially Received",
  RECEIVED: "Received",
  CANCELLED: "Cancelled",
};

async function getPurchaseOrder(id: string) {
  const res = await fetch(`/api/purchase-orders/${id}`);
  if (!res.ok) {
    if (res.status === 404) return null;
    throw new Error("Failed to fetch purchase order");
  }
  return res.json() as Promise<PurchaseOrder>;
}

async function getPOStatusHistory(id: string) {
  const res = await fetch(`/api/purchase-orders/${id}/status-history`);
  if (!res.ok) {
    throw new Error("Failed to fetch status history");
  }
  return res.json();
}

export default function PurchaseOrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const { user } = useClientAuth();
  const [po, setPO] = useState<PurchaseOrder | null>(null);
  const [history, setHistory] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isApproving, setIsApproving] = useState(false);
  const [isSubmittingForApproval, setIsSubmittingForApproval] = useState(false);
  const [isSendingOrder, setIsSendingOrder] = useState(false);
  const [isMarkingReceived, setIsMarkingReceived] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [showQRCodeModal, setShowQRCodeModal] = useState(false);

  const handleSubmitForApproval = async () => {
    if (!po) return;

    try {
      setIsSubmittingForApproval(true);

      const response = await fetch(`/api/purchase-orders/${po.id}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toStatus: "PENDING_APPROVAL",
          reason: "BUSINESS_REQUIREMENT",
          notes: "Purchase order submitted for approval"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to submit purchase order for approval");
      }

      const updatedPO = await response.json();
      setPO(updatedPO);
      toast.success("Purchase order submitted for approval successfully");

      // Refresh the page data
      await fetchData();
    } catch (error) {
      console.error("Error submitting purchase order for approval:", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit purchase order for approval");
    } finally {
      setIsSubmittingForApproval(false);
    }
  };

  const handleApprove = async () => {
    if (!po) return;

    try {
      setIsApproving(true);

      const response = await fetch(`/api/purchase-orders/${po.id}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toStatus: "APPROVED",
          reason: "MANAGEMENT_DECISION",
          notes: "Purchase order approved"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to approve purchase order");
      }

      const updatedPO = await response.json();
      setPO(updatedPO);
      toast.success("Purchase order approved successfully");

      // Refresh the page data
      await fetchData();
    } catch (error) {
      console.error("Error approving purchase order:", error);
      toast.error(error instanceof Error ? error.message : "Failed to approve purchase order");
    } finally {
      setIsApproving(false);
    }
  };

  const handleSendOrder = async () => {
    if (!po) return;

    try {
      setIsSendingOrder(true);

      const response = await fetch(`/api/purchase-orders/${po.id}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toStatus: "ORDERED",
          reason: "OPERATIONAL_CHANGE",
          notes: "Purchase order sent to supplier"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to send purchase order");
      }

      const updatedPO = await response.json();
      setPO(updatedPO);
      toast.success("Purchase order sent to supplier successfully");

      // Refresh the page data
      await fetchData();
    } catch (error) {
      console.error("Error sending purchase order:", error);
      toast.error(error instanceof Error ? error.message : "Failed to send purchase order");
    } finally {
      setIsSendingOrder(false);
    }
  };

  const handleMarkAsReceived = async () => {
    if (!po) return;

    try {
      setIsMarkingReceived(true);

      // Prepare receiving data for all items at full quantities
      const receivingItems = po.items.map((item) => {
        const remainingQuantity = Number(item.quantity) - Number(item.receivedQuantity || 0);
        return {
          purchaseOrderItemId: item.id,
          receivedQuantity: remainingQuantity,
          discrepancyReason: "",
          notes: "Bulk received via Mark as Received",
          batchNumber: `PO-${po.id.slice(-8).toUpperCase()}-${Date.now()}`,
          expiryDate: null,
          batchNotes: "Auto-generated batch for bulk receiving",
        };
      }).filter(item => item.receivedQuantity > 0); // Only include items with remaining quantities

      if (receivingItems.length === 0) {
        toast.error("All items have already been received");
        return;
      }

      const payload = {
        notes: "Purchase order marked as fully received via bulk operation",
        discrepancyReason: "",
        items: receivingItems,
      };

      const response = await fetch(`/api/purchase-orders/${po.id}/receive`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to mark purchase order as received");
      }

      toast.success("Purchase order marked as received successfully. Inventory has been updated.");

      // Refresh the page data
      await fetchData();
    } catch (error) {
      console.error("Error marking purchase order as received:", error);
      toast.error(error instanceof Error ? error.message : "Failed to mark purchase order as received");
    } finally {
      setIsMarkingReceived(false);
    }
  };

  const handleCancel = async () => {
    if (!po) return;

    try {
      setIsCancelling(true);

      const response = await fetch(`/api/purchase-orders/${po.id}/status-transition`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          toStatus: "CANCELLED",
          reason: "MANAGEMENT_DECISION",
          notes: "Purchase order cancelled"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to cancel purchase order");
      }

      const updatedPO = await response.json();
      setPO(updatedPO);
      toast.success("Purchase order cancelled successfully");

      // Refresh the page data
      await fetchData();
    } catch (error) {
      console.error("Error cancelling purchase order:", error);
      toast.error(error instanceof Error ? error.message : "Failed to cancel purchase order");
    } finally {
      setIsCancelling(false);
    }
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch PO details
      const poRes = await fetch(`/api/purchase-orders/${resolvedParams.id}`);
      if (!poRes.ok) {
        if (poRes.status === 404) {
          setPO(null);
          return;
        }
        throw new Error("Failed to fetch purchase order");
      }
      const poData = await poRes.json();
      setPO(poData);

      // Fetch PO history
      const historyRes = await fetch(`/api/purchase-orders/${resolvedParams.id}/status-history`);
      if (!historyRes.ok) {
        throw new Error("Failed to fetch status history");
      }
      const historyData = await historyRes.json();
      setHistory(historyData.statusHistory || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [resolvedParams.id]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">{error}</p>
      </div>
    );
  }

  if (!po) {
    notFound();
  }

  // Role-based permissions
  const isSuperAdmin = user?.role === "SUPER_ADMIN";
  const isWarehouseAdmin = user?.role === "WAREHOUSE_ADMIN";
  const hasApprovalPermission = isSuperAdmin;
  const hasWarehousePermission = isSuperAdmin || isWarehouseAdmin;

  // Status-based permissions
  const canSubmitForApproval = po.status === "DRAFT" && hasWarehousePermission;
  const canApprove = po.status === "PENDING_APPROVAL" && hasApprovalPermission;
  const canSendOrder = po.status === "APPROVED" && hasWarehousePermission;
  const canReceiveItems = ["ORDERED", "PARTIALLY_RECEIVED"].includes(po.status) && hasWarehousePermission;
  const canMarkAsReceived = po.status === "ORDERED" && hasWarehousePermission;
  const canCancel = !["RECEIVED", "CANCELLED"].includes(po.status);
  const canEdit = ["DRAFT", "PENDING_APPROVAL"].includes(po.status);

  return (
    <MainLayout>
      <PageHeader
        title={`Purchase Order #${po.id.slice(-8).toUpperCase()}`}
        description={`Purchase order for ${po.supplier.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/purchase-orders">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Purchase Orders
              </Link>
            </Button>

            {canEdit && (
              <Button variant="outline" asChild>
                <Link href={`/inventory/purchase-orders/${po.id}/edit`}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Link>
              </Button>
            )}

            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // Fetch PDF with proper authentication
                  const response = await fetch(`/api/purchase-orders/${po.id}?format=pdf`);
                  if (!response.ok) {
                    throw new Error(`Failed to generate PDF: ${response.statusText}`);
                  }

                  // Get the PDF blob
                  const pdfBlob = await response.blob();

                  // Create a URL for the PDF blob
                  const pdfUrl = URL.createObjectURL(pdfBlob);

                  // Open in new window for printing
                  const newWindow = window.open(pdfUrl, '_blank');
                  if (newWindow) {
                    // Auto-print after a short delay to allow PDF to load
                    setTimeout(() => {
                      newWindow.print();
                    }, 1000);
                  }

                  // Clean up the URL after a delay
                  setTimeout(() => {
                    URL.revokeObjectURL(pdfUrl);
                  }, 5000);
                } catch (error) {
                  console.error('Error generating PDF:', error);
                  alert('Failed to generate PDF. Please try again.');
                }
              }}
            >
              <Printer className="h-4 w-4 mr-2" />
              Print PDF
            </Button>

            {/* QR Code Button - Show for RECEIVED and PARTIALLY_RECEIVED status */}
            {(po.status === "RECEIVED" || po.status === "PARTIALLY_RECEIVED") && hasWarehousePermission && (
              <Dialog open={showQRCodeModal} onOpenChange={setShowQRCodeModal}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR Code
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Purchase Order QR Code</DialogTitle>
                  </DialogHeader>
                  <QRCodeDisplay
                    purchaseOrderId={po.id}
                    onClose={() => setShowQRCodeModal(false)}
                  />
                </DialogContent>
              </Dialog>
            )}

            {canSubmitForApproval && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button disabled={isSubmittingForApproval}>
                    {isSubmittingForApproval ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    {isSubmittingForApproval ? "Submitting..." : "Submit for Approval"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Submit for Approval</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to submit this purchase order for approval? Once submitted,
                      it will require management approval before proceeding.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleSubmitForApproval} disabled={isSubmittingForApproval}>
                      {isSubmittingForApproval ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        "Submit for Approval"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canApprove && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button disabled={isApproving}>
                    {isApproving ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Check className="h-4 w-4 mr-2" />
                    )}
                    {isApproving ? "Approving..." : "Approve"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Approve Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to approve this purchase order? This action cannot be
                      undone and will allow the order to proceed to the next stage.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleApprove} disabled={isApproving}>
                      {isApproving ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Approving...
                        </>
                      ) : (
                        "Approve"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canSendOrder && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button disabled={isSendingOrder}>
                    {isSendingOrder ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4 mr-2" />
                    )}
                    {isSendingOrder ? "Sending..." : "Send Order"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Send Order to Supplier</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to send this purchase order to the supplier? This will
                      change the status to ORDERED and the supplier will be notified.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleSendOrder} disabled={isSendingOrder}>
                      {isSendingOrder ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        "Send Order"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canReceiveItems && (
              <Button asChild>
                <Link href={`/inventory/purchase-orders/${po.id}/receive`}>
                  <Package className="h-4 w-4 mr-2" />
                  Receive Items
                </Link>
              </Button>
            )}

            {canMarkAsReceived && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" disabled={isMarkingReceived}>
                    {isMarkingReceived ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Package className="h-4 w-4 mr-2" />
                    )}
                    {isMarkingReceived ? "Marking as Received..." : "Mark as Received"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Mark as Received</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to mark this purchase order as fully received? This will:
                      <br />• Add all remaining quantities to warehouse inventory
                      <br />• Create inventory batch records for tracking
                      <br />• Mark the order as complete
                      <br />• Update all product stock levels
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleMarkAsReceived} disabled={isMarkingReceived}>
                      {isMarkingReceived ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Marking as Received...
                        </>
                      ) : (
                        "Mark as Received"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}

            {canCancel && (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" disabled={isCancelling}>
                    {isCancelling ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <X className="h-4 w-4 mr-2" />
                    )}
                    {isCancelling ? "Cancelling..." : "Cancel"}
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Cancel Purchase Order</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to cancel this purchase order? This action cannot be
                      undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>No, Keep Order</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleCancel}
                      disabled={isCancelling}
                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                    >
                      {isCancelling ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Cancelling...
                        </>
                      ) : (
                        "Yes, Cancel Order"
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
          </div>
        }
      />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Purchase Order #{po.id}</h1>
            <div className="flex items-center gap-4 mt-2">
              <POStatusBadge status={po.status} />
              <span className="text-sm text-muted-foreground">
                Created on {format(new Date(po.createdAt), "MMM d, yyyy")}
              </span>
            </div>
          </div>
          <POStatusTransition
            purchaseOrderId={po.id}
            currentStatus={po.status}
            userRole={user?.role || ""}
            onStatusChanged={async () => {
              // Refresh the page data after status change
              await fetchData();
            }}
          />
        </div>

        {/* Status Information */}
        {po.status === "DRAFT" && (
          <Alert>
            <AlertDescription>
              This purchase order is in draft status. {canSubmitForApproval
                ? "Submit for approval to proceed with the approval process."
                : "Only warehouse administrators can submit this order for approval."}
            </AlertDescription>
          </Alert>
        )}

        {po.status === "PENDING_APPROVAL" && (
          <Alert>
            <AlertDescription>
              This purchase order is pending approval. {hasApprovalPermission
                ? "You can approve or reject this order."
                : "Only super administrators can approve purchase orders."}
            </AlertDescription>
          </Alert>
        )}

        {po.status === "APPROVED" && (
          <Alert>
            <AlertDescription>
              This purchase order has been approved and is ready to be sent to the supplier. {canSendOrder
                ? "Click 'Send Order' to notify the supplier and begin the ordering process."
                : "Only warehouse administrators can send orders to suppliers."}
            </AlertDescription>
          </Alert>
        )}

        {po.status === "ORDERED" && (
          <Alert>
            <AlertDescription>
              This purchase order has been sent to the supplier and is awaiting delivery. {canReceiveItems
                ? "Use 'Receive Items' for detailed receiving with batch tracking, or 'Mark as Received' to receive all items at once."
                : "Only warehouse administrators can process receiving."}
            </AlertDescription>
          </Alert>
        )}

        {po.status === "PARTIALLY_RECEIVED" && (
          <Alert>
            <AlertDescription>
              This purchase order has been partially received. {canReceiveItems
                ? "Continue receiving remaining items using 'Receive Items'."
                : "Contact warehouse administrators to complete receiving."}
            </AlertDescription>
          </Alert>
        )}

        {po.status === "RECEIVED" && (
          <Alert>
            <AlertDescription>
              This purchase order has been fully received and completed. All items have been added to inventory.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-3 gap-6">
          {/* Left Column - PO Details */}
          <div className="col-span-2 space-y-6">
            {/* Supplier Info */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Supplier Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Supplier Name</span>
                      <p className="text-lg font-semibold">{po.supplier.name}</p>
                    </div>
                    {po.supplier.contactPerson && (
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Contact Person</span>
                        <p className="flex items-center gap-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          {po.supplier.contactPerson}
                        </p>
                      </div>
                    )}
                  </div>
                  <div className="space-y-3">
                    {po.supplier.phone && (
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Phone</span>
                        <p className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          {po.supplier.phone}
                        </p>
                      </div>
                    )}
                    {po.supplier.email && (
                      <div>
                        <span className="text-sm font-medium text-muted-foreground">Email</span>
                        <p className="flex items-center gap-2">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          {po.supplier.email}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Items */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Order Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="text-left border-b">
                        <th className="pb-2">Product Information</th>
                        <th className="pb-2">Supplier Details</th>
                        <th className="pb-2">Quantity</th>
                        <th className="pb-2">Unit Price</th>
                        <th className="pb-2 text-right">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {po.items.map((item) => (
                        <tr key={item.id} className="border-b last:border-0">
                          <td className="py-3">
                            <div className="space-y-1">
                              <p className="font-medium">{item.product.name}</p>
                              <p className="text-sm text-muted-foreground flex items-center gap-1">
                                <Tag className="h-3 w-3" />
                                SKU: {item.product.sku}
                              </p>
                            </div>
                          </td>
                          <td className="py-3">
                            <div className="space-y-1">
                              {item.productSupplier?.supplierProductCode && (
                                <p className="text-sm font-medium text-blue-600">
                                  Code: {item.productSupplier.supplierProductCode}
                                </p>
                              )}
                              {item.productSupplier?.supplierProductName && (
                                <p className="text-sm text-muted-foreground">
                                  Name: {item.productSupplier.supplierProductName}
                                </p>
                              )}
                              {!item.productSupplier?.supplierProductCode && !item.productSupplier?.supplierProductName && (
                                <p className="text-sm text-muted-foreground">No supplier details</p>
                              )}
                            </div>
                          </td>
                          <td className="py-3">{item.quantity}</td>
                          <td className="py-3">
                            {new Intl.NumberFormat("id-ID", {
                              style: "currency",
                              currency: "IDR",
                            }).format(item.unitPrice)}
                          </td>
                          <td className="py-3 text-right">
                            {new Intl.NumberFormat("id-ID", {
                              style: "currency",
                              currency: "IDR",
                            }).format(item.quantity * item.unitPrice)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot>
                      <tr className="font-medium">
                        <td colSpan={4} className="pt-4">
                          Total
                        </td>
                        <td className="pt-4 text-right">
                          {new Intl.NumberFormat("id-ID", {
                            style: "currency",
                            currency: "IDR",
                          }).format(
                            po.items.reduce(
                              (total: number, item: PurchaseOrderItem) =>
                                total + item.quantity * item.unitPrice,
                              0
                            )
                          )}
                        </td>
                      </tr>
                    </tfoot>
                  </table>
                </div>
              </CardContent>
            </Card>

            {/* Invoices Section */}
            {(po.status === "RECEIVED" || po.status === "PARTIALLY_RECEIVED") && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg font-semibold flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      Invoices
                    </CardTitle>
                    {(user?.role === "SUPER_ADMIN" || user?.role === "FINANCE_ADMIN" || user?.role === "WAREHOUSE_ADMIN") && (
                      <Button size="sm" asChild>
                        <Link href={`/invoices/new?po=${po.id}`}>
                          <Plus className="h-4 w-4 mr-2" />
                          Create Invoice
                        </Link>
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {po.invoices && po.invoices.length > 0 ? (
                    <div className="space-y-3">
                      {po.invoices.map((invoice) => (
                        <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="space-y-1">
                            <Link
                              href={`/invoices/${invoice.id}`}
                              className="font-medium text-blue-600 hover:underline"
                            >
                              {invoice.invoiceNumber}
                            </Link>
                            <p className="text-sm text-muted-foreground">
                              Created on {format(new Date(invoice.createdAt), "MMM d, yyyy")}
                            </p>
                          </div>
                          <div className="text-right space-y-1">
                            <Badge variant={invoice.status === "APPROVED" ? "default" : "secondary"}>
                              {invoice.status}
                            </Badge>
                            <p className="text-sm font-medium">
                              {new Intl.NumberFormat("id-ID", {
                                style: "currency",
                                currency: "IDR",
                              }).format(invoice.total)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-6">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground mb-4">No invoices created yet</p>
                      {(user?.role === "SUPER_ADMIN" || user?.role === "FINANCE_ADMIN" || user?.role === "WAREHOUSE_ADMIN") && (
                        <Button asChild>
                          <Link href={`/invoices/new?po=${po.id}`}>
                            <Plus className="h-4 w-4 mr-2" />
                            Create First Invoice
                          </Link>
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Status History */}
            <POStatusHistoryCard history={history} />
          </div>

          {/* Right Column - Performance Metrics */}
          <div className="space-y-6">
            <POPerformanceCard
              performanceScore={po.performanceData?.performanceScore ?? null}
              qualityScore={po.performanceData?.qualityScore ?? null}
              supplierScore={po.performanceData?.supplierScore ?? null}
              hasDelays={po.performanceData?.hasDelays ?? false}
              delayReason={po.performanceData?.delayReason}
              lastTransitionAt={po.lastTransitionAt ? new Date(po.lastTransitionAt) : null}
              timeInCurrentStatus={po.performanceData?.timeInStatus ?? null}
              batchMetrics={
                po.performanceData?.batchMetrics ?? {
                  totalBatches: 0,
                  activeBatches: 0,
                  expiringBatches: 0,
                  expiredBatches: 0,
                }
              }
            />

            {/* Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-semibold">Notes</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{po.notes || "No notes available"}</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
