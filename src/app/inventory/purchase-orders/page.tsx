"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Pagination } from "@/components/custom/pagination";
import { POStatusBadge } from "@/components/purchase-orders/POStatusBadge";
import { POStatusTransition } from "@/components/purchase-orders/POStatusTransition";
import { Loader2, Plus, Search, Eye, FileText, Filter, CalendarIcon, ArrowRight } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { POStatus, POPriority } from "@prisma/client";
import { getStatusFilterOptions, getPriorityFilterOptions } from "@/lib/po-status-management";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface PurchaseOrder {
  id: string;
  orderDate: string;
  status: POStatus;
  priority: POPriority;
  subtotal: number;
  tax: number;
  taxPercentage?: number;
  total: number;
  notes?: string;
  createdAt: string;
  expectedDeliveryDate?: string;
  actualDeliveryDate?: string;
  holdReason?: string;
  holdUntil?: string;
  supplier: {
    id: string;
    name: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  approvedBy?: {
    id: string;
    name: string;
    email: string;
  };
  items: Array<{
    id: string;
    quantity: number;
    receivedQuantity: number;
    unitPrice: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
    };
  }>;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface Supplier {
  id: string;
  name: string;
}

const statusColors = {
  DRAFT: "bg-gray-100 text-gray-800",
  PENDING_APPROVAL: "bg-yellow-100 text-yellow-800",
  APPROVED: "bg-blue-100 text-blue-800",
  ORDERED: "bg-purple-100 text-purple-800",
  PARTIALLY_RECEIVED: "bg-orange-100 text-orange-800",
  RECEIVED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

const statusLabels = {
  DRAFT: "Draft",
  PENDING_APPROVAL: "Pending Approval",
  APPROVED: "Approved",
  ORDERED: "Ordered",
  PARTIALLY_RECEIVED: "Partially Received",
  RECEIVED: "Received",
  CANCELLED: "Cancelled",
};

export default function PurchaseOrdersPage() {
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  });

  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedSupplier, setSelectedSupplier] = useState<string>("all");
  const [selectedStatus, setSelectedStatus] = useState<string>("all");
  const [selectedPriority, setSelectedPriority] = useState<string>("all");
  const [dateFrom, setDateFrom] = useState<Date | undefined>(undefined);
  const [dateTo, setDateTo] = useState<Date | undefined>(undefined);

  // Fetch suppliers for filter (show all for filtering historical data)
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        const response = await fetch("/api/suppliers?showInactive=true");
        if (!response.ok) throw new Error("Failed to fetch suppliers");
        const data = await response.json();
        setSuppliers(data.suppliers || []);
      } catch (error) {
        console.error("Error fetching suppliers:", error);
      }
    };

    fetchSuppliers();
  }, []);

  // Fetch purchase orders
  const fetchPurchaseOrders = async (page = 1) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
      });

      if (searchTerm) params.append("search", searchTerm);
      if (selectedSupplier !== "all") params.append("supplierId", selectedSupplier);
      if (selectedStatus !== "all") params.append("status", selectedStatus);
      if (selectedPriority !== "all") params.append("priority", selectedPriority);
      if (dateFrom) params.append("dateFrom", format(dateFrom, "yyyy-MM-dd"));
      if (dateTo) params.append("dateTo", format(dateTo, "yyyy-MM-dd"));

      const response = await fetch(`/api/purchase-orders?${params}`);
      if (!response.ok) throw new Error("Failed to fetch purchase orders");

      const data = await response.json();
      setPurchaseOrders(data.purchaseOrders || []);
      setPagination(data.pagination);
    } catch (error) {
      console.error("Error fetching purchase orders:", error);
      setError("Failed to load purchase orders");
      toast.error("Failed to load purchase orders");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPurchaseOrders(1);
  }, [searchTerm, selectedSupplier, selectedStatus, selectedPriority, dateFrom, dateTo]);

  const handlePageChange = (page: number) => {
    fetchPurchaseOrders(page);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchPurchaseOrders(1);
  };

  return (
    <MainLayout>
      <PageHeader
        title="Purchase Orders"
        description="Manage purchase orders and supplier orders"
        actions={
          <Button asChild>
            <Link href="/inventory/purchase-orders/new">
              <Plus className="h-4 w-4 mr-2" />
              Create Purchase Order
            </Link>
          </Button>
        }
      />

      {/* Filters */}
      <div className="mb-6 space-y-4">
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="flex-1">
            <Input
              placeholder="Search purchase orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <Button type="submit" variant="outline">
            <Search className="h-4 w-4 mr-2" />
            Search
          </Button>
        </form>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
            <SelectTrigger>
              <SelectValue placeholder="All Suppliers" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {suppliers.map((supplier) => (
                <SelectItem key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedStatus} onValueChange={setSelectedStatus}>
            <SelectTrigger>
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {getStatusFilterOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedPriority} onValueChange={setSelectedPriority}>
            <SelectTrigger>
              <SelectValue placeholder="All Priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              {getPriorityFilterOptions().map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>From Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dateFrom && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateFrom ? format(dateFrom, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dateFrom}
                  onSelect={setDateFrom}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label>To Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !dateTo && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {dateTo ? format(dateTo, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={dateTo}
                  onSelect={setDateTo}
                  initialFocus
                  disabled={(date) => dateFrom ? date < dateFrom : false}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading purchase orders...</span>
        </div>
      ) : purchaseOrders.length === 0 ? (
        <div className="text-center p-12 border rounded-lg bg-muted/20">
          <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No purchase orders found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedSupplier !== "all" || selectedStatus !== "all"
              ? "Try adjusting your filters or search terms."
              : "Get started by creating your first purchase order."}
          </p>
          <Button asChild>
            <Link href="/inventory/purchase-orders/new">
              <Plus className="h-4 w-4 mr-2" />
              Create Purchase Order
            </Link>
          </Button>
        </div>
      ) : (
        <>
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>PO Number</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Order Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Priority</TableHead>
                  <TableHead>Items</TableHead>
                  <TableHead>Total</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {purchaseOrders.map((po) => {
                  const isOverdue = po.expectedDeliveryDate &&
                    new Date(po.expectedDeliveryDate) < new Date() &&
                    !['RECEIVED', 'CANCELLED'].includes(po.status);

                  const receivedItems = po.items.reduce((sum, item) => sum + (item.receivedQuantity || 0), 0);
                  const totalItems = po.items.reduce((sum, item) => sum + item.quantity, 0);

                  return (
                    <TableRow key={po.id} className={isOverdue ? "bg-red-50" : ""}>
                      <TableCell className="font-medium">{po.id.slice(-8).toUpperCase()}</TableCell>
                      <TableCell>{po.supplier.name}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div>{new Date(po.orderDate).toLocaleDateString()}</div>
                          {po.expectedDeliveryDate && (
                            <div className="text-xs text-muted-foreground">
                              Expected: {new Date(po.expectedDeliveryDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <POStatusBadge
                            status={po.status}
                            priority={po.priority}
                            showIcon={true}
                            size="sm"
                          />
                          {po.status === "DRAFT" && po.items.length === 0 && (
                            <Badge variant="outline" className="text-xs">
                              Incomplete
                            </Badge>
                          )}
                          {isOverdue && (
                            <Badge variant="destructive" className="text-xs">
                              Overdue
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <POStatusBadge
                          status={po.status}
                          priority={po.priority}
                          showIcon={false}
                          size="sm"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div>{po.items.length} items</div>
                          {po.status === 'PARTIALLY_RECEIVED' && (
                            <div className="text-xs text-muted-foreground">
                              {receivedItems}/{totalItems} received
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>Rp {Number(po.total).toLocaleString("id-ID")}</TableCell>
                      <TableCell>{po.createdBy.name}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center gap-2 justify-end">
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/inventory/purchase-orders/${po.id}`}>
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Link>
                          </Button>
                          <POStatusTransition
                            purchaseOrderId={po.id}
                            currentStatus={po.status}
                            userRole="SUPER_ADMIN" // This should come from auth context
                            onStatusChanged={() => fetchPurchaseOrders(pagination.page)}
                            trigger={
                              <Button variant="ghost" size="sm">
                                <ArrowRight className="h-4 w-4" />
                              </Button>
                            }
                          />
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {pagination.pages > 1 && (
            <div className="mt-6">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}
    </MainLayout>
  );
}
