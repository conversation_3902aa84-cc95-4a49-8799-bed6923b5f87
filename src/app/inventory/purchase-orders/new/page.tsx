"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { ArrowLeft, Plus, Trash2, Loader2, Package, CalendarIcon } from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import SupplierRecommendations from "@/components/purchase-orders/SupplierRecommendations";
import { z } from "zod";
import { formatCurrency, cn } from "@/lib/utils";
import { format } from "date-fns";

interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  basePrice?: number;
  purchasePrice?: number; // Fallback for legacy compatibility
  category?: {
    id: string;
    name: string;
  };
  unit?: {
    id: string;
    name: string;
    abbreviation: string;
  };
  // Supplier-specific information
  supplierInfo?: {
    id: string; // ProductSupplier relationship ID
    productId: string;
    supplierId: string;
    supplierProductCode?: string | null;
    supplierProductName?: string | null;
    purchasePrice: number;
    minimumOrderQuantity?: number | null;
    leadTimeDays?: number | null;
    isPreferred: boolean;
    isActive: boolean;
    notes?: string | null;
    createdAt: string;
    updatedAt: string;
  };
}

interface POItem {
  productId: string;
  quantity: number;
  unitPrice: number;
  subtotal: number;
  product?: Product;
  productSupplierId?: string; // Reference to ProductSupplier relationship
}

const purchaseOrderSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  taxPercentage: z.coerce
    .number()
    .min(0, { message: "Tax percentage must be non-negative" })
    .max(100, { message: "Tax percentage cannot exceed 100%" })
    .default(0),
  expectedDeliveryDate: z.date().optional(),
  notes: z.string().optional(),
});

type PurchaseOrderFormValues = z.infer<typeof purchaseOrderSchema>;

export default function NewPurchaseOrderPage() {
  const router = useRouter();
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [items, setItems] = useState<POItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [selectedSupplierId, setSelectedSupplierId] = useState<string>("");
  const [totals, setTotals] = useState({ subtotal: 0, taxAmount: 0, total: 0 });

  const form = useForm<PurchaseOrderFormValues>({
    resolver: zodResolver(purchaseOrderSchema),
    defaultValues: {
      supplierId: "",
      taxPercentage: 0,
      expectedDeliveryDate: undefined,
      notes: "",
    },
  });

  // Fetch suppliers on component mount (active only)
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        const suppliersRes = await fetch("/api/suppliers?activeOnly=true");

        if (!suppliersRes.ok) {
          throw new Error("Failed to fetch suppliers");
        }

        const suppliersData = await suppliersRes.json();
        setSuppliers(suppliersData.suppliers || []);
      } catch (error) {
        console.error("Error fetching suppliers:", error);
        toast.error("Failed to load suppliers");
      }
    };

    fetchSuppliers();
  }, []);

  // Fetch products when supplier changes
  useEffect(() => {
    const fetchSupplierProducts = async () => {
      if (!selectedSupplierId) {
        setProducts([]);
        return;
      }

      setLoadingProducts(true);
      try {
        const response = await fetch(`/api/suppliers/${selectedSupplierId}/products?active=true`);

        if (!response.ok) {
          throw new Error("Failed to fetch supplier products");
        }

        const data = await response.json();
        console.log('Fetched supplier products:', data.products);
        setProducts(data.products || []);
      } catch (error) {
        console.error("Error fetching supplier products:", error);
        toast.error("Failed to load products for selected supplier");
        setProducts([]);
      } finally {
        setLoadingProducts(false);
      }
    };

    fetchSupplierProducts();
  }, [selectedSupplierId]);

  // Recalculate totals when items or tax percentage changes
  useEffect(() => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);
    const taxPercentage = form.watch("taxPercentage") || 0;
    const taxAmount = subtotal * (taxPercentage / 100);
    const total = subtotal + taxAmount;
    setTotals({ subtotal, taxAmount, total });
  }, [items, form.watch("taxPercentage")]);

  // Handle supplier change
  const handleSupplierChange = (supplierId: string) => {
    setSelectedSupplierId(supplierId);
    form.setValue("supplierId", supplierId);

    // Clear existing items when supplier changes
    if (items.length > 0) {
      setItems([]);
      toast.info("Items cleared due to supplier change. Please re-add products for the new supplier.");
    }
  };

  // Handle supplier selection from recommendations
  const handleSupplierSelect = (supplierId: string, supplierName: string) => {
    handleSupplierChange(supplierId);
    toast.success(`Selected supplier: ${supplierName}`);
  };

  // Handle applying recommendations
  const handleRecommendationApply = (recommendations: any[]) => {
    // Clear existing items
    setItems([]);

    // Add recommended items
    const newItems = recommendations.map(rec => {
      const supplier = rec.recommendedSupplier;
      return {
        productId: rec.productId,
        quantity: rec.requestedQuantity,
        unitPrice: supplier.purchasePrice,
        subtotal: rec.requestedQuantity * supplier.purchasePrice,
        productSupplierId: supplier.productSupplierId,
        product: { name: rec.productName } // Minimal product info for display
      };
    });

    setItems(newItems);
    toast.success(`Applied recommendations for ${recommendations.length} products`);
  };

  const addItem = () => {
    setItems([
      ...items,
      {
        productId: "",
        quantity: 1,
        unitPrice: 0,
        subtotal: 0,
      },
    ]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: keyof POItem, value: any) => {
    const updatedItems = [...items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // If product changed, update unit price and quantity with supplier-specific values
    if (field === "productId") {
      const product = products.find((p) => p.id === value);
      if (product) {
        updatedItems[index].product = product;

        // Use supplier-specific information if available
        if (product.supplierInfo) {
          updatedItems[index].unitPrice = Number(product.supplierInfo.purchasePrice);
          updatedItems[index].quantity = Number(product.supplierInfo.minimumOrderQuantity) || 1;
          updatedItems[index].productSupplierId = product.supplierInfo.id;
        } else {
          // Fallback to product's general purchase price
          updatedItems[index].unitPrice = product.purchasePrice || product.basePrice || 0;
          updatedItems[index].quantity = 1;
        }
      }
    }

    // Recalculate subtotal
    if (field === "quantity" || field === "unitPrice" || field === "productId") {
      updatedItems[index].subtotal = updatedItems[index].quantity * updatedItems[index].unitPrice;
    }

    setItems(updatedItems);
  };





  const onSubmit = async (data: PurchaseOrderFormValues) => {
    console.log("Form submitted with data:", data);
    console.log("Items:", items);

    if (items.length === 0) {
      toast.error("Please add at least one item");
      return;
    }

    // Validate all items have required fields
    const invalidItems = items.filter(
      (item) =>
        !item.productId ||
        !item.quantity ||
        item.quantity <= 0 ||
        !item.unitPrice ||
        item.unitPrice <= 0
    );

    console.log("Invalid items:", invalidItems);

    if (invalidItems.length > 0) {
      toast.error("Please fill in all item details with valid values");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const payload = {
        supplierId: data.supplierId,
        orderDate: new Date().toISOString().split("T")[0], // Always use current date
        tax: Number(totals.taxAmount), // Use state totals
        taxPercentage: Number(data.taxPercentage) || 0, // Also send percentage for reference
        expectedDeliveryDate: data.expectedDeliveryDate ? data.expectedDeliveryDate.toISOString() : undefined,
        notes: data.notes || "",
        items: items.map((item) => ({
          productId: item.productId,
          productSupplierId: item.productSupplierId,
          quantity: Number(item.quantity), // Ensure quantity is a number
          unitPrice: Number(item.unitPrice), // Ensure unitPrice is a number
        })),
      };

      console.log("Payload being sent:", payload);

      const response = await fetch("/api/purchase-orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("API Error Response:", errorData);

        // Show detailed validation errors if available
        if (errorData.issues) {
          console.error("Validation Issues:", errorData.issues);
          const validationErrors = errorData.issues
            .map((issue: any) => `${issue.path.join(".")}: ${issue.message}`)
            .join(", ");
          throw new Error(`Validation failed: ${validationErrors}`);
        }

        throw new Error(errorData.error || "Failed to create purchase order");
      }

      const result = await response.json();
      toast.success("Purchase order created successfully");
      router.push(`/inventory/purchase-orders/${result.id}`);
    } catch (error) {
      console.error("Error creating purchase order:", error);
      setError(error instanceof Error ? error.message : "Failed to create purchase order");
      toast.error("Failed to create purchase order");
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Create Purchase Order"
        description="Create a new purchase order for supplier"
        actions={
          <Button variant="outline" asChild>
            <Link href="/inventory/purchase-orders">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Purchase Orders
            </Link>
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Purchase Order Details</CardTitle>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
                id="purchase-order-form"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormField
                    control={form.control}
                    name="supplierId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier</FormLabel>
                        <Select onValueChange={handleSupplierChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select supplier" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {suppliers.map((supplier) => (
                              <SelectItem key={supplier.id} value={supplier.id}>
                                {supplier.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxPercentage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax Percentage (%)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="0"
                            min="0"
                            max="100"
                            step="0.1"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e.target.valueAsNumber || 0);
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="expectedDeliveryDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Expected Delivery Date (Optional)</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP")
                                ) : (
                                  <span>Pick a delivery date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date < new Date()
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes</FormLabel>
                      <FormControl>
                        <Textarea placeholder="Additional notes or comments..." {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Items Section */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Items</CardTitle>
              <Button
                onClick={addItem}
                variant="outline"
                disabled={!selectedSupplierId || loadingProducts}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {items.length === 0 ? (
              <div className="text-center p-8 border-2 border-dashed rounded-lg">
                <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No items added</h3>
                <p className="text-muted-foreground mb-4">Add products to this purchase order</p>
                <Button
                  onClick={addItem}
                  disabled={!selectedSupplierId || loadingProducts}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Item
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Subtotal</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {items.map((item, index) => (
                      <TableRow key={index}>
                        <TableCell>
                          <Select
                            value={item.productId}
                            onValueChange={(value) => updateItem(index, "productId", value)}
                            disabled={!selectedSupplierId || loadingProducts}
                          >
                            <SelectTrigger>
                              <SelectValue
                                placeholder={
                                  !selectedSupplierId
                                    ? "Select supplier first"
                                    : loadingProducts
                                    ? "Loading products..."
                                    : "Select product"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {products.length === 0 && selectedSupplierId && !loadingProducts ? (
                                <div className="p-4 text-center text-muted-foreground">
                                  <p className="text-sm">No products available for this supplier.</p>
                                  <p className="text-xs mt-1">
                                    Please add products to this supplier first in the{" "}
                                    <Link
                                      href="/inventory/products"
                                      className="text-primary hover:underline"
                                    >
                                      Products
                                    </Link>{" "}
                                    section.
                                  </p>
                                </div>
                              ) : (
                                products.map((product) => (
                                  <SelectItem key={product.id} value={product.id}>
                                    <div className="flex flex-col">
                                      <div className="flex items-center gap-2">
                                        <span className="font-medium">{product.name}</span>
                                        <span className="text-xs text-muted-foreground">({product.sku})</span>
                                        {selectedSupplierId && (
                                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">
                                            {suppliers.find(s => s.id === selectedSupplierId)?.name}
                                          </span>
                                        )}
                                      </div>
                                      {product.supplierInfo && (
                                        <div className="text-xs text-muted-foreground mt-1">
                                          <span className="font-medium">Price: {formatCurrency(Number(product.supplierInfo.purchasePrice))}</span>
                                          {product.supplierInfo.minimumOrderQuantity &&
                                            ` • MOQ: ${product.supplierInfo.minimumOrderQuantity}`
                                          }
                                          {product.supplierInfo.isPreferred && (
                                            <span className="text-green-600 font-medium"> • Preferred</span>
                                          )}
                                          {product.supplierInfo.supplierProductCode && (
                                            <span className="text-blue-600"> • Code: {product.supplierInfo.supplierProductCode}</span>
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) =>
                              updateItem(index, "quantity", parseInt(e.target.value) || 0)
                            }
                            className="w-20"
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) =>
                              updateItem(index, "unitPrice", parseFloat(e.target.value) || 0)
                            }
                            className="w-32"
                          />
                        </TableCell>
                        <TableCell>Rp {item.subtotal.toLocaleString("id-ID")}</TableCell>
                        <TableCell>
                          <Button variant="ghost" size="sm" onClick={() => removeItem(index)}>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals */}
                <div className="flex justify-end">
                  <div className="w-64 space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>Rp {totals.subtotal.toLocaleString("id-ID")}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Tax ({form.watch("taxPercentage") || 0}%):</span>
                      <span>Rp {totals.taxAmount.toLocaleString("id-ID")}</span>
                    </div>
                    <div className="flex justify-between font-bold border-t pt-2">
                      <span>Total:</span>
                      <span>Rp {totals.total.toLocaleString("id-ID")}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Supplier Recommendations */}
        {items.length > 0 && (
          <SupplierRecommendations
            items={items
              .filter(item => item.productId && item.quantity > 0)
              .map(item => ({
                productId: item.productId,
                quantity: item.quantity
              }))}
            onSupplierSelect={handleSupplierSelect}
            onRecommendationApply={handleRecommendationApply}
          />
        )}

        {/* Actions */}
        <div className="flex justify-end gap-4">
          <Button variant="outline" asChild>
            <Link href="/inventory/purchase-orders">Cancel</Link>
          </Button>
          <Button type="submit" form="purchase-order-form" disabled={loading || items.length === 0}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Create Purchase Order
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
