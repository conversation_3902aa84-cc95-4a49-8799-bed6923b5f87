"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { toast } from "sonner";
import { format } from "date-fns";
import { useDebounce } from "@/hooks/useDebounce";
import Link from "next/link";
import {
  Loader2,
  FileText,
  Download,
  RefreshCw,
  Calendar as CalendarIcon,
  DollarSign,
  Package,
  AlertTriangle,
  Search,
} from "lucide-react";

interface Supplier {
  id: string;
  name: string;
}

interface ReportFilters {
  poNumber: string;
  supplierId: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
  minAmount: string;
  maxAmount: string;
}

interface PurchaseOrder {
  id: string;
  orderDate: string;
  receivedAt?: string;
  supplier: {
    id: string;
    name: string;
    contactPerson?: string;
    phone?: string;
  };
  status: string;
  total: number;
  itemCount: number;
  createdBy: {
    name: string;
  };
  approvedBy?: {
    name: string;
  };
  daysPending?: number;
  daysOverdue?: number;
  severity?: string;
  fulfillmentTime?: number;
}

interface ReportData {
  reportType: string;
  generatedAt: string;
  summary: any;
  purchaseOrders: PurchaseOrder[];
}

export default function PurchaseOrderReportsPage() {
  const [activeTab, setActiveTab] = useState("pending");
  const [isLoading, setIsLoading] = useState(false);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<ReportFilters>({
    poNumber: "",
    supplierId: "all",
    startDate: undefined,
    endDate: undefined,
    minAmount: "",
    maxAmount: "",
  });

  // Debounced values for text inputs to prevent excessive API calls
  const debouncedPoNumber = useDebounce(filters.poNumber, 300);
  const debouncedMinAmount = useDebounce(filters.minAmount, 300);
  const debouncedMaxAmount = useDebounce(filters.maxAmount, 300);

  // Fetch suppliers for filter dropdown
  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
    }
  };

  // Fetch report data based on active tab and filters
  const fetchReportData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append("type", activeTab);

      if (debouncedPoNumber) queryParams.append("poNumber", debouncedPoNumber);
      if (filters.supplierId && filters.supplierId !== "all")
        queryParams.append("supplierId", filters.supplierId);
      if (filters.startDate)
        queryParams.append("startDate", format(filters.startDate, "yyyy-MM-dd"));
      if (filters.endDate) queryParams.append("endDate", format(filters.endDate, "yyyy-MM-dd"));
      if (debouncedMinAmount) queryParams.append("minAmount", debouncedMinAmount);
      if (debouncedMaxAmount) queryParams.append("maxAmount", debouncedMaxAmount);

      // Fetch report data
      const response = await fetch(`/api/purchase-orders/reports?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Error fetching report: ${response.statusText}`);
      }

      const data = await response.json();
      setReportData(data);
    } catch (error) {
      console.error("Error fetching report:", error);
      setError("Failed to load report data");
      toast.error("Failed to load report data");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  useEffect(() => {
    fetchReportData();
  }, [activeTab]);

  // Track if this is the initial mount to avoid duplicate API calls
  const [isInitialMount, setIsInitialMount] = useState(true);

  // Auto-apply filters when debounced values or immediate filters change
  useEffect(() => {
    // Skip the very first effect run to avoid duplicate call with activeTab effect
    if (isInitialMount) {
      setIsInitialMount(false);
      return;
    }

    // Always fetch data when any filter changes, including when they're cleared
    fetchReportData();
  }, [debouncedPoNumber, debouncedMinAmount, debouncedMaxAmount, filters.supplierId, filters.startDate, filters.endDate]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
    setReportData(null);
  };

  // Handle filter change
  const handleFilterChange = (key: keyof ReportFilters, value: string | Date | undefined) => {
    setFilters({ ...filters, [key]: value });
  };

  // Handle manual refresh
  const handleRefresh = () => {
    toast.info("Refreshing report data...");
    fetchReportData();
  };

  // Handle export
  const handleExport = (format: string) => {
    const queryParams = new URLSearchParams();
    queryParams.append("type", activeTab);
    queryParams.append("format", format);

    if (debouncedPoNumber) queryParams.append("poNumber", debouncedPoNumber);
    if (filters.supplierId && filters.supplierId !== "all")
      queryParams.append("supplierId", filters.supplierId);
    if (filters.startDate) queryParams.append("startDate", format(filters.startDate, "yyyy-MM-dd"));
    if (filters.endDate) queryParams.append("endDate", format(filters.endDate, "yyyy-MM-dd"));
    if (debouncedMinAmount) queryParams.append("minAmount", debouncedMinAmount);
    if (debouncedMaxAmount) queryParams.append("maxAmount", debouncedMaxAmount);

    window.open(`/api/purchase-orders/reports/export?${queryParams.toString()}`, "_blank");
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("id-ID");
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case "DRAFT":
        return "secondary";
      case "PENDING_APPROVAL":
        return "outline";
      case "APPROVED":
        return "default";
      case "ORDERED":
        return "default";
      case "PARTIALLY_RECEIVED":
        return "outline";
      case "RECEIVED":
        return "default";
      case "CANCELLED":
        return "destructive";
      default:
        return "secondary";
    }
  };

  const getSeverityBadgeVariant = (severity: string) => {
    switch (severity) {
      case "mild":
        return "outline";
      case "moderate":
        return "secondary";
      case "severe":
        return "destructive";
      default:
        return "secondary";
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Purchase Order Reports"
        description="View and export purchase order reports"
        actions={
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="mr-2 h-4 w-4" />
              )}
              Refresh
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport("xlsx")}
              disabled={isLoading || !reportData}
            >
              <Download className="mr-2 h-4 w-4" />
              Export Excel
            </Button>
            <Button
              variant="outline"
              onClick={() => handleExport("csv")}
              disabled={isLoading || !reportData}
            >
              <Download className="mr-2 h-4 w-4" />
              Export CSV
            </Button>
          </div>
        }
      />

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter reports by PO number, supplier, date range, and amount</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* First row: PO Number and Supplier */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>PO Number</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search by PO number..."
                    value={filters.poNumber}
                    onChange={(e) => handleFilterChange("poNumber", e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Supplier</Label>
                <Select
                  value={filters.supplierId}
                  onValueChange={(value) => handleFilterChange("supplierId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Suppliers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Suppliers</SelectItem>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Second row: Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.startDate ? (
                        format(filters.startDate, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filters.startDate}
                      onSelect={(date) => handleFilterChange("startDate", date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-full justify-start text-left font-normal">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.endDate ? format(filters.endDate, "PPP") : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={filters.endDate}
                      onSelect={(date) => handleFilterChange("endDate", date)}
                      initialFocus
                      disabled={(date) => (filters.startDate ? date < filters.startDate : false)}
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Third row: Amount Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Min Amount</Label>
                <Input
                  type="number"
                  placeholder="0"
                  value={filters.minAmount}
                  onChange={(e) => handleFilterChange("minAmount", e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label>Max Amount</Label>
                <Input
                  type="number"
                  placeholder="No limit"
                  value={filters.maxAmount}
                  onChange={(e) => handleFilterChange("maxAmount", e.target.value)}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="pending" value={activeTab} onValueChange={handleTabChange}>
        <div className="flex justify-between items-center mb-4">
          <TabsList>
            <TabsTrigger value="pending">Pending POs</TabsTrigger>
            <TabsTrigger value="received">Received POs</TabsTrigger>
            <TabsTrigger value="overdue">Overdue POs</TabsTrigger>
          </TabsList>
        </div>

        {/* Summary Cards */}
        {reportData && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Package className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Total POs</p>
                    <p className="text-2xl font-bold">{reportData.summary.totalPOs}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <DollarSign className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Total Value</p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(reportData.summary.totalValue)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CalendarIcon className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Average Value</p>
                    <p className="text-2xl font-bold">
                      {formatCurrency(reportData.summary.avgValue)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {activeTab === "overdue" && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <AlertTriangle className="h-8 w-8 text-red-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-muted-foreground">Avg Days Overdue</p>
                      <p className="text-2xl font-bold">
                        {reportData.summary.avgDaysOverdue?.toFixed(1) || "0"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {activeTab === "received" && (
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <CalendarIcon className="h-8 w-8 text-green-600" />
                    <div className="ml-4">
                      <p className="text-sm font-medium text-muted-foreground">Fulfillment Rate</p>
                      <p className="text-2xl font-bold">
                        {reportData.summary.fulfillmentRate?.toFixed(1) || "0"}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <div className="mt-4">
          <TabsContent value="pending">
            {isLoading ? (
              <div className="flex justify-center items-center p-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading pending POs...</span>
              </div>
            ) : error ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-red-600">
                    <p>{error}</p>
                    <Button onClick={fetchReportData} className="mt-2">
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : reportData ? (
              <Card>
                <CardHeader>
                  <CardTitle>Pending Purchase Orders</CardTitle>
                  <CardDescription>
                    Purchase orders awaiting approval, ordering, or delivery
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {reportData.purchaseOrders.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4" />
                      <p>No pending purchase orders found</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>PO Number</TableHead>
                          <TableHead>Order Date</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Total</TableHead>
                          <TableHead>Items</TableHead>
                          <TableHead>Days Pending</TableHead>
                          <TableHead>Created By</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {reportData.purchaseOrders.map((po) => (
                          <TableRow key={po.id}>
                            <TableCell className="font-medium">
                              <Link
                                href={`/inventory/purchase-orders/${po.id}`}
                                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                              >
                                {po.id.slice(-8).toUpperCase()}
                              </Link>
                            </TableCell>
                            <TableCell>{formatDate(po.orderDate)}</TableCell>
                            <TableCell>{po.supplier.name}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(po.status)}>
                                {po.status.replace("_", " ")}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatCurrency(po.total)}</TableCell>
                            <TableCell>{po.itemCount}</TableCell>
                            <TableCell>{po.daysPending} days</TableCell>
                            <TableCell>{po.createdBy.name}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            ) : null}
          </TabsContent>

          <TabsContent value="received">
            {isLoading ? (
              <div className="flex justify-center items-center p-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading received POs...</span>
              </div>
            ) : error ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-red-600">
                    <p>{error}</p>
                    <Button onClick={fetchReportData} className="mt-2">
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : reportData ? (
              <Card>
                <CardHeader>
                  <CardTitle>Received Purchase Orders</CardTitle>
                  <CardDescription>
                    Purchase orders that have been completed or partially received
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {reportData.purchaseOrders.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4" />
                      <p>No received purchase orders found</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>PO Number</TableHead>
                          <TableHead>Order Date</TableHead>
                          <TableHead>Received Date</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Total</TableHead>
                          <TableHead>Items</TableHead>
                          <TableHead>Fulfillment Time</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {reportData.purchaseOrders.map((po) => (
                          <TableRow key={po.id}>
                            <TableCell className="font-medium">
                              <Link
                                href={`/inventory/purchase-orders/${po.id}`}
                                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                              >
                                {po.id.slice(-8).toUpperCase()}
                              </Link>
                            </TableCell>
                            <TableCell>{formatDate(po.orderDate)}</TableCell>
                            <TableCell>
                              {po.receivedAt ? formatDate(po.receivedAt) : "N/A"}
                            </TableCell>
                            <TableCell>{po.supplier.name}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(po.status)}>
                                {po.status.replace("_", " ")}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatCurrency(po.total)}</TableCell>
                            <TableCell>{po.itemCount}</TableCell>
                            <TableCell>
                              {po.fulfillmentTime ? `${po.fulfillmentTime} days` : "N/A"}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            ) : null}
          </TabsContent>

          <TabsContent value="overdue">
            {isLoading ? (
              <div className="flex justify-center items-center p-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <span className="ml-2">Loading overdue POs...</span>
              </div>
            ) : error ? (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center text-red-600">
                    <p>{error}</p>
                    <Button onClick={fetchReportData} className="mt-2">
                      Try Again
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : reportData ? (
              <Card>
                <CardHeader>
                  <CardTitle>Overdue Purchase Orders</CardTitle>
                  <CardDescription>
                    Purchase orders that are past their expected delivery dates
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {reportData.purchaseOrders.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-12 w-12 mx-auto mb-4" />
                      <p>No overdue purchase orders found</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>PO Number</TableHead>
                          <TableHead>Order Date</TableHead>
                          <TableHead>Supplier</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Total</TableHead>
                          <TableHead>Items</TableHead>
                          <TableHead>Days Overdue</TableHead>
                          <TableHead>Severity</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {reportData.purchaseOrders.map((po) => (
                          <TableRow key={po.id}>
                            <TableCell className="font-medium">
                              <Link
                                href={`/inventory/purchase-orders/${po.id}`}
                                className="text-blue-600 hover:text-blue-800 hover:underline transition-colors"
                              >
                                {po.id.slice(-8).toUpperCase()}
                              </Link>
                            </TableCell>
                            <TableCell>{formatDate(po.orderDate)}</TableCell>
                            <TableCell>{po.supplier.name}</TableCell>
                            <TableCell>
                              <Badge variant={getStatusBadgeVariant(po.status)}>
                                {po.status.replace("_", " ")}
                              </Badge>
                            </TableCell>
                            <TableCell>{formatCurrency(po.total)}</TableCell>
                            <TableCell>{po.itemCount}</TableCell>
                            <TableCell>{po.daysOverdue} days</TableCell>
                            <TableCell>
                              <Badge variant={getSeverityBadgeVariant(po.severity || "mild")}>
                                {(po.severity || "mild").toUpperCase()}
                              </Badge>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            ) : null}
          </TabsContent>
        </div>
      </Tabs>
    </MainLayout>
  );
}
