"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Loader2,
  Plus,
  Edit,
  Trash,
  Phone,
  Mail,
  User,
  AlertTriangle,
  Info,
  Eye,
  Package,
  FileText,
  TrendingUp,
  Power
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Pagination } from "@/components/custom/pagination";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";

interface Supplier {
  id: string;
  name: string;
  contactPerson: string | null;
  phone: string | null;
  email: string | null;
  address: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    productSuppliers: number;
    purchaseOrders: number;
  };
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Supplier form schema
const supplierSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: "Invalid email address",
    }),
  address: z.string().optional(),
  isActive: z.boolean().default(true),
});

type SupplierFormValues = z.infer<typeof supplierSchema>;

// Helper function to determine if supplier can be deleted (has no linked data)
const canDeleteSupplier = (supplier: Supplier): boolean => {
  return (supplier._count?.productSuppliers || 0) === 0 &&
         (supplier._count?.purchaseOrders || 0) === 0;
};

export default function SuppliersPage() {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState<Supplier | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [supplierToDelete, setSupplierToDelete] = useState<Supplier | null>(null);
  const [supplierToToggle, setSupplierToToggle] = useState<Supplier | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isToggleStatusDialogOpen, setIsToggleStatusDialogOpen] = useState(false);
  const [isErrorDialogOpen, setIsErrorDialogOpen] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [showInactive, setShowInactive] = useState(false);
  const [isTogglingStatus, setIsTogglingStatus] = useState(false);

  // Initialize form
  const form = useForm<SupplierFormValues>({
    resolver: zodResolver(supplierSchema),
    defaultValues: {
      name: "",
      contactPerson: "",
      phone: "",
      email: "",
      address: "",
      isActive: true,
    },
  });

  // Fetch suppliers
  const fetchSuppliers = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      queryParams.append("page", pagination.page.toString());
      queryParams.append("limit", pagination.limit.toString());
      if (searchTerm) queryParams.append("search", searchTerm);
      if (showInactive) queryParams.append("showInactive", "true");

      const response = await fetch(`/api/suppliers?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch suppliers");
      }

      const data = await response.json();
      setSuppliers(data.suppliers || []);

      // Handle case where pagination info is not provided
      if (data.pagination) {
        setPagination(data.pagination);
      } else {
        // Create default pagination based on suppliers length
        const totalItems = data.suppliers?.length || 0;
        setPagination({
          total: totalItems,
          page: 1,
          limit: 10,
          pages: Math.ceil(totalItems / 10) || 1,
        });
      }
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      setError("Failed to load suppliers. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch suppliers when pagination or filters change
  useEffect(() => {
    fetchSuppliers();
  }, [pagination.page, pagination.limit, showInactive]);

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    fetchSuppliers();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Open dialog for creating a new supplier
  const handleAddSupplier = () => {
    setEditingSupplier(null);
    form.reset({
      name: "",
      contactPerson: "",
      phone: "",
      email: "",
      address: "",
      isActive: true,
    });
    setIsDialogOpen(true);
  };

  // Open dialog for editing a supplier
  const handleEditSupplier = (supplier: Supplier) => {
    setEditingSupplier(supplier);
    form.reset({
      name: supplier.name,
      contactPerson: supplier.contactPerson || "",
      phone: supplier.phone || "",
      email: supplier.email || "",
      address: supplier.address || "",
      isActive: supplier.isActive,
    });
    setIsDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: SupplierFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      if (editingSupplier) {
        // Update existing supplier
        const response = await fetch(`/api/suppliers/${editingSupplier.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to update supplier");
        }
      } else {
        // Create new supplier
        const response = await fetch("/api/suppliers", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create supplier");
        }
      }

      // Refresh suppliers and close dialog
      await fetchSuppliers();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving supplier:", error);
      setError((error as Error).message || "Failed to save supplier. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (supplier: Supplier) => {
    setSupplierToDelete(supplier);
    setIsDeleteDialogOpen(true);
  };

  // Open toggle status confirmation dialog
  const openToggleStatusDialog = (supplier: Supplier) => {
    setSupplierToToggle(supplier);
    setIsToggleStatusDialogOpen(true);
  };

  // Handle supplier status toggle (activate/deactivate)
  const handleToggleSupplierStatus = async () => {
    if (!supplierToToggle) return;

    setIsTogglingStatus(true);
    setError(null);

    try {
      const newStatus = !supplierToToggle.isActive;
      const response = await fetch(`/api/suppliers/${supplierToToggle.id}/toggle-status`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          isActive: newStatus,
          reason: newStatus ? "Supplier reactivated" : "Supplier deactivated for safety"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update supplier status");
      }

      // Refresh suppliers and close dialog
      await fetchSuppliers();
      setIsToggleStatusDialogOpen(false);
    } catch (error) {
      console.error("Error toggling supplier status:", error);
      setError((error as Error).message || "Failed to update supplier status. Please try again.");
    } finally {
      setIsTogglingStatus(false);
    }
  };

  // Handle supplier deletion
  const handleDeleteSupplier = async () => {
    if (!supplierToDelete) return;

    setLoading(true);
    setError(null);
    setDeleteError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierToDelete.id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();

        // Check if this is a "cannot delete" error due to linked products
        if (
          response.status === 400 &&
          errorData.message &&
          errorData.message.includes("associated with products")
        ) {
          setDeleteError(errorData.message);
          setIsErrorDialogOpen(true);
          setIsDeleteDialogOpen(false);
          throw new Error(errorData.message);
        }

        throw new Error(errorData.error || "Failed to delete supplier");
      }

      // Refresh suppliers and close dialog
      await fetchSuppliers();
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Error deleting supplier:", error);
      if (!deleteError) {
        setError((error as Error).message || "Failed to delete supplier. Please try again.");
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Suppliers"
        description="Manage your product suppliers"
        actions={
          <Button onClick={handleAddSupplier}>
            <Plus className="h-4 w-4 mr-2" />
            Add Supplier
          </Button>
        }
      />

      <div className="mb-6 space-y-4">
        <form onSubmit={handleSearch} className="flex gap-2">
          <Input
            placeholder="Search suppliers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
          <Button type="submit">Search</Button>
        </form>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="showInactive"
            checked={showInactive}
            onChange={(e) => setShowInactive(e.target.checked)}
            className="rounded border-gray-300"
          />
          <label htmlFor="showInactive" className="text-sm font-medium">
            Show inactive suppliers
          </label>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading && suppliers.length === 0 ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading suppliers...</span>
        </div>
      ) : suppliers.length === 0 ? (
        <div className="text-center p-12 border rounded-lg bg-muted/20">
          <h3 className="text-lg font-medium mb-2">No suppliers found</h3>
          <p className="text-muted-foreground mb-4">Get started by adding your first supplier.</p>
          <Button onClick={handleAddSupplier}>
            <Plus className="h-4 w-4 mr-2" />
            Add Supplier
          </Button>
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Contact Info</TableHead>
                  <TableHead>Products</TableHead>
                  <TableHead>Purchase Orders</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="w-[120px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {suppliers.map((supplier) => (
                  <TableRow
                    key={supplier.id}
                    className={!supplier.isActive ? "opacity-60 bg-muted/20" : ""}
                  >
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{supplier.name}</div>
                        {supplier.contactPerson && (
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            <User className="h-3 w-3" />
                            {supplier.contactPerson}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        {supplier.phone && (
                          <div className="flex items-center gap-1 text-sm">
                            <Phone className="h-3 w-3" /> {supplier.phone}
                          </div>
                        )}
                        {supplier.email && (
                          <div className="flex items-center gap-1 text-sm">
                            <Mail className="h-3 w-3" /> {supplier.email}
                          </div>
                        )}
                        {!supplier.phone && !supplier.email && (
                          <span className="text-muted-foreground">-</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <span>{supplier._count?.productSuppliers || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <FileText className="h-4 w-4 text-muted-foreground" />
                        <span>{supplier._count?.purchaseOrders || 0}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={supplier.isActive ? "secondary" : "outline"}
                        className={supplier.isActive ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-600"}
                      >
                        {supplier.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          asChild
                          title="View Details"
                          className="h-8 w-8"
                        >
                          <Link href={`/inventory/suppliers/${supplier.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleEditSupplier(supplier)}
                          title="Edit Supplier"
                          className="h-8 w-8"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>

                        {/* Show delete button only for suppliers with no linked data */}
                        {canDeleteSupplier(supplier) ? (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => openDeleteDialog(supplier)}
                            title="Delete Supplier"
                            className="h-8 w-8 text-red-600 hover:text-red-700"
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        ) : (
                          /* Show deactivate/reactivate button for suppliers with linked data */
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => openToggleStatusDialog(supplier)}
                            title={supplier.isActive ? "Deactivate Supplier" : "Reactivate Supplier"}
                            className={`h-8 w-8 ${supplier.isActive ? "text-orange-600 hover:text-orange-700" : "text-green-600 hover:text-green-700"}`}
                          >
                            {supplier.isActive ? (
                              <AlertTriangle className="h-4 w-4" />
                            ) : (
                              <TrendingUp className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {pagination.pages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}

      {/* Supplier Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingSupplier ? "Edit Supplier" : "Add Supplier"}</DialogTitle>
            <DialogDescription>
              {editingSupplier
                ? "Update the supplier details below."
                : "Enter the details for the new supplier."}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Supplier Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter supplier name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="contactPerson"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Contact Person (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter contact person name"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter phone number"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email (Optional)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter email address"
                        {...field}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter address" {...field} value={field.value || ""} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Active Supplier</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Active suppliers are available for new purchase orders and product relationships
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the supplier "{supplierToDelete?.name}". This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSupplier}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Toggle Status Confirmation Dialog */}
      <AlertDialog open={isToggleStatusDialogOpen} onOpenChange={setIsToggleStatusDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Power className="h-5 w-5" />
              {supplierToToggle?.isActive ? "Deactivate" : "Reactivate"} Supplier
            </AlertDialogTitle>
            <AlertDialogDescription>
              {supplierToToggle?.isActive ? (
                <>
                  This will deactivate the supplier "{supplierToToggle?.name}". The supplier will be hidden
                  from new selections but will remain visible in historical data and existing relationships.
                </>
              ) : (
                <>
                  This will reactivate the supplier "{supplierToToggle?.name}". The supplier will be
                  available for new selections and operations.
                </>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleToggleSupplierStatus}
              disabled={isTogglingStatus}
              className={supplierToToggle?.isActive
                ? "bg-orange-600 text-white hover:bg-orange-700"
                : "bg-green-600 text-white hover:bg-green-700"
              }
            >
              {isTogglingStatus ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  {supplierToToggle?.isActive ? "Deactivating..." : "Reactivating..."}
                </>
              ) : (
                supplierToToggle?.isActive ? "Deactivate" : "Reactivate"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Error Dialog for Linked Products */}
      <AlertDialog open={isErrorDialogOpen} onOpenChange={setIsErrorDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2 text-amber-600">
              <AlertTriangle className="h-5 w-5" />
              Cannot Delete Supplier
            </AlertDialogTitle>
            <AlertDialogDescription className="pt-2">
              <div className="space-y-2">
                <p>{deleteError}</p>
                {supplierToDelete && supplierToDelete._count?.products > 0 && (
                  <div className="mt-4 rounded-md bg-muted p-4">
                    <div className="flex items-center gap-2 font-medium">
                      <Info className="h-4 w-4 text-blue-500" />
                      Linked Resources
                    </div>
                    <ul className="mt-2 list-inside list-disc text-sm">
                      <li>
                        <span className="font-medium">{supplierToDelete._count.products}</span>{" "}
                        product(s) linked to this supplier
                      </li>
                    </ul>
                    <p className="mt-2 text-sm">
                      To delete this supplier, you must first remove these associations or assign
                      the products to a different supplier.
                    </p>
                  </div>
                )}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction>Understood</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </MainLayout>
  );
}
