"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Phone,
  Mail,
  MapPin,
  User,
  Package,
  FileText,
  TrendingUp,
  Loader2,
  Star,
  DollarSign,
  Clock,
  AlertTriangle
} from "lucide-react";
import Link from "next/link";
import { formatCurrency } from "@/lib/utils";
import { SupplierProductsTab } from "@/components/suppliers/SupplierProductsTab";
import { SupplierPurchaseOrdersTab } from "@/components/suppliers/SupplierPurchaseOrdersTab";
import { SupplierAnalyticsTab } from "@/components/suppliers/SupplierAnalyticsTab";

interface Supplier {
  id: string;
  name: string;
  contactPerson: string | null;
  phone: string | null;
  email: string | null;
  address: string | null;
  createdAt: string;
  updatedAt: string;
  _count: {
    productSuppliers: number;
    purchaseOrders: number;
    purchaseOrderTemplates: number;
  };
}

interface SupplierStats {
  totalProducts: number;
  totalPurchaseOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  preferredProducts: number;
  lastOrderDate: string | null;
}

export default function SupplierDetailPage() {
  const params = useParams();
  const router = useRouter();
  const supplierId = params.id as string;

  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [stats, setStats] = useState<SupplierStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");

  // Fetch supplier details
  const fetchSupplier = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/suppliers/${supplierId}`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Supplier not found");
        }
        throw new Error("Failed to fetch supplier details");
      }

      const data = await response.json();
      setSupplier(data.supplier);
      setStats(data.stats);
    } catch (error) {
      console.error("Error fetching supplier:", error);
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (supplierId) {
      fetchSupplier();
    }
  }, [supplierId]);

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading supplier details...</span>
        </div>
      </MainLayout>
    );
  }

  if (error || !supplier) {
    return (
      <MainLayout>
        <div className="p-6">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {error || "Supplier not found"}
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button asChild variant="outline">
              <Link href="/inventory/suppliers">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Suppliers
              </Link>
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={supplier.name}
        description="Supplier details and management"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" asChild>
              <Link href="/inventory/suppliers">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Suppliers
              </Link>
            </Button>
            <Button>
              <Edit className="h-4 w-4 mr-2" />
              Edit Supplier
            </Button>
          </div>
        }
      />

      <div className="space-y-6 w-full max-w-full overflow-hidden supplier-detail-content">
        {/* Supplier Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalProducts || 0}</div>
              <p className="text-xs text-muted-foreground">
                {stats?.preferredProducts || 0} preferred
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Purchase Orders</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalPurchaseOrders || 0}</div>
              <p className="text-xs text-muted-foreground">
                Total orders placed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats?.totalSpent || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                All time spending
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Order Value</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats?.averageOrderValue || 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Per purchase order
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Supplier Information */}
        <Card>
          <CardHeader>
            <CardTitle>Supplier Information</CardTitle>
            <CardDescription>Contact details and basic information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Supplier Name</label>
                  <p className="text-sm">{supplier.name}</p>
                </div>
                
                {supplier.contactPerson && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                    <div className="flex items-center gap-2 text-sm">
                      <User className="h-4 w-4 text-muted-foreground" />
                      {supplier.contactPerson}
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {supplier.phone && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Phone</label>
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      {supplier.phone}
                    </div>
                  </div>
                )}

                {supplier.email && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email</label>
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      {supplier.email}
                    </div>
                  </div>
                )}

                {supplier.address && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Address</label>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      {supplier.address}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs for detailed information */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 w-full max-w-full">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="products">Products ({stats?.totalProducts || 0})</TabsTrigger>
            <TabsTrigger value="purchase-orders">Purchase Orders ({stats?.totalPurchaseOrders || 0})</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4 w-full max-w-full">
            <div className="text-center py-8">
              <p className="text-muted-foreground">
                Select a tab above to view detailed information about this supplier.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="products" className="w-full max-w-full overflow-hidden">
            <SupplierProductsTab supplierId={supplierId} />
          </TabsContent>

          <TabsContent value="purchase-orders" className="w-full max-w-full overflow-hidden">
            <SupplierPurchaseOrdersTab supplierId={supplierId} />
          </TabsContent>

          <TabsContent value="analytics" className="w-full max-w-full overflow-hidden">
            <SupplierAnalyticsTab supplierId={supplierId} />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
