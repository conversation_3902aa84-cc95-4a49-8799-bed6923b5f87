"use client";

import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "sonner";
import { Loader2, Plus, Trash2, ArrowLeft } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodR<PERSON>olver } from "@hookform/resolvers/zod";
import Link from "next/link";

interface Supplier {
  id: string;
  name: string;
  contactPerson?: string;
  phone?: string;
  email?: string;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  purchasePrice?: number;
  category?: {
    name: string;
  };
  unit?: {
    name: string;
  };
  supplierInfo?: {
    id: string;
    supplierProductCode?: string;
    supplierProductName?: string;
    purchasePrice?: number;
    minimumOrderQuantity?: number;
    leadTimeDays?: number;
    isPreferred?: boolean;
    isActive?: boolean;
    notes?: string;
  };
}

interface TemplateItem {
  id?: string;
  productId: string;
  productSupplierId?: string;
  quantity: number;
  unitPrice: number;
  product?: Product;
}

interface POTemplate {
  id: string;
  name: string;
  description?: string;
  supplierId: string;
  supplier: Supplier;
  taxPercentage?: number;
  notes?: string;
  isActive: boolean;
  items: TemplateItem[];
}

const templateSchema = z.object({
  name: z.string().min(1, { message: "Template name is required" }),
  description: z.string().optional(),
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  taxPercentage: z.coerce
    .number()
    .min(0, { message: "Tax percentage must be non-negative" })
    .max(100, { message: "Tax percentage cannot exceed 100%" })
    .default(0),
  notes: z.string().optional(),
  isActive: z.boolean().default(true),
});

type TemplateFormValues = z.infer<typeof templateSchema>;

export default function EditPurchaseOrderTemplatePage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<POTemplate | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [templateItems, setTemplateItems] = useState<TemplateItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [availableProducts, setAvailableProducts] = useState<Product[]>([]);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  const [loadingProducts, setLoadingProducts] = useState(false);

  const templateId = params.id as string;

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<TemplateFormValues>({
    resolver: zodResolver(templateSchema),
  });

  // Fetch template data
  const fetchTemplate = async () => {
    try {
      const response = await fetch(`/api/purchase-order-templates/${templateId}`);
      if (!response.ok) throw new Error("Failed to fetch template");
      const data = await response.json();
      
      setTemplate(data);
      setTemplateItems(data.items?.map((item: any) => ({
        id: item.id,
        productId: item.productId,
        productSupplierId: item.productSupplierId,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        product: item.product,
      })) || []);
      
      // Populate form with existing data
      reset({
        name: data.name,
        description: data.description || "",
        supplierId: data.supplierId,
        taxPercentage: data.taxPercentage || 0,
        notes: data.notes || "",
        isActive: data.isActive,
      });
    } catch (error) {
      console.error("Error fetching template:", error);
      toast.error("Failed to load template");
    }
  };

  // Fetch suppliers
  const fetchSuppliers = async () => {
    try {
      const response = await fetch("/api/suppliers");
      if (!response.ok) throw new Error("Failed to fetch suppliers");
      const data = await response.json();
      setSuppliers(data.suppliers || []);
    } catch (error) {
      console.error("Error fetching suppliers:", error);
      toast.error("Failed to load suppliers");
    }
  };

  // Load products for dropdown
  const loadProducts = async (searchTerm: string = "") => {
    setLoadingProducts(true);
    const selectedSupplierId = watch("supplierId");

    try {
      let response;
      if (selectedSupplierId) {
        // Load supplier-specific products
        const searchParam = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : "";
        response = await fetch(`/api/suppliers/${selectedSupplierId}/products?limit=50&active=true${searchParam}`);
        if (!response.ok) throw new Error("Failed to load supplier products");
        const data = await response.json();
        setAvailableProducts(data.products || []);
      } else {
        // Load all products
        const searchParam = searchTerm ? `&search=${encodeURIComponent(searchTerm)}` : "";
        response = await fetch(`/api/products?limit=50&active=true${searchParam}`);
        if (!response.ok) throw new Error("Failed to load products");
        const data = await response.json();
        setAvailableProducts(data.products || []);
      }
    } catch (error) {
      console.error("Error loading products:", error);
      setAvailableProducts([]);
    } finally {
      setLoadingProducts(false);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      await Promise.all([fetchTemplate(), fetchSuppliers()]);
      setLoading(false);
    };
    loadData();
  }, [templateId]);

  // Load products when supplier changes
  useEffect(() => {
    if (suppliers.length > 0) { // Only load after suppliers are fetched
      loadProducts();
      setSelectedProductId(""); // Clear selected product when supplier changes
    }
  }, [watch("supplierId"), suppliers.length]);

  // Debounced search for products
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm) {
        loadProducts(searchTerm);
      } else {
        loadProducts(); // Load all products when search is cleared
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  const addProductToTemplate = () => {
    if (!selectedProductId) {
      toast.error("Please select a product");
      return;
    }

    const product = availableProducts.find(p => p.id === selectedProductId);
    if (!product) {
      toast.error("Selected product not found");
      return;
    }

    // Check if product is already in the template
    if (templateItems.some(item => item.productId === product.id)) {
      toast.error("Product is already in the template");
      return;
    }

    const newItem: TemplateItem = {
      productId: product.id,
      productSupplierId: product.supplierInfo?.id,
      quantity: product.supplierInfo?.minimumOrderQuantity ? Number(product.supplierInfo.minimumOrderQuantity) : 1,
      unitPrice: product.supplierInfo?.purchasePrice || product.purchasePrice || 0,
      product,
    };

    setTemplateItems([...templateItems, newItem]);
    setSelectedProductId(""); // Clear selection
    toast.success("Product added to template");
  };

  const removeProductFromTemplate = (productId: string) => {
    setTemplateItems(templateItems.filter(item => item.productId !== productId));
    toast.success("Product removed from template");
  };

  const updateTemplateItem = (productId: string, field: 'quantity' | 'unitPrice', value: number) => {
    setTemplateItems(templateItems.map(item =>
      item.productId === productId
        ? { ...item, [field]: value }
        : item
    ));
  };

  const calculateSubtotal = () => {
    return templateItems.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  const calculateTax = () => {
    const taxPercentage = watch("taxPercentage") || 0;
    return (calculateSubtotal() * taxPercentage) / 100;
  };

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTax();
  };

  const onSubmit = async (data: TemplateFormValues) => {
    if (templateItems.length === 0) {
      toast.error("Please add at least one product to the template");
      return;
    }

    setSaving(true);
    try {
      const templateData = {
        ...data,
        items: templateItems.map(item => ({
          productId: item.productId,
          productSupplierId: item.productSupplierId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
      };

      const response = await fetch(`/api/purchase-order-templates/${templateId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(templateData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update template");
      }

      const result = await response.json();
      toast.success("Purchase order template updated successfully");
      router.push(`/inventory/purchase-order-templates/${result.id}`);
    } catch (error) {
      console.error("Error updating template:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update template");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading template...</span>
        </div>
      </MainLayout>
    );
  }

  if (!template) {
    return (
      <MainLayout>
        <div className="text-center p-12">
          <h3 className="text-lg font-medium mb-2">Template not found</h3>
          <p className="text-muted-foreground mb-4">The requested template could not be found.</p>
          <Button asChild>
            <Link href="/inventory/purchase-order-templates">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Templates
            </Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={`Edit Template: ${template.name}`}
        description="Update purchase order template details"
        actions={
          <Button variant="outline" asChild>
            <Link href={`/inventory/purchase-order-templates/${templateId}`}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Template
            </Link>
          </Button>
        }
      />

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
            <CardDescription>Update basic details for the purchase order template</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Template Name *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="e.g., Weekly Office Supplies"
                />
                {errors.name && (
                  <p className="text-sm text-red-600">{errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="supplierId">Supplier *</Label>
                <Select value={watch("supplierId")} onValueChange={(value) => setValue("supplierId", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.supplierId && (
                  <p className="text-sm text-red-600">{errors.supplierId.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Optional description for the template"
                rows={3}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="taxPercentage">Tax Percentage (%)</Label>
                <Input
                  id="taxPercentage"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  {...register("taxPercentage")}
                  placeholder="0"
                />
                {errors.taxPercentage && (
                  <p className="text-sm text-red-600">{errors.taxPercentage.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="isActive">Status</Label>
                <Select value={watch("isActive") ? "true" : "false"} onValueChange={(value) => setValue("isActive", value === "true")}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Active</SelectItem>
                    <SelectItem value="false">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                {...register("notes")}
                placeholder="Optional notes for the template"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Product Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Add Products</CardTitle>
            <CardDescription>Select products to add to the template</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <Select
                    value={selectedProductId}
                    onValueChange={setSelectedProductId}
                    disabled={loadingProducts}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        loadingProducts
                          ? "Loading products..."
                          : availableProducts.length === 0
                            ? "No products available"
                            : "Select a product"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      <div className="p-2">
                        <Input
                          placeholder="Search products..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="mb-2"
                        />
                      </div>
                      {availableProducts.length === 0 ? (
                        <div className="p-2 text-sm text-muted-foreground text-center">
                          {loadingProducts ? "Loading..." : "No products found"}
                        </div>
                      ) : (
                        availableProducts.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            <div className="flex flex-col">
                              <div className="font-medium">
                                {product.supplierInfo?.supplierProductName || product.name}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                SKU: {product.sku}
                                {product.supplierInfo?.supplierProductCode && (
                                  <span className="ml-2 text-blue-600">
                                    Code: {product.supplierInfo.supplierProductCode}
                                  </span>
                                )}
                                {(product.supplierInfo?.purchasePrice || product.purchasePrice) && (
                                  <span className="ml-2 text-green-600">
                                    {new Intl.NumberFormat("id-ID", {
                                      style: "currency",
                                      currency: "IDR",
                                      minimumFractionDigits: 0,
                                    }).format(product.supplierInfo?.purchasePrice || product.purchasePrice)}
                                  </span>
                                )}
                              </div>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>
                <Button
                  type="button"
                  onClick={addProductToTemplate}
                  disabled={!selectedProductId || loadingProducts}
                >
                  Add Product
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Template Items */}
        <Card>
          <CardHeader>
            <CardTitle>Template Items ({templateItems.length})</CardTitle>
            <CardDescription>Products included in this template</CardDescription>
          </CardHeader>
          <CardContent>
            {templateItems.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No products added yet. Search and add products above.
              </div>
            ) : (
              <div className="space-y-4">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product</TableHead>
                      <TableHead>SKU</TableHead>
                      <TableHead>Quantity</TableHead>
                      <TableHead>Unit Price</TableHead>
                      <TableHead>Subtotal</TableHead>
                      <TableHead className="w-16"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {templateItems.map((item) => (
                      <TableRow key={item.productId}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {item.product?.productSupplier?.supplierProductName || item.product?.name}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {item.product?.category?.name}
                              {item.product?.productSupplier?.supplierProductCode && (
                                <span className="ml-2 text-blue-600">
                                  Code: {item.product.productSupplier.supplierProductCode}
                                </span>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div>{item.product?.sku}</div>
                            {item.product?.productSupplier?.minimumOrderQuantity && (
                              <div className="text-xs text-muted-foreground">
                                MOQ: {item.product.productSupplier.minimumOrderQuantity}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="1"
                            step="1"
                            value={item.quantity}
                            onChange={(e) => updateTemplateItem(item.productId, 'quantity', parseFloat(e.target.value) || 1)}
                            className="w-20"
                          />
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            min="0"
                            step="0.01"
                            value={item.unitPrice}
                            onChange={(e) => updateTemplateItem(item.productId, 'unitPrice', parseFloat(e.target.value) || 0)}
                            className="w-32"
                          />
                        </TableCell>
                        <TableCell>
                          {new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(item.quantity * item.unitPrice)}
                        </TableCell>
                        <TableCell>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeProductFromTemplate(item.productId)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* Totals */}
                <div className="border-t pt-4">
                  <div className="flex justify-end space-y-2">
                    <div className="w-64 space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>{new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                        }).format(calculateSubtotal())}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tax ({watch("taxPercentage") || 0}%):</span>
                        <span>{new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                        }).format(calculateTax())}</span>
                      </div>
                      <div className="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>{new Intl.NumberFormat('id-ID', {
                          style: 'currency',
                          currency: 'IDR',
                          minimumFractionDigits: 0,
                        }).format(calculateTotal())}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={saving || templateItems.length === 0}>
            {saving ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              "Update Template"
            )}
          </Button>
        </div>
      </form>
    </MainLayout>
  );
}
