"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, Plus, Edit, Trash } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Pagination } from "@/components/custom/pagination";

interface Unit {
  id: string;
  name: string;
  abbreviation: string;
  createdAt: string;
  updatedAt: string;
}

interface PaginationInfo {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

// Unit form schema
const unitSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  abbreviation: z.string().min(1, { message: "Abbreviation is required" }),
});

type UnitFormValues = z.infer<typeof unitSchema>;

export default function UnitsPage() {
  const [units, setUnits] = useState<Unit[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);

  // Initialize form
  const form = useForm<UnitFormValues>({
    resolver: zodResolver(unitSchema),
    defaultValues: {
      name: "",
      abbreviation: "",
    },
  });

  // Fetch units
  const fetchUnits = async () => {
    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      queryParams.append("page", pagination.page.toString());
      queryParams.append("limit", pagination.limit.toString());

      const response = await fetch(`/api/units?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error("Failed to fetch units");
      }

      const data = await response.json();
      setUnits(data.units || []);

      // Handle case where pagination info is not provided
      if (data.pagination) {
        setPagination(data.pagination);
      } else {
        // Create default pagination based on units length
        const totalItems = data.units?.length || 0;
        setPagination({
          total: totalItems,
          page: 1,
          limit: 10,
          pages: Math.ceil(totalItems / 10) || 1,
        });
      }
    } catch (error) {
      console.error("Error fetching units:", error);
      setError("Failed to load units. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Fetch units when pagination changes
  useEffect(() => {
    fetchUnits();
  }, [pagination.page, pagination.limit]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Open dialog for creating a new unit
  const handleAddUnit = () => {
    setEditingUnit(null);
    form.reset({ name: "", abbreviation: "" });
    setIsDialogOpen(true);
  };

  // Open dialog for editing a unit
  const handleEditUnit = (unit: Unit) => {
    setEditingUnit(unit);
    form.reset({ name: unit.name, abbreviation: unit.abbreviation });
    setIsDialogOpen(true);
  };

  // Handle form submission
  const onSubmit = async (data: UnitFormValues) => {
    setIsSubmitting(true);
    setError(null);

    try {
      if (editingUnit) {
        // Update existing unit
        const response = await fetch(`/api/units/${editingUnit.id}`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to update unit");
        }
      } else {
        // Create new unit
        const response = await fetch("/api/units", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to create unit");
        }
      }

      // Refresh units and close dialog
      await fetchUnits();
      setIsDialogOpen(false);
    } catch (error) {
      console.error("Error saving unit:", error);
      setError((error as Error).message || "Failed to save unit. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle unit deletion
  const handleDeleteUnit = async (id: string) => {
    if (
      !confirm(
        "Are you sure you want to delete this unit? This may affect products using this unit."
      )
    ) {
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/units/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete unit");
      }

      // Refresh units
      await fetchUnits();
    } catch (error) {
      console.error("Error deleting unit:", error);
      setError((error as Error).message || "Failed to delete unit. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <PageHeader
        title="Units"
        description="Manage product measurement units"
        actions={
          <Button onClick={handleAddUnit}>
            <Plus className="h-4 w-4 mr-2" />
            Add Unit
          </Button>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {loading && units.length === 0 ? (
        <div className="flex justify-center items-center p-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading units...</span>
        </div>
      ) : units.length === 0 ? (
        <div className="text-center p-12 border rounded-lg bg-muted/20">
          <h3 className="text-lg font-medium mb-2">No units found</h3>
          <p className="text-muted-foreground mb-4">
            Get started by adding your first measurement unit.
          </p>
          <Button onClick={handleAddUnit}>
            <Plus className="h-4 w-4 mr-2" />
            Add Unit
          </Button>
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Abbreviation</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Last Updated</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {units.map((unit) => (
                  <TableRow key={unit.id}>
                    <TableCell className="font-medium">{unit.name}</TableCell>
                    <TableCell>{unit.abbreviation}</TableCell>
                    <TableCell>{new Date(unit.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell>{new Date(unit.updatedAt).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Button variant="ghost" size="icon" onClick={() => handleEditUnit(unit)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleDeleteUnit(unit.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {pagination.pages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination
                currentPage={pagination.page}
                totalPages={pagination.pages}
                onPageChange={handlePageChange}
              />
            </div>
          )}
        </>
      )}

      {/* Unit Form Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingUnit ? "Edit Unit" : "Add Unit"}</DialogTitle>
            <DialogDescription>
              {editingUnit
                ? "Update the measurement unit details below."
                : "Enter the details for the new measurement unit."}
            </DialogDescription>
          </DialogHeader>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Unit Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter unit name (e.g., Kilogram)" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="abbreviation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Abbreviation</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter abbreviation (e.g., kg)" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
