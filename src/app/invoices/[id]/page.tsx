"use client";

import { useState, useEffect } from "react";
import { useRouter, useParams } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  ArrowLeft,
  FileText,
  Building2,
  Calendar,
  DollarSign,
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  Plus,
  Download,
  Edit,
  AlertTriangle,
  Eye,
  X
} from "lucide-react";
import { formatCurrency, getInvoiceStatusColor, getPaymentStatusColor } from "@/lib/invoice-utils";
import { PaymentRecordingDialog } from "@/components/invoices/PaymentRecordingDialog";

interface InvoiceDetail {
  id: string;
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string | null;
  status: string;
  paymentStatus: string;
  subtotal: number;
  tax: number;
  taxPercentage: number | null;
  total: number;
  paidAmount: number;
  notes: string | null;
  supplier: {
    id: string;
    name: string;
    email: string | null;
    phone: string | null;
    address: string | null;
    contactPerson: string | null;
  };
  purchaseOrder: {
    id: string;
    orderDate: string;
    status: string;
    expectedDeliveryDate: string | null;
    receivedAt: string | null;
  } | null;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  approvedBy: {
    id: string;
    name: string;
    email: string;
  } | null;
  items: Array<{
    id: string;
    description: string;
    quantity: number;
    unitPrice: number;
    subtotal: number;
    product: {
      id: string;
      name: string;
      sku: string;
      unit: {
        name: string;
        abbreviation: string;
      } | null;
    };
  }>;
  payments: Array<{
    id: string;
    amount: number;
    paymentDate: string;
    paymentMethod: string;
    paymentReference: string | null;
    notes: string | null;
    proofImageUrl: string | null;
    createdBy: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  installments: Array<{
    id: string;
    installmentNumber: number;
    dueDate: string;
    amount: number;
    description: string | null;
    status: string;
    paidAmount: number;
    paidAt: string | null;
  }>;
}

export default function InvoiceDetailPage() {
  const router = useRouter();
  const params = useParams();
  const invoiceId = params.id as string;

  const [invoice, setInvoice] = useState<InvoiceDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [statusUpdateLoading, setStatusUpdateLoading] = useState<string | null>(null);
  const [proofModalOpen, setProofModalOpen] = useState(false);
  const [selectedProofUrl, setSelectedProofUrl] = useState<string | null>(null);

  const fetchInvoiceDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/invoices/${invoiceId}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch invoice: ${response.statusText}`);
      }

      const data = await response.json();
      setInvoice(data);
    } catch (error) {
      console.error("Error fetching invoice:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch invoice");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (invoiceId) {
      fetchInvoiceDetail();
    }
  }, [invoiceId]);

  const updateInvoiceStatus = async (newStatus: string) => {
    try {
      setStatusUpdateLoading(newStatus);

      const response = await fetch(`/api/invoices/${invoiceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update invoice status');
      }

      // Refresh invoice data
      await fetchInvoiceDetail();

    } catch (error) {
      console.error('Error updating invoice status:', error);
      alert(`Failed to update invoice status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setStatusUpdateLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const color = getInvoiceStatusColor(status);
    const icons = {
      PENDING: <Clock className="h-3 w-3" />,
      APPROVED: <CheckCircle className="h-3 w-3" />,
      REJECTED: <XCircle className="h-3 w-3" />,
      CANCELLED: <XCircle className="h-3 w-3" />,
    };

    return (
      <Badge variant={color === "green" ? "default" : "secondary"} className={`bg-${color}-100 text-${color}-800 flex items-center gap-1`}>
        {icons[status as keyof typeof icons]}
        {status.replace("_", " ")}
      </Badge>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    const color = getPaymentStatusColor(status);
    return (
      <Badge variant={color === "green" ? "default" : "secondary"} className={`bg-${color}-100 text-${color}-800`}>
        {status.replace("_", " ")}
      </Badge>
    );
  };

  const renderStatusActionButtons = () => {
    const hasPayments = invoice.paidAmount > 0 || invoice.payments.length > 0;
    const buttons = [];

    switch (invoice.status) {
      case 'PENDING':
        buttons.push(
          <Button
            key="approve"
            variant="outline"
            size="sm"
            onClick={() => updateInvoiceStatus('APPROVED')}
            disabled={statusUpdateLoading === 'APPROVED'}
            className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
          >
            {statusUpdateLoading === 'APPROVED' ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            {statusUpdateLoading === 'APPROVED' ? 'Approving...' : 'Approve'}
          </Button>
        );

        buttons.push(
          <Button
            key="reject"
            variant="outline"
            size="sm"
            onClick={() => updateInvoiceStatus('REJECTED')}
            disabled={statusUpdateLoading === 'REJECTED'}
            className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
          >
            {statusUpdateLoading === 'REJECTED' ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <XCircle className="h-4 w-4 mr-2" />
            )}
            {statusUpdateLoading === 'REJECTED' ? 'Rejecting...' : 'Reject'}
          </Button>
        );
        break;

      case 'APPROVED':
        if (!hasPayments) {
          buttons.push(
            <Button
              key="cancel"
              variant="outline"
              size="sm"
              onClick={() => updateInvoiceStatus('CANCELLED')}
              disabled={statusUpdateLoading === 'CANCELLED'}
              className="bg-gray-50 hover:bg-gray-100 text-gray-700 border-gray-200"
            >
              {statusUpdateLoading === 'CANCELLED' ? (
                <Clock className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <AlertTriangle className="h-4 w-4 mr-2" />
              )}
              {statusUpdateLoading === 'CANCELLED' ? 'Cancelling...' : 'Cancel'}
            </Button>
          );
        }
        break;

      case 'REJECTED':
        buttons.push(
          <Button
            key="pending"
            variant="outline"
            size="sm"
            onClick={() => updateInvoiceStatus('PENDING')}
            disabled={statusUpdateLoading === 'PENDING'}
            className="bg-yellow-50 hover:bg-yellow-100 text-yellow-700 border-yellow-200"
          >
            {statusUpdateLoading === 'PENDING' ? (
              <Clock className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Clock className="h-4 w-4 mr-2" />
            )}
            {statusUpdateLoading === 'PENDING' ? 'Updating...' : 'Mark as Pending'}
          </Button>
        );
        break;

      default:
        break;
    }

    return buttons;
  };

  const handleViewProof = (proofUrl: string) => {
    setSelectedProofUrl(proofUrl);
    setProofModalOpen(true);
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-muted-foreground">Loading invoice details...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !invoice) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Invoice Not Found</h2>
            <p className="text-muted-foreground mb-4">
              {error || "The requested invoice could not be found."}
            </p>
            <Button onClick={() => router.push("/invoices")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Invoices
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const remainingAmount = invoice.total - invoice.paidAmount;

  return (
    <MainLayout>
      <PageHeader
        title={invoice.invoiceNumber}
        description="Invoice Details"
        actions={
          <div className="flex items-center space-x-2">
            {renderStatusActionButtons()}
            <Button
              variant="outline"
              onClick={async () => {
                try {
                  // Fetch PDF with proper authentication
                  const response = await fetch(`/api/invoices/${invoice.id}/pdf`);
                  if (!response.ok) {
                    throw new Error(`Failed to generate PDF: ${response.statusText}`);
                  }

                  // Get the HTML content
                  const htmlContent = await response.text();

                  // Open in new window with the HTML content
                  const newWindow = window.open('', '_blank');
                  if (newWindow) {
                    newWindow.document.write(htmlContent);
                    newWindow.document.close();
                    // Auto-print after a short delay
                    setTimeout(() => {
                      newWindow.print();
                    }, 500);
                  }
                } catch (error) {
                  console.error('Error generating PDF:', error);
                  alert('Failed to generate PDF. Please try again.');
                }
              }}
            >
              <Download className="h-4 w-4 mr-2" />
              Download PDF
            </Button>
            {invoice.status === 'PENDING' && invoice.paidAmount === 0 && (
              <Button
                variant="outline"
                onClick={() => router.push(`/invoices/${invoice.id}/edit`)}
              >
                <Edit className="h-4 w-4 mr-2" />
                Edit Invoice
              </Button>
            )}
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Invoice Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Invoice Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Invoice Number</label>
                  <p className="font-semibold">{invoice.invoiceNumber}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1">{getStatusBadge(invoice.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Invoice Date</label>
                  <p>{new Date(invoice.invoiceDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Due Date</label>
                  {invoice.installments && invoice.installments.length > 0 ? (
                    <div>
                      <p className="text-sm text-muted-foreground">Multiple installments</p>
                      <p className="text-xs text-muted-foreground">
                        Next: {(() => {
                          const nextInstallment = invoice.installments.find(inst => inst.status === 'PENDING');
                          return nextInstallment ? new Date(nextInstallment.dueDate).toLocaleDateString() : 'All paid';
                        })()}
                      </p>
                    </div>
                  ) : (
                    <p>{invoice.dueDate ? new Date(invoice.dueDate).toLocaleDateString() : "No due date"}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Payment Status</label>
                  <div className="mt-1">{getPaymentStatusBadge(invoice.paymentStatus)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Created By</label>
                  <p>{invoice.createdBy.name}</p>
                </div>
              </div>
              
              {invoice.notes && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Notes</label>
                  <p className="mt-1 text-sm bg-muted p-3 rounded-md">{invoice.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Invoice Items */}
          <Card>
            <CardHeader>
              <CardTitle>Invoice Items</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Product</TableHead>
                    <TableHead>SKU</TableHead>
                    <TableHead className="text-right">Quantity</TableHead>
                    <TableHead className="text-right">Unit Price</TableHead>
                    <TableHead className="text-right">Subtotal</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {invoice.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.product.name}</p>
                          <p className="text-sm text-muted-foreground">{item.description}</p>
                        </div>
                      </TableCell>
                      <TableCell className="font-mono text-sm">{item.product.sku}</TableCell>
                      <TableCell className="text-right">
                        {item.quantity} {item.product.unit?.abbreviation || "pcs"}
                      </TableCell>
                      <TableCell className="text-right">{formatCurrency(item.unitPrice)}</TableCell>
                      <TableCell className="text-right font-medium">{formatCurrency(item.subtotal)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <Separator className="my-4" />

              {/* Totals */}
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.subtotal)}</span>
                </div>
                {invoice.taxPercentage && (
                  <div className="flex justify-between">
                    <span>Tax ({invoice.taxPercentage}%):</span>
                    <span>{formatCurrency(invoice.tax)}</span>
                  </div>
                )}
                <Separator />
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.total)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Payment Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total Amount:</span>
                  <span className="font-semibold">{formatCurrency(invoice.total)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Paid Amount:</span>
                  <span className="font-semibold text-green-600">{formatCurrency(invoice.paidAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Remaining:</span>
                  <span className="font-semibold text-red-600">{formatCurrency(remainingAmount)}</span>
                </div>
              </div>

              {remainingAmount > 0 && invoice.status === 'APPROVED' && (
                <Button
                  className="w-full"
                  onClick={() => setPaymentDialogOpen(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Record Payment
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Company</label>
                <p className="font-semibold">{invoice.supplier.name}</p>
              </div>
              {invoice.supplier.contactPerson && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Contact Person</label>
                  <p>{invoice.supplier.contactPerson}</p>
                </div>
              )}
              {invoice.supplier.email && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Email</label>
                  <p>{invoice.supplier.email}</p>
                </div>
              )}
              {invoice.supplier.phone && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Phone</label>
                  <p>{invoice.supplier.phone}</p>
                </div>
              )}
              {invoice.supplier.address && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Address</label>
                  <p className="text-sm">{invoice.supplier.address}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Purchase Order Information */}
          {invoice.purchaseOrder && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  Purchase Order
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">PO Number</label>
                  <p className="font-semibold">
                    <button
                      onClick={() => router.push(`/inventory/purchase-orders/${invoice.purchaseOrder.id}`)}
                      className="text-blue-600 hover:underline"
                    >
                      {invoice.purchaseOrder.id.slice(-8).toUpperCase()}
                    </button>
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Order Date</label>
                  <p>{new Date(invoice.purchaseOrder.orderDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">PO Status</label>
                  <p>{invoice.purchaseOrder.status.replace("_", " ")}</p>
                </div>
                {invoice.purchaseOrder.receivedAt && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Received Date</label>
                    <p>{new Date(invoice.purchaseOrder.receivedAt).toLocaleDateString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Payment History */}
      {invoice.payments.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Reference</TableHead>
                  <TableHead>Recorded By</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Proof</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>{new Date(payment.paymentDate).toLocaleDateString()}</TableCell>
                    <TableCell className="font-medium">{formatCurrency(payment.amount)}</TableCell>
                    <TableCell>{payment.paymentMethod}</TableCell>
                    <TableCell className="font-mono text-sm">{payment.paymentReference || "-"}</TableCell>
                    <TableCell>{payment.createdBy.name}</TableCell>
                    <TableCell>{payment.notes || "-"}</TableCell>
                    <TableCell>
                      {payment.proofImageUrl ? (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewProof(payment.proofImageUrl)}
                          className="h-8 px-2"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View Proof
                        </Button>
                      ) : (
                        <span className="text-muted-foreground text-sm">No proof</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Installment Schedule */}
      {invoice.installments && invoice.installments.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Installment Schedule
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Installment</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Paid Amount</TableHead>
                  <TableHead>Paid Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoice.installments.map((installment) => (
                  <TableRow key={installment.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">
                          {installment.description || `Installment ${installment.installmentNumber}`}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          #{installment.installmentNumber}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className={`${new Date(installment.dueDate) < new Date() && installment.status === 'PENDING' ? 'text-red-600 font-medium' : ''}`}>
                        {new Date(installment.dueDate).toLocaleDateString()}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(installment.amount)}
                    </TableCell>
                    <TableCell>
                      <Badge variant={
                        installment.status === 'PAID' ? 'default' :
                        installment.status === 'OVERDUE' ? 'destructive' :
                        new Date(installment.dueDate) < new Date() ? 'destructive' : 'secondary'
                      }>
                        {installment.status === 'PAID' ? 'Paid' :
                         new Date(installment.dueDate) < new Date() && installment.status === 'PENDING' ? 'Overdue' :
                         installment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {installment.paidAmount > 0 ? formatCurrency(installment.paidAmount) : '-'}
                    </TableCell>
                    <TableCell>
                      {installment.paidAt ? new Date(installment.paidAt).toLocaleDateString() : '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}

      {/* Payment Recording Dialog */}
      {invoice && (
        <PaymentRecordingDialog
          open={paymentDialogOpen}
          onOpenChange={setPaymentDialogOpen}
          invoiceId={invoice.id}
          invoiceNumber={invoice.invoiceNumber}
          totalAmount={invoice.total}
          paidAmount={invoice.paidAmount}
          installments={invoice.installments}
          onPaymentRecorded={fetchInvoiceDetail}
        />
      )}

      {/* Payment Proof Modal */}
      <Dialog open={proofModalOpen} onOpenChange={setProofModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              Payment Proof
              <Button
                variant="outline"
                size="sm"
                onClick={() => setProofModalOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="flex justify-center items-center p-4 bg-gray-50 rounded-lg max-h-[70vh] overflow-auto">
            {selectedProofUrl && (
              <>
                {selectedProofUrl.toLowerCase().endsWith('.pdf') ? (
                  <iframe
                    src={`/api/files/payment-proofs/${selectedProofUrl.split('/').pop()}`}
                    className="w-full h-[60vh] border-0"
                    title="Payment Proof PDF"
                  />
                ) : selectedProofUrl ? (
                  <img
                    src={`/api/files/payment-proofs/${selectedProofUrl.split('/').pop()}`}
                    alt="Payment Proof"
                    className="max-w-full max-h-full object-contain"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const errorDiv = document.createElement('div');
                      errorDiv.className = 'text-center text-gray-500 p-8';
                      errorDiv.innerHTML = '<p>Unable to load payment proof image</p>';
                      target.parentNode?.appendChild(errorDiv);
                    }}
                  />
                ) : (
                  <div className="text-center text-gray-500 p-8">
                    <p>No payment proof available</p>
                  </div>
                )}
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}
