"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { formatDistanceToNow } from "date-fns";
import BackupHistory from "@/components/backup/BackupHistory";
import UploadDialog from "@/components/backup/UploadDialog";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";

interface Backup {
  filename: string;
  path: string;
  timestamp: string;
  size: number;
  comment?: string;
}

export default function BackupPage() {
  const [backups, setBackups] = useState<Backup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  // Removed unused state

  // Fetch backups
  const fetchBackups = async () => {
    setLoading(true);
    try {
      console.log('Fetching backups...');

      const response = await fetch("/api/backup");
      const data = await response.json();

      console.log('Fetch backups response:', data);

      if (!response.ok || data.error) {
        throw new Error(data.message || data.error || `HTTP ${response.status}: Failed to fetch backups`);
      }

      setBackups(data.backups || []);
      setError(null);

      console.log('Backups loaded successfully:', data.backups?.length || 0, 'backups');
    } catch (err: any) {
      console.error("Error fetching backups:", err);
      setError(err.message || "Failed to fetch backups");
    } finally {
      setLoading(false);
    }
  };

  // Create a new backup
  const createBackup = async (comment = "") => {
    setLoading(true);
    setError(null); // Clear any previous errors
    setSuccess(null); // Clear any previous success messages

    try {
      console.log('Creating backup with comment:', comment);

      const response = await fetch("/api/backup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ comment }),
      });

      const data = await response.json();
      console.log('Backup API response:', data);

      if (!response.ok || data.error) {
        throw new Error(data.message || data.error || `HTTP ${response.status}: Failed to create backup`);
      }

      setSuccess(data.message || "Backup created successfully!");

      // Refresh the backup list
      await fetchBackups();
    } catch (err: any) {
      console.error("Error creating backup:", err);
      setError(err.message || "Failed to create backup");
    } finally {
      setLoading(false);
    }
  };

  // State for schema validation dialog
  const [schemaValidation, setSchemaValidation] = useState<any>(null);

  // Restore from backup
  const restoreBackup = async (backupPath: string) => {
    setLoading(true);
    try {
      const response = await fetch("/api/backup/restore", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ backupPath }),
      });

      const data = await response.json();

      // If there's a schema mismatch, show warning dialog and prevent restore
      if (!data.success && data.prevented && data.schemaValidation) {
        setSchemaValidation(data.schemaValidation);
        setLoading(false);
        return;
      }

      if (data.error) {
        throw new Error(data.message || "Failed to restore backup");
      }

      setSuccess("Database restored successfully!");
      // Clear any schema validation state
      setSchemaValidation(null);
    } catch (err: any) {
      console.error("Error restoring backup:", err);
      setError(err.message || "Failed to restore backup");
    } finally {
      setLoading(false);
    }
  };

  // Delete backup
  const deleteBackup = async (backupPath: string) => {
    setLoading(true);
    try {
      const response = await fetch("/api/backup", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ backupPath }),
      });

      const data = await response.json();

      if (data.error) {
        throw new Error(data.message || "Failed to delete backup");
      }

      setSuccess("Backup deleted successfully!");
      fetchBackups();
    } catch (err: any) {
      console.error("Error deleting backup:", err);
      setError(err.message || "Failed to delete backup");
    } finally {
      setLoading(false);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes < 1024) return bytes + " B";
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + " KB";
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + " MB";
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + " GB";
  };

  // Load backups on component mount
  useEffect(() => {
    fetchBackups();
  }, []);

  // Clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [success]);

  return (
    <MainLayout>
      <PageHeader
        title="Database Backup & Restore"
        description="Manage database backups and restore operations"
        actions={
          <div className="flex flex-col sm:flex-row gap-2">
            <UploadDialog onUploadSuccess={fetchBackups} />
            <Button onClick={() => createBackup()} disabled={loading}>
              Create New Backup
            </Button>
          </div>
        }
      />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="mb-4">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {/* Schema Validation Dialog */}
      {schemaValidation && (
        <AlertDialog open={!!schemaValidation} onOpenChange={() => setSchemaValidation(null)}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Schema Version Mismatch</AlertDialogTitle>
              <AlertDialogDescription asChild>
                <div className="space-y-4 text-sm text-muted-foreground">
                  <div className="text-amber-600 font-medium">{schemaValidation.message}</div>

                  <div className="bg-amber-50 p-3 rounded-md border border-amber-200">
                    <div className="mb-1">
                      <strong>Backup Schema Version:</strong> {schemaValidation.backupSchemaVersion}
                    </div>
                    <div>
                      <strong>Current Schema Version:</strong>{" "}
                      {schemaValidation.currentSchemaVersion}
                    </div>
                  </div>

                  <div>
                    Restoring a backup with a different schema version may cause data
                    inconsistencies or application errors. It is recommended to only restore backups
                    with matching schema versions.
                  </div>

                  <div className="bg-red-50 p-3 rounded-md border border-red-200">
                    <div className="text-red-600 font-medium">
                      Restore operation prevented for safety reasons.
                    </div>
                  </div>
                </div>
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Close</AlertDialogCancel>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-semibold">Backup Management</h2>
      </div>

      {/* Mobile view (show cards) */}
      <div className="md:hidden">
        {loading && <p className="text-center py-4">Loading backups...</p>}

        {!loading && backups.length === 0 && (
          <p className="text-center py-4">No backups found. Create your first backup!</p>
        )}

        {!loading &&
          backups.map((backup) => (
            <div key={backup.path} className="mb-4 p-4 border rounded-lg bg-white shadow-sm">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium">{backup.filename}</h3>
                <span className="text-xs text-gray-500">
                  {formatDistanceToNow(new Date(backup.timestamp), { addSuffix: true })}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                <div>
                  <span className="text-gray-500">Size:</span> {formatFileSize(backup.size)}
                </div>
                <div>
                  <span className="text-gray-500">Comment:</span> {backup.comment || "-"}
                </div>
                <div>
                  <span className="text-gray-500">Schema:</span>{" "}
                  {backup.schemaVersion ? (
                    <span className={backup.schemaCompatible ? "text-green-600" : "text-amber-600"}>
                      {backup.schemaVersion}
                      {!backup.schemaCompatible && " (mismatch)"}
                    </span>
                  ) : (
                    "unknown"
                  )}
                </div>
                <div>
                  <span className="text-gray-500">Method:</span> {backup.method || "-"}
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => window.open(`/api/backup/download/${backup.filename}`, "_blank")}
                >
                  Download
                </Button>
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      Restore
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Restore Database</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to restore the database from this backup? This will
                        overwrite all current data with the data from the backup. This action cannot
                        be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => restoreBackup(backup.path)}>
                        Restore
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>

                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Backup</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete this backup? This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => deleteBackup(backup.path)}>
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          ))}
      </div>

      {/* Desktop view (show table) - hide on mobile */}
      <div className="hidden md:block bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Filename
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Size
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Comment
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Schema
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading && (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center">
                  Loading backups...
                </td>
              </tr>
            )}

            {!loading && backups.length === 0 && (
              <tr>
                <td colSpan={5} className="px-6 py-4 text-center">
                  No backups found. Create your first backup!
                </td>
              </tr>
            )}

            {!loading &&
              backups.map((backup) => (
                <tr key={backup.path}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {backup.filename}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDistanceToNow(new Date(backup.timestamp), { addSuffix: true })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatFileSize(backup.size)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {backup.comment || "-"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    {backup.schemaVersion ? (
                      <span
                        className={backup.schemaCompatible ? "text-green-600" : "text-amber-600"}
                      >
                        {backup.schemaVersion}
                        {!backup.schemaCompatible && " (mismatch)"}
                      </span>
                    ) : (
                      <span className="text-gray-500">unknown</span>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="secondary"
                        size="sm"
                        onClick={() =>
                          window.open(`/api/backup/download/${backup.filename}`, "_blank")
                        }
                      >
                        Download
                      </Button>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="outline" size="sm">
                            Restore
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Restore Database</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to restore the database from this backup? This
                              will overwrite all current data with the data from the backup. This
                              action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => restoreBackup(backup.path)}>
                              Restore
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>

                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" size="sm">
                            Delete
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Backup</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete this backup? This action cannot be
                              undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={() => deleteBackup(backup.path)}>
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </td>
                </tr>
              ))}
          </tbody>
        </table>
      </div>

      <BackupHistory />

      <div className="mt-8 bg-gray-50 p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Backup Information</h2>
        <p className="mb-2">
          <strong>Backup Location:</strong> <code>/backups</code> directory in the project root
        </p>
        <p className="mb-2">
          <strong>Automatic Backups:</strong> Configured to run daily after store closing
        </p>
        <p className="mb-2">
          <strong>Retention Policy:</strong> Keeps the last 30 backups by default
        </p>
        <p className="mb-4">
          <strong>Manual Backups:</strong> Use the "Create New Backup" button above
        </p>

        <div className="mb-4 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-medium mb-2">Backup Methods</h3>
          <p className="mb-2">The system supports two backup methods:</p>
          <ul className="list-disc pl-5 mb-2">
            <li>
              <strong>PostgreSQL Tools (pg_dump/pg_restore):</strong> Used if PostgreSQL
              command-line tools are available on the system
            </li>
            <li>
              <strong>Prisma Method:</strong> Used as a fallback if PostgreSQL tools are not
              available
            </li>
          </ul>
          <p>The system automatically selects the best method based on your environment.</p>
        </div>

        <div className="mb-4 p-4 bg-green-50 rounded-lg">
          <h3 className="text-lg font-medium mb-2">Schema Version Tracking</h3>
          <p className="mb-2">
            The backup system includes schema version tracking to ensure data integrity:
          </p>
          <ul className="list-disc pl-5 mb-2">
            <li>
              <strong>Schema Version:</strong> Each backup stores the database schema version at the
              time of backup
            </li>
            <li>
              <strong>Pre-Restore Validation:</strong> Before restoring, the system checks if the
              backup schema matches the current schema
            </li>
            <li>
              <strong>Strict Safety Enforcement:</strong> If schemas don't match, the system
              prevents restoration and displays a warning message
            </li>
          </ul>
          <p>
            This protection system ensures database integrity by preventing the restoration of
            backups with incompatible schema versions, which could cause data inconsistencies or
            application errors.
          </p>
        </div>

        <Alert>
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            It's recommended to periodically copy backups to an external storage device for
            additional safety. Backups are crucial for data recovery in case of hardware failure or
            other issues.
          </AlertDescription>
        </Alert>
      </div>
    </MainLayout>
  );
}
