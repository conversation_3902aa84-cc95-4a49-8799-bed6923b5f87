"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Separator } from "@/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Users,
  Settings,
  Shield,
  Activity,
  Database,
  MessageSquare,
  ArrowRightCircle,
  FileText,
  Lock,
} from "lucide-react";
import Link from "next/link";

export default function AdminLandingPage() {
  const adminFeatures = [
    {
      title: "User Management",
      description: "Manage user accounts, roles, and permissions",
      icon: <Users className="h-8 w-8 text-primary" />,
      link: "/admin/users",
      actions: [
        { label: "View All Users", link: "/admin/users" },
        { label: "Add New User", link: "/admin/users/new" },
        { label: "Manage Roles", link: "/admin/roles" },
      ],
    },
    {
      title: "Activity Logs",
      description: "Track and monitor user activities in the system",
      icon: <Activity className="h-8 w-8 text-primary" />,
      link: "/admin/activity-logs",
      actions: [
        { label: "View Activity Logs", link: "/admin/activity-logs" },
        { label: "Filter by User", link: "/admin/activity-logs?filter=user" },
        { label: "Filter by Action", link: "/admin/activity-logs?filter=action" },
      ],
    },
    {
      title: "System Settings",
      description: "Configure system-wide settings and preferences",
      icon: <Settings className="h-8 w-8 text-primary" />,
      link: "/admin/settings",
      actions: [
        { label: "General Settings", link: "/admin/settings" },
        { label: "Feature Toggles", link: "/admin/settings?tab=features" },
        { label: "Appearance Settings", link: "/admin/settings?tab=appearance" },
      ],
    },
    {
      title: "Backup & Restore",
      description: "Manage database backups and restoration",
      icon: <Database className="h-8 w-8 text-primary" />,
      link: "/admin/backup",
      actions: [
        { label: "View Backups", link: "/admin/backup" },
        { label: "Create Backup", link: "/admin/backup?action=create" },
        { label: "Restore Database", link: "/admin/backup?action=restore" },
      ],
    },
    {
      title: "Message Center",
      description: "Manage system messages and notifications",
      icon: <MessageSquare className="h-8 w-8 text-primary" />,
      link: "/admin/messages",
      actions: [
        { label: "View Messages", link: "/admin/messages" },
        { label: "Message Settings", link: "/admin/messages/settings" },
        { label: "Notification Templates", link: "/admin/messages/templates" },
      ],
    },
    {
      title: "Security",
      description: "Manage security settings and access controls",
      icon: <Shield className="h-8 w-8 text-primary" />,
      link: "/admin/security",
      actions: [
        { label: "Security Dashboard", link: "/admin/security" },
        { label: "Security Logs", link: "/admin/security/logs" },
        { label: "Device Management", link: "/admin/security" },
      ],
    },
    {
      title: "Documentation",
      description: "Access system documentation and help resources",
      icon: <FileText className="h-8 w-8 text-primary" />,
      link: "/admin/docs",
      actions: [
        { label: "User Guide", link: "/admin/docs/user-guide" },
        { label: "Admin Guide", link: "/admin/docs/admin-guide" },
        { label: "API Documentation", link: "/admin/docs/api" },
      ],
    },
    {
      title: "Developer Tools",
      description: "Access developer tools and testing utilities",
      icon: <Lock className="h-8 w-8 text-primary" />,
      link: "/development",
      actions: [
        { label: "API Tests", link: "/development/api-tests" },
        { label: "System Info", link: "/development/system-info" },
        { label: "Debug Console", link: "/development/console" },
      ],
    },
  ];

  return (
    <MainLayout>
      <PageHeader
        title="Administration"
        description="Manage users, system settings, and administrative functions"
      />

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {adminFeatures.map((feature, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-3">
                {feature.icon}
                <div>
                  <CardTitle>{feature.title}</CardTitle>
                  <CardDescription>{feature.description}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pb-2">
              <div className="space-y-2">
                {feature.actions.map((action, actionIndex) => (
                  <div key={actionIndex} className="flex items-center">
                    <ArrowRightCircle className="h-4 w-4 mr-2 text-muted-foreground" />
                    <Link href={action.link} className="text-sm hover:underline">
                      {action.label}
                    </Link>
                  </div>
                ))}
              </div>
            </CardContent>
            <CardFooter>
              <Button asChild className="w-full">
                <Link href={feature.link}>Go to {feature.title}</Link>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </MainLayout>
  );
}
