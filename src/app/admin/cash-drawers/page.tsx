"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  Select<PERSON><PERSON><PERSON>,
  <PERSON>Value,
} from "@/components/ui/select";
import {
  <PERSON>ader2,
  <PERSON>,
  Trash,
  AlertCircle,
  ExternalLink,
  Info,
  Calculator,
  UserX,
} from "lucide-react";
import Link from "next/link";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useAuth } from "@/hooks/use-auth";
import { formatDate } from "@/lib/utils";

// Drawer schema for validation
const drawerSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  location: z.string().optional(),
  isActive: z.boolean().default(true),
});

type DrawerFormValues = z.infer<typeof drawerSchema>;

interface CashDrawer {
  id: string;
  name: string;
  location?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  terminal?: {
    id: string;
    name: string;
    location?: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
    active: boolean;
  };
  _count: {
    drawerSessions: number;
  };
}

interface Terminal {
  id: string;
  name: string;
  location?: string;
}

export default function CashDrawersPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [drawers, setDrawers] = useState<CashDrawer[]>([]);
  const [terminals, setTerminals] = useState<Terminal[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [assignDialogOpen, setAssignDialogOpen] = useState(false);
  const [selectedDrawer, setSelectedDrawer] = useState<CashDrawer | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isAssigning, setIsAssigning] = useState(false);

  // Edit form
  const editForm = useForm<DrawerFormValues>({
    resolver: zodResolver(drawerSchema),
    defaultValues: {
      name: "",
      location: "",
      isActive: true,
    },
  });

  // Fetch cash drawers
  const fetchDrawers = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch("/api/cash-drawers");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch cash drawers");
      }

      setDrawers(data.drawers);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error fetching cash drawers:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch available terminals
  const fetchTerminals = async () => {
    try {
      const response = await fetch("/api/terminals?isActive=true");
      if (!response.ok) {
        throw new Error("Failed to fetch terminals");
      }

      const data = await response.json();
      setTerminals(data.terminals);
    } catch (error) {
      console.error("Error fetching terminals:", error);
    }
  };

  // Load drawers and terminals on mount
  useEffect(() => {
    fetchDrawers();
    fetchTerminals();
  }, []);

  // Handle edit drawer form submission
  const handleEditSubmit = async (values: DrawerFormValues) => {
    if (!selectedDrawer) return;

    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/cash-drawers/${selectedDrawer.id}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to update cash drawer");
      }

      toast.success("Cash drawer updated successfully");
      setEditDialogOpen(false);
      setSelectedDrawer(null);
      fetchDrawers();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error updating cash drawer:", err);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit button click
  const handleEditClick = (drawer: CashDrawer) => {
    setSelectedDrawer(drawer);
    editForm.reset({
      name: drawer.name,
      location: drawer.location || "",
      isActive: drawer.isActive,
    });
    setEditDialogOpen(true);
  };

  // Handle view drawer sessions
  const handleViewSessions = (drawerId: string) => {
    router.push(`/admin/cash-drawers/${drawerId}`);
  };

  // Handle assign terminal button click
  const handleAssignClick = (drawer: CashDrawer) => {
    setSelectedDrawer(drawer);
    setAssignDialogOpen(true);
  };

  // Handle terminal assignment
  const handleTerminalAssignment = async (terminalId: string | null) => {
    if (!selectedDrawer) return;

    setIsAssigning(true);

    try {
      const response = await fetch(`/api/cash-drawers/${selectedDrawer.id}/assign-terminal`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ terminalId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to assign terminal");
      }

      toast.success(
        terminalId ? "Terminal assigned successfully" : "Terminal unassigned successfully"
      );
      setAssignDialogOpen(false);
      setSelectedDrawer(null);
      fetchDrawers();
      fetchTerminals();
    } catch (err) {
      toast.error(err instanceof Error ? err.message : "An unknown error occurred");
      console.error("Error assigning terminal:", err);
    } finally {
      setIsAssigning(false);
    }
  };

  // Get available terminals for assignment (not already assigned to other drawers)
  const getAvailableTerminals = () => {
    return terminals.filter((terminal) => {
      // Include terminals that are not assigned to any drawer
      // or terminals that are assigned to the current drawer
      const assignedDrawer = drawers.find((drawer) => drawer.terminal?.id === terminal.id);
      return !assignedDrawer || assignedDrawer.id === selectedDrawer?.id;
    });
  };

  // Check if manual activation should be disabled for the selected drawer
  const isManualActivationDisabled = () => {
    if (!selectedDrawer) return false;

    // Allow manual activation for unassigned drawers (no user)
    if (!selectedDrawer.user) return false;

    // Disable manual activation if assigned user's role is not CASHIER
    return selectedDrawer.user.role !== "CASHIER";
  };

  // Get the reason why manual activation is disabled
  const getActivationDisabledReason = () => {
    if (!selectedDrawer?.user) return "";

    return `This drawer is assigned to ${selectedDrawer.user.name} who currently has the role "${selectedDrawer.user.role}". Only users with the CASHIER role can have active drawers. To activate this drawer, change ${selectedDrawer.user.name}'s role to CASHIER in the user management page.`;
  };

  return (
    <MainLayout>
      <PageHeader
        heading="Cash Drawers"
        subheading="View and manage cash drawers for your point of sale system"
        actions={
          <Button variant="outline" onClick={() => router.push("/admin/drawer-sessions")}>
            <Calculator className="h-4 w-4 mr-2" />
            Drawer Sessions
          </Button>
        }
      />

      <Card>
        <CardHeader>
          <CardTitle>Cash Drawers</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
            </div>
          ) : error ? (
            <div className="bg-destructive/10 text-destructive p-4 rounded-md">{error}</div>
          ) : drawers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <p>No cash drawers found.</p>
              <p className="text-sm mt-2">
                Drawers are automatically created when you add new cashiers to the system.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Assigned Cashier</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Terminal Assignment</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date Created</TableHead>
                  <TableHead>Sessions</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {drawers.map((drawer) => (
                  <TableRow key={drawer.id}>
                    <TableCell className="font-medium">{drawer.name}</TableCell>
                    <TableCell>
                      {drawer.user ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{drawer.user.name}</span>
                          <span className="text-sm text-muted-foreground">{drawer.user.email}</span>
                          {!drawer.user.active && (
                            <Badge variant="outline" className="w-fit mt-1">
                              Inactive User
                            </Badge>
                          )}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Unassigned</span>
                      )}
                    </TableCell>
                    <TableCell>{drawer.location || "-"}</TableCell>
                    <TableCell>
                      {drawer.terminal ? (
                        <div className="flex flex-col">
                          <span className="font-medium">{drawer.terminal.name}</span>
                          {drawer.terminal.location && (
                            <span className="text-sm text-muted-foreground">
                              {drawer.terminal.location}
                            </span>
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">Unassigned</Badge>
                          {drawer.isActive && getAvailableTerminals().length > 0 ? (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleAssignClick(drawer)}
                            >
                              Assign Terminal
                            </Button>
                          ) : !drawer.isActive ? (
                            <span className="text-sm text-muted-foreground">Drawer inactive</span>
                          ) : (
                            <span className="text-sm text-muted-foreground">
                              No terminals available
                            </span>
                          )}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {drawer.isActive ? (
                        <Badge variant="success">Active</Badge>
                      ) : (
                        <Badge variant="secondary">Inactive</Badge>
                      )}
                    </TableCell>
                    <TableCell>{formatDate(new Date(drawer.createdAt))}</TableCell>
                    <TableCell>{drawer._count.drawerSessions}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewSessions(drawer.id)}
                        >
                          View Sessions
                        </Button>
                        {drawer.terminal && drawer.isActive && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleAssignClick(drawer)}
                          >
                            Reassign
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleEditClick(drawer)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Drawer Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Cash Drawer</DialogTitle>
            <DialogDescription>Update the details of this cash drawer.</DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleEditSubmit)} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location (Optional)</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="isActive"
                render={({ field }) => {
                  const isDisabled = isManualActivationDisabled();
                  return (
                    <div className="space-y-3">
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Active</FormLabel>
                          <FormDescription>
                            Inactive drawers cannot be used for transactions.
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={isDisabled ? undefined : field.onChange}
                            disabled={isDisabled}
                          />
                        </FormControl>
                      </FormItem>
                      {isDisabled && (
                        <div className="p-3 bg-amber-50 border border-amber-200 rounded-md">
                          <div className="flex items-start gap-3">
                            <UserX className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                            <div>
                              <h4 className="text-sm font-medium text-amber-900">
                                Manual Activation Restricted
                              </h4>
                              <p className="text-sm text-amber-700 mt-1">
                                {getActivationDisabledReason()}
                              </p>
                              <Link
                                href="/admin/users"
                                className="inline-flex items-center gap-1 text-sm text-amber-600 hover:text-amber-800 mt-2"
                              >
                                <ExternalLink className="h-3 w-3" />
                                Manage User Roles
                              </Link>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  );
                }}
              />
              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Save Changes
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Terminal Assignment Dialog */}
      <Dialog open={assignDialogOpen} onOpenChange={setAssignDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Terminal</DialogTitle>
            <DialogDescription>
              {selectedDrawer?.terminal
                ? `Reassign terminal for drawer "${selectedDrawer.name}"`
                : `Assign a terminal to drawer "${selectedDrawer?.name}"`}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedDrawer?.terminal && (
              <div className="p-3 bg-muted rounded-md">
                <p className="text-sm font-medium">Currently assigned to:</p>
                <p className="text-sm text-muted-foreground">
                  {selectedDrawer.terminal.name}
                  {selectedDrawer.terminal.location && ` (${selectedDrawer.terminal.location})`}
                </p>
              </div>
            )}

            {getAvailableTerminals().length === 0 ? (
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-start gap-3">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-blue-900">No Available Terminals</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      All active terminals are already assigned to other drawers. You need to create
                      new terminals or unassign existing ones.
                    </p>
                    <Link
                      href="/admin/terminals"
                      className="inline-flex items-center gap-1 text-sm text-blue-600 hover:text-blue-800 mt-2"
                    >
                      <ExternalLink className="h-3 w-3" />
                      Manage Terminals
                    </Link>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                <label className="text-sm font-medium">Select Terminal:</label>
                <Select
                  onValueChange={(value) =>
                    handleTerminalAssignment(value === "unassign" ? null : value)
                  }
                  disabled={isAssigning}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a terminal..." />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedDrawer?.terminal && (
                      <SelectItem value="unassign">
                        <span className="text-destructive">Unassign Terminal</span>
                      </SelectItem>
                    )}
                    {getAvailableTerminals().map((terminal) => (
                      <SelectItem key={terminal.id} value={terminal.id}>
                        {terminal.name}
                        {terminal.location && ` (${terminal.location})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setAssignDialogOpen(false)}
              disabled={isAssigning}
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* No Available Terminals Message */}
      {!isLoading && drawers.some((drawer) => !drawer.terminal) && terminals.length === 0 && (
        <Card className="mt-6">
          <CardContent className="pt-6">
            <div className="flex items-start gap-3 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium text-blue-900">No Terminals Available</h4>
                <p className="text-sm text-blue-700 mt-1">
                  You have unassigned cash drawers but no terminals available for assignment. Create
                  new terminals to enable drawer assignments.
                </p>
                <Link href="/admin/terminals">
                  <Button variant="outline" size="sm" className="mt-3">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Manage Terminals
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </MainLayout>
  );
}
