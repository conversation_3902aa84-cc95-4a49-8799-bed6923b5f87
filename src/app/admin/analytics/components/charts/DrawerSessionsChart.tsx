"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  Bar<PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { formatCurrency, formatChartDate, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { DrawerSessionData, AnalyticsFilters } from "@/lib/types/analytics";

interface DrawerSessionsChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function DrawerSessionsChart({ filters, className }: DrawerSessionsChartProps) {
  const [data, setData] = useState<DrawerSessionData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();
      
      if (filters.dateRange.from) {
        params.append('from', filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append('to', filters.dateRange.to.toISOString());
      }
      if (filters.cashierIds?.length) {
        params.append('cashierIds', filters.cashierIds.join(','));
      }
      if (filters.terminalIds?.length) {
        params.append('terminalIds', filters.terminalIds.join(','));
      }

      const response = await fetch(`/api/analytics/drawer-sessions?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch drawer sessions data: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch drawer sessions data');
      }

      setData(result.data || []);
    } catch (err) {
      console.error('Error fetching drawer sessions data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{data.cashierName}</p>
          <p className="text-sm text-gray-600 mb-1">
            Opened: {formatChartDate(data.openedAt)}
          </p>
          {data.closedAt && (
            <p className="text-sm text-gray-600 mb-1">
              Closed: {formatChartDate(data.closedAt)}
            </p>
          )}
          <p className="text-sm text-gray-600 mb-1">
            Opening Balance: {formatCurrency(data.openingBalance)}
          </p>
          {data.closingBalance !== undefined && (
            <p className="text-sm text-gray-600 mb-1">
              Closing Balance: {formatCurrency(data.closingBalance)}
            </p>
          )}
          {data.discrepancy !== undefined && (
            <p className={`text-sm font-medium ${
              Math.abs(data.discrepancy) > 1000 ? 'text-red-600' : 'text-green-600'
            }`}>
              Discrepancy: {formatCurrency(data.discrepancy)}
            </p>
          )}
          <p className="text-sm text-gray-600">
            Transactions: {data.transactionCount}
          </p>
        </div>
      );
    }
    return null;
  };

  // Transform data for chart display
  const chartData = data.map((session, index) => ({
    ...session,
    name: session.cashierName,
    value: session.closingBalance || session.openingBalance,
    discrepancyAbs: Math.abs(session.discrepancy || 0),
    status: session.closedAt ? 'closed' : 'open',
    fill: session.closedAt 
      ? (Math.abs(session.discrepancy || 0) > 1000 ? CHART_COLORS.danger[0] : CHART_COLORS.success[0])
      : CHART_COLORS.warning[0]
  }));

  return (
    <BaseChart
      title="Drawer Sessions"
      subtitle="Cash drawer sessions and discrepancies"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="name"
            stroke="#666"
            fontSize={12}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis
            stroke="#666"
            fontSize={12}
            tickFormatter={(value) => formatCurrency(value)}
          />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="value" radius={[4, 4, 0, 0]}>
            {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.fill} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
