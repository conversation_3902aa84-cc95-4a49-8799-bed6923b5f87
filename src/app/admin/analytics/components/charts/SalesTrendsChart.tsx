"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts";
import { formatCurrency, formatChartDate, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { SalesTrendData, AnalyticsFilters } from "@/lib/types/analytics";

interface SalesTrendsChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function SalesTrendsChart({ filters, className }: SalesTrendsChartProps) {
  const [data, setData] = useState<SalesTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      // Add custom date range or preset
      if (!filters.dateRange.from || !filters.dateRange.to) {
        throw new Error("Date range is required");
      }

      const daysDiff = Math.ceil(
        (filters.dateRange.to.getTime() - filters.dateRange.from.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Always pass custom dates for precise filtering
      params.append("fromDate", filters.dateRange.from.toISOString());
      params.append("toDate", filters.dateRange.to.toISOString());

      // Also pass preset for backward compatibility
      if (daysDiff <= 7) {
        params.append("dateRange", "7d");
      } else if (daysDiff <= 30) {
        params.append("dateRange", "30d");
      } else if (daysDiff <= 90) {
        params.append("dateRange", "90d");
      } else {
        params.append("dateRange", "1y");
      }

      // Add all filter parameters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      console.log("[SalesTrendsChart] Fetching with params:", params.toString());
      console.log("[SalesTrendsChart] Filters:", filters);

      const response = await fetch(`/api/analytics/sales-trends?${params.toString()}`);

      console.log("Sales trends response status:", response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Sales trends API error:", errorText);
        throw new Error(`Failed to fetch sales trends: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();

      console.log("Sales trends result success:", result.success);

      if (!result.success) {
        console.error("Sales trends result error:", result.error);
        throw new Error(result.error || "Failed to fetch sales trends");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching sales trends:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{formatChartDate(label, "medium")}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">
              <span className="text-blue-600">Revenue: </span>
              <span className="font-medium">{formatCurrency(payload[0]?.value || 0)}</span>
            </p>
            <p className="text-sm">
              <span className="text-green-600">Transactions: </span>
              <span className="font-medium">{payload[0]?.payload?.transactions || 0}</span>
            </p>
            <p className="text-sm">
              <span className="text-purple-600">Avg Order Value: </span>
              <span className="font-medium">
                {formatCurrency(payload[0]?.payload?.averageOrderValue || 0)}
              </span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <BaseChart
      title="Sales Trends"
      subtitle="Revenue and transaction patterns over time"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="revenueGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.8} />
              <stop offset="95%" stopColor={CHART_COLORS.primary[0]} stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => formatChartDate(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis tickFormatter={(value) => formatCurrency(value)} stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="revenue"
            stroke={CHART_COLORS.primary[0]}
            strokeWidth={2}
            fill="url(#revenueGradient)"
            dot={{ fill: CHART_COLORS.primary[0], strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: CHART_COLORS.primary[0], strokeWidth: 2 }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
