"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts";
import { formatCurrency, formatChartDate, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { SalesTrendData, AnalyticsFilters } from "@/lib/types/analytics";

interface AverageOrderValueChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function AverageOrderValueChart({ filters, className }: AverageOrderValueChartProps) {
  const [data, setData] = useState<SalesTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      if (filters.dateRange.from) {
        params.append("from", filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append("to", filters.dateRange.to.toISOString());
      }
      if (filters.cashierIds?.length) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.categoryIds?.length) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods?.length) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }
      if (filters.terminalIds?.length) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }

      const response = await fetch(`/api/analytics/sales-trends?${params}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch AOV trends data: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch AOV trends data");
      }

      setData(result.data || []);
    } catch (err) {
      console.error("Error fetching AOV trends data:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{formatChartDate(data.date)}</p>
          <p className="text-sm text-gray-600 mb-1">
            Average Order Value: {formatCurrency(data.averageOrderValue)}
          </p>
          <p className="text-sm text-gray-600 mb-1">
            Total Revenue: {formatCurrency(data.revenue)}
          </p>
          <p className="text-sm text-gray-600">
            Transactions: {data.transactions.toLocaleString()}
          </p>
        </div>
      );
    }
    return null;
  };

  // Calculate average AOV for the period
  const averageAOV =
    data.length > 0 ? data.reduce((sum, item) => sum + item.averageOrderValue, 0) / data.length : 0;

  return (
    <BaseChart
      title="Average Order Value Trends"
      subtitle={`Average AOV: ${formatCurrency(averageAOV)}`}
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <defs>
            <linearGradient id="aovGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={CHART_COLORS.info[0]} stopOpacity={0.8} />
              <stop offset="95%" stopColor={CHART_COLORS.info[0]} stopOpacity={0.1} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="date"
            tickFormatter={(value) => formatChartDate(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis stroke="#666" fontSize={12} tickFormatter={(value) => formatCurrency(value)} />
          <Tooltip content={<CustomTooltip />} />
          <Area
            type="monotone"
            dataKey="averageOrderValue"
            stroke={CHART_COLORS.info[0]}
            strokeWidth={2}
            fill="url(#aovGradient)"
            dot={{ fill: CHART_COLORS.info[0], strokeWidth: 2, r: 4 }}
            activeDot={{ r: 6, stroke: CHART_COLORS.info[0], strokeWidth: 2 }}
          />
        </AreaChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
