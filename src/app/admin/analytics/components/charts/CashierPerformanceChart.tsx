"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import { formatCurrency, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { CashierPerformanceData, AnalyticsFilters } from "@/lib/types/analytics";

interface CashierPerformanceChartProps {
  filters: AnalyticsFilters;
  className?: string;
  limit?: number;
}

export function CashierPerformanceChart({
  filters,
  className,
  limit = 10,
}: CashierPerformanceChartProps) {
  const [data, setData] = useState<CashierPerformanceData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      // Date range
      if (filters.dateRange.from && filters.dateRange.to) {
        params.append("fromDate", filters.dateRange.from.toISOString());
        params.append("toDate", filters.dateRange.to.toISOString());
      }

      // Other filters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      // Add limit
      params.append("limit", limit.toString());

      console.log("[CashierPerformanceChart] Fetching data with params:", params.toString());

      const response = await fetch(`/api/analytics/cashier-performance?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      console.log("Cashier performance result success:", result.success);

      if (!result.success) {
        console.error("Cashier performance result error:", result.error);
        throw new Error(result.error || "Failed to fetch cashier performance");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching cashier performance:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters, limit]);

  const handleRefresh = () => {
    fetchData();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length && payload[0]?.payload) {
      const data = payload[0].payload;

      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.name || "Unknown"}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">
              <span className="text-blue-600">Revenue: </span>
              <span className="font-medium">{formatCurrency(data.revenue || 0)}</span>
            </p>
            <p className="text-sm">
              <span className="text-green-600">Transactions: </span>
              <span className="font-medium">{(data.transactions || 0).toLocaleString()}</span>
            </p>
            <p className="text-sm">
              <span className="text-purple-600">Avg Order Value: </span>
              <span className="font-medium">{formatCurrency(data.averageOrderValue || 0)}</span>
            </p>
            <p className="text-sm">
              <span className="text-orange-600">Efficiency: </span>
              <span className="font-medium">{((data.efficiency ?? 0) || 0).toFixed(1)}%</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Prepare chart data with truncated names for display
  const chartData = Array.isArray(data)
    ? data.map((item) => ({
        ...item,
        displayName:
          item.name && item.name.length > 15
            ? `${item.name.substring(0, 15)}...`
            : item.name || "Unknown",
      }))
    : [];

  return (
    <BaseChart
      title="Cashier Performance"
      subtitle={`Top performing cashiers by revenue (Top ${limit})`}
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="displayName"
            stroke="#666"
            fontSize={11}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis
            yAxisId="revenue"
            orientation="left"
            tickFormatter={(value) => formatCurrency(value)}
            stroke="#666"
            fontSize={12}
          />
          <YAxis yAxisId="transactions" orientation="right" stroke="#666" fontSize={12} />
          <Tooltip content={<CustomTooltip />} />
          <Legend />
          <Bar
            yAxisId="revenue"
            dataKey="revenue"
            fill={CHART_COLORS.primary[0]}
            radius={[4, 4, 0, 0]}
            name="Revenue"
          />
          <Bar
            yAxisId="transactions"
            dataKey="transactions"
            fill={CHART_COLORS.success[0]}
            radius={[4, 4, 0, 0]}
            name="Transactions"
          />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
