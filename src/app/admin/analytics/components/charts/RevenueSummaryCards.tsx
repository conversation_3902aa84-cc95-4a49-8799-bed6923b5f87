"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LineC<PERSON>, Line, ResponsiveContainer } from "recharts";
import { formatCurrency, getGrowthIndicator, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { RevenueSummary, AnalyticsFilters, SalesTrendData } from "@/lib/types/analytics";
import { DollarSign, ShoppingCart, TrendingUp, Users, Loader2, AlertCircle } from "lucide-react";

interface RevenueSummaryCardsProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function RevenueSummaryCards({ filters, className }: RevenueSummaryCardsProps) {
  const [summaryData, setSummaryData] = useState<RevenueSummary | null>(null);
  const [trendsData, setTrendsData] = useState<SalesTrendData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      // Add custom date range or preset
      if (!filters.dateRange.from || !filters.dateRange.to) {
        throw new Error("Date range is required");
      }

      const daysDiff = Math.ceil(
        (filters.dateRange.to.getTime() - filters.dateRange.from.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Always pass custom dates for precise filtering
      params.append("fromDate", filters.dateRange.from.toISOString());
      params.append("toDate", filters.dateRange.to.toISOString());

      // Also pass preset for backward compatibility
      if (daysDiff <= 7) {
        params.append("dateRange", "7d");
      } else if (daysDiff <= 30) {
        params.append("dateRange", "30d");
      } else if (daysDiff <= 90) {
        params.append("dateRange", "90d");
      } else {
        params.append("dateRange", "1y");
      }

      // Add all filter parameters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      console.log("Fetching revenue data with params:", params.toString());

      // Fetch both summary and trends data
      const [summaryResponse, trendsResponse] = await Promise.all([
        fetch(`/api/analytics/revenue-summary?${params.toString()}`),
        fetch(`/api/analytics/sales-trends?${params.toString()}`),
      ]);

      console.log(
        "Response status - Summary:",
        summaryResponse.status,
        "Trends:",
        trendsResponse.status
      );

      if (!summaryResponse.ok) {
        const summaryError = await summaryResponse.text();
        console.error("Revenue summary API error:", summaryError);
        throw new Error(
          `Failed to fetch revenue summary: ${summaryResponse.status} ${summaryResponse.statusText}`
        );
      }

      if (!trendsResponse.ok) {
        const trendsError = await trendsResponse.text();
        console.error("Sales trends API error:", trendsError);
        throw new Error(
          `Failed to fetch sales trends: ${trendsResponse.status} ${trendsResponse.statusText}`
        );
      }

      const [summaryResult, trendsResult] = await Promise.all([
        summaryResponse.json(),
        trendsResponse.json(),
      ]);

      console.log(
        "API Results - Summary success:",
        summaryResult.success,
        "Trends success:",
        trendsResult.success
      );

      if (!summaryResult.success) {
        console.error("Revenue summary result error:", summaryResult.error);
        throw new Error(summaryResult.error || "Failed to fetch revenue summary");
      }

      if (!trendsResult.success) {
        console.error("Sales trends result error:", trendsResult.error);
        throw new Error(trendsResult.error || "Failed to fetch sales trends");
      }

      setSummaryData(summaryResult.data);
      setTrendsData(trendsResult.data);
      console.log("Revenue data fetched successfully");
    } catch (err) {
      console.error("Error fetching revenue summary:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  if (isLoading) {
    return (
      <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              </CardTitle>
              <div className="h-4 w-4 bg-muted animate-pulse rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-32 bg-muted animate-pulse rounded mb-2" />
              <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">Error loading revenue summary: {error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!summaryData) {
    return null;
  }

  // Create sparkline data from trends
  const sparklineData = trendsData.slice(-7).map((item) => ({ value: item.revenue }));

  const cards = [
    {
      title: "Total Revenue",
      value: summaryData.totalRevenue,
      growth: summaryData.revenueGrowth,
      icon: DollarSign,
      formatter: formatCurrency,
    },
    {
      title: "Total Transactions",
      value: summaryData.totalTransactions,
      growth: summaryData.transactionGrowth,
      icon: ShoppingCart,
      formatter: (value: number) => value.toLocaleString(),
    },
    {
      title: "Average Order Value",
      value: summaryData.averageOrderValue,
      growth: summaryData.aovGrowth,
      icon: TrendingUp,
      formatter: formatCurrency,
    },
    {
      title: "Active Customers",
      value: summaryData.activeCustomers,
      growth: summaryData.customerGrowth,
      icon: Users,
      formatter: (value: number) => value.toLocaleString(),
    },
  ];

  return (
    <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {cards.map((card, index) => {
        const growthIndicator = getGrowthIndicator(card.growth);
        const Icon = card.icon;

        return (
          <Card key={card.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.formatter(card.value)}</div>
              <div className="flex items-center justify-between mt-2">
                <p className="text-xs text-muted-foreground">
                  <span className={growthIndicator.color}>{growthIndicator.text}</span> from last
                  period
                </p>
                {sparklineData.length > 0 && (
                  <div className="h-8 w-16">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={sparklineData}>
                        <Line
                          type="monotone"
                          dataKey="value"
                          stroke={
                            card.growth >= 0 ? CHART_COLORS.success[0] : CHART_COLORS.danger[0]
                          }
                          strokeWidth={1.5}
                          dot={false}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
