"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Toolt<PERSON>, Legend } from "recharts";
import { formatCurrency, formatPercentage, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { PaymentMethodData, AnalyticsFilters } from "@/lib/types/analytics";

interface PaymentMethodsChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function PaymentMethodsChart({ filters, className }: PaymentMethodsChartProps) {
  const [data, setData] = useState<PaymentMethodData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      // Add custom date range or preset
      if (!filters.dateRange.from || !filters.dateRange.to) {
        throw new Error("Date range is required");
      }

      const daysDiff = Math.ceil(
        (filters.dateRange.to.getTime() - filters.dateRange.from.getTime()) / (1000 * 60 * 60 * 24)
      );

      // Always pass custom dates for precise filtering
      params.append("fromDate", filters.dateRange.from.toISOString());
      params.append("toDate", filters.dateRange.to.toISOString());

      // Also pass preset for backward compatibility
      if (daysDiff <= 7) {
        params.append("dateRange", "7d");
      } else if (daysDiff <= 30) {
        params.append("dateRange", "30d");
      } else if (daysDiff <= 90) {
        params.append("dateRange", "90d");
      } else {
        params.append("dateRange", "1y");
      }

      // Add all filter parameters
      if (filters.cashierIds && filters.cashierIds.length > 0) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.terminalIds && filters.terminalIds.length > 0) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods && filters.paymentMethods.length > 0) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }

      console.log("[PaymentMethodsChart] Fetching with params:", params.toString());
      console.log("[PaymentMethodsChart] Filters:", filters);

      const response = await fetch(`/api/analytics/payment-methods?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch payment methods: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch payment methods");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching payment methods:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900">{data.method}</p>
          <div className="mt-2 space-y-1">
            <p className="text-sm">
              <span className="text-blue-600">Amount: </span>
              <span className="font-medium">{formatCurrency(data.amount)}</span>
            </p>
            <p className="text-sm">
              <span className="text-green-600">Transactions: </span>
              <span className="font-medium">{data.count.toLocaleString()}</span>
            </p>
            <p className="text-sm">
              <span className="text-purple-600">Percentage: </span>
              <span className="font-medium">{formatPercentage(data.percentage)}</span>
            </p>
          </div>
        </div>
      );
    }
    return null;
  };

  // Custom legend
  const CustomLegend = ({ payload }: any) => {
    return (
      <div className="flex flex-wrap justify-center gap-4 mt-4">
        {payload.map((entry: any, index: number) => (
          <div key={index} className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
            <span className="text-sm text-gray-600">{entry.value}</span>
          </div>
        ))}
      </div>
    );
  };

  // Prepare data for donut chart
  const chartData = data.map((item, index) => ({
    ...item,
    name: item.method,
    value: item.amount,
    fill: CHART_COLORS.mixed[index % CHART_COLORS.mixed.length],
  }));

  // Calculate total for center display
  const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);
  const totalTransactions = data.reduce((sum, item) => sum + item.count, 0);

  return (
    <BaseChart
      title="Payment Methods"
      subtitle="Distribution of payment methods by revenue"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <div className="space-y-4">
        {/* Chart Area */}
        <ResponsiveContainer width="100%" height={350}>
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={110}
              paddingAngle={2}
              dataKey="value"
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
            <Tooltip content={<CustomTooltip />} />
            <Legend content={<CustomLegend />} />
          </PieChart>
        </ResponsiveContainer>

        {/* External Statistics Card - Responsive Design */}
        <div className="bg-gray-50 rounded-lg p-3 md:p-4 border border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4 text-center">
            <div className="bg-white rounded-md p-3 shadow-sm">
              <p className="text-xs md:text-sm text-gray-600 mb-1">Total Revenue</p>
              <p className="text-base md:text-lg font-semibold text-gray-900">
                {formatCurrency(totalAmount)}
              </p>
            </div>
            <div className="bg-white rounded-md p-3 shadow-sm">
              <p className="text-xs md:text-sm text-gray-600 mb-1">Total Transactions</p>
              <p className="text-base md:text-lg font-semibold text-gray-900">
                {totalTransactions.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>
    </BaseChart>
  );
}
