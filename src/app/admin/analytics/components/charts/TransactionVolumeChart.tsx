"use client";

import { useState, useEffect } from "react";
import { BaseChart } from "@/components/charts/BaseChart";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts";
import { formatCurrency, CHART_COLORS } from "@/lib/analytics/chartUtils";
import { TransactionVolumeData, AnalyticsFilters } from "@/lib/types/analytics";

interface TransactionVolumeChartProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function TransactionVolumeChart({ filters, className }: TransactionVolumeChartProps) {
  const [data, setData] = useState<TransactionVolumeData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      if (filters.dateRange.from) {
        params.append("from", filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append("to", filters.dateRange.to.toISOString());
      }
      if (filters.cashierIds?.length) {
        params.append("cashierIds", filters.cashierIds.join(","));
      }
      if (filters.categoryIds?.length) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }
      if (filters.paymentMethods?.length) {
        params.append("paymentMethods", filters.paymentMethods.join(","));
      }
      if (filters.terminalIds?.length) {
        params.append("terminalIds", filters.terminalIds.join(","));
      }

      const response = await fetch(`/api/analytics/transaction-volume?${params}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch transaction volume data: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || "Failed to fetch transaction volume data");
      }

      setData(result.data || []);
    } catch (err) {
      console.error("Error fetching transaction volume data:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{data.range}</p>
          <p className="text-sm text-gray-600 mb-1">Transactions: {data.count.toLocaleString()}</p>
          <p className="text-sm text-gray-600">Percentage: {(data.percentage ?? 0).toFixed(1)}%</p>
        </div>
      );
    }
    return null;
  };

  // Transform data for chart display with colors
  const chartData = data.map((item, index) => ({
    ...item,
    fill: CHART_COLORS.primary[index % CHART_COLORS.primary.length],
  }));

  return (
    <BaseChart
      title="Transaction Volume Distribution"
      subtitle="Distribution of transactions by amount ranges"
      isLoading={isLoading}
      error={error}
      onRefresh={handleRefresh}
      className={className}
    >
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="range"
            stroke="#666"
            fontSize={12}
            angle={-45}
            textAnchor="end"
            height={80}
          />
          <YAxis stroke="#666" fontSize={12} tickFormatter={(value) => value.toLocaleString()} />
          <Tooltip content={<CustomTooltip />} />
          <Bar dataKey="count" fill={CHART_COLORS.primary[0]} radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    </BaseChart>
  );
}
