"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  RefreshCw,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  DollarSign,
  Package,
  Target,
  BarChart3,
  ShoppingCart,
  Calendar,
} from "lucide-react";
import { formatCurrency, formatPercentage } from "@/lib/analytics/chartUtils";
import { AnalyticsFilters } from "@/lib/types/analytics";

interface ProductInsightsCardProps {
  filters: AnalyticsFilters;
  className?: string;
}

interface ProductInsight {
  name: string;
  sku: string;
  category: string;
  turnoverRate?: number;
  totalSold?: number;
  daysSinceLastSale?: number;
  profitMargin?: number;
  basePrice?: number;
  purchasePrice?: number;
  currentMargin?: number;
  suggestedAction?: string;
  potentialIncrease?: number;
  currentStock?: number;
  lastSaleValue?: number;
  suggestedDiscount?: number;
}

interface ProductInsightsData {
  stockTurnover: {
    averageRate: number;
    fastMoving: ProductInsight[];
    slowMoving: ProductInsight[];
  };
  profitMargins: {
    averageMargin: number;
    highest: ProductInsight[];
    lowest: ProductInsight[];
  };
  priceOptimization: ProductInsight[];
  deadStock: ProductInsight[];
  promotionOpportunities: ProductInsight[];
  salesVelocity: {
    increasing: Array<{
      name: string;
      sku: string;
      category: string;
      trendValue: number;
      currentTurnover: number;
      totalSold: number;
    }>;
    decreasing: Array<{
      name: string;
      sku: string;
      category: string;
      trendValue: number;
      currentTurnover: number;
      totalSold: number;
    }>;
    stable: Array<{
      name: string;
      sku: string;
      category: string;
      currentTurnover: number;
      totalSold: number;
    }>;
  };
  bundleSuggestions: Array<{
    product1: { name: string; sku: string; category: string };
    product2: { name: string; sku: string; category: string };
    frequency: number;
    avgQuantity1: number;
    avgQuantity2: number;
    confidence: number;
  }>;
  seasonalTrends: {
    highlySeasonal: Array<{
      name: string;
      sku: string;
      category: string;
      seasonalityScore: number;
      bestMonth: string;
      bestMonthQuantity: number;
      worstMonth: string;
      worstMonthQuantity: number;
    }>;
    stable: Array<{
      name: string;
      sku: string;
      category: string;
      seasonalityScore: number;
      avgMonthlyQuantity: number;
    }>;
    summary: {
      totalAnalyzed: number;
      highlySeasonalCount: number;
      stableCount: number;
    };
  };
  summary: {
    totalProducts: number;
    fastMovingCount: number;
    slowMovingCount: number;
    deadStockCount: number;
    highMarginCount: number;
    lowMarginCount: number;
    increasingTrendCount: number;
    decreasingTrendCount: number;
  };
}

export function ProductInsightsCard({ filters, className }: ProductInsightsCardProps) {
  const [data, setData] = useState<ProductInsightsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      if (filters.dateRange.from) {
        params.append("from", filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append("to", filters.dateRange.to.toISOString());
      }
      if (filters.categoryIds?.length) {
        params.append("categoryIds", filters.categoryIds.join(","));
      }

      console.log("[ProductInsightsCard] Fetching with params:", params.toString());

      const response = await fetch(`/api/analytics/product-insights?${params}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch product insights");
      }

      console.log("[ProductInsightsCard] Data received:", result.data);
      setData(result.data);
    } catch (err) {
      console.error("Error fetching product insights:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Additional Product Insights
            <RefreshCw className="h-4 w-4 animate-spin" />
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 text-muted-foreground mx-auto mb-2 animate-spin" />
              <p className="text-sm text-muted-foreground">Loading insights...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Additional Product Insights
            <button onClick={handleRefresh} className="p-1 hover:bg-gray-100 rounded">
              <RefreshCw className="h-4 w-4" />
            </button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-red-600">{error}</p>
              <button
                onClick={handleRefresh}
                className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200"
              >
                Try Again
              </button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!data) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Additional Product Insights</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <p className="text-sm text-muted-foreground">No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Additional Product Insights
          <button onClick={handleRefresh} className="p-1 hover:bg-gray-100 rounded">
            <RefreshCw className="h-4 w-4" />
          </button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Summary Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-6">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Total Products</span>
            </div>
            <p className="text-2xl font-bold text-blue-900">{data.summary.totalProducts}</p>
          </div>

          <div className="bg-green-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Fast Moving</span>
            </div>
            <p className="text-2xl font-bold text-green-900">{data.summary.fastMovingCount}</p>
          </div>

          <div className="bg-red-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-sm font-medium text-red-900">Dead Stock</span>
            </div>
            <p className="text-2xl font-bold text-red-900">{data.summary.deadStockCount}</p>
          </div>

          <div className="bg-emerald-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4 text-emerald-600" />
              <span className="text-sm font-medium text-emerald-900">Increasing</span>
            </div>
            <p className="text-2xl font-bold text-emerald-900">
              {data.summary.increasingTrendCount}
            </p>
          </div>

          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4 text-orange-600" />
              <span className="text-sm font-medium text-orange-900">Decreasing</span>
            </div>
            <p className="text-2xl font-bold text-orange-900">
              {data.summary.decreasingTrendCount}
            </p>
          </div>

          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="flex items-center gap-2">
              <ShoppingCart className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-900">Bundle Pairs</span>
            </div>
            <p className="text-2xl font-bold text-purple-900">{data.bundleSuggestions.length}</p>
          </div>
        </div>

        {/* Insights Tabs */}
        <Tabs defaultValue="turnover" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="turnover">Stock Turnover</TabsTrigger>
            <TabsTrigger value="margins">Profit Margins</TabsTrigger>
            <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
            <TabsTrigger value="velocity">Sales Velocity</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
          </TabsList>

          <TabsContent value="turnover" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {/* Fast Moving Products */}
              <div>
                <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Fast Moving Products
                </h4>
                <div className="space-y-2">
                  {data.stockTurnover.fastMoving.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {product.turnoverRate?.toFixed(2)}/day
                        </Badge>
                      </div>
                      <p className="text-xs text-green-700 mt-1">{product.totalSold} units sold</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Slow Moving Products */}
              <div>
                <h4 className="font-semibold text-orange-700 mb-2 flex items-center gap-2">
                  <TrendingDown className="h-4 w-4" />
                  Slow Moving Products
                </h4>
                <div className="space-y-2">
                  {data.stockTurnover.slowMoving.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                          {product.turnoverRate?.toFixed(2)}/day
                        </Badge>
                      </div>
                      <p className="text-xs text-orange-700 mt-1">
                        Last sale: {product.daysSinceLastSale} days ago
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="margins" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {/* Highest Margins */}
              <div>
                <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Highest Profit Margins
                </h4>
                <div className="space-y-2">
                  {data.profitMargins.highest.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {formatPercentage(product.profitMargin || 0)}
                        </Badge>
                      </div>
                      <p className="text-xs text-green-700 mt-1">
                        Sale: {formatCurrency(product.basePrice || 0)} • Cost:{" "}
                        {formatCurrency(product.purchasePrice || 0)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Lowest Margins */}
              <div>
                <h4 className="font-semibold text-red-700 mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Lowest Profit Margins
                </h4>
                <div className="space-y-2">
                  {data.profitMargins.lowest.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          {formatPercentage(product.profitMargin || 0)}
                        </Badge>
                      </div>
                      <p className="text-xs text-red-700 mt-1">
                        Sale: {formatCurrency(product.basePrice || 0)} • Cost:{" "}
                        {formatCurrency(product.purchasePrice || 0)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="opportunities" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {/* Price Optimization */}
              <div>
                <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Price Optimization
                </h4>
                <div className="space-y-2">
                  {data.priceOptimization.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          +{formatPercentage(product.potentialIncrease || 0)}
                        </Badge>
                      </div>
                      <p className="text-xs text-blue-700 mt-1">{product.suggestedAction}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Promotion Opportunities */}
              <div>
                <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Promotion Opportunities
                </h4>
                <div className="space-y-2">
                  {data.promotionOpportunities.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          -{formatPercentage(product.suggestedDiscount || 0)}
                        </Badge>
                      </div>
                      <p className="text-xs text-purple-700 mt-1">
                        Stock: {product.currentStock} • Margin:{" "}
                        {formatPercentage(product.profitMargin || 0)}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Dead Stock */}
            {data.deadStock.length > 0 && (
              <div>
                <h4 className="font-semibold text-red-700 mb-2 flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4" />
                  Dead Stock (No Sales 30+ Days)
                </h4>
                <div className="grid md:grid-cols-2 gap-2">
                  {data.deadStock.slice(0, 4).map((product, index) => (
                    <div key={index} className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-red-100 text-red-800">
                          {product.daysSinceLastSale}d
                        </Badge>
                      </div>
                      <p className="text-xs text-red-700 mt-1">Stock: {product.currentStock}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          {/* Sales Velocity Tab */}
          <TabsContent value="velocity" className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              {/* Increasing Trends */}
              <div>
                <h4 className="font-semibold text-emerald-700 mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Increasing Sales Trends
                </h4>
                <div className="space-y-2">
                  {data.salesVelocity.increasing.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-emerald-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-emerald-100 text-emerald-800">
                          +{formatPercentage(product.trendValue)}
                        </Badge>
                      </div>
                      <p className="text-xs text-emerald-700 mt-1">
                        Current: {product.currentTurnover.toFixed(2)}/day • Total:{" "}
                        {product.totalSold}
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Decreasing Trends */}
              <div>
                <h4 className="font-semibold text-orange-700 mb-2 flex items-center gap-2">
                  <TrendingDown className="h-4 w-4" />
                  Decreasing Sales Trends
                </h4>
                <div className="space-y-2">
                  {data.salesVelocity.decreasing.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                          -{formatPercentage(product.trendValue)}
                        </Badge>
                      </div>
                      <p className="text-xs text-orange-700 mt-1">
                        Current: {product.currentTurnover.toFixed(2)}/day • Total:{" "}
                        {product.totalSold}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Stable Products */}
            {data.salesVelocity.stable.length > 0 && (
              <div>
                <h4 className="font-semibold text-gray-700 mb-2 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Stable Sales Performance
                </h4>
                <div className="grid md:grid-cols-3 gap-2">
                  {data.salesVelocity.stable.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-gray-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                      </div>
                      <p className="text-xs text-gray-700 mt-1">
                        {product.currentTurnover.toFixed(2)}/day • {product.totalSold} sold
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </TabsContent>

          {/* Advanced Analytics Tab */}
          <TabsContent value="advanced" className="space-y-4">
            {/* Bundle Suggestions */}
            <div>
              <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Bundle Suggestions (Frequently Bought Together)
              </h4>
              <div className="space-y-2">
                {data.bundleSuggestions.slice(0, 5).map((bundle, index) => (
                  <div key={index} className="bg-purple-50 p-3 rounded border">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-sm">{bundle.product1.name}</span>
                          <span className="text-gray-400">+</span>
                          <span className="font-medium text-sm">{bundle.product2.name}</span>
                        </div>
                        <p className="text-xs text-gray-600">
                          {bundle.product1.category} • {bundle.product2.category}
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800 mb-1">
                          {bundle.confidence}% confidence
                        </Badge>
                        <p className="text-xs text-purple-700">
                          Bought together {bundle.frequency} times
                        </p>
                      </div>
                    </div>
                    <p className="text-xs text-purple-700 mt-1">
                      Avg quantities: {bundle.avgQuantity1} + {bundle.avgQuantity2}
                    </p>
                  </div>
                ))}
              </div>
            </div>

            {/* Seasonal Trends */}
            <div>
              <h4 className="font-semibold text-indigo-700 mb-2 flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Seasonal Trends Analysis
              </h4>

              {data.seasonalTrends.highlySeasonal.length > 0 ? (
                <div className="space-y-2">
                  {data.seasonalTrends.highlySeasonal.slice(0, 3).map((product, index) => (
                    <div key={index} className="bg-indigo-50 p-3 rounded border">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-sm">{product.name}</p>
                          <p className="text-xs text-gray-600">
                            {product.sku} • {product.category}
                          </p>
                        </div>
                        <Badge variant="secondary" className="bg-indigo-100 text-indigo-800">
                          {product.seasonalityScore}% seasonal
                        </Badge>
                      </div>
                      <div className="mt-2 grid grid-cols-2 gap-2 text-xs">
                        <div className="bg-green-100 p-2 rounded">
                          <p className="text-green-800 font-medium">Best: {product.bestMonth}</p>
                          <p className="text-green-700">{product.bestMonthQuantity} units</p>
                        </div>
                        <div className="bg-red-100 p-2 rounded">
                          <p className="text-red-800 font-medium">Worst: {product.worstMonth}</p>
                          <p className="text-red-700">{product.worstMonthQuantity} units</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 p-4 rounded border text-center">
                  <Calendar className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">
                    Not enough historical data for seasonal analysis
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Requires sales data across multiple months
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
