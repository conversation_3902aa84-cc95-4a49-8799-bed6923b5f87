"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import {
  RefreshCw,
  DollarSign,
  Clock,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Shield,
  Activity,
  Package,
  BarChart3,
  Target,
  Users,
  Zap,
  CheckCircle,
  Info,
} from "lucide-react";
import { OperationalMetricsData, AnalyticsFilters } from "@/lib/types/analytics";
import { formatCurrency, formatPercentage } from "@/lib/analytics/chartUtils";

interface OperationalMetricsCardProps {
  filters: AnalyticsFilters;
  className?: string;
}

export function OperationalMetricsCard({ filters, className }: OperationalMetricsCardProps) {
  const [data, setData] = useState<OperationalMetricsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();

      if (filters.dateRange.from) {
        params.append("from", filters.dateRange.from.toISOString());
      }
      if (filters.dateRange.to) {
        params.append("to", filters.dateRange.to.toISOString());
      }

      console.log("[OperationalMetricsCard] Fetching with params:", params.toString());

      const response = await fetch(`/api/analytics/operational-metrics?${params}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch operational metrics");
      }

      setData(result.data);
    } catch (err) {
      console.error("Error fetching operational metrics:", err);
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [filters]);

  const handleRefresh = () => {
    fetchData();
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Additional Operational Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Additional Operational Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[400px] flex items-center justify-center">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                {error || "No operational data available"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case "LOW":
        return "bg-green-100 text-green-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "HIGH":
        return "bg-orange-100 text-orange-800";
      case "CRITICAL":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getGradeColor = (grade: string) => {
    if (grade.startsWith("A")) return "bg-green-100 text-green-800";
    if (grade.startsWith("B")) return "bg-blue-100 text-blue-800";
    if (grade.startsWith("C")) return "bg-yellow-100 text-yellow-800";
    if (grade.startsWith("D")) return "bg-orange-100 text-orange-800";
    return "bg-red-100 text-red-800";
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case "IMPROVING":
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case "DECLINING":
        return <TrendingDown className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-blue-600" />;
    }
  };

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Additional Operational Metrics
            <button onClick={handleRefresh} className="p-1 hover:bg-gray-100 rounded">
              <RefreshCw className="h-4 w-4" />
            </button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Summary Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {/* Health Score */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-blue-50 p-3 rounded-lg cursor-help">
                  <div className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Health Score</span>
                    <Info className="h-3 w-3 text-blue-500" />
                  </div>
                  <p className="text-2xl font-bold text-blue-900">
                    {data.summary.overallHealthScore}
                  </p>
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="font-semibold">Overall Health Score (0-100)</p>
                  <p className="text-sm">Composite score calculated from:</p>
                  <ul className="text-xs space-y-0.5 ml-2">
                    <li>• Daily operational score</li>
                    <li>• Reconciliation accuracy</li>
                    <li>• Transaction processing efficiency</li>
                  </ul>
                  <p className="text-xs text-muted-foreground">
                    Higher scores indicate better operational health
                  </p>
                </div>
              </TooltipContent>
            </Tooltip>

            {/* Alerts */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-orange-50 p-3 rounded-lg cursor-help">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium text-orange-900">Alerts</span>
                    <Info className="h-3 w-3 text-orange-500" />
                  </div>
                  <p className="text-2xl font-bold text-orange-900">
                    {data.summary.alertsRequiringAttention}
                  </p>
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="font-semibold">Alerts Requiring Attention</p>
                  <p className="text-sm">Total count of active alerts from:</p>
                  <ul className="text-xs space-y-0.5 ml-2">
                    <li>• Pending cash reconciliations</li>
                    <li>• High severity security alerts</li>
                    <li>• Critical low stock alerts</li>
                  </ul>
                  <p className="text-xs text-muted-foreground">
                    Lower numbers indicate better operational control
                  </p>
                </div>
              </TooltipContent>
            </Tooltip>

            {/* Grade */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-green-50 p-3 rounded-lg cursor-help">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Grade</span>
                    <Info className="h-3 w-3 text-green-500" />
                  </div>
                  <p className="text-2xl font-bold text-green-900">
                    {data.performanceBenchmarks.dailyOperationalScore.grade}
                  </p>
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="font-semibold">Performance Grade (A+ to F)</p>
                  <p className="text-sm">Letter grade based on overall operational score:</p>
                  <ul className="text-xs space-y-0.5 ml-2">
                    <li>• A+/A: 93-100 (Excellent)</li>
                    <li>• B+/B: 80-92 (Good)</li>
                    <li>• C+/C: 70-79 (Average)</li>
                    <li>• D: 60-69 (Below Average)</li>
                    <li>• F: Below 60 (Poor)</li>
                  </ul>
                </div>
              </TooltipContent>
            </Tooltip>

            {/* Metrics */}
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="bg-purple-50 p-3 rounded-lg cursor-help">
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">Metrics</span>
                    <Info className="h-3 w-3 text-purple-500" />
                  </div>
                  <p className="text-2xl font-bold text-purple-900">{data.summary.totalMetrics}</p>
                </div>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="font-semibold">Total Metrics Tracked</p>
                  <p className="text-sm">Number of operational KPIs monitored across:</p>
                  <ul className="text-xs space-y-0.5 ml-2">
                    <li>• Cash Operations (8 metrics)</li>
                    <li>• System Performance (7 metrics)</li>
                    <li>• Inventory Operations (5 metrics)</li>
                    <li>• Audit & Compliance (5 metrics)</li>
                  </ul>
                  <p className="text-xs text-muted-foreground">
                    Comprehensive operational monitoring coverage
                  </p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Operational Metrics Tabs */}
          <Tabs defaultValue="cash" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="cash">Cash Ops</TabsTrigger>
              <TabsTrigger value="system">System</TabsTrigger>
              <TabsTrigger value="inventory">Inventory</TabsTrigger>
              <TabsTrigger value="audit">Audit</TabsTrigger>
              <TabsTrigger value="performance">Performance</TabsTrigger>
            </TabsList>

            {/* Cash Operations Tab */}
            <TabsContent value="cash" className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                {/* Drawer Sessions */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Drawer Sessions
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Sessions</span>
                        <span className="font-medium">
                          {data.cashOperations.drawerSessions.totalSessions}
                        </span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Avg Duration</span>
                        <span className="font-medium">
                          {data.cashOperations.drawerSessions.averageSessionDuration}m
                        </span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Utilization Rate</span>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {formatPercentage(data.cashOperations.drawerSessions.utilizationRate)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Cash Discrepancies */}
                <div>
                  <h4 className="font-semibold text-red-700 mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Cash Discrepancies
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Discrepancies</span>
                        <span className="font-medium">
                          {data.cashOperations.cashDiscrepancies.totalDiscrepancies}
                        </span>
                      </div>
                    </div>
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Average Amount</span>
                        <span className="font-medium">
                          {formatCurrency(data.cashOperations.cashDiscrepancies.averageDiscrepancy)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Large Discrepancies</span>
                        <Badge variant="destructive">
                          {data.cashOperations.cashDiscrepancies.largeDiscrepanciesCount}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Reconciliation Performance & Cash Flow */}
              <div className="grid md:grid-cols-2 gap-4">
                {/* Reconciliation Performance */}
                <div>
                  <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Reconciliation Performance
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Pending Count</span>
                        <span className="font-medium">
                          {data.cashOperations.reconciliationPerformance.pendingCount}
                        </span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Resolution Rate</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {formatPercentage(
                            data.cashOperations.reconciliationPerformance.resolutionRate
                          )}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Avg Resolution Time</span>
                        <span className="font-medium">
                          {data.cashOperations.reconciliationPerformance.averageResolutionTime}h
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Cash Flow */}
                <div>
                  <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Cash Flow
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Net Cash Flow</span>
                        <span className="font-medium">
                          {formatCurrency(data.cashOperations.cashFlow.netCashFlow)}
                        </span>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Avg Opening Balance</span>
                        <span className="font-medium">
                          {formatCurrency(data.cashOperations.cashFlow.averageOpeningBalance)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* System Performance Tab */}
            <TabsContent value="system" className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                {/* User Activity */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    User Activity
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Logins</span>
                        <span className="font-medium">
                          {data.systemPerformance.userActivity.totalLogins}
                        </span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Active Users</span>
                        <span className="font-medium">
                          {data.systemPerformance.userActivity.activeUsersCount}
                        </span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Peak Hour</span>
                        <span className="font-medium">
                          {data.systemPerformance.userActivity.peakHourActivity}:00
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Transaction Processing */}
                <div>
                  <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Transaction Processing
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Transactions/Hour</span>
                        <span className="font-medium">
                          {data.systemPerformance.transactionProcessing.transactionsPerHour}
                        </span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Processing Efficiency</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {formatPercentage(
                            data.systemPerformance.transactionProcessing.processingEfficiency
                          )}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Peak Hour Volume</span>
                        <span className="font-medium">
                          {data.systemPerformance.transactionProcessing.peakHourTransactions}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Error Rates & Peak Load */}
              <div className="grid md:grid-cols-2 gap-4">
                {/* Error Rates */}
                <div>
                  <h4 className="font-semibold text-red-700 mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Error Rates
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Failed Transactions</span>
                        <span className="font-medium">
                          {data.systemPerformance.errorRates.failedTransactions}
                        </span>
                      </div>
                    </div>
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Error Rate</span>
                        <Badge variant="destructive">
                          {formatPercentage(data.systemPerformance.errorRates.errorRate)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Peak Load Analysis */}
                <div>
                  <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Peak Load Analysis
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Busiest Hour</span>
                        <span className="font-medium">
                          {data.systemPerformance.peakLoadAnalysis.busiestHour}:00
                        </span>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">System Load</span>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          {formatPercentage(data.systemPerformance.peakLoadAnalysis.systemLoad)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Inventory Operations Tab */}
            <TabsContent value="inventory" className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                {/* Stock Adjustments */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Stock Adjustments
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Adjustments</span>
                        <span className="font-medium">
                          {data.inventoryOps.stockAdjustments.totalAdjustments}
                        </span>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Daily Frequency</span>
                        <span className="font-medium">
                          {data.inventoryOps.stockAdjustments.adjustmentFrequency}/day
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Adjustment Reasons */}
                  {data.inventoryOps.stockAdjustments.reasonBreakdown.length > 0 && (
                    <div className="mt-3">
                      <h5 className="text-sm font-medium mb-2">Adjustment Reasons</h5>
                      <div className="space-y-1">
                        {data.inventoryOps.stockAdjustments.reasonBreakdown
                          .slice(0, 3)
                          .map((reason, index) => (
                            <div key={index} className="bg-blue-50 p-2 rounded border">
                              <div className="flex justify-between items-center">
                                <span className="text-xs">{reason.reason}</span>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs font-medium">{reason.count}</span>
                                  <Badge
                                    variant="secondary"
                                    className="bg-blue-100 text-blue-800 text-xs"
                                  >
                                    {formatPercentage(reason.percentage)}
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Stock Movement */}
                <div>
                  <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    Stock Movement
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Fast Moving Items</span>
                        <span className="font-medium">
                          {data.inventoryOps.stockMovement.fastMovingItems}
                        </span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Slow Moving Items</span>
                        <span className="font-medium">
                          {data.inventoryOps.stockMovement.slowMovingItems}
                        </span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Stockout Events</span>
                        <Badge variant="destructive">
                          {data.inventoryOps.stockMovement.stockoutEvents}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Low Stock Alerts & Transfer Efficiency */}
              <div className="grid md:grid-cols-2 gap-4">
                {/* Low Stock Alerts */}
                <div>
                  <h4 className="font-semibold text-orange-700 mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Low Stock Alerts
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Alerts</span>
                        <span className="font-medium">
                          {data.inventoryOps.lowStockAlerts.totalAlerts}
                        </span>
                      </div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Critical Alerts</span>
                        <Badge variant="destructive">
                          {data.inventoryOps.lowStockAlerts.criticalAlerts}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Transfer Efficiency */}
                <div>
                  <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Transfer Efficiency
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Completion Rate</span>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          {formatPercentage(data.inventoryOps.transferEfficiency.completionRate)}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Transfer Accuracy</span>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          {formatPercentage(data.inventoryOps.transferEfficiency.transferAccuracy)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Audit & Compliance Tab */}
            <TabsContent value="audit" className="space-y-4">
              <div className="grid md:grid-cols-2 gap-4">
                {/* Security Alerts */}
                <div>
                  <h4 className="font-semibold text-red-700 mb-2 flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Security Alerts
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Total Alerts</span>
                        <span className="font-medium">
                          {data.auditCompliance.securityAlerts.totalAlerts}
                        </span>
                      </div>
                    </div>
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">High Severity</span>
                        <Badge variant="destructive">
                          {data.auditCompliance.securityAlerts.highSeverityAlerts}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-red-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Avg Resolution Time</span>
                        <span className="font-medium">
                          {data.auditCompliance.securityAlerts.averageResolutionTime}h
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Compliance Scores */}
                <div>
                  <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    Compliance Scores
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Reconciliation Accuracy</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {formatPercentage(
                            data.auditCompliance.complianceScores.reconciliationAccuracy
                          )}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Audit Trail Completeness</span>
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          {formatPercentage(
                            data.auditCompliance.complianceScores.auditTrailCompleteness
                          )}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Compliance Rating</span>
                        <Badge
                          variant="secondary"
                          className={getGradeColor(
                            data.auditCompliance.complianceScores.complianceRating
                          )}
                        >
                          {data.auditCompliance.complianceScores.complianceRating}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Risk Indicators & Resolution Efficiency */}
              <div className="grid md:grid-cols-2 gap-4">
                {/* Risk Indicators */}
                <div>
                  <h4 className="font-semibold text-orange-700 mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Risk Indicators
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Risk Score</span>
                        <span className="font-medium">
                          {data.auditCompliance.riskIndicators.riskScore}/100
                        </span>
                      </div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Risk Level</span>
                        <Badge
                          variant="secondary"
                          className={getRiskLevelColor(
                            data.auditCompliance.riskIndicators.riskLevel
                          )}
                        >
                          {data.auditCompliance.riskIndicators.riskLevel}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Large Discrepancies</span>
                        <Badge variant="destructive">
                          {data.auditCompliance.riskIndicators.largeDiscrepancyFrequency}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Resolution Efficiency */}
                <div>
                  <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Resolution Efficiency
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">First Time Resolution</span>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {formatPercentage(
                            data.auditCompliance.resolutionEfficiency.firstTimeResolutionRate
                          )}
                        </Badge>
                      </div>
                    </div>
                    <div className="bg-blue-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Escalation Rate</span>
                        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                          {formatPercentage(
                            data.auditCompliance.resolutionEfficiency.escalationRate
                          )}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            {/* Performance Benchmarks Tab */}
            <TabsContent value="performance" className="space-y-4">
              {/* Daily Operational Score */}
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border">
                <h4 className="font-semibold text-blue-700 mb-3 flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Daily Operational Score
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-900 mb-1">
                      {data.performanceBenchmarks.dailyOperationalScore.overallScore}
                    </div>
                    <div className="text-sm text-blue-700">Overall Score</div>
                    <Badge
                      variant="secondary"
                      className={getGradeColor(
                        data.performanceBenchmarks.dailyOperationalScore.grade
                      )}
                    >
                      {data.performanceBenchmarks.dailyOperationalScore.grade}
                    </Badge>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-blue-800 mb-1">
                      {
                        data.performanceBenchmarks.dailyOperationalScore.scoreBreakdown
                          .cashManagement
                      }
                    </div>
                    <div className="text-xs text-blue-600">Cash Management</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-green-800 mb-1">
                      {
                        data.performanceBenchmarks.dailyOperationalScore.scoreBreakdown
                          .systemPerformance
                      }
                    </div>
                    <div className="text-xs text-green-600">System Performance</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-purple-800 mb-1">
                      {data.performanceBenchmarks.dailyOperationalScore.scoreBreakdown.inventoryOps}
                    </div>
                    <div className="text-xs text-purple-600">Inventory Ops</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-orange-800 mb-1">
                      {data.performanceBenchmarks.dailyOperationalScore.scoreBreakdown.compliance}
                    </div>
                    <div className="text-xs text-orange-600">Compliance</div>
                  </div>
                </div>
              </div>

              <div className="grid md:grid-cols-2 gap-4">
                {/* Trend Comparisons */}
                <div>
                  <h4 className="font-semibold text-green-700 mb-2 flex items-center gap-2">
                    {getTrendIcon(data.performanceBenchmarks.trendComparisons.trendDirection)}
                    Trend Comparisons
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Trend Direction</span>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(data.performanceBenchmarks.trendComparisons.trendDirection)}
                          <span className="font-medium">
                            {data.performanceBenchmarks.trendComparisons.trendDirection}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Week over Week</span>
                        <span className="font-medium">
                          {formatPercentage(
                            data.performanceBenchmarks.trendComparisons.weekOverWeek
                          )}
                        </span>
                      </div>
                    </div>
                    <div className="bg-green-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Month over Month</span>
                        <span className="font-medium">
                          {formatPercentage(
                            data.performanceBenchmarks.trendComparisons.monthOverMonth
                          )}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Efficiency Ratios */}
                <div>
                  <h4 className="font-semibold text-purple-700 mb-2 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    Efficiency Ratios
                  </h4>
                  <div className="space-y-2">
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Revenue/Hour</span>
                        <span className="font-medium">
                          {formatCurrency(
                            data.performanceBenchmarks.efficiencyRatios.revenuePerHour
                          )}
                        </span>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Transactions/Cashier</span>
                        <span className="font-medium">
                          {data.performanceBenchmarks.efficiencyRatios.transactionsPerCashier}
                        </span>
                      </div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded border">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Profit Margin</span>
                        <Badge variant="secondary" className="bg-purple-100 text-purple-800">
                          {formatPercentage(
                            data.performanceBenchmarks.efficiencyRatios.profitMarginRatio
                          )}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Target vs Actual */}
              <div>
                <h4 className="font-semibold text-blue-700 mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Target vs Actual Performance
                </h4>
                <div className="bg-blue-50 p-4 rounded border">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-blue-900 mb-1">
                        {formatCurrency(data.performanceBenchmarks.targetVsActual.actualRevenue)}
                      </div>
                      <div className="text-sm text-blue-700">Actual Revenue</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-green-900 mb-1">
                        {formatCurrency(data.performanceBenchmarks.targetVsActual.revenueTarget)}
                      </div>
                      <div className="text-sm text-green-700">Revenue Target</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-900 mb-1">
                        {formatPercentage(
                          data.performanceBenchmarks.targetVsActual.targetAchievement
                        )}
                      </div>
                      <div className="text-sm text-purple-700">Achievement</div>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
