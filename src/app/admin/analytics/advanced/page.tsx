"use client";

import React, { useState } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Brain, 
  TrendingUp, 
  Calendar, 
  BarChart3, 
  AlertTriangle,
  Lightbulb,
  Target,
  Zap
} from "lucide-react";
import { DemandForecastingDashboard } from "@/components/analytics/DemandForecastingDashboard";
import { SeasonalPredictionsDashboard } from "@/components/analytics/SeasonalPredictionsDashboard";
import { useClientAuth } from "@/hooks/use-client-auth";

export default function AdvancedAnalyticsPage() {
  const { user } = useClientAuth();
  const [selectedSupplierId, setSelectedSupplierId] = useState<string>("");
  const [suppliers, setSuppliers] = useState<any[]>([]);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);

  // Check permissions
  const allowedRoles = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"];
  const hasAccess = user && allowedRoles.includes(user.role);

  // Load suppliers on component mount
  const loadSuppliers = async () => {
    setIsLoadingSuppliers(true);
    try {
      const response = await fetch("/api/suppliers");
      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.suppliers || []);
        
        // Auto-select first supplier if available
        if (data.suppliers && data.suppliers.length > 0 && !selectedSupplierId) {
          setSelectedSupplierId(data.suppliers[0].id);
        }
      }
    } catch (error) {
      console.error("Failed to load suppliers:", error);
    } finally {
      setIsLoadingSuppliers(false);
    }
  };

  // Load suppliers on first render
  React.useEffect(() => {
    if (hasAccess) {
      loadSuppliers();
    }
  }, [hasAccess]);

  if (!hasAccess) {
    return (
      <MainLayout>
        <div className="container mx-auto py-8">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You don't have permission to access advanced analytics. Contact your administrator.
            </AlertDescription>
          </Alert>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 space-y-6">
        {/* Page Header */}
        <PageHeader
          title="Advanced Analytics"
          description="AI-powered demand forecasting and seasonal performance predictions"
        />

        {/* Feature Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card className="border-blue-200 bg-blue-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-900">
                <Brain className="h-5 w-5" />
                Demand Forecasting
                <Badge variant="secondary" className="ml-auto">AI-Powered</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-blue-800">
              <p className="text-sm mb-4">
                Predict future demand for products using advanced machine learning algorithms 
                that analyze historical consumption patterns, seasonal trends, and supplier-specific factors.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs">
                  <Target className="h-3 w-3" />
                  <span>Reduce stockouts by 15-25%</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <TrendingUp className="h-3 w-3" />
                  <span>Optimize inventory carrying costs</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <Zap className="h-3 w-3" />
                  <span>Improve cash flow through better timing</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-purple-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-900">
                <Calendar className="h-5 w-5" />
                Seasonal Predictions
                <Badge variant="secondary" className="ml-auto">Predictive</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="text-purple-800">
              <p className="text-sm mb-4">
                Analyze historical supplier performance data to predict how suppliers will perform 
                during different seasons, helping prepare for seasonal variations in quality and delivery.
              </p>
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-xs">
                  <Target className="h-3 w-3" />
                  <span>Proactive seasonal planning</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <TrendingUp className="h-3 w-3" />
                  <span>Risk mitigation strategies</span>
                </div>
                <div className="flex items-center gap-2 text-xs">
                  <Zap className="h-3 w-3" />
                  <span>Anticipate price fluctuations</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Supplier Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Supplier Selection</CardTitle>
            <p className="text-sm text-muted-foreground">
              Select a supplier to analyze demand forecasting and seasonal performance predictions
            </p>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4">
              <div className="flex-1 max-w-md">
                <Select 
                  value={selectedSupplierId} 
                  onValueChange={setSelectedSupplierId}
                  disabled={isLoadingSuppliers}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={isLoadingSuppliers ? "Loading suppliers..." : "Select a supplier"} />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                        {supplier.contactPerson && (
                          <span className="text-muted-foreground ml-2">
                            ({supplier.contactPerson})
                          </span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {selectedSupplierId && (
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {suppliers.find(s => s.id === selectedSupplierId)?.name}
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Analytics Content */}
        {selectedSupplierId ? (
          <Tabs defaultValue="demand-forecasting" className="space-y-6">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="demand-forecasting" className="flex items-center gap-2">
                <Brain className="h-4 w-4" />
                Demand Forecasting
              </TabsTrigger>
              <TabsTrigger value="seasonal-predictions" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Seasonal Predictions
              </TabsTrigger>
            </TabsList>

            <TabsContent value="demand-forecasting">
              <DemandForecastingDashboard supplierId={selectedSupplierId} />
            </TabsContent>

            <TabsContent value="seasonal-predictions">
              <SeasonalPredictionsDashboard supplierId={selectedSupplierId} />
            </TabsContent>
          </Tabs>
        ) : (
          <Card>
            <CardContent className="py-12">
              <div className="text-center space-y-4">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
                <div>
                  <h3 className="text-lg font-medium">Select a Supplier to Begin</h3>
                  <p className="text-muted-foreground">
                    Choose a supplier from the dropdown above to view advanced analytics and predictions
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Information Panel */}
        <Card className="border-amber-200 bg-amber-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-900">
              <Lightbulb className="h-5 w-5" />
              How It Works
            </CardTitle>
          </CardHeader>
          <CardContent className="text-amber-800">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Demand Forecasting</h4>
                <ul className="text-sm space-y-1">
                  <li>• Analyzes 12+ months of transaction history</li>
                  <li>• Uses FIFO batch consumption patterns</li>
                  <li>• Applies seasonal adjustment factors</li>
                  <li>• Considers supplier-specific lead times</li>
                  <li>• Provides confidence scoring (10-95%)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Seasonal Predictions</h4>
                <ul className="text-sm space-y-1">
                  <li>• Analyzes 24+ months of supplier data</li>
                  <li>• Tracks quality, delivery, and pricing patterns</li>
                  <li>• Identifies seasonal risk factors</li>
                  <li>• Predicts capacity constraints</li>
                  <li>• Generates mitigation strategies</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
