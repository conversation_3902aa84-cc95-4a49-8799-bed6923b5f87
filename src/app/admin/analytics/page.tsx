"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { useAuth } from "@/hooks/use-auth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RevenueSummaryCards } from "./components/charts/RevenueSummaryCards";
import { SalesTrendsChart } from "./components/charts/SalesTrendsChart";
import { TopProductsChart } from "./components/charts/TopProductsChart";
import { PaymentMethodsChart } from "./components/charts/PaymentMethodsChart";
import { HourlyPatternsChart } from "./components/charts/HourlyPatternsChart";
import { CashierPerformanceChart } from "./components/charts/CashierPerformanceChart";
import { CategoryPerformanceChart } from "./components/charts/CategoryPerformanceChart";
import { DrawerSessionsChart } from "./components/charts/DrawerSessionsChart";
import { TransactionVolumeChart } from "./components/charts/TransactionVolumeChart";
import { AverageOrderValueChart } from "./components/charts/AverageOrderValueChart";
import { ProductInsightsCard } from "./components/charts/ProductInsightsCard";
import { OperationalMetricsCard } from "./components/charts/OperationalMetricsCard";
import { DailyTransactionCountChart } from "./components/charts/DailyTransactionCountChart";
import { DailyRevenueProfitChart } from "./components/charts/DailyRevenueProfitChart";
import { subDays, format } from "date-fns";
import { AnalyticsFilters } from "@/lib/types/analytics";
import { toast } from "sonner";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Calendar,
  CalendarIcon,
  RefreshCw,
  Info,
  X,
} from "lucide-react";

export default function AnalyticsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDateRange, setSelectedDateRange] = useState("7d");
  const [filters, setFilters] = useState<AnalyticsFilters>({
    dateRange: { from: subDays(new Date(), 7), to: new Date() },
    cashierIds: [],
    categoryIds: [],
    paymentMethods: [],
    terminalIds: [],
  });
  const [availableOptions, setAvailableOptions] = useState<{
    cashiers: { id: string; name: string }[];
    terminals: { id: string; name: string }[];
    categories: { id: string; name: string }[];
  }>({
    cashiers: [],
    terminals: [],
    categories: [],
  });

  // Role-based access control
  const isSuperAdmin = user?.role === "SUPER_ADMIN";
  const isFinanceAdmin = user?.role === "FINANCE_ADMIN";
  const isCashier = user?.role === "CASHIER";
  const hasAnalyticsAccess = isSuperAdmin || isFinanceAdmin || isCashier;

  // Handle unauthorized access in useEffect to avoid setState during render
  useEffect(() => {
    if (user && !hasAnalyticsAccess) {
      router.push("/dashboard");
    }
  }, [user, hasAnalyticsAccess, router]);

  const fetchAvailableOptions = async () => {
    try {
      const promises = [
        fetch("/api/users?role=CASHIER").then((res) => (res.ok ? res.json() : { users: [] })),
        fetch("/api/terminals").then((res) => (res.ok ? res.json() : { terminals: [] })),
        fetch("/api/categories").then((res) => (res.ok ? res.json() : { categories: [] })),
      ];

      const [cashiersData, terminalsData, categoriesData] = await Promise.all(promises);

      setAvailableOptions({
        cashiers: (Array.isArray(cashiersData) ? cashiersData : cashiersData.users || [])
          .filter((user: any) => user.role === "CASHIER")
          .map((user: any) => ({
            id: user.id,
            name: user.name,
          })),
        terminals: (Array.isArray(terminalsData)
          ? terminalsData
          : terminalsData.terminals || []
        ).map((terminal: any) => ({
          id: terminal.id,
          name: terminal.name,
        })),
        categories: (Array.isArray(categoriesData)
          ? categoriesData
          : categoriesData.categories || []
        ).map((category: any) => ({
          id: category.id,
          name: category.name,
        })),
      });
    } catch (error) {
      console.error("Error fetching available options:", error);
    }
  };

  // Load available options on component mount
  useEffect(() => {
    fetchAvailableOptions();
  }, []);

  const handleRefresh = () => {
    setIsLoading(true);
    // Trigger refresh for all charts
    toast.info("Refreshing analytics data...");
    setTimeout(() => {
      setIsLoading(false);
      toast.success("Analytics data refreshed");
    }, 1000);
  };

  const handleFilterChange = (key: keyof AnalyticsFilters, value: any) => {
    setFilters((prev) => {
      const newFilters = { ...prev, [key]: value };

      // Check if the date range is custom (not matching any preset)
      if (key === "dateRange") {
        const isCustomDateRange = !isPresetDateRange(newFilters.dateRange);
        if (isCustomDateRange) {
          setSelectedDateRange("custom");
        }
      }

      return newFilters;
    });
  };

  const isPresetDateRange = (dateRange: { from: Date | undefined; to: Date | undefined }) => {
    if (!dateRange.from || !dateRange.to) {
      return false;
    }

    const now = new Date();
    const daysDiff = Math.ceil(
      (dateRange.to.getTime() - dateRange.from.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Check if it matches any preset range (with some tolerance for time differences)
    const presets = [
      { days: 7, tolerance: 1 },
      { days: 30, tolerance: 2 },
      { days: 90, tolerance: 3 },
      { days: 365, tolerance: 5 },
    ];

    return presets.some((preset) => Math.abs(daysDiff - preset.days) <= preset.tolerance);
  };

  const handleDateRangeChange = (range: string) => {
    setSelectedDateRange(range);

    // Update advanced filters date range
    let fromDate: Date;
    let toDate = new Date();

    switch (range) {
      case "7d":
        fromDate = subDays(new Date(), 7);
        break;
      case "30d":
        fromDate = subDays(new Date(), 30);
        break;
      case "90d":
        fromDate = subDays(new Date(), 90);
        break;
      case "1y":
        fromDate = subDays(new Date(), 365);
        break;
      default:
        fromDate = subDays(new Date(), 30);
    }

    setFilters((prev) => ({
      ...prev,
      dateRange: { from: fromDate, to: toDate },
    }));
  };

  const clearFilter = (filterType: keyof AnalyticsFilters, value?: string) => {
    setFilters((prev) => {
      if (filterType === "dateRange") {
        return prev; // Don't clear date range this way
      }

      const currentArray = (prev[filterType] as string[]) || [];
      const newArray = value ? currentArray.filter((item) => item !== value) : [];

      return {
        ...prev,
        [filterType]: newArray,
      };
    });
  };

  const clearAllFilters = () => {
    setFilters((prev) => ({
      ...prev,
      cashierIds: [],
      categoryIds: [],
      paymentMethods: [],
      terminalIds: [],
    }));
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.cashierIds && filters.cashierIds.length > 0) count++;
    if (filters.categoryIds && filters.categoryIds.length > 0) count++;
    if (filters.paymentMethods && filters.paymentMethods.length > 0) count++;
    if (filters.terminalIds && filters.terminalIds.length > 0) count++;
    return count;
  };

  // Determine which tabs are available based on role
  const getAvailableTabs = () => {
    const tabs = [
      { value: "overview", label: "Overview", available: true },
      { value: "sales", label: "Sales", available: isSuperAdmin || isFinanceAdmin },
      { value: "products", label: "Products", available: isSuperAdmin || isFinanceAdmin },
      { value: "operations", label: "Operations", available: isSuperAdmin || isFinanceAdmin },
      { value: "customers", label: "Customers", available: false }, // Hidden - not yet implemented
    ];

    return tabs.filter((tab) => tab.available);
  };

  // Create filters object for charts
  const getFilters = (): AnalyticsFilters => {
    return filters;
  };

  // Show loading or return null while checking access
  if (!user || !hasAnalyticsAccess) {
    return null;
  }

  return (
    <MainLayout>
      <PageHeader
        title="Analytics & Business Intelligence"
        description="Comprehensive insights into your business performance and operations"
        actions={
          <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? "animate-spin" : ""}`} />
            Refresh
          </Button>
        }
      />

      {/* Filter Controls */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Date Range Presets */}
            <div className="flex flex-wrap items-center gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Date Range:</span>
                <div className="flex gap-1">
                  {[
                    { value: "7d", label: "7 Days" },
                    { value: "30d", label: "30 Days" },
                    { value: "90d", label: "3 Months" },
                    { value: "1y", label: "1 Year" },
                  ].map((range) => (
                    <Button
                      key={range.value}
                      variant={selectedDateRange === range.value ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleDateRangeChange(range.value)}
                    >
                      {range.label}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* Individual Filter Controls */}
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              {/* Start Date */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.dateRange.from ? (
                        format(filters.dateRange.from, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={filters.dateRange.from}
                      onSelect={(date) =>
                        handleFilterChange("dateRange", { ...filters.dateRange, from: date })
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* End Date */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {filters.dateRange.to ? (
                        format(filters.dateRange.to, "PPP")
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={filters.dateRange.to}
                      onSelect={(date) =>
                        handleFilterChange("dateRange", { ...filters.dateRange, to: date })
                      }
                      disabled={(date) =>
                        filters.dateRange.from ? date < filters.dateRange.from : false
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Cashier Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Cashier</Label>
                <Select
                  value={filters.cashierIds?.[0] || "ALL"}
                  onValueChange={(value) =>
                    handleFilterChange("cashierIds", value === "ALL" ? [] : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Cashiers" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Cashiers</SelectItem>
                    {availableOptions.cashiers.map((cashier) => (
                      <SelectItem key={cashier.id} value={cashier.id}>
                        {cashier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Terminal Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Terminal</Label>
                <Select
                  value={filters.terminalIds?.[0] || "ALL"}
                  onValueChange={(value) =>
                    handleFilterChange("terminalIds", value === "ALL" ? [] : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Terminals" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Terminals</SelectItem>
                    {availableOptions.terminals.map((terminal) => (
                      <SelectItem key={terminal.id} value={terminal.id}>
                        {terminal.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Category Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Category</Label>
                <Select
                  value={filters.categoryIds?.[0] || "ALL"}
                  onValueChange={(value) =>
                    handleFilterChange("categoryIds", value === "ALL" ? [] : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Categories</SelectItem>
                    {availableOptions.categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Payment Method Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Payment Method</Label>
                <Select
                  value={filters.paymentMethods?.[0] || "ALL"}
                  onValueChange={(value) =>
                    handleFilterChange("paymentMethods", value === "ALL" ? [] : [value])
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Methods" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Methods</SelectItem>
                    <SelectItem value="CASH">Cash</SelectItem>
                    <SelectItem value="DEBIT">Debit Card</SelectItem>
                    <SelectItem value="QRIS">QRIS</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Active Filters Display */}
            {getActiveFiltersCount() > 0 && (
              <div className="flex flex-wrap items-center gap-2">
                <span className="text-sm text-muted-foreground">Active filters:</span>

                {/* Cashier Filters */}
                {filters.cashierIds?.map((cashierId) => {
                  const cashier = availableOptions.cashiers.find((c) => c.id === cashierId);
                  return (
                    <Badge key={`cashier-${cashierId}`} variant="secondary" className="gap-1">
                      Cashier: {cashier?.name || cashierId.substring(0, 8) + "..."}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => clearFilter("cashierIds", cashierId)}
                      />
                    </Badge>
                  );
                })}

                {/* Terminal Filters */}
                {filters.terminalIds?.map((terminalId) => {
                  const terminal = availableOptions.terminals.find((t) => t.id === terminalId);
                  return (
                    <Badge key={`terminal-${terminalId}`} variant="secondary" className="gap-1">
                      Terminal: {terminal?.name || terminalId.substring(0, 8) + "..."}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => clearFilter("terminalIds", terminalId)}
                      />
                    </Badge>
                  );
                })}

                {/* Category Filters */}
                {filters.categoryIds?.map((categoryId) => {
                  const category = availableOptions.categories.find((c) => c.id === categoryId);
                  return (
                    <Badge key={`category-${categoryId}`} variant="secondary" className="gap-1">
                      Category: {category?.name || categoryId.substring(0, 8) + "..."}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => clearFilter("categoryIds", categoryId)}
                      />
                    </Badge>
                  );
                })}

                {/* Payment Method Filters */}
                {filters.paymentMethods?.map((method) => (
                  <Badge key={`payment-${method}`} variant="secondary" className="gap-1">
                    {method}
                    <X
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => clearFilter("paymentMethods", method)}
                    />
                  </Badge>
                ))}

                {/* Clear All Filters */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-muted-foreground hover:text-foreground"
                >
                  Clear all
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className={`grid w-full grid-cols-${getAvailableTabs().length}`}>
          {getAvailableTabs().map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics Cards */}
          <RevenueSummaryCards filters={getFilters()} />

          {/* Interactive Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <SalesTrendsChart filters={getFilters()} />
            <TopProductsChart filters={getFilters()} limit={5} />
          </div>

          {/* Payment Methods and Hourly Patterns Charts */}
          <div className="grid gap-6 md:grid-cols-2">
            <PaymentMethodsChart filters={getFilters()} />
            <HourlyPatternsChart filters={getFilters()} />
          </div>
        </TabsContent>

        {/* Sales Tab */}
        {(isSuperAdmin || isFinanceAdmin) && (
          <TabsContent value="sales" className="space-y-6">
            {/* Sales Trends and AOV Charts */}
            <div className="grid gap-6 md:grid-cols-2">
              <SalesTrendsChart filters={getFilters()} />
              <AverageOrderValueChart filters={getFilters()} />
            </div>

            {/* Transaction Volume and Drawer Sessions Charts */}
            <div className="grid gap-6 md:grid-cols-2">
              <TransactionVolumeChart filters={getFilters()} />
              <DrawerSessionsChart filters={getFilters()} />
            </div>

            {/* Daily Transaction Count and Revenue vs Profit Charts */}
            <div className="grid gap-6 md:grid-cols-2">
              <DailyTransactionCountChart filters={getFilters()} />
              <DailyRevenueProfitChart filters={getFilters()} />
            </div>
          </TabsContent>
        )}

        {/* Products Tab */}
        {(isSuperAdmin || isFinanceAdmin) && (
          <TabsContent value="products" className="space-y-6">
            {/* Product Performance Charts */}
            <div className="grid gap-6 md:grid-cols-2">
              <TopProductsChart filters={getFilters()} limit={10} />
              <CategoryPerformanceChart filters={getFilters()} limit={8} />
            </div>

            {/* Additional Product Insights */}
            <ProductInsightsCard filters={getFilters()} />
          </TabsContent>
        )}

        {/* Operations Tab */}
        {(isSuperAdmin || isFinanceAdmin) && (
          <TabsContent value="operations" className="space-y-6">
            {/* Cashier Performance Chart */}
            <CashierPerformanceChart filters={getFilters()} limit={8} />

            {/* Additional operational metrics */}
            <OperationalMetricsCard filters={getFilters()} />
          </TabsContent>
        )}

        {/* Customers Tab
        {(isSuperAdmin || isFinanceAdmin) && (
          <TabsContent value="customers" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Customer Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px] flex items-center justify-center bg-muted/20 rounded-md">
                  <div className="text-center">
                    <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <p className="text-lg font-medium text-muted-foreground mb-2">
                      Customer Analytics Coming Soon
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Customer behavior and transaction patterns will be available here
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )} */}
      </Tabs>

      {/* Information Card
      <Card className="mt-6">
        <CardContent className="pt-6">
          <div className="flex items-start gap-3 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <Info className="h-5 w-5 text-blue-600 mt-0.5" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-blue-900">
                Analytics System Under Development
              </h4>
              <p className="text-sm text-blue-700 mt-1">
                This comprehensive analytics system is currently being built. Charts and detailed
                insights will be progressively added to provide you with powerful business
                intelligence capabilities.
              </p>
            </div>
          </div>
        </CardContent>
      </Card> */}
    </MainLayout>
  );
}
