"use client";

import { useState } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Trash2, RefreshCw, Star } from "lucide-react";
import { useClientAuth } from "@/hooks/use-client-auth";

export default function ChatManagementPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [daysToKeep, setDaysToKeep] = useState(30);
  const [flushResult, setFlushResult] = useState<any>(null);
  const { user } = useClientAuth();

  // Check if user is super admin
  const isSuperAdmin = user?.role === "SUPER_ADMIN";

  // Flush conversations
  const flushConversations = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);
    setFlushResult(null);

    try {
      const response = await fetch("/api/conversations/flush", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ daysToKeep }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to flush conversations");
      }

      setSuccess(`Successfully flushed all unstarred conversations`);
      setFlushResult(data.flushed);
    } catch (err: any) {
      console.error("Error flushing conversations:", err);
      setError(err.message || "An error occurred while flushing conversations");
    } finally {
      setLoading(false);
    }
  };

  if (!isSuperAdmin) {
    return (
      <MainLayout>
        <Alert variant="destructive" className="mt-4">
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You do not have permission to access this page. Only Super Admins can manage chat
            settings.
          </AlertDescription>
        </Alert>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Chat Management"
        description="Manage chat system settings and maintenance"
      />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Flush Conversations</CardTitle>
            <CardDescription>
              Remove all conversations that haven't been starred by any user. This helps keep the
              database clean and improves performance.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="rounded-md bg-amber-50 p-4 border border-amber-200">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-amber-800">Important</h3>
                    <div className="mt-2 text-sm text-amber-700">
                      <p>
                        This action will immediately delete <strong>ALL</strong> conversations that
                        are not starred, regardless of their age. Only starred conversations will be
                        preserved.
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {flushResult && (
                <div className="rounded-md bg-muted p-4">
                  <h4 className="mb-2 font-medium">Flush Results:</h4>
                  <ul className="space-y-1 text-sm">
                    <li>Conversations removed: {flushResult.conversations}</li>
                    <li>Messages removed: {flushResult.messages}</li>
                    <li>Participants removed: {flushResult.participants}</li>
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
          <CardFooter>
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="destructive" disabled={loading}>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Flush All Unstarred Conversations
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action will permanently delete <strong>ALL</strong> conversations that are
                    not starred by any user, regardless of their age. Only starred conversations
                    will be preserved. This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={flushConversations}>
                    {loading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      "Yes, Flush All Unstarred Conversations"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Chat System Information</CardTitle>
            <CardDescription>
              Important information about the chat system and how it works.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-medium">Conversation Management</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  Conversations remain in the system until manually flushed by an administrator. Use
                  the "Flush All Unstarred Conversations" button to clean up the database.
                </p>
              </div>

              <div>
                <h4 className="font-medium">Starring Conversations</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  <Star className="inline-block h-4 w-4 mr-1 text-yellow-400" />
                  Users can star important conversations to prevent them from being automatically
                  deleted. Starred conversations are kept indefinitely.
                </p>
              </div>

              <div>
                <h4 className="font-medium">Automatic Cleanup</h4>
                <p className="text-sm text-muted-foreground mt-1">
                  The system does not automatically flush old conversations. As an administrator,
                  you need to manually trigger the flush operation from this page.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
