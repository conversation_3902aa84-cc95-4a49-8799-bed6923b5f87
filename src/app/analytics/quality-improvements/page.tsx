"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  CheckCircle2,
  TrendingUp,
  Target,
  DollarSign,
  Clock,
  Building2,
  BarChart3,
  Download,
  Lightbulb,
  Award,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface QualityImprovementAnalytics {
  summary: {
    totalImprovements: number;
    activeImprovements: number;
    completedImprovements: number;
    averageEffectivenessScore: number;
    totalROI: number;
    suppliersImproved: number;
  };
  topPerformingImprovements: Array<{
    improvementId: string;
    title: string;
    supplierName: string;
    improvementType: string;
    effectivenessScore: number;
    roi: number;
    startDate: string;
    completionDate?: string;
    beforeMetrics: {
      returnRate: number;
      defectRate: number;
      qualityScore: number;
    };
    afterMetrics: {
      returnRate: number;
      defectRate: number;
      qualityScore: number;
    };
    improvement: {
      returnRateImprovement: number;
      defectRateImprovement: number;
      qualityScoreImprovement: number;
      returnValueReduction: number;
    };
  }>;
  improvementsByType: Array<{
    type: string;
    count: number;
    averageEffectiveness: number;
    averageROI: number;
  }>;
  supplierProgress: Array<{
    supplierId: string;
    supplierName: string;
    activeImprovements: number;
    completedImprovements: number;
    averageEffectiveness: number;
    qualityTrend: 'improving' | 'stable' | 'declining';
    currentQualityScore: number;
  }>;
  timelineAnalysis: Array<{
    month: string;
    improvementsStarted: number;
    improvementsCompleted: number;
    averageEffectiveness: number;
    totalROI: number;
  }>;
  recommendations: Array<{
    type: 'process' | 'supplier' | 'improvement_type';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    expectedImpact: string;
  }>;
  generatedAt: string;
}

export default function QualityImprovementAnalyticsPage() {
  const [data, setData] = useState<QualityImprovementAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("180");
  const [analysisType, setAnalysisType] = useState("dashboard");

  useEffect(() => {
    fetchQualityImprovementAnalytics();
  }, [timeframe, analysisType]);

  const fetchQualityImprovementAnalytics = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        type: analysisType,
        timeframe,
      });

      const response = await fetch(`/api/analytics/quality-improvements?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality improvement analytics');
      }

      const result = await response.json();
      setData(result.data);
    } catch (error) {
      console.error('Error fetching quality improvement analytics:', error);
      toast.error('Failed to load quality improvement analytics');
    } finally {
      setLoading(false);
    }
  };

  const getEffectivenessColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getROIColor = (roi: number) => {
    if (roi >= 100) return 'text-green-600';
    if (roi >= 50) return 'text-yellow-600';
    if (roi >= 0) return 'text-orange-600';
    return 'text-red-600';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'declining': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatImprovementType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const exportData = () => {
    if (!data) return;
    
    const csvContent = [
      ['Improvement', 'Supplier', 'Type', 'Effectiveness Score', 'ROI %', 'Quality Score Improvement'],
      ...data.topPerformingImprovements.map(improvement => [
        improvement.title,
        improvement.supplierName,
        formatImprovementType(improvement.improvementType),
        improvement.effectivenessScore.toString(),
        improvement.roi.toFixed(1),
        improvement.improvement.qualityScoreImprovement.toFixed(1)
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `quality-improvements-analytics-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Quality Improvement Analytics"
        description="Track effectiveness, ROI, and impact of quality improvement initiatives"
        actions={
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        }
      />

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Analytics Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                  <SelectItem value="730">Last 2 years</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Analysis Type</label>
              <Select value={analysisType} onValueChange={setAnalysisType}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dashboard">Dashboard Overview</SelectItem>
                  <SelectItem value="effectiveness">Effectiveness Analysis</SelectItem>
                  <SelectItem value="roi">ROI Analysis</SelectItem>
                  <SelectItem value="trends">Trend Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchQualityImprovementAnalytics} className="w-full">
                Refresh Analytics
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Improvements</CardTitle>
              <Lightbulb className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.totalImprovements}</div>
              <p className="text-xs text-muted-foreground">
                {data.summary.activeImprovements} active, {data.summary.completedImprovements} completed
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Effectiveness</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getEffectivenessColor(data.summary.averageEffectivenessScore)}`}>
                {data.summary.averageEffectivenessScore.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Out of 100
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total ROI</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getROIColor(data.summary.totalROI)}`}>
                {data.summary.totalROI.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Return on investment
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Suppliers Improved</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.suppliersImproved}</div>
              <p className="text-xs text-muted-foreground">
                Suppliers with improvements
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Top Performing Improvements */}
      {data && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Top Performing Improvements
            </CardTitle>
            <CardDescription>
              Most effective quality improvement initiatives
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.topPerformingImprovements.slice(0, 5).map((improvement) => (
                <div key={improvement.improvementId} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="font-semibold">{improvement.title}</h3>
                      <p className="text-sm text-muted-foreground">
                        {improvement.supplierName} - {formatImprovementType(improvement.improvementType)}
                      </p>
                    </div>
                    <div className="text-right space-y-1">
                      <div className={`text-lg font-bold ${getEffectivenessColor(improvement.effectivenessScore)}`}>
                        {improvement.effectivenessScore}
                      </div>
                      <div className="text-sm text-muted-foreground">Effectiveness</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-1">
                      <div className="text-sm font-medium">Quality Score</div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {improvement.beforeMetrics.qualityScore.toFixed(1)}
                        </span>
                        <TrendingUp className="h-3 w-3 text-green-600" />
                        <span className="text-sm font-bold text-green-600">
                          {improvement.afterMetrics.qualityScore.toFixed(1)}
                        </span>
                      </div>
                      <div className="text-xs text-green-600">
                        +{improvement.improvement.qualityScoreImprovement.toFixed(1)} points
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">Return Rate</div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-muted-foreground">
                          {improvement.beforeMetrics.returnRate.toFixed(2)}%
                        </span>
                        <TrendingUp className="h-3 w-3 text-green-600 rotate-180" />
                        <span className="text-sm font-bold text-green-600">
                          {improvement.afterMetrics.returnRate.toFixed(2)}%
                        </span>
                      </div>
                      <div className="text-xs text-green-600">
                        -{improvement.improvement.returnRateImprovement.toFixed(2)}%
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">ROI</div>
                      <div className={`text-lg font-bold ${getROIColor(improvement.roi)}`}>
                        {improvement.roi.toFixed(1)}%
                      </div>
                    </div>
                    <div className="space-y-1">
                      <div className="text-sm font-medium">Value Saved</div>
                      <div className="text-lg font-bold text-green-600">
                        {formatCurrency(improvement.improvement.returnValueReduction)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Started: {new Date(improvement.startDate).toLocaleDateString()}</span>
                    {improvement.completionDate && (
                      <span>Completed: {new Date(improvement.completionDate).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Improvements by Type and Supplier Progress */}
      {data && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <Card>
            <CardHeader>
              <CardTitle>Improvements by Type</CardTitle>
              <CardDescription>
                Effectiveness analysis by improvement type
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.improvementsByType.map((type, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{formatImprovementType(type.type)}</div>
                      <div className="text-sm text-muted-foreground">{type.count} improvements</div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">Effectiveness</div>
                        <div className={`font-bold ${getEffectivenessColor(type.averageEffectiveness)}`}>
                          {type.averageEffectiveness.toFixed(1)}
                        </div>
                        <Progress value={type.averageEffectiveness} className="h-2 mt-1" />
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">Avg ROI</div>
                        <div className={`font-bold ${getROIColor(type.averageROI)}`}>
                          {type.averageROI.toFixed(1)}%
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Supplier Progress</CardTitle>
              <CardDescription>
                Quality improvement progress by supplier
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.supplierProgress.slice(0, 5).map((supplier) => (
                  <div key={supplier.supplierId} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{supplier.supplierName}</div>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(supplier.qualityTrend)}
                        <span className="text-sm capitalize">{supplier.qualityTrend}</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <div className="text-muted-foreground">Active</div>
                        <div className="font-bold">{supplier.activeImprovements}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Completed</div>
                        <div className="font-bold">{supplier.completedImprovements}</div>
                      </div>
                      <div>
                        <div className="text-muted-foreground">Quality Score</div>
                        <div className={`font-bold ${getEffectivenessColor(supplier.currentQualityScore)}`}>
                          {supplier.currentQualityScore.toFixed(1)}
                        </div>
                      </div>
                    </div>
                    <Progress value={supplier.averageEffectiveness} className="h-2" />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Recommendations */}
      {data && data.recommendations.length > 0 && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
            <CardDescription>
              AI-powered recommendations for quality improvement optimization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.recommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded">
                  <Badge className={getPriorityColor(rec.priority)}>
                    {rec.priority.toUpperCase()}
                  </Badge>
                  <div className="flex-1 space-y-1">
                    <h4 className="font-medium">{rec.title}</h4>
                    <p className="text-sm text-muted-foreground">{rec.description}</p>
                    <p className="text-xs text-green-600">Expected Impact: {rec.expectedImpact}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {data && (
        <div className="text-xs text-muted-foreground text-center">
          Analytics generated on {new Date(data.generatedAt).toLocaleString()}
        </div>
      )}
    </MainLayout>
  );
}
