"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  RotateCcw,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Package,
  Building2,
  AlertTriangle,
  BarChart3,
  PieChart,
  Download,
  Calendar,
  Users
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface ReturnDashboardData {
  summary: {
    totalReturns: number;
    totalReturnValue: number;
    returnRate: number;
    averageReturnValue: number;
    topReturnReason: string;
    returnTrend: number;
    customersAffected: number;
    suppliersAffected: number;
  };
  returnsByReason: Array<{
    reason: string;
    count: number;
    percentage: number;
    totalValue: number;
    trend: number;
  }>;
  returnsBySupplier: Array<{
    supplierId: string;
    supplierName: string;
    returnCount: number;
    returnValue: number;
    returnRate: number;
    trend: number;
    topReasons: string[];
  }>;
  returnsByProduct: Array<{
    productId: string;
    productName: string;
    sku: string;
    returnCount: number;
    returnValue: number;
    returnRate: number;
    topReasons: string[];
  }>;
  monthlyTrends: Array<{
    month: string;
    returnCount: number;
    returnValue: number;
    returnRate: number;
    defectCount: number;
  }>;
  batchAnalysis: Array<{
    batchId: string;
    batchNumber: string;
    productName: string;
    supplierName: string;
    returnCount: number;
    returnValue: number;
    returnPercentage: number;
    receivedDate: string;
    defectTypes: string[];
  }>;
  customerImpact: {
    totalCustomersAffected: number;
    repeatReturnCustomers: number;
    averageReturnFrequency: number;
    customerSatisfactionImpact: number;
  };
  financialImpact: {
    directCosts: number;
    indirectCosts: number;
    totalImpact: number;
    costPerReturn: number;
    monthlyTrend: number;
  };
  generatedAt: string;
}

export default function ReturnDashboardPage() {
  const [data, setData] = useState<ReturnDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("90");
  const [viewType, setViewType] = useState("overview");

  useEffect(() => {
    fetchReturnDashboard();
  }, [timeframe, viewType]);

  const fetchReturnDashboard = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        viewType,
      });

      const response = await fetch(`/api/analytics/return-dashboard?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch return dashboard data');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching return dashboard:', error);
      toast.error('Failed to load return dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 0) return <TrendingUp className="h-4 w-4 text-red-600" />;
    if (trend < 0) return <TrendingDown className="h-4 w-4 text-green-600" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 0) return 'text-red-600';
    if (trend < 0) return 'text-green-600';
    return 'text-gray-600';
  };

  const exportData = () => {
    if (!data) return;
    
    const csvContent = [
      ['Metric', 'Value'],
      ['Total Returns', data.summary.totalReturns.toString()],
      ['Total Return Value', data.summary.totalReturnValue.toString()],
      ['Return Rate %', data.summary.returnRate.toFixed(2)],
      ['Average Return Value', data.summary.averageReturnValue.toString()],
      ['Top Return Reason', data.summary.topReturnReason],
      ['Customers Affected', data.summary.customersAffected.toString()],
      ['Suppliers Affected', data.summary.suppliersAffected.toString()],
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `return-dashboard-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Return Analytics Dashboard"
        description="Comprehensive analysis of returns, trends, and financial impact"
        actions={
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        }
      />

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Dashboard Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">View Type</label>
              <Select value={viewType} onValueChange={setViewType}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="overview">Overview</SelectItem>
                  <SelectItem value="supplier_focus">Supplier Focus</SelectItem>
                  <SelectItem value="product_focus">Product Focus</SelectItem>
                  <SelectItem value="financial_focus">Financial Focus</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchReturnDashboard} className="w-full">
                Refresh Dashboard
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
              <RotateCcw className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.totalReturns}</div>
              <div className="flex items-center gap-1 text-xs">
                {getTrendIcon(data.summary.returnTrend)}
                <span className={getTrendColor(data.summary.returnTrend)}>
                  {data.summary.returnTrend > 0 ? '+' : ''}{data.summary.returnTrend.toFixed(1)}%
                </span>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Return Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(data.summary.totalReturnValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                Avg: {formatCurrency(data.summary.averageReturnValue)}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Return Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.summary.returnRate.toFixed(2)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Of total sales
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customers Affected</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.customersAffected}</div>
              <p className="text-xs text-muted-foreground">
                {data.customerImpact.repeatReturnCustomers} repeat returns
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Returns by Reason */}
      {data && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Returns by Reason</CardTitle>
            <CardDescription>
              Breakdown of return reasons and their trends
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.returnsByReason.map((reason, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded">
                  <div className="space-y-1">
                    <div className="font-medium">{reason.reason}</div>
                    <div className="text-sm text-muted-foreground">
                      {reason.count} returns ({reason.percentage.toFixed(1)}%)
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <div className="font-medium">
                      {formatCurrency(reason.totalValue)}
                    </div>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(reason.trend)}
                      <span className={`text-xs ${getTrendColor(reason.trend)}`}>
                        {reason.trend > 0 ? '+' : ''}{reason.trend.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Top Affected Suppliers and Products */}
      {data && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Top Affected Suppliers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.returnsBySupplier.slice(0, 5).map((supplier) => (
                  <div key={supplier.supplierId} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="font-medium">{supplier.supplierName}</div>
                      <div className="text-right">
                        <div className="font-bold">{supplier.returnCount} returns</div>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(supplier.returnValue)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <Progress value={supplier.returnRate * 10} className="flex-1 mr-4" />
                      <div className="flex items-center gap-1">
                        {getTrendIcon(supplier.trend)}
                        <span className={`text-xs ${getTrendColor(supplier.trend)}`}>
                          {supplier.trend > 0 ? '+' : ''}{supplier.trend.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Top reasons: {supplier.topReasons.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Top Affected Products
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.returnsByProduct.slice(0, 5).map((product) => (
                  <div key={product.productId} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <div className="font-medium">{product.productName}</div>
                        <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{product.returnCount} returns</div>
                        <div className="text-sm text-muted-foreground">
                          {formatCurrency(product.returnValue)}
                        </div>
                      </div>
                    </div>
                    <Progress value={product.returnRate * 10} className="w-full" />
                    <div className="text-xs text-muted-foreground">
                      Top reasons: {product.topReasons.join(', ')}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Batch Analysis */}
      {data && data.batchAnalysis.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Problematic Batches</CardTitle>
            <CardDescription>
              Batches with high return rates requiring attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.batchAnalysis.slice(0, 10).map((batch) => (
                <div key={batch.batchId} className="flex items-center justify-between p-3 border rounded">
                  <div className="space-y-1">
                    <div className="font-medium">Batch {batch.batchNumber}</div>
                    <div className="text-sm text-muted-foreground">
                      {batch.productName} - {batch.supplierName}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Received: {new Date(batch.receivedDate).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Defects: {batch.defectTypes.join(', ')}
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <div className="font-bold text-red-600">
                      {batch.returnPercentage.toFixed(1)}%
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {batch.returnCount} returns
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {formatCurrency(batch.returnValue)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Financial Impact */}
      {data && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Financial Impact Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {formatCurrency(data.financialImpact.directCosts)}
                </div>
                <div className="text-sm text-muted-foreground">Direct Costs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {formatCurrency(data.financialImpact.indirectCosts)}
                </div>
                <div className="text-sm text-muted-foreground">Indirect Costs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatCurrency(data.financialImpact.totalImpact)}
                </div>
                <div className="text-sm text-muted-foreground">Total Impact</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatCurrency(data.financialImpact.costPerReturn)}
                </div>
                <div className="text-sm text-muted-foreground">Cost per Return</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {data && (
        <div className="text-xs mt-6 text-muted-foreground text-center">
          Dashboard generated on {new Date(data.generatedAt).toLocaleString()}
        </div>
      )}
    </MainLayout>
  );
}
