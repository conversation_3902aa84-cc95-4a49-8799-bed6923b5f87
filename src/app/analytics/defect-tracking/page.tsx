"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import {
  AlertTriangle,
  Package,
  Building2,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Download,
  Calendar,
  Target
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface DefectAnalysis {
  defectType: string;
  totalOccurrences: number;
  affectedProducts: number;
  affectedSuppliers: number;
  totalQuantity: number;
  totalValue: number;
  averageSeverity: number;
  trend: 'increasing' | 'stable' | 'decreasing';
  trendPercentage: number;
  topProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    occurrences: number;
    quantity: number;
    value: number;
  }>;
  topSuppliers: Array<{
    supplierId: string;
    supplierName: string;
    occurrences: number;
    quantity: number;
    value: number;
  }>;
  monthlyTrend: Array<{
    month: string;
    occurrences: number;
    quantity: number;
    value: number;
  }>;
  severityDistribution: Array<{
    severity: string;
    count: number;
    percentage: number;
  }>;
}

interface DefectTrackingResponse {
  defectAnalysis: DefectAnalysis[];
  summary: {
    totalDefects: number;
    totalValue: number;
    mostCommonDefect: string;
    averageResolutionTime: number;
    defectRate: number;
    trendDirection: 'improving' | 'stable' | 'worsening';
  };
  timeComparison: {
    currentPeriod: {
      defects: number;
      value: number;
    };
    previousPeriod: {
      defects: number;
      value: number;
    };
    changePercentage: number;
  };
  generatedAt: string;
}

export default function DefectTrackingPage() {
  const [data, setData] = useState<DefectTrackingResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("90");
  const [groupBy, setGroupBy] = useState("defectType");
  const [selectedDefectType, setSelectedDefectType] = useState("all");

  useEffect(() => {
    fetchDefectTracking();
  }, [timeframe, groupBy]);

  const fetchDefectTracking = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        groupBy,
        ...(selectedDefectType && selectedDefectType !== "all" && { defectType: selectedDefectType }),
      });

      const response = await fetch(`/api/analytics/defect-tracking?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch defect tracking data');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching defect tracking:', error);
      toast.error('Failed to load defect tracking data');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-red-600" />;
      case 'decreasing': return <TrendingDown className="h-4 w-4 text-green-600" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'increasing': return 'text-red-600';
      case 'decreasing': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  const formatDefectType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const exportData = () => {
    if (!data) return;
    
    const csvContent = [
      ['Defect Type', 'Occurrences', 'Affected Products', 'Affected Suppliers', 'Total Value', 'Trend'],
      ...data.defectAnalysis.map(defect => [
        formatDefectType(defect.defectType),
        defect.totalOccurrences.toString(),
        defect.affectedProducts.toString(),
        defect.affectedSuppliers.toString(),
        defect.totalValue.toString(),
        defect.trend
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `defect-tracking-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Defect Tracking Dashboard"
        description="Comprehensive analysis of product defects by type, supplier, and trends"
        actions={
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        }
      />

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Analysis Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Group By</label>
              <Select value={groupBy} onValueChange={setGroupBy}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="defectType">Defect Type</SelectItem>
                  <SelectItem value="supplier">Supplier</SelectItem>
                  <SelectItem value="product">Product</SelectItem>
                  <SelectItem value="severity">Severity</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Defect Type Filter</label>
              <Select value={selectedDefectType} onValueChange={setSelectedDefectType}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="All Defect Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Defect Types</SelectItem>
                  <SelectItem value="DEFECTIVE_PRODUCT">Defective Product</SelectItem>
                  <SelectItem value="PACKAGING_DAMAGE">Packaging Damage</SelectItem>
                  <SelectItem value="WRONG_SPECIFICATION">Wrong Specification</SelectItem>
                  <SelectItem value="CONTAMINATION">Contamination</SelectItem>
                  <SelectItem value="EXPIRY_ISSUE">Expiry Issue</SelectItem>
                  <SelectItem value="QUALITY_DEGRADATION">Quality Degradation</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchDefectTracking} className="w-full">
                Refresh Data
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card >
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Defects</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.totalDefects}</div>
              <p className="text-xs text-muted-foreground">
                {data.timeComparison.changePercentage > 0 ? '+' : ''}{data.timeComparison.changePercentage.toFixed(1)}% from previous period
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Value Impact</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(data.summary.totalValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                Financial impact of defects
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Defect Rate</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.summary.defectRate.toFixed(2)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Of total inventory
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Resolution Time</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {data.summary.averageResolutionTime.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Days to resolve
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Defect Analysis Table */}
      <Card className="mt-6" >
        <CardHeader>
          <CardTitle>Defect Analysis by Type</CardTitle>
          <CardDescription>
            Detailed breakdown of defects and their impact
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Defect Type</TableHead>
                  <TableHead>Occurrences</TableHead>
                  <TableHead>Affected Products</TableHead>
                  <TableHead>Affected Suppliers</TableHead>
                  <TableHead>Total Quantity</TableHead>
                  <TableHead>Total Value</TableHead>
                  <TableHead>Avg Severity</TableHead>
                  <TableHead>Trend</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.defectAnalysis.map((defect) => (
                  <TableRow key={defect.defectType}>
                    <TableCell>
                      <div className="font-medium">
                        {formatDefectType(defect.defectType)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-bold">{defect.totalOccurrences}</div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        {defect.affectedProducts}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        {defect.affectedSuppliers}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">{defect.totalQuantity.toLocaleString()}</div>
                    </TableCell>
                    <TableCell>
                      <div className="font-medium">
                        {formatCurrency(defect.totalValue)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{defect.averageSeverity.toFixed(1)}</div>
                        <Progress value={defect.averageSeverity * 25} className="w-16" />
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(defect.trend)}
                        <span className={`text-sm ${getTrendColor(defect.trend)}`}>
                          {defect.trendPercentage > 0 ? '+' : ''}{defect.trendPercentage.toFixed(1)}%
                        </span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Top Products and Suppliers */}
      {data && data.defectAnalysis.length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Affected Products */}
          <Card>
            <CardHeader>
              <CardTitle>Most Affected Products</CardTitle>
              <CardDescription>
                Products with highest defect occurrences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.defectAnalysis[0]?.topProducts.slice(0, 5).map((product, index) => (
                  <div key={product.productId} className="flex items-center justify-between p-3 border rounded">
                    <div className="space-y-1">
                      <div className="font-medium">{product.productName}</div>
                      <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
                    </div>
                    <div className="text-right space-y-1">
                      <div className="font-bold">{product.occurrences} defects</div>
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(product.value)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Affected Suppliers */}
          <Card>
            <CardHeader>
              <CardTitle>Most Affected Suppliers</CardTitle>
              <CardDescription>
                Suppliers with highest defect occurrences
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {data.defectAnalysis[0]?.topSuppliers.slice(0, 5).map((supplier, index) => (
                  <div key={supplier.supplierId} className="flex items-center justify-between p-3 border rounded">
                    <div className="space-y-1">
                      <div className="font-medium">{supplier.supplierName}</div>
                      <div className="text-sm text-muted-foreground">
                        {supplier.quantity.toLocaleString()} units affected
                      </div>
                    </div>
                    <div className="text-right space-y-1">
                      <div className="font-bold">{supplier.occurrences} defects</div>
                      <div className="text-sm text-muted-foreground">
                        {formatCurrency(supplier.value)}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {data && (
        <div className="text-xs text-muted-foreground text-center">
          Report generated on {new Date(data.generatedAt).toLocaleString()}
        </div>
      )}
    </MainLayout>
  );
}
