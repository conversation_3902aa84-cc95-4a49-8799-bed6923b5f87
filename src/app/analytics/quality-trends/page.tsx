"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle2,
  BarChart3,
  LineChart,
  Download,
  Calendar,
  Target,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface QualityTrend {
  supplierId: string;
  supplierName: string;
  currentPeriod: {
    qualityScore: number;
    returnRate: number;
    defectRate: number;
    issueCount: number;
    averageResolutionTime: number;
  };
  previousPeriod: {
    qualityScore: number;
    returnRate: number;
    defectRate: number;
    issueCount: number;
    averageResolutionTime: number;
  };
  trends: {
    qualityScoreTrend: number;
    returnRateTrend: number;
    defectRateTrend: number;
    issueCountTrend: number;
    resolutionTimeTrend: number;
  };
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  prediction: {
    nextPeriodQualityScore: number;
    confidence: number;
    riskFactors: string[];
    recommendations: string[];
  };
  monthlyData: Array<{
    month: string;
    qualityScore: number;
    returnRate: number;
    defectRate: number;
    issueCount: number;
  }>;
}

interface QualityTrendsResponse {
  trends: QualityTrend[];
  summary: {
    totalSuppliers: number;
    improvingSuppliers: number;
    decliningSuppliers: number;
    stableSuppliers: number;
    averageQualityScore: number;
    averageQualityTrend: number;
    highRiskSuppliers: number;
    alertsTriggered: number;
  };
  alerts: Array<{
    supplierId: string;
    supplierName: string;
    alertType: string;
    severity: string;
    message: string;
    triggeredAt: string;
    threshold: number;
    currentValue: number;
  }>;
  predictions: {
    overallTrend: 'improving' | 'stable' | 'declining';
    expectedQualityScore: number;
    confidence: number;
    timeframe: string;
  };
  generatedAt: string;
}

export default function QualityTrendsPage() {
  const [data, setData] = useState<QualityTrendsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("90");
  const [analysisType, setAnalysisType] = useState("comprehensive");
  const [selectedSupplier, setSelectedSupplier] = useState("all");

  useEffect(() => {
    fetchQualityTrends();
  }, [timeframe, analysisType]);

  const fetchQualityTrends = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        analysisType,
        ...(selectedSupplier && selectedSupplier !== "all" && { supplierId: selectedSupplier }),
      });

      const response = await fetch(`/api/analytics/quality-trends?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality trends data');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching quality trends:', error);
      toast.error('Failed to load quality trends data');
    } finally {
      setLoading(false);
    }
  };

  const getTrendIcon = (trend: number) => {
    if (trend > 5) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (trend < -5) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Activity className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = (trend: number) => {
    if (trend > 5) return 'text-green-600';
    if (trend < -5) return 'text-red-600';
    return 'text-gray-600';
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const exportData = () => {
    if (!data) return;
    
    const csvContent = [
      ['Supplier', 'Current Quality Score', 'Quality Trend %', 'Return Rate %', 'Defect Rate %', 'Risk Level', 'Prediction'],
      ...data.trends.map(trend => [
        trend.supplierName,
        trend.currentPeriod.qualityScore.toFixed(1),
        trend.trends.qualityScoreTrend.toFixed(1),
        trend.currentPeriod.returnRate.toFixed(2),
        trend.currentPeriod.defectRate.toFixed(2),
        trend.riskLevel,
        trend.prediction.nextPeriodQualityScore.toFixed(1)
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `quality-trends-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Quality Trends Analysis"
        description="Monitor quality trends, predictions, and early warning alerts"
        actions={
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        }
      />

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Analysis Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Analysis Type</label>
              <Select value={analysisType} onValueChange={setAnalysisType}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="comprehensive">Comprehensive</SelectItem>
                  <SelectItem value="quality_score">Quality Score Focus</SelectItem>
                  <SelectItem value="defect_analysis">Defect Analysis</SelectItem>
                  <SelectItem value="predictive">Predictive Analysis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Supplier Filter</label>
              <Select value={selectedSupplier} onValueChange={setSelectedSupplier}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="All Suppliers" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Suppliers</SelectItem>
                  {data?.trends.map((trend) => (
                    <SelectItem key={trend.supplierId} value={trend.supplierId}>
                      {trend.supplierName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchQualityTrends} className="w-full">
                Refresh Analysis
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Improving Suppliers</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {data.summary.improvingSuppliers}
              </div>
              <p className="text-xs text-muted-foreground">
                Out of {data.summary.totalSuppliers} suppliers
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Declining Suppliers</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {data.summary.decliningSuppliers}
              </div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quality Trend</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getTrendColor(data.summary.averageQualityTrend)}`}>
                {data.summary.averageQualityTrend > 0 ? '+' : ''}{data.summary.averageQualityTrend.toFixed(1)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Average change
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Alerts</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">
                {data.summary.alertsTriggered}
              </div>
              <p className="text-xs text-muted-foreground">
                Quality alerts triggered
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quality Alerts */}
      {data && data.alerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Active Quality Alerts
            </CardTitle>
            <CardDescription>
              Real-time quality alerts requiring immediate attention
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.alerts.map((alert, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge className={getAlertSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                      <span className="font-medium">{alert.supplierName}</span>
                    </div>
                    <p className="text-sm text-muted-foreground">{alert.message}</p>
                    <p className="text-xs text-muted-foreground">
                      Threshold: {alert.threshold} | Current: {alert.currentValue}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-muted-foreground">
                      {new Date(alert.triggeredAt).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Supplier Trends Table */}
      <Card className="mt-6">
        <CardHeader>
          <CardTitle>Supplier Quality Trends</CardTitle>
          <CardDescription>
            Detailed trend analysis and predictions for each supplier
          </CardDescription>
        </CardHeader>
        <CardContent>
          {data && (
            <div className="space-y-4">
              {data.trends.map((trend) => (
                <div key={trend.supplierId} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-1">
                      <h3 className="font-semibold text-lg">{trend.supplierName}</h3>
                      <div className="flex items-center gap-2">
                        <Badge className={getRiskColor(trend.riskLevel)}>
                          {trend.riskLevel} Risk
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          Prediction Confidence: {trend.prediction.confidence}%
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold">
                        {trend.currentPeriod.qualityScore.toFixed(1)}
                      </div>
                      <div className="text-sm text-muted-foreground">Quality Score</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Quality Score</span>
                        {getTrendIcon(trend.trends.qualityScoreTrend)}
                      </div>
                      <Progress value={trend.currentPeriod.qualityScore} className="h-2" />
                      <div className={`text-xs ${getTrendColor(trend.trends.qualityScoreTrend)}`}>
                        {trend.trends.qualityScoreTrend > 0 ? '+' : ''}{trend.trends.qualityScoreTrend.toFixed(1)}%
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Return Rate</span>
                        {getTrendIcon(-trend.trends.returnRateTrend)}
                      </div>
                      <div className="text-lg font-bold">
                        {trend.currentPeriod.returnRate.toFixed(2)}%
                      </div>
                      <div className={`text-xs ${getTrendColor(-trend.trends.returnRateTrend)}`}>
                        {trend.trends.returnRateTrend > 0 ? '+' : ''}{trend.trends.returnRateTrend.toFixed(1)}%
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Defect Rate</span>
                        {getTrendIcon(-trend.trends.defectRateTrend)}
                      </div>
                      <div className="text-lg font-bold">
                        {trend.currentPeriod.defectRate.toFixed(2)}%
                      </div>
                      <div className={`text-xs ${getTrendColor(-trend.trends.defectRateTrend)}`}>
                        {trend.trends.defectRateTrend > 0 ? '+' : ''}{trend.trends.defectRateTrend.toFixed(1)}%
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Prediction</span>
                        <Target className="h-4 w-4 text-muted-foreground" />
                      </div>
                      <div className="text-lg font-bold">
                        {trend.prediction.nextPeriodQualityScore.toFixed(1)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Next period score
                      </div>
                    </div>
                  </div>

                  {trend.prediction.recommendations.length > 0 && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-sm">Recommendations:</h4>
                      <ul className="text-sm text-muted-foreground space-y-1">
                        {trend.prediction.recommendations.slice(0, 3).map((rec, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckCircle2 className="h-3 w-3 mt-0.5 text-green-600" />
                            {rec}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Overall Prediction */}
      {data && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <LineChart className="h-5 w-5" />
              Overall Quality Prediction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold">
                  {data.predictions.expectedQualityScore.toFixed(1)}
                </div>
                <div className="text-sm text-muted-foreground">
                  Expected Quality Score
                </div>
              </div>
              <div className="text-center">
                <div className={`text-3xl font-bold ${data.predictions.overallTrend === 'improving' ? 'text-green-600' : data.predictions.overallTrend === 'declining' ? 'text-red-600' : 'text-gray-600'}`}>
                  {data.predictions.overallTrend.toUpperCase()}
                </div>
                <div className="text-sm text-muted-foreground">
                  Overall Trend
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">
                  {data.predictions.confidence}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Prediction Confidence
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {data && (
        <div className="text-xs mt-6 text-muted-foreground text-center">
          Analysis generated on {new Date(data.generatedAt).toLocaleString()}
        </div>
      )}
    </MainLayout>
  );
}
