"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import {
  PieChart,
  BarChart3,
  TrendingDown,
  TrendingUp,
  AlertTriangle,
  CheckCircle2,
  DollarSign,
  Package,
  RotateCcw,
  Building2,
  Download
} from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface SupplierAccountability {
  supplierId: string;
  supplierName: string;
  contactPerson: string;
  totalPurchaseValue: number;
  totalReturnValue: number;
  returnRate: number;
  defectRate: number;
  qualityScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  totalBatches: number;
  affectedBatches: number;
  totalReturns: number;
  totalDefects: number;
  averageResolutionTime: number;
  lastIncidentDate?: string;
  improvementTrend: 'improving' | 'stable' | 'declining';
  topDefectTypes: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    returnRate: number;
    defectRate: number;
    qualityScore: number;
  }>;
  recentIssues: Array<{
    id: string;
    issueType: string;
    severity: string;
    reportedAt: string;
    status: string;
  }>;
}

interface SupplierAccountabilityResponse {
  suppliers: SupplierAccountability[];
  summary: {
    totalSuppliers: number;
    averageQualityScore: number;
    totalReturnValue: number;
    highRiskSuppliers: number;
  };
  generatedAt: string;
}

export default function SupplierAccountabilityPage() {
  const [data, setData] = useState<SupplierAccountabilityResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState("90");
  const [sortBy, setSortBy] = useState("qualityScore");
  const [filterRisk, setFilterRisk] = useState("all");

  useEffect(() => {
    fetchSupplierAccountability();
  }, [timeframe, sortBy]);

  const fetchSupplierAccountability = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        timeframe,
        sortBy,
        ...(filterRisk && filterRisk !== "all" && { riskLevel: filterRisk }),
      });

      const response = await fetch(`/api/analytics/supplier-accountability?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch supplier accountability data');
      }

      const result = await response.json();
      setData(result);
    } catch (error) {
      console.error('Error fetching supplier accountability:', error);
      toast.error('Failed to load supplier accountability data');
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-600" />;
      default: return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  const getQualityScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    if (score >= 60) return 'text-orange-600';
    return 'text-red-600';
  };

  const exportData = () => {
    if (!data) return;
    
    const csvContent = [
      ['Supplier', 'Quality Score', 'Return Rate %', 'Defect Rate %', 'Risk Level', 'Total Returns', 'Return Value'],
      ...data.suppliers.map(supplier => [
        supplier.supplierName,
        supplier.qualityScore.toString(),
        supplier.returnRate.toFixed(2),
        supplier.defectRate.toFixed(2),
        supplier.riskLevel,
        supplier.totalReturns.toString(),
        supplier.totalReturnValue.toString()
      ])
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `supplier-accountability-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const filteredSuppliers = data?.suppliers.filter(supplier =>
    !filterRisk || filterRisk === "all" || supplier.riskLevel === filterRisk
  ) || [];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Supplier Accountability"
        description="Comprehensive supplier performance and quality accountability reports"
        actions={
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        }
      />

      {/* Controls */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Analysis Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium">Timeframe</label>
              <Select value={timeframe} onValueChange={setTimeframe}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="180">Last 6 months</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Sort By</label>
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="qualityScore">Quality Score</SelectItem>
                  <SelectItem value="returnRate">Return Rate</SelectItem>
                  <SelectItem value="defectRate">Defect Rate</SelectItem>
                  <SelectItem value="totalReturnValue">Return Value</SelectItem>
                  <SelectItem value="riskLevel">Risk Level</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">Risk Filter</label>
              <Select value={filterRisk} onValueChange={setFilterRisk}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="All Risk Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Risk Levels</SelectItem>
                  <SelectItem value="CRITICAL">Critical</SelectItem>
                  <SelectItem value="HIGH">High</SelectItem>
                  <SelectItem value="MEDIUM">Medium</SelectItem>
                  <SelectItem value="LOW">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={fetchSupplierAccountability} className="w-full">
                Refresh Data
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Cards */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card className="m-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
              <Building2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data.summary.totalSuppliers}</div>
              <p className="text-xs text-muted-foreground">
                {data.summary.highRiskSuppliers} high risk
              </p>
            </CardContent>
          </Card>
          <Card className="m-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Quality Score</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getQualityScoreColor(data.summary.averageQualityScore)}`}>
                {data.summary.averageQualityScore.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Out of 100
              </p>
            </CardContent>
          </Card>
          <Card className="m-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Return Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(data.summary.totalReturnValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                Last {timeframe} days
              </p>
            </CardContent>
          </Card>
          <Card className="m-2">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">High Risk Suppliers</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">
                {data.summary.highRiskSuppliers}
              </div>
              <p className="text-xs text-muted-foreground">
                Require attention
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Supplier Accountability Table */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Supplier Performance Details</CardTitle>
          <CardDescription>
            {filteredSuppliers.length} suppliers shown
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Supplier</TableHead>
                <TableHead>Quality Score</TableHead>
                <TableHead>Return Rate</TableHead>
                <TableHead>Defect Rate</TableHead>
                <TableHead>Risk Level</TableHead>
                <TableHead>Return Value</TableHead>
                <TableHead>Trend</TableHead>
                <TableHead>Last Incident</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSuppliers.map((supplier) => (
                <TableRow key={supplier.supplierId}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{supplier.supplierName}</div>
                      <div className="text-sm text-muted-foreground">
                        {supplier.contactPerson}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className={`font-bold ${getQualityScoreColor(supplier.qualityScore)}`}>
                        {supplier.qualityScore.toFixed(1)}
                      </div>
                      <Progress value={supplier.qualityScore} className="w-16" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{supplier.returnRate.toFixed(2)}%</div>
                      <div className="text-xs text-muted-foreground">
                        {supplier.totalReturns} returns
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{supplier.defectRate.toFixed(2)}%</div>
                      <div className="text-xs text-muted-foreground">
                        {supplier.totalDefects} defects
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge className={getRiskColor(supplier.riskLevel)}>
                      {supplier.riskLevel}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">
                      {formatCurrency(supplier.totalReturnValue)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTrendIcon(supplier.improvementTrend)}
                      <span className="text-sm capitalize">
                        {supplier.improvementTrend}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {supplier.lastIncidentDate ? (
                      <div className="text-sm">
                        {new Date(supplier.lastIncidentDate).toLocaleDateString()}
                      </div>
                    ) : (
                      <div className="text-sm text-muted-foreground">None</div>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {data && (
        <div className="text-xs text-muted-foreground text-center">
          Report generated on {new Date(data.generatedAt).toLocaleString()}
        </div>
      )}
    </MainLayout>
  );
}
