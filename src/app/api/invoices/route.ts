import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';

// Validation schema for creating invoices
const createInvoiceSchema = z.object({
  purchaseOrderId: z.string().optional(), // Made optional to support manual invoices
  supplierId: z.string().min(1, "Supplier ID is required"), // Added supplierId validation
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  invoiceDate: z.string().datetime("Invalid invoice date"),
  dueDate: z.string().datetime("Invalid due date").optional(),
  taxPercentage: z.coerce.number().min(0).max(100).optional(), // Added coercion
  notes: z.string().optional(),
  attachmentUrl: z.string().url().optional(),
  enableInstallments: z.boolean().default(false),
  numberOfInstallments: z.coerce.number().min(2).max(12).optional(), // Added coercion
  installments: z.array(z.object({
    dueDate: z.string().datetime("Invalid installment due date"),
    amount: z.coerce.number().positive("Installment amount must be positive"), // Added coercion
    description: z.string().optional(),
  })).optional(),
  items: z.array(z.object({
    purchaseOrderItemId: z.string().optional(),
    productId: z.string().min(1, "Product ID is required"),
    description: z.string().min(1, "Description is required"),
    quantity: z.coerce.number().positive("Quantity must be positive"), // Added coercion
    unitPrice: z.coerce.number().positive("Unit price must be positive"), // Added coercion
  })).min(1, "At least one item is required"),
}).refine((data) => {
  if (data.enableInstallments) {
    if (!data.numberOfInstallments || !data.installments) {
      return false;
    }
    if (data.installments.length !== data.numberOfInstallments) {
      return false;
    }
  }
  return true;
}, {
  message: "Invalid installment configuration",
});

// GET /api/invoices - List invoices with filtering
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const paymentStatus = searchParams.get('paymentStatus');
    const supplierId = searchParams.get('supplierId');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    
    if (status) {
      where.status = status;
    }
    
    if (paymentStatus) {
      where.paymentStatus = paymentStatus;
    }
    
    if (supplierId) {
      where.supplierId = supplierId;
    }
    
    if (search) {
      where.OR = [
        { invoiceNumber: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
        { purchaseOrder: { id: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [invoices, total] = await Promise.all([
      prisma.invoice.findMany({
        where,
        include: {
          supplier: {
            select: { id: true, name: true, email: true }
          },
          purchaseOrder: {
            select: { id: true, orderDate: true }
          },
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          approvedBy: {
            select: { id: true, name: true, email: true }
          },
          items: {
            include: {
              product: {
                select: { id: true, name: true, sku: true }
              }
            }
          },
          payments: true,
          installments: {
            orderBy: { installmentNumber: 'asc' }
          },
          _count: {
            select: { items: true, payments: true, installments: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.invoice.count({ where }),
    ]);

    // Transform decimal fields to numbers for JSON serialization
    const transformedInvoices = invoices.map(invoice => ({
      ...invoice,
      subtotal: Number(invoice.subtotal),
      tax: Number(invoice.tax),
      taxPercentage: invoice.taxPercentage ? Number(invoice.taxPercentage) : null,
      total: Number(invoice.total),
      paidAmount: Number(invoice.paidAmount),
      items: invoice.items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
      })),
      payments: invoice.payments.map(payment => ({
        ...payment,
        amount: Number(payment.amount),
      })),
      installments: invoice.installments.map(installment => ({
        ...installment,
        amount: Number(installment.amount),
        paidAmount: Number(installment.paidAmount),
      })),
    }));

    return NextResponse.json({
      invoices: transformedInvoices,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });

  } catch (error) {
    console.error('Error fetching invoices:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoices' },
      { status: 500 }
    );
  }
}

// POST /api/invoices - Create new invoice
export async function POST(request: NextRequest) {
  console.log('🚀 Invoice creation API called');

  try {
    console.log('🔐 Verifying authentication...');
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      console.log('❌ Authentication failed:', auth.error);
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }
    console.log('✅ Authentication successful, user:', auth.user.email, 'role:', auth.user.role);

    // Check permissions
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role)) {
      console.log('❌ Insufficient permissions for role:', auth.user.role);
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }
    console.log('✅ Permissions verified');

    console.log('📥 Parsing request body...');
    const body = await request.json();
    console.log('📋 Request body received:', JSON.stringify(body, null, 2));

    console.log('🔍 Validating data with schema...');
    const validatedData = createInvoiceSchema.parse(body);
    console.log('✅ Data validation successful:', JSON.stringify(validatedData, null, 2));

    // Verify supplier exists
    console.log('🏢 Verifying supplier exists:', validatedData.supplierId);
    const supplier = await prisma.supplier.findUnique({
      where: { id: validatedData.supplierId }
    });

    if (!supplier) {
      console.log('❌ Supplier not found:', validatedData.supplierId);
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }
    console.log('✅ Supplier found:', supplier.name);

    // Verify purchase order exists and is in correct status (if provided)
    let purchaseOrder = null;
    if (validatedData.purchaseOrderId) {
      console.log('📦 Verifying purchase order:', validatedData.purchaseOrderId);
      purchaseOrder = await prisma.purchaseOrder.findUnique({
        where: { id: validatedData.purchaseOrderId },
        include: {
          supplier: true,
          items: {
            include: {
              product: true
            }
          }
        }
      });

      if (!purchaseOrder) {
        console.log('❌ Purchase order not found:', validatedData.purchaseOrderId);
        return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
      }
      console.log('✅ Purchase order found:', purchaseOrder.id, 'status:', purchaseOrder.status);

      if (!['ORDERED', 'PARTIALLY_RECEIVED', 'RECEIVED'].includes(purchaseOrder.status)) {
        console.log('❌ Invalid PO status:', purchaseOrder.status);
        return NextResponse.json({
          error: 'Purchase order must be ordered, partially received, or received before creating invoice'
        }, { status: 400 });
      }

      // Verify PO supplier matches the provided supplier
      if (purchaseOrder.supplierId !== validatedData.supplierId) {
        console.log('❌ PO supplier mismatch. PO supplier:', purchaseOrder.supplierId, 'Selected supplier:', validatedData.supplierId);
        return NextResponse.json({
          error: 'Purchase order supplier does not match the selected supplier'
        }, { status: 400 });
      }
      console.log('✅ Purchase order validation passed');
    } else {
      console.log('ℹ️  No purchase order provided, creating manual invoice');
    }

    // Check if invoice number already exists
    console.log('🔢 Checking invoice number uniqueness:', validatedData.invoiceNumber);
    const existingInvoice = await prisma.invoice.findUnique({
      where: { invoiceNumber: validatedData.invoiceNumber }
    });

    if (existingInvoice) {
      console.log('❌ Invoice number already exists:', validatedData.invoiceNumber);
      return NextResponse.json({
        error: 'Invoice number already exists'
      }, { status: 400 });
    }
    console.log('✅ Invoice number is unique');

    // Calculate totals
    console.log('💰 Calculating invoice totals...');
    let subtotal = 0;
    const items = validatedData.items.map(item => {
      console.log(`   Item: ${item.description}, Qty: ${item.quantity}, Price: ${item.unitPrice}`);
      const itemSubtotal = item.quantity * item.unitPrice;
      subtotal += itemSubtotal;
      return {
        ...item,
        subtotal: itemSubtotal,
      };
    });

    const taxPercentage = validatedData.taxPercentage || 0;
    const tax = (subtotal * taxPercentage) / 100;
    const total = subtotal + tax;
    console.log(`💰 Totals calculated - Subtotal: ${subtotal}, Tax: ${tax} (${taxPercentage}%), Total: ${total}`);

    // Validate installment amounts if enabled
    if (validatedData.enableInstallments && validatedData.installments) {
      const totalInstallmentAmount = validatedData.installments.reduce((sum, inst) => sum + inst.amount, 0);
      if (Math.abs(totalInstallmentAmount - total) > 0.01) {
        return NextResponse.json({
          error: `Installment amounts (${totalInstallmentAmount}) must equal invoice total (${total})`
        }, { status: 400 });
      }
    }

    // Create invoice with items and installments
    console.log('💾 Creating invoice in database...');
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber: validatedData.invoiceNumber,
        purchaseOrderId: validatedData.purchaseOrderId || null, // Handle optional PO
        supplierId: validatedData.supplierId, // Use validated supplierId
        invoiceDate: new Date(validatedData.invoiceDate),
        dueDate: validatedData.dueDate ? new Date(validatedData.dueDate) : null,
        subtotal: new Decimal(subtotal),
        tax: new Decimal(tax),
        taxPercentage: taxPercentage ? new Decimal(taxPercentage) : null,
        total: new Decimal(total),
        hasInstallments: validatedData.enableInstallments || false,
        numberOfInstallments: validatedData.numberOfInstallments,
        notes: validatedData.notes,
        attachmentUrl: validatedData.attachmentUrl,
        createdById: auth.user.id,
        items: {
          create: items.map(item => ({
            purchaseOrderItemId: item.purchaseOrderItemId,
            productId: item.productId,
            description: item.description,
            quantity: new Decimal(item.quantity),
            unitPrice: new Decimal(item.unitPrice),
            subtotal: new Decimal(item.subtotal),
          }))
        },
        ...(validatedData.enableInstallments && validatedData.installments && {
          installments: {
            create: validatedData.installments.map((installment, index) => ({
              installmentNumber: index + 1,
              dueDate: new Date(installment.dueDate),
              amount: new Decimal(installment.amount),
              description: installment.description,
            }))
          }
        })
      },
      include: {
        supplier: {
          select: { id: true, name: true, email: true }
        },
        purchaseOrder: {
          select: { id: true, orderDate: true }
        },
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        items: {
          include: {
            product: {
              select: { id: true, name: true, sku: true }
            }
          }
        },
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    // Transform decimal fields for response
    const transformedInvoice = {
      ...invoice,
      subtotal: Number(invoice.subtotal),
      tax: Number(invoice.tax),
      taxPercentage: invoice.taxPercentage ? Number(invoice.taxPercentage) : null,
      total: Number(invoice.total),
      paidAmount: Number(invoice.paidAmount),
      items: invoice.items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
      })),
      installments: invoice.installments?.map(installment => ({
        ...installment,
        amount: Number(installment.amount),
        paidAmount: Number(installment.paidAmount),
      })) || [],
    };

    console.log('✅ Invoice created successfully:', invoice.id);
    return NextResponse.json(transformedInvoice, { status: 201 });

  } catch (error) {
    console.error('❌ Error creating invoice:', error);

    // Log detailed error information
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    if (error instanceof z.ZodError) {
      console.error('🔍 Zod validation error details:', error.errors);
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors
      }, { status: 400 });
    }

    // Check for Prisma errors
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('🗄️  Prisma error code:', (error as any).code);
      console.error('🗄️  Prisma error meta:', (error as any).meta);
    }

    // Ensure we always return a proper error response
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    console.error('📤 Returning error response:', errorMessage);

    return NextResponse.json(
      {
        error: 'Failed to create invoice',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
