import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

/**
 * Generate a unique invoice number
 * Format: INV-YYYY-MM-NNNN (e.g., INV-2024-01-0001)
 */
async function generateInvoiceNumber(): Promise<string> {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const prefix = `INV-${year}-${month}`;

  // Find the highest invoice number for this month
  const lastInvoice = await prisma.invoice.findFirst({
    where: {
      invoiceNumber: {
        startsWith: prefix
      }
    },
    orderBy: {
      invoiceNumber: 'desc'
    }
  });

  let nextNumber = 1;
  
  if (lastInvoice) {
    // Extract the sequence number from the last invoice
    const lastNumber = lastInvoice.invoiceNumber.split('-').pop();
    if (lastNumber) {
      nextNumber = parseInt(lastNumber) + 1;
    }
  }

  const sequenceNumber = String(nextNumber).padStart(4, '0');
  return `${prefix}-${sequenceNumber}`;
}

// GET /api/invoices/generate-number - Generate a new invoice number
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check permissions - only users who can create invoices should generate numbers
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const invoiceNumber = await generateInvoiceNumber();

    return NextResponse.json({ 
      invoiceNumber,
      generated: true,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error generating invoice number:', error);
    return NextResponse.json(
      { error: 'Failed to generate invoice number' },
      { status: 500 }
    );
  }
}

// POST /api/invoices/generate-number - Generate a new invoice number (alternative method)
export async function POST(request: NextRequest) {
  // Same logic as GET for flexibility
  return GET(request);
}
