import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { getInvoiceSummaryStats } from '@/lib/invoice-utils';

// GET /api/invoices/summary - Get invoice summary statistics
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get('supplierId');

    const summary = await getInvoiceSummaryStats(supplierId || undefined);

    return NextResponse.json(summary);

  } catch (error) {
    console.error('Error fetching invoice summary:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoice summary' },
      { status: 500 }
    );
  }
}
