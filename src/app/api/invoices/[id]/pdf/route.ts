import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication
    console.log('PDF endpoint: Verifying authentication...');
    const auth = await verifyAuthToken(request);
    console.log('PDF endpoint: Auth result:', { success: auth.success, authenticated: auth.authenticated, error: auth.error });

    if (!auth.success && !auth.authenticated) {
      console.log('PDF endpoint: Authentication failed');
      return NextResponse.json({ error: 'Unauthorized', details: auth.error }, { status: 401 });
    }

    const { id } = await params;

    // Fetch invoice with all related data
    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        approvedBy: {
          select: { id: true, name: true, email: true }
        },
        items: {
          include: {
            product: {
              include: {
                unit: true
              }
            }
          }
        },
        payments: {
          include: {
            createdBy: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { paymentDate: 'desc' }
        },
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Generate HTML content for the PDF
    const htmlContent = generateInvoiceHTML(invoice);

    // For now, we'll return the HTML content as a simple PDF-like response
    // In a production environment, you would use a library like puppeteer or jsPDF
    // to generate actual PDF content
    
    return new NextResponse(htmlContent, {
      headers: {
        'Content-Type': 'text/html',
        'Content-Disposition': `inline; filename="invoice-${invoice.invoiceNumber}.html"`,
      },
    });

  } catch (error) {
    console.error('Error generating invoice PDF:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}

function generateInvoiceHTML(invoice: any): string {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice ${invoice.invoiceNumber}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.6;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
        }
        .company-info h1 {
            margin: 0;
            color: #2563eb;
            font-size: 28px;
        }
        .company-info p {
            margin: 5px 0;
            color: #666;
        }
        .invoice-details {
            text-align: right;
        }
        .invoice-details h2 {
            margin: 0;
            font-size: 24px;
            color: #333;
        }
        .invoice-details p {
            margin: 5px 0;
        }
        .billing-info {
            display: flex;
            justify-content: space-between;
            margin: 30px 0;
        }
        .billing-section {
            flex: 1;
            margin-right: 20px;
        }
        .billing-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .items-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .items-table .text-right {
            text-align: right;
        }
        .totals {
            margin-left: auto;
            width: 300px;
            margin-top: 20px;
        }
        .totals table {
            width: 100%;
            border-collapse: collapse;
        }
        .totals td {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        .totals .total-row {
            font-weight: bold;
            font-size: 18px;
            border-top: 2px solid #333;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-pending { background-color: #fef3c7; color: #92400e; }
        .status-approved { background-color: #d1fae5; color: #065f46; }
        .status-rejected { background-color: #fee2e2; color: #991b1b; }
        .payment-unpaid { background-color: #fee2e2; color: #991b1b; }
        .payment-paid { background-color: #d1fae5; color: #065f46; }
        .payment-partially-paid { background-color: #fef3c7; color: #92400e; }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            text-align: center;
            color: #666;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="invoice-header">
        <div class="company-info">
            <h1>NPOS System</h1>
            <p>Invoice Management System</p>
            <p>Generated on ${formatDate(new Date().toISOString())}</p>
        </div>
        <div class="invoice-details">
            <h2>INVOICE</h2>
            <p><strong>Invoice #:</strong> ${invoice.invoiceNumber}</p>
            <p><strong>Date:</strong> ${formatDate(invoice.invoiceDate)}</p>
            ${invoice.dueDate ? `<p><strong>Due Date:</strong> ${formatDate(invoice.dueDate)}</p>` : ''}
            <p><span class="status-badge status-${invoice.status.toLowerCase()}">${invoice.status}</span></p>
            <p><span class="status-badge payment-${invoice.paymentStatus.toLowerCase().replace('_', '-')}">${invoice.paymentStatus.replace('_', ' ')}</span></p>
        </div>
    </div>

    <div class="billing-info">
        <div class="billing-section">
            <h3>Bill To:</h3>
            <p><strong>${invoice.supplier.name}</strong></p>
            ${invoice.supplier.contactPerson ? `<p>${invoice.supplier.contactPerson}</p>` : ''}
            ${invoice.supplier.email ? `<p>${invoice.supplier.email}</p>` : ''}
            ${invoice.supplier.phone ? `<p>${invoice.supplier.phone}</p>` : ''}
            ${invoice.supplier.address ? `<p>${invoice.supplier.address}</p>` : ''}
        </div>
        <div class="billing-section">
            <h3>Invoice Details:</h3>
            <p><strong>Created By:</strong> ${invoice.createdBy.name}</p>
            ${invoice.approvedBy ? `<p><strong>Approved By:</strong> ${invoice.approvedBy.name}</p>` : ''}
            <p><strong>Total Items:</strong> ${invoice.items.length}</p>
            ${invoice.notes ? `<p><strong>Notes:</strong> ${invoice.notes}</p>` : ''}
        </div>
    </div>

    <table class="items-table">
        <thead>
            <tr>
                <th>Product</th>
                <th>SKU</th>
                <th class="text-right">Quantity</th>
                <th class="text-right">Unit Price</th>
                <th class="text-right">Subtotal</th>
            </tr>
        </thead>
        <tbody>
            ${invoice.items.map((item: any) => `
                <tr>
                    <td>
                        <strong>${item.product.name}</strong>
                        ${item.description !== item.product.name ? `<br><small>${item.description}</small>` : ''}
                    </td>
                    <td>${item.product.sku}</td>
                    <td class="text-right">${item.quantity} ${item.product.unit?.abbreviation || 'pcs'}</td>
                    <td class="text-right">${formatCurrency(Number(item.unitPrice))}</td>
                    <td class="text-right">${formatCurrency(Number(item.subtotal))}</td>
                </tr>
            `).join('')}
        </tbody>
    </table>

    <div class="totals">
        <table>
            <tr>
                <td>Subtotal:</td>
                <td class="text-right">${formatCurrency(Number(invoice.subtotal))}</td>
            </tr>
            ${invoice.taxPercentage ? `
                <tr>
                    <td>Tax (${invoice.taxPercentage}%):</td>
                    <td class="text-right">${formatCurrency(Number(invoice.tax))}</td>
                </tr>
            ` : ''}
            <tr class="total-row">
                <td>Total:</td>
                <td class="text-right">${formatCurrency(Number(invoice.total))}</td>
            </tr>
            ${invoice.paidAmount > 0 ? `
                <tr>
                    <td>Paid Amount:</td>
                    <td class="text-right" style="color: green;">${formatCurrency(Number(invoice.paidAmount))}</td>
                </tr>
                <tr>
                    <td>Remaining:</td>
                    <td class="text-right" style="color: red;">${formatCurrency(Number(invoice.total) - Number(invoice.paidAmount))}</td>
                </tr>
            ` : ''}
        </table>
    </div>

    ${invoice.installments && invoice.installments.length > 0 ? `
        <div style="margin-top: 30px;">
            <h3>Installment Schedule</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Installment</th>
                        <th>Due Date</th>
                        <th class="text-right">Amount</th>
                        <th>Status</th>
                        <th class="text-right">Paid Amount</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.installments.map((inst: any) => `
                        <tr>
                            <td>#${inst.installmentNumber} ${inst.description ? `- ${inst.description}` : ''}</td>
                            <td>${formatDate(inst.dueDate)}</td>
                            <td class="text-right">${formatCurrency(Number(inst.amount))}</td>
                            <td><span class="status-badge status-${inst.status.toLowerCase()}">${inst.status}</span></td>
                            <td class="text-right">${inst.paidAmount > 0 ? formatCurrency(Number(inst.paidAmount)) : '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    ` : ''}

    ${invoice.payments && invoice.payments.length > 0 ? `
        <div style="margin-top: 30px;">
            <h3>Payment History</h3>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th class="text-right">Amount</th>
                        <th>Method</th>
                        <th>Reference</th>
                        <th>Recorded By</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoice.payments.map((payment: any) => `
                        <tr>
                            <td>${formatDate(payment.paymentDate)}</td>
                            <td class="text-right">${formatCurrency(Number(payment.amount))}</td>
                            <td>${payment.paymentMethod}</td>
                            <td>${payment.paymentReference || '-'}</td>
                            <td>${payment.createdBy.name}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    ` : ''}

    <div class="footer">
        <p>This is a computer-generated invoice from NPOS System.</p>
        <p>For any questions regarding this invoice, please contact the finance department.</p>
    </div>

    <script>
        // Auto-print when opened in new window
        if (window.location.search.includes('print=true')) {
            window.print();
        }
    </script>
</body>
</html>
  `;
}
