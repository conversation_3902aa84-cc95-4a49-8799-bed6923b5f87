import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';
import { saveFileToLocal } from '@/lib/file-upload-server';
import { notifyInvoicePaymentMade } from '@/lib/notifications/financial';

// Validation schema for creating payments
const createPaymentSchema = z.object({
  amount: z.number().positive("Amount must be positive"),
  paymentDate: z.string().datetime("Invalid payment date").optional(),
  paymentMethod: z.string().min(1, "Payment method is required"),
  paymentReference: z.string().optional(),
  notes: z.string().optional(),
  proofImageUrl: z.string().optional(), // Will be set after file upload
});

// GET /api/invoices/[id]/payments - Get invoice payments
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { id: invoiceId } = await params;

    // Verify invoice exists
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      select: { id: true, total: true, paidAmount: true }
    });

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    const payments = await prisma.invoicePayment.findMany({
      where: { invoiceId },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true }
        }
      },
      orderBy: { paymentDate: 'desc' }
    });

    // Transform decimal fields for response
    const transformedPayments = payments.map(payment => ({
      ...payment,
      amount: Number(payment.amount),
    }));

    return NextResponse.json({
      payments: transformedPayments,
      summary: {
        totalAmount: Number(invoice.total),
        paidAmount: Number(invoice.paidAmount),
        remainingAmount: Number(invoice.total) - Number(invoice.paidAmount),
        paymentCount: payments.length
      }
    });

  } catch (error) {
    console.error('Error fetching invoice payments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payments' },
      { status: 500 }
    );
  }
}

// POST /api/invoices/[id]/payments - Create payment
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id: invoiceId } = await params;

    // Handle both JSON and FormData
    let validatedData: any;
    let proofImageUrl: string | undefined;

    const contentType = request.headers.get('content-type');

    if (contentType?.includes('multipart/form-data')) {
      // Handle FormData with file upload
      const formData = await request.formData();

      // Extract form fields
      const formFields = {
        amount: Number(formData.get('amount')),
        paymentDate: formData.get('paymentDate') as string || undefined,
        paymentMethod: formData.get('paymentMethod') as string,
        paymentReference: formData.get('paymentReference') as string || undefined,
        notes: formData.get('notes') as string || undefined,
      };

      // Handle file upload if present
      const proofFile = formData.get('proofImage') as File;
      if (proofFile && proofFile.size > 0) {
        const uploadResult = await saveFileToLocal(proofFile, 'uploads/payment-proofs', 'payment-proof');
        if (!uploadResult.success) {
          return NextResponse.json({
            error: `File upload failed: ${uploadResult.error}`
          }, { status: 400 });
        }
        proofImageUrl = uploadResult.filePath;
      }

      validatedData = createPaymentSchema.parse({
        ...formFields,
        proofImageUrl
      });
    } else {
      // Handle JSON data (backward compatibility)
      const body = await request.json();
      validatedData = createPaymentSchema.parse(body);
    }

    // Verify invoice exists and is approved
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    if (invoice.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: 'Invoice must be approved before payments can be recorded' 
      }, { status: 400 });
    }

    if (invoice.paymentStatus === 'PAID') {
      return NextResponse.json({ 
        error: 'Invoice is already fully paid' 
      }, { status: 400 });
    }

    const currentPaidAmount = Number(invoice.paidAmount);
    const totalAmount = Number(invoice.total);
    const remainingAmount = totalAmount - currentPaidAmount;

    if (validatedData.amount > remainingAmount) {
      return NextResponse.json({ 
        error: `Payment amount (${validatedData.amount}) exceeds remaining balance (${remainingAmount})` 
      }, { status: 400 });
    }

    // Create payment and update invoice in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create payment record
      const payment = await tx.invoicePayment.create({
        data: {
          invoiceId,
          amount: new Decimal(validatedData.amount),
          paymentDate: validatedData.paymentDate ? new Date(validatedData.paymentDate) : new Date(),
          paymentMethod: validatedData.paymentMethod,
          paymentReference: validatedData.paymentReference,
          notes: validatedData.notes,
          proofImageUrl: validatedData.proofImageUrl,
          createdById: auth.user.id,
        },
        include: {
          createdBy: {
            select: { id: true, name: true, email: true }
          }
        }
      });

      // Calculate new paid amount and payment status
      const newPaidAmount = currentPaidAmount + validatedData.amount;
      let newPaymentStatus: 'UNPAID' | 'PARTIALLY_PAID' | 'PAID' = 'PARTIALLY_PAID';
      
      if (newPaidAmount >= totalAmount) {
        newPaymentStatus = 'PAID';
      } else if (newPaidAmount > 0) {
        newPaymentStatus = 'PARTIALLY_PAID';
      } else {
        newPaymentStatus = 'UNPAID';
      }

      // Update installments if they exist
      let updatedInstallments = [];
      if (invoice.installments && invoice.installments.length > 0) {
        // Apply payment to installments in order (FIFO)
        let remainingPayment = validatedData.amount;

        for (const installment of invoice.installments) {
          if (remainingPayment <= 0) break;

          const currentPaid = Number(installment.paidAmount);
          const installmentAmount = Number(installment.amount);
          const remainingForInstallment = installmentAmount - currentPaid;

          if (remainingForInstallment > 0) {
            const paymentForThisInstallment = Math.min(remainingPayment, remainingForInstallment);
            const newInstallmentPaid = currentPaid + paymentForThisInstallment;
            const isInstallmentFullyPaid = newInstallmentPaid >= installmentAmount;

            const updatedInstallment = await tx.invoiceInstallment.update({
              where: { id: installment.id },
              data: {
                paidAmount: new Decimal(newInstallmentPaid),
                status: isInstallmentFullyPaid ? 'PAID' : 'PENDING', // Keep as PENDING if partially paid
                paidAt: isInstallmentFullyPaid ? new Date() : installment.paidAt,
              }
            });

            updatedInstallments.push(updatedInstallment);
            remainingPayment -= paymentForThisInstallment;
          }
        }
      }

      // Update invoice
      const updatedInvoice = await tx.invoice.update({
        where: { id: invoiceId },
        data: {
          paidAmount: new Decimal(newPaidAmount),
          paymentStatus: newPaymentStatus,
          paidAt: newPaymentStatus === 'PAID' ? new Date() : null,
          paymentMethod: validatedData.paymentMethod,
          paymentReference: validatedData.paymentReference,
        }
      });

      return { payment, updatedInvoice, updatedInstallments };
    });

    // Transform decimal fields for response
    const transformedPayment = {
      ...result.payment,
      amount: Number(result.payment.amount),
    };

    // Trigger payment notification
    try {
      const remainingBalance = totalAmount - Number(result.updatedInvoice.paidAmount);
      await notifyInvoicePaymentMade(
        invoiceId,
        invoice.invoiceNumber,
        validatedData.amount,
        validatedData.paymentMethod,
        remainingBalance > 0 ? remainingBalance : undefined,
        {
          paymentDate: validatedData.paymentDate || new Date().toISOString(),
          paymentReference: validatedData.paymentReference,
          paymentId: result.payment.id,
          createdById: auth.user.id,
          createdByName: auth.user.name,
          previousPaidAmount: currentPaidAmount,
          newPaymentStatus: result.updatedInvoice.paymentStatus,
        }
      );
      console.log(`✅ Invoice payment notification sent for ${invoice.invoiceNumber} - ${validatedData.amount}`);
    } catch (notificationError) {
      console.error('❌ Error sending invoice payment notification:', notificationError);
      // Don't fail the request if notification fails
    }

    return NextResponse.json({
      payment: transformedPayment,
      invoice: {
        id: result.updatedInvoice.id,
        paidAmount: Number(result.updatedInvoice.paidAmount),
        paymentStatus: result.updatedInvoice.paymentStatus,
        remainingAmount: totalAmount - Number(result.updatedInvoice.paidAmount),
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating payment:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    );
  }
}
