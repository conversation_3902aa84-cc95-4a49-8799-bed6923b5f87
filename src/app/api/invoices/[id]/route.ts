import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';
import { notifyInvoiceApproved } from '@/lib/notifications/financial';

// Validation schema for status updates only
const statusUpdateSchema = z.object({
  status: z.enum(['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED']).optional(),
  paymentStatus: z.enum(['UNPAID', 'PARTIALLY_PAID', 'PAID', 'OVERDUE']).optional(),
  paymentMethod: z.string().optional(),
  paymentReference: z.string().optional(),
  notes: z.string().optional(),
  attachmentUrl: z.string().url().optional(),
});

// Validation schema for full invoice updates
const fullUpdateInvoiceSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required").optional(),
  supplierId: z.string().min(1, "Supplier ID is required").optional(),
  invoiceDate: z.string().datetime("Invalid invoice date").optional(),
  dueDate: z.string().datetime("Invalid due date").optional(),
  taxPercentage: z.coerce.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  attachmentUrl: z.string().url().optional(),
  items: z.array(z.object({
    id: z.string().optional(),
    productId: z.string().min(1, "Product ID is required"),
    description: z.string().min(1, "Description is required"),
    quantity: z.coerce.number().positive("Quantity must be positive"),
    unitPrice: z.coerce.number().positive("Unit price must be positive"),
  })).optional(),
});

// GET /api/invoices/[id] - Get invoice details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { id } = await params;

    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        supplier: {
          select: { 
            id: true, 
            name: true, 
            email: true, 
            phone: true, 
            address: true,
            contactPerson: true 
          }
        },
        purchaseOrder: {
          select: { 
            id: true, 
            orderDate: true, 
            status: true,
            expectedDeliveryDate: true,
            receivedAt: true
          }
        },
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        approvedBy: {
          select: { id: true, name: true, email: true }
        },
        items: {
          include: {
            product: {
              select: { 
                id: true, 
                name: true, 
                sku: true,
                unit: {
                  select: { name: true, abbreviation: true }
                }
              }
            },
            purchaseOrderItem: {
              select: {
                id: true,
                quantity: true,
                receivedQuantity: true,
                unitPrice: true
              }
            }
          }
        },
        payments: {
          include: {
            createdBy: {
              select: { id: true, name: true, email: true }
            }
          },
          orderBy: { paymentDate: 'desc' }
        },
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    if (!invoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Transform decimal fields for response
    const transformedInvoice = {
      ...invoice,
      subtotal: Number(invoice.subtotal),
      tax: Number(invoice.tax),
      taxPercentage: invoice.taxPercentage ? Number(invoice.taxPercentage) : null,
      total: Number(invoice.total),
      paidAmount: Number(invoice.paidAmount),
      items: invoice.items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
        purchaseOrderItem: item.purchaseOrderItem ? {
          ...item.purchaseOrderItem,
          quantity: Number(item.purchaseOrderItem.quantity),
          receivedQuantity: Number(item.purchaseOrderItem.receivedQuantity),
          unitPrice: Number(item.purchaseOrderItem.unitPrice),
        } : null,
      })),
      payments: invoice.payments.map(payment => ({
        ...payment,
        amount: Number(payment.amount),
      })),
      installments: invoice.installments.map(installment => ({
        ...installment,
        amount: Number(installment.amount),
        paidAmount: Number(installment.paidAmount),
      })),
    };

    return NextResponse.json(transformedInvoice);

  } catch (error) {
    console.error('Error fetching invoice:', error);
    return NextResponse.json(
      { error: 'Failed to fetch invoice' },
      { status: 500 }
    );
  }
}

// PUT /api/invoices/[id] - Update invoice
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        payments: true,
        items: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Determine if this is a status update or full invoice update
    const isStatusUpdate = body.status !== undefined && Object.keys(body).length <= 3; // status, notes, and maybe one more field

    let validatedData: any;
    if (isStatusUpdate) {
      validatedData = statusUpdateSchema.parse(body);
    } else {
      validatedData = fullUpdateInvoiceSchema.parse(body);

      // Check if invoice can be edited (only PENDING invoices with no payments)
      if (existingInvoice.status !== 'PENDING') {
        return NextResponse.json({
          error: 'Only pending invoices can be edited'
        }, { status: 400 });
      }

      if (existingInvoice.payments.length > 0) {
        return NextResponse.json({
          error: 'Cannot edit invoice with payments'
        }, { status: 400 });
      }
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (isStatusUpdate) {
      // Handle status updates
      Object.assign(updateData, validatedData);

      // Handle status changes
      if (validatedData.status === 'APPROVED' && existingInvoice.status !== 'APPROVED') {
        updateData.approvedById = auth.user.id;
        updateData.approvedAt = new Date();
      }

      // Clear PO association when invoice is cancelled or rejected
      if (validatedData.status === 'CANCELLED' || validatedData.status === 'REJECTED') {
        updateData.purchaseOrderId = null;
      }
    } else {
      // Handle full invoice updates
      if (validatedData.invoiceNumber) updateData.invoiceNumber = validatedData.invoiceNumber;
      if (validatedData.supplierId) updateData.supplierId = validatedData.supplierId;
      if (validatedData.invoiceDate) updateData.invoiceDate = new Date(validatedData.invoiceDate);
      if (validatedData.dueDate) updateData.dueDate = new Date(validatedData.dueDate);
      if (validatedData.taxPercentage !== undefined) updateData.taxPercentage = validatedData.taxPercentage;
      if (validatedData.notes !== undefined) updateData.notes = validatedData.notes;
      if (validatedData.attachmentUrl !== undefined) updateData.attachmentUrl = validatedData.attachmentUrl;

      // Handle items update
      if (validatedData.items) {
        // Calculate totals
        let subtotal = 0;
        const items = validatedData.items.map((item: any) => {
          const itemSubtotal = item.quantity * item.unitPrice;
          subtotal += itemSubtotal;
          return {
            productId: item.productId,
            description: item.description,
            quantity: new Decimal(item.quantity),
            unitPrice: new Decimal(item.unitPrice),
            subtotal: new Decimal(itemSubtotal),
          };
        });

        const taxPercentage = validatedData.taxPercentage ?? existingInvoice.taxPercentage ?? 0;
        const tax = subtotal * (Number(taxPercentage) / 100);
        const total = subtotal + tax;

        updateData.subtotal = new Decimal(subtotal);
        updateData.tax = new Decimal(tax);
        updateData.total = new Decimal(total);

        // Delete existing items and create new ones
        await prisma.invoiceItem.deleteMany({
          where: { invoiceId: id }
        });

        updateData.items = {
          create: items
        };
      }
    }

    // Update invoice
    const updatedInvoice = await prisma.invoice.update({
      where: { id },
      data: updateData,
      include: {
        supplier: {
          select: { id: true, name: true, email: true }
        },
        purchaseOrder: {
          select: { id: true, orderDate: true }
        },
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        approvedBy: {
          select: { id: true, name: true, email: true }
        },
        items: {
          include: {
            product: {
              select: { id: true, name: true, sku: true }
            }
          }
        },
        payments: {
          include: {
            createdBy: {
              select: { id: true, name: true, email: true }
            }
          }
        },
        installments: {
          orderBy: { installmentNumber: 'asc' }
        }
      }
    });

    // Trigger notification for invoice approval
    if (isStatusUpdate && validatedData.status === 'APPROVED' && existingInvoice.status !== 'APPROVED') {
      try {
        await notifyInvoiceApproved(
          updatedInvoice.id,
          updatedInvoice.invoiceNumber,
          updatedInvoice.supplier.name,
          Number(updatedInvoice.total),
          auth.user.name,
          {
            approvalDate: new Date().toISOString(),
            approvedById: auth.user.id,
            previousStatus: existingInvoice.status,
          }
        );
        console.log(`✅ Invoice approval notification sent for ${updatedInvoice.invoiceNumber}`);
      } catch (notificationError) {
        console.error('❌ Error sending invoice approval notification:', notificationError);
        // Don't fail the request if notification fails
      }
    }

    // Transform decimal fields for response
    const transformedInvoice = {
      ...updatedInvoice,
      subtotal: Number(updatedInvoice.subtotal),
      tax: Number(updatedInvoice.tax),
      taxPercentage: updatedInvoice.taxPercentage ? Number(updatedInvoice.taxPercentage) : null,
      total: Number(updatedInvoice.total),
      paidAmount: Number(updatedInvoice.paidAmount),
      items: updatedInvoice.items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
      })),
      payments: updatedInvoice.payments.map(payment => ({
        ...payment,
        amount: Number(payment.amount),
      })),
      installments: updatedInvoice.installments.map(installment => ({
        ...installment,
        amount: Number(installment.amount),
        paidAmount: Number(installment.paidAmount),
      })),
    };

    return NextResponse.json(transformedInvoice);

  } catch (error) {
    console.error('Error updating invoice:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update invoice' },
      { status: 500 }
    );
  }
}

// DELETE /api/invoices/[id] - Delete invoice
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check permissions - only SUPER_ADMIN can delete invoices
    if (auth.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        payments: true
      }
    });

    if (!existingInvoice) {
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Check if invoice has payments
    if (existingInvoice.payments.length > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete invoice with payments' 
      }, { status: 400 });
    }

    // Check if invoice is approved
    if (existingInvoice.status === 'APPROVED') {
      return NextResponse.json({ 
        error: 'Cannot delete approved invoice' 
      }, { status: 400 });
    }

    // Delete invoice (items will be deleted due to cascade)
    await prisma.invoice.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Invoice deleted successfully' });

  } catch (error) {
    console.error('Error deleting invoice:', error);
    return NextResponse.json(
      { error: 'Failed to delete invoice' },
      { status: 500 }
    );
  }
}
