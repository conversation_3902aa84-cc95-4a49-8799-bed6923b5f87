import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/units/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/units/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Unit update schema for validation
const unitUpdateSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  abbreviation: z.string().min(1, { message: "Abbreviation is required" }).optional(),
  description: z.string().optional().nullable(),
});

// GET /api/units/[id] - Get a specific unit
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const includeProducts = url.searchParams.get("includeProducts") === "true";

    // Get unit
    const unit = await prisma.unit.findUnique({
      where: { id: params.id },
      include: {
        products: includeProducts ? {
          include: {
            category: true,
            storeStock: true,
          },
          where: {
            active: true,
          },
          orderBy: {
            name: "asc",
          },
        } : undefined,
        _count: {
          select: { products: true }
        }
      },
    });

    // Check if unit exists
    if (!unit) {
      return NextResponse.json(
        { error: "Unit not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ unit });
  } catch (error) {
    console.error("Error fetching unit:", error);
    return NextResponse.json(
      { error: "Failed to fetch unit", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/units/[id] - Update a unit
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update units
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get unit
    const existingUnit = await prisma.unit.findUnique({
      where: { id: params.id },
    });

    // Check if unit exists
    if (!existingUnit) {
      return NextResponse.json(
        { error: "Unit not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate unit data
    const validationResult = unitUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const unitData = validationResult.data;

    // Check if unit name already exists (if changing name)
    if (unitData.name && unitData.name !== existingUnit.name) {
      const existingName = await prisma.unit.findUnique({
        where: { name: unitData.name },
      });

      if (existingName) {
        return NextResponse.json(
          { error: "Unit name already exists" },
          { status: 400 }
        );
      }
    }

    // Track changes for activity log
    const changes: string[] = [];
    if (unitData.name && unitData.name !== existingUnit.name) {
      changes.push(`name: ${existingUnit.name} → ${unitData.name}`);
    }
    if (unitData.abbreviation && unitData.abbreviation !== existingUnit.abbreviation) {
      changes.push(`abbreviation: ${existingUnit.abbreviation} → ${unitData.abbreviation}`);
    }
    if (unitData.description !== undefined && unitData.description !== existingUnit.description) {
      changes.push(`description updated`);
    }

    // Update unit
    const unit = await prisma.unit.update({
      where: { id: params.id },
      data: unitData,
      include: {
        _count: {
          select: { products: true }
        }
      },
    });

    // Log activity
    if (changes.length > 0) {
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UPDATE_UNIT",
          details: `Updated unit: ${unit.name} (${changes.join(", ")})`,
        },
      });
    }

    return NextResponse.json({ unit });
  } catch (error) {
    console.error("Error updating unit:", error);
    return NextResponse.json(
      { error: "Failed to update unit", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/units/[id] - Delete a unit
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete units
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get unit
    const unit = await prisma.unit.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { products: true }
        }
      },
    });

    // Check if unit exists
    if (!unit) {
      return NextResponse.json(
        { error: "Unit not found" },
        { status: 404 }
      );
    }

    // Check if unit has products
    if (unit._count.products > 0) {
      return NextResponse.json(
        { error: "Cannot delete unit with associated products" },
        { status: 400 }
      );
    }

    // Delete unit
    await prisma.unit.delete({
      where: { id: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_UNIT",
        details: `Deleted unit: ${unit.name} (${unit.abbreviation})`,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Unit deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting unit:", error);
    return NextResponse.json(
      { error: "Failed to delete unit", message: (error as Error).message },
      { status: 500 }
    );
  }
}
