import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { QualityTrendAnalysisService } from "@/lib/quality-trend-analysis";

// GET /api/analytics/quality-trends - Get quality trend analysis
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const analysisType = searchParams.get("type") || "global"; // "global" or "supplier"
    const supplierId = searchParams.get("supplierId");
    const timeframe = parseInt(searchParams.get("timeframe") || "90"); // days

    if (analysisType === "supplier") {
      if (!supplierId) {
        return NextResponse.json({ error: 'Supplier ID is required for supplier analysis' }, { status: 400 });
      }

      // Generate supplier-specific trend analysis
      const analysis = await QualityTrendAnalysisService.analyzeSupplierTrends(
        supplierId,
        timeframe
      );

      // Transform supplier analysis to match the frontend interface
      const response = {
        trends: [{
          supplierId: analysis.supplierId,
          supplierName: analysis.supplierName,
          currentPeriod: {
            qualityScore: analysis.currentMetrics.qualityScore,
            returnRate: analysis.currentMetrics.returnRate,
            defectRate: analysis.currentMetrics.defectRate,
            returnValue: analysis.currentMetrics.returnValue,
          },
          trends: {
            qualityScoreTrend: 0, // Placeholder - would need historical comparison
            returnRateTrend: 0,
            defectRateTrend: 0,
          },
          riskLevel: analysis.currentMetrics.riskLevel,
          prediction: {
            nextPeriodQualityScore: analysis.predictions.nextMonthQualityScore,
            nextPeriodReturnRate: analysis.predictions.nextMonthReturnRate,
            confidence: 85, // Placeholder
            recommendations: analysis.predictions.recommendedActions,
          },
          monthlyData: [], // Placeholder - would transform trendData
        }],
        summary: {
          totalSuppliers: 1,
          improvingSuppliers: analysis.currentMetrics.trend === 'improving' ? 1 : 0,
          decliningSuppliers: analysis.currentMetrics.trend === 'declining' ? 1 : 0,
          stableSuppliers: analysis.currentMetrics.trend === 'stable' ? 1 : 0,
          averageQualityScore: analysis.currentMetrics.qualityScore,
          averageQualityTrend: 0, // Placeholder
          highRiskSuppliers: analysis.currentMetrics.riskLevel === 'high' || analysis.currentMetrics.riskLevel === 'critical' ? 1 : 0,
          alertsTriggered: analysis.alerts.length,
        },
        alerts: analysis.alerts.map(alert => ({
          supplierId: analysis.supplierId,
          supplierName: analysis.supplierName,
          alertType: alert.type,
          severity: alert.severity,
          message: alert.description,
          triggeredAt: alert.createdAt,
          threshold: alert.thresholdValue || 0,
          currentValue: alert.currentValue,
        })),
        predictions: {
          overallTrend: analysis.currentMetrics.trend,
          expectedQualityScore: analysis.predictions.nextMonthQualityScore,
          confidence: 85, // Placeholder
          timeframe: "next month",
        },
        generatedAt: new Date().toISOString(),
      };

      return NextResponse.json(response);

    } else {
      // Generate global trend analysis
      const globalTrends = await QualityTrendAnalysisService.analyzeGlobalTrends(timeframe);

      // Transform the global trends to match the frontend interface
      const response = {
        trends: [], // Placeholder - would need individual supplier trend analysis
        summary: {
          totalSuppliers: globalTrends.totalSuppliers,
          improvingSuppliers: Math.floor(globalTrends.totalSuppliers * 0.3), // Placeholder
          decliningSuppliers: globalTrends.suppliersAtRisk,
          stableSuppliers: globalTrends.totalSuppliers - globalTrends.suppliersAtRisk - Math.floor(globalTrends.totalSuppliers * 0.3),
          averageQualityScore: globalTrends.averageQualityScore,
          averageQualityTrend: globalTrends.trendAnalysis.qualityScoreTrend,
          highRiskSuppliers: globalTrends.suppliersAtRisk,
          alertsTriggered: globalTrends.criticalAlerts,
        },
        alerts: [], // Placeholder - would need to collect alerts from all suppliers
        predictions: {
          overallTrend: globalTrends.overallTrend,
          expectedQualityScore: Math.max(0, Math.min(100, globalTrends.averageQualityScore + globalTrends.trendAnalysis.qualityScoreTrend)),
          confidence: 75, // Placeholder
          timeframe: "next month",
        },
        generatedAt: new Date().toISOString(),
      };

      return NextResponse.json(response);
    }

  } catch (error) {
    console.error("Error generating quality trend analysis:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate quality trend analysis",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
