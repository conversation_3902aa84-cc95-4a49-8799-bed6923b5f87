import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { analyzeBatchReturns, generateSupplierReturnTraceability } from "@/lib/return-batch-analytics";

// GET /api/analytics/batch-returns - Get batch return analysis
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get("supplierId");
    const timeRange = searchParams.get("timeRange") || "3months";
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");
    const analysisType = searchParams.get("type") || "batch"; // "batch" or "supplier"

    // Calculate date range
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam);
      endDate = new Date(endDateParam);
    } else {
      endDate = new Date();
      switch (timeRange) {
        case "1month":
          startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case "3months":
          startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case "6months":
          startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case "1year":
          startDate = new Date();
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        case "all":
        default:
          startDate = undefined;
          endDate = undefined;
          break;
      }
    }

    if (analysisType === "supplier" && supplierId) {
      // Generate supplier-specific traceability report
      const supplierReport = await generateSupplierReturnTraceability(
        supplierId,
        startDate,
        endDate
      );

      return NextResponse.json({
        success: true,
        analysisType: "supplier",
        timeRange: `${startDate?.toISOString().split('T')[0] || 'All time'} - ${endDate?.toISOString().split('T')[0] || 'Present'}`,
        data: supplierReport
      });
    } else {
      // Generate batch-level analysis
      const batchAnalysis = await analyzeBatchReturns(
        supplierId || undefined,
        startDate,
        endDate
      );

      // Calculate summary statistics
      const summary = {
        totalBatches: batchAnalysis.length,
        totalReturnValue: batchAnalysis.reduce((sum, b) => sum + b.returnValue, 0),
        averageReturnRate: batchAnalysis.length > 0 
          ? batchAnalysis.reduce((sum, b) => sum + b.returnRate, 0) / batchAnalysis.length 
          : 0,
        highRiskBatches: batchAnalysis.filter(b => b.returnRate > 10).length,
        uniqueSuppliers: new Set(batchAnalysis.map(b => b.supplierId)).size,
        topDefectTypes: getTopDefectTypes(batchAnalysis),
        qualityDistribution: getQualityDistribution(batchAnalysis),
      };

      return NextResponse.json({
        success: true,
        analysisType: "batch",
        timeRange: `${startDate?.toISOString().split('T')[0] || 'All time'} - ${endDate?.toISOString().split('T')[0] || 'Present'}`,
        summary,
        data: batchAnalysis
      });
    }

  } catch (error) {
    console.error("Error generating batch return analysis:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate batch return analysis",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

function getTopDefectTypes(batchAnalysis: any[]) {
  const defectMap = new Map<string, { count: number; affectedBatches: number; totalQuantity: number }>();
  
  for (const batch of batchAnalysis) {
    const batchDefectTypes = new Set<string>();
    for (const defect of batch.defectTypes) {
      if (!defectMap.has(defect.defectType)) {
        defectMap.set(defect.defectType, { count: 0, affectedBatches: 0, totalQuantity: 0 });
      }
      const defectData = defectMap.get(defect.defectType)!;
      defectData.count += defect.count;
      defectData.totalQuantity += defect.quantity;
      batchDefectTypes.add(defect.defectType);
    }
    // Count affected batches
    for (const defectType of batchDefectTypes) {
      defectMap.get(defectType)!.affectedBatches++;
    }
  }

  return Array.from(defectMap.entries())
    .map(([defectType, data]) => ({
      defectType,
      count: data.count,
      affectedBatches: data.affectedBatches,
      totalQuantity: data.totalQuantity,
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
}

function getQualityDistribution(batchAnalysis: any[]) {
  const distribution = {
    excellent: 0, // 90-100
    good: 0,      // 70-89
    fair: 0,      // 50-69
    poor: 0,      // 0-49
  };

  for (const batch of batchAnalysis) {
    if (batch.qualityScore >= 90) distribution.excellent++;
    else if (batch.qualityScore >= 70) distribution.good++;
    else if (batch.qualityScore >= 50) distribution.fair++;
    else distribution.poor++;
  }

  return distribution;
}
