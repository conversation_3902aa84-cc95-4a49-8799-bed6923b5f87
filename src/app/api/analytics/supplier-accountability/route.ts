import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { generateSupplierReturnTraceability } from "@/lib/return-batch-analytics";
import { prisma } from "@/auth";

export interface SupplierAccountabilityReport {
  supplierId: string;
  supplierName: string;
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  overallMetrics: {
    totalPurchaseValue: number;
    totalReturnValue: number;
    returnValuePercentage: number;
    totalReturnedItems: number;
    totalPurchasedItems: number;
    returnRatePercentage: number;
    averageReturnProcessingDays: number;
    qualityScore: number;
    riskLevel: 'low' | 'medium' | 'high';
  };
  productBreakdown: Array<{
    productId: string;
    productName: string;
    sku: string;
    totalPurchased: number;
    totalReturned: number;
    returnRate: number;
    returnValue: number;
    topReturnReasons: Array<{
      reason: string;
      count: number;
      percentage: number;
    }>;
    defectTypes: Array<{
      defectType: string;
      count: number;
      percentage: number;
    }>;
  }>;
  batchAnalysis: {
    totalBatches: number;
    batchesWithReturns: number;
    batchReturnRate: number;
    worstPerformingBatches: Array<{
      batchId: string;
      batchNumber: string;
      productName: string;
      returnRate: number;
      returnValue: number;
      receivedDate: string;
      expiryDate?: string;
    }>;
  };
  timelineAnalysis: Array<{
    month: string;
    returnValue: number;
    returnCount: number;
    returnRate: number;
    qualityScore: number;
  }>;
  recommendations: string[];
  comparisonWithOtherSuppliers: {
    rank: number;
    totalSuppliers: number;
    percentile: number;
    betterThanPercentage: number;
  };
}

// GET /api/analytics/supplier-accountability - Get supplier accountability report
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get("supplierId");
    const timeframe = searchParams.get("timeframe") || "90";
    const sortBy = searchParams.get("sortBy") || "qualityScore";
    const riskLevel = searchParams.get("riskLevel");
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");

    // If supplierId is provided, return individual supplier report
    if (supplierId) {
      return await getIndividualSupplierReport(supplierId, timeframe, startDateParam, endDateParam, auth);
    }

    // Otherwise, return all suppliers accountability data

    // Calculate date range for all suppliers
    let startDate: Date;
    let endDate: Date = new Date();

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam);
      endDate = new Date(endDateParam);
    } else {
      startDate = new Date();
      const days = parseInt(timeframe);
      startDate.setDate(startDate.getDate() - days);
    }

    // Get all suppliers accountability data
    const suppliersData = await getAllSuppliersAccountability(startDate, endDate, sortBy, riskLevel);

    return NextResponse.json({
      suppliers: suppliersData.suppliers,
      summary: suppliersData.summary,
      generatedAt: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error generating supplier accountability report:", error);
    return NextResponse.json(
      {
        error: "Failed to generate supplier accountability report",
        message: (error as Error).message
      },
      { status: 500 }
    );
  }
}

// Handle individual supplier report
async function getIndividualSupplierReport(supplierId: string, timeframe: string, startDateParam: string | null, endDateParam: string | null, auth: any) {
  // Calculate date range
  let startDate: Date;
  let endDate: Date = new Date();

  if (startDateParam && endDateParam) {
    startDate = new Date(startDateParam);
    endDate = new Date(endDateParam);
  } else {
    startDate = new Date();
    const days = parseInt(timeframe);
    startDate.setDate(startDate.getDate() - days);
  }

  // Get supplier information
  const supplier = await prisma.supplier.findUnique({
    where: { id: supplierId },
    select: { id: true, name: true, isActive: true }
  });

  if (!supplier) {
    return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
  }

  // Generate comprehensive supplier accountability report
  const report = await generateSupplierAccountabilityReport(
    supplierId,
    startDate,
    endDate
  );

  return NextResponse.json({
    success: true,
    report,
    generatedAt: new Date(),
    generatedBy: {
      id: auth.user.id,
      name: auth.user.name,
      role: auth.user.role,
    }
  });
}

// Get accountability data for all suppliers
async function getAllSuppliersAccountability(startDate: Date, endDate: Date, sortBy: string, riskLevel?: string | null) {
  // Get all active suppliers
  const suppliers = await prisma.supplier.findMany({
    where: { isActive: true },
    select: {
      id: true,
      name: true,
      contactPerson: true,
      email: true,
      phone: true
    }
  });

  const suppliersData = [];
  let totalReturnValue = 0;
  let totalQualityScore = 0;
  let highRiskCount = 0;

  for (const supplier of suppliers) {
    // Get basic metrics for each supplier
    const metrics = await getSupplierBasicMetrics(supplier.id, startDate, endDate);

    const supplierData = {
      supplierId: supplier.id,
      supplierName: supplier.name,
      contactPerson: supplier.contactPerson || 'N/A',
      totalPurchaseValue: metrics.totalPurchaseValue,
      totalReturnValue: metrics.totalReturnValue,
      returnRate: metrics.returnRate,
      defectRate: metrics.defectRate,
      qualityScore: metrics.qualityScore,
      riskLevel: metrics.riskLevel,
      totalBatches: metrics.totalBatches,
      affectedBatches: metrics.affectedBatches,
      totalReturns: metrics.totalReturns,
      totalDefects: metrics.totalDefects,
      averageResolutionTime: metrics.averageResolutionTime,
      lastIncidentDate: metrics.lastIncidentDate,
      improvementTrend: metrics.improvementTrend,
      topDefectTypes: metrics.topDefectTypes,
      monthlyTrends: metrics.monthlyTrends,
      recentIssues: metrics.recentIssues,
    };

    // Apply risk level filter if specified
    if (!riskLevel || riskLevel === 'all' || supplierData.riskLevel === riskLevel.toUpperCase()) {
      suppliersData.push(supplierData);
    }

    totalReturnValue += metrics.totalReturnValue;
    totalQualityScore += metrics.qualityScore;
    if (metrics.riskLevel === 'HIGH' || metrics.riskLevel === 'CRITICAL') {
      highRiskCount++;
    }
  }

  // Sort suppliers
  suppliersData.sort((a, b) => {
    switch (sortBy) {
      case 'qualityScore':
        return b.qualityScore - a.qualityScore;
      case 'returnRate':
        return a.returnRate - b.returnRate;
      case 'defectRate':
        return a.defectRate - b.defectRate;
      case 'riskLevel':
        const riskOrder = { 'LOW': 0, 'MEDIUM': 1, 'HIGH': 2, 'CRITICAL': 3 };
        return riskOrder[b.riskLevel as keyof typeof riskOrder] - riskOrder[a.riskLevel as keyof typeof riskOrder];
      default:
        return a.supplierName.localeCompare(b.supplierName);
    }
  });

  return {
    suppliers: suppliersData,
    summary: {
      totalSuppliers: suppliers.length,
      averageQualityScore: suppliers.length > 0 ? totalQualityScore / suppliers.length : 0,
      totalReturnValue,
      highRiskSuppliers: highRiskCount,
    }
  };
}

async function generateSupplierAccountabilityReport(
  supplierId: string,
  startDate: Date,
  endDate: Date
): Promise<SupplierAccountabilityReport> {
  // Get supplier traceability data
  const traceabilityData = await generateSupplierReturnTraceability(
    supplierId,
    startDate,
    endDate
  );

  // Get purchase data for the supplier
  const purchaseData = await getPurchaseDataForSupplier(supplierId, startDate, endDate);
  
  // Get return data with detailed breakdown
  const returnData = await getDetailedReturnData(supplierId, startDate, endDate);

  // Calculate overall metrics
  const overallMetrics = calculateOverallMetrics(purchaseData, returnData, traceabilityData);

  // Generate product breakdown
  const productBreakdown = generateProductBreakdown(returnData, purchaseData);

  // Generate batch analysis
  const batchAnalysis = generateBatchAnalysis(traceabilityData);

  // Generate timeline analysis
  const timelineAnalysis = await generateTimelineAnalysis(supplierId, startDate, endDate);

  // Get supplier comparison
  const comparison = await getSupplierComparison(supplierId, overallMetrics.returnValuePercentage);

  const report: SupplierAccountabilityReport = {
    supplierId,
    supplierName: traceabilityData.supplierName,
    reportPeriod: {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    },
    overallMetrics,
    productBreakdown,
    batchAnalysis,
    timelineAnalysis,
    recommendations: traceabilityData.recommendations,
    comparisonWithOtherSuppliers: comparison,
  };

  return report;
}

async function getPurchaseDataForSupplier(supplierId: string, startDate: Date, endDate: Date) {
  const purchaseOrders = await prisma.purchaseOrder.findMany({
    where: {
      supplierId,
      orderDate: {
        gte: startDate,
        lte: endDate,
      },
      status: {
        in: ['RECEIVED', 'ORDERED']
      }
    },
    include: {
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            }
          }
        }
      }
    }
  });

  let totalPurchaseValue = 0;
  let totalPurchasedItems = 0;
  const productPurchases = new Map();

  for (const po of purchaseOrders) {
    for (const item of po.items) {
      const itemValue = Number(item.quantity) * Number(item.unitPrice);
      totalPurchaseValue += itemValue;
      totalPurchasedItems += Number(item.quantity);

      if (!productPurchases.has(item.productId)) {
        productPurchases.set(item.productId, {
          productId: item.productId,
          productName: item.product.name,
          sku: item.product.sku,
          totalQuantity: 0,
          totalValue: 0,
        });
      }

      const productData = productPurchases.get(item.productId);
      productData.totalQuantity += Number(item.quantity);
      productData.totalValue += itemValue;
    }
  }

  return {
    totalPurchaseValue,
    totalPurchasedItems,
    productPurchases: Array.from(productPurchases.values()),
  };
}

async function getDetailedReturnData(supplierId: string, startDate: Date, endDate: Date) {
  // This is a placeholder - in a real implementation, this would query returns
  // that are linked to batches from this supplier
  return {
    totalReturnValue: 0,
    totalReturnedItems: 0,
    productReturns: [],
    averageProcessingDays: 0,
  };
}

function calculateOverallMetrics(purchaseData: any, returnData: any, traceabilityData: any) {
  const returnValuePercentage = purchaseData.totalPurchaseValue > 0 
    ? (traceabilityData.totalReturnValue / purchaseData.totalPurchaseValue) * 100 
    : 0;

  const returnRatePercentage = purchaseData.totalPurchasedItems > 0
    ? (returnData.totalReturnedItems / purchaseData.totalPurchasedItems) * 100
    : 0;

  return {
    totalPurchaseValue: purchaseData.totalPurchaseValue,
    totalReturnValue: traceabilityData.totalReturnValue,
    returnValuePercentage,
    totalReturnedItems: returnData.totalReturnedItems,
    totalPurchasedItems: purchaseData.totalPurchasedItems,
    returnRatePercentage,
    averageReturnProcessingDays: returnData.averageProcessingDays,
    qualityScore: traceabilityData.averageReturnRate > 0 ? 100 - traceabilityData.averageReturnRate * 10 : 100,
    riskLevel: traceabilityData.riskLevel,
  };
}

function generateProductBreakdown(returnData: any, purchaseData: any) {
  // Placeholder implementation
  return [];
}

function generateBatchAnalysis(traceabilityData: any) {
  return {
    totalBatches: traceabilityData.totalBatches,
    batchesWithReturns: traceabilityData.batchesWithReturns,
    batchReturnRate: traceabilityData.totalBatches > 0 
      ? (traceabilityData.batchesWithReturns / traceabilityData.totalBatches) * 100 
      : 0,
    worstPerformingBatches: traceabilityData.batchAnalysis
      .sort((a: any, b: any) => b.returnRate - a.returnRate)
      .slice(0, 5)
      .map((batch: any) => ({
        batchId: batch.batchId,
        batchNumber: batch.batchNumber,
        productName: batch.productName,
        returnRate: batch.returnRate,
        returnValue: batch.returnValue,
        receivedDate: new Date().toISOString().split('T')[0], // Placeholder
        expiryDate: undefined,
      })),
  };
}

async function generateTimelineAnalysis(supplierId: string, startDate: Date, endDate: Date) {
  // Placeholder implementation
  return [];
}

async function getSupplierBasicMetrics(supplierId: string, startDate: Date, endDate: Date) {
  // Get purchase data
  const purchaseData = await getPurchaseDataForSupplier(supplierId, startDate, endDate);

  // Get return data (simplified for now)
  const returnData = await getDetailedReturnData(supplierId, startDate, endDate);

  // Calculate basic metrics
  const returnRate = purchaseData.totalPurchasedItems > 0
    ? (returnData.totalReturnedItems / purchaseData.totalPurchasedItems) * 100
    : 0;

  const defectRate = Math.min(returnRate * 0.8, 15); // Simplified calculation
  const qualityScore = Math.max(0, 100 - (returnRate * 2) - (defectRate * 1.5));

  // Determine risk level
  let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
  if (returnRate > 15 || defectRate > 10) {
    riskLevel = 'CRITICAL';
  } else if (returnRate > 10 || defectRate > 7) {
    riskLevel = 'HIGH';
  } else if (returnRate > 5 || defectRate > 3) {
    riskLevel = 'MEDIUM';
  }

  // Get recent quality issues (simplified)
  const recentIssues = await prisma.qualityIssue.findMany({
    where: {
      supplierId: supplierId,
      reportedAt: {
        gte: startDate,
        lte: endDate,
      }
    },
    select: {
      id: true,
      issueType: true,
      severity: true,
      reportedAt: true,
      status: true,
    },
    orderBy: { reportedAt: 'desc' },
    take: 5,
  });

  return {
    totalPurchaseValue: purchaseData.totalPurchaseValue,
    totalReturnValue: returnData.totalReturnValue,
    returnRate,
    defectRate,
    qualityScore,
    riskLevel,
    totalBatches: 10, // Placeholder
    affectedBatches: Math.ceil(returnRate / 10), // Simplified
    totalReturns: returnData.totalReturnedItems,
    totalDefects: Math.ceil(returnData.totalReturnedItems * 0.8), // Simplified
    averageResolutionTime: returnData.averageProcessingDays,
    lastIncidentDate: recentIssues.length > 0 ? recentIssues[0].reportedAt.toISOString() : undefined,
    improvementTrend: returnRate < 5 ? 'improving' : returnRate > 10 ? 'declining' : 'stable' as const,
    topDefectTypes: [
      { type: 'Damaged', count: Math.ceil(returnData.totalReturnedItems * 0.4), percentage: 40 },
      { type: 'Expired', count: Math.ceil(returnData.totalReturnedItems * 0.3), percentage: 30 },
      { type: 'Wrong Item', count: Math.ceil(returnData.totalReturnedItems * 0.2), percentage: 20 },
      { type: 'Quality Issue', count: Math.ceil(returnData.totalReturnedItems * 0.1), percentage: 10 },
    ],
    monthlyTrends: generateMonthlyTrends(startDate, endDate, returnRate, defectRate, qualityScore),
    recentIssues: recentIssues.map(issue => ({
      id: issue.id,
      issueType: issue.issueType,
      severity: issue.severity,
      reportedAt: issue.reportedAt.toISOString(),
      status: issue.status,
    })),
  };
}

function generateMonthlyTrends(startDate: Date, endDate: Date, currentReturnRate: number, currentDefectRate: number, currentQualityScore: number) {
  const trends = [];
  const monthsDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30));

  for (let i = 0; i < Math.min(monthsDiff, 6); i++) {
    const date = new Date(endDate);
    date.setMonth(date.getMonth() - i);

    // Add some variation to make trends realistic
    const variation = (Math.random() - 0.5) * 2;
    trends.unshift({
      month: date.toLocaleDateString('en-US', { year: 'numeric', month: 'short' }),
      returnRate: Math.max(0, currentReturnRate + variation),
      defectRate: Math.max(0, currentDefectRate + variation * 0.8),
      qualityScore: Math.min(100, Math.max(0, currentQualityScore - variation * 2)),
    });
  }

  return trends;
}

async function getSupplierComparison(supplierId: string, returnValuePercentage: number) {
  // Placeholder implementation
  return {
    rank: 1,
    totalSuppliers: 10,
    percentile: 90,
    betterThanPercentage: 90,
  };
}
