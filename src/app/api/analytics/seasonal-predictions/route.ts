import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { SeasonalSupplierPredictionEngine } from "@/lib/seasonal-supplier-predictions";

/**
 * GET /api/analytics/seasonal-predictions
 * Generate seasonal performance predictions for suppliers
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || "Unauthorized" }, { status: auth.status || 401 });
    }

    // Check role permissions
    const allowedRoles = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get("supplierId");

    if (!supplierId) {
      return NextResponse.json({ error: "Supplier ID is required" }, { status: 400 });
    }

    console.log(`[SeasonalPredictions] Generating seasonal predictions for supplier ${supplierId}`);

    // Generate seasonal predictions
    const predictions = await SeasonalSupplierPredictionEngine.generateSeasonalPredictions(supplierId);

    console.log(`[SeasonalPredictions] Generated predictions for ${predictions.upcomingSeasons.length} seasons`);

    // Calculate summary statistics
    const summaryStats = {
      totalSeasons: predictions.upcomingSeasons.length,
      highRiskSeasons: predictions.upcomingSeasons.filter(s => 
        s.predictions.qualityScore.riskLevel === 'high' ||
        s.predictions.capacityConstraints.riskOfShortages === 'high'
      ).length,
      averageQualityScore: predictions.upcomingSeasons.reduce((sum, s) => 
        sum + s.predictions.qualityScore.predicted, 0) / predictions.upcomingSeasons.length,
      averageDeliveryRate: predictions.upcomingSeasons.reduce((sum, s) => 
        sum + s.predictions.deliveryPerformance.onTimeDeliveryRate, 0) / predictions.upcomingSeasons.length,
      totalRecommendations: predictions.strategicRecommendations.length,
      overallRiskLevel: predictions.overallRiskAssessment.riskLevel
    };

    return NextResponse.json({
      success: true,
      data: predictions,
      summaryStats,
      metadata: {
        generatedAt: new Date().toISOString(),
        supplierId,
        analysisScope: "24 months historical data",
        predictiveHorizon: "Next 12 months"
      }
    });

  } catch (error) {
    console.error("[SeasonalPredictions] Error generating predictions:", error);
    
    return NextResponse.json({
      error: "Failed to generate seasonal predictions",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * POST /api/analytics/seasonal-predictions
 * Generate seasonal predictions for multiple suppliers
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || "Unauthorized" }, { status: auth.status || 401 });
    }

    // Check role permissions
    const allowedRoles = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const { supplierIds } = body;

    if (!supplierIds || !Array.isArray(supplierIds) || supplierIds.length === 0) {
      return NextResponse.json({ 
        error: "Supplier IDs array is required and must not be empty" 
      }, { status: 400 });
    }

    if (supplierIds.length > 5) {
      return NextResponse.json({ 
        error: "Maximum 5 suppliers allowed per request (seasonal analysis is computationally intensive)" 
      }, { status: 400 });
    }

    console.log(`[SeasonalPredictions] Generating seasonal predictions for ${supplierIds.length} suppliers`);

    // Generate predictions for all suppliers
    const predictions = await Promise.allSettled(
      supplierIds.map(supplierId => 
        SeasonalSupplierPredictionEngine.generateSeasonalPredictions(supplierId)
      )
    );

    // Process results
    const successfulPredictions = [];
    const failedPredictions = [];

    predictions.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successfulPredictions.push(result.value);
      } else {
        failedPredictions.push({
          supplierId: supplierIds[index],
          error: result.reason?.message || 'Unknown error'
        });
      }
    });

    console.log(`[SeasonalPredictions] Generated ${successfulPredictions.length} successful predictions, ${failedPredictions.length} failed`);

    // Calculate comparative analysis
    const comparativeAnalysis = {
      supplierRiskRanking: successfulPredictions
        .map(p => ({
          supplierId: p.supplierId,
          supplierName: p.supplierName,
          overallRiskLevel: p.overallRiskAssessment.riskLevel,
          riskScore: p.overallRiskAssessment.riskLevel === 'high' ? 3 : 
                    p.overallRiskAssessment.riskLevel === 'medium' ? 2 : 1,
          highRiskSeasons: p.upcomingSeasons.filter(s => 
            s.predictions.qualityScore.riskLevel === 'high' ||
            s.predictions.capacityConstraints.riskOfShortages === 'high'
          ).length,
          averageQualityScore: p.upcomingSeasons.reduce((sum, s) => 
            sum + s.predictions.qualityScore.predicted, 0) / p.upcomingSeasons.length
        }))
        .sort((a, b) => b.riskScore - a.riskScore),
      
      seasonalRiskMatrix: this.generateSeasonalRiskMatrix(successfulPredictions),
      
      aggregateInsights: this.generateAggregateInsights(successfulPredictions)
    };

    return NextResponse.json({
      success: true,
      data: {
        predictions: successfulPredictions,
        failures: failedPredictions,
        comparativeAnalysis
      },
      metadata: {
        generatedAt: new Date().toISOString(),
        requestedSuppliers: supplierIds.length,
        successRate: (successfulPredictions.length / supplierIds.length) * 100,
        analysisScope: "24 months historical data",
        predictiveHorizon: "Next 12 months"
      }
    });

  } catch (error) {
    console.error("[SeasonalPredictions] Error generating bulk predictions:", error);
    
    return NextResponse.json({
      error: "Failed to generate seasonal predictions",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * Generate seasonal risk matrix across all suppliers
 */
function generateSeasonalRiskMatrix(predictions: any[]) {
  const seasons = ['Q1', 'Q2', 'Q3', 'Q4', 'Ramadan', 'Christmas'];
  const riskMatrix: any = {};

  seasons.forEach(season => {
    const seasonData = predictions.map(p => {
      const seasonPrediction = p.upcomingSeasons.find((s: any) => s.season === season);
      return seasonPrediction ? {
        supplierId: p.supplierId,
        supplierName: p.supplierName,
        qualityRisk: seasonPrediction.predictions.qualityScore.riskLevel,
        deliveryRisk: seasonPrediction.predictions.deliveryPerformance.onTimeDeliveryRate < 70 ? 'high' : 
                     seasonPrediction.predictions.deliveryPerformance.onTimeDeliveryRate < 85 ? 'medium' : 'low',
        capacityRisk: seasonPrediction.predictions.capacityConstraints.riskOfShortages,
        overallRisk: seasonPrediction.predictions.qualityScore.riskLevel === 'high' ||
                    seasonPrediction.predictions.capacityConstraints.riskOfShortages === 'high' ? 'high' :
                    seasonPrediction.predictions.qualityScore.riskLevel === 'medium' ||
                    seasonPrediction.predictions.capacityConstraints.riskOfShortages === 'medium' ? 'medium' : 'low'
      } : null;
    }).filter(Boolean);

    riskMatrix[season] = {
      totalSuppliers: seasonData.length,
      highRiskSuppliers: seasonData.filter(s => s.overallRisk === 'high').length,
      mediumRiskSuppliers: seasonData.filter(s => s.overallRisk === 'medium').length,
      lowRiskSuppliers: seasonData.filter(s => s.overallRisk === 'low').length,
      suppliers: seasonData
    };
  });

  return riskMatrix;
}

/**
 * Generate aggregate insights across all suppliers
 */
function generateAggregateInsights(predictions: any[]) {
  const insights = [];

  // Find most problematic season
  const seasonRisks = ['Q1', 'Q2', 'Q3', 'Q4', 'Ramadan', 'Christmas'].map(season => {
    const seasonPredictions = predictions.flatMap(p => 
      p.upcomingSeasons.filter((s: any) => s.season === season)
    );
    
    const highRiskCount = seasonPredictions.filter(s => 
      s.predictions.qualityScore.riskLevel === 'high' ||
      s.predictions.capacityConstraints.riskOfShortages === 'high'
    ).length;

    return { season, riskCount: highRiskCount, total: seasonPredictions.length };
  });

  const mostProblematicSeason = seasonRisks.reduce((max, current) => 
    current.riskCount > max.riskCount ? current : max
  );

  if (mostProblematicSeason.riskCount > 0) {
    insights.push(`${mostProblematicSeason.season} shows highest risk across suppliers (${mostProblematicSeason.riskCount}/${mostProblematicSeason.total} suppliers at risk)`);
  }

  // Find suppliers with consistent issues
  const consistentlyProblematicSuppliers = predictions.filter(p => 
    p.upcomingSeasons.filter((s: any) => 
      s.predictions.qualityScore.riskLevel === 'high' ||
      s.predictions.capacityConstraints.riskOfShortages === 'high'
    ).length >= 3
  );

  if (consistentlyProblematicSuppliers.length > 0) {
    insights.push(`${consistentlyProblematicSuppliers.length} suppliers show consistent seasonal issues - consider supplier development programs`);
  }

  // Find opportunities
  const reliableSuppliers = predictions.filter(p => 
    p.overallRiskAssessment.riskLevel === 'low'
  );

  if (reliableSuppliers.length > 0) {
    insights.push(`${reliableSuppliers.length} suppliers show consistent reliability - opportunity to increase volume`);
  }

  return insights;
}
