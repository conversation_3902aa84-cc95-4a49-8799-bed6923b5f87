import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";

export interface DefectTrackingAnalysis {
  reportPeriod: {
    startDate: string;
    endDate: string;
  };
  overallMetrics: {
    totalDefects: number;
    totalDefectValue: number;
    defectRate: number; // Percentage of returns with defects
    averageDefectValue: number;
    mostCommonDefectType: string;
    defectTrend: 'increasing' | 'decreasing' | 'stable';
  };
  defectsByType: Array<{
    defectType: string;
    count: number;
    percentage: number;
    totalValue: number;
    averageValue: number;
    affectedSuppliers: number;
    affectedProducts: number;
    trend: 'increasing' | 'decreasing' | 'stable';
  }>;
  defectsBySupplier: Array<{
    supplierId: string;
    supplierName: string;
    totalDefects: number;
    defectRate: number;
    totalDefectValue: number;
    topDefectTypes: Array<{
      defectType: string;
      count: number;
      percentage: number;
    }>;
    riskLevel: 'low' | 'medium' | 'high';
    qualityScore: number;
  }>;
  defectsByProduct: Array<{
    productId: string;
    productName: string;
    sku: string;
    totalDefects: number;
    defectRate: number;
    totalDefectValue: number;
    topDefectTypes: Array<{
      defectType: string;
      count: number;
    }>;
    affectedBatches: number;
    affectedSuppliers: number;
  }>;
  batchDefectAnalysis: Array<{
    batchId: string;
    batchNumber: string;
    productName: string;
    supplierName: string;
    defectCount: number;
    defectRate: number;
    defectValue: number;
    receivedDate: string;
    expiryDate?: string;
    defectTypes: string[];
  }>;
  timelineAnalysis: Array<{
    period: string; // YYYY-MM format
    totalDefects: number;
    defectValue: number;
    defectRate: number;
    topDefectType: string;
  }>;
  recommendations: Array<{
    type: 'supplier' | 'product' | 'process';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    targetId?: string;
    targetName?: string;
  }>;
}

// GET /api/analytics/defect-tracking - Get comprehensive defect tracking analysis
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "6months";
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");
    const supplierId = searchParams.get("supplierId");
    const productId = searchParams.get("productId");
    const defectType = searchParams.get("defectType");

    // Calculate date range
    let startDate: Date;
    let endDate: Date = new Date();

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam);
      endDate = new Date(endDateParam);
    } else {
      startDate = new Date();
      switch (timeRange) {
        case "1month":
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case "3months":
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case "6months":
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case "1year":
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(startDate.getMonth() - 6);
          break;
      }
    }

    // Generate defect tracking analysis
    const analysis = await generateDefectTrackingAnalysis(
      startDate,
      endDate,
      { supplierId, productId, defectType }
    );

    // Transform the analysis to match the frontend interface
    const response = {
      defectAnalysis: analysis.defectsByType.map(defect => ({
        defectType: defect.defectType,
        totalOccurrences: defect.count,
        affectedProducts: defect.affectedProducts,
        affectedSuppliers: defect.affectedSuppliers,
        totalQuantity: defect.count, // Using count as quantity for now
        totalValue: defect.totalValue,
        averageSeverity: 2.5, // Placeholder - would need severity data
        trend: defect.trend,
        trendPercentage: 0, // Placeholder - would need historical data
        topProducts: [], // Placeholder - would need product-level analysis
        topSuppliers: [], // Placeholder - would need supplier-level analysis
        monthlyTrend: [], // Placeholder - would need timeline data
        severityDistribution: [], // Placeholder - would need severity data
      })),
      summary: {
        totalDefects: analysis.overallMetrics.totalDefects,
        totalValue: analysis.overallMetrics.totalDefectValue,
        mostCommonDefect: analysis.overallMetrics.mostCommonDefectType,
        averageResolutionTime: 5.2, // Placeholder - would need resolution time data
        defectRate: analysis.overallMetrics.defectRate,
        trendDirection: analysis.overallMetrics.defectTrend === 'increasing' ? 'worsening' :
                       analysis.overallMetrics.defectTrend === 'decreasing' ? 'improving' : 'stable',
      },
      timeComparison: {
        currentPeriod: {
          defects: analysis.overallMetrics.totalDefects,
          value: analysis.overallMetrics.totalDefectValue,
        },
        previousPeriod: {
          defects: Math.floor(analysis.overallMetrics.totalDefects * 0.9), // Placeholder
          value: Math.floor(analysis.overallMetrics.totalDefectValue * 0.9), // Placeholder
        },
        changePercentage: 10, // Placeholder - would need historical comparison
      },
      generatedAt: new Date().toISOString(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error generating defect tracking analysis:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate defect tracking analysis",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

async function generateDefectTrackingAnalysis(
  startDate: Date,
  endDate: Date,
  filters: { supplierId?: string; productId?: string; defectType?: string }
): Promise<DefectTrackingAnalysis> {
  
  // Build where clause for returns with defects
  const returnWhere: any = {
    returnDate: {
      gte: startDate,
      lte: endDate,
    },
    items: {
      some: {
        defectType: { not: null },
        ...(filters.defectType ? { defectType: filters.defectType } : {}),
        ...(filters.productId ? { productId: filters.productId } : {}),
        ...(filters.supplierId ? {
          batch: {
            productSupplier: {
              supplierId: filters.supplierId
            }
          }
        } : {})
      }
    }
  };

  // Get returns with defect information
  const returnsWithDefects = await prisma.return.findMany({
    where: returnWhere,
    include: {
      items: {
        where: {
          defectType: { not: null }
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            }
          },
          batch: {
            include: {
              productSupplier: {
                include: {
                  supplier: {
                    select: {
                      id: true,
                      name: true,
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  });

  // Calculate overall metrics
  const overallMetrics = calculateOverallDefectMetrics(returnsWithDefects);

  // Analyze defects by type
  const defectsByType = analyzeDefectsByType(returnsWithDefects);

  // Analyze defects by supplier
  const defectsBySupplier = analyzeDefectsBySupplier(returnsWithDefects);

  // Analyze defects by product
  const defectsByProduct = analyzeDefectsByProduct(returnsWithDefects);

  // Analyze batch-level defects
  const batchDefectAnalysis = analyzeBatchDefects(returnsWithDefects);

  // Generate timeline analysis
  const timelineAnalysis = generateDefectTimelineAnalysis(returnsWithDefects, startDate, endDate);

  // Generate recommendations
  const recommendations = generateDefectRecommendations(
    defectsByType,
    defectsBySupplier,
    defectsByProduct
  );

  return {
    reportPeriod: {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    },
    overallMetrics,
    defectsByType,
    defectsBySupplier,
    defectsByProduct,
    batchDefectAnalysis,
    timelineAnalysis,
    recommendations,
  };
}

function calculateOverallDefectMetrics(returnsWithDefects: any[]): any {
  let totalDefects = 0;
  let totalDefectValue = 0;
  const defectTypeCounts = new Map<string, number>();

  for (const returnRecord of returnsWithDefects) {
    for (const item of returnRecord.items) {
      if (item.defectType) {
        totalDefects++;
        totalDefectValue += Number(item.subtotal);
        
        const count = defectTypeCounts.get(item.defectType) || 0;
        defectTypeCounts.set(item.defectType, count + 1);
      }
    }
  }

  // Find most common defect type
  let mostCommonDefectType = '';
  let maxCount = 0;
  for (const [defectType, count] of defectTypeCounts) {
    if (count > maxCount) {
      maxCount = count;
      mostCommonDefectType = defectType;
    }
  }

  // Calculate defect rate (percentage of returns with defects)
  const totalReturns = returnsWithDefects.length;
  const defectRate = totalReturns > 0 ? (totalDefects / totalReturns) * 100 : 0;

  return {
    totalDefects,
    totalDefectValue,
    defectRate,
    averageDefectValue: totalDefects > 0 ? totalDefectValue / totalDefects : 0,
    mostCommonDefectType,
    defectTrend: 'stable' as const, // Placeholder - would need historical data
  };
}

function analyzeDefectsByType(returnsWithDefects: any[]): any[] {
  const defectTypeMap = new Map<string, {
    count: number;
    totalValue: number;
    suppliers: Set<string>;
    products: Set<string>;
  }>();

  let totalDefects = 0;

  for (const returnRecord of returnsWithDefects) {
    for (const item of returnRecord.items) {
      if (item.defectType) {
        totalDefects++;
        
        if (!defectTypeMap.has(item.defectType)) {
          defectTypeMap.set(item.defectType, {
            count: 0,
            totalValue: 0,
            suppliers: new Set(),
            products: new Set(),
          });
        }

        const defectData = defectTypeMap.get(item.defectType)!;
        defectData.count++;
        defectData.totalValue += Number(item.subtotal);
        defectData.products.add(item.productId);
        
        if (item.batch?.productSupplier?.supplier) {
          defectData.suppliers.add(item.batch.productSupplier.supplier.id);
        }
      }
    }
  }

  return Array.from(defectTypeMap.entries()).map(([defectType, data]) => ({
    defectType,
    count: data.count,
    percentage: totalDefects > 0 ? (data.count / totalDefects) * 100 : 0,
    totalValue: data.totalValue,
    averageValue: data.count > 0 ? data.totalValue / data.count : 0,
    affectedSuppliers: data.suppliers.size,
    affectedProducts: data.products.size,
    trend: 'stable' as const, // Placeholder
  })).sort((a, b) => b.count - a.count);
}

function analyzeDefectsBySupplier(returnsWithDefects: any[]): any[] {
  const supplierMap = new Map<string, {
    supplierName: string;
    defects: number;
    totalValue: number;
    defectTypes: Map<string, number>;
  }>();

  for (const returnRecord of returnsWithDefects) {
    for (const item of returnRecord.items) {
      if (item.defectType && item.batch?.productSupplier?.supplier) {
        const supplier = item.batch.productSupplier.supplier;
        
        if (!supplierMap.has(supplier.id)) {
          supplierMap.set(supplier.id, {
            supplierName: supplier.name,
            defects: 0,
            totalValue: 0,
            defectTypes: new Map(),
          });
        }

        const supplierData = supplierMap.get(supplier.id)!;
        supplierData.defects++;
        supplierData.totalValue += Number(item.subtotal);
        
        const defectCount = supplierData.defectTypes.get(item.defectType) || 0;
        supplierData.defectTypes.set(item.defectType, defectCount + 1);
      }
    }
  }

  return Array.from(supplierMap.entries()).map(([supplierId, data]) => {
    const topDefectTypes = Array.from(data.defectTypes.entries())
      .map(([defectType, count]) => ({
        defectType,
        count,
        percentage: (count / data.defects) * 100,
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 3);

    // Calculate risk level based on defect count and value
    let riskLevel: 'low' | 'medium' | 'high' = 'low';
    if (data.defects > 10 || data.totalValue > 1000000) riskLevel = 'high';
    else if (data.defects > 5 || data.totalValue > 500000) riskLevel = 'medium';

    return {
      supplierId,
      supplierName: data.supplierName,
      totalDefects: data.defects,
      defectRate: 0, // Placeholder - would need total items from supplier
      totalDefectValue: data.totalValue,
      topDefectTypes,
      riskLevel,
      qualityScore: Math.max(0, 100 - (data.defects * 5)), // Simple scoring
    };
  }).sort((a, b) => b.totalDefects - a.totalDefects);
}

function analyzeDefectsByProduct(returnsWithDefects: any[]): any[] {
  // Similar implementation to analyzeDefectsBySupplier but grouped by product
  // Placeholder implementation
  return [];
}

function analyzeBatchDefects(returnsWithDefects: any[]): any[] {
  // Analyze defects at batch level
  // Placeholder implementation
  return [];
}

function generateDefectTimelineAnalysis(returnsWithDefects: any[], startDate: Date, endDate: Date): any[] {
  // Generate monthly timeline of defects
  // Placeholder implementation
  return [];
}

function generateDefectRecommendations(
  defectsByType: any[],
  defectsBySupplier: any[],
  defectsByProduct: any[]
): any[] {
  const recommendations: any[] = [];

  // High-defect suppliers
  const highRiskSuppliers = defectsBySupplier.filter(s => s.riskLevel === 'high');
  for (const supplier of highRiskSuppliers.slice(0, 3)) {
    recommendations.push({
      type: 'supplier',
      priority: 'high',
      title: `Review supplier quality: ${supplier.supplierName}`,
      description: `Supplier has ${supplier.totalDefects} defects worth ${(supplier.totalDefectValue / 1000000).toFixed(1)}M IDR. Consider quality audit.`,
      targetId: supplier.supplierId,
      targetName: supplier.supplierName,
    });
  }

  // Most common defect types
  if (defectsByType.length > 0) {
    const topDefect = defectsByType[0];
    recommendations.push({
      type: 'process',
      priority: 'medium',
      title: `Address common defect: ${topDefect.defectType}`,
      description: `${topDefect.defectType} accounts for ${topDefect.percentage.toFixed(1)}% of all defects. Implement preventive measures.`,
    });
  }

  return recommendations;
}
