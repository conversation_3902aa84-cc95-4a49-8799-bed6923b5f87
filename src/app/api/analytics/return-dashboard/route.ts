import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import { analyzeBatchReturns } from "@/lib/return-batch-analytics";

export interface ReturnDashboardData {
  summary: {
    totalReturns: number;
    totalReturnValue: number;
    averageReturnValue: number;
    returnRate: number; // Percentage of transactions that have returns
    totalDefects: number;
    defectRate: number; // Percentage of returns with defects
    suppliersWithReturns: number;
    productsWithReturns: number;
    batchesWithReturns: number;
  };
  trends: {
    returnValueTrend: Array<{
      period: string;
      value: number;
      count: number;
      change: number; // Percentage change from previous period
    }>;
    defectTrend: Array<{
      period: string;
      defectCount: number;
      defectRate: number;
      change: number;
    }>;
    supplierQualityTrend: Array<{
      period: string;
      averageQualityScore: number;
      suppliersAtRisk: number;
    }>;
  };
  topIssues: {
    worstSuppliers: Array<{
      supplierId: string;
      supplierName: string;
      returnValue: number;
      returnCount: number;
      returnRate: number;
      qualityScore: number;
      riskLevel: 'low' | 'medium' | 'high';
    }>;
    problematicProducts: Array<{
      productId: string;
      productName: string;
      sku: string;
      returnValue: number;
      returnCount: number;
      returnRate: number;
      topReturnReason: string;
      affectedSuppliers: number;
    }>;
    criticalBatches: Array<{
      batchId: string;
      batchNumber: string;
      productName: string;
      supplierName: string;
      returnRate: number;
      returnValue: number;
      defectTypes: string[];
      receivedDate: string;
    }>;
    commonDefects: Array<{
      defectType: string;
      count: number;
      percentage: number;
      totalValue: number;
      affectedSuppliers: number;
      affectedProducts: number;
    }>;
  };
  alerts: Array<{
    id: string;
    type: 'supplier_risk' | 'product_issue' | 'batch_problem' | 'defect_spike';
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description: string;
    targetId: string;
    targetName: string;
    value?: number;
    threshold?: number;
    createdAt: string;
  }>;
  recommendations: Array<{
    type: 'immediate' | 'short_term' | 'long_term';
    priority: 'high' | 'medium' | 'low';
    category: 'supplier' | 'product' | 'process' | 'quality';
    title: string;
    description: string;
    expectedImpact: string;
    estimatedCost?: number;
    targetId?: string;
    targetName?: string;
  }>;
}

// GET /api/analytics/return-dashboard - Get comprehensive return analytics dashboard
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "3months";
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");

    // Calculate date range
    let startDate: Date;
    let endDate: Date = new Date();

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam);
      endDate = new Date(endDateParam);
    } else {
      startDate = new Date();
      switch (timeRange) {
        case "1month":
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case "3months":
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case "6months":
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case "1year":
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        default:
          startDate.setMonth(startDate.getMonth() - 3);
          break;
      }
    }

    // Generate comprehensive dashboard data
    const dashboardData = await generateReturnDashboardData(startDate, endDate);

    // Transform the dashboard data to match the frontend interface
    const response = {
      summary: {
        totalReturns: dashboardData.summary.totalReturns,
        totalReturnValue: dashboardData.summary.totalReturnValue,
        returnRate: dashboardData.summary.returnRate,
        averageReturnValue: dashboardData.summary.averageReturnValue,
        topReturnReason: 'Quality Issue', // Placeholder - would need analysis
        returnTrend: 0, // Placeholder - would need historical comparison
        customersAffected: 0, // Placeholder - would need customer analysis
        suppliersAffected: dashboardData.summary.suppliersWithReturns,
      },
      returnsByReason: [], // Placeholder - would need reason analysis
      returnsBySupplier: dashboardData.topIssues.worstSuppliers.map(supplier => ({
        supplierId: supplier.supplierId,
        supplierName: supplier.supplierName,
        returnCount: supplier.returnCount,
        returnValue: supplier.returnValue,
        returnRate: supplier.returnRate,
        trend: 0, // Placeholder
        topReasons: ['Quality Issue'], // Placeholder
      })),
      returnsByProduct: dashboardData.topIssues.problematicProducts.map(product => ({
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        returnCount: product.returnCount,
        returnValue: product.returnValue,
        returnRate: product.returnRate,
        topReasons: [product.topReturnReason],
      })),
      monthlyTrends: dashboardData.trends.returnValueTrend.map(trend => ({
        month: trend.period,
        returnCount: trend.count,
        returnValue: trend.value,
        returnRate: 0, // Placeholder
        defectCount: 0, // Placeholder
      })),
      batchAnalysis: [], // Placeholder - would need batch analysis
      customerImpact: {
        totalCustomersAffected: 0, // Placeholder
        repeatReturnCustomers: 0, // Placeholder
        averageReturnFrequency: 0, // Placeholder
        customerSatisfactionImpact: 0, // Placeholder
      },
      financialImpact: {
        directCosts: dashboardData.summary.totalReturnValue,
        indirectCosts: dashboardData.summary.totalReturnValue * 0.3, // Estimate
        totalImpact: dashboardData.summary.totalReturnValue * 1.3,
        costPerReturn: dashboardData.summary.averageReturnValue,
        monthlyTrend: 0, // Placeholder
      },
      generatedAt: new Date().toISOString(),
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error generating return dashboard data:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate return dashboard data",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

async function generateReturnDashboardData(
  startDate: Date,
  endDate: Date
): Promise<ReturnDashboardData> {
  
  // Get all returns in the period
  const returns = await prisma.return.findMany({
    where: {
      returnDate: {
        gte: startDate,
        lte: endDate,
      }
    },
    include: {
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            }
          },
          batch: {
            include: {
              productSupplier: {
                include: {
                  supplier: {
                    select: {
                      id: true,
                      name: true,
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  });

  // Calculate summary metrics
  const summary = calculateSummaryMetrics(returns, startDate, endDate);

  // Generate trend analysis
  const trends = await generateTrendAnalysis(startDate, endDate);

  // Identify top issues
  const topIssues = await identifyTopIssues(returns);

  // Generate alerts
  const alerts = generateAlerts(summary, topIssues);

  // Generate recommendations
  const recommendations = generateRecommendations(summary, topIssues, trends);

  return {
    summary,
    trends,
    topIssues,
    alerts,
    recommendations,
  };
}

function calculateSummaryMetrics(returns: any[], startDate: Date, endDate: Date) {
  let totalReturnValue = 0;
  let totalDefects = 0;
  const suppliersWithReturns = new Set<string>();
  const productsWithReturns = new Set<string>();
  const batchesWithReturns = new Set<string>();

  for (const returnRecord of returns) {
    totalReturnValue += Number(returnRecord.total);
    
    for (const item of returnRecord.items) {
      if (item.defectType) {
        totalDefects++;
      }
      
      productsWithReturns.add(item.productId);
      
      if (item.batch) {
        batchesWithReturns.add(item.batch.id);
        if (item.batch.productSupplier?.supplier) {
          suppliersWithReturns.add(item.batch.productSupplier.supplier.id);
        }
      }
    }
  }

  const totalReturns = returns.length;
  const averageReturnValue = totalReturns > 0 ? totalReturnValue / totalReturns : 0;
  const defectRate = totalReturns > 0 ? (totalDefects / totalReturns) * 100 : 0;

  return {
    totalReturns,
    totalReturnValue,
    averageReturnValue,
    returnRate: 0, // Placeholder - would need total transactions
    totalDefects,
    defectRate,
    suppliersWithReturns: suppliersWithReturns.size,
    productsWithReturns: productsWithReturns.size,
    batchesWithReturns: batchesWithReturns.size,
  };
}

async function generateTrendAnalysis(startDate: Date, endDate: Date) {
  // Generate monthly trends
  const months = [];
  const current = new Date(startDate);
  
  while (current <= endDate) {
    months.push(new Date(current));
    current.setMonth(current.getMonth() + 1);
  }

  // Placeholder implementation - would generate actual trend data
  const returnValueTrend = months.map((month, index) => ({
    period: month.toISOString().substring(0, 7), // YYYY-MM format
    value: Math.random() * 1000000,
    count: Math.floor(Math.random() * 50),
    change: index > 0 ? (Math.random() - 0.5) * 20 : 0,
  }));

  const defectTrend = months.map((month, index) => ({
    period: month.toISOString().substring(0, 7),
    defectCount: Math.floor(Math.random() * 20),
    defectRate: Math.random() * 10,
    change: index > 0 ? (Math.random() - 0.5) * 5 : 0,
  }));

  const supplierQualityTrend = months.map(month => ({
    period: month.toISOString().substring(0, 7),
    averageQualityScore: 70 + Math.random() * 25,
    suppliersAtRisk: Math.floor(Math.random() * 5),
  }));

  return {
    returnValueTrend,
    defectTrend,
    supplierQualityTrend,
  };
}

async function identifyTopIssues(returns: any[]) {
  // Analyze returns to identify top issues
  const supplierMap = new Map();
  const productMap = new Map();
  const batchMap = new Map();
  const defectMap = new Map();

  for (const returnRecord of returns) {
    for (const item of returnRecord.items) {
      // Track supplier issues
      if (item.batch?.productSupplier?.supplier) {
        const supplier = item.batch.productSupplier.supplier;
        if (!supplierMap.has(supplier.id)) {
          supplierMap.set(supplier.id, {
            supplierId: supplier.id,
            supplierName: supplier.name,
            returnValue: 0,
            returnCount: 0,
          });
        }
        const supplierData = supplierMap.get(supplier.id);
        supplierData.returnValue += Number(item.subtotal);
        supplierData.returnCount++;
      }

      // Track product issues
      if (!productMap.has(item.productId)) {
        productMap.set(item.productId, {
          productId: item.productId,
          productName: item.product.name,
          sku: item.product.sku,
          returnValue: 0,
          returnCount: 0,
          suppliers: new Set(),
        });
      }
      const productData = productMap.get(item.productId);
      productData.returnValue += Number(item.subtotal);
      productData.returnCount++;
      if (item.batch?.productSupplier?.supplier) {
        productData.suppliers.add(item.batch.productSupplier.supplier.id);
      }

      // Track defects
      if (item.defectType) {
        if (!defectMap.has(item.defectType)) {
          defectMap.set(item.defectType, {
            defectType: item.defectType,
            count: 0,
            totalValue: 0,
            suppliers: new Set(),
            products: new Set(),
          });
        }
        const defectData = defectMap.get(item.defectType);
        defectData.count++;
        defectData.totalValue += Number(item.subtotal);
        defectData.products.add(item.productId);
        if (item.batch?.productSupplier?.supplier) {
          defectData.suppliers.add(item.batch.productSupplier.supplier.id);
        }
      }
    }
  }

  // Convert to arrays and sort
  const worstSuppliers = Array.from(supplierMap.values())
    .map(supplier => ({
      ...supplier,
      returnRate: 0, // Placeholder
      qualityScore: Math.max(0, 100 - supplier.returnCount * 5),
      riskLevel: supplier.returnValue > 1000000 ? 'high' : 
                 supplier.returnValue > 500000 ? 'medium' : 'low' as const,
    }))
    .sort((a, b) => b.returnValue - a.returnValue)
    .slice(0, 10);

  const problematicProducts = Array.from(productMap.values())
    .map(product => ({
      ...product,
      returnRate: 0, // Placeholder
      topReturnReason: 'Quality Issue', // Placeholder
      affectedSuppliers: product.suppliers.size,
    }))
    .sort((a, b) => b.returnValue - a.returnValue)
    .slice(0, 10);

  const commonDefects = Array.from(defectMap.values())
    .map(defect => ({
      ...defect,
      percentage: (defect.count / returns.length) * 100,
      affectedSuppliers: defect.suppliers.size,
      affectedProducts: defect.products.size,
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);

  return {
    worstSuppliers,
    problematicProducts,
    criticalBatches: [], // Placeholder
    commonDefects,
  };
}

function generateAlerts(summary: any, topIssues: any) {
  const alerts: any[] = [];

  // High-risk suppliers
  for (const supplier of topIssues.worstSuppliers.slice(0, 3)) {
    if (supplier.riskLevel === 'high') {
      alerts.push({
        id: `supplier_risk_${supplier.supplierId}`,
        type: 'supplier_risk',
        severity: 'high',
        title: `High-risk supplier: ${supplier.supplierName}`,
        description: `Supplier has ${supplier.returnCount} returns worth ${(supplier.returnValue / 1000000).toFixed(1)}M IDR`,
        targetId: supplier.supplierId,
        targetName: supplier.supplierName,
        value: supplier.returnValue,
        createdAt: new Date().toISOString(),
      });
    }
  }

  // High defect rate
  if (summary.defectRate > 15) {
    alerts.push({
      id: 'defect_spike',
      type: 'defect_spike',
      severity: 'medium',
      title: 'High defect rate detected',
      description: `Current defect rate is ${summary.defectRate.toFixed(1)}%, above normal threshold`,
      targetId: 'system',
      targetName: 'Quality Control',
      value: summary.defectRate,
      threshold: 15,
      createdAt: new Date().toISOString(),
    });
  }

  return alerts;
}

function generateRecommendations(summary: any, topIssues: any, trends: any) {
  const recommendations: any[] = [];

  // Immediate actions for high-risk suppliers
  for (const supplier of topIssues.worstSuppliers.slice(0, 2)) {
    if (supplier.riskLevel === 'high') {
      recommendations.push({
        type: 'immediate',
        priority: 'high',
        category: 'supplier',
        title: `Urgent supplier review: ${supplier.supplierName}`,
        description: `Schedule immediate quality audit and corrective action plan`,
        expectedImpact: `Potential reduction of ${(supplier.returnValue * 0.5 / 1000000).toFixed(1)}M IDR in returns`,
        targetId: supplier.supplierId,
        targetName: supplier.supplierName,
      });
    }
  }

  // Process improvements
  if (summary.defectRate > 10) {
    recommendations.push({
      type: 'short_term',
      priority: 'medium',
      category: 'process',
      title: 'Implement enhanced quality control',
      description: 'Strengthen incoming inspection processes and supplier quality requirements',
      expectedImpact: 'Reduce defect rate by 30-50%',
      estimatedCost: 50000000, // 50M IDR
    });
  }

  return recommendations;
}
