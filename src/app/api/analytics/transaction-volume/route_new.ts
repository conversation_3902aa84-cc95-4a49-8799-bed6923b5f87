import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToTransactionVolume } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Transaction Volume API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Transaction Volume API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethods = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[TransactionVolumeAPI] Query params:", {
      dateRange,
      cashierIds,
      terminalIds,
      categoryIds,
      paymentMethods,
      customFromDate,
      customToDate
    });

    // Calculate date range
    let fromDate: Date;
    let toDate: Date;

    if (customFromDate && customToDate) {
      fromDate = startOfDay(new Date(customFromDate));
      toDate = endOfDay(new Date(customToDate));
    } else {
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
    }

    // Build where clause
    const where: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      where.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      where.terminalId = {
        in: terminalIds,
      };
    }

    // Add payment method filter if specified
    if (paymentMethods && paymentMethods.length > 0) {
      where.paymentMethod = {
        in: paymentMethods,
      };
    }

    // Add category filter if specified (requires joining with transaction items)
    if (categoryIds && categoryIds.length > 0) {
      where.items = {
        some: {
          product: {
            categoryId: {
              in: categoryIds,
            },
          },
        },
      };
    }

    // For cashiers, only show their own data
    if (userRole === "CASHIER") {
      where.cashierId = userId;
    }

    console.log('[Transaction Volume API] Fetching transaction volume with filters:', where);

    console.log('[Transaction Volume API] Starting data fetch...');

    // Fetch transactions from database
    const transactions = await prisma.transaction.findMany({
      where,
      select: {
        id: true,
        total: true,
        status: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    console.log('[Transaction Volume API] Found', transactions.length, 'transactions');

    // Transform data using the existing transformer
    const transactionVolume = transformToTransactionVolume(transactions);

    console.log('[Transaction Volume API] Transformed to', transactionVolume.length, 'volume ranges');

    return NextResponse.json({
      success: true,
      data: transactionVolume,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('[Transaction Volume API] Error fetching transaction volume:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch transaction volume data',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
