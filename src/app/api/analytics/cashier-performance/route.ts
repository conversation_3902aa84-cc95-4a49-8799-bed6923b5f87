import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToCashierPerformance } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Cashier Performance API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access (only super admin and finance admin for cashier performance)
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Cashier Performance API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethods = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Calculate date range
    let fromDate: Date;
    let toDate = endOfDay(new Date());

    switch (dateRange) {
      case "7d":
        fromDate = startOfDay(subDays(new Date(), 7));
        break;
      case "30d":
        fromDate = startOfDay(subDays(new Date(), 30));
        break;
      case "90d":
        fromDate = startOfDay(subDays(new Date(), 90));
        break;
      case "1y":
        fromDate = startOfDay(subDays(new Date(), 365));
        break;
      default:
        fromDate = startOfDay(subDays(new Date(), 30));
    }

    // Build where clause
    const whereClause: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethods && paymentMethods.length > 0) {
      whereClause.paymentMethod = {
        in: paymentMethods,
      };
    }

    // Add category filter if specified (requires joining with transaction items)
    if (categoryIds && categoryIds.length > 0) {
      whereClause.items = {
        some: {
          product: {
            categoryId: {
              in: categoryIds,
            },
          },
        },
      };
    }

    // Fetch transactions with user information
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      select: {
        id: true,
        total: true,
        createdAt: true,
        status: true,
        cashierId: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Fetch all users (cashiers) for the transformer
    const users = await prisma.user.findMany({
      where: {
        role: "CASHIER",
        active: true,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    // Transform data for chart
    const cashierPerformance = transformToCashierPerformance(transactions, users);

    return NextResponse.json({
      data: cashierPerformance,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching cashier performance:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch cashier performance",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
