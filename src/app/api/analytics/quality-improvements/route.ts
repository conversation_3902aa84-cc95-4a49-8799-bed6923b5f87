import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { QualityImprovementAnalytics } from "@/lib/quality-improvement-analytics";

// GET /api/analytics/quality-improvements - Get quality improvement analytics
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const analysisType = searchParams.get("type") || "dashboard"; // "dashboard" or "effectiveness"
    const improvementId = searchParams.get("improvementId");
    const timeframe = parseInt(searchParams.get("timeframe") || "180"); // days

    if (analysisType === "effectiveness") {
      if (!improvementId) {
        return NextResponse.json({ error: 'Improvement ID is required for effectiveness analysis' }, { status: 400 });
      }

      // Generate effectiveness analysis for specific improvement
      const effectiveness = await QualityImprovementAnalytics.calculateImprovementEffectiveness(
        improvementId
      );

      return NextResponse.json({
        success: true,
        analysisType: "effectiveness",
        data: effectiveness,
        generatedAt: new Date(),
        generatedBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        }
      });

    } else {
      // Generate comprehensive dashboard
      const dashboard = await QualityImprovementAnalytics.generateImprovementDashboard(timeframe);

      return NextResponse.json({
        success: true,
        analysisType: "dashboard",
        timeframe: `${timeframe} days`,
        data: dashboard,
        generatedAt: new Date(),
        generatedBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        }
      });
    }

  } catch (error) {
    console.error("Error generating quality improvement analytics:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate quality improvement analytics",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
