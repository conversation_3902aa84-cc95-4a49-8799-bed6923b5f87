import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToCategoryPerformance } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Category Performance API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Category Performance API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethods = searchParams.get("paymentMethods")?.split(",").filter(Boolean);
    const limit = parseInt(searchParams.get("limit") || "10");

    // Handle custom date range
    const fromDateParam = searchParams.get("fromDate");
    const toDateParam = searchParams.get("toDate");

    let fromDate: Date;
    let toDate: Date;

    if (fromDateParam && toDateParam) {
      fromDate = new Date(fromDateParam);
      toDate = new Date(toDateParam);
      console.log("[CategoryPerformanceAPI] Using custom date range:", fromDate, "to", toDate);
    } else {
      // Calculate date range from preset
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[CategoryPerformanceAPI] Using preset date range:", dateRange, fromDate, "to", toDate);
    }

    // Build where clause for transaction items
    const whereClause: any = {
      transaction: {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
        status: {
          not: "VOIDED",
        },
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.transaction.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.transaction.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethods && paymentMethods.length > 0) {
      whereClause.transaction.paymentMethod = {
        in: paymentMethods,
      };
    }

    // Add category filter if specified
    if (categoryIds && categoryIds.length > 0) {
      whereClause.product = {
        categoryId: {
          in: categoryIds,
        },
      };
    }

    // For cashiers, only show their own data
    if (userRole === "CASHIER") {
      whereClause.transaction.cashierId = userId;
    }

    console.log("[CategoryPerformanceAPI] Where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch transaction items with product and category information
    const transactionItems = await prisma.transactionItem.findMany({
      where: whereClause,
      include: {
        product: {
          include: {
            category: true,
          },
        },
        transaction: {
          select: {
            status: true,
            createdAt: true,
          },
        },
      },
    });

    console.log("[CategoryPerformanceAPI] Found", transactionItems.length, "transaction items");

    // Fetch all categories for the transformer
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        name: true,
      },
    });

    console.log("[CategoryPerformanceAPI] Found", categories.length, "categories");

    // Transform data for chart
    const categoryPerformance = transformToCategoryPerformance(transactionItems, categories);

    console.log("[CategoryPerformanceAPI] Transformed to", categoryPerformance.length, "category performance items");

    // Limit results
    const limitedResults = categoryPerformance.slice(0, limit);

    console.log("[CategoryPerformanceAPI] Returning", limitedResults.length, "limited results");

    return NextResponse.json({
      data: limitedResults,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching category performance:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch category performance",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
