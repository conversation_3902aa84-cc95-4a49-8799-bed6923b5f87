import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/auth';
import { verifyAuthToken } from '@/lib/auth-utils';
import { DrawerSessionData } from '@/lib/types/analytics';

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Drawer Sessions API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Drawer Sessions API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const from = searchParams.get('from');
    const to = searchParams.get('to');
    const cashierIds = searchParams.get('cashierIds')?.split(',').filter(Boolean);
    const terminalIds = searchParams.get('terminalIds')?.split(',').filter(Boolean);

    console.log('[Drawer Sessions API] Query params received:', { from, to, cashierIds, terminalIds });

    // Build where clause
    const where: any = {};

    if (from || to) {
      where.openedAt = {};
      if (from) where.openedAt.gte = new Date(from);
      if (to) where.openedAt.lte = new Date(to);
    }

    if (cashierIds?.length) {
      where.userId = { in: cashierIds };
    }

    if (terminalIds?.length) {
      where.terminalId = { in: terminalIds };
    }

    console.log('[Drawer Sessions API] Where clause:', JSON.stringify(where, null, 2));

    // Fetch drawer sessions from database
    const drawerSessions = await prisma.drawerSession.findMany({
      where,
      include: {
        user: {
          select: {
            name: true
          }
        },
        terminal: {
          select: {
            name: true
          }
        },
        drawer: {
          select: {
            name: true
          }
        },
        transactions: {
          where: {
            status: 'COMPLETED'
          },
          select: {
            id: true
          }
        }
      },
      orderBy: {
        openedAt: 'desc'
      },
      take: 50 // Limit to recent 50 sessions
    });

    console.log(`[Drawer Sessions API] Found ${drawerSessions.length} drawer sessions`);

    // Transform data
    const data: DrawerSessionData[] = drawerSessions.map((session) => {
      // Calculate discrepancy
      const discrepancy = session.actualClosingBalance !== null && session.expectedClosingBalance !== null
        ? Number(session.actualClosingBalance) - Number(session.expectedClosingBalance)
        : undefined;

      return {
        id: session.id,
        cashierName: session.user.name,
        terminalName: session.terminal?.name,
        drawerName: session.drawer?.name,
        openedAt: session.openedAt.toISOString(),
        closedAt: session.closedAt?.toISOString(),
        openingBalance: Number(session.openingBalance),
        closingBalance: session.actualClosingBalance ? Number(session.actualClosingBalance) : undefined,
        expectedClosingBalance: session.expectedClosingBalance ? Number(session.expectedClosingBalance) : undefined,
        discrepancy,
        transactionCount: session.transactions.length,
        status: session.status,
        businessDate: session.businessDate.toISOString().split('T')[0]
      };
    });

    console.log('[Drawer Sessions API] Transformed data:', data.slice(0, 3));

    return NextResponse.json({
      success: true,
      data,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Analytics] Error fetching drawer sessions:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch drawer sessions data',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}