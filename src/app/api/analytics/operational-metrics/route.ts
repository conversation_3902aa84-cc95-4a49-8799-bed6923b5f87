import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { 
  OperationalMetricsData,
  CashOperationMetrics,
  SystemPerformanceMetrics,
  InventoryOperationMetrics,
  AuditComplianceMetrics,
  PerformanceBenchmarks
} from "@/lib/types/analytics";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

export async function GET(request: NextRequest) {
  try {
    console.log('[Operational Metrics API] Starting data fetch...');

    const { searchParams } = new URL(request.url);
    const from = searchParams.get("from");
    const to = searchParams.get("to");

    // Set default date range (last 30 days)
    const endDate = to ? new Date(to) : new Date();
    const startDate = from ? new Date(from) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    console.log('[Operational Metrics API] Date range:', { startDate, endDate });

    // Fetch all required data in parallel
    const [
      drawerSessions,
      cashReconciliations,
      cashAuditAlerts,
      activityLogs,
      transactions,
      stockAdjustments,
      users,
      products
    ] = await Promise.all([
      // Drawer Sessions
      prisma.drawerSession.findMany({
        where: {
          openedAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          openedAt: true,
          closedAt: true,
          openingBalance: true,
          actualClosingBalance: true,
          discrepancy: true,
          status: true
        }
      }),

      // Cash Reconciliations
      prisma.cashReconciliation.findMany({
        where: {
          businessDate: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          businessDate: true,
          discrepancy: true,
          status: true,
          resolutionStatus: true,
          resolvedAt: true,
          createdAt: true,
          openingBalance: true,
          expectedAmount: true,
          actualAmount: true
        }
      }),

      // Cash Audit Alerts
      prisma.cashAuditAlert.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          alertType: true,
          severity: true,
          isResolved: true,
          resolvedAt: true,
          createdAt: true,
          threshold: true,
          actualValue: true
        }
      }),

      // Activity Logs
      prisma.activityLog.findMany({
        where: {
          timestamp: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          userId: true,
          action: true,
          timestamp: true
        }
      }),

      // Transactions
      prisma.transaction.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          total: true,
          createdAt: true,
          status: true,
          paymentStatus: true,
          cashierId: true
        }
      }),

      // Stock Adjustments
      prisma.stockAdjustment.findMany({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        },
        select: {
          id: true,
          reason: true,
          adjustmentQuantity: true,
          createdAt: true,
          userId: true
        }
      }),

      // Active Users
      prisma.user.findMany({
        where: { active: true },
        select: {
          id: true,
          role: true,
          createdAt: true
        }
      }),

      // Products with transaction data for stock movement calculation
      prisma.product.findMany({
        include: {
          category: { select: { name: true } },
          storeStock: { select: { quantity: true, minThreshold: true } },
          transactionItems: {
            where: {
              transaction: {
                createdAt: { gte: startDate, lte: endDate },
                status: 'COMPLETED'
              }
            },
            select: {
              quantity: true,
              unitPrice: true,
              subtotal: true,
              transaction: {
                select: {
                  createdAt: true,
                  id: true
                }
              }
            }
          }
        }
      })
    ]);

    console.log('[Operational Metrics API] Data fetched, processing metrics...');

    // Calculate Cash Operations Metrics
    const cashOperations = calculateCashOperationMetrics(
      drawerSessions,
      cashReconciliations,
      startDate,
      endDate
    );

    // Calculate System Performance Metrics
    const systemPerformance = calculateSystemPerformanceMetrics(
      activityLogs,
      transactions,
      users,
      startDate,
      endDate
    );

    // Calculate Inventory Operations Metrics
    const inventoryOps = await await calculateInventoryOperationMetrics(
      stockAdjustments,
      products,
      startDate,
      endDate
    );

    // Calculate Audit & Compliance Metrics
    const auditCompliance = await calculateAuditComplianceMetrics(
      cashAuditAlerts,
      cashReconciliations,
      activityLogs,
      transactions,
      startDate,
      endDate
    );

    // Calculate Performance Benchmarks
    const performanceBenchmarks = await calculatePerformanceBenchmarks(
      transactions,
      drawerSessions,
      users,
      products,
      stockAdjustments,
      cashAuditAlerts,
      cashReconciliations,
      startDate,
      endDate
    );

    // Calculate summary metrics
    const summary = {
      totalMetrics: 25, // Total number of metrics tracked
      alertsRequiringAttention: 
        cashOperations.reconciliationPerformance.pendingCount +
        auditCompliance.securityAlerts.highSeverityAlerts +
        inventoryOps.lowStockAlerts.criticalAlerts,
      overallHealthScore: Math.round(
        (performanceBenchmarks.dailyOperationalScore.overallScore +
         (auditCompliance.complianceScores.reconciliationAccuracy || 0) +
         (systemPerformance.transactionProcessing.processingEfficiency || 0)) / 3
      ),
      lastUpdated: new Date().toISOString()
    };

    const operationalMetrics: OperationalMetricsData = {
      cashOperations,
      systemPerformance,
      inventoryOps,
      auditCompliance,
      performanceBenchmarks,
      summary
    };

    console.log('[Operational Metrics API] Metrics calculated successfully');

    return NextResponse.json({
      success: true,
      data: operationalMetrics,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Operational Metrics API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch operational metrics',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate cash operation metrics
function calculateCashOperationMetrics(
  drawerSessions: any[],
  cashReconciliations: any[],
  startDate: Date,
  endDate: Date
): CashOperationMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  
  // Drawer Sessions Metrics
  const closedSessions = drawerSessions.filter(s => s.closedAt);
  const totalSessionDuration = closedSessions.reduce((sum, session) => {
    if (session.closedAt && session.openedAt) {
      return sum + (new Date(session.closedAt).getTime() - new Date(session.openedAt).getTime());
    }
    return sum;
  }, 0);
  
  const averageSessionDuration = closedSessions.length > 0 
    ? Math.round(totalSessionDuration / (closedSessions.length * 60 * 1000)) // in minutes
    : 0;

  // Cash Discrepancies
  const discrepancies = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) > 0);
  const totalDiscrepancy = discrepancies.reduce((sum, r) => sum + Math.abs(Number(r.discrepancy)), 0);
  const averageDiscrepancy = discrepancies.length > 0 ? totalDiscrepancy / discrepancies.length : 0;
  
  // Large discrepancies (> 50000 IDR equivalent)
  const largeDiscrepancies = discrepancies.filter(r => Math.abs(Number(r.discrepancy)) > 50000);

  // Reconciliation Performance
  const pendingReconciliations = cashReconciliations.filter(r => r.resolutionStatus === 'PENDING');
  const resolvedReconciliations = cashReconciliations.filter(r => r.resolvedAt);
  
  const resolutionTimes = resolvedReconciliations
    .filter(r => r.resolvedAt && r.createdAt)
    .map(r => new Date(r.resolvedAt).getTime() - new Date(r.createdAt).getTime());
  
  const averageResolutionTime = resolutionTimes.length > 0
    ? Math.round(resolutionTimes.reduce((sum, time) => sum + time, 0) / (resolutionTimes.length * 60 * 60 * 1000)) // in hours
    : 0;

  // Cash Flow
  const totalCashIn = cashReconciliations.reduce((sum, r) => sum + Number(r.expectedAmount), 0);
  const totalCashOut = cashReconciliations.reduce((sum, r) => sum + Number(r.actualAmount), 0);
  const averageOpeningBalance = cashReconciliations.length > 0
    ? cashReconciliations.reduce((sum, r) => sum + Number(r.openingBalance), 0) / cashReconciliations.length
    : 0;

  return {
    drawerSessions: {
      totalSessions: drawerSessions.length,
      averageSessionDuration,
      sessionsPerDay: Math.round(drawerSessions.length / Math.max(daysDiff, 1)),
      utilizationRate: Math.round((closedSessions.length / Math.max(drawerSessions.length, 1)) * 100)
    },
    cashDiscrepancies: {
      totalDiscrepancies: discrepancies.length,
      averageDiscrepancy: Math.round(averageDiscrepancy),
      discrepancyTrend: 0, // Would need historical data to calculate
      largeDiscrepanciesCount: largeDiscrepancies.length
    },
    reconciliationPerformance: {
      pendingCount: pendingReconciliations.length,
      averageResolutionTime,
      resolutionRate: Math.round((resolvedReconciliations.length / Math.max(cashReconciliations.length, 1)) * 100),
      overdueCount: pendingReconciliations.filter(r => {
        const daysSinceCreated = (Date.now() - new Date(r.createdAt).getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceCreated > 1; // Overdue if pending for more than 1 day
      }).length
    },
    cashFlow: {
      totalCashIn: Math.round(totalCashIn),
      totalCashOut: Math.round(totalCashOut),
      netCashFlow: Math.round(totalCashIn - totalCashOut),
      averageOpeningBalance: Math.round(averageOpeningBalance)
    }
  };
}

// Helper function to calculate system performance metrics
function calculateSystemPerformanceMetrics(
  activityLogs: any[],
  transactions: any[],
  users: any[],
  startDate: Date,
  endDate: Date
): SystemPerformanceMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // User Activity
  const loginLogs = activityLogs.filter(log => log.action.includes('login') || log.action.includes('LOGIN'));
  const uniqueUsers = new Set(activityLogs.map(log => log.userId));

  // Transaction Processing
  const completedTransactions = transactions.filter(t => t.status === 'COMPLETED');
  const failedTransactions = transactions.filter(t => t.status === 'FAILED' || t.paymentStatus === 'FAILED');

  // Calculate hourly transaction distribution
  const hourlyTransactions = new Array(24).fill(0);
  transactions.forEach(t => {
    const hour = new Date(t.createdAt).getHours();
    hourlyTransactions[hour]++;
  });
  const peakHour = hourlyTransactions.indexOf(Math.max(...hourlyTransactions));
  const peakHourTransactions = Math.max(...hourlyTransactions);

  return {
    userActivity: {
      totalLogins: loginLogs.length,
      averageSessionDuration: calculateAverageSessionDuration(activityLogs, daysDiff),
      peakHourActivity: peakHour,
      activeUsersCount: uniqueUsers.size
    },
    transactionProcessing: {
      averageProcessingTime: calculateAverageProcessingTime(transactions),
      transactionsPerHour: Math.round(transactions.length / Math.max(daysDiff * 24, 1)),
      peakHourTransactions,
      processingEfficiency: Math.round((completedTransactions.length / Math.max(transactions.length, 1)) * 100)
    },
    errorRates: {
      failedTransactions: failedTransactions.length,
      errorRate: Math.round((failedTransactions.length / Math.max(transactions.length, 1)) * 100),
      systemIssues: calculateSystemIssues(activityLogs, transactions),
      recoveryTime: calculateRecoveryTime(activityLogs, transactions)
    },
    peakLoadAnalysis: {
      busiestHour: peakHour,
      concurrentUsers: Math.max(uniqueUsers.size, 1),
      systemLoad: Math.min(Math.round((transactions.length / (daysDiff * 100)) * 100), 100),
      responseTime: calculateResponseTime(transactions, daysDiff)
    }
  };
}

// Helper function to calculate inventory operation metrics
async function calculateInventoryOperationMetrics(
  stockAdjustments: any[],
  products: any[],
  startDate: Date,
  endDate: Date
): InventoryOperationMetrics {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Stock Adjustments
  const reasonCounts = stockAdjustments.reduce((acc, adj) => {
    acc[adj.reason] = (acc[adj.reason] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const reasonBreakdown = Object.entries(reasonCounts).map(([reason, count]) => ({
    reason,
    count,
    percentage: Math.round((Number(count) / Math.max(stockAdjustments.length, 1)) * 100)
  }));

  return {
    stockAdjustments: {
      totalAdjustments: stockAdjustments.length,
      adjustmentFrequency: Math.round(stockAdjustments.length / Math.max(daysDiff, 1)),
      reasonBreakdown,
      adjustmentTrend: await calculateAdjustmentTrend(startDate, endDate)
    },
    stockMovement: {
      fastMovingItems: calculateFastMovingItemsCount(products, startDate, endDate),
      slowMovingItems: calculateSlowMovingItemsCount(products, startDate, endDate),
      turnoverVelocity: calculateAverageTurnoverVelocity(products, startDate, endDate),
      stockoutEvents: reasonBreakdown.find(r => r.reason === 'STOCKOUT')?.count || 0
    },
    lowStockAlerts: {
      totalAlerts: calculateLowStockAlerts(products).totalAlerts,
      criticalAlerts: calculateLowStockAlerts(products).criticalAlerts,
      averageResponseTime: calculateLowStockAlerts(products).averageResponseTime,
      restockEfficiency: calculateRestockEfficiency(stockAdjustments, products)
    },
    transferEfficiency: {
      totalTransfers: calculateTransferMetrics(stockAdjustments).totalTransfers,
      completionRate: calculateTransferMetrics(stockAdjustments).completionRate,
      averageProcessingTime: calculateTransferMetrics(stockAdjustments).averageProcessingTime,
      transferAccuracy: calculateTransferMetrics(stockAdjustments).transferAccuracy
    }
  };
}

// Helper function to calculate audit compliance metrics
async function calculateAuditComplianceMetrics(
  cashAuditAlerts: any[],
  cashReconciliations: any[],
  activityLogs: any[],
  transactions: any[],
  startDate: Date,
  endDate: Date
): AuditComplianceMetrics {
  // Security Alerts
  const highSeverityAlerts = cashAuditAlerts.filter(alert => alert.severity === 'HIGH' || alert.severity === 'CRITICAL');
  const resolvedAlerts = cashAuditAlerts.filter(alert => alert.isResolved);

  const resolutionTimes = resolvedAlerts
    .filter(alert => alert.resolvedAt && alert.createdAt)
    .map(alert => new Date(alert.resolvedAt).getTime() - new Date(alert.createdAt).getTime());

  const averageResolutionTime = resolutionTimes.length > 0
    ? Math.round(resolutionTimes.reduce((sum, time) => sum + time, 0) / (resolutionTimes.length * 60 * 60 * 1000))
    : 0;

  // Compliance Scores
  const accurateReconciliations = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) <= 1000); // Within 1000 IDR
  const reconciliationAccuracy = Math.round((accurateReconciliations.length / Math.max(cashReconciliations.length, 1)) * 100);

  // Risk Indicators
  const largeDiscrepancies = cashReconciliations.filter(r => Math.abs(Number(r.discrepancy)) > 50000);
  const riskScore = Math.min(100, Math.max(0, 100 - (largeDiscrepancies.length * 10) - (highSeverityAlerts.length * 5)));

  let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
  if (riskScore >= 80) riskLevel = 'LOW';
  else if (riskScore >= 60) riskLevel = 'MEDIUM';
  else if (riskScore >= 40) riskLevel = 'HIGH';
  else riskLevel = 'CRITICAL';

  // Compliance Rating
  let complianceRating = 'A';
  if (reconciliationAccuracy >= 95) complianceRating = 'A';
  else if (reconciliationAccuracy >= 90) complianceRating = 'B';
  else if (reconciliationAccuracy >= 80) complianceRating = 'C';
  else if (reconciliationAccuracy >= 70) complianceRating = 'D';
  else complianceRating = 'F';

  return {
    securityAlerts: {
      totalAlerts: cashAuditAlerts.length,
      highSeverityAlerts: highSeverityAlerts.length,
      averageResolutionTime,
      alertTrend: await calculateAlertTrend(startDate, endDate)
    },
    complianceScores: {
      reconciliationAccuracy,
      auditTrailCompleteness: calculateAuditTrailCompleteness(activityLogs, transactions, cashReconciliations),
      complianceRating,
      improvementTrend: await calculateImprovementTrend(startDate, endDate)
    },
    riskIndicators: {
      largeDiscrepancyFrequency: largeDiscrepancies.length,
      unusualTransactionPatterns: calculateUnusualTransactionPatterns(transactions),
      riskScore,
      riskLevel
    },
    resolutionEfficiency: {
      averageResolutionTime,
      firstTimeResolutionRate: Math.round((resolvedAlerts.length / Math.max(cashAuditAlerts.length, 1)) * 100),
      escalationRate: Math.round((highSeverityAlerts.length / Math.max(cashAuditAlerts.length, 1)) * 100),
      customerSatisfaction: calculateCustomerSatisfaction(cashAuditAlerts, cashReconciliations)
    }
  };
}

// Helper function to calculate performance benchmarks
async function calculatePerformanceBenchmarks(
  transactions: any[],
  drawerSessions: any[],
  users: any[],
  products: any[],
  stockAdjustments: any[],
  cashAuditAlerts: any[],
  cashReconciliations: any[],
  startDate: Date,
  endDate: Date
): Promise<PerformanceBenchmarks> {
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  const hoursDiff = daysDiff * 24;

  // Calculate total revenue
  const totalRevenue = transactions.reduce((sum, t) => sum + Number(t.total), 0);
  const completedTransactions = transactions.filter(t => t.status === 'COMPLETED');
  const cashiers = users.filter(u => u.role === 'CASHIER');

  // Calculate individual scores (0-100)
  const cashManagementScore = Math.min(100, Math.max(0,
    100 - (drawerSessions.filter(s => s.discrepancy && Math.abs(Number(s.discrepancy)) > 10000).length * 10)
  ));

  const systemPerformanceScore = Math.min(100, Math.max(0,
    (completedTransactions.length / Math.max(transactions.length, 1)) * 100
  ));

  const inventoryOpsScore = calculateInventoryOpsScore(products, stockAdjustments);
  const complianceScore = calculateComplianceScore(cashAuditAlerts, cashReconciliations);

  // Overall score
  const overallScore = Math.round(
    (cashManagementScore + systemPerformanceScore + inventoryOpsScore + complianceScore) / 4
  );

  // Grade calculation
  let grade = 'F';
  if (overallScore >= 97) grade = 'A+';
  else if (overallScore >= 93) grade = 'A';
  else if (overallScore >= 90) grade = 'A-';
  else if (overallScore >= 87) grade = 'B+';
  else if (overallScore >= 83) grade = 'B';
  else if (overallScore >= 80) grade = 'B-';
  else if (overallScore >= 77) grade = 'C+';
  else if (overallScore >= 73) grade = 'C';
  else if (overallScore >= 70) grade = 'C-';
  else if (overallScore >= 60) grade = 'D';

  // Efficiency ratios
  const revenuePerHour = hoursDiff > 0 ? Math.round(totalRevenue / hoursDiff) : 0;
  const transactionsPerCashier = cashiers.length > 0 ? Math.round(transactions.length / cashiers.length) : 0;
  const costPerTransaction = calculateCostPerTransaction(transactions, stockAdjustments, users);
  const profitMarginRatio = await calculateRealProfitMargin(transactions, startDate, endDate);

  // Trend direction (placeholder - would need historical data)
  let trendDirection: 'IMPROVING' | 'STABLE' | 'DECLINING' = 'STABLE';
  if (overallScore >= 85) trendDirection = 'IMPROVING';
  else if (overallScore <= 70) trendDirection = 'DECLINING';

  return {
    dailyOperationalScore: {
      overallScore,
      scoreBreakdown: {
        cashManagement: Math.round(cashManagementScore),
        systemPerformance: Math.round(systemPerformanceScore),
        inventoryOps: Math.round(inventoryOpsScore),
        compliance: Math.round(complianceScore)
      },
      grade
    },
    trendComparisons: {
      weekOverWeek: await calculateWeekOverWeekTrend(totalRevenue, startDate, endDate),
      monthOverMonth: await calculateMonthOverMonthTrend(totalRevenue, startDate, endDate),
      yearOverYear: await calculateYearOverYearTrend(totalRevenue, startDate, endDate),
      trendDirection
    },
    targetVsActual: await calculateTargetVsActual(totalRevenue, startDate, endDate),
    efficiencyRatios: {
      revenuePerHour,
      transactionsPerCashier,
      costPerTransaction,
      profitMarginRatio
    }
  };
}

// Helper function to calculate target vs actual performance with real revenue targets
async function calculateTargetVsActual(totalRevenue: number, startDate: Date, endDate: Date) {
  try {
    // Try to find an active revenue target for the period
    const currentTarget = await prisma.revenueTarget.findFirst({
      where: {
        targetType: 'MONTHLY',
        isActive: true,
        startDate: { lte: endDate },
        endDate: { gte: startDate }
      },
      orderBy: [
        { startDate: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    let revenueTarget: number;
    let isDefaultTarget = false;

    if (currentTarget) {
      revenueTarget = parseFloat(currentTarget.amount.toString());
      console.log(`[Target vs Actual] Using target: ${currentTarget.name} - ${revenueTarget}`);
    } else {
      // Calculate historical-based target
      const historicalData = await calculateHistoricalAverage(startDate, endDate);
      revenueTarget = historicalData.suggestedTarget;
      isDefaultTarget = true;
      console.log(`[Target vs Actual] Using historical target: ${revenueTarget}`);
    }

    const targetAchievement = Math.round((totalRevenue / revenueTarget) * 100);
    const performanceGap = revenueTarget - totalRevenue;

    return {
      revenueTarget,
      actualRevenue: totalRevenue,
      targetAchievement,
      performanceGap,
      isDefaultTarget
    };

  } catch (error) {
    console.error('[Target vs Actual] Error:', error);
    // Fallback to default calculation
    const revenueTarget = totalRevenue * 1.1; // 10% growth target
    return {
      revenueTarget,
      actualRevenue: totalRevenue,
      targetAchievement: Math.round((totalRevenue / revenueTarget) * 100),
      performanceGap: revenueTarget - totalRevenue,
      isDefaultTarget: true
    };
  }
}

// Helper function to calculate historical average for target suggestion
async function calculateHistoricalAverage(from: Date, to: Date) {
  try {
    const periodLength = to.getTime() - from.getTime();
    const periodsToAnalyze = 6; // Analyze last 6 similar periods

    let totalRevenue = 0;
    let validPeriods = 0;

    for (let i = 1; i <= periodsToAnalyze; i++) {
      const periodStart = new Date(from.getTime() - (periodLength * i));
      const periodEnd = new Date(to.getTime() - (periodLength * i));

      const transactions = await prisma.transaction.findMany({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: periodStart,
            lte: periodEnd
          }
        },
        select: { total: true }
      });

      const periodRevenue = transactions.reduce((sum, t) => sum + parseFloat(t.total.toString()), 0);

      if (periodRevenue > 0) {
        totalRevenue += periodRevenue;
        validPeriods++;
      }
    }

    const average = validPeriods > 0 ? totalRevenue / validPeriods : 0;
    const growthRate = 0.10; // 10% growth target
    const suggestedTarget = average * (1 + growthRate);

    console.log(`[Historical Analysis] Average: ${average}, Suggested Target: ${suggestedTarget}, Periods: ${validPeriods}`);

    return {
      average,
      suggestedTarget,
      growthRate,
      periodsAnalyzed: validPeriods
    };

  } catch (error) {
    console.error('[Historical Analysis] Error:', error);
    return {
      average: 0,
      suggestedTarget: 1000000, // Default 1M IDR
      growthRate: 0.10,
      periodsAnalyzed: 0
    };
  }
}

// Helper functions for stock movement calculations
function calculateFastMovingItemsCount(products: any[], startDate: Date, endDate: Date): number {
  const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Calculate insights for each product
  const productInsights = products.map(product => {
    const sales = product.transactionItems || [];
    const totalQuantitySold = sales.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);
    const turnoverRate = totalQuantitySold / daysDiff;
    
    return {
      ...product,
      turnoverRate,
      totalQuantitySold
    };
  });

  // Calculate average turnover rate
  const totalTurnover = productInsights.reduce((sum, p) => sum + p.turnoverRate, 0);
  const avgTurnoverRate = productInsights.length > 0 ? totalTurnover / productInsights.length : 0;

  // Count fast-moving products (above average turnover rate)
  return productInsights.filter(p => p.turnoverRate > avgTurnoverRate).length;
}

function calculateSlowMovingItemsCount(products: any[], startDate: Date, endDate: Date): number {
  const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Calculate insights for each product
  const productInsights = products.map(product => {
    const sales = product.transactionItems || [];
    const totalQuantitySold = sales.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);
    const turnoverRate = totalQuantitySold / daysDiff;
    
    return {
      ...product,
      turnoverRate,
      totalQuantitySold
    };
  });

  // Calculate average turnover rate
  const totalTurnover = productInsights.reduce((sum, p) => sum + p.turnoverRate, 0);
  const avgTurnoverRate = productInsights.length > 0 ? totalTurnover / productInsights.length : 0;

  // Count slow-moving products (below 50% of average turnover rate)
  return productInsights.filter(p => p.turnoverRate < avgTurnoverRate * 0.5).length;
}

function calculateAverageTurnoverVelocity(products: any[], startDate: Date, endDate: Date): number {
  const daysDiff = Math.max(1, Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)));
  
  // Calculate insights for each product
  const productInsights = products.map(product => {
    const sales = product.transactionItems || [];
    const totalQuantitySold = sales.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);
    const turnoverRate = totalQuantitySold / daysDiff;
    
    return {
      ...product,
      turnoverRate,
      totalQuantitySold
    };
  });

  // Calculate average turnover rate
  const totalTurnover = productInsights.reduce((sum, p) => sum + p.turnoverRate, 0);
  return productInsights.length > 0 ? Math.round((totalTurnover / productInsights.length) * 100) / 100 : 0;
}

// ===== SYSTEM PERFORMANCE HELPER FUNCTIONS =====

function calculateAverageSessionDuration(activityLogs: any[], daysDiff: number): number {
  // Calculate based on login/logout patterns
  const loginLogs = activityLogs.filter(log => log.action === 'LOGIN');
  const logoutLogs = activityLogs.filter(log => log.action === 'LOGOUT');
  
  if (loginLogs.length === 0) return 0;
  
  // Estimate session duration based on activity patterns
  const totalHours = daysDiff * 24;
  const averageSessionsPerDay = loginLogs.length / Math.max(daysDiff, 1);
  const estimatedHoursPerSession = totalHours / Math.max(loginLogs.length, 1);
  
  // Cap at reasonable business hours (8 hours max per session)
  return Math.min(Math.round(estimatedHoursPerSession * 60), 480);
}

function calculateAverageProcessingTime(transactions: any[]): number {
  if (transactions.length === 0) return 0;
  
  // Calculate based on transaction complexity
  let totalProcessingTime = 0;
  let processedCount = 0;
  
  transactions.forEach(transaction => {
    // Estimate processing time based on transaction amount and status
    let estimatedTime = 10; // Base time in seconds
    
    // Add time based on transaction amount (larger amounts take longer)
    if (transaction.total > 100000) estimatedTime += 5; // 100k IDR+
    if (transaction.total > 500000) estimatedTime += 10; // 500k IDR+
    
    // Add time for failed transactions (they take longer to process)
    if (transaction.status === 'FAILED' || transaction.status === 'CANCELLED') {
      estimatedTime += 15;
    }
    
    totalProcessingTime += estimatedTime;
    processedCount++;
  });
  
  return processedCount > 0 ? Math.round(totalProcessingTime / processedCount) : 15;
}

function calculateSystemIssues(activityLogs: any[], transactions: any[]): number {
  // Count system-related issues
  const errorLogs = activityLogs.filter(log => 
    log.action.includes('ERROR') || 
    log.action.includes('FAILED') ||
    log.action.includes('TIMEOUT')
  );
  
  const failedTransactions = transactions.filter(t => 
    t.status === 'FAILED' || t.status === 'CANCELLED'
  );
  
  // System issues = error logs + failed transactions that might be system-related
  return errorLogs.length + Math.floor(failedTransactions.length * 0.5); // Assume 50% of failed transactions are system issues
}

function calculateRecoveryTime(activityLogs: any[], transactions: any[]): number {
  // Calculate average time to recover from issues
  const errorLogs = activityLogs.filter(log => 
    log.action.includes('ERROR') || log.action.includes('FAILED')
  );
  
  if (errorLogs.length === 0) return 0;
  
  // Estimate recovery time based on error frequency
  const failedTransactions = transactions.filter(t => t.status === 'FAILED');
  const errorRate = failedTransactions.length / Math.max(transactions.length, 1);
  
  // Higher error rates suggest longer recovery times
  if (errorRate > 0.1) return 15; // High error rate = 15 min recovery
  if (errorRate > 0.05) return 8;  // Medium error rate = 8 min recovery
  return 3; // Low error rate = 3 min recovery
}

function calculateResponseTime(transactions: any[], daysDiff: number): number {
  if (transactions.length === 0) return 200;
  
  // Estimate response time based on transaction volume and system load
  const transactionsPerDay = transactions.length / Math.max(daysDiff, 1);
  
  // Base response time
  let responseTime = 150;
  
  // Add latency based on load
  if (transactionsPerDay > 100) responseTime += 50;  // High load
  if (transactionsPerDay > 200) responseTime += 100; // Very high load
  if (transactionsPerDay > 500) responseTime += 150; // Extreme load
  
  return Math.round(responseTime);
}

// ===== INVENTORY OPERATIONS HELPER FUNCTIONS =====

async function calculateAdjustmentTrend(startDate: Date, endDate: Date): Promise<number> {
  try {
    // Calculate previous period for comparison
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodLength);
    const previousEndDate = new Date(startDate.getTime());
    
    const [currentAdjustments, previousAdjustments] = await Promise.all([
      prisma.stockAdjustment.count({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.stockAdjustment.count({
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate }
        }
      })
    ]);
    
    if (previousAdjustments === 0) return currentAdjustments > 0 ? 100 : 0;
    return Math.round(((currentAdjustments - previousAdjustments) / previousAdjustments) * 100);
  } catch (error) {
    console.error('[Adjustment Trend] Error:', error);
    return 0;
  }
}

function calculateLowStockAlerts(products: any[]): {
  totalAlerts: number;
  criticalAlerts: number;
  averageResponseTime: number;
} {
  let totalAlerts = 0;
  let criticalAlerts = 0;
  
  products.forEach(product => {
    const stock = product.storeStock;
    if (stock) {
      const currentQuantity = Number(stock.quantity) || 0;
      const minThreshold = Number(stock.minThreshold) || 0;
      
      if (currentQuantity <= minThreshold) {
        totalAlerts++;
        
        // Critical if stock is 50% below threshold or zero
        if (currentQuantity <= minThreshold * 0.5 || currentQuantity === 0) {
          criticalAlerts++;
        }
      }
    }
  });
  
  // Estimate average response time based on alert severity
  const averageResponseTime = criticalAlerts > 0 ? 
    Math.round(2 + (criticalAlerts / Math.max(totalAlerts, 1)) * 4) : // 2-6 hours based on criticality
    totalAlerts > 0 ? 4 : 0; // 4 hours for non-critical
  
  return { totalAlerts, criticalAlerts, averageResponseTime };
}

function calculateRestockEfficiency(stockAdjustments: any[], products: any[]): number {
  // Calculate efficiency based on restock adjustments vs low stock situations
  const restockAdjustments = stockAdjustments.filter(adj => 
    adj.reason === 'RESTOCK' || adj.reason === 'PURCHASE' || adj.adjustmentQuantity > 0
  );
  
  const lowStockProducts = products.filter(product => {
    const stock = product.storeStock;
    return stock && Number(stock.quantity) <= Number(stock.minThreshold);
  });
  
  if (lowStockProducts.length === 0) return 100; // Perfect efficiency if no low stock
  
  // Efficiency = (restock actions / low stock situations) * 100, capped at 100%
  const efficiency = Math.min((restockAdjustments.length / lowStockProducts.length) * 100, 100);
  return Math.round(efficiency);
}

function calculateTransferMetrics(stockAdjustments: any[]): {
  totalTransfers: number;
  completionRate: number;
  averageProcessingTime: number;
  transferAccuracy: number;
} {
  const transferAdjustments = stockAdjustments.filter(adj => 
    adj.reason === 'TRANSFER' || adj.reason === 'TRANSFER_IN' || adj.reason === 'TRANSFER_OUT'
  );
  
  const totalTransfers = transferAdjustments.length;
  
  if (totalTransfers === 0) {
    return {
      totalTransfers: 0,
      completionRate: 100,
      averageProcessingTime: 0,
      transferAccuracy: 100
    };
  }
  
  // Assume all recorded transfers are completed (since they're in the system)
  const completionRate = 100;
  
  // Estimate processing time based on transfer volume
  const averageProcessingTime = totalTransfers > 10 ? 3 : 2; // Hours
  
  // Estimate accuracy based on adjustment patterns
  const positiveAdjustments = transferAdjustments.filter(adj => adj.adjustmentQuantity > 0).length;
  const negativeAdjustments = transferAdjustments.filter(adj => adj.adjustmentQuantity < 0).length;
  
  // Good accuracy if transfers are balanced (in/out)
  const balance = Math.abs(positiveAdjustments - negativeAdjustments) / Math.max(totalTransfers, 1);
  const transferAccuracy = Math.round(Math.max(85, 100 - (balance * 20))); // 85-100% based on balance
  
  return {
    totalTransfers,
    completionRate,
    averageProcessingTime,
    transferAccuracy
  };
}

// ===== AUDIT & COMPLIANCE HELPER FUNCTIONS =====

async function calculateAlertTrend(startDate: Date, endDate: Date): Promise<number> {
  try {
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodLength);
    const previousEndDate = new Date(startDate.getTime());
    
    const [currentAlerts, previousAlerts] = await Promise.all([
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate }
        }
      })
    ]);
    
    if (previousAlerts === 0) return currentAlerts > 0 ? 100 : 0;
    return Math.round(((currentAlerts - previousAlerts) / previousAlerts) * 100);
  } catch (error) {
    console.error('[Alert Trend] Error:', error);
    return 0;
  }
}

function calculateAuditTrailCompleteness(activityLogs: any[], transactions: any[], cashReconciliations: any[]): number {
  // Calculate completeness based on expected vs actual audit entries
  const expectedEntries = transactions.length + cashReconciliations.length;
  const actualAuditEntries = activityLogs.filter(log => 
    log.action.includes('TRANSACTION') || 
    log.action.includes('CASH') ||
    log.action.includes('RECONCILIATION')
  ).length;
  
  if (expectedEntries === 0) return 100;
  
  const completeness = Math.min((actualAuditEntries / expectedEntries) * 100, 100);
  return Math.round(completeness);
}

async function calculateImprovementTrend(startDate: Date, endDate: Date): Promise<number> {
  try {
    // Calculate improvement based on resolution times and alert reduction
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodLength);
    const previousEndDate = new Date(startDate.getTime());
    
    const [currentResolved, previousResolved] = await Promise.all([
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: startDate, lte: endDate },
          isResolved: true
        }
      }),
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate },
          isResolved: true
        }
      })
    ]);
    
    const [currentTotal, previousTotal] = await Promise.all([
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: startDate, lte: endDate }
        }
      }),
      prisma.cashAuditAlert.count({
        where: {
          createdAt: { gte: previousStartDate, lte: previousEndDate }
        }
      })
    ]);
    
    const currentResolutionRate = currentTotal > 0 ? (currentResolved / currentTotal) * 100 : 100;
    const previousResolutionRate = previousTotal > 0 ? (previousResolved / previousTotal) * 100 : 100;
    
    return Math.round(currentResolutionRate - previousResolutionRate);
  } catch (error) {
    console.error('[Improvement Trend] Error:', error);
    return 0;
  }
}

function calculateUnusualTransactionPatterns(transactions: any[]): number {
  if (transactions.length === 0) return 0;
  
  // Detect unusual patterns
  let unusualCount = 0;
  
  // Calculate average transaction amount
  const totalAmount = transactions.reduce((sum, t) => sum + Number(t.total), 0);
  const averageAmount = totalAmount / transactions.length;
  
  // Find transactions that are significantly higher than average
  const highValueThreshold = averageAmount * 5; // 5x average
  const highValueTransactions = transactions.filter(t => Number(t.total) > highValueThreshold);
  
  // Find time-based patterns (transactions outside business hours)
  const offHoursTransactions = transactions.filter(t => {
    const hour = new Date(t.createdAt).getHours();
    return hour < 6 || hour > 22; // Before 6 AM or after 10 PM
  });
  
  // Find rapid transaction patterns (multiple transactions in short time)
  const rapidTransactions = transactions.filter((t, index) => {
    if (index === 0) return false;
    const currentTime = new Date(t.createdAt).getTime();
    const previousTime = new Date(transactions[index - 1].createdAt).getTime();
    return (currentTime - previousTime) < 60000; // Less than 1 minute apart
  });
  
  unusualCount = highValueTransactions.length + offHoursTransactions.length + rapidTransactions.length;
  
  return unusualCount;
}

function calculateCustomerSatisfaction(cashAuditAlerts: any[], cashReconciliations: any[]): number {
  // Calculate satisfaction based on resolution efficiency and accuracy
  const totalAlerts = cashAuditAlerts.length;
  const resolvedAlerts = cashAuditAlerts.filter(alert => alert.isResolved).length;
  
  const totalReconciliations = cashReconciliations.length;
  const accurateReconciliations = cashReconciliations.filter(rec => 
    Math.abs(Number(rec.discrepancy)) < 10000 // Less than 10k IDR discrepancy
  ).length;
  
  if (totalAlerts === 0 && totalReconciliations === 0) return 90; // Default good score
  
  // Calculate satisfaction based on resolution rate and accuracy
  const resolutionRate = totalAlerts > 0 ? (resolvedAlerts / totalAlerts) * 100 : 100;
  const accuracyRate = totalReconciliations > 0 ? (accurateReconciliations / totalReconciliations) * 100 : 100;
  
  // Weighted average (60% resolution, 40% accuracy)
  const satisfaction = (resolutionRate * 0.6) + (accuracyRate * 0.4);
  
  return Math.round(Math.max(60, Math.min(100, satisfaction))); // 60-100 range
}

// ===== PERFORMANCE BENCHMARKS HELPER FUNCTIONS =====

function calculateInventoryOpsScore(products: any[], stockAdjustments: any[]): number {
  if (products.length === 0) return 85; // Default score if no products
  
  // Calculate score based on stock health and adjustment efficiency
  let score = 100;
  
  // Deduct points for low stock situations
  const lowStockProducts = products.filter(product => {
    const stock = product.storeStock;
    return stock && Number(stock.quantity) <= Number(stock.minThreshold);
  });
  
  const lowStockPenalty = (lowStockProducts.length / products.length) * 30; // Up to 30 points penalty
  score -= lowStockPenalty;
  
  // Deduct points for excessive adjustments (indicates poor planning)
  const adjustmentsPerProduct = stockAdjustments.length / Math.max(products.length, 1);
  if (adjustmentsPerProduct > 2) score -= 10; // Penalty for too many adjustments
  if (adjustmentsPerProduct > 5) score -= 20; // Higher penalty
  
  // Bonus for good turnover
  const fastMovingCount = products.filter(product => {
    const sales = product.transactionItems || [];
    return sales.length > 0; // Has sales activity
  }).length;
  
  const turnoverBonus = (fastMovingCount / products.length) * 10; // Up to 10 points bonus
  score += turnoverBonus;
  
  return Math.round(Math.max(50, Math.min(100, score))); // 50-100 range
}

function calculateComplianceScore(cashAuditAlerts: any[], cashReconciliations: any[]): number {
  let score = 100;
  
  // Deduct points for unresolved alerts
  const unresolvedAlerts = cashAuditAlerts.filter(alert => !alert.isResolved);
  const alertPenalty = Math.min(unresolvedAlerts.length * 5, 30); // Up to 30 points penalty
  score -= alertPenalty;
  
  // Deduct points for large discrepancies
  const largeDiscrepancies = cashReconciliations.filter(rec => 
    Math.abs(Number(rec.discrepancy)) > 50000 // More than 50k IDR
  );
  const discrepancyPenalty = Math.min(largeDiscrepancies.length * 10, 40); // Up to 40 points penalty
  score -= discrepancyPenalty;
  
  // Deduct points for pending reconciliations
  const pendingReconciliations = cashReconciliations.filter(rec => 
    rec.status === 'PENDING' || rec.resolutionStatus === 'PENDING'
  );
  const pendingPenalty = Math.min(pendingReconciliations.length * 3, 20); // Up to 20 points penalty
  score -= pendingPenalty;
  
  return Math.round(Math.max(60, Math.min(100, score))); // 60-100 range
}

function calculateCostPerTransaction(transactions: any[], stockAdjustments: any[], users: any[]): number {
  if (transactions.length === 0) return 0;
  
  // Estimate operational costs
  const staffCount = users.filter(user => user.role === 'CASHIER' || user.role === 'SUPER_ADMIN').length;
  const dailyStaffCost = staffCount * 100000; // 100k IDR per staff per day (estimated)
  
  // Add system maintenance costs
  const systemCost = 50000; // 50k IDR per day (estimated)
  
  // Add inventory management costs
  const inventoryCost = stockAdjustments.length * 5000; // 5k IDR per adjustment
  
  const totalDailyCost = dailyStaffCost + systemCost + inventoryCost;
  const costPerTransaction = totalDailyCost / transactions.length;
  
  return Math.round(costPerTransaction);
}

async function calculateWeekOverWeekTrend(currentRevenue: number, startDate: Date, endDate: Date): Promise<number> {
  try {
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodLength);
    const previousEndDate = new Date(startDate.getTime());
    
    const previousTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: { gte: previousStartDate, lte: previousEndDate },
        status: 'COMPLETED'
      },
      select: { total: true }
    });
    
    const previousRevenue = previousTransactions.reduce((sum, t) => sum + Number(t.total), 0);
    
    if (previousRevenue === 0) return currentRevenue > 0 ? 100 : 0;
    return Math.round(((currentRevenue - previousRevenue) / previousRevenue) * 100);
  } catch (error) {
    console.error('[Week over Week] Error:', error);
    return 0;
  }
}

async function calculateMonthOverMonthTrend(currentRevenue: number, startDate: Date, endDate: Date): Promise<number> {
  try {
    // Calculate month-over-month by going back 30 days from start date
    const monthAgoStart = new Date(startDate.getTime() - (30 * 24 * 60 * 60 * 1000));
    const monthAgoEnd = new Date(endDate.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    const previousMonthTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: { gte: monthAgoStart, lte: monthAgoEnd },
        status: 'COMPLETED'
      },
      select: { total: true }
    });
    
    const previousMonthRevenue = previousMonthTransactions.reduce((sum, t) => sum + Number(t.total), 0);
    
    if (previousMonthRevenue === 0) return currentRevenue > 0 ? 100 : 0;
    return Math.round(((currentRevenue - previousMonthRevenue) / previousMonthRevenue) * 100);
  } catch (error) {
    console.error('[Month over Month] Error:', error);
    return 0;
  }
}

async function calculateYearOverYearTrend(currentRevenue: number, startDate: Date, endDate: Date): Promise<number> {
  try {
    // Calculate year-over-year by going back 365 days
    const yearAgoStart = new Date(startDate.getTime() - (365 * 24 * 60 * 60 * 1000));
    const yearAgoEnd = new Date(endDate.getTime() - (365 * 24 * 60 * 60 * 1000));
    
    const previousYearTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: { gte: yearAgoStart, lte: yearAgoEnd },
        status: 'COMPLETED'
      },
      select: { total: true }
    });
    
    const previousYearRevenue = previousYearTransactions.reduce((sum, t) => sum + Number(t.total), 0);
    
    if (previousYearRevenue === 0) return currentRevenue > 0 ? 100 : 0;
    return Math.round(((currentRevenue - previousYearRevenue) / previousYearRevenue) * 100);
  } catch (error) {
    console.error('[Year over Year] Error:', error);
    return 0;
  }
}

// Helper function to calculate real profit margin based on actual product costs
async function calculateRealProfitMargin(transactions: any[], startDate: Date, endDate: Date): Promise<number> {
  try {
    // Fetch transaction items with product cost information
    const transactionItems = await prisma.transactionItem.findMany({
      where: {
        transaction: {
          createdAt: { gte: startDate, lte: endDate },
          status: 'COMPLETED'
        }
      },
      include: {
        product: {
          select: {
            costPrice: true
          }
        }
      }
    });

    let totalRevenue = 0;
    let totalCost = 0;

    transactionItems.forEach(item => {
      const unitPrice = Number(item.unitPrice || 0);
      const quantity = Number(item.quantity || 0);
      const costPrice = Number(item.product?.costPrice || 0);
      
      const itemRevenue = unitPrice * quantity;
      const itemCost = costPrice * quantity;
      
      totalRevenue += itemRevenue;
      totalCost += itemCost;
    });

    if (totalRevenue === 0) return 0;
    
    const profit = totalRevenue - totalCost;
    const profitMarginRatio = Math.round((profit / totalRevenue) * 100);
    
    console.log('[Profit Margin] Total Revenue:', totalRevenue, 'Total Cost:', totalCost, 'Profit:', profit, 'Margin:', profitMarginRatio + '%');
    
    return Math.max(0, profitMarginRatio); // Ensure non-negative
  } catch (error) {
    console.error('[Profit Margin] Error calculating real profit margin:', error);
    // Fallback to a conservative estimate if calculation fails
    return 25; // 25% fallback margin
  }
}
