import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { DemandForecastingEngine } from "@/lib/demand-forecasting";

/**
 * GET /api/analytics/demand-forecasting
 * Generate demand forecasts for suppliers
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || "Unauthorized" }, { status: auth.status || 401 });
    }

    // Check role permissions
    const allowedRoles = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get("supplierId");
    const forecastPeriod = searchParams.get("forecastPeriod") as '30days' | '60days' | '90days' || '60days';

    if (!supplierId) {
      return NextResponse.json({ error: "Supplier ID is required" }, { status: 400 });
    }

    // Validate forecast period
    const validPeriods = ['30days', '60days', '90days'];
    if (!validPeriods.includes(forecastPeriod)) {
      return NextResponse.json({ 
        error: "Invalid forecast period. Must be one of: 30days, 60days, 90days" 
      }, { status: 400 });
    }

    console.log(`[DemandForecasting] Generating forecast for supplier ${supplierId}, period: ${forecastPeriod}`);

    // Generate demand forecast
    const forecast = await DemandForecastingEngine.generateSupplierDemandForecast(
      supplierId,
      forecastPeriod
    );

    console.log(`[DemandForecasting] Generated ${forecast.forecasts.length} product forecasts`);

    return NextResponse.json({
      success: true,
      data: forecast,
      metadata: {
        generatedAt: new Date().toISOString(),
        forecastPeriod,
        supplierId,
        totalProducts: forecast.totalProducts,
        averageConfidence: forecast.aggregateMetrics.averageConfidenceLevel
      }
    });

  } catch (error) {
    console.error("[DemandForecasting] Error generating forecast:", error);
    
    return NextResponse.json({
      error: "Failed to generate demand forecast",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}

/**
 * POST /api/analytics/demand-forecasting
 * Generate demand forecasts for multiple suppliers
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || "Unauthorized" }, { status: auth.status || 401 });
    }

    // Check role permissions
    const allowedRoles = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const { supplierIds, forecastPeriod = '60days' } = body;

    if (!supplierIds || !Array.isArray(supplierIds) || supplierIds.length === 0) {
      return NextResponse.json({ 
        error: "Supplier IDs array is required and must not be empty" 
      }, { status: 400 });
    }

    if (supplierIds.length > 10) {
      return NextResponse.json({ 
        error: "Maximum 10 suppliers allowed per request" 
      }, { status: 400 });
    }

    // Validate forecast period
    const validPeriods = ['30days', '60days', '90days'];
    if (!validPeriods.includes(forecastPeriod)) {
      return NextResponse.json({ 
        error: "Invalid forecast period. Must be one of: 30days, 60days, 90days" 
      }, { status: 400 });
    }

    console.log(`[DemandForecasting] Generating forecasts for ${supplierIds.length} suppliers`);

    // Generate forecasts for all suppliers
    const forecasts = await Promise.allSettled(
      supplierIds.map(supplierId => 
        DemandForecastingEngine.generateSupplierDemandForecast(supplierId, forecastPeriod)
      )
    );

    // Process results
    const successfulForecasts = [];
    const failedForecasts = [];

    forecasts.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        successfulForecasts.push(result.value);
      } else {
        failedForecasts.push({
          supplierId: supplierIds[index],
          error: result.reason?.message || 'Unknown error'
        });
      }
    });

    console.log(`[DemandForecasting] Generated ${successfulForecasts.length} successful forecasts, ${failedForecasts.length} failed`);

    // Calculate aggregate statistics
    const aggregateStats = {
      totalSuppliers: supplierIds.length,
      successfulForecasts: successfulForecasts.length,
      failedForecasts: failedForecasts.length,
      totalProducts: successfulForecasts.reduce((sum, f) => sum + f.totalProducts, 0),
      totalPredictedDemand: successfulForecasts.reduce((sum, f) => sum + f.aggregateMetrics.totalPredictedDemand, 0),
      totalRecommendedOrderValue: successfulForecasts.reduce((sum, f) => sum + f.aggregateMetrics.totalRecommendedOrderValue, 0),
      averageConfidenceLevel: successfulForecasts.length > 0 ? 
        successfulForecasts.reduce((sum, f) => sum + f.aggregateMetrics.averageConfidenceLevel, 0) / successfulForecasts.length : 0
    };

    return NextResponse.json({
      success: true,
      data: {
        forecasts: successfulForecasts,
        failures: failedForecasts,
        aggregateStats
      },
      metadata: {
        generatedAt: new Date().toISOString(),
        forecastPeriod,
        requestedSuppliers: supplierIds.length,
        successRate: (successfulForecasts.length / supplierIds.length) * 100
      }
    });

  } catch (error) {
    console.error("[DemandForecasting] Error generating bulk forecasts:", error);
    
    return NextResponse.json({
      error: "Failed to generate demand forecasts",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
