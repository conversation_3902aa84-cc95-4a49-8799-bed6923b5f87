import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToProductPerformance } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JW<PERSON> token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Top Products API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Top Products API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const limit = parseInt(searchParams.get("limit") || "10");
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const paymentMethods = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[TopProductsAPI] Query params:", {
      dateRange,
      limit,
      categoryIds,
      cashierIds,
      terminalIds,
      paymentMethods,
      customFromDate,
      customToDate
    });

    // Calculate date range - prioritize custom dates
    let fromDate: Date;
    let toDate: Date;

    if (customFromDate && customToDate) {
      fromDate = startOfDay(new Date(customFromDate));
      toDate = endOfDay(new Date(customToDate));
      console.log("[TopProductsAPI] Using custom date range:", { fromDate, toDate });
    } else {
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[TopProductsAPI] Using preset date range:", { dateRange, fromDate, toDate });
    }

    // Build where clause for transaction items
    const whereClause: any = {
      transaction: {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
        status: {
          not: "VOIDED",
        },
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.transaction.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.transaction.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethods && paymentMethods.length > 0) {
      whereClause.transaction.paymentMethod = {
        in: paymentMethods,
      };
    }

    // Add category filter if specified
    if (categoryIds && categoryIds.length > 0) {
      whereClause.product = {
        categoryId: {
          in: categoryIds,
        },
      };
    }

    // For cashiers, only show their own data
    if (userRole === "CASHIER") {
      whereClause.transaction.cashierId = userId;
    }

    console.log("[TopProductsAPI] Where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch transaction items with product and category information
    const transactionItems = await prisma.transactionItem.findMany({
      where: whereClause,
      include: {
        product: {
          include: {
            category: true,
          },
        },
        transaction: {
          select: {
            status: true,
            createdAt: true,
          },
        },
      },
    });

    console.log("[TopProductsAPI] Found", transactionItems.length, "transaction items");

    // Fetch all products for the transformer
    const products = await prisma.product.findMany({
      include: {
        category: true,
      },
    });

    console.log("[TopProductsAPI] Found", products.length, "products");

    // Transform data for chart
    const productPerformance = transformToProductPerformance(transactionItems, products);

    console.log("[TopProductsAPI] Transformed to", productPerformance.length, "product performance items");

    // Limit results
    const limitedResults = productPerformance.slice(0, limit);

    console.log("[TopProductsAPI] Returning", limitedResults.length, "limited results");

    return NextResponse.json({
      data: limitedResults,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching top products:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch top products",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
