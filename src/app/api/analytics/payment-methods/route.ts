import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { transformToPaymentMethods } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    // Verify authentication using JWT token from cookies
    const authResult = await verifyAuthToken(request);

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Payment Methods API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Payment Methods API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethodsFilter = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[PaymentMethodsAPI] Query params:", {
      dateRange,
      cashierIds,
      terminalIds,
      categoryIds,
      paymentMethodsFilter,
      customFromDate,
      customToDate
    });

    // Calculate date range - prioritize custom dates
    let fromDate: Date;
    let toDate: Date;

    if (customFromDate && customToDate) {
      fromDate = startOfDay(new Date(customFromDate));
      toDate = endOfDay(new Date(customToDate));
      console.log("[PaymentMethodsAPI] Using custom date range:", { fromDate, toDate });
    } else {
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[PaymentMethodsAPI] Using preset date range:", { dateRange, fromDate, toDate });
    }

    // Build where clause
    const whereClause: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethodsFilter && paymentMethodsFilter.length > 0) {
      whereClause.paymentMethod = {
        in: paymentMethodsFilter,
      };
    }

    // Add category filter if specified (requires joining with transaction items)
    if (categoryIds && categoryIds.length > 0) {
      whereClause.items = {
        some: {
          product: {
            categoryId: {
              in: categoryIds,
            },
          },
        },
      };
    }

    // For cashiers, only show their own data
    if (userRole === "CASHIER") {
      whereClause.cashierId = userId;
    }

    console.log("[PaymentMethodsAPI] Where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch transactions with payment method information
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      select: {
        id: true,
        total: true,
        paymentMethod: true,
        status: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    // Transform data for chart
    const paymentMethodsData = transformToPaymentMethods(transactions);

    return NextResponse.json({
      data: paymentMethodsData,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching payment methods:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch payment methods",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
