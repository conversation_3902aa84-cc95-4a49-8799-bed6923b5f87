import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { format, subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    console.log("[Daily Revenue Profit API] Starting data fetch...");

    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      console.log("[Daily Revenue Profit API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethodsFilter = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[DailyRevenueProfitAPI] Query params:", {
      dateRange,
      cashierIds,
      terminalIds,
      categoryIds,
      paymentMethodsFilter,
      customFromDate,
      customToDate
    });

    // Calculate date range - prioritize custom dates
    let fromDate: Date;
    let toDate: Date;

    if (customFromDate && customToDate) {
      fromDate = startOfDay(new Date(customFromDate));
      toDate = endOfDay(new Date(customToDate));
      console.log("[DailyRevenueProfitAPI] Using custom date range:", { fromDate, toDate });
    } else {
      toDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          fromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          fromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          fromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          fromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          fromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[DailyRevenueProfitAPI] Using preset date range:", { dateRange, fromDate, toDate });
    }

    // Build where clause
    const whereClause: any = {
      createdAt: {
        gte: fromDate,
        lte: toDate,
      },
      status: {
        not: "VOIDED",
      },
      paymentStatus: "PAID", // Only count paid transactions
    };

    // Add cashier filter if specified
    if (cashierIds && cashierIds.length > 0) {
      whereClause.cashierId = {
        in: cashierIds,
      };
    }

    // Add terminal filter if specified
    if (terminalIds && terminalIds.length > 0) {
      whereClause.OR = [
        // Direct terminal association
        {
          terminalId: {
            in: terminalIds,
          },
        },
        // Indirect terminal association through drawer session
        {
          drawerSession: {
            terminalId: {
              in: terminalIds,
            },
          },
        },
      ];
    }

    // Add payment method filter if specified
    if (paymentMethodsFilter && paymentMethodsFilter.length > 0) {
      whereClause.paymentMethod = {
        in: paymentMethodsFilter,
      };
    }

    // Add category filter if specified (requires joining with transaction items)
    if (categoryIds && categoryIds.length > 0) {
      whereClause.items = {
        some: {
          product: {
            categoryId: {
              in: categoryIds,
            },
          },
        },
      };
    }

    console.log("[DailyRevenueProfitAPI] Where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch transactions with items for revenue and profit calculation
    const transactions = await prisma.transaction.findMany({
      where: whereClause,
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                purchasePrice: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "asc",
      },
    });

    console.log("[DailyRevenueProfitAPI] Found transactions:", transactions.length);

    // Group transactions by date and calculate revenue/profit
    const dailyData = new Map<string, { revenue: number; profit: number }>();

    transactions.forEach((transaction) => {
      const date = format(transaction.createdAt, "yyyy-MM-dd");
      const existing = dailyData.get(date) || { revenue: 0, profit: 0 };
      
      // Calculate revenue from transaction items
      let transactionRevenue = 0;
      let transactionProfit = 0;
      
      transaction.items.forEach((item) => {
        const itemRevenue = Number(item.unitPrice) * Number(item.quantity);
        transactionRevenue += itemRevenue;
        
        // Calculate profit (revenue - cost)
        const purchasePrice = Number(item.product.purchasePrice || 0);
        const itemCost = purchasePrice * Number(item.quantity);
        const itemProfit = itemRevenue - itemCost;
        transactionProfit += itemProfit;
      });
      
      dailyData.set(date, {
        revenue: existing.revenue + transactionRevenue,
        profit: existing.profit + transactionProfit,
      });
    });

    // Convert to array and fill missing dates with 0
    const result = [];
    const currentDate = new Date(fromDate);
    
    while (currentDate <= toDate) {
      const dateStr = format(currentDate, "yyyy-MM-dd");
      const data = dailyData.get(dateStr) || { revenue: 0, profit: 0 };
      result.push({
        date: dateStr,
        revenue: Math.round(data.revenue * 100) / 100, // Round to 2 decimal places
        profit: Math.round(data.profit * 100) / 100,
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    console.log("[DailyRevenueProfitAPI] Returning data points:", result.length);

    return NextResponse.json({
      data: result,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching daily revenue profit:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch daily revenue profit data",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}