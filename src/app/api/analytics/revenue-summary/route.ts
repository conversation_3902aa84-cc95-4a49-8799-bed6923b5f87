import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { calculateRevenueSummary } from "@/lib/analytics/dataTransformers";
import { subDays, startOfDay, endOfDay } from "date-fns";

export async function GET(request: NextRequest) {
  try {
    console.log("[Revenue Summary API] Starting request processing");

    // Verify authentication using JWT token from cookies
    const authResult = await verifyAuthToken(request);

    console.log("[Revenue Summary API] Auth result:", authResult.authenticated ? {
      id: authResult.user?.id,
      role: authResult.user?.role,
      email: authResult.user?.email
    } : 'Authentication failed');

    if (!authResult.authenticated || !authResult.user) {
      console.log("[Revenue Summary API] Authentication failed:", authResult.error);
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { user } = authResult;
    const userRole = user.role;
    const userId = user.id;

    // Check if user has analytics access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(userRole);
    if (!hasAccess) {
      console.log("[Revenue Summary API] User does not have access:", userRole);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateRange = searchParams.get("dateRange") || "30d";
    const cashierIds = searchParams.get("cashierIds")?.split(",").filter(Boolean);
    const terminalIds = searchParams.get("terminalIds")?.split(",").filter(Boolean);
    const categoryIds = searchParams.get("categoryIds")?.split(",").filter(Boolean);
    const paymentMethods = searchParams.get("paymentMethods")?.split(",").filter(Boolean);

    // Custom date range parameters
    const customFromDate = searchParams.get("fromDate");
    const customToDate = searchParams.get("toDate");

    console.log("[RevenueSummaryAPI] Query params:", {
      dateRange,
      cashierIds,
      terminalIds,
      categoryIds,
      paymentMethods,
      customFromDate,
      customToDate
    });

    // Calculate current period date range
    let currentFromDate: Date;
    let currentToDate: Date;
    let periodDays: number;

    if (customFromDate && customToDate) {
      currentFromDate = startOfDay(new Date(customFromDate));
      currentToDate = endOfDay(new Date(customToDate));
      periodDays = Math.ceil((currentToDate.getTime() - currentFromDate.getTime()) / (1000 * 60 * 60 * 24));
      console.log("[RevenueSummaryAPI] Using custom date range:", { currentFromDate, currentToDate, periodDays });
    } else {
      currentToDate = endOfDay(new Date());
      switch (dateRange) {
        case "7d":
          periodDays = 7;
          currentFromDate = startOfDay(subDays(new Date(), 7));
          break;
        case "30d":
          periodDays = 30;
          currentFromDate = startOfDay(subDays(new Date(), 30));
          break;
        case "90d":
          periodDays = 90;
          currentFromDate = startOfDay(subDays(new Date(), 90));
          break;
        case "1y":
          periodDays = 365;
          currentFromDate = startOfDay(subDays(new Date(), 365));
          break;
        default:
          periodDays = 30;
          currentFromDate = startOfDay(subDays(new Date(), 30));
      }
      console.log("[RevenueSummaryAPI] Using preset date range:", { dateRange, currentFromDate, currentToDate, periodDays });
    }

    // Calculate previous period for comparison
    const previousFromDate = startOfDay(subDays(currentFromDate, periodDays));
    const previousToDate = endOfDay(subDays(currentFromDate, 1));

    // Build base where clause
    const buildWhereClause = (fromDate: Date, toDate: Date) => {
      const whereClause: any = {
        createdAt: {
          gte: fromDate,
          lte: toDate,
        },
        status: {
          not: "VOIDED",
        },
      };

      // Add cashier filter if specified
      if (cashierIds && cashierIds.length > 0) {
        whereClause.cashierId = {
          in: cashierIds,
        };
      }

      // Add terminal filter if specified
      if (terminalIds && terminalIds.length > 0) {
        whereClause.OR = [
          // Direct terminal association
          {
            terminalId: {
              in: terminalIds,
            },
          },
          // Indirect terminal association through drawer session
          {
            drawerSession: {
              terminalId: {
                in: terminalIds,
              },
            },
          },
        ];
      }

      // Add payment method filter if specified
      if (paymentMethods && paymentMethods.length > 0) {
        whereClause.paymentMethod = {
          in: paymentMethods,
        };
      }

      // Add category filter if specified (requires joining with transaction items)
      if (categoryIds && categoryIds.length > 0) {
        whereClause.items = {
          some: {
            product: {
              categoryId: {
                in: categoryIds,
              },
            },
          },
        };
      }

      // For cashiers, only show their own data
      if (userRole === "CASHIER") {
        whereClause.cashierId = userId;
      }

      return whereClause;
    };

    // Fetch current period transactions
    const currentTransactions = await prisma.transaction.findMany({
      where: buildWhereClause(currentFromDate, currentToDate),
      select: {
        id: true,
        total: true,
        createdAt: true,
        status: true,
        customerId: true,
      },
    });

    // Fetch previous period transactions for comparison
    const previousTransactions = await prisma.transaction.findMany({
      where: buildWhereClause(previousFromDate, previousToDate),
      select: {
        id: true,
        total: true,
        createdAt: true,
        status: true,
        customerId: true,
      },
    });

    // Calculate revenue summary with growth metrics
    const revenueSummary = calculateRevenueSummary(currentTransactions, previousTransactions);

    return NextResponse.json({
      data: revenueSummary,
      success: true,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error fetching revenue summary:", error);
    return NextResponse.json(
      {
        error: "Failed to fetch revenue summary",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
