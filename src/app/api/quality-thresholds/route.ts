import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Quality threshold configuration schema
const qualityThresholdSchema = z.object({
  returnRateThreshold: z.number().min(0).max(100).default(5), // Percentage
  defectRateThreshold: z.number().min(0).max(100).default(2), // Percentage
  qualityScoreThreshold: z.number().min(0).max(100).default(70), // Score 0-100
  returnValueThreshold: z.number().min(0).default(1000000), // IDR
  escalationEnabled: z.boolean().default(true),
  autoEscalationLevel1Days: z.number().min(1).default(3), // Days before level 1 escalation
  autoEscalationLevel2Days: z.number().min(1).default(7), // Days before level 2 escalation
  notificationEnabled: z.boolean().default(true),
  monitoringPeriodDays: z.number().min(1).default(30), // Days to look back for threshold checks
});

// Default quality thresholds
const DEFAULT_THRESHOLDS = {
  returnRateThreshold: 5,
  defectRateThreshold: 2,
  qualityScoreThreshold: 70,
  returnValueThreshold: 1000000,
  escalationEnabled: true,
  autoEscalationLevel1Days: 3,
  autoEscalationLevel2Days: 7,
  notificationEnabled: true,
  monitoringPeriodDays: 30,
};

// GET /api/quality-thresholds - Get quality threshold configuration
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get quality threshold settings from system settings
    const thresholdSettings = await prisma.systemSetting.findMany({
      where: {
        key: {
          in: [
            'quality_return_rate_threshold',
            'quality_defect_rate_threshold',
            'quality_score_threshold',
            'quality_return_value_threshold',
            'quality_escalation_enabled',
            'quality_auto_escalation_level1_days',
            'quality_auto_escalation_level2_days',
            'quality_notification_enabled',
            'quality_monitoring_period_days',
          ],
        },
      },
    });

    // Convert settings to object with defaults
    const thresholds = { ...DEFAULT_THRESHOLDS };
    for (const setting of thresholdSettings) {
      switch (setting.key) {
        case 'quality_return_rate_threshold':
          thresholds.returnRateThreshold = parseFloat(setting.value);
          break;
        case 'quality_defect_rate_threshold':
          thresholds.defectRateThreshold = parseFloat(setting.value);
          break;
        case 'quality_score_threshold':
          thresholds.qualityScoreThreshold = parseFloat(setting.value);
          break;
        case 'quality_return_value_threshold':
          thresholds.returnValueThreshold = parseFloat(setting.value);
          break;
        case 'quality_escalation_enabled':
          thresholds.escalationEnabled = setting.value === 'true';
          break;
        case 'quality_auto_escalation_level1_days':
          thresholds.autoEscalationLevel1Days = parseInt(setting.value);
          break;
        case 'quality_auto_escalation_level2_days':
          thresholds.autoEscalationLevel2Days = parseInt(setting.value);
          break;
        case 'quality_notification_enabled':
          thresholds.notificationEnabled = setting.value === 'true';
          break;
        case 'quality_monitoring_period_days':
          thresholds.monitoringPeriodDays = parseInt(setting.value);
          break;
      }
    }

    return NextResponse.json({
      thresholds,
      lastUpdated: thresholdSettings.length > 0 
        ? Math.max(...thresholdSettings.map(s => new Date(s.updatedAt).getTime()))
        : null,
    });
  } catch (error) {
    console.error('Error fetching quality thresholds:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/quality-thresholds - Update quality threshold configuration
export async function PUT(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only SUPER_ADMIN can update thresholds
    if (auth.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Only SUPER_ADMIN can update quality thresholds' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = qualityThresholdSchema.parse(body);

    // Update system settings
    const settingsToUpdate = [
      { key: 'quality_return_rate_threshold', value: validatedData.returnRateThreshold.toString() },
      { key: 'quality_defect_rate_threshold', value: validatedData.defectRateThreshold.toString() },
      { key: 'quality_score_threshold', value: validatedData.qualityScoreThreshold.toString() },
      { key: 'quality_return_value_threshold', value: validatedData.returnValueThreshold.toString() },
      { key: 'quality_escalation_enabled', value: validatedData.escalationEnabled.toString() },
      { key: 'quality_auto_escalation_level1_days', value: validatedData.autoEscalationLevel1Days.toString() },
      { key: 'quality_auto_escalation_level2_days', value: validatedData.autoEscalationLevel2Days.toString() },
      { key: 'quality_notification_enabled', value: validatedData.notificationEnabled.toString() },
      { key: 'quality_monitoring_period_days', value: validatedData.monitoringPeriodDays.toString() },
    ];

    // Use transaction to update all settings
    await prisma.$transaction(async (tx) => {
      for (const setting of settingsToUpdate) {
        await tx.systemSetting.upsert({
          where: { key: setting.key },
          update: { 
            value: setting.value,
            updatedAt: new Date(),
          },
          create: {
            key: setting.key,
            value: setting.value,
            description: getThresholdDescription(setting.key),
          },
        });
      }
    });

    return NextResponse.json({
      message: 'Quality thresholds updated successfully',
      thresholds: validatedData,
      updatedBy: auth.user.name,
      updatedAt: new Date(),
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating quality thresholds:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

function getThresholdDescription(key: string): string {
  const descriptions: Record<string, string> = {
    'quality_return_rate_threshold': 'Return rate threshold percentage for quality alerts',
    'quality_defect_rate_threshold': 'Defect rate threshold percentage for quality alerts',
    'quality_score_threshold': 'Minimum quality score threshold for supplier performance',
    'quality_return_value_threshold': 'Return value threshold in IDR for escalation',
    'quality_escalation_enabled': 'Enable automatic quality issue escalation',
    'quality_auto_escalation_level1_days': 'Days before level 1 automatic escalation',
    'quality_auto_escalation_level2_days': 'Days before level 2 automatic escalation',
    'quality_notification_enabled': 'Enable quality threshold breach notifications',
    'quality_monitoring_period_days': 'Number of days to look back for quality monitoring',
  };
  return descriptions[key] || 'Quality threshold configuration setting';
}
