import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { notify } from '@/lib/notifications';
import { Decimal } from '@prisma/client/runtime/library';

// Helper function to calculate maximum safe quantity based on unit price
function calculateMaxSafeQuantity(unitPrice: number): number {
  const maxDecimalValue = 99999999.99; // Database constraint @db.Decimal(10, 2)
  return Math.floor(maxDecimalValue / unitPrice);
}

// Schema for creating PO from suggestion
const createPOFromSuggestionSchema = z.object({
  suggestionId: z.string().min(1, { message: "Suggestion ID is required" }),
  adjustedQuantity: z.number().positive().optional(),
  notes: z.string().optional(),
  expectedDeliveryDate: z.string().optional(),
});

// Schema for bulk PO creation
const bulkCreatePOSchema = z.object({
  suggestions: z.array(z.object({
    suggestionId: z.string().min(1),
    adjustedQuantity: z.number().positive().optional(),
    notes: z.string().optional(),
  })).min(1, { message: "At least one suggestion is required" }),
  groupBySupplierId: z.boolean().optional().default(true),
  expectedDeliveryDate: z.string().optional(),
});

/**
 * POST /api/po-suggestions/create-po
 * Create purchase order(s) from PO suggestions
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse request body
    const body = await request.json();
    
    // Determine if this is single or bulk creation
    const isBulk = Array.isArray(body.suggestions);
    
    if (isBulk) {
      return await handleBulkPOCreation(body, auth.user);
    } else {
      return await handleSinglePOCreation(body, auth.user);
    }
  } catch (error) {
    console.error("Error creating PO from suggestion:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create PO from suggestion", message: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Handle single PO creation from suggestion
 */
async function handleSinglePOCreation(body: any, user: any) {
  try {
    console.log('🔄 Starting single PO creation with body:', JSON.stringify(body, null, 2));
    console.log('👤 User:', { id: user.id, name: user.name, role: user.role });

    const validatedData = createPOFromSuggestionSchema.parse(body);
    console.log('✅ Validated data:', JSON.stringify(validatedData, null, 2));

  // Get the suggestion data from the auto PO generation engine
  // For now, we'll need to reconstruct the suggestion data
  // In a real implementation, you'd store suggestions in the database

  // Mock suggestion data - in reality, this would come from the suggestion ID
  const mockSuggestion = {
    productId: 'product-id-placeholder',
    supplierId: 'supplier-id-placeholder',
    productSupplierId: 'product-supplier-id-placeholder',
    quantity: validatedData.adjustedQuantity || 10,
    unitPrice: 50000, // IDR
  };

  // Verify supplier exists
  const supplier = await prisma.supplier.findFirst({
    where: { isActive: true },
    include: {
      productSuppliers: {
        where: { isActive: true },
        include: { product: true }
      }
    }
  });

  if (!supplier) {
    return NextResponse.json({ error: 'No active suppliers found' }, { status: 404 });
  }

  // Get a product from this supplier
  const productSupplier = supplier.productSuppliers[0];
  if (!productSupplier) {
    return NextResponse.json({ error: 'No products found for supplier' }, { status: 404 });
  }

    const requestedQuantity = validatedData.adjustedQuantity || 10;
    const unitPrice = Number(productSupplier.purchasePrice || 50000);

    console.log(`💰 Single PO Request: Quantity: ${requestedQuantity}, Unit Price: ${unitPrice.toLocaleString()} IDR`);

    // CRITICAL: Validate quantity against unit price to prevent database constraint violations
    const maxSafeQuantity = calculateMaxSafeQuantity(unitPrice);
    console.log(`🔒 Maximum safe quantity for this unit price: ${maxSafeQuantity.toLocaleString()}`);

    if (requestedQuantity > maxSafeQuantity) {
      throw new Error(`Requested quantity ${requestedQuantity.toLocaleString()} exceeds maximum safe quantity ${maxSafeQuantity.toLocaleString()} for unit price ${unitPrice.toLocaleString()} IDR. Total cost would exceed database limit of 99,999,999.99 IDR.`);
    }

    const quantity = requestedQuantity; // Use requested quantity if it passes validation
    const subtotal = quantity * unitPrice;
    const tax = 0;
    const total = subtotal + tax;

    console.log(`💰 Single PO Totals: Quantity: ${quantity}, Unit Price: ${unitPrice.toLocaleString()}, Subtotal: ${subtotal.toLocaleString()}, Total: ${total.toLocaleString()}`);

    // Validate decimal constraints (10 digits total, 2 decimal places) - should not fail now
    if (quantity > 99999999.99) {
      throw new Error(`Quantity ${quantity} exceeds maximum allowed value (99,999,999.99)`);
    }
    if (unitPrice > 99999999.99) {
      throw new Error(`Unit price ${unitPrice} exceeds maximum allowed value (99,999,999.99)`);
    }
    if (subtotal > 99999999.99) {
      throw new Error(`Subtotal ${subtotal} exceeds maximum allowed value (99,999,999.99)`);
    }
    if (total > 99999999.99) {
      throw new Error(`Total ${total} exceeds maximum allowed value (99,999,999.99)`);
    }

    // Create the purchase order with proper Decimal conversion
    const purchaseOrder = await prisma.purchaseOrder.create({
      data: {
        supplierId: supplier.id,
        orderDate: new Date(),
        subtotal: new Decimal(subtotal.toFixed(2)),
        tax: new Decimal(tax.toFixed(2)),
        total: new Decimal(total.toFixed(2)),
        notes: validatedData.notes || `Created from PO suggestion: ${validatedData.suggestionId}`,
        expectedDeliveryDate: validatedData.expectedDeliveryDate ? new Date(validatedData.expectedDeliveryDate) : null,
        status: 'DRAFT',
        createdById: user.id,
        items: {
          create: [{
            productId: productSupplier.productId,
            productSupplierId: productSupplier.id,
            quantity: new Decimal(quantity.toFixed(2)),
            unitPrice: new Decimal(unitPrice.toFixed(2)),
            subtotal: new Decimal(subtotal.toFixed(2)),
          }]
        },
      },
    include: {
      supplier: true,
      createdBy: {
        select: { id: true, name: true, email: true },
      },
      items: {
        include: {
          product: {
            include: {
              category: true,
              unit: true,
            },
          },
        },
      },
    },
  });

  // Log activity
  await prisma.activityLog.create({
    data: {
      userId: user.id,
      action: 'CREATE_PURCHASE_ORDER',
      details: `Created purchase order from PO suggestion for supplier: ${supplier.name}`,
    },
  });

  // Send notification
  await notify({
    eventType: 'po.created.from_suggestion',
    sourceId: purchaseOrder.id,
    sourceType: 'purchase_order',
    payload: {
      poNumber: `PO-${purchaseOrder.id.slice(-6)}`,
      suggestionId: validatedData.suggestionId,
      createdBy: user.name,
      total: Number(purchaseOrder.total),
    },
  });

    return NextResponse.json({
      success: true,
      purchaseOrder,
      message: 'Purchase order created successfully from suggestion',
    });

  } catch (error) {
    console.error('❌ Error in single PO creation:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      error: 'Failed to create purchase order from suggestion',
      message: errorMessage,
      timestamp: new Date().toISOString(),
      user: { id: user.id, name: user.name },
      requestBody: body,
    };

    console.error('Error details:', JSON.stringify(errorDetails, null, 2));

    return NextResponse.json(errorDetails, { status: 500 });
  }
}

/**
 * Handle bulk PO creation from multiple suggestions
 */
async function handleBulkPOCreation(body: any, user: any) {
  try {
    console.log('🔄 Starting bulk PO creation with body:', JSON.stringify(body, null, 2));
    console.log('👤 User:', { id: user.id, name: user.name, role: user.role });

    const validatedData = bulkCreatePOSchema.parse(body);
    console.log('✅ Validated data:', JSON.stringify(validatedData, null, 2));

    // Additional validation
    if (!validatedData.suggestions || validatedData.suggestions.length === 0) {
      throw new Error('No suggestions provided for bulk PO creation');
    }

    // Validate each suggestion has required fields
    validatedData.suggestions.forEach((suggestion, index) => {
      if (!suggestion.suggestionId) {
        throw new Error(`Suggestion ${index + 1} is missing suggestionId`);
      }
      if (suggestion.adjustedQuantity !== undefined && suggestion.adjustedQuantity <= 0) {
        throw new Error(`Suggestion ${index + 1} has invalid adjustedQuantity: ${suggestion.adjustedQuantity}`);
      }
    });

    console.log(`📋 Processing ${validatedData.suggestions.length} suggestions for bulk PO creation`);

    // Get active suppliers with their products
    const suppliers = await prisma.supplier.findMany({
      where: { isActive: true },
      include: {
        productSuppliers: {
          where: { isActive: true },
          include: { product: true }
        }
      }
    });

    console.log(`📦 Found ${suppliers.length} active suppliers`);
    suppliers.forEach((supplier, index) => {
      console.log(`  ${index + 1}. ${supplier.name} (${supplier.productSuppliers.length} products)`);
    });

    if (suppliers.length === 0) {
      console.log('❌ No active suppliers found');
      return NextResponse.json({ error: 'No active suppliers found' }, { status: 404 });
    }

    // Group suggestions by supplier if requested
    const suggestionGroups = validatedData.groupBySupplierId
      ? groupSuggestionsBySupplier(validatedData.suggestions, suppliers)
      : [{ supplier: suppliers[0], suggestions: validatedData.suggestions }];

    console.log(`🔗 Created ${suggestionGroups.length} supplier groups`);

    const createdPOs = [];

    for (const [groupIndex, group] of suggestionGroups.entries()) {
      console.log(`\n📋 Processing group ${groupIndex + 1}: ${group.supplier.name}`);
      console.log(`   Products available: ${group.supplier.productSuppliers.length}`);
      console.log(`   Suggestions: ${group.suggestions.length}`);

      if (!group.supplier.productSuppliers.length) {
        console.log(`⚠️  Skipping ${group.supplier.name} - no products available`);
        continue;
      }

      // Calculate totals for this group
      let subtotal = 0;
      const items = [];

      const itemCount = Math.min(group.suggestions.length, group.supplier.productSuppliers.length);
      console.log(`   Creating ${itemCount} items for this PO`);

      for (let i = 0; i < itemCount; i++) {
        const suggestion = group.suggestions[i];
        const productSupplier = group.supplier.productSuppliers[i];

        console.log(`     Item ${i + 1}: ${productSupplier.product.name}`);
        console.log(`       Product ID: ${productSupplier.productId}`);
        console.log(`       ProductSupplier ID: ${productSupplier.id}`);
        console.log(`       Purchase Price: ${productSupplier.purchasePrice}`);

        const requestedQuantity = suggestion.adjustedQuantity || 10;
        const unitPrice = Number(productSupplier.purchasePrice || 50000);

        console.log(`       Product: ${productSupplier.product.name}`);
        console.log(`       Requested Quantity: ${requestedQuantity}, Unit Price: ${unitPrice.toLocaleString()} IDR`);

        // CRITICAL: Validate quantity against unit price to prevent database constraint violations
        const maxSafeQuantity = calculateMaxSafeQuantity(unitPrice);
        console.log(`       🔒 Maximum safe quantity for this unit price: ${maxSafeQuantity.toLocaleString()}`);

        if (requestedQuantity > maxSafeQuantity) {
          throw new Error(`Product "${productSupplier.product.name}": Requested quantity ${requestedQuantity.toLocaleString()} exceeds maximum safe quantity ${maxSafeQuantity.toLocaleString()} for unit price ${unitPrice.toLocaleString()} IDR. Total cost would exceed database limit.`);
        }

        const quantity = requestedQuantity; // Use requested quantity if it passes validation
        const itemSubtotal = quantity * unitPrice;

        console.log(`       ✅ Final Quantity: ${quantity}, Unit Price: ${unitPrice.toLocaleString()}, Subtotal: ${itemSubtotal.toLocaleString()}`);

        // Validate decimal constraints (10 digits total, 2 decimal places) - should not fail now
        if (quantity > 99999999.99) {
          throw new Error(`Quantity ${quantity} exceeds maximum allowed value (99,999,999.99)`);
        }
        if (unitPrice > 99999999.99) {
          throw new Error(`Unit price ${unitPrice} exceeds maximum allowed value (99,999,999.99)`);
        }
        if (itemSubtotal > 99999999.99) {
          throw new Error(`Item subtotal ${itemSubtotal} exceeds maximum allowed value (99,999,999.99)`);
        }

        subtotal += itemSubtotal;
        items.push({
          productId: productSupplier.productId,
          productSupplierId: productSupplier.id,
          quantity: new Decimal(quantity.toFixed(2)),
          unitPrice: new Decimal(unitPrice.toFixed(2)),
          subtotal: new Decimal(itemSubtotal.toFixed(2)),
        });
      }

      const tax = 0;
      const total = subtotal + tax;

      console.log(`   💰 PO Totals: Subtotal: ${subtotal}, Tax: ${tax}, Total: ${total}`);

      // Validate total amounts against database constraints
      if (subtotal > 99999999.99) {
        throw new Error(`Subtotal ${subtotal} exceeds maximum allowed value (99,999,999.99)`);
      }
      if (total > 99999999.99) {
        throw new Error(`Total ${total} exceeds maximum allowed value (99,999,999.99)`);
      }

      // Validate required fields before creation
      if (!group.supplier.id) {
        throw new Error(`Supplier ID is missing for ${group.supplier.name}`);
      }
      if (!user.id) {
        throw new Error('User ID is missing');
      }
      if (items.length === 0) {
        throw new Error('No items to create for PO');
      }

      // Validate each item (fix Decimal comparison)
      items.forEach((item, index) => {
        if (!item.productId) {
          throw new Error(`Product ID missing for item ${index + 1}`);
        }
        if (!item.productSupplierId) {
          throw new Error(`ProductSupplier ID missing for item ${index + 1}`);
        }
        // Convert Decimal to number for comparison
        const quantityNum = Number(item.quantity);
        const unitPriceNum = Number(item.unitPrice);

        if (quantityNum <= 0) {
          throw new Error(`Invalid quantity for item ${index + 1}: ${quantityNum}`);
        }
        if (unitPriceNum <= 0) {
          throw new Error(`Invalid unit price for item ${index + 1}: ${unitPriceNum}`);
        }

        console.log(`       ✅ Item ${index + 1} validation passed: Qty=${quantityNum}, Price=${unitPriceNum}`);
      });

      console.log(`   🔨 Creating PO for ${group.supplier.name}...`);

      // Create PO for this supplier group with proper Decimal conversion
      let purchaseOrder;
      try {
        purchaseOrder = await prisma.purchaseOrder.create({
          data: {
            supplierId: group.supplier.id,
            orderDate: new Date(),
            subtotal: new Decimal(subtotal.toFixed(2)),
            tax: new Decimal(tax.toFixed(2)),
            total: new Decimal(total.toFixed(2)),
            notes: `Bulk PO created from ${group.suggestions.length} suggestions`,
            expectedDeliveryDate: validatedData.expectedDeliveryDate ? new Date(validatedData.expectedDeliveryDate) : null,
            status: 'DRAFT',
            createdById: user.id,
            items: {
              create: items,
            },
          },
          include: {
            supplier: true,
            createdBy: {
              select: { id: true, name: true, email: true },
            },
            items: {
              include: {
                product: {
                  include: {
                    category: true,
                    unit: true,
                  },
                },
              },
            },
          },
        });
      } catch (dbError) {
        console.error(`❌ Database error creating PO for ${group.supplier.name}:`, dbError);
        throw new Error(`Failed to create purchase order for ${group.supplier.name}: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`);
      }

      console.log(`   ✅ Created PO: ${purchaseOrder.id} for ${group.supplier.name}`);
      console.log(`      Items created: ${purchaseOrder.items.length}`);
      console.log(`      Total: ${purchaseOrder.total}`);

      createdPOs.push(purchaseOrder);

      // Log activity (non-blocking)
      try {
        await prisma.activityLog.create({
          data: {
            userId: user.id,
            action: 'CREATE_PURCHASE_ORDER',
            details: `Created bulk purchase order from ${group.suggestions.length} suggestions for supplier: ${group.supplier.name}`,
          },
        });
        console.log(`   📝 Activity logged for PO ${purchaseOrder.id}`);
      } catch (logError) {
        console.warn(`⚠️  Failed to log activity for PO ${purchaseOrder.id}:`, logError);
        // Don't throw - activity logging failure shouldn't break PO creation
      }

      // Send notification for each PO (non-blocking)
      try {
        await notify({
          eventType: 'po.created.bulk_from_suggestions',
          sourceId: purchaseOrder.id,
          sourceType: 'purchase_order',
          payload: {
            poNumber: `PO-${purchaseOrder.id.slice(-6)}`,
            suggestionCount: group.suggestions.length,
            createdBy: user.name,
            total: Number(purchaseOrder.total),
          },
        });
        console.log(`   🔔 Notification sent for PO ${purchaseOrder.id}`);
      } catch (notifyError) {
        console.warn(`⚠️  Failed to send notification for PO ${purchaseOrder.id}:`, notifyError);
        // Don't throw - notification failure shouldn't break PO creation
      }
    }

    console.log(`🎉 Successfully created ${createdPOs.length} purchase orders`);

    return NextResponse.json({
      success: true,
      purchaseOrders: createdPOs,
      message: `${createdPOs.length} purchase order(s) created successfully from ${validatedData.suggestions.length} suggestions`,
      summary: {
        totalPOs: createdPOs.length,
        totalSuggestions: validatedData.suggestions.length,
        groupedBySupplier: validatedData.groupBySupplierId,
      },
    });

  } catch (error) {
    console.error('❌ Error in bulk PO creation:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');

    // Determine error type and provide specific context
    let errorType = 'UNKNOWN_ERROR';
    let statusCode = 500;

    if (error instanceof z.ZodError) {
      errorType = 'VALIDATION_ERROR';
      statusCode = 400;
    } else if (error instanceof Error) {
      if (error.message.includes('No active suppliers')) {
        errorType = 'NO_SUPPLIERS_ERROR';
        statusCode = 404;
      } else if (error.message.includes('Product ID missing') || error.message.includes('ProductSupplier ID missing')) {
        errorType = 'MISSING_PRODUCT_DATA';
        statusCode = 400;
      } else if (error.message.includes('Invalid quantity') || error.message.includes('Invalid unit price')) {
        errorType = 'INVALID_PRICING_DATA';
        statusCode = 400;
      } else if (error.message.includes('exceeds maximum allowed value')) {
        errorType = 'DECIMAL_CONSTRAINT_ERROR';
        statusCode = 400;
      } else if (error.message.includes('exceeds maximum safe quantity')) {
        errorType = 'QUANTITY_PRICE_CONSTRAINT_ERROR';
        statusCode = 400;
      } else if (error.message.includes('Failed to create purchase order')) {
        errorType = 'DATABASE_ERROR';
        statusCode = 500;
      }
    }

    // Return detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    const errorDetails = {
      error: 'Failed to create bulk purchase orders',
      errorType,
      message: errorMessage,
      timestamp: new Date().toISOString(),
      user: { id: user?.id, name: user?.name },
      requestBody: body,
      suggestions: body?.suggestions?.length || 0,
    };

    console.error('Error details:', JSON.stringify(errorDetails, null, 2));

    return NextResponse.json(errorDetails, { status: statusCode });
  }
}

/**
 * Group suggestions by supplier ID
 */
function groupSuggestionsBySupplier(suggestions: any[], suppliers: any[]) {
  // For now, distribute suggestions evenly across suppliers
  // In a real implementation, you'd group by actual supplier from suggestion data
  const groups = [];

  suppliers.forEach((supplier, index) => {
    const supplierSuggestions = suggestions.filter((_, i) => i % suppliers.length === index);
    if (supplierSuggestions.length > 0) {
      groups.push({
        supplier,
        suggestions: supplierSuggestions,
      });
    }
  });

  return groups;
}
