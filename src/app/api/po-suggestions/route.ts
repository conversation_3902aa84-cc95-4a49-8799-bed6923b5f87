import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { AutoPOGenerationEngine } from '@/lib/auto-po-generation-engine';

// Schema for generating PO suggestions
const generateSuggestionsSchema = z.object({
  categoryId: z.string().optional(),
  forceRefresh: z.boolean().optional().default(false),
});

/**
 * GET /api/po-suggestions
 * Get current PO suggestions
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId') || undefined;
    const urgencyFilter = searchParams.get('urgency') as 'critical' | 'high' | 'medium' | 'low' | undefined;
    const limit = parseInt(searchParams.get('limit') || '50', 10);
    const offset = parseInt(searchParams.get('offset') || '0', 10);

    console.log('Generating PO suggestions with filters:', { categoryId, urgencyFilter, limit, offset });

    // Generate PO suggestions
    const result = await AutoPOGenerationEngine.generatePOSuggestions(categoryId);

    // Apply filters
    let filteredSuggestions = result.suggestions;
    
    if (urgencyFilter) {
      filteredSuggestions = filteredSuggestions.filter(s => s.urgencyLevel === urgencyFilter);
    }

    // Apply pagination
    const totalCount = filteredSuggestions.length;
    const paginatedSuggestions = filteredSuggestions.slice(offset, offset + limit);

    return NextResponse.json({
      suggestions: paginatedSuggestions,
      summary: result.summary,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount,
      },
      filters: {
        categoryId,
        urgencyFilter,
      },
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting PO suggestions:", error);
    return NextResponse.json(
      { error: "Failed to get PO suggestions", message: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/po-suggestions
 * Generate new PO suggestions (force refresh)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = generateSuggestionsSchema.parse(body);

    console.log('Force generating PO suggestions:', validatedData);

    // Generate PO suggestions
    const result = await AutoPOGenerationEngine.generatePOSuggestions(validatedData.categoryId);

    return NextResponse.json({
      suggestions: result.suggestions,
      summary: result.summary,
      generatedAt: new Date().toISOString(),
      requestedBy: auth.user.name,
    });
  } catch (error) {
    console.error("Error generating PO suggestions:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to generate PO suggestions", message: (error as Error).message },
      { status: 500 }
    );
  }
}
