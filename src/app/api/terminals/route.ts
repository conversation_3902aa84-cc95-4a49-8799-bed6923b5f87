import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Terminal schema for validation
const terminalSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  ipAddress: z.string().optional(),
  macAddress: z.string().optional(),
  location: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  drawerId: z.string().optional(),
});

// GET /api/terminals - Get all terminals
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get("isActive");
    const drawerId = searchParams.get("drawerId");
    const unassigned = searchParams.get("unassigned");

    // Build filter
    const filter: any = {};
    if (isActive !== null) {
      filter.isActive = isActive === "true";
    }
    if (drawerId) {
      filter.drawerId = drawerId;
    }
    if (unassigned === "true") {
      filter.drawerId = null;
    }

    // Get terminals
    const terminals = await prisma.terminal.findMany({
      where: filter,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        drawer: true,
      },
    });

    return NextResponse.json({ terminals });
  } catch (error) {
    console.error("Error fetching terminals:", error);
    return NextResponse.json(
      { error: "Failed to fetch terminals", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/terminals - Create a new terminal
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = terminalSchema.parse(body);

    // Check if drawer is already assigned to another terminal and validate drawer status
    if (validatedData.drawerId) {
      // Check if drawer exists and is active
      const drawer = await prisma.cashDrawer.findUnique({
        where: { id: validatedData.drawerId },
      });

      if (!drawer) {
        return NextResponse.json(
          { error: "Cash drawer not found" },
          { status: 404 }
        );
      }

      if (!drawer.isActive) {
        return NextResponse.json(
          { error: "Cash drawer is not active" },
          { status: 400 }
        );
      }

      const existingTerminal = await prisma.terminal.findFirst({
        where: {
          drawerId: validatedData.drawerId,
          id: { not: body.id }, // Exclude current terminal if updating
        },
      });

      if (existingTerminal) {
        return NextResponse.json(
          {
            error: "Drawer already assigned",
            message: `This drawer is already assigned to terminal "${existingTerminal.name}"`
          },
          { status: 400 }
        );
      }
    }

    // Create terminal
    const terminal = await prisma.terminal.create({
      data: validatedData,
      include: {
        drawer: true,
      },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_TERMINAL",
        details: `Created terminal "${terminal.name}"`,
      },
    });

    return NextResponse.json({ terminal });
  } catch (error) {
    console.error("Error creating terminal:", error);
    return NextResponse.json(
      { error: "Failed to create terminal", message: (error as Error).message },
      { status: 500 }
    );
  }
}
