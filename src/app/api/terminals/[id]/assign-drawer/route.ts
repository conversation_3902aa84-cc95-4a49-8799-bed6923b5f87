import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Drawer assignment schema for validation
const drawerAssignmentSchema = z.object({
  drawerId: z.string().nullable(),
});

// POST /api/terminals/[id]/assign-drawer - Assign a drawer to a terminal
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to assign drawers
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = drawerAssignmentSchema.parse(body);

    // Check if terminal exists
    const terminal = await prisma.terminal.findUnique({
      where: { id: params.id },
      include: {
        drawer: true,
      },
    });

    if (!terminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    // If drawerId is null, unassign the drawer
    if (validatedData.drawerId === null) {
      // Check if there are any open drawer sessions for this terminal
      const openSessions = await prisma.drawerSession.findFirst({
        where: {
          terminalId: params.id,
          status: "OPEN",
        },
      });

      if (openSessions) {
        return NextResponse.json(
          { 
            error: "Terminal has open drawer sessions", 
            message: "Cannot unassign drawer while there are open sessions" 
          },
          { status: 400 }
        );
      }

      // Unassign drawer
      const updatedTerminal = await prisma.terminal.update({
        where: { id: params.id },
        data: {
          drawerId: null,
        },
        include: {
          drawer: true,
        },
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UNASSIGN_DRAWER",
          details: `Unassigned drawer from terminal "${terminal.name}"`,
        },
      });

      return NextResponse.json({ terminal: updatedTerminal });
    }

    // Check if drawer exists
    const drawer = await prisma.cashDrawer.findUnique({
      where: { id: validatedData.drawerId },
    });

    if (!drawer) {
      return NextResponse.json(
        { error: "Cash drawer not found" },
        { status: 404 }
      );
    }

    // Check if drawer is active
    if (!drawer.isActive) {
      return NextResponse.json(
        { error: "Cash drawer is not active" },
        { status: 400 }
      );
    }

    // Check if drawer is already assigned to another terminal
    const existingTerminal = await prisma.terminal.findFirst({
      where: {
        drawerId: validatedData.drawerId,
        id: { not: params.id }, // Exclude current terminal
      },
    });

    if (existingTerminal) {
      return NextResponse.json(
        { 
          error: "Drawer already assigned", 
          message: `This drawer is already assigned to terminal "${existingTerminal.name}"` 
        },
        { status: 400 }
      );
    }

    // Assign drawer to terminal
    const updatedTerminal = await prisma.terminal.update({
      where: { id: params.id },
      data: {
        drawerId: validatedData.drawerId,
      },
      include: {
        drawer: true,
      },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "ASSIGN_DRAWER",
        details: `Assigned drawer "${drawer.name}" to terminal "${terminal.name}"`,
      },
    });

    return NextResponse.json({ terminal: updatedTerminal });
  } catch (error) {
    console.error("Error assigning drawer:", error);
    return NextResponse.json(
      { error: "Failed to assign drawer", message: (error as Error).message },
      { status: 500 }
    );
  }
}
