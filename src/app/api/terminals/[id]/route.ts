import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Terminal schema for validation
const terminalSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  ipAddress: z.string().optional(),
  macAddress: z.string().optional(),
  location: z.string().optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  drawerId: z.string().optional().nullable(),
});

// GET /api/terminals/[id] - Get a specific terminal
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get terminal
    const terminal = await prisma.terminal.findUnique({
      where: { id: params.id },
      include: {
        drawer: true,
        drawerSessions: {
          orderBy: {
            openedAt: "desc",
          },
          take: 10,
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!terminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ terminal });
  } catch (error) {
    console.error("Error fetching terminal:", error);
    return NextResponse.json(
      { error: "Failed to fetch terminal", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/terminals/[id] - Update a terminal
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = terminalSchema.parse(body);

    // Check if terminal exists
    const existingTerminal = await prisma.terminal.findUnique({
      where: { id: params.id },
    });

    if (!existingTerminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    // Check if drawer is already assigned to another terminal and validate drawer status
    if (validatedData.drawerId) {
      // Check if drawer exists and is active
      const drawer = await prisma.cashDrawer.findUnique({
        where: { id: validatedData.drawerId },
      });

      if (!drawer) {
        return NextResponse.json(
          { error: "Cash drawer not found" },
          { status: 404 }
        );
      }

      if (!drawer.isActive) {
        return NextResponse.json(
          { error: "Cash drawer is not active" },
          { status: 400 }
        );
      }

      const existingTerminalWithDrawer = await prisma.terminal.findFirst({
        where: {
          drawerId: validatedData.drawerId,
          id: { not: params.id }, // Exclude current terminal
        },
      });

      if (existingTerminalWithDrawer) {
        return NextResponse.json(
          {
            error: "Drawer already assigned",
            message: `This drawer is already assigned to terminal "${existingTerminalWithDrawer.name}"`
          },
          { status: 400 }
        );
      }
    }

    // Update terminal
    const terminal = await prisma.terminal.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        drawer: true,
      },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_TERMINAL",
        details: `Updated terminal "${terminal.name}"`,
      },
    });

    return NextResponse.json({ terminal });
  } catch (error) {
    console.error("Error updating terminal:", error);
    return NextResponse.json(
      { error: "Failed to update terminal", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/terminals/[id] - Delete a terminal
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete terminals
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Check if terminal exists
    const existingTerminal = await prisma.terminal.findUnique({
      where: { id: params.id },
    });

    if (!existingTerminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    // Check if terminal has open drawer sessions (only prevent deletion for open sessions)
    const openSessionCount = await prisma.drawerSession.count({
      where: {
        terminalId: params.id,
        status: "OPEN"
      },
    });

    if (openSessionCount > 0) {
      return NextResponse.json(
        {
          error: "Terminal has open drawer sessions",
          message: "Cannot delete a terminal with open drawer sessions. Please close all drawer sessions first."
        },
        { status: 400 }
      );
    }

    // Delete terminal
    await prisma.terminal.delete({
      where: { id: params.id },
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "DELETE_TERMINAL",
        details: `Deleted terminal "${existingTerminal.name}"`,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting terminal:", error);
    return NextResponse.json(
      { error: "Failed to delete terminal", message: (error as Error).message },
      { status: 500 }
    );
  }
}
