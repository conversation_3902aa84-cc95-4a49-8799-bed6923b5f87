import { NextRequest, NextResponse } from 'next/server';
import { getBackupHistory, getLastBackupOperation, getLastRestoreOperation } from '@/lib/backup/backup-history';
import { jwtVerify } from "jose";

// GET /api/backup/history - Get backup history
export async function GET(request: NextRequest) {
  try {
    // Get the session token from cookies
    const token = request.cookies.get("session-token");

    // Check if token exists
    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to access backup history." },
        { status: 401 }
      );
    }

    // Verify the token
    let payload;
    try {
      const result = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );
      payload = result.payload;
    } catch (error) {
      console.error("Token verification failed:", error);
      return NextResponse.json(
        { error: "Unauthorized. Invalid authentication token." },
        { status: 401 }
      );
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access backup functionality
    if (!payload.role || !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(payload.role as string)) {
      return NextResponse.json(
        { error: "Unauthorized. You don't have permission to access backup history." },
        { status: 403 }
      );
    }

    const history = getBackupHistory();
    const lastBackup = getLastBackupOperation();
    const lastRestore = getLastRestoreOperation();

    return NextResponse.json({
      history,
      lastBackup,
      lastRestore,
    });
  } catch (error) {
    console.error('Error getting backup history:', error);
    return NextResponse.json(
      { error: 'Failed to get backup history', message: error.message },
      { status: 500 }
    );
  }
}
