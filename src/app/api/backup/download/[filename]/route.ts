import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { addBackupHistoryEntry } from '@/lib/backup/backup-history';
import { jwtVerify } from "jose";
import { prisma } from '@/auth';

// Default backup directory
const DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups');

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' bytes';
  else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
  else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
  else return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
}

// GET /api/backup/download/[filename] - Download a backup file
export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  try {
    // Get the session token from cookies
    const token = request.cookies.get("session-token");

    // Check if token exists
    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to download backup files." },
        { status: 401 }
      );
    }

    // Verify the token
    let payload;
    try {
      const result = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );
      payload = result.payload;
    } catch (error) {
      console.error("Token verification failed:", error);
      return NextResponse.json(
        { error: "Unauthorized. Invalid authentication token." },
        { status: 401 }
      );
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access backup functionality
    if (!payload.role || !["SUPER_ADMIN", "FINANCE_ADMIN"].includes(payload.role as string)) {
      return NextResponse.json(
        { error: "Unauthorized. You don't have permission to download backup files." },
        { status: 403 }
      );
    }

    const filename = params.filename;

    // Validate filename to prevent directory traversal attacks
    if (!filename || filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
      return NextResponse.json(
        { error: 'Invalid filename' },
        { status: 400 }
      );
    }

    const filePath = path.join(DEFAULT_BACKUP_DIR, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Backup file not found' },
        { status: 404 }
      );
    }

    // Read the file
    const fileBuffer = fs.readFileSync(filePath);

    // Log the download operation
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: filePath,
      fileName: filename,
      method: 'download',
      success: true,
      message: `Backup file downloaded: ${filename}`,
    });

    // Get file stats for size information
    const stats = fs.statSync(filePath);

    // Log the backup download in activity logs
    await prisma.activityLog.create({
      data: {
        userId: payload.id as string,
        action: "DOWNLOAD_BACKUP",
        details: `Downloaded backup file: ${filename} (${formatFileSize(stats.size)})`,
      },
    });

    // Create response with appropriate headers
    const response = new NextResponse(fileBuffer);

    // Set content type based on file extension
    if (filename.endsWith('.sql')) {
      response.headers.set('Content-Type', 'application/sql');
    } else {
      response.headers.set('Content-Type', 'application/octet-stream');
    }

    // Set content disposition to force download
    response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);

    return response;
  } catch (error: any) {
    console.error('Error downloading backup:', error);

    // Log the failed download attempt
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: '',
      fileName: params.filename,
      method: 'download',
      success: false,
      message: `Download failed: ${error?.message || String(error)}`,
    });

    return NextResponse.json(
      { error: 'Failed to download backup', message: error?.message || String(error) },
      { status: 500 }
    );
  }
}
