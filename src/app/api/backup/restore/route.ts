import { NextRequest, NextResponse } from 'next/server';
import { restoreBackup, validateSchemaCompatibility } from '@/lib/backup/db-backup';
import { jwtVerify } from "jose";
import { prisma } from '@/auth';
import path from 'path';

// POST /api/backup/restore - Restore from a backup
export async function POST(request: NextRequest) {
  try {
    // Get the session token from cookies
    const token = request.cookies.get("session-token");

    // Check if token exists
    if (!token) {
      return NextResponse.json(
        { error: "Unauthorized. You must be logged in to restore backups." },
        { status: 401 }
      );
    }

    // Verify the token
    let payload;
    try {
      const result = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );
      payload = result.payload;
    } catch (error) {
      console.error("Token verification failed:", error);
      return NextResponse.json(
        { error: "Unauthorized. Invalid authentication token." },
        { status: 401 }
      );
    }

    // Only allow SUPER_ADMIN to restore backups (more restrictive than viewing)
    if (!payload.role || payload.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized. Only administrators can restore backups." },
        { status: 403 }
      );
    }

    const data = await request.json();
    const { backupPath } = data;

    if (!backupPath) {
      return NextResponse.json(
        { error: 'Backup path is required' },
        { status: 400 }
      );
    }

    // Validate schema compatibility first
    const schemaValidation = await validateSchemaCompatibility(backupPath);

    // If schemas don't match, prevent restore and return warning
    if (!schemaValidation.compatible) {
      return NextResponse.json({
        success: false,
        prevented: true,
        message: schemaValidation.message,
        schemaValidation: schemaValidation
      }, { status: 409 }); // 409 Conflict is appropriate for schema version mismatch
    }

    // Log the backup restoration in activity logs BEFORE the restore operation
    // This ensures the log entry is preserved even if the restore affects the database
    await prisma.activityLog.create({
      data: {
        userId: payload.id as string,
        action: "RESTORE_BACKUP",
        details: `Restored database from backup: ${path.basename(backupPath)} (Schema version: ${schemaValidation.backupSchemaVersion})`,
      },
    });

    // Only proceed with restore if schemas match
    const result = await restoreBackup(backupPath);

    return NextResponse.json({ success: result.success, result });
  } catch (error) {
    console.error('Error restoring backup:', error);
    return NextResponse.json(
      { error: 'Failed to restore backup', message: error.message },
      { status: 500 }
    );
  }
}
