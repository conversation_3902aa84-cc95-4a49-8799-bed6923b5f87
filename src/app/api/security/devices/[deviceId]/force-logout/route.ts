import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { enhancedAuth } from "@/lib/enhanced-auth";
import { logSecurityEvent } from "@/lib/security";

const prisma = new PrismaClient();

// POST /api/security/devices/[deviceId]/force-logout - Force logout users from a specific device
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ deviceId: string }> }
) {
  try {
    const { deviceId } = await params;

    // Enhanced authentication - only admins can force logout devices
    const auth = await enhancedAuth(request, {
      requireDeviceAuth: false,
      sessionType: "admin_session",
      sensitiveAction: true,
    });

    if (!auth.authenticated || !auth.user) {
      return NextResponse.json(
        { error: "Not authenticated" },
        { status: 401 }
      );
    }

    // Only SUPER_ADMIN and FINANCE_ADMIN can force logout devices
    if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Find the device authorization record
    const deviceAuth = await prisma.deviceAuthorization.findUnique({
      where: { id: deviceId },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        }
      }
    });

    if (!deviceAuth) {
      return NextResponse.json(
        { error: "Device authorization record not found" },
        { status: 404 }
      );
    }

    // Revoke device authorization if not already revoked
    if (deviceAuth.isAuthorized) {
      await prisma.deviceAuthorization.update({
        where: { id: deviceId },
        data: {
          isAuthorized: false,
          authorizedBy: auth.user.id, // Track who revoked it
        },
      });
    }

    // Note: In a real-world scenario, you would also:
    // 1. Invalidate all active sessions for this device
    // 2. Add the device to a blacklist for immediate session termination
    // 3. Send real-time notifications to force logout
    // 
    // For this implementation, we'll log the action and rely on
    // the device authorization check in enhancedAuth to block future requests

    // Log the force logout action
    await logSecurityEvent(auth.securityContext!, {
      action: "DEVICE_FORCE_LOGOUT",
      resource: "device",
      resourceId: deviceAuth.deviceId,
      metadata: {
        deviceId: deviceAuth.deviceId,
        userId: deviceAuth.userId,
        userName: deviceAuth.user.name,
        forcedLogoutBy: auth.user.name,
        action: "FORCE_LOGOUT"
      }
    });

    return NextResponse.json({
      success: true,
      message: "Device access revoked and force logout initiated",
      device: {
        id: deviceAuth.id,
        deviceId: deviceAuth.deviceId,
        userId: deviceAuth.userId,
        userName: deviceAuth.user.name,
        isAuthorized: false
      }
    });

  } catch (error) {
    console.error("Error forcing device logout:", error);
    return NextResponse.json(
      { error: "Failed to force device logout" },
      { status: 500 }
    );
  }
}
