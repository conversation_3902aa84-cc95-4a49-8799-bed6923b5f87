import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Allow SUPER_ADMIN and FINANCE_ADMIN to view all category data
    // Allow CASHIER to view their own category data only
    const allowedRoles = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get("category");
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");

    // Support legacy single date parameter for backward compatibility
    const legacyDateParam = searchParams.get("date");

    if (!category) {
      return NextResponse.json(
        { error: "Category is required" },
        { status: 400 }
      );
    }

    let startOfDay: Date;
    let endOfDay: Date;

    if (startDateParam && endDateParam) {
      // New date range mode
      const startDate = new Date(startDateParam);
      const endDate = new Date(endDateParam);

      startOfDay = new Date(startDate);
      startOfDay.setHours(0, 0, 0, 0);

      endOfDay = new Date(endDate);
      endOfDay.setHours(23, 59, 59, 999);
    } else if (legacyDateParam) {
      // Legacy single date mode
      const targetDate = new Date(legacyDateParam);
      startOfDay = new Date(targetDate);
      startOfDay.setHours(0, 0, 0, 0);

      endOfDay = new Date(targetDate);
      endOfDay.setHours(23, 59, 59, 999);
    } else {
      return NextResponse.json(
        { error: "Date parameters are required (startDate & endDate, or legacy date)" },
        { status: 400 }
      );
    }

    // Get detailed reconciliations for the specific category
    const reconciliations = await prisma.cashReconciliation.findMany({
      where: {
        discrepancyCategory: category,
        businessDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format the response
    const formattedReconciliations = reconciliations.map((rec) => ({
      id: rec.id,
      businessDate: rec.businessDate,
      cashier: rec.user.name,
      discrepancy: Number(rec.discrepancy),
      discrepancyCategory: rec.discrepancyCategory,
      resolutionStatus: rec.resolutionStatus || "PENDING",
      notes: rec.notes,
      resolutionNotes: rec.resolutionNotes,
      createdAt: rec.createdAt,
    }));

    // Calculate summary statistics
    const totalAmount = reconciliations.reduce((sum, rec) => sum + Math.abs(Number(rec.discrepancy)), 0);
    const avgAmount = reconciliations.length > 0 ? totalAmount / reconciliations.length : 0;
    const resolvedCount = reconciliations.filter(rec => rec.resolutionStatus === 'RESOLVED').length;
    const resolutionRate = reconciliations.length > 0 ? (resolvedCount / reconciliations.length) * 100 : 0;

    return NextResponse.json({
      category,
      startDate: startOfDay.toISOString().split("T")[0],
      endDate: endOfDay.toISOString().split("T")[0],
      // Legacy compatibility
      date: startOfDay.toISOString().split("T")[0],
      reconciliations: formattedReconciliations,
      summary: {
        totalOccurrences: reconciliations.length,
        totalAmount,
        avgAmount,
        resolvedCount,
        resolutionRate,
      },
    });

  } catch (error: any) {
    console.error("Error fetching category details:", error);
    return NextResponse.json(
      { error: "Failed to fetch category details", details: error.message },
      { status: 500 }
    );
  }
}
