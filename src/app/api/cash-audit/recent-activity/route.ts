import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";
import { subDays } from "date-fns";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access recent activity
    if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get reconciliations created in the last 7 days, regardless of business date
    const sevenDaysAgo = subDays(new Date(), 7);

    const reconciliations = await prisma.cashReconciliation.findMany({
      where: {
        createdAt: {
          gte: sevenDaysAgo,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        investigator: {
          select: {
            id: true,
            name: true,
          },
        },
        auditAlerts: {
          where: {
            isResolved: false,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 20, // Limit to most recent 20 records
    });

    // Transform the data to include additional context
    const transformedReconciliations = reconciliations.map((rec) => ({
      id: rec.id,
      businessDate: rec.businessDate,
      createdAt: rec.createdAt,
      cashier: rec.user.name,
      discrepancy: Number(rec.discrepancy),
      discrepancyCategory: rec.discrepancyCategory,
      resolutionStatus: rec.resolutionStatus,
      alertCount: rec.auditAlerts.length,
      notes: rec.notes,
      resolutionNotes: rec.resolutionNotes,
      investigator: rec.investigator?.name || null,
      // Add flags to help with UI display
      isFromToday: rec.businessDate.toDateString() === new Date().toDateString(),
      daysSinceCreated: Math.floor((new Date().getTime() - rec.createdAt.getTime()) / (1000 * 60 * 60 * 24)),
      daysSinceBusinessDate: Math.floor((new Date().getTime() - rec.businessDate.getTime()) / (1000 * 60 * 60 * 24)),
    }));

    return NextResponse.json({
      reconciliations: transformedReconciliations,
      summary: {
        totalRecent: reconciliations.length,
        pendingResolution: reconciliations.filter(r => r.resolutionStatus === 'PENDING').length,
        withAlerts: reconciliations.filter(r => r.auditAlerts.length > 0).length,
      },
    });

  } catch (error) {
    console.error("Error fetching recent activity:", error);
    return NextResponse.json(
      { error: "Failed to fetch recent activity" },
      { status: 500 }
    );
  }
}
