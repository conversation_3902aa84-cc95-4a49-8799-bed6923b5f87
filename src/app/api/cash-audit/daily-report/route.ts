import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";
import { startOfDay, endOfDay, subDays } from "date-fns";

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and FINANCE_ADMIN to access cash audit reports
    if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");

    // Support legacy single date parameter for backward compatibility
    const legacyDateParam = searchParams.get("date");

    let startDate: Date;
    let endDate: Date;

    if (startDateParam && endDateParam) {
      // New date range mode
      const startTargetDate = new Date(startDateParam);
      const endTargetDate = new Date(endDateParam);

      startDate = startOfDay(startTargetDate);
      endDate = endOfDay(endTargetDate);
    } else if (legacyDateParam) {
      // Legacy single date mode
      const targetDate = new Date(legacyDateParam);
      startDate = startOfDay(targetDate);
      endDate = endOfDay(targetDate);
    } else {
      // Default to today if no parameters provided
      const today = new Date();
      startDate = startOfDay(today);
      endDate = endOfDay(today);
    }

    // Get all cash reconciliations for the day
    const reconciliations = await prisma.cashReconciliation.findMany({
      where: {
        businessDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        investigator: {
          select: {
            id: true,
            name: true,
          },
        },
        auditAlerts: {
          where: {
            isResolved: false,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Calculate summary statistics
    const totalReconciliations = reconciliations.length;
    const totalDiscrepancy = reconciliations.reduce(
      (sum, rec) => sum + Number(rec.discrepancy),
      0
    );
    const totalSurplus = reconciliations
      .filter((rec) => Number(rec.discrepancy) > 0)
      .reduce((sum, rec) => sum + Number(rec.discrepancy), 0);
    const totalShortage = Math.abs(
      reconciliations
        .filter((rec) => Number(rec.discrepancy) < 0)
        .reduce((sum, rec) => sum + Number(rec.discrepancy), 0)
    );

    const reconciliationsWithDiscrepancies = reconciliations.filter(
      (rec) => Number(rec.discrepancy) !== 0
    ).length;

    const pendingResolutions = reconciliations.filter(
      (rec) => rec.resolutionStatus === "PENDING" && Number(rec.discrepancy) !== 0
    ).length;

    const activeAlerts = reconciliations.reduce(
      (sum, rec) => sum + rec.auditAlerts.length,
      0
    );

    // Group by discrepancy category
    const discrepancyByCategory = reconciliations.reduce((acc, rec) => {
      if (rec.discrepancyCategory && Number(rec.discrepancy) !== 0) {
        acc[rec.discrepancyCategory] = (acc[rec.discrepancyCategory] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    // Group by cashier performance
    const cashierPerformance = reconciliations.reduce((acc, rec) => {
      const cashierId = rec.user.id;
      if (!acc[cashierId]) {
        acc[cashierId] = {
          name: rec.user.name,
          totalSessions: 0,
          totalDiscrepancy: 0,
          discrepancyCount: 0,
        };
      }
      acc[cashierId].totalSessions += 1;
      acc[cashierId].totalDiscrepancy += Number(rec.discrepancy);
      if (Number(rec.discrepancy) !== 0) {
        acc[cashierId].discrepancyCount += 1;
      }
      return acc;
    }, {} as Record<string, any>);

    return NextResponse.json({
      startDate: startDate.toISOString().split("T")[0],
      endDate: endDate.toISOString().split("T")[0],
      // Legacy compatibility
      date: startDate.toISOString().split("T")[0],
      summary: {
        totalReconciliations,
        totalDiscrepancy,
        totalSurplus,
        totalShortage,
        reconciliationsWithDiscrepancies,
        discrepancyRate: totalReconciliations > 0 
          ? (reconciliationsWithDiscrepancies / totalReconciliations) * 100 
          : 0,
        pendingResolutions,
        activeAlerts,
      },
      discrepancyByCategory,
      cashierPerformance: Object.values(cashierPerformance),
      reconciliations: reconciliations.map((rec) => ({
        id: rec.id,
        businessDate: rec.businessDate,
        cashier: rec.user.name,
        openingBalance: Number(rec.openingBalance),
        expectedAmount: Number(rec.expectedAmount),
        actualAmount: Number(rec.actualAmount),
        discrepancy: Number(rec.discrepancy),
        discrepancyCategory: rec.discrepancyCategory,
        resolutionStatus: rec.resolutionStatus,
        status: rec.status,
        notes: rec.notes,
        resolutionNotes: rec.resolutionNotes,
        investigator: rec.investigator?.name,
        alertCount: rec.auditAlerts.length,
        createdAt: rec.createdAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching daily cash audit report:", error);
    return NextResponse.json(
      { error: "Failed to fetch daily report", message: (error as Error).message },
      { status: 500 }
    );
  }
}
