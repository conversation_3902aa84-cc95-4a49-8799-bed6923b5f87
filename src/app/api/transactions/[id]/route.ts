import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { BatchManagementService } from "@/lib/batch-management";
import { z } from "zod";

// Transaction update schema for validation
const transactionUpdateSchema = z.object({
  paymentStatus: z.enum(["PENDING", "PAID", "PARTIAL", "CANCELLED"]).optional(),
  status: z.enum(["PENDING", "COMPLETED", "VOIDED"]).optional(),
  notes: z.string().optional(),
});

// GET /api/transactions/[id] - Get a specific transaction
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get transaction
    const transaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        customer: true,
        cashier: {
          select: {
            id: true,
            name: true,
          },
        },
        approver: {
          select: {
            id: true,
            name: true,
          },
        },
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                unit: true,
              },
            },
          },
        },
      },
    });

    // Check if transaction exists
    if (!transaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ transaction });
  } catch (error) {
    console.error("Error fetching transaction:", error);
    return NextResponse.json(
      { error: "Failed to fetch transaction", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/transactions/[id] - Update a transaction (payment status, void, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update transactions
    const hasPermission = ["SUPER_ADMIN", "CASHIER", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get transaction
    const existingTransaction = await prisma.transaction.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    // Check if transaction exists
    if (!existingTransaction) {
      return NextResponse.json(
        { error: "Transaction not found" },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const data = await request.json();
    const validationResult = transactionUpdateSchema.safeParse(data);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Start a transaction to ensure all operations succeed or fail together
    const transaction = await prisma.$transaction(async (prismaClient) => {
      // Handle voiding a transaction
      if (validatedData.status === "VOIDED" && existingTransaction.status !== "VOIDED") {
        // Return items to inventory and restore batch quantities
        for (const item of existingTransaction.items) {
          // Get current stock
          const storeStock = await prismaClient.storeStock.findFirst({
            where: { productId: item.productId },
          });

          if (!storeStock) {
            throw new Error(`No stock found for product ID: ${item.productId}`);
          }

          // Update stock quantity (add back the sold quantity)
          await prismaClient.storeStock.update({
            where: { id: storeStock.id },
            data: {
              quantity: {
                increment: item.quantity,
              },
              lastUpdated: new Date(),
            },
          });

          // Restore batch quantity if item was linked to a batch
          if (item.batchId) {
            const batch = await prismaClient.stockBatch.findUnique({
              where: { id: item.batchId },
              select: { remainingQuantity: true, status: true }
            });

            if (batch) {
              const newRemainingQuantity = Number(batch.remainingQuantity) + Number(item.quantity);

              await prismaClient.stockBatch.update({
                where: { id: item.batchId },
                data: {
                  remainingQuantity: newRemainingQuantity,
                  status: 'ACTIVE' // Reactivate if it was sold out
                }
              });

              // Create stock history for batch restoration
              await prismaClient.stockHistory.create({
                data: {
                  productId: item.productId,
                  batchId: item.batchId,
                  storeStockId: storeStock.id,
                  userId: auth.user.id,
                  source: "RETURN",
                  previousQuantity: batch.remainingQuantity,
                  newQuantity: newRemainingQuantity,
                  changeQuantity: Number(item.quantity),
                  referenceId: existingTransaction.id,
                  referenceType: "Transaction",
                  notes: `Batch quantity restored due to transaction void ${existingTransaction.id}`,
                },
              });
            }
          }

          // Get current stock quantity after increment
          const updatedStoreStock = await prismaClient.storeStock.findUnique({
            where: { id: storeStock.id }
          });

          if (!updatedStoreStock) {
            throw new Error(`Failed to retrieve updated store stock for product ID: ${item.productId}`);
          }

          // Create stock history record
          await prismaClient.stockHistory.create({
            data: {
              productId: item.productId,
              storeStockId: storeStock.id,
              userId: auth.user.id,
              source: "RETURN", // Using RETURN as the source for voided transactions
              previousQuantity: Number(storeStock.quantity),
              newQuantity: Number(updatedStoreStock.quantity),
              changeQuantity: Number(item.quantity), // Positive for returns to inventory
              referenceId: existingTransaction.id,
              referenceType: "VoidedTransaction",
              notes: `Voided transaction ${existingTransaction.id}`,
            },
          });
        }
      }

      // Update transaction
      const updatedTransaction = await prismaClient.transaction.update({
        where: { id: params.id },
        data: {
          ...validatedData,
          // If voiding, set payment status to CANCELLED
          ...(validatedData.status === "VOIDED" ? { paymentStatus: "CANCELLED" } : {}),
          // If approving, set approver and approval time
          ...(validatedData.status === "COMPLETED" && existingTransaction.status === "PENDING" ? {
            approverId: auth.user.id,
            approvedAt: new Date(),
          } : {}),
        },
        include: {
          customer: true,
          cashier: {
            select: {
              id: true,
              name: true,
            },
          },
          terminal: {
            select: {
              id: true,
              name: true,
              location: true,
            },
          },
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Log activity
      await prismaClient.activityLog.create({
        data: {
          userId: auth.user.id,
          action: validatedData.status === "VOIDED" ? "VOID_TRANSACTION" : "UPDATE_TRANSACTION",
          details: validatedData.status === "VOIDED"
            ? `Voided transaction ${params.id}`
            : `Updated transaction ${params.id} status to ${validatedData.status || existingTransaction.status}, payment status to ${validatedData.paymentStatus || existingTransaction.paymentStatus}`,
        },
      });

      return updatedTransaction;
    });

    return NextResponse.json({ transaction });
  } catch (error) {
    console.error("Error updating transaction:", error);
    return NextResponse.json(
      { error: "Failed to update transaction", message: (error as Error).message },
      { status: 500 }
    );
  }
}
