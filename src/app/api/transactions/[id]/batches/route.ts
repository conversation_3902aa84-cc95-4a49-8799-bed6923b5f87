import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// GET /api/transactions/[id]/batches - Get batch information for transaction items
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN', 'CASHIER'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get transaction with batch information
    const transaction = await prisma.transaction.findUnique({
      where: { id },
      include: {
        customer: true,
        cashier: {
          select: {
            id: true,
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                barcode: true,
              },
            },
            stockBatch: {
              include: {
                productSupplier: {
                  include: {
                    supplier: {
                      select: {
                        id: true,
                        name: true,
                        contactPerson: true,
                        phone: true,
                        email: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    // Group items by product and include batch information
    const itemsWithBatchInfo = transaction.items.map(item => ({
      id: item.id,
      productId: item.productId,
      product: item.product,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discount: item.discount,
      subtotal: item.subtotal,
      batch: item.stockBatch ? {
        id: item.stockBatch.id,
        batchNumber: item.stockBatch.batchNumber,
        receivedDate: item.stockBatch.receivedDate,
        expiryDate: item.stockBatch.expiryDate,
        purchasePrice: item.stockBatch.purchasePrice,
        supplier: item.stockBatch.productSupplier?.supplier || null,
        supplierInfo: item.stockBatch.productSupplier ? {
          id: item.stockBatch.productSupplier.id,
          supplierProductCode: item.stockBatch.productSupplier.supplierProductCode,
          supplierProductName: item.stockBatch.productSupplier.supplierProductName,
          purchasePrice: item.stockBatch.productSupplier.purchasePrice,
          isPreferred: item.stockBatch.productSupplier.isPreferred,
        } : null,
      } : null,
    }));

    // Calculate summary statistics
    const batchSummary = {
      totalItems: transaction.items.length,
      itemsWithBatches: transaction.items.filter(item => item.batchId).length,
      uniqueSuppliers: new Set(
        transaction.items
          .filter(item => item.stockBatch?.productSupplier?.supplier)
          .map(item => item.stockBatch!.productSupplier!.supplier.id)
      ).size,
      uniqueBatches: new Set(
        transaction.items
          .filter(item => item.batchId)
          .map(item => item.batchId)
      ).size,
    };

    return NextResponse.json({
      transaction: {
        id: transaction.id,
        transactionDate: transaction.transactionDate,
        total: transaction.total,
        status: transaction.status,
        customer: transaction.customer,
        cashier: transaction.cashier,
      },
      items: itemsWithBatchInfo,
      batchSummary,
    });
  } catch (error) {
    console.error('Error fetching transaction batch information:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
