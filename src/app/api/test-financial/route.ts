import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { notifyInvoiceApproved, notifyInvoicePaymentMade } from "@/lib/notifications/financial";

// POST /api/test-financial - Test financial notification events
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing financial notification events...');

    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error || "Authentication failed" },
        { status: auth.status || 401 }
      );
    }

    // Only allow SUPER_ADMIN to test notifications
    if (auth.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json(
        { error: "Only SUPER_ADMIN users can test financial notifications" },
        { status: 403 }
      );
    }

    const { testType } = await request.json();

    let result = {};

    switch (testType) {
      case 'invoice_approved':
        console.log('🧪 Testing invoice approval notification...');
        await notifyInvoiceApproved(
          'test-invoice-456',
          'INV-2024-001',
          'PT Supplier Utama',
          2500000, // 2.5 million IDR
          'Jane Smith',
          {
            testNotification: true,
            approvalDate: new Date().toISOString(),
          }
        );
        result = {
          type: 'invoice_approved',
          message: 'Invoice approval notification sent',
          details: {
            invoiceNumber: 'INV-2024-001',
            supplier: 'PT Supplier Utama',
            amount: 2500000,
            formattedAmount: 'Rp 2.500.000',
            approver: 'Jane Smith'
          }
        };
        break;

      case 'invoice_payment':
        console.log('🧪 Testing invoice payment notification...');
        await notifyInvoicePaymentMade(
          'test-invoice-789',
          'INV-2024-002',
          1000000, // 1 million IDR payment
          'Bank Transfer',
          500000, // 500k remaining balance
          {
            testNotification: true,
            paymentDate: new Date().toISOString(),
            paymentReference: 'TXN-********-001',
          }
        );
        result = {
          type: 'invoice_payment',
          message: 'Invoice payment notification sent',
          details: {
            invoiceNumber: 'INV-2024-002',
            paymentAmount: 1000000,
            formattedPaymentAmount: 'Rp 1.000.000',
            paymentMethod: 'Bank Transfer',
            remainingBalance: 500000,
            formattedRemainingBalance: 'Rp 500.000'
          }
        };
        break;

      default:
        return NextResponse.json(
          { 
            error: "Invalid test type",
            validTypes: ['invoice_approved', 'invoice_payment']
          },
          { status: 400 }
        );
    }

    console.log('✅ Financial notification test completed successfully');

    return NextResponse.json({
      success: true,
      message: "Financial notification test completed",
      result,
      timestamp: new Date().toISOString(),
      testedBy: auth.user.name,
      systemInfo: {
        totalFinancialEventTypes: 3,
        targetRoles: ['SUPER_ADMIN', 'FINANCE_ADMIN'],
        deliveryMethods: ['IN_APP', 'TOAST', 'EMAIL']
      }
    });

  } catch (error) {
    console.error("❌ Error testing financial notifications:", error);
    return NextResponse.json(
      {
        error: "Failed to test financial notifications",
        details: error.message,
        stack: error.stack
      },
      { status: 500 }
    );
  }
}

// GET /api/test-financial - Get test information
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error || "Authentication failed" },
        { status: auth.status || 401 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Financial notification test endpoint",
      availableTests: [
        {
          type: 'invoice_approved',
          description: 'Test invoice approval notification',
          eventType: 'invoice.approved',
          priority: 'NORMAL'
        },
        {
          type: 'invoice_payment',
          description: 'Test invoice payment confirmation',
          eventType: 'invoice.payment_made',
          priority: 'NORMAL'
        }
      ],
      roleAccess: {
        allowedRoles: ['SUPER_ADMIN', 'FINANCE_ADMIN'],
        restrictedRoles: ['CASHIER', 'WAREHOUSE_ADMIN', 'MARKETING'],
        testingRole: 'SUPER_ADMIN'
      },
      usage: {
        method: 'POST',
        body: { testType: 'invoice_approved | invoice_payment' },
        authentication: 'Required (SUPER_ADMIN only for testing)'
      }
    });

  } catch (error) {
    console.error("❌ Error getting test info:", error);
    return NextResponse.json(
      { error: "Failed to get test information", details: error.message },
      { status: 500 }
    );
  }
}
