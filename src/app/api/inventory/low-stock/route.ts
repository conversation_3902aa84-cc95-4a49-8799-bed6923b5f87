import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/low-stock - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/low-stock - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/low-stock - Get products with low stock
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view low stock
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const categoryId = searchParams.get("categoryId");
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query for products with low stock
    const query: any = {
      storeStock: {
        quantity: {
          lte: prisma.storeStock.fields.minThreshold
        }
      },
      active: true
    };

    if (categoryId) {
      query.categoryId = categoryId;
    }

    // Get total count for pagination
    const totalCount = await prisma.product.count({
      where: query
    });

    // Get products with low stock
    const lowStockProducts = await prisma.product.findMany({
      where: query,
      include: {
        category: true,
        unit: true,
        storeStock: true
      },
      skip,
      take: limit,
      orderBy: [
        {
          storeStock: {
            quantity: "asc"
          }
        },
        {
          name: "asc"
        }
      ]
    });

    // Calculate stock status for each product
    const productsWithStatus = lowStockProducts.map(product => {
      const storeStock = product.storeStock;
      let stockStatus = "NORMAL";
      let percentRemaining = 100;

      if (storeStock) {
        const quantity = Number(storeStock.quantity);
        const minThreshold = Number(storeStock.minThreshold);
        
        if (quantity <= 0) {
          stockStatus = "OUT_OF_STOCK";
          percentRemaining = 0;
        } else if (quantity <= minThreshold * 0.5) {
          stockStatus = "CRITICAL";
          percentRemaining = Math.round((quantity / minThreshold) * 100);
        } else if (quantity <= minThreshold) {
          stockStatus = "LOW";
          percentRemaining = Math.round((quantity / minThreshold) * 100);
        }
      }

      return {
        ...product,
        stockStatus,
        percentRemaining
      };
    });

    return NextResponse.json({
      lowStockProducts: productsWithStatus,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching low stock products:", error);
    return NextResponse.json(
      { error: "Failed to fetch low stock products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/low-stock/notify - Create notifications for low stock items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create notifications
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Find all products with low stock
    const lowStockProducts = await prisma.product.findMany({
      where: {
        storeStock: {
          quantity: {
            lte: prisma.storeStock.fields.minThreshold
          }
        },
        active: true
      },
      include: {
        category: true,
        storeStock: true
      }
    });

    // Find users who should receive notifications (warehouse admins and super admins)
    const users = await prisma.user.findMany({
      where: {
        role: {
          in: ["SUPER_ADMIN", "WAREHOUSE_ADMIN"]
        },
        active: true
      }
    });

    // Create notifications for each user
    const notifications = [];
    for (const user of users) {
      // Create a summary notification
      if (lowStockProducts.length > 0) {
        const notification = await prisma.notification.create({
          data: {
            userId: user.id,
            title: "Low Stock Alert",
            message: `${lowStockProducts.length} products are below minimum stock levels.`,
            type: "ALERT"
          }
        });
        notifications.push(notification);
      }
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "CREATE_LOW_STOCK_NOTIFICATIONS",
        details: `Created ${notifications.length} low stock notifications for ${users.length} users.`,
      }
    });

    return NextResponse.json({
      success: true,
      notificationsCreated: notifications.length,
      lowStockCount: lowStockProducts.length
    });
  } catch (error) {
    console.error("Error creating low stock notifications:", error);
    return NextResponse.json(
      { error: "Failed to create low stock notifications", message: (error as Error).message },
      { status: 500 }
    );
  }
}
