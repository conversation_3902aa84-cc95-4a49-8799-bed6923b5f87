import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { StockChangeSource } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/history - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/history - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/history - Get stock history
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view stock history
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get("productId");
    const source = searchParams.get("source") as StockChangeSource | null;
    const locationType = searchParams.get("locationType");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query
    const query: any = {};

    if (productId) {
      query.productId = productId;
    }

    if (source) {
      query.source = source;
    }

    if (locationType) {
      if (locationType === "STORE") {
        query.storeStockId = { not: null };
        query.warehouseStockId = null;
      } else if (locationType === "WAREHOUSE") {
        query.warehouseStockId = { not: null };
        query.storeStockId = null;
      }
    }

    // Process date range with proper timezone handling
    if (startDate || endDate) {
      const { processDateRange } = await import("@/lib/utils");
      const { start, end } = processDateRange(startDate, endDate);

      query.date = {};

      if (start) {
        query.date.gte = start;
      }

      if (end) {
        query.date.lte = end;
      }

      // Log the processed dates for debugging
      console.log("[API] /api/inventory/history - Date filter applied:", {
        startDate: startDate ? new Date(startDate).toISOString() : null,
        endDate: endDate ? new Date(endDate).toISOString() : null,
        processedStart: start?.toISOString(),
        processedEnd: end?.toISOString(),
      });
    }

    // Get total count for pagination
    const totalCount = await prisma.stockHistory.count({
      where: query
    });

    // Get stock history with related details
    const history = await prisma.stockHistory.findMany({
      where: query,
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        storeStock: true,
        warehouseStock: true
      },
      skip,
      take: limit,
      orderBy: {
        date: "desc"
      }
    });

    return NextResponse.json({
      history,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching stock history:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock history", message: (error as Error).message },
      { status: 500 }
    );
  }
}
