import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { TransferStatus } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/simple-transfers - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/simple-transfers - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/simple-transfers - Get all simple stock transfers
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view transfers
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get("productId");
    const status = searchParams.get("status") as TransferStatus | null;
    const fromStore = searchParams.get("fromStore") === "true" ? true :
                     searchParams.get("fromStore") === "false" ? false : null;
    const toStore = searchParams.get("toStore") === "true" ? true :
                   searchParams.get("toStore") === "false" ? false : null;
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query
    const query: any = {};

    if (productId) {
      query.productId = productId;
    }

    if (status) {
      query.status = status;
    }

    if (fromStore !== null) {
      query.fromStore = fromStore;
    }

    if (toStore !== null) {
      query.toStore = toStore;
    }

    // Get total count for pagination
    const totalCount = await prisma.simpleStockTransfer.count({
      where: query
    });

    // Get stock transfers with related details
    const transfers = await prisma.simpleStockTransfer.findMany({
      where: query,
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        requestedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      },
      skip,
      take: limit,
      orderBy: {
        date: "desc"
      }
    });

    return NextResponse.json({
      transfers,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching simple stock transfers:", error);
    return NextResponse.json(
      { error: "Failed to fetch simple stock transfers", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/simple-transfers - Create a simple stock transfer
export async function POST(request: NextRequest) {
  try {
    console.log("[API] POST /api/inventory/simple-transfers - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    console.log("[API] Authentication result:", auth);

    if (!auth.authenticated) {
      console.log("[API] Authentication failed:", auth.error);
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create transfer requests
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      console.log("[API] Insufficient permissions:", auth.user.role);
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    console.log("[API] User authorized:", auth.user.name, auth.user.role);

    // Get request body
    let body;
    try {
      body = await request.json();
      console.log("[API] Request body:", body);
    } catch (error) {
      console.error("[API] Error parsing request body:", error);
      return NextResponse.json(
        { error: "Invalid request body", message: "Could not parse JSON body" },
        { status: 400 }
      );
    }

    // Validate transfer data
    const transferSchema = z.object({
      productId: z.string(),
      quantity: z.number().positive(),
      fromStore: z.boolean(),
      toStore: z.boolean(),
      notes: z.string().optional()
    });

    console.log("[API] Validating request body against schema");
    const validationResult = transferSchema.safeParse(body);
    if (!validationResult.success) {
      console.error("[API] Validation failed:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    console.log("[API] Validation successful");
    const { productId, quantity, fromStore, toStore, notes } = validationResult.data;

    // Check if product exists
    console.log("[API] Checking if product exists:", productId);
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        storeStock: true,
        warehouseStock: true
      }
    });

    if (!product) {
      console.log("[API] Product not found:", productId);
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }
    console.log("[API] Product found:", product.name);

    // Check if source and destination are different
    if (fromStore === toStore) {
      console.log("[API] Source and destination are the same");
      return NextResponse.json(
        { error: "Source and destination cannot be the same" },
        { status: 400 }
      );
    }
    console.log("[API] Source and destination are different");

    // Check if source has enough stock
    if (fromStore && product.storeStock) {
      if (Number(product.storeStock.quantity) < quantity) {
        console.log("[API] Insufficient stock in store:", product.storeStock.quantity, "needed:", quantity);
        return NextResponse.json(
          { error: "Insufficient stock in store", available: product.storeStock.quantity },
          { status: 400 }
        );
      }
      console.log("[API] Store has sufficient stock");
    } else if (!fromStore && product.warehouseStock) {
      if (Number(product.warehouseStock.quantity) < quantity) {
        console.log("[API] Insufficient stock in warehouse:", product.warehouseStock.quantity, "needed:", quantity);
        return NextResponse.json(
          { error: "Insufficient stock in warehouse", available: product.warehouseStock.quantity },
          { status: 400 }
        );
      }
      console.log("[API] Warehouse has sufficient stock");
    } else {
      console.log("[API] Source stock not found");
      return NextResponse.json(
        { error: fromStore ? "Store stock not found" : "Warehouse stock not found" },
        { status: 404 }
      );
    }

    // Check if destination exists
    if (toStore && !product.storeStock) {
      console.log("[API] Destination store stock not found");
      return NextResponse.json(
        { error: "Destination store stock not found" },
        { status: 404 }
      );
    } else if (!toStore && !product.warehouseStock) {
      console.log("[API] Destination warehouse stock not found");
      return NextResponse.json(
        { error: "Destination warehouse stock not found" },
        { status: 404 }
      );
    }
    console.log("[API] Destination stock found");

    // Create transfer request
    console.log("[API] Creating simple stock transfer");
    try {
      const transfer = await prisma.simpleStockTransfer.create({
        data: {
          productId,
          quantity,
          fromStore,
          toStore,
          status: TransferStatus.PENDING,
          notes: notes || "",
          requestedById: auth.user.id
        },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          requestedBy: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        }
      });

      console.log("[API] Simple stock transfer created successfully:", transfer.id);

      // Log activity
      console.log("[API] Creating activity log");
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "CREATE_SIMPLE_STOCK_TRANSFER",
          details: `Created stock transfer request for ${product.name} (${product.sku}): ${quantity} units from ${fromStore ? "Store" : "Warehouse"} to ${toStore ? "Store" : "Warehouse"}`,
        }
      });
      console.log("[API] Activity log created");

      return NextResponse.json({ transfer });
    } catch (error) {
      console.error("[API] Error creating simple stock transfer:", error);

      // Ensure we have a proper error message
      let errorMessage = "Failed to create stock transfer";
      if (error instanceof Error) {
        errorMessage = error.message || errorMessage;
      }

      return NextResponse.json(
        {
          error: errorMessage,
          details: error instanceof Error ? error.stack : String(error)
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("[API] Unhandled error in POST /api/inventory/simple-transfers:", error);

    // Try to get more detailed error information
    let errorMessage = "Unknown error";
    let errorDetails = null;

    if (error instanceof Error) {
      errorMessage = error.message || "Unknown error";
      errorDetails = (error as any).stack;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      try {
        errorMessage = JSON.stringify(error);
      } catch (jsonError) {
        errorMessage = "Error object could not be stringified";
        console.error("[API] Error stringifying error object:", jsonError);
      }
    }

    console.error("[API] Error details:", errorMessage);
    if (errorDetails) console.error("[API] Stack trace:", errorDetails);

    return NextResponse.json(
      {
        error: "Failed to create stock transfer",
        message: errorMessage,
        details: errorDetails
      },
      { status: 500 }
    );
  }
}
