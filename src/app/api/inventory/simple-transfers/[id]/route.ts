import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { TransferStatus } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/simple-transfers/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/simple-transfers/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/simple-transfers/[id] - Get a specific simple stock transfer
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get the transfer
    const transfer = await prisma.simpleStockTransfer.findUnique({
      where: { id: params.id },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        requestedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        }
      }
    });

    if (!transfer) {
      return NextResponse.json(
        { error: "Transfer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ transfer });
  } catch (error) {
    console.error("Error fetching simple stock transfer:", error);
    return NextResponse.json(
      { error: "Failed to fetch simple stock transfer", message: (error as Error).message },
      { status: 500 }
    );
  }
}
