import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { verifyAuthToken } from "@/lib/auth-utils";
import { BatchManagementService } from "@/lib/batch-management";

// GET /api/inventory/batch-integrity - Validate batch-stock integrity
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view batch integrity
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const productId = searchParams.get("productId");
    const location = searchParams.get("location") as 'store' | 'warehouse' | null;

    if (productId && location) {
      // Validate specific product and location
      const validation = await BatchManagementService.validateStockBatchIntegrity(
        productId,
        location
      );

      return NextResponse.json({
        productId,
        location,
        ...validation
      });
    }

    // Validate all products in both locations
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        sku: true,
        storeStock: {
          select: {
            quantity: true
          }
        },
        warehouseStock: {
          select: {
            quantity: true
          }
        }
      }
    });

    const validationResults = [];

    for (const product of products) {
      // Check store stock integrity
      if (product.storeStock) {
        const storeValidation = await BatchManagementService.validateStockBatchIntegrity(
          product.id,
          'store'
        );
        
        validationResults.push({
          productId: product.id,
          productName: product.name,
          productSku: product.sku,
          location: 'store',
          ...storeValidation
        });
      }

      // Check warehouse stock integrity
      if (product.warehouseStock) {
        const warehouseValidation = await BatchManagementService.validateStockBatchIntegrity(
          product.id,
          'warehouse'
        );
        
        validationResults.push({
          productId: product.id,
          productName: product.name,
          productSku: product.sku,
          location: 'warehouse',
          ...warehouseValidation
        });
      }
    }

    // Separate valid and invalid results
    const validResults = validationResults.filter(r => r.isValid);
    const invalidResults = validationResults.filter(r => !r.isValid);

    return NextResponse.json({
      summary: {
        totalChecked: validationResults.length,
        validCount: validResults.length,
        invalidCount: invalidResults.length,
        integrityPercentage: validationResults.length > 0 
          ? Math.round((validResults.length / validationResults.length) * 100) 
          : 100
      },
      validResults,
      invalidResults,
      allResults: validationResults
    });

  } catch (error) {
    console.error("Error validating batch integrity:", error);
    return NextResponse.json(
      { error: "Failed to validate batch integrity", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/batch-integrity - Fix batch integrity issues
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to fix batch integrity
    const hasPermission = ["SUPER_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can fix batch integrity issues" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { productId, location, action } = body;

    if (!productId || !location || !action) {
      return NextResponse.json(
        { error: "Missing required fields: productId, location, action" },
        { status: 400 }
      );
    }

    if (!['store', 'warehouse'].includes(location)) {
      return NextResponse.json(
        { error: "Invalid location. Must be 'store' or 'warehouse'" },
        { status: 400 }
      );
    }

    if (!['reconcile_to_stock', 'reconcile_to_batches'].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'reconcile_to_stock' or 'reconcile_to_batches'" },
        { status: 400 }
      );
    }

    // Get current validation state
    const validation = await BatchManagementService.validateStockBatchIntegrity(
      productId,
      location
    );

    if (validation.isValid) {
      return NextResponse.json({
        message: "No integrity issues found for this product and location",
        validation
      });
    }

    const result = await prisma.$transaction(async (tx) => {
      if (action === 'reconcile_to_stock') {
        // Update batch quantities to match stock quantity
        // This assumes stock quantity is correct and batches need adjustment
        
        // Get all active batches for this product/location
        const whereClause: any = {
          productId,
          status: 'ACTIVE'
        };

        if (location === 'store') {
          whereClause.storeStockId = { not: null };
        } else {
          whereClause.warehouseStockId = { not: null };
        }

        const batches = await tx.stockBatch.findMany({
          where: whereClause,
          orderBy: [
            { receivedDate: 'asc' },
            { createdAt: 'asc' }
          ]
        });

        if (batches.length === 0) {
          throw new Error("No active batches found to reconcile");
        }

        // Distribute stock quantity across batches proportionally
        let remainingStock = validation.stockQuantity;
        const updates = [];

        for (let i = 0; i < batches.length; i++) {
          const batch = batches[i];
          const isLastBatch = i === batches.length - 1;
          
          let newRemainingQuantity;
          if (isLastBatch) {
            // Give all remaining stock to the last batch
            newRemainingQuantity = remainingStock;
          } else {
            // Distribute proportionally based on original batch quantity
            const proportion = Number(batch.quantity) / validation.batchTotalQuantity;
            newRemainingQuantity = Math.floor(validation.stockQuantity * proportion);
          }

          if (newRemainingQuantity < 0) newRemainingQuantity = 0;
          
          await tx.stockBatch.update({
            where: { id: batch.id },
            data: {
              remainingQuantity: newRemainingQuantity,
              status: newRemainingQuantity === 0 ? 'SOLD_OUT' : 'ACTIVE'
            }
          });

          updates.push({
            batchId: batch.id,
            oldQuantity: Number(batch.remainingQuantity),
            newQuantity: newRemainingQuantity
          });

          remainingStock -= newRemainingQuantity;
        }

        return { action: 'reconcile_to_stock', updates };

      } else {
        // reconcile_to_batches: Update stock quantity to match batch total
        // This assumes batch quantities are correct and stock needs adjustment
        
        if (location === 'store') {
          await tx.storeStock.update({
            where: { productId },
            data: {
              quantity: validation.batchTotalQuantity,
              lastUpdated: new Date()
            }
          });
        } else {
          await tx.warehouseStock.update({
            where: { productId },
            data: {
              quantity: validation.batchTotalQuantity,
              lastUpdated: new Date()
            }
          });
        }

        // Create stock history record
        await tx.stockHistory.create({
          data: {
            productId,
            storeStockId: location === 'store' ? 
              (await tx.storeStock.findUnique({ where: { productId }, select: { id: true } }))?.id : null,
            warehouseStockId: location === 'warehouse' ? 
              (await tx.warehouseStock.findUnique({ where: { productId }, select: { id: true } }))?.id : null,
            userId: auth.user.id,
            source: "ADJUSTMENT",
            previousQuantity: validation.stockQuantity,
            newQuantity: validation.batchTotalQuantity,
            changeQuantity: validation.batchTotalQuantity - validation.stockQuantity,
            notes: `Batch integrity reconciliation - stock adjusted to match batch total`,
          },
        });

        return { 
          action: 'reconcile_to_batches', 
          oldStockQuantity: validation.stockQuantity,
          newStockQuantity: validation.batchTotalQuantity
        };
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "FIX_BATCH_INTEGRITY",
        details: `Fixed batch integrity for product ${productId} in ${location}: ${action}`,
      },
    });

    return NextResponse.json({
      message: "Batch integrity issue fixed successfully",
      result,
      validation: await BatchManagementService.validateStockBatchIntegrity(productId, location)
    });

  } catch (error) {
    console.error("Error fixing batch integrity:", error);
    return NextResponse.json(
      { error: "Failed to fix batch integrity", message: (error as Error).message },
      { status: 500 }
    );
  }
}
