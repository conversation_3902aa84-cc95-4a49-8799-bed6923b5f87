import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { getSupplierCostAnalysis } from "@/lib/supplier-cost-calculations";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/inventory/supplier-cost-analysis - Get supplier cost analysis
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view reports
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const supplierId = searchParams.get("supplierId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const reportType = searchParams.get("type") || "single"; // single or comparison

    if (reportType === "single") {
      // Single supplier analysis
      if (!supplierId) {
        return NextResponse.json(
          { error: "Supplier ID is required for single supplier analysis" },
          { status: 400 }
        );
      }

      const startDateObj = startDate ? new Date(startDate) : undefined;
      const endDateObj = endDate ? new Date(endDate) : undefined;

      const analysis = await getSupplierCostAnalysis(supplierId, startDateObj, endDateObj);

      return NextResponse.json({
        reportType: "supplier_cost_analysis",
        analysisType: "single",
        generatedAt: new Date(),
        filters: {
          supplierId,
          startDate,
          endDate
        },
        ...analysis
      });

    } else if (reportType === "comparison") {
      // Multi-supplier comparison
      const dateFilter: any = {};
      if (startDate) dateFilter.gte = new Date(startDate);
      if (endDate) dateFilter.lte = new Date(endDate);

      // Get all suppliers with their cost data
      const suppliers = await prisma.supplier.findMany({
        include: {
          productSuppliers: {
            where: {
              isActive: true
            },
            include: {
              stockBatches: {
                where: Object.keys(dateFilter).length > 0 ? { receivedDate: dateFilter } : {},
                select: {
                  quantity: true,
                  remainingQuantity: true,
                  purchasePrice: true,
                  receivedDate: true,
                  status: true
                }
              },
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  category: {
                    select: {
                      name: true
                    }
                  }
                }
              }
            }
          }
        }
      });

      // Calculate comparison metrics for each supplier
      const supplierComparison = suppliers.map(supplier => {
        const allBatches = supplier.productSuppliers.flatMap(ps => ps.stockBatches);
        
        const totalBatches = allBatches.length;
        const totalQuantityReceived = allBatches.reduce((sum, batch) => sum + Number(batch.quantity), 0);
        const totalValueReceived = allBatches.reduce(
          (sum, batch) => sum + (Number(batch.quantity) * Number(batch.purchasePrice)), 
          0
        );
        const averageCostPerUnit = totalQuantityReceived > 0 ? totalValueReceived / totalQuantityReceived : 0;

        // Current inventory metrics
        const activeBatches = allBatches.filter(batch => batch.status === 'ACTIVE' && Number(batch.remainingQuantity) > 0);
        const currentInventoryQuantity = activeBatches.reduce((sum, batch) => sum + Number(batch.remainingQuantity), 0);
        const currentInventoryValue = activeBatches.reduce(
          (sum, batch) => sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 
          0
        );

        // Product diversity
        const uniqueProducts = new Set(supplier.productSuppliers.map(ps => ps.product.id)).size;
        const productCategories = new Set(
          supplier.productSuppliers.map(ps => ps.product.category?.name || 'Uncategorized')
        ).size;

        // Inventory turnover
        const inventoryTurnover = totalQuantityReceived > 0 
          ? ((totalQuantityReceived - currentInventoryQuantity) / totalQuantityReceived) * 100 
          : 0;

        return {
          supplierId: supplier.id,
          supplierName: supplier.name,
          contactPerson: supplier.contactPerson,
          phone: supplier.phone,
          email: supplier.email,
          metrics: {
            totalBatches,
            totalQuantityReceived,
            totalValueReceived,
            averageCostPerUnit,
            currentInventoryQuantity,
            currentInventoryValue,
            uniqueProducts,
            productCategories,
            inventoryTurnover
          },
          performance: {
            costEfficiency: averageCostPerUnit, // Lower is better
            inventoryTurnover,
            productDiversity: uniqueProducts,
            categoryDiversity: productCategories
          }
        };
      }).filter(supplier => supplier.metrics.totalBatches > 0); // Only include suppliers with data

      // Calculate ranking metrics
      const rankedSuppliers = supplierComparison.map(supplier => {
        // Calculate composite score (you can adjust weights as needed)
        const costScore = supplier.metrics.averageCostPerUnit > 0 ? 1 / supplier.metrics.averageCostPerUnit : 0;
        const turnoverScore = supplier.metrics.inventoryTurnover / 100;
        const diversityScore = (supplier.metrics.uniqueProducts + supplier.metrics.productCategories) / 20; // Normalize
        
        const compositeScore = (costScore * 0.4) + (turnoverScore * 0.3) + (diversityScore * 0.3);

        return {
          ...supplier,
          compositeScore,
          ranking: 0 // Will be set after sorting
        };
      });

      // Sort by composite score and assign rankings
      rankedSuppliers.sort((a, b) => b.compositeScore - a.compositeScore);
      rankedSuppliers.forEach((supplier, index) => {
        supplier.ranking = index + 1;
      });

      // Calculate summary statistics
      const totalSuppliers = rankedSuppliers.length;
      const totalValue = rankedSuppliers.reduce((sum, s) => sum + s.metrics.totalValueReceived, 0);
      const totalQuantity = rankedSuppliers.reduce((sum, s) => sum + s.metrics.totalQuantityReceived, 0);
      const averageInventoryTurnover = totalSuppliers > 0 
        ? rankedSuppliers.reduce((sum, s) => sum + s.metrics.inventoryTurnover, 0) / totalSuppliers 
        : 0;

      return NextResponse.json({
        reportType: "supplier_cost_analysis",
        analysisType: "comparison",
        generatedAt: new Date(),
        filters: {
          startDate,
          endDate
        },
        summary: {
          totalSuppliers,
          totalValue,
          totalQuantity,
          averageInventoryTurnover
        },
        suppliers: rankedSuppliers
      });

    } else {
      return NextResponse.json(
        { error: "Invalid report type. Valid types: single, comparison" },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error("Error generating supplier cost analysis:", error);
    return NextResponse.json(
      { error: "Failed to generate supplier cost analysis", message: (error as Error).message },
      { status: 500 }
    );
  }
}
