import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { BatchManagementService } from "@/lib/batch-management";
import { notify, EVENT_TYPES } from "@/lib/notifications";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/adjustments/[id]/approve - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token using jose
  try {
    const { jwtVerify } = await import("jose");
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    console.log("[APPROVE] JWT payload:", JSON.stringify(payload, null, 2));

    const userId = payload.id;
    const userRole = payload.role;

    console.log("[APPROVE] Extracted userId:", userId);
    console.log("[APPROVE] Extracted userRole:", userRole);

    if (!userId) {
      console.error("[APPROVE] JWT payload missing user ID");
      console.error("[APPROVE] Full payload:", payload);
      return {
        authenticated: false,
        error: "Unauthorized - Invalid token structure",
        status: 403,
        user: null
      };
    }

    return {
      authenticated: true,
      user: {
        id: userId as string,
        name: payload.name as string || "Unknown User",
        email: payload.email as string || "<EMAIL>",
        role: payload.role as string || "CASHIER"
      }
    };
  } catch (error) {
    console.error("JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Schema for approval/rejection
const approvalSchema = z.object({
  action: z.enum(["approve", "reject"]),
  rejectionReason: z.string().optional()
});

// PUT - Approve adjustment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[API] PUT /api/inventory/adjustments/${id}/approve - Starting approval process`);

    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      console.log("[API] Authentication failed");
      return NextResponse.json(
        { error: auth.error || "Unauthorized" },
        { status: auth.status || 403 }
      );
    }

    // Check if user has approval permissions (SUPER_ADMIN only)
    if (auth.user.role !== "SUPER_ADMIN") {
      console.log(`[API] User ${auth.user.email} with role ${auth.user.role} attempted to approve adjustment`);
      return NextResponse.json(
        { error: "Insufficient permissions. Only SUPER_ADMIN can approve adjustments." },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    const validationResult = approvalSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { action, rejectionReason } = validationResult.data;

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Get the adjustment
      const adjustment = await tx.stockAdjustment.findUnique({
        where: { id },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          user: true,
          storeStock: true,
          warehouseStock: true
        }
      });

      if (!adjustment) {
        throw new Error("Adjustment not found");
      }

      // Check if adjustment is in pending status
      if (adjustment.status !== "PENDING_APPROVAL") {
        throw new Error(`Adjustment is not pending approval. Current status: ${adjustment.status}`);
      }

      if (action === "approve") {
        console.log("[APPROVE] Processing approval for adjustment:", adjustment.id);
        console.log("[APPROVE] Adjustment details:", {
          productId: adjustment.productId,
          adjustmentQuantity: adjustment.adjustmentQuantity,
          previousQuantity: adjustment.previousQuantity,
          newQuantity: adjustment.newQuantity,
          storeStockId: adjustment.storeStockId,
          warehouseStockId: adjustment.warehouseStockId
        });

        // Update main stock quantities (StoreStock or WarehouseStock)
        if (adjustment.storeStockId) {
          console.log("[APPROVE] Updating store stock...");
          const updatedStoreStock = await tx.storeStock.update({
            where: { id: adjustment.storeStockId },
            data: {
              quantity: Number(adjustment.newQuantity),
              lastUpdated: new Date()
            }
          });
          console.log("[APPROVE] Store stock updated:", {
            id: updatedStoreStock.id,
            previousQuantity: adjustment.previousQuantity,
            newQuantity: updatedStoreStock.quantity
          });
        } else if (adjustment.warehouseStockId) {
          console.log("[APPROVE] Updating warehouse stock...");
          const updatedWarehouseStock = await tx.warehouseStock.update({
            where: { id: adjustment.warehouseStockId },
            data: {
              quantity: Number(adjustment.newQuantity),
              lastUpdated: new Date()
            }
          });
          console.log("[APPROVE] Warehouse stock updated:", {
            id: updatedWarehouseStock.id,
            previousQuantity: adjustment.previousQuantity,
            newQuantity: updatedWarehouseStock.quantity
          });
        }

        // Process batch adjustments for negative adjustments
        let batchAdjustmentResult = null;
        if (Number(adjustment.adjustmentQuantity) < 0) {
          console.log("[APPROVE] Processing batch adjustments for negative quantity...");
          const location = adjustment.storeStockId ? 'store' : 'warehouse';
          batchAdjustmentResult = await BatchManagementService.processBatchAdjustment(
            adjustment.productId,
            Number(adjustment.adjustmentQuantity),
            location,
            tx,
            {
              source: "ADJUSTMENT",
              referenceId: adjustment.id,
              referenceType: "StockAdjustment",
              notes: `Approved adjustment: ${adjustment.reason}`,
              userId: auth.user.id,
              reason: adjustment.reason
            }
          );

          if (!batchAdjustmentResult.success && batchAdjustmentResult.insufficientStock) {
            throw new Error(`Insufficient batch stock for adjustment. Cannot reduce by ${Math.abs(Number(adjustment.adjustmentQuantity))} units.`);
          }
          console.log("[APPROVE] Batch adjustment result:", batchAdjustmentResult);
        }

        // Create stock history entry for the approval
        console.log("[APPROVE] Creating stock history entry...");
        await tx.stockHistory.create({
          data: {
            productId: adjustment.productId,
            storeStockId: adjustment.storeStockId,
            warehouseStockId: adjustment.warehouseStockId,
            previousQuantity: Number(adjustment.previousQuantity),
            newQuantity: Number(adjustment.newQuantity),
            changeQuantity: Number(adjustment.adjustmentQuantity),
            source: "ADJUSTMENT",
            referenceId: adjustment.id,
            referenceType: "StockAdjustment",
            notes: `Approved adjustment: ${adjustment.reason}`,
            userId: auth.user.id,
            reason: adjustment.reason
          }
        });
        console.log("[APPROVE] Stock history entry created");

        // Update adjustment status to APPROVED and APPLIED
        const updatedAdjustment = await tx.stockAdjustment.update({
          where: { id },
          data: {
            status: "APPLIED",
            approvedById: auth.user.id,
            approvedAt: new Date(),
            notes: batchAdjustmentResult ? 
              `${adjustment.notes || ''} [Batch integration: ${batchAdjustmentResult.batchesAffected} batches affected]`.trim() :
              adjustment.notes
          },
          include: {
            product: {
              include: {
                category: true,
                unit: true
              }
            },
            user: true,
            approvedBy: true
          }
        });

        // Send approval notification to the requesting user
        await notify({
          eventType: EVENT_TYPES.STOCK_ADJUSTMENT_APPROVED,
          sourceId: adjustment.id,
          sourceType: 'stock_adjustment',
          payload: {
            adjustmentId: adjustment.id,
            productName: adjustment.product.name,
            adjustmentQuantity: Number(adjustment.adjustmentQuantity),
            reason: adjustment.reason,
            approvedBy: auth.user.name,
            requestedBy: adjustment.user.name
          }
        });

        return { adjustment: updatedAdjustment, action: "approved" };

      } else if (action === "reject") {
        // Update adjustment status to REJECTED
        const updatedAdjustment = await tx.stockAdjustment.update({
          where: { id },
          data: {
            status: "REJECTED",
            approvedById: auth.user.id,
            rejectedAt: new Date(),
            rejectionReason: rejectionReason || "No reason provided"
          },
          include: {
            product: {
              include: {
                category: true,
                unit: true
              }
            },
            user: true,
            approvedBy: true
          }
        });

        // Send rejection notification to the requesting user
        await notify({
          eventType: EVENT_TYPES.STOCK_ADJUSTMENT_REJECTED,
          sourceId: adjustment.id,
          sourceType: 'stock_adjustment',
          payload: {
            adjustmentId: adjustment.id,
            productName: adjustment.product.name,
            adjustmentQuantity: Number(adjustment.adjustmentQuantity),
            reason: adjustment.reason,
            rejectionReason: rejectionReason || "No reason provided",
            rejectedBy: auth.user.name,
            requestedBy: adjustment.user.name
          }
        });

        return { adjustment: updatedAdjustment, action: "rejected" };
      }
    });

    console.log(`[API] Adjustment ${id} ${result.action} successfully`);
    return NextResponse.json({
      success: true,
      message: `Adjustment ${result.action} successfully`,
      adjustment: result.adjustment
    });

  } catch (error) {
    console.error(`[API] Error processing adjustment approval:`, error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to process adjustment approval" },
      { status: 500 }
    );
  }
}
