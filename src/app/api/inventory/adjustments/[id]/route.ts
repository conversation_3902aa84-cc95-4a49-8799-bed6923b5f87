import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { AdjustmentReason } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/adjustments/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/adjustments/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/adjustments/[id] - Get a specific stock adjustment
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the adjustment
    const adjustment = await prisma.stockAdjustment.findUnique({
      where: { id },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        },
        storeStock: true,
        warehouseStock: true
      }
    });

    if (!adjustment) {
      return NextResponse.json(
        { error: "Adjustment not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ adjustment });
  } catch (error) {
    console.error("Error fetching stock adjustment:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock adjustment", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/inventory/adjustments/[id] - Update a stock adjustment
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id } = params;
    const body = await request.json();

    // Validate update data
    const updateSchema = z.object({
      reason: z.enum([
        "INVENTORY_COUNT",
        "DAMAGED",
        "EXPIRED",
        "THEFT",
        "LOSS",
        "RETURN",
        "CORRECTION",
        "OTHER"
      ]),
      notes: z.string().optional()
    });

    const validationResult = updateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { reason, notes } = validationResult.data;

    // Get the existing adjustment
    const existingAdjustment = await prisma.stockAdjustment.findUnique({
      where: { id },
      include: {
        product: true
      }
    });

    if (!existingAdjustment) {
      return NextResponse.json(
        { error: "Adjustment not found" },
        { status: 404 }
      );
    }

    // Update the adjustment (only reason and notes)
    const updatedAdjustment = await prisma.stockAdjustment.update({
      where: { id },
      data: {
        reason: reason as AdjustmentReason,
        notes
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        }
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_STOCK_ADJUSTMENT",
        details: `Updated stock adjustment for ${existingAdjustment.product.name}: Changed reason to ${reason}${notes ? ' and updated notes' : ''}`,
      }
    });

    return NextResponse.json({ adjustment: updatedAdjustment });
  } catch (error) {
    console.error("Error updating stock adjustment:", error);
    return NextResponse.json(
      { error: "Failed to update stock adjustment", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory/adjustments/[id] - Cancel a stock adjustment
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to cancel adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id } = params;

    // Get the existing adjustment
    const existingAdjustment = await prisma.stockAdjustment.findUnique({
      where: { id },
      include: {
        product: true,
        storeStock: true,
        warehouseStock: true
      }
    });

    if (!existingAdjustment) {
      return NextResponse.json(
        { error: "Adjustment not found" },
        { status: 404 }
      );
    }

    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Reverse the stock adjustment
      if (existingAdjustment.storeStockId) {
        // Reverse store stock adjustment
        const storeStock = await tx.storeStock.findUnique({
          where: { id: existingAdjustment.storeStockId }
        });

        if (!storeStock) {
          throw new Error("Store stock not found");
        }

        // Calculate the reverse adjustment (going back to previous quantity)
        const reverseAdjustment = existingAdjustment.previousQuantity - Number(storeStock.quantity);

        // Update the store stock to the previous quantity
        await tx.storeStock.update({
          where: { id: storeStock.id },
          data: {
            quantity: existingAdjustment.previousQuantity,
            lastUpdated: new Date()
          }
        });

        // Create a new adjustment record for the cancellation
        const cancellationAdjustment = await tx.stockAdjustment.create({
          data: {
            productId: existingAdjustment.productId,
            storeStockId: storeStock.id,
            previousQuantity: Number(storeStock.quantity),
            newQuantity: existingAdjustment.previousQuantity,
            adjustmentQuantity: reverseAdjustment,
            reason: "CORRECTION",
            notes: `Cancellation of adjustment #${id}`,
            userId: auth.user.id
          }
        });

        // Create stock history entry for the cancellation
        await tx.stockHistory.create({
          data: {
            productId: existingAdjustment.productId,
            storeStockId: storeStock.id,
            previousQuantity: Number(storeStock.quantity),
            newQuantity: existingAdjustment.previousQuantity,
            changeQuantity: reverseAdjustment,
            source: "ADJUSTMENT",
            referenceId: cancellationAdjustment.id,
            referenceType: "StockAdjustment",
            notes: `Cancellation of adjustment #${id}`,
            userId: auth.user.id
          }
        });
      } else if (existingAdjustment.warehouseStockId) {
        // Reverse warehouse stock adjustment
        const warehouseStock = await tx.warehouseStock.findUnique({
          where: { id: existingAdjustment.warehouseStockId }
        });

        if (!warehouseStock) {
          throw new Error("Warehouse stock not found");
        }

        // Calculate the reverse adjustment
        const reverseAdjustment = existingAdjustment.previousQuantity - Number(warehouseStock.quantity);

        // Update the warehouse stock to the previous quantity
        await tx.warehouseStock.update({
          where: { id: warehouseStock.id },
          data: {
            quantity: existingAdjustment.previousQuantity,
            lastUpdated: new Date()
          }
        });

        // Create a new adjustment record for the cancellation
        const cancellationAdjustment = await tx.stockAdjustment.create({
          data: {
            productId: existingAdjustment.productId,
            warehouseStockId: warehouseStock.id,
            previousQuantity: Number(warehouseStock.quantity),
            newQuantity: existingAdjustment.previousQuantity,
            adjustmentQuantity: reverseAdjustment,
            reason: "CORRECTION",
            notes: `Cancellation of adjustment #${id}`,
            userId: auth.user.id
          }
        });

        // Create stock history entry for the cancellation
        await tx.stockHistory.create({
          data: {
            productId: existingAdjustment.productId,
            warehouseStockId: warehouseStock.id,
            previousQuantity: Number(warehouseStock.quantity),
            newQuantity: existingAdjustment.previousQuantity,
            changeQuantity: reverseAdjustment,
            source: "ADJUSTMENT",
            referenceId: cancellationAdjustment.id,
            referenceType: "StockAdjustment",
            notes: `Cancellation of adjustment #${id}`,
            userId: auth.user.id
          }
        });
      }

      // Log activity
      await tx.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "CANCEL_STOCK_ADJUSTMENT",
          details: `Cancelled stock adjustment for ${existingAdjustment.product.name} and reversed stock to previous quantity`,
        }
      });

      return { success: true };
    });

    return NextResponse.json({
      message: "Stock adjustment cancelled successfully",
      result
    });
  } catch (error) {
    console.error("Error cancelling stock adjustment:", error);
    return NextResponse.json(
      { error: "Failed to cancel stock adjustment", message: (error as Error).message },
      { status: 500 }
    );
  }
}
