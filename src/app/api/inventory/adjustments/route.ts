import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { z } from "zod";
import { AdjustmentReason } from "@prisma/client";
import { notifyLowStock, notifyOutOfStock, notify, EVENT_TYPES } from "@/lib/notifications";
import { BatchManagementService } from "@/lib/batch-management";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/adjustments - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/adjustments - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/adjustments - Get all stock adjustments
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view adjustments
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get("productId");
    const reason = searchParams.get("reason") as AdjustmentReason | null;
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query
    const query: any = {};
    
    if (productId) {
      query.productId = productId;
    }

    if (reason) {
      query.reason = reason;
    }

    // Get total count for pagination
    const totalCount = await prisma.stockAdjustment.count({
      where: query
    });

    // Get stock adjustments with product details
    // Try with new fields first, fallback to basic query if schema not updated
    let adjustments;
    try {
      adjustments = await prisma.stockAdjustment.findMany({
        where: query,
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          approvedBy: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          storeStock: true,
          warehouseStock: true
        },
        skip,
        take: limit,
        orderBy: {
          date: "desc"
        }
      });
    } catch (error) {
      console.warn("Failed to fetch with new schema, falling back to basic query:", error);
      // Fallback to basic query without new fields
      adjustments = await prisma.stockAdjustment.findMany({
        where: query,
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          },
          storeStock: true,
          warehouseStock: true
        },
        skip,
        take: limit,
        orderBy: {
          date: "desc"
        }
      });

      // Add default status for backward compatibility
      adjustments = adjustments.map(adj => ({
        ...adj,
        status: 'APPLIED', // Default status for existing records
        approvedBy: null,
        approvedById: null,
        approvedAt: null,
        rejectedAt: null,
        rejectionReason: null
      }));
    }

    return NextResponse.json({
      adjustments,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching stock adjustments:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock adjustments", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/adjustments - Create a stock adjustment
export async function POST(request: NextRequest) {
  try {
    console.log("🚀 [BACKEND] POST /api/inventory/adjustments - Starting request processing...");

    // Check authentication
    console.log("🔐 [BACKEND] Checking authentication...");
    const auth = await verifyAuthToken(request);

    console.log("🔐 [BACKEND] Auth result:", {
      authenticated: auth.authenticated,
      userId: auth.authenticated ? auth.user.id : null,
      userRole: auth.authenticated ? auth.user.role : null,
      userEmail: auth.authenticated ? auth.user.email : null,
      error: auth.authenticated ? null : auth.error,
      status: auth.authenticated ? null : auth.status
    });

    if (!auth.authenticated) {
      console.log("❌ [BACKEND] Authentication failed:", auth.error);
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create adjustments
    console.log("🔒 [BACKEND] Checking permissions for role:", auth.user.role);
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    console.log("🔒 [BACKEND] Has permission:", hasPermission);

    if (!hasPermission) {
      console.log("❌ [BACKEND] Insufficient permissions for role:", auth.user.role);
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Determine adjustment status and batch processing based on user role
    const isSuperAdmin = auth.user.role === "SUPER_ADMIN";
    const adjustmentStatus = isSuperAdmin ? "APPLIED" : "PENDING_APPROVAL";
    const shouldProcessBatches = isSuperAdmin; // Only process batches immediately for SUPER_ADMIN

    console.log("👤 [BACKEND] User role processing:", {
      isSuperAdmin,
      adjustmentStatus,
      shouldProcessBatches
    });

    // Get request body
    console.log("📦 [BACKEND] Parsing request body...");
    const body = await request.json();
    console.log("📦 [BACKEND] Request body:", JSON.stringify(body, null, 2));

    // Validate adjustment data
    console.log("✅ [BACKEND] Validating request data...");
    const adjustmentSchema = z.object({
      productId: z.string(),
      locationType: z.enum(["STORE", "WAREHOUSE"]),
      adjustmentQuantity: z.number(),
      reason: z.enum([
        "INVENTORY_COUNT",
        "DAMAGED",
        "EXPIRED",
        "THEFT",
        "LOSS",
        "RETURN",
        "CORRECTION",
        "OTHER"
      ]),
      notes: z.string().optional()
    });

    const validationResult = adjustmentSchema.safeParse(body);
    console.log("✅ [BACKEND] Validation result:", {
      success: validationResult.success,
      data: validationResult.success ? validationResult.data : null,
      errors: validationResult.success ? null : validationResult.error.issues
    });

    if (!validationResult.success) {
      console.log("❌ [BACKEND] Validation failed:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productId, locationType, adjustmentQuantity, reason, notes } = validationResult.data;
    console.log("✅ [BACKEND] Validated data:", {
      productId,
      locationType,
      adjustmentQuantity,
      reason,
      notes: notes || "(none)"
    });

    // Check if product exists
    console.log("🔍 [BACKEND] Looking up product:", productId);
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      console.log("❌ [BACKEND] Product not found:", productId);
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    console.log("✅ [BACKEND] Product found:", {
      id: product.id,
      name: product.name,
      sku: product.sku,
      minThreshold: product.minThreshold
    });

    // Process the adjustment based on location type using database transaction
    console.log("🔄 [BACKEND] Starting database transaction...");
    const result = await prisma.$transaction(async (tx) => {
      console.log("🔄 [BACKEND] Inside transaction for location:", locationType);
      if (locationType === "STORE") {
        console.log("🏪 [BACKEND] Processing STORE adjustment...");

        // Get current store stock
        console.log("🔍 [BACKEND] Looking up store stock for product:", productId);
        const storeStock = await tx.storeStock.findUnique({
          where: { productId }
        });

        if (!storeStock) {
          console.log("❌ [BACKEND] Store stock not found for product:", productId);
          throw new Error("Store stock not found for this product");
        }

        const previousQuantity = Number(storeStock.quantity);
        const newQuantity = Math.max(0, previousQuantity + adjustmentQuantity);

        console.log("📊 [BACKEND] Store stock calculation:", {
          previousQuantity,
          adjustmentQuantity,
          newQuantity,
          storeStockId: storeStock.id
        });

        // Process batch adjustments if this is a reduction (negative adjustment) and user is SUPER_ADMIN
        let batchAdjustmentResult = null;
        if (adjustmentQuantity < 0 && shouldProcessBatches) {
          batchAdjustmentResult = await BatchManagementService.processBatchAdjustment(
            productId,
            adjustmentQuantity,
            'store',
            tx,
            {
              source: "ADJUSTMENT",
              referenceType: "StockAdjustment",
              notes: notes || `Manual adjustment: ${reason}`,
              userId: auth.user.id,
              reason: reason
            }
          );

          if (!batchAdjustmentResult.success && batchAdjustmentResult.insufficientStock) {
            throw new Error(`Insufficient batch stock for adjustment. Cannot reduce by ${Math.abs(adjustmentQuantity)} units.`);
          }
        }

        // Update store stock only if SUPER_ADMIN (immediate application)
        let updatedStoreStock = null;
        if (shouldProcessBatches) {
          updatedStoreStock = await tx.storeStock.update({
            where: { id: storeStock.id },
            data: {
              quantity: newQuantity,
              lastUpdated: new Date()
            }
          });
        }

        return {
          locationType: "STORE",
          storeStock,
          updatedStoreStock,
          previousQuantity,
          newQuantity,
          batchAdjustmentResult
        };
      } else if (locationType === "WAREHOUSE") {
        console.log("🏭 [BACKEND] Processing WAREHOUSE adjustment...");

        // Get current warehouse stock
        console.log("🔍 [BACKEND] Looking up warehouse stock for product:", productId);
        const warehouseStock = await tx.warehouseStock.findUnique({
          where: { productId }
        });

        if (!warehouseStock) {
          console.log("❌ [BACKEND] Warehouse stock not found for product:", productId);
          throw new Error("Warehouse stock not found for this product");
        }

        const previousQuantity = Number(warehouseStock.quantity);
        const newQuantity = Math.max(0, previousQuantity + adjustmentQuantity);

        console.log("📊 [BACKEND] Warehouse stock calculation:", {
          previousQuantity,
          adjustmentQuantity,
          newQuantity,
          warehouseStockId: warehouseStock.id
        });

        // Process batch adjustments if this is a reduction (negative adjustment) and user is SUPER_ADMIN
        let batchAdjustmentResult = null;
        if (adjustmentQuantity < 0 && shouldProcessBatches) {
          batchAdjustmentResult = await BatchManagementService.processBatchAdjustment(
            productId,
            adjustmentQuantity,
            'warehouse',
            tx,
            {
              source: "ADJUSTMENT",
              referenceType: "StockAdjustment",
              notes: notes || `Manual adjustment: ${reason}`,
              userId: auth.user.id,
              reason: reason
            }
          );

          if (!batchAdjustmentResult.success && batchAdjustmentResult.insufficientStock) {
            throw new Error(`Insufficient batch stock for adjustment. Cannot reduce by ${Math.abs(adjustmentQuantity)} units.`);
          }
        }

        // Update warehouse stock only if SUPER_ADMIN (immediate application)
        let updatedWarehouseStock = null;
        if (shouldProcessBatches) {
          updatedWarehouseStock = await tx.warehouseStock.update({
            where: { id: warehouseStock.id },
            data: {
              quantity: newQuantity,
              lastUpdated: new Date()
            }
          });
        }

        return {
          locationType: "WAREHOUSE",
          warehouseStock,
          updatedWarehouseStock,
          previousQuantity,
          newQuantity,
          batchAdjustmentResult
        };
      } else {
        throw new Error("Invalid location type");
      }
    });

    console.log("✅ [BACKEND] Database transaction completed successfully");
    console.log("📊 [BACKEND] Transaction result:", {
      locationType: result.locationType,
      previousQuantity: result.previousQuantity,
      newQuantity: result.newQuantity,
      batchesAffected: result.batchAdjustmentResult?.batchesAffected || 0
    });

    // Handle post-transaction operations based on location type
    if (result.locationType === "STORE") {
      const { storeStock, previousQuantity, newQuantity, batchAdjustmentResult } = result;

      // Check for low stock or out of stock conditions and emit notifications
      const minThreshold = product.minThreshold || 0;

      console.log(`📊 Stock adjustment check for ${product.name}: ${newQuantity} (min: ${minThreshold})`);

      if (newQuantity <= 0) {
        // Product is out of stock
        console.log(`🚨 Out of stock detected after adjustment: ${product.name}`);
        try {
          await notifyOutOfStock(product.id, product.name, {
            sku: product.sku,
            previousQuantity: previousQuantity,
            newQuantity: newQuantity,
            adjustmentQuantity: adjustmentQuantity,
            adjustmentReason: reason,
            adjustmentId: null, // Will be set after adjustment is created
          });
          console.log(`✅ Out of stock notification sent for ${product.name}`);
        } catch (notificationError) {
          console.error(`❌ Failed to send out of stock notification for ${product.name}:`, notificationError);
        }
      } else if (newQuantity <= minThreshold) {
        // Product is low on stock
        console.log(`⚠️ Low stock detected after adjustment: ${product.name} (${newQuantity} <= ${minThreshold})`);
        try {
          await notifyLowStock(product.id, product.name, newQuantity, minThreshold, {
            sku: product.sku,
            previousQuantity: previousQuantity,
            adjustmentQuantity: adjustmentQuantity,
            adjustmentReason: reason,
            stockPercentage: Math.round((newQuantity / minThreshold) * 100),
            adjustmentId: null, // Will be set after adjustment is created
          });
          console.log(`✅ Low stock notification sent for ${product.name}`);
        } catch (notificationError) {
          console.error(`❌ Failed to send low stock notification for ${product.name}:`, notificationError);
        }
      }

      // Create adjustment record
      console.log("📝 [BACKEND] Creating store adjustment record...");
      const adjustment = await prisma.stockAdjustment.create({
        data: {
          productId,
          storeStockId: storeStock.id,
          previousQuantity,
          newQuantity: newQuantity, // Always show calculated new quantity for display
          adjustmentQuantity,
          reason: reason as AdjustmentReason,
          status: adjustmentStatus,
          notes: batchAdjustmentResult ?
            `${notes || ''} [Batch integration: ${batchAdjustmentResult.batchesAffected} batches affected]`.trim() :
            notes,
          userId: auth.user.id,
          ...(isSuperAdmin && { approvedById: auth.user.id, approvedAt: new Date() })
        }
      });

      console.log("✅ [BACKEND] Store adjustment record created:", adjustment.id);

      // Fetch the complete adjustment with relations
      const adjustmentWithRelations = await prisma.stockAdjustment.findUnique({
        where: { id: adjustment.id },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          storeStock: true,
          user: true,
          approvedBy: true
        }
      });

      // Create stock history entry
      await prisma.stockHistory.create({
        data: {
          productId,
          storeStockId: storeStock.id,
          previousQuantity,
          newQuantity: shouldProcessBatches ? newQuantity : previousQuantity, // Stock history shows actual applied quantity
          changeQuantity: shouldProcessBatches ? adjustmentQuantity : 0, // No change if pending approval
          source: "ADJUSTMENT",
          referenceId: adjustment.id,
          referenceType: "StockAdjustment",
          notes: notes || `Adjustment: ${reason}${!shouldProcessBatches ? ' [Pending approval]' : ''}`,
          userId: auth.user.id
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "ADJUST_STORE_STOCK",
          details: `${isSuperAdmin ? 'Adjusted' : 'Requested adjustment for'} store stock for ${product.name} (${product.sku}): ${adjustmentQuantity > 0 ? '+' : ''}${adjustmentQuantity} - Reason: ${reason}${batchAdjustmentResult ? ` [${batchAdjustmentResult.batchesAffected} batches affected]` : ''}${!isSuperAdmin ? ' [Pending approval]' : ''}`,
        }
      });

      // Send notification for pending approval if WAREHOUSE_ADMIN
      if (!isSuperAdmin) {
        await notify({
          eventType: EVENT_TYPES.STOCK_ADJUSTMENT_PENDING,
          sourceId: adjustment.id,
          sourceType: 'stock_adjustment',
          payload: {
            adjustmentId: adjustment.id,
            productName: product.name,
            adjustmentQuantity: adjustmentQuantity,
            reason: reason,
            location: 'store',
            requestedBy: auth.user.name,
            requestedByEmail: auth.user.email
          }
        });
      }

      console.log("✅ [BACKEND] Store adjustment completed successfully");
      console.log("✅ [BACKEND] Adjustment ID:", adjustment.id);
      console.log("✅ [BACKEND] Adjustment status:", adjustment.status);

      return NextResponse.json({
        adjustment: adjustmentWithRelations,
        batchIntegration: batchAdjustmentResult ? {
          batchesAffected: batchAdjustmentResult.batchesAffected,
          totalAdjusted: batchAdjustmentResult.totalAdjusted
        } : null
      });
    } else if (result.locationType === "WAREHOUSE") {
      const { warehouseStock, previousQuantity, newQuantity, batchAdjustmentResult } = result;

      // Create adjustment record
      console.log("📝 [BACKEND] Creating warehouse adjustment record...");
      const adjustment = await prisma.stockAdjustment.create({
        data: {
          productId,
          warehouseStockId: warehouseStock.id,
          previousQuantity,
          newQuantity: newQuantity, // Always show calculated new quantity for display
          adjustmentQuantity,
          reason: reason as AdjustmentReason,
          status: adjustmentStatus,
          notes: batchAdjustmentResult ?
            `${notes || ''} [Batch integration: ${batchAdjustmentResult.batchesAffected} batches affected]`.trim() :
            notes,
          userId: auth.user.id,
          ...(isSuperAdmin && { approvedById: auth.user.id, approvedAt: new Date() })
        }
      });

      console.log("✅ [BACKEND] Warehouse adjustment record created:", adjustment.id);

      // Fetch the complete adjustment with relations
      const adjustmentWithRelations = await prisma.stockAdjustment.findUnique({
        where: { id: adjustment.id },
        include: {
          product: {
            include: {
              category: true,
              unit: true
            }
          },
          warehouseStock: true,
          user: true,
          approvedBy: true
        }
      });

      // Create stock history entry
      await prisma.stockHistory.create({
        data: {
          productId,
          warehouseStockId: warehouseStock.id,
          previousQuantity,
          newQuantity: shouldProcessBatches ? newQuantity : previousQuantity, // Stock history shows actual applied quantity
          changeQuantity: shouldProcessBatches ? adjustmentQuantity : 0, // No change if pending approval
          source: "ADJUSTMENT",
          referenceId: adjustment.id,
          referenceType: "StockAdjustment",
          notes: notes || `Adjustment: ${reason}${!shouldProcessBatches ? ' [Pending approval]' : ''}`,
          userId: auth.user.id
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "ADJUST_WAREHOUSE_STOCK",
          details: `${isSuperAdmin ? 'Adjusted' : 'Requested adjustment for'} warehouse stock for ${product.name} (${product.sku}): ${adjustmentQuantity > 0 ? '+' : ''}${adjustmentQuantity} - Reason: ${reason}${batchAdjustmentResult ? ` [${batchAdjustmentResult.batchesAffected} batches affected]` : ''}${!isSuperAdmin ? ' [Pending approval]' : ''}`,
        }
      });

      // Send notification for pending approval if WAREHOUSE_ADMIN
      if (!isSuperAdmin) {
        await notify({
          eventType: EVENT_TYPES.STOCK_ADJUSTMENT_PENDING,
          sourceId: adjustment.id,
          sourceType: 'stock_adjustment',
          payload: {
            adjustmentId: adjustment.id,
            productName: product.name,
            adjustmentQuantity: adjustmentQuantity,
            reason: reason,
            location: 'warehouse',
            requestedBy: auth.user.name,
            requestedByEmail: auth.user.email
          }
        });
      }

      console.log("✅ [BACKEND] Warehouse adjustment completed successfully");
      console.log("✅ [BACKEND] Adjustment ID:", adjustment.id);
      console.log("✅ [BACKEND] Adjustment status:", adjustment.status);

      return NextResponse.json({
        adjustment: adjustmentWithRelations,
        batchIntegration: batchAdjustmentResult ? {
          batchesAffected: batchAdjustmentResult.batchesAffected,
          totalAdjusted: batchAdjustmentResult.totalAdjusted
        } : null
      });
    } else {
      // This should never happen due to validation in the transaction, but handle it gracefully
      return NextResponse.json(
        { error: "Invalid location type in result" },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error("💥 [BACKEND] Error creating stock adjustment:", error);
    console.error("💥 [BACKEND] Error type:", typeof error);
    console.error("💥 [BACKEND] Error name:", error instanceof Error ? error.name : 'Unknown');
    console.error("💥 [BACKEND] Error message:", error instanceof Error ? error.message : String(error));
    console.error("💥 [BACKEND] Error stack:", error instanceof Error ? error.stack : 'No stack trace');

    // Check if it's a Prisma error
    if (error && typeof error === 'object' && 'code' in error) {
      console.error("💥 [BACKEND] Prisma error code:", (error as any).code);
      console.error("💥 [BACKEND] Prisma error meta:", (error as any).meta);
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
    console.error("💥 [BACKEND] Returning error response:", errorMessage);

    return NextResponse.json(
      { error: "Failed to create stock adjustment", message: errorMessage },
      { status: 500 }
    );
  }
}
