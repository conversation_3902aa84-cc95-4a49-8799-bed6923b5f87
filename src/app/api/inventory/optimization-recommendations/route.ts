import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { InventoryOptimizationEngine } from "@/lib/inventory-optimization";

// GET /api/inventory/optimization-recommendations - Generate inventory optimization recommendations
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view optimization recommendations
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "INVENTORY_MANAGER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get("categoryId");
    const timeRange = searchParams.get("timeRange") as '30days' | '90days' | '6months' || '90days';
    const type = searchParams.get("type"); // Filter by recommendation type
    const priority = searchParams.get("priority"); // Filter by priority

    console.log(`[Optimization API] Generating recommendations for user ${auth.user.id}`);
    console.log(`[Optimization API] Filters: categoryId=${categoryId}, timeRange=${timeRange}, type=${type}, priority=${priority}`);

    // Generate optimization recommendations
    const analysis = await InventoryOptimizationEngine.generateRecommendations(
      categoryId || undefined,
      timeRange
    );

    // Apply additional filters if specified
    let filteredRecommendations = analysis.recommendations;

    if (type) {
      filteredRecommendations = filteredRecommendations.filter(r => r.type === type);
    }

    if (priority) {
      filteredRecommendations = filteredRecommendations.filter(r => r.priority === priority);
    }

    // Update summary based on filtered results
    const filteredSummary = {
      totalRecommendations: filteredRecommendations.length,
      highPriorityCount: filteredRecommendations.filter(r => r.priority === 'high').length,
      estimatedSavings: filteredRecommendations.reduce((sum, r) => sum + r.impact.financial, 0),
      riskMitigation: filteredRecommendations.filter(r => 
        r.type === 'expiry_risk' || r.type === 'overstock'
      ).length
    };

    const response = {
      ...analysis,
      summary: filteredSummary,
      recommendations: filteredRecommendations,
      filters: {
        categoryId,
        timeRange,
        type,
        priority
      },
      generatedAt: new Date().toISOString(),
      generatedBy: auth.user.id
    };

    console.log(`[Optimization API] Generated ${response.recommendations.length} recommendations`);
    console.log(`[Optimization API] High priority: ${response.summary.highPriorityCount}, Estimated savings: ${response.summary.estimatedSavings}`);

    return NextResponse.json(response);

  } catch (error) {
    console.error("Error generating optimization recommendations:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate optimization recommendations", 
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

// POST /api/inventory/optimization-recommendations - Mark recommendation as implemented or dismissed
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to manage recommendations
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "INVENTORY_MANAGER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { recommendationId, action, notes } = body;

    if (!recommendationId || !action) {
      return NextResponse.json(
        { error: "Missing required fields: recommendationId, action" },
        { status: 400 }
      );
    }

    if (!['implemented', 'dismissed', 'in_progress'].includes(action)) {
      return NextResponse.json(
        { error: "Invalid action. Must be 'implemented', 'dismissed', or 'in_progress'" },
        { status: 400 }
      );
    }

    // Store recommendation action in activity logs for tracking
    console.log(`[Optimization API] Recommendation ${recommendationId} marked as ${action} by user ${auth.user.id}`);
    if (notes) {
      console.log(`[Optimization API] Notes: ${notes}`);
    }

    // Log the action for audit trail
    try {
      const { prisma } = await import("@/auth");
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: `RECOMMENDATION_${action.toUpperCase()}`,
          entityType: 'OPTIMIZATION_RECOMMENDATION',
          entityId: recommendationId,
          details: {
            recommendationId,
            action,
            notes: notes || null,
            timestamp: new Date().toISOString()
          }
        }
      });
    } catch (error) {
      console.error("Failed to log recommendation action:", error);
      // Don't fail the request if logging fails
    }

    return NextResponse.json({
      success: true,
      message: `Recommendation ${action} successfully`,
      recommendationId,
      action,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error("Error updating recommendation status:", error);
    return NextResponse.json(
      { 
        error: "Failed to update recommendation status", 
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

// GET /api/inventory/optimization-recommendations/summary - Get quick summary of recommendations
export async function HEAD(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "INVENTORY_MANAGER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Generate quick summary without full analysis
    const analysis = await InventoryOptimizationEngine.generateRecommendations(undefined, '30days');
    
    const summary = {
      totalRecommendations: analysis.summary.totalRecommendations,
      highPriorityCount: analysis.summary.highPriorityCount,
      estimatedSavings: analysis.summary.estimatedSavings,
      topRisks: analysis.insights.topRisks.slice(0, 3),
      lastGenerated: new Date().toISOString()
    };

    return NextResponse.json(summary);

  } catch (error) {
    console.error("Error generating optimization summary:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate optimization summary", 
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
