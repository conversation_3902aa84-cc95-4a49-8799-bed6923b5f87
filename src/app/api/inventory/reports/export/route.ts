import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import * as XLSX from 'xlsx';
import { parse } from 'json2csv';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/reports/export - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/reports/export - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if user has permission to export reports
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "FINANCE_ADMIN", "DEVELOPER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "summary";
    const format = searchParams.get("format") || "xlsx";
    const categoryId = searchParams.get("categoryId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Fetch report data
    const reportData = await fetchReportData(reportType, categoryId, startDate, endDate);

    // Generate file based on requested format
    if (format === "xlsx") {
      return exportToExcel(reportData, reportType);
    } else if (format === "pdf") {
      return exportToPdf(reportData, reportType);
    } else {
      // CSV format (default fallback)
      return exportToCsv(reportData, reportType);
    }
  } catch (error) {
    console.error("Error exporting report:", error);
    return NextResponse.json(
      { error: "Failed to export report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Fetch report data from the reports API
async function fetchReportData(reportType: string, categoryId: string | null, startDate: string | null, endDate: string | null) {
  // Generate the requested report
  switch (reportType) {
    case "summary":
      return await generateSummaryReport(categoryId);
    case "valuation":
      return await generateValuationReport(categoryId);
    case "movement":
      return await generateMovementReport(startDate, endDate, categoryId);
    case "low_stock":
      return await generateLowStockReport(categoryId);
    default:
      throw new Error("Invalid report type");
  }
}

// Export report to Excel format
function exportToExcel(reportData: any, reportType: string) {
  // Prepare data for export based on report type
  let worksheetData: any[] = [];
  let sheetName = "";

  switch (reportType) {
    case "summary":
      worksheetData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        "Store Quantity": product.storeQuantity,
        "Warehouse Quantity": product.warehouseQuantity,
        "Min Threshold": product.minThreshold
      }));
      sheetName = "Inventory Summary";
      break;
    case "valuation":
      worksheetData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        "Purchase Price": product.purchasePrice,
        "Store Quantity": product.storeQuantity,
        "Warehouse Quantity": product.warehouseQuantity,
        "Store Value": product.storeValue,
        "Warehouse Value": product.warehouseValue,
        "Total Value": product.totalValue
      }));
      sheetName = "Inventory Valuation";
      break;
    case "movement":
      // Flatten movement data for Excel
      worksheetData = [];
      reportData.products.forEach((productData: any) => {
        productData.movements.forEach((movement: any) => {
          worksheetData.push({
            "Product Name": productData.product.name,
            "Product SKU": productData.product.sku,
            "Category": productData.product.category,
            "Date": new Date(movement.date).toLocaleString(),
            "Source": movement.source,
            "Previous Quantity": movement.previousQuantity,
            "Change": movement.changeQuantity,
            "New Quantity": movement.newQuantity,
            "User": movement.user,
            "Notes": movement.notes || ""
          });
        });
      });
      sheetName = "Stock Movement";
      break;
    case "low_stock":
      worksheetData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        Supplier: product.supplier || "",
        "Current Quantity": product.currentQuantity,
        "Min Threshold": product.minThreshold,
        "Stock Status": product.stockStatus,
        "Percent Remaining": `${Math.round(product.percentRemaining)}%`,
        "Days Until Stockout": product.daysUntilStockout || "N/A"
      }));
      sheetName = "Low Stock";
      break;
  }

  // Create Excel workbook
  const worksheet = XLSX.utils.json_to_sheet(worksheetData);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

  // Add summary sheet for additional information
  const summaryData: any[] = [];
  summaryData.push({ "Report Type": reportType.replace('_', ' ').toUpperCase() });
  summaryData.push({ "Generated At": new Date(reportData.generatedAt).toLocaleString() });

  // Add report-specific summary data
  if (reportType === "summary") {
    summaryData.push({ "Total Products": reportData.summary.totalProducts });
    summaryData.push({ "Total Store Quantity": reportData.summary.totalStoreQuantity });
    summaryData.push({ "Total Warehouse Quantity": reportData.summary.totalWarehouseQuantity });
    summaryData.push({ "Low Stock Count": reportData.summary.lowStockCount });
    summaryData.push({ "Out of Stock Count": reportData.summary.outOfStockCount });
  } else if (reportType === "valuation") {
    summaryData.push({ "Total Products": reportData.summary.totalProducts });
    summaryData.push({ "Total Store Value": reportData.summary.totalStoreValue });
    summaryData.push({ "Total Warehouse Value": reportData.summary.totalWarehouseValue });
    summaryData.push({ "Total Inventory Value": reportData.summary.totalInventoryValue });
  } else if (reportType === "movement") {
    summaryData.push({ "Period Start": new Date(reportData.period.startDate).toLocaleString() });
    summaryData.push({ "Period End": new Date(reportData.period.endDate).toLocaleString() });
    summaryData.push({ "Total Products": reportData.summary.totalProducts });
    summaryData.push({ "Total Movements": reportData.summary.totalMovements });
    summaryData.push({ "Total In": reportData.summary.totalIn });
    summaryData.push({ "Total Out": reportData.summary.totalOut });
    summaryData.push({ "Net Change": reportData.summary.netChange });
  } else if (reportType === "low_stock") {
    summaryData.push({ "Total Low Stock": reportData.summary.totalLowStock });
    summaryData.push({ "Critical Count": reportData.summary.criticalCount });
    summaryData.push({ "Out of Stock Count": reportData.summary.outOfStockCount });
  }

  const summarySheet = XLSX.utils.json_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");

  // Convert to buffer
  const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

  // Return as downloadable file
  return new NextResponse(buffer, {
    headers: {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename="${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx"`,
    },
  });
}

// Export report to CSV format
function exportToCsv(reportData: any, reportType: string) {
  // Prepare data for export based on report type
  let csvData: any[] = [];

  switch (reportType) {
    case "summary":
      csvData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        StoreQuantity: product.storeQuantity,
        WarehouseQuantity: product.warehouseQuantity,
        MinThreshold: product.minThreshold
      }));
      break;
    case "valuation":
      csvData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        PurchasePrice: product.purchasePrice,
        StoreQuantity: product.storeQuantity,
        WarehouseQuantity: product.warehouseQuantity,
        StoreValue: product.storeValue,
        WarehouseValue: product.warehouseValue,
        TotalValue: product.totalValue
      }));
      break;
    case "movement":
      // Flatten movement data for CSV
      csvData = [];
      reportData.products.forEach((productData: any) => {
        productData.movements.forEach((movement: any) => {
          csvData.push({
            ProductName: productData.product.name,
            ProductSKU: productData.product.sku,
            Category: productData.product.category,
            Date: new Date(movement.date).toLocaleString(),
            Source: movement.source,
            PreviousQuantity: movement.previousQuantity,
            Change: movement.changeQuantity,
            NewQuantity: movement.newQuantity,
            User: movement.user,
            Notes: movement.notes || ""
          });
        });
      });
      break;
    case "low_stock":
      csvData = reportData.products.map((product: any) => ({
        Name: product.name,
        SKU: product.sku,
        Category: product.category,
        Unit: product.unit,
        Supplier: product.supplier || "",
        CurrentQuantity: product.currentQuantity,
        MinThreshold: product.minThreshold,
        StockStatus: product.stockStatus,
        PercentRemaining: `${Math.round(product.percentRemaining)}%`,
        DaysUntilStockout: product.daysUntilStockout || "N/A"
      }));
      break;
  }

  // Convert to CSV
  const csv = parse(csvData, {
    fields: Object.keys(csvData[0] || {})
  });

  // Return as downloadable file
  return new NextResponse(csv, {
    headers: {
      "Content-Type": "text/csv",
      "Content-Disposition": `attachment; filename="${reportType}_report_${new Date().toISOString().split('T')[0]}.csv"`,
    },
  });
}

// Export report to PDF format
async function exportToPdf(reportData: any, reportType: string) {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();

  // Add a page to the document
  const page = pdfDoc.addPage([842, 595]); // A4 landscape

  // Embed the standard font
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Set page margins
  const margin = 50;
  const pageWidth = page.getWidth() - margin * 2;
  const pageHeight = page.getHeight() - margin * 2;

  // Add title
  const reportTitle = `${reportType.replace('_', ' ').toUpperCase()} REPORT`;
  page.drawText(reportTitle, {
    x: margin,
    y: pageHeight - 20,
    size: 16,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  // Add generation date
  page.drawText(`Generated: ${new Date(reportData.generatedAt).toLocaleString()}`, {
    x: margin,
    y: pageHeight - 40,
    size: 10,
    font,
    color: rgb(0.3, 0.3, 0.3),
  });

  // Add summary section
  let summaryY = pageHeight - 70;
  page.drawText("Summary:", {
    x: margin,
    y: summaryY,
    size: 12,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  summaryY -= 20;

  // Add report-specific summary data
  if (reportType === "summary") {
    page.drawText(`Total Products: ${reportData.summary.totalProducts}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Store Quantity: ${reportData.summary.totalStoreQuantity}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Warehouse Quantity: ${reportData.summary.totalWarehouseQuantity}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Low Stock Count: ${reportData.summary.lowStockCount}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Out of Stock Count: ${reportData.summary.outOfStockCount}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
  } else if (reportType === "valuation") {
    page.drawText(`Total Products: ${reportData.summary.totalProducts}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Store Value: ${formatCurrency(reportData.summary.totalStoreValue)}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Warehouse Value: ${formatCurrency(reportData.summary.totalWarehouseValue)}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Inventory Value: ${formatCurrency(reportData.summary.totalInventoryValue)}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
  } else if (reportType === "movement") {
    page.drawText(`Period: ${new Date(reportData.period.startDate).toLocaleDateString()} to ${new Date(reportData.period.endDate).toLocaleDateString()}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Products: ${reportData.summary.totalProducts}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Movements: ${reportData.summary.totalMovements}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total In: ${reportData.summary.totalIn}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Total Out: ${reportData.summary.totalOut}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Net Change: ${reportData.summary.netChange}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
  } else if (reportType === "low_stock") {
    page.drawText(`Total Low Stock: ${reportData.summary.totalLowStock}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Critical Count: ${reportData.summary.criticalCount}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
    summaryY -= 15;
    page.drawText(`Out of Stock Count: ${reportData.summary.outOfStockCount}`, {
      x: margin,
      y: summaryY,
      size: 10,
      font,
      color: rgb(0, 0, 0),
    });
  }

  // Add table header
  summaryY -= 30;
  let columns: { header: string; width: number; property: string | ((item: any) => string) }[] = [];

  // Define columns based on report type
  if (reportType === "summary") {
    columns = [
      { header: 'Name', width: 150, property: 'name' },
      { header: 'SKU', width: 80, property: 'sku' },
      { header: 'Category', width: 100, property: 'category' },
      { header: 'Unit', width: 60, property: 'unit' },
      { header: 'Store Qty', width: 80, property: 'storeQuantity' },
      { header: 'Warehouse Qty', width: 80, property: 'warehouseQuantity' },
      { header: 'Min Threshold', width: 80, property: 'minThreshold' },
    ];
  } else if (reportType === "valuation") {
    columns = [
      { header: 'Name', width: 120, property: 'name' },
      { header: 'SKU', width: 70, property: 'sku' },
      { header: 'Category', width: 80, property: 'category' },
      { header: 'Purchase Price', width: 80, property: (p: any) => formatCurrency(p.purchasePrice) },
      { header: 'Store Qty', width: 60, property: 'storeQuantity' },
      { header: 'Warehouse Qty', width: 60, property: 'warehouseQuantity' },
      { header: 'Store Value', width: 80, property: (p: any) => formatCurrency(p.storeValue) },
      { header: 'Warehouse Value', width: 80, property: (p: any) => formatCurrency(p.warehouseValue) },
      { header: 'Total Value', width: 80, property: (p: any) => formatCurrency(p.totalValue) },
    ];
  } else if (reportType === "low_stock") {
    columns = [
      { header: 'Name', width: 120, property: 'name' },
      { header: 'SKU', width: 70, property: 'sku' },
      { header: 'Category', width: 80, property: 'category' },
      { header: 'Supplier', width: 80, property: 'supplier' },
      { header: 'Current Qty', width: 60, property: 'currentQuantity' },
      { header: 'Min Threshold', width: 60, property: 'minThreshold' },
      { header: 'Status', width: 70, property: 'stockStatus' },
      { header: '% Remaining', width: 70, property: (p: any) => `${Math.round(p.percentRemaining)}%` },
    ];
  } else if (reportType === "movement") {
    // For movement report, we'll just show product summary
    columns = [
      { header: 'Name', width: 120, property: (p: any) => p.product.name },
      { header: 'SKU', width: 70, property: (p: any) => p.product.sku },
      { header: 'Category', width: 80, property: (p: any) => p.product.category },
      { header: 'Total In', width: 70, property: 'totalIn' },
      { header: 'Total Out', width: 70, property: 'totalOut' },
      { header: 'Net Change', width: 70, property: 'netChange' },
      { header: 'Movements', width: 70, property: (p: any) => p.movements.length.toString() },
    ];
  }

  // Calculate total width
  const totalWidth = columns.reduce((sum, col) => sum + col.width, 0);

  // Draw table header
  let x = margin;
  let y = summaryY;

  // Draw header background
  page.drawRectangle({
    x,
    y: y - 5,
    width: totalWidth,
    height: 25,
    color: rgb(0.9, 0.9, 0.9),
    borderColor: rgb(0.7, 0.7, 0.7),
    borderWidth: 1,
  });

  // Draw header text
  columns.forEach(column => {
    page.drawText(column.header, {
      x: x + 5,
      y: y,
      size: 10,
      font: boldFont,
      color: rgb(0, 0, 0),
    });
    x += column.width;
  });

  // Draw table rows
  y -= 30;
  let rowCount = 0;
  const rowHeight = 25;
  const maxRowsPerPage = Math.floor((pageHeight - 150) / rowHeight);

  // Get data based on report type
  const items = reportType === "movement" ? reportData.products : reportData.products;

  for (const item of items) {
    // Check if we need a new page
    if (rowCount >= maxRowsPerPage) {
      // Add a new page
      const newPage = pdfDoc.addPage([842, 595]);

      // Reset position
      y = pageHeight - 40;
      rowCount = 0;

      // Add page header
      newPage.drawText(reportTitle + " (continued)", {
        x: margin,
        y: pageHeight - 20,
        size: 16,
        font: boldFont,
        color: rgb(0, 0, 0),
      });

      // Draw header background
      x = margin;
      newPage.drawRectangle({
        x,
        y: y - 5,
        width: totalWidth,
        height: 25,
        color: rgb(0.9, 0.9, 0.9),
        borderColor: rgb(0.7, 0.7, 0.7),
        borderWidth: 1,
      });

      // Draw header text
      columns.forEach(column => {
        newPage.drawText(column.header, {
          x: x + 5,
          y: y,
          size: 10,
          font: boldFont,
          color: rgb(0, 0, 0),
        });
        x += column.width;
      });

      y -= 30;
    }

    // Get current page
    const currentPage = pdfDoc.getPages()[pdfDoc.getPageCount() - 1];

    // Draw row background (alternating colors)
    currentPage.drawRectangle({
      x: margin,
      y: y - 5,
      width: totalWidth,
      height: rowHeight,
      color: rowCount % 2 === 0 ? rgb(1, 1, 1) : rgb(0.95, 0.95, 0.95),
      borderColor: rgb(0.9, 0.9, 0.9),
      borderWidth: 1,
    });

    // Draw row data
    x = margin;
    columns.forEach(column => {
      const value = typeof column.property === 'function'
        ? column.property(item)
        : item[column.property]?.toString() || '';

      currentPage.drawText(value, {
        x: x + 5,
        y: y,
        size: 9,
        font,
        color: rgb(0, 0, 0),
        maxWidth: column.width - 10,
      });
      x += column.width;
    });

    y -= rowHeight;
    rowCount++;
  }

  // Add footer with page numbers
  const pageCount = pdfDoc.getPageCount();
  for (let i = 0; i < pageCount; i++) {
    const page = pdfDoc.getPage(i);
    page.drawText(`Page ${i + 1} of ${pageCount}`, {
      x: page.getWidth() - margin - 100,
      y: margin / 2,
      size: 8,
      font,
      color: rgb(0.5, 0.5, 0.5),
    });
  }

  // Serialize the PDFDocument to bytes
  const pdfBytes = await pdfDoc.save();

  // Return as downloadable file
  return new NextResponse(pdfBytes, {
    headers: {
      "Content-Type": "application/pdf",
      "Content-Disposition": `attachment; filename="${reportType}_report_${new Date().toISOString().split('T')[0]}.pdf"`,
    },
  });
}

// Helper function to format currency
function formatCurrency(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
}

// Generate a summary report of current inventory levels
async function generateSummaryReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with stock information
  const products = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      storeStock: true,
      warehouseStock: true
    },
    orderBy: {
      name: "asc"
    }
  });

  // Calculate summary statistics
  const totalProducts = products.length;
  const totalStoreQuantity = products.reduce((sum, product) =>
    sum + (product.storeStock ? Number(product.storeStock.quantity) : 0), 0);
  const totalWarehouseQuantity = products.reduce((sum, product) =>
    sum + (product.warehouseStock ? Number(product.warehouseStock.quantity) : 0), 0);
  const lowStockCount = products.filter(product =>
    product.storeStock && Number(product.storeStock.quantity) <= Number(product.storeStock.minThreshold)).length;
  const outOfStockCount = products.filter(product =>
    !product.storeStock || Number(product.storeStock.quantity) <= 0).length;

  // Group by category
  const categoryGroups: any = {};
  products.forEach(product => {
    const categoryName = product.category?.name || "Uncategorized";
    if (!categoryGroups[categoryName]) {
      categoryGroups[categoryName] = {
        count: 0,
        storeQuantity: 0,
        warehouseQuantity: 0
      };
    }

    categoryGroups[categoryName].count++;
    categoryGroups[categoryName].storeQuantity += product.storeStock ? Number(product.storeStock.quantity) : 0;
    categoryGroups[categoryName].warehouseQuantity += product.warehouseStock ? Number(product.warehouseStock.quantity) : 0;
  });

  return {
    reportType: "summary",
    generatedAt: new Date(),
    summary: {
      totalProducts,
      totalStoreQuantity,
      totalWarehouseQuantity,
      lowStockCount,
      outOfStockCount
    },
    categoryBreakdown: Object.entries(categoryGroups).map(([category, data]) => ({
      category,
      ...data
    })),
    products: products.map(product => ({
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      storeQuantity: product.storeStock ? Number(product.storeStock.quantity) : 0,
      warehouseQuantity: product.warehouseStock ? Number(product.warehouseStock.quantity) : 0,
      minThreshold: product.storeStock ? Number(product.storeStock.minThreshold) : 0
    }))
  };
}

// Generate an inventory valuation report
async function generateValuationReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with stock and price information
  const products = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      storeStock: true,
      warehouseStock: true
    },
    orderBy: {
      name: "asc"
    }
  });

  // Calculate valuation
  let totalStoreValue = 0;
  let totalWarehouseValue = 0;
  let totalInventoryValue = 0;

  // Group by category for valuation
  const categoryValuation: any = {};

  // Calculate values for each product
  const productsWithValue = products.map(product => {
    const purchasePrice = Number(product.purchasePrice || 0);
    const storeQuantity = product.storeStock ? Number(product.storeStock.quantity) : 0;
    const warehouseQuantity = product.warehouseStock ? Number(product.warehouseStock.quantity) : 0;

    const storeValue = purchasePrice * storeQuantity;
    const warehouseValue = purchasePrice * warehouseQuantity;
    const totalValue = storeValue + warehouseValue;

    // Add to totals
    totalStoreValue += storeValue;
    totalWarehouseValue += warehouseValue;
    totalInventoryValue += totalValue;

    // Add to category breakdown
    const categoryName = product.category?.name || "Uncategorized";
    if (!categoryValuation[categoryName]) {
      categoryValuation[categoryName] = {
        count: 0,
        storeValue: 0,
        warehouseValue: 0,
        totalValue: 0
      };
    }

    categoryValuation[categoryName].count++;
    categoryValuation[categoryName].storeValue += storeValue;
    categoryValuation[categoryName].warehouseValue += warehouseValue;
    categoryValuation[categoryName].totalValue += totalValue;

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      purchasePrice,
      storeQuantity,
      warehouseQuantity,
      storeValue,
      warehouseValue,
      totalValue
    };
  });

  return {
    reportType: "valuation",
    generatedAt: new Date(),
    summary: {
      totalProducts: products.length,
      totalStoreValue,
      totalWarehouseValue,
      totalInventoryValue
    },
    categoryBreakdown: Object.entries(categoryValuation).map(([category, data]) => ({
      category,
      ...data
    })),
    products: productsWithValue
  };
}

// Generate a stock movement report for a specific period
async function generateMovementReport(startDate: string | null, endDate: string | null, categoryId: string | null) {
  // Parse dates
  const start = startDate ? new Date(startDate) : new Date(new Date().setDate(new Date().getDate() - 30));
  const end = endDate ? new Date(endDate) : new Date();

  // Build the query for stock history
  const historyQuery: any = {
    date: {
      gte: start,
      lte: end
    }
  };

  if (categoryId) {
    historyQuery.product = {
      categoryId
    };
  }

  // Get stock history for the period
  const stockHistory = await prisma.stockHistory.findMany({
    where: historyQuery,
    include: {
      product: {
        include: {
          category: true,
          unit: true
        }
      },
      user: {
        select: {
          id: true,
          name: true,
          role: true
        }
      }
    },
    orderBy: {
      date: "desc"
    }
  });

  // Group by product
  const productMovements: any = {};
  stockHistory.forEach(history => {
    const productId = history.productId;
    if (!productMovements[productId]) {
      productMovements[productId] = {
        product: {
          id: history.product.id,
          name: history.product.name,
          sku: history.product.sku,
          category: history.product.category?.name || "Uncategorized",
          unit: history.product.unit.abbreviation
        },
        movements: [],
        totalIn: 0,
        totalOut: 0,
        netChange: 0
      };
    }

    const changeQuantity = Number(history.changeQuantity);
    productMovements[productId].movements.push({
      date: history.date,
      source: history.source,
      previousQuantity: Number(history.previousQuantity),
      newQuantity: Number(history.newQuantity),
      changeQuantity,
      notes: history.notes,
      user: history.user.name
    });

    // Update totals
    if (changeQuantity > 0) {
      productMovements[productId].totalIn += changeQuantity;
    } else {
      productMovements[productId].totalOut += Math.abs(changeQuantity);
    }
    productMovements[productId].netChange += changeQuantity;
  });

  // Calculate summary statistics
  const totalIn = Object.values(productMovements).reduce((sum: any, product: any) => sum + product.totalIn, 0);
  const totalOut = Object.values(productMovements).reduce((sum: any, product: any) => sum + product.totalOut, 0);
  const netChange = totalIn - totalOut;

  return {
    reportType: "movement",
    generatedAt: new Date(),
    period: {
      startDate: start,
      endDate: end
    },
    summary: {
      totalProducts: Object.keys(productMovements).length,
      totalMovements: stockHistory.length,
      totalIn,
      totalOut,
      netChange
    },
    products: Object.values(productMovements)
  };
}

// Generate a low stock report
async function generateLowStockReport(categoryId: string | null) {
  // Build the query
  const query: any = {
    active: true
  };

  if (categoryId) {
    query.categoryId = categoryId;
  }

  // Get products with stock information
  const products = await prisma.product.findMany({
    where: query,
    include: {
      category: true,
      unit: true,
      supplier: true,
      storeStock: true
    },
    orderBy: {
      name: "asc"
    }
  });

  // Filter to only low stock products
  const lowStockProducts = products.filter(product =>
    product.storeStock && Number(product.storeStock.quantity) <= Number(product.storeStock.minThreshold)
  );

  // Group by category
  const categoryGroups: any = {};
  lowStockProducts.forEach(product => {
    const categoryName = product.category?.name || "Uncategorized";
    if (!categoryGroups[categoryName]) {
      categoryGroups[categoryName] = {
        lowStockCount: 0,
        criticalCount: 0,
        outOfStockCount: 0
      };
    }

    categoryGroups[categoryName].lowStockCount++;

    const quantity = product.storeStock ? Number(product.storeStock.quantity) : 0;
    const threshold = product.storeStock ? Number(product.storeStock.minThreshold) : 0;

    if (quantity <= 0) {
      categoryGroups[categoryName].outOfStockCount++;
    } else if (quantity <= threshold * 0.25) {
      categoryGroups[categoryName].criticalCount++;
    }
  });

  // Add stock status and percentage remaining to each product
  const productsWithStatus = lowStockProducts.map(product => {
    const currentQuantity = product.storeStock ? Number(product.storeStock.quantity) : 0;
    const minThreshold = product.storeStock ? Number(product.storeStock.minThreshold) : 0;

    let stockStatus = "NORMAL";
    let percentRemaining = 100;
    let daysUntilStockout = null;

    if (currentQuantity <= 0) {
      stockStatus = "OUT_OF_STOCK";
      percentRemaining = 0;
    } else if (currentQuantity <= minThreshold * 0.25) {
      stockStatus = "CRITICAL";
      percentRemaining = (currentQuantity / minThreshold) * 100;
    } else if (currentQuantity <= minThreshold) {
      stockStatus = "LOW";
      percentRemaining = (currentQuantity / minThreshold) * 100;
    }

    // Calculate days until stockout (if we have usage data)
    // This is a placeholder - in a real system, you'd calculate based on average daily usage
    if (currentQuantity > 0) {
      daysUntilStockout = Math.round(currentQuantity / 0.5); // Assuming 0.5 units used per day
    }

    return {
      id: product.id,
      name: product.name,
      sku: product.sku,
      category: product.category?.name || "Uncategorized",
      unit: product.unit.abbreviation,
      supplier: product.supplier?.name,
      currentQuantity: product.storeStock ? Number(product.storeStock.quantity) : 0,
      minThreshold: product.storeStock ? Number(product.storeStock.minThreshold) : 0,
      stockStatus,
      percentRemaining,
      daysUntilStockout
    };
  });

  return {
    reportType: "low_stock",
    generatedAt: new Date(),
    summary: {
      totalLowStock: lowStockProducts.length,
      criticalCount: productsWithStatus.filter(p => p.stockStatus === "CRITICAL").length,
      outOfStockCount: productsWithStatus.filter(p => p.stockStatus === "OUT_OF_STOCK").length
    },
    categoryBreakdown: Object.entries(categoryGroups).map(([category, data]) => ({
      category,
      ...data
    })),
    products: productsWithStatus
  };
}
