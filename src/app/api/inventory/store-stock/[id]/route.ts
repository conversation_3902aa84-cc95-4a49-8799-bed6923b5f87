import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/store-stock/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/store-stock/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/store-stock/[id] - Get a specific store stock item
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    if (!auth.user || !["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get store stock item
    const storeStock = await prisma.storeStock.findUnique({
      where: { id: params.id },
      include: {
        product: {
          include: {
            category: true,
            unit: true,
            supplier: true
          }
        }
      }
    });

    if (!storeStock) {
      return NextResponse.json(
        { error: "Store stock not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ storeStock });
  } catch (error) {
    console.error("Error fetching store stock:", error);
    return NextResponse.json(
      { error: "Failed to fetch store stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/inventory/store-stock/[id] - Update a store stock item
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update inventory
    if (!auth.user || !["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get store stock item
    const storeStock = await prisma.storeStock.findUnique({
      where: { id: params.id },
      include: {
        product: true
      }
    });

    if (!storeStock) {
      return NextResponse.json(
        { error: "Store stock not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate store stock data
    const storeStockSchema = z.object({
      quantity: z.number().min(0),
      minThreshold: z.number().min(0),
      maxThreshold: z.number().optional().nullable(),
    });

    const validationResult = storeStockSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { quantity, minThreshold, maxThreshold } = validationResult.data;
    const previousQuantity = Number(storeStock.quantity);

    // Update store stock
    const updatedStoreStock = await prisma.storeStock.update({
      where: { id: params.id },
      data: {
        quantity,
        minThreshold,
        maxThreshold,
        lastUpdated: new Date()
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        }
      }
    });

    // Create stock history entry if user is authenticated
    if (auth.user) {
      await prisma.stockHistory.create({
        data: {
          productId: storeStock.productId,
          storeStockId: storeStock.id,
          previousQuantity,
          newQuantity: quantity,
          changeQuantity: quantity - previousQuantity,
          source: "ADJUSTMENT",
          notes: "Manual stock update",
          userId: auth.user.id,
        }
      });

      // Log activity
      await prisma.activityLog.create({
        data: {
          userId: auth.user.id,
          action: "UPDATE_STORE_STOCK",
          details: `Updated store stock for ${storeStock.product.name} (${storeStock.product.sku}): quantity=${quantity}, minThreshold=${minThreshold}`,
        }
      });
    }

    return NextResponse.json({ storeStock: updatedStoreStock });
  } catch (error) {
    console.error("Error updating store stock:", error);
    return NextResponse.json(
      { error: "Failed to update store stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}
