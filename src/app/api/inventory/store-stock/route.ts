import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/store-stock - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/store-stock - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/inventory/store-stock - Get all store stock
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const productId = searchParams.get("productId");
    const lowStock = searchParams.get("lowStock") === "true";
    const limit = parseInt(searchParams.get("limit") || "100");
    const page = parseInt(searchParams.get("page") || "1");
    const skip = (page - 1) * limit;

    // Build the query
    const query: any = {};
    
    if (productId) {
      query.productId = productId;
    }

    if (lowStock) {
      query.quantity = {
        lte: prisma.storeStock.fields.minThreshold
      };
    }

    // Get total count for pagination
    const totalCount = await prisma.storeStock.count({
      where: query
    });

    // Get store stock with product details and batch information
    const storeStock = await prisma.storeStock.findMany({
      where: query,
      include: {
        product: {
          include: {
            category: true,
            unit: true,
            productSuppliers: {
              include: {
                supplier: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          }
        },
        // Include batch count for this store stock
        _count: {
          select: {
            stockBatches: {
              where: {
                quantity: {
                  gt: 0
                }
              }
            }
          }
        }
      },
      skip,
      take: limit,
      orderBy: {
        lastUpdated: "desc"
      }
    });

    // Get batch details for each product
    const storeStockWithBatches = await Promise.all(
      storeStock.map(async (stock) => {
        // Get batches linked to this store stock OR all batches for this product if no store-specific batches exist
        const storeSpecificBatches = await prisma.stockBatch.findMany({
          where: {
            storeStockId: stock.id,
            quantity: {
              gt: 0
            }
          },
          include: {
            productSupplier: {
              include: {
                supplier: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            expiryDate: "asc"
          },
          take: 5 // Limit to first 5 batches for performance
        });

        // If no store-specific batches, get all batches for this product (fallback for data integrity issues)
        let batches = storeSpecificBatches;
        let batchCount = stock._count.stockBatches;

        if (storeSpecificBatches.length === 0) {
          const allProductBatches = await prisma.stockBatch.findMany({
            where: {
              productId: stock.productId,
              quantity: {
                gt: 0
              }
            },
            include: {
              productSupplier: {
                include: {
                  supplier: {
                    select: {
                      id: true,
                      name: true,
                    },
                  },
                },
              },
            },
            orderBy: {
              expiryDate: "asc"
            },
            take: 5 // Limit to first 5 batches for performance
          });

          // Count all batches for this product
          const totalBatchCount = await prisma.stockBatch.count({
            where: {
              productId: stock.productId,
              quantity: {
                gt: 0
              }
            }
          });

          batches = allProductBatches;
          batchCount = totalBatchCount;
        }

        const earliestExpiryDate = batches.length > 0 && batches[0].expiryDate
          ? batches[0].expiryDate
          : null;



        return {
          ...stock,
          batchCount: batchCount,
          batches: batches,
          earliestExpiryDate,
        };
      })
    );

    return NextResponse.json({
      storeStock: storeStockWithBatches,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error("Error fetching store stock:", error);
    return NextResponse.json(
      { error: "Failed to fetch store stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/inventory/store-stock - Create or update store stock
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate store stock data
    const storeStockSchema = z.object({
      productId: z.string(),
      quantity: z.number().min(0),
      minThreshold: z.number().min(0),
      maxThreshold: z.number().optional().nullable(),
    });

    const validationResult = storeStockSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productId, quantity, minThreshold, maxThreshold } = validationResult.data;

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Create or update store stock
    const storeStock = await prisma.storeStock.upsert({
      where: { productId },
      update: {
        quantity,
        minThreshold,
        maxThreshold,
        lastUpdated: new Date()
      },
      create: {
        productId,
        quantity,
        minThreshold,
        maxThreshold: maxThreshold || null,
        lastUpdated: new Date()
      },
      include: {
        product: {
          include: {
            category: true,
            unit: true
          }
        }
      }
    });

    // Create stock history entry
    await prisma.stockHistory.create({
      data: {
        productId,
        storeStockId: storeStock.id,
        previousQuantity: 0, // For new entries
        newQuantity: quantity,
        changeQuantity: quantity,
        source: "INITIAL",
        notes: "Initial stock setup",
        userId: auth.user.id,
      }
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_STORE_STOCK",
        details: `Updated store stock for ${product.name} (${product.sku}): quantity=${quantity}, minThreshold=${minThreshold}`,
      }
    });

    return NextResponse.json({ storeStock });
  } catch (error) {
    console.error("Error updating store stock:", error);
    return NextResponse.json(
      { error: "Failed to update store stock", message: (error as Error).message },
      { status: 500 }
    );
  }
}
