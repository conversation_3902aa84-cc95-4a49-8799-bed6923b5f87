import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/auth';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/partial-receipts - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/partial-receipts - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Query parameters schema for filtering partial receipts
const partialReceiptQuerySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('20'),
  search: z.string().optional(),
  supplierId: z.string().optional(),
  sortBy: z.enum(['orderDate', 'total', 'supplier', 'lastReceived']).optional().default('orderDate'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export async function GET(request: NextRequest) {
  try {
    console.log("[API] GET /api/inventory/partial-receipts - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view partial receipts
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = partialReceiptQuerySchema.parse(queryParams);

    const page = parseInt(validatedQuery.page);
    const limit = parseInt(validatedQuery.limit);
    const offset = (page - 1) * limit;

    // Build where clause for filtering partially received POs
    const whereClause: any = {
      status: 'PARTIALLY_RECEIVED',
    };

    // Filter by search term (PO number, supplier name)
    if (validatedQuery.search) {
      whereClause.OR = [
        {
          id: {
            contains: validatedQuery.search,
            mode: 'insensitive',
          },
        },
        {
          supplier: {
            name: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
        },
      ];
    }

    // Filter by supplier
    if (validatedQuery.supplierId) {
      whereClause.supplierId = validatedQuery.supplierId;
    }

    // Build order by clause
    let orderBy: any = {};
    switch (validatedQuery.sortBy) {
      case 'orderDate':
        orderBy = { orderDate: validatedQuery.sortOrder };
        break;
      case 'total':
        orderBy = { total: validatedQuery.sortOrder };
        break;
      case 'supplier':
        orderBy = { supplier: { name: validatedQuery.sortOrder } };
        break;
      case 'lastReceived':
        orderBy = { receivedAt: validatedQuery.sortOrder };
        break;
      default:
        orderBy = { orderDate: 'desc' };
    }

    console.log("[API] Fetching partial receipts with filters:", { whereClause, orderBy, offset, limit });

    // Fetch partially received purchase orders with comprehensive data
    const [partialReceipts, totalCount] = await Promise.all([
      prisma.purchaseOrder.findMany({
        where: whereClause,
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
              phone: true,
              email: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            include: {
              product: {
                select: {
                  id: true,
                  name: true,
                  sku: true,
                  unit: {
                    select: {
                      name: true,
                      abbreviation: true,
                    },
                  },
                },
              },
            },
          },
          // Get the most recent receipt for this PO
          receivings: {
            orderBy: {
              receivedAt: 'desc',
            },
            take: 1,
            include: {
              receivedBy: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
        orderBy,
        skip: offset,
        take: limit,
      }),
      prisma.purchaseOrder.count({
        where: whereClause,
      }),
    ]);

    console.log("[API] Found", partialReceipts.length, "partial receipts out of", totalCount, "total");

    // Calculate additional metrics for each partial receipt
    const enrichedPartialReceipts = partialReceipts.map((po) => {
      const totalItemsOrdered = po.items.reduce(
        (sum, item) => sum + Number(item.quantity),
        0
      );

      const totalItemsReceived = po.items.reduce(
        (sum, item) => sum + Number(item.receivedQuantity),
        0
      );

      const totalItemsPending = totalItemsOrdered - totalItemsReceived;

      const fulfillmentPercentage = totalItemsOrdered > 0 
        ? Math.round((totalItemsReceived / totalItemsOrdered) * 100) 
        : 0;

      // Calculate pending items details
      const pendingItems = po.items.filter(item => 
        Number(item.receivedQuantity) < Number(item.quantity)
      ).map(item => ({
        ...item,
        pendingQuantity: Number(item.quantity) - Number(item.receivedQuantity),
      }));

      const lastReceiving = po.receivings[0] || null;

      return {
        ...po,
        metrics: {
          totalItemsOrdered,
          totalItemsReceived,
          totalItemsPending,
          fulfillmentPercentage,
          pendingItemsCount: pendingItems.length,
          totalPendingValue: pendingItems.reduce(
            (sum, item) => sum + (item.pendingQuantity * Number(item.unitPrice)),
            0
          ),
        },
        pendingItems,
        lastReceiving,
        daysSinceLastReceiving: lastReceiving 
          ? Math.floor((Date.now() - new Date(lastReceiving.receivedAt).getTime()) / (1000 * 60 * 60 * 24))
          : null,
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      partialReceipts: enrichedPartialReceipts,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching partial receipts:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
