import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/auth';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/receipt-history - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/receipt-history - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Query parameters schema for filtering receipt history
const receiptHistoryQuerySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('20'),
  search: z.string().optional(),
  supplierId: z.string().optional(),
  productId: z.string().optional(),
  purchaseOrderId: z.string().optional(),
  receivedById: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.enum(['RECEIVED', 'PARTIALLY_RECEIVED']).optional(),
  sortBy: z.enum(['receivedAt', 'total', 'supplier', 'items']).optional().default('receivedAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

export async function GET(request: NextRequest) {
  try {
    console.log("[API] GET /api/inventory/receipt-history - Start");

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view receipt history
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = receiptHistoryQuerySchema.parse(queryParams);

    const page = parseInt(validatedQuery.page);
    const limit = parseInt(validatedQuery.limit);
    const offset = (page - 1) * limit;

    // Build where clause for filtering
    const whereClause: any = {};

    // Filter by search term (PO number, supplier name, product name)
    if (validatedQuery.search) {
      whereClause.OR = [
        {
          purchaseOrder: {
            id: {
              contains: validatedQuery.search,
              mode: 'insensitive',
            },
          },
        },
        {
          purchaseOrder: {
            supplier: {
              name: {
                contains: validatedQuery.search,
                mode: 'insensitive',
              },
            },
          },
        },
        {
          items: {
            some: {
              purchaseOrderItem: {
                product: {
                  name: {
                    contains: validatedQuery.search,
                    mode: 'insensitive',
                  },
                },
              },
            },
          },
        },
      ];
    }

    // Filter by supplier
    if (validatedQuery.supplierId) {
      whereClause.purchaseOrder = {
        ...whereClause.purchaseOrder,
        supplierId: validatedQuery.supplierId,
      };
    }

    // Filter by purchase order
    if (validatedQuery.purchaseOrderId) {
      whereClause.purchaseOrderId = validatedQuery.purchaseOrderId;
    }

    // Filter by received by user
    if (validatedQuery.receivedById) {
      whereClause.receivedById = validatedQuery.receivedById;
    }

    // Filter by date range
    if (validatedQuery.startDate || validatedQuery.endDate) {
      whereClause.receivedAt = {};
      if (validatedQuery.startDate) {
        whereClause.receivedAt.gte = new Date(validatedQuery.startDate);
      }
      if (validatedQuery.endDate) {
        whereClause.receivedAt.lte = new Date(validatedQuery.endDate);
      }
    }

    // Filter by PO status (to show only received or partially received)
    if (validatedQuery.status) {
      whereClause.purchaseOrder = {
        ...whereClause.purchaseOrder,
        status: validatedQuery.status,
      };
    }

    // Build order by clause
    let orderBy: any = {};
    switch (validatedQuery.sortBy) {
      case 'receivedAt':
        orderBy = { receivedAt: validatedQuery.sortOrder };
        break;
      case 'total':
        orderBy = { purchaseOrder: { total: validatedQuery.sortOrder } };
        break;
      case 'supplier':
        orderBy = { purchaseOrder: { supplier: { name: validatedQuery.sortOrder } } };
        break;
      case 'items':
        orderBy = { items: { _count: validatedQuery.sortOrder } };
        break;
      default:
        orderBy = { receivedAt: 'desc' };
    }

    console.log("[API] Fetching receipt history with filters:", { whereClause, orderBy, offset, limit });

    // Fetch receipt history with comprehensive data
    const [receipts, totalCount] = await Promise.all([
      prisma.purchaseOrderReceiving.findMany({
        where: whereClause,
        include: {
          purchaseOrder: {
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true,
                  phone: true,
                  email: true,
                },
              },
              items: {
                select: {
                  id: true,
                  quantity: true,
                  receivedQuantity: true,
                  unitPrice: true,
                  product: {
                    select: {
                      id: true,
                      name: true,
                      sku: true,
                      unit: {
                        select: {
                          name: true,
                          abbreviation: true,
                        },
                      },
                    },
                  },
                },
              },
            },
          },
          receivedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          items: {
            select: {
              id: true,
              receivedQuantity: true,
              discrepancyQuantity: true,
              discrepancyReason: true,
              notes: true,
              purchaseOrderItemId: true,
            },
          },
        },
        orderBy,
        skip: offset,
        take: limit,
      }),
      prisma.purchaseOrderReceiving.count({
        where: whereClause,
      }),
    ]);

    console.log("[API] Found", receipts.length, "receipts out of", totalCount, "total");

    // Calculate additional metrics for each receipt
    const enrichedReceipts = receipts.map((receipt) => {
      const totalItemsReceived = receipt.items.reduce(
        (sum, item) => sum + Number(item.receivedQuantity),
        0
      );

      const totalItemsOrdered = receipt.purchaseOrder.items.reduce(
        (sum, item) => sum + Number(item.quantity),
        0
      );

      const totalDiscrepancies = receipt.items.reduce(
        (sum, item) => sum + Math.abs(Number(item.discrepancyQuantity)),
        0
      );

      const hasDiscrepancies = totalDiscrepancies > 0;

      // Calculate batch information if available
      const batchesCreated = receipt.items.filter(item => 
        item.notes && item.notes.includes('Batch:')
      ).length;

      return {
        ...receipt,
        metrics: {
          totalItemsReceived,
          totalItemsOrdered,
          totalDiscrepancies,
          hasDiscrepancies,
          batchesCreated,
          fulfillmentPercentage: totalItemsOrdered > 0 
            ? Math.round((totalItemsReceived / totalItemsOrdered) * 100) 
            : 0,
        },
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      receipts: enrichedReceipts,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching receipt history:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid query parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
