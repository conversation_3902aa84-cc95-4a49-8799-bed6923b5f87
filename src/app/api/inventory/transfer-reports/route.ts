import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const supplierId = searchParams.get("supplierId");
    const status = searchParams.get("status");
    const productId = searchParams.get("productId");

    // Build date filter
    const dateFilter: any = {};
    if (startDate) {
      dateFilter.gte = new Date(startDate);
    }
    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      dateFilter.lte = end;
    }

    // Build where clause
    const where: any = {};
    
    if (Object.keys(dateFilter).length > 0) {
      where.date = dateFilter;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (productId) {
      where.productId = productId;
    }

    // Get transfers with supplier information
    const transfers = await prisma.simpleStockTransfer.findMany({
      where,
      include: {
        product: {
          include: {
            category: true,
            unit: true,
            productSuppliers: {
              where: supplierId ? { supplierId } : {},
              include: {
                supplier: {
                  select: {
                    id: true,
                    name: true,
                    contactPerson: true,
                  }
                }
              }
            }
          }
        },
        requestedBy: {
          select: {
            id: true,
            name: true,
            role: true
          }
        },
        approvedBy: {
          select: {
            id: true,
            name: true,
            role: true
          }
        }
      },
      orderBy: {
        date: "desc"
      }
    });

    // Calculate summary statistics
    const totalTransfers = transfers.length;
    const completedTransfers = transfers.filter(t => t.status === "COMPLETED").length;
    const pendingTransfers = transfers.filter(t => t.status === "PENDING").length;
    const approvedTransfers = transfers.filter(t => t.status === "APPROVED").length;

    // Group by supplier
    const supplierBreakdown = new Map();
    
    transfers.forEach(transfer => {
      const suppliers = transfer.product.productSuppliers;
      
      if (suppliers.length === 0) {
        // Handle products without suppliers
        const key = "No Supplier";
        if (!supplierBreakdown.has(key)) {
          supplierBreakdown.set(key, {
            supplierId: null,
            supplierName: "No Supplier",
            transferCount: 0,
            totalQuantity: 0,
            completedCount: 0,
            pendingCount: 0,
            approvedCount: 0
          });
        }
        
        const data = supplierBreakdown.get(key);
        data.transferCount++;
        data.totalQuantity += Number(transfer.quantity);
        
        if (transfer.status === "COMPLETED") data.completedCount++;
        else if (transfer.status === "PENDING") data.pendingCount++;
        else if (transfer.status === "APPROVED") data.approvedCount++;
      } else {
        // Handle products with suppliers
        suppliers.forEach(productSupplier => {
          const key = productSupplier.supplier.id;
          if (!supplierBreakdown.has(key)) {
            supplierBreakdown.set(key, {
              supplierId: productSupplier.supplier.id,
              supplierName: productSupplier.supplier.name,
              contactPerson: productSupplier.supplier.contactPerson,
              transferCount: 0,
              totalQuantity: 0,
              completedCount: 0,
              pendingCount: 0,
              approvedCount: 0
            });
          }
          
          const data = supplierBreakdown.get(key);
          data.transferCount++;
          data.totalQuantity += Number(transfer.quantity);
          
          if (transfer.status === "COMPLETED") data.completedCount++;
          else if (transfer.status === "PENDING") data.pendingCount++;
          else if (transfer.status === "APPROVED") data.approvedCount++;
        });
      }
    });

    // Convert map to array
    const supplierBreakdownArray = Array.from(supplierBreakdown.values());

    // Format transfers for response
    const formattedTransfers = transfers.map(transfer => ({
      id: transfer.id,
      date: transfer.date,
      productId: transfer.productId,
      productName: transfer.product.name,
      productSku: transfer.product.sku,
      category: transfer.product.category?.name || "Uncategorized",
      unit: transfer.product.unit.abbreviation,
      quantity: Number(transfer.quantity),
      fromStore: transfer.fromStore,
      toStore: transfer.toStore,
      status: transfer.status,
      notes: transfer.notes,
      requestedBy: transfer.requestedBy,
      approvedBy: transfer.approvedBy,
      completedAt: transfer.completedAt,
      suppliers: transfer.product.productSuppliers.map(ps => ({
        id: ps.supplier.id,
        name: ps.supplier.name,
        contactPerson: ps.supplier.contactPerson,
        isPreferred: ps.isPreferred,
        purchasePrice: Number(ps.purchasePrice)
      }))
    }));

    return NextResponse.json({
      reportType: "transfer",
      generatedAt: new Date(),
      filters: {
        startDate,
        endDate,
        supplierId,
        status,
        productId
      },
      summary: {
        totalTransfers,
        completedTransfers,
        pendingTransfers,
        approvedTransfers,
        completionRate: totalTransfers > 0 ? (completedTransfers / totalTransfers) * 100 : 0
      },
      supplierBreakdown: supplierBreakdownArray,
      transfers: formattedTransfers
    });

  } catch (error) {
    console.error("Error generating transfer report:", error);
    return NextResponse.json(
      { error: "Failed to generate transfer report", message: (error as Error).message },
      { status: 500 }
    );
  }
}
