import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import * as XLSX from "xlsx";
import { Parser } from "json2csv";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const format = searchParams.get("format") || "xlsx";
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const supplierId = searchParams.get("supplierId");
    const status = searchParams.get("status");
    const productId = searchParams.get("productId");

    // Fetch report data
    const reportData = await fetchTransferReportData(startDate, endDate, supplierId, status, productId);

    // Generate file based on requested format
    if (format === "xlsx") {
      return exportToExcel(reportData);
    } else {
      // CSV format (default fallback)
      return exportToCsv(reportData);
    }
  } catch (error) {
    console.error("Error exporting transfer report:", error);
    return NextResponse.json(
      { error: "Failed to export transfer report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Fetch transfer report data
async function fetchTransferReportData(
  startDate: string | null,
  endDate: string | null,
  supplierId: string | null,
  status: string | null,
  productId: string | null
) {
  // Build date filter
  const dateFilter: any = {};
  if (startDate) {
    dateFilter.gte = new Date(startDate);
  }
  if (endDate) {
    const end = new Date(endDate);
    end.setHours(23, 59, 59, 999);
    dateFilter.lte = end;
  }

  // Build where clause
  const where: any = {};
  
  if (Object.keys(dateFilter).length > 0) {
    where.date = dateFilter;
  }
  
  if (status) {
    where.status = status;
  }
  
  if (productId) {
    where.productId = productId;
  }

  // Get transfers with supplier information
  const transfers = await prisma.simpleStockTransfer.findMany({
    where,
    include: {
      product: {
        include: {
          category: true,
          unit: true,
          productSuppliers: {
            where: supplierId ? { supplierId } : {},
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true,
                }
              }
            }
          }
        }
      },
      requestedBy: {
        select: {
          id: true,
          name: true,
          role: true
        }
      },
      approvedBy: {
        select: {
          id: true,
          name: true,
          role: true
        }
      }
    },
    orderBy: {
      date: "desc"
    }
  });

  // Calculate summary statistics
  const totalTransfers = transfers.length;
  const completedTransfers = transfers.filter(t => t.status === "COMPLETED").length;
  const pendingTransfers = transfers.filter(t => t.status === "PENDING").length;
  const approvedTransfers = transfers.filter(t => t.status === "APPROVED").length;

  // Group by supplier
  const supplierBreakdown = new Map();
  
  transfers.forEach(transfer => {
    const suppliers = transfer.product.productSuppliers;
    
    if (suppliers.length === 0) {
      // Handle products without suppliers
      const key = "No Supplier";
      if (!supplierBreakdown.has(key)) {
        supplierBreakdown.set(key, {
          supplierId: null,
          supplierName: "No Supplier",
          transferCount: 0,
          totalQuantity: 0,
          completedCount: 0,
          pendingCount: 0,
          approvedCount: 0
        });
      }
      
      const breakdown = supplierBreakdown.get(key);
      breakdown.transferCount++;
      breakdown.totalQuantity += Number(transfer.quantity);
      
      if (transfer.status === "COMPLETED") breakdown.completedCount++;
      else if (transfer.status === "PENDING") breakdown.pendingCount++;
      else if (transfer.status === "APPROVED") breakdown.approvedCount++;
    } else {
      suppliers.forEach(ps => {
        const key = ps.supplier.id;
        if (!supplierBreakdown.has(key)) {
          supplierBreakdown.set(key, {
            supplierId: ps.supplier.id,
            supplierName: ps.supplier.name,
            contactPerson: ps.supplier.contactPerson,
            transferCount: 0,
            totalQuantity: 0,
            completedCount: 0,
            pendingCount: 0,
            approvedCount: 0
          });
        }
        
        const breakdown = supplierBreakdown.get(key);
        breakdown.transferCount++;
        breakdown.totalQuantity += Number(transfer.quantity);
        
        if (transfer.status === "COMPLETED") breakdown.completedCount++;
        else if (transfer.status === "PENDING") breakdown.pendingCount++;
        else if (transfer.status === "APPROVED") breakdown.approvedCount++;
      });
    }
  });

  const supplierBreakdownArray = Array.from(supplierBreakdown.values());

  // Format transfers for response
  const formattedTransfers = transfers.map(transfer => ({
    id: transfer.id,
    date: transfer.date,
    productId: transfer.productId,
    productName: transfer.product.name,
    productSku: transfer.product.sku,
    category: transfer.product.category?.name || "Uncategorized",
    unit: transfer.product.unit.abbreviation,
    quantity: Number(transfer.quantity),
    fromStore: transfer.fromStore,
    toStore: transfer.toStore,
    status: transfer.status,
    notes: transfer.notes,
    requestedBy: transfer.requestedBy,
    approvedBy: transfer.approvedBy,
    completedAt: transfer.completedAt,
    suppliers: transfer.product.productSuppliers.map(ps => ({
      id: ps.supplier.id,
      name: ps.supplier.name,
      contactPerson: ps.supplier.contactPerson,
      isPreferred: ps.isPreferred,
      purchasePrice: Number(ps.purchasePrice)
    }))
  }));

  return {
    reportType: "transfer",
    generatedAt: new Date(),
    filters: {
      startDate,
      endDate,
      supplierId,
      status,
      productId
    },
    summary: {
      totalTransfers,
      completedTransfers,
      pendingTransfers,
      approvedTransfers,
      completionRate: totalTransfers > 0 ? (completedTransfers / totalTransfers) * 100 : 0
    },
    supplierBreakdown: supplierBreakdownArray,
    transfers: formattedTransfers
  };
}

// Export to Excel format
function exportToExcel(data: any) {
  const workbook = XLSX.utils.book_new();

  // Main transfers data
  const transfersData = data.transfers.map((transfer: any) => ({
    "Transfer ID": transfer.id,
    "Date": new Date(transfer.date).toLocaleDateString(),
    "Product Name": transfer.productName,
    "Product SKU": transfer.productSku,
    "Category": transfer.category,
    "Quantity": transfer.quantity,
    "Unit": transfer.unit,
    "From Store": transfer.fromStore ? "Yes" : "No",
    "To Store": transfer.toStore ? "Yes" : "No",
    "Status": transfer.status,
    "Requested By": transfer.requestedBy.name,
    "Approved By": transfer.approvedBy?.name || "N/A",
    "Completed At": transfer.completedAt ? new Date(transfer.completedAt).toLocaleDateString() : "N/A",
    "Notes": transfer.notes || "",
    "Primary Supplier": transfer.suppliers.length > 0 ? transfer.suppliers[0].name : "No Supplier"
  }));

  const transfersSheet = XLSX.utils.json_to_sheet(transfersData);
  XLSX.utils.book_append_sheet(workbook, transfersSheet, "Transfers");

  // Supplier breakdown data
  const supplierData = data.supplierBreakdown.map((supplier: any) => ({
    "Supplier Name": supplier.supplierName,
    "Contact Person": supplier.contactPerson || "N/A",
    "Total Transfers": supplier.transferCount,
    "Total Quantity": supplier.totalQuantity,
    "Completed": supplier.completedCount,
    "Pending": supplier.pendingCount,
    "Approved": supplier.approvedCount,
    "Completion Rate": supplier.transferCount > 0 ? ((supplier.completedCount / supplier.transferCount) * 100).toFixed(1) + "%" : "0%"
  }));

  const supplierSheet = XLSX.utils.json_to_sheet(supplierData);
  XLSX.utils.book_append_sheet(workbook, supplierSheet, "Supplier Breakdown");

  // Generate Excel file
  const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "buffer" });

  const fileName = `transfer-report-${new Date().toISOString().split('T')[0]}.xlsx`;

  return new NextResponse(excelBuffer, {
    headers: {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename="${fileName}"`,
    },
  });
}

// Export to CSV format
function exportToCsv(data: any) {
  const transfersData = data.transfers.map((transfer: any) => ({
    "Transfer ID": transfer.id,
    "Date": new Date(transfer.date).toLocaleDateString(),
    "Product Name": transfer.productName,
    "Product SKU": transfer.productSku,
    "Category": transfer.category,
    "Quantity": transfer.quantity,
    "Unit": transfer.unit,
    "From Store": transfer.fromStore ? "Yes" : "No",
    "To Store": transfer.toStore ? "Yes" : "No",
    "Status": transfer.status,
    "Requested By": transfer.requestedBy.name,
    "Approved By": transfer.approvedBy?.name || "N/A",
    "Completed At": transfer.completedAt ? new Date(transfer.completedAt).toLocaleDateString() : "N/A",
    "Notes": transfer.notes || "",
    "Primary Supplier": transfer.suppliers.length > 0 ? transfer.suppliers[0].name : "No Supplier"
  }));

  const parser = new Parser();
  const csv = parser.parse(transfersData);

  const fileName = `transfer-report-${new Date().toISOString().split('T')[0]}.csv`;

  return new NextResponse(csv, {
    headers: {
      "Content-Type": "text/csv",
      "Content-Disposition": `attachment; filename="${fileName}"`,
    },
  });
}
