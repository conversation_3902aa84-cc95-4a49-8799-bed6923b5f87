import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { BatchStatus } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/inventory/stock-batches/[id] - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/inventory/stock-batches/[id] - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Validation schema for updating a stock batch
const updateBatchSchema = z.object({
  batchNumber: z.string().optional().nullable(),
  expiryDate: z.string().datetime().optional().nullable(),
  purchasePrice: z.number().positive().optional(),
  status: z.nativeEnum(BatchStatus).optional(),
  notes: z.string().optional().nullable(),
});

// GET /api/inventory/stock-batches/[id] - Get specific batch details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log("[API] GET /api/inventory/stock-batches/[id] - Start, ID:", id);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get the stock batch
    const batch = await prisma.stockBatch.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            barcode: true,
            basePrice: true,
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true
              }
            }
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
                phone: true,
                email: true,
                address: true
              }
            }
          }
        },
        purchaseOrder: {
          select: {
            id: true,
            orderDate: true,
            status: true,
            total: true
          }
        },
        storeStock: {
          select: {
            id: true,
            quantity: true,
            minThreshold: true,
            maxThreshold: true
          }
        },
        warehouseStock: {
          select: {
            id: true,
            quantity: true,
            minThreshold: true,
            maxThreshold: true
          }
        },
        stockHistory: {
          select: {
            id: true,
            date: true,
            changeQuantity: true,
            source: true,
            notes: true
          },
          orderBy: {
            date: 'desc'
          },
          take: 10 // Get last 10 history entries
        },
        transactionItems: {
          select: {
            id: true,
            quantity: true,
            unitPrice: true,
            transaction: {
              select: {
                id: true,
                transactionDate: true,
                total: true
              }
            }
          },
          orderBy: {
            transaction: {
              transactionDate: 'desc'
            }
          },
          take: 10 // Get last 10 transaction items
        }
      }
    });

    if (!batch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    console.log("[API] Stock batch found:", batch.id);

    return NextResponse.json({ batch });
  } catch (error) {
    console.error("[API] Error fetching stock batch:", error);
    return NextResponse.json(
      { error: "Failed to fetch stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/inventory/stock-batches/[id] - Update batch information
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log("[API] PUT /api/inventory/stock-batches/[id] - Start, ID:", id);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update stock batches
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log("[API] Request body:", body);

    // Validate update data
    const validationResult = updateBatchSchema.safeParse(body);
    if (!validationResult.success) {
      console.error("[API] Validation failed:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    // Check if batch exists
    const existingBatch = await prisma.stockBatch.findUnique({
      where: { id },
      select: { id: true, productId: true, remainingQuantity: true }
    });

    if (!existingBatch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    const updateData: any = {};
    const { batchNumber, expiryDate, purchasePrice, status, notes } = validationResult.data;

    if (batchNumber !== undefined) updateData.batchNumber = batchNumber;
    if (expiryDate !== undefined) updateData.expiryDate = expiryDate ? new Date(expiryDate) : null;
    if (purchasePrice !== undefined) updateData.purchasePrice = purchasePrice;
    if (status !== undefined) updateData.status = status;
    if (notes !== undefined) updateData.notes = notes;

    console.log("[API] Updating stock batch with data:", updateData);

    // Update the stock batch
    const updatedBatch = await prisma.stockBatch.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true
              }
            }
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true
              }
            }
          }
        }
      }
    });

    console.log("[API] Stock batch updated successfully:", updatedBatch.id);

    return NextResponse.json({
      message: "Stock batch updated successfully",
      batch: updatedBatch
    });

  } catch (error) {
    console.error("[API] Error updating stock batch:", error);
    return NextResponse.json(
      { error: "Failed to update stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// DELETE /api/inventory/stock-batches/[id] - Delete/deactivate batch
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log("[API] DELETE /api/inventory/stock-batches/[id] - Start, ID:", id);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to delete stock batches
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Check if batch exists
    const existingBatch = await prisma.stockBatch.findUnique({
      where: { id },
      select: {
        id: true,
        remainingQuantity: true,
        status: true,
        transactionItems: {
          select: { id: true }
        },
        stockHistory: {
          select: { id: true }
        }
      }
    });

    if (!existingBatch) {
      return NextResponse.json(
        { error: "Stock batch not found" },
        { status: 404 }
      );
    }

    // Check if batch has been used in transactions or has history
    const hasTransactions = existingBatch.transactionItems.length > 0;
    const hasHistory = existingBatch.stockHistory.length > 0;

    if (hasTransactions || hasHistory) {
      // If batch has been used, deactivate instead of delete
      console.log("[API] Batch has transactions/history, deactivating instead of deleting");

      const deactivatedBatch = await prisma.stockBatch.update({
        where: { id },
        data: {
          status: BatchStatus.SOLD_OUT,
          remainingQuantity: 0
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true
            }
          }
        }
      });

      return NextResponse.json({
        message: "Stock batch deactivated successfully (batch had transaction history)",
        batch: deactivatedBatch
      });
    } else {
      // If batch has no transactions or history, safe to delete
      console.log("[API] Batch has no transactions/history, safe to delete");

      await prisma.stockBatch.delete({
        where: { id }
      });

      return NextResponse.json({
        message: "Stock batch deleted successfully"
      });
    }

  } catch (error) {
    console.error("[API] Error deleting stock batch:", error);
    return NextResponse.json(
      { error: "Failed to delete stock batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}
