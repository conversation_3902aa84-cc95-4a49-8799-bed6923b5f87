import { NextRequest, NextResponse } from 'next/server';
import { AutoPOGenerationEngine } from '@/lib/auto-po-generation-engine';

/**
 * POST /api/cron/po-suggestions
 * Scheduled task to generate PO suggestions and send notifications
 * This endpoint should be called by a cron service (e.g., Vercel Cron, GitHub Actions, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Verify this is a legitimate cron request
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log('🕐 Starting scheduled PO suggestions generation...');
    
    // Generate PO suggestions for all categories
    const result = await AutoPOGenerationEngine.generatePOSuggestions();
    
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      summary: result.summary,
      suggestionsGenerated: result.suggestions.length,
      criticalSuggestions: result.suggestions.filter(s => s.urgencyLevel === 'critical').length,
      highPrioritySuggestions: result.suggestions.filter(s => s.urgencyLevel === 'high').length,
      totalEstimatedCost: result.summary.totalEstimatedCost,
    };

    console.log('✅ Scheduled PO suggestions completed:', response);
    
    return NextResponse.json(response);
  } catch (error) {
    console.error('❌ Error in scheduled PO suggestions:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to generate PO suggestions', 
        message: (error as Error).message,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/cron/po-suggestions
 * Health check for the cron endpoint
 */
export async function GET() {
  return NextResponse.json({
    status: 'healthy',
    endpoint: 'PO Suggestions Cron Job',
    timestamp: new Date().toISOString(),
    description: 'This endpoint generates automatic purchase order suggestions and sends notifications',
  });
}
