import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for updating a supplier return
const updateSupplierReturnSchema = z.object({
  reason: z.string().min(1).optional(),
  notes: z.string().optional(),
  status: z.enum(['PENDING', 'APPROVED', 'COMPLETED', 'REJECTED']).optional(),
});

// GET /api/supplier-returns/[id] - Get supplier return details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const supplierReturn = await prisma.supplierReturn.findUnique({
      where: { id },
      include: {
        supplier: true,
        purchaseOrder: {
          include: {
            createdBy: {
              select: { id: true, name: true, email: true },
            },
          },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    if (!supplierReturn) {
      return NextResponse.json({ error: 'Supplier return not found' }, { status: 404 });
    }

    return NextResponse.json(supplierReturn);
  } catch (error) {
    console.error('Error fetching supplier return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/supplier-returns/[id] - Update supplier return
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateSupplierReturnSchema.parse(body);

    // Check if supplier return exists
    const existingReturn = await prisma.supplierReturn.findUnique({
      where: { id },
      include: { items: true },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Supplier return not found' }, { status: 404 });
    }

    // Prevent updating completed or rejected returns
    if (existingReturn.status === 'COMPLETED' || existingReturn.status === 'REJECTED') {
      return NextResponse.json({ 
        error: 'Cannot update completed or rejected supplier returns' 
      }, { status: 400 });
    }

    // If approving, update inventory
    if (validatedData.status === 'APPROVED' && existingReturn.status === 'PENDING') {
      const result = await prisma.$transaction(async (tx) => {
        // Update supplier return status
        const updatedReturn = await tx.supplierReturn.update({
          where: { id },
          data: validatedData,
          include: {
            supplier: true,
            purchaseOrder: true,
            items: {
              include: {
                product: true,
              },
            },
          },
        });

        // Update inventory for each returned item (reduce warehouse stock)
        for (const item of existingReturn.items) {
          const warehouseStock = await tx.warehouseStock.findUnique({
            where: { productId: item.productId },
          });

          if (warehouseStock) {
            await tx.warehouseStock.update({
              where: { productId: item.productId },
              data: {
                quantity: { decrement: item.quantity },
                lastUpdated: new Date(),
              },
            });

            // Create stock adjustment record
            await tx.stockAdjustment.create({
              data: {
                productId: item.productId,
                warehouseStockId: warehouseStock.id,
                previousQuantity: warehouseStock.quantity,
                newQuantity: warehouseStock.quantity.sub(item.quantity),
                adjustmentQuantity: item.quantity.neg(),
                reason: 'RETURN',
                notes: `Supplier return approved: ${existingReturn.reason}`,
                userId: auth.user.id,
              },
            });

            // Create stock history record
            await tx.stockHistory.create({
              data: {
                productId: item.productId,
                warehouseStockId: warehouseStock.id,
                previousQuantity: warehouseStock.quantity,
                newQuantity: warehouseStock.quantity.sub(item.quantity),
                changeQuantity: item.quantity.neg(),
                source: 'RETURN',
                referenceId: existingReturn.id,
                referenceType: 'SupplierReturn',
                notes: `Supplier return approved: ${existingReturn.reason}`,
                userId: auth.user.id,
              },
            });
          }
        }

        return updatedReturn;
      });

      return NextResponse.json(result);
    } else {
      // Regular update without inventory changes
      const updatedReturn = await prisma.supplierReturn.update({
        where: { id },
        data: validatedData,
        include: {
          supplier: true,
          purchaseOrder: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      return NextResponse.json(updatedReturn);
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating supplier return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
