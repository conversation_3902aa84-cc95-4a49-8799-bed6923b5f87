import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';

// Purchase order template item schema for validation
const purchaseOrderTemplateItemUpdateSchema = z.object({
  id: z.string().optional(), // For existing items
  productId: z.string().min(1, { message: "Product is required" }),
  productSupplierId: z.string().optional(),
  quantity: z.number().positive({ message: "Quantity must be positive" }),
  unitPrice: z.number().positive({ message: "Unit price must be positive" }),
});

// Purchase order template update schema for validation
const purchaseOrderTemplateUpdateSchema = z.object({
  name: z.string().min(1, { message: "Template name is required" }).optional(),
  description: z.string().optional(),
  supplierId: z.string().min(1, { message: "Supplier is required" }).optional(),
  taxPercentage: z.number().min(0, { message: "Tax percentage must be non-negative" }).max(100, { message: "Tax percentage cannot exceed 100%" }).optional(),
  notes: z.string().optional(),
  isActive: z.boolean().optional(),
  items: z.array(purchaseOrderTemplateItemUpdateSchema).optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to view templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: templateId } = await params;

    // Fetch template with all related data
    const template = await prisma.purchaseOrderTemplate.findUnique({
      where: { id: templateId },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
            productSupplier: {
              include: {
                supplier: true,
              },
            },
          },
        },
      },
    });

    if (!template) {
      return NextResponse.json(
        { error: "Purchase order template not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(template);
  } catch (error) {
    console.error("Error fetching purchase order template:", error);
    return NextResponse.json(
      { error: "Failed to fetch purchase order template", message: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to update templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: templateId } = await params;

    // Check if template exists
    const existingTemplate = await prisma.purchaseOrderTemplate.findUnique({
      where: { id: templateId },
      include: { items: true },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Purchase order template not found" },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = purchaseOrderTemplateUpdateSchema.parse(body);

    // If supplier is being updated, verify it exists
    if (validatedData.supplierId) {
      const supplier = await prisma.supplier.findUnique({
        where: { id: validatedData.supplierId },
      });

      if (!supplier) {
        return NextResponse.json(
          { error: "Supplier not found" },
          { status: 404 }
        );
      }
    }

    // If items are being updated, verify all products exist
    if (validatedData.items) {
      const productIds = validatedData.items.map(item => item.productId);
      const products = await prisma.product.findMany({
        where: { id: { in: productIds }, active: true },
      });

      if (products.length !== productIds.length) {
        return NextResponse.json(
          { error: "One or more products not found or inactive" },
          { status: 400 }
        );
      }
    }

    // Check for duplicate template name if name is being updated
    if (validatedData.name && validatedData.name !== existingTemplate.name) {
      const duplicateTemplate = await prisma.purchaseOrderTemplate.findFirst({
        where: {
          name: validatedData.name,
          supplierId: validatedData.supplierId || existingTemplate.supplierId,
          isActive: true,
          id: { not: templateId },
        },
      });

      if (duplicateTemplate) {
        return NextResponse.json(
          { error: "A template with this name already exists for this supplier" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.name !== undefined) updateData.name = validatedData.name;
    if (validatedData.description !== undefined) updateData.description = validatedData.description;
    if (validatedData.supplierId !== undefined) updateData.supplierId = validatedData.supplierId;
    if (validatedData.taxPercentage !== undefined) updateData.taxPercentage = validatedData.taxPercentage;
    if (validatedData.notes !== undefined) updateData.notes = validatedData.notes;
    if (validatedData.isActive !== undefined) updateData.isActive = validatedData.isActive;

    // Handle items update if provided
    if (validatedData.items) {
      // Delete existing items and create new ones
      updateData.items = {
        deleteMany: {},
        create: validatedData.items.map(item => ({
          productId: item.productId,
          productSupplierId: item.productSupplierId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
        })),
      };
    }

    // Update template
    const updatedTemplate = await prisma.purchaseOrderTemplate.update({
      where: { id: templateId },
      data: updateData,
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
            productSupplier: {
              include: {
                supplier: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(updatedTemplate);
  } catch (error) {
    console.error("Error updating purchase order template:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update purchase order template", message: (error as Error).message },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to delete templates
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { id: templateId } = await params;

    // Check if template exists
    const existingTemplate = await prisma.purchaseOrderTemplate.findUnique({
      where: { id: templateId },
    });

    if (!existingTemplate) {
      return NextResponse.json(
        { error: "Purchase order template not found" },
        { status: 404 }
      );
    }

    // Soft delete by setting isActive to false
    await prisma.purchaseOrderTemplate.update({
      where: { id: templateId },
      data: { isActive: false },
    });

    return NextResponse.json({ message: "Purchase order template deleted successfully" });
  } catch (error) {
    console.error("Error deleting purchase order template:", error);
    return NextResponse.json(
      { error: "Failed to delete purchase order template", message: (error as Error).message },
      { status: 500 }
    );
  }
}
