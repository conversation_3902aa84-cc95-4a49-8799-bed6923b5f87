import { NextRequest, NextResponse } from "next/server";
import { calculateSupplierRelationshipHealth } from "@/lib/relationship-health";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 401,
      user: null
    };
  }

  try {
    // Verify the token
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Invalid token",
      status: 401,
      user: null
    };
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get("timeRange") || "6months";
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");

    // Calculate date range based on timeRange parameter
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam);
      endDate = new Date(endDateParam);
    } else {
      endDate = new Date();
      switch (timeRange) {
        case "3months":
          startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case "6months":
          startDate = new Date();
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case "1year":
          startDate = new Date();
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
        case "2years":
          startDate = new Date();
          startDate.setFullYear(startDate.getFullYear() - 2);
          break;
        case "all":
        default:
          startDate = undefined;
          endDate = undefined;
          break;
      }
    }

    const relationshipAnalysis = await calculateSupplierRelationshipHealth(
      params.id,
      startDate,
      endDate
    );

    return NextResponse.json({
      success: true,
      relationshipAnalysis
    });

  } catch (error) {
    console.error("Error fetching supplier relationship health:", error);
    
    if (error instanceof Error && error.message === "Supplier not found") {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        error: "Failed to fetch supplier relationship health",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
