import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import { subMonths, format, startOfMonth, endOfMonth } from "date-fns";
import { calculateSupplierQualityMetrics } from "@/lib/supplier-quality-metrics";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/suppliers/[id]/analytics - Get supplier analytics
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { id: supplierId } = await params;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, name: true }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get("timeRange") || "12months";

    // Calculate date range
    let startDate: Date;
    const endDate = new Date();

    switch (timeRange) {
      case "3months":
        startDate = subMonths(endDate, 3);
        break;
      case "6months":
        startDate = subMonths(endDate, 6);
        break;
      case "12months":
        startDate = subMonths(endDate, 12);
        break;
      default:
        startDate = new Date(0); // All time
    }

    // Get purchase orders for the time range
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: {
        supplierId,
        orderDate: timeRange === "all" ? undefined : {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
              }
            }
          }
        }
      },
    });

    // Get product suppliers
    const productSuppliers = await prisma.productSupplier.findMany({
      where: { supplierId },
      include: {
        product: {
          select: {
            id: true,
            name: true,
          }
        }
      }
    });

    // Calculate basic metrics
    const totalSpent = purchaseOrders.reduce((sum, po) => sum + Number(po.total), 0);
    const totalOrders = purchaseOrders.length;
    const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
    const totalProducts = productSuppliers.length;
    const preferredProducts = productSuppliers.filter(ps => ps.isPreferred).length;

    // Calculate delivery performance from actual data
    const receivedOrders = purchaseOrders.filter(po =>
      po.receivedAt && po.expectedDeliveryDate
    );

    let onTimeDeliveryRate = 100;
    let averageDeliveryTime = 0;

    if (receivedOrders.length > 0) {
      const onTimeDeliveries = receivedOrders.filter(po => {
        const receivedDate = new Date(po.receivedAt!);
        const expectedDate = new Date(po.expectedDeliveryDate!);
        return receivedDate <= expectedDate;
      });

      onTimeDeliveryRate = (onTimeDeliveries.length / receivedOrders.length) * 100;

      const totalDeliveryDays = receivedOrders.reduce((sum, po) => {
        const orderDate = new Date(po.orderDate);
        const receivedDate = new Date(po.receivedAt!);
        const deliveryDays = Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
        return sum + deliveryDays;
      }, 0);

      averageDeliveryTime = totalDeliveryDays / receivedOrders.length;
    }

    // Calculate monthly spending
    const monthlySpending = [];
    if (timeRange !== "all") {
      const months = timeRange === "3months" ? 3 : timeRange === "6months" ? 6 : 12;
      
      for (let i = months - 1; i >= 0; i--) {
        const monthStart = startOfMonth(subMonths(endDate, i));
        const monthEnd = endOfMonth(subMonths(endDate, i));
        
        const monthOrders = purchaseOrders.filter(po => {
          const orderDate = new Date(po.orderDate);
          return orderDate >= monthStart && orderDate <= monthEnd;
        });
        
        const monthTotal = monthOrders.reduce((sum, po) => sum + Number(po.total), 0);
        
        monthlySpending.push({
          month: format(monthStart, "MMM yyyy"),
          amount: monthTotal,
        });
      }
    }

    // Calculate top products by spending
    const productSpending = new Map();
    
    purchaseOrders.forEach(po => {
      po.items.forEach(item => {
        const productId = item.product.id;
        const productName = item.product.name;
        const itemTotal = Number(item.subtotal);
        const quantity = Number(item.quantity);
        
        if (productSpending.has(productId)) {
          const existing = productSpending.get(productId);
          existing.totalSpent += itemTotal;
          existing.totalQuantity += quantity;
          existing.orderCount += 1;
          existing.lastOrderDate = po.orderDate > existing.lastOrderDate ? po.orderDate : existing.lastOrderDate;
        } else {
          productSpending.set(productId, {
            productId,
            productName,
            totalSpent: itemTotal,
            totalQuantity: quantity,
            orderCount: 1,
            lastOrderDate: po.orderDate,
          });
        }
      });
    });

    const topProducts = Array.from(productSpending.values())
      .map(product => ({
        ...product,
        averagePrice: product.totalSpent / product.totalQuantity,
        lastOrderDate: product.lastOrderDate.toISOString(),
      }))
      .sort((a, b) => b.totalSpent - a.totalSpent)
      .slice(0, 10);

    // Calculate performance metrics from real data
    let qualityScore = 75; // Default neutral score
    try {
      const qualityMetrics = await calculateSupplierQualityMetrics(
        supplierId,
        startDate,
        endDate
      );
      qualityScore = qualityMetrics.metrics.qualityScore;
    } catch (error) {
      console.warn(`Could not calculate quality score for supplier ${supplierId}:`, error);
    }

    // Calculate reliability score based on delivery performance
    const reliabilityScore = Math.round(
      onTimeDeliveryRate * 0.7 + // 70% weight on on-time delivery
      Math.max(0, 100 - (averageDeliveryTime - 7) * 5) * 0.3 // 30% weight on delivery speed
    );

    // Calculate cost effectiveness score based on order value trends
    let costEffectivenessScore = 75; // Default neutral score
    if (monthlySpending.length >= 2) {
      const recentSpending = monthlySpending.slice(-3).reduce((sum, month) => sum + month.amount, 0);
      const earlierSpending = monthlySpending.slice(0, 3).reduce((sum, month) => sum + month.amount, 0);

      if (earlierSpending > 0) {
        const spendingTrend = (recentSpending - earlierSpending) / earlierSpending;
        // Lower spending increase = better cost effectiveness
        costEffectivenessScore = Math.max(0, Math.min(100, 85 - (spendingTrend * 100)));
      }
    }

    // Calculate overall score
    const overallScore = Math.round(
      qualityScore * 0.4 + // 40% weight on quality
      reliabilityScore * 0.4 + // 40% weight on reliability
      costEffectivenessScore * 0.2 // 20% weight on cost effectiveness
    );

    const performanceMetrics = {
      qualityScore: Math.round(qualityScore),
      reliabilityScore: Math.round(reliabilityScore),
      costEffectivenessScore: Math.round(costEffectivenessScore),
      overallScore: Math.round(overallScore),
    };

    // Get last order date
    const lastOrderDate = purchaseOrders.length > 0 
      ? purchaseOrders.sort((a, b) => new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime())[0].orderDate.toISOString()
      : null;

    const analytics = {
      totalSpent,
      totalOrders,
      averageOrderValue,
      averageDeliveryTime,
      onTimeDeliveryRate,
      totalProducts,
      preferredProducts,
      lastOrderDate,
      monthlySpending,
      topProducts,
      performanceMetrics,
    };

    return NextResponse.json({
      supplier,
      analytics,
    });
  } catch (error) {
    console.error("Error fetching supplier analytics:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier analytics", message: (error as Error).message },
      { status: 500 }
    );
  }
}
