import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// POST /api/suppliers/[id]/toggle-status - Toggle supplier active status
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update suppliers
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id: supplierId } = await params;

    // Get supplier
    const existingSupplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, name: true, isActive: true }
    });

    // Check if supplier exists
    if (!existingSupplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();
    const { isActive, reason } = body;

    if (typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: "isActive field is required and must be a boolean" },
        { status: 400 }
      );
    }

    // Update supplier status
    const updatedSupplier = await prisma.supplier.update({
      where: { id: supplierId },
      data: { isActive },
    });

    // Log activity
    const action = isActive ? "REACTIVATE_SUPPLIER" : "DEACTIVATE_SUPPLIER";
    const statusText = isActive ? "reactivated" : "deactivated";
    const details = reason 
      ? `${statusText.charAt(0).toUpperCase() + statusText.slice(1)} supplier: ${updatedSupplier.name} (Reason: ${reason})`
      : `${statusText.charAt(0).toUpperCase() + statusText.slice(1)} supplier: ${updatedSupplier.name}`;

    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action,
        details,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Supplier ${statusText} successfully`,
      supplier: updatedSupplier,
    });
  } catch (error) {
    console.error("Error toggling supplier status:", error);
    return NextResponse.json(
      { error: "Failed to update supplier status", message: (error as Error).message },
      { status: 500 }
    );
  }
}
