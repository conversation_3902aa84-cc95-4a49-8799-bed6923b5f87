import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/suppliers/[id]/purchase-orders - Get all purchase orders for a supplier
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { id: supplierId } = await params;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { 
        id: true, 
        name: true, 
        contactPerson: true,
        phone: true,
        email: true,
        address: true 
      }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const search = url.searchParams.get("search") || "";
    const status = url.searchParams.get("status") || "";
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = { supplierId };

    if (status && status !== "all") {
      where.status = status;
    }

    if (search) {
      where.OR = [
        { id: { contains: search, mode: "insensitive" } },
        { createdBy: { name: { contains: search, mode: "insensitive" } } },
      ];
    }

    // Get purchase orders with pagination
    const [purchaseOrders, total] = await Promise.all([
      prisma.purchaseOrder.findMany({
        where,
        skip,
        take: limit,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          },
          _count: {
            select: {
              items: true
            }
          }
        },
        orderBy: { orderDate: 'desc' },
      }),
      prisma.purchaseOrder.count({ where }),
    ]);

    // Transform the data
    const transformedPurchaseOrders = purchaseOrders.map(po => ({
      id: po.id,
      orderDate: po.orderDate.toISOString(),
      status: po.status,
      subtotal: Number(po.subtotal),
      tax: Number(po.tax),
      total: Number(po.total),
      expectedDeliveryDate: po.expectedDeliveryDate?.toISOString() || null,
      receivedAt: po.receivedAt?.toISOString() || null,
      approvedAt: po.approvedAt?.toISOString() || null,
      createdBy: po.createdBy,
      _count: po._count,
    }));

    return NextResponse.json({
      supplier,
      purchaseOrders: transformedPurchaseOrders,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching supplier purchase orders:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier purchase orders", message: (error as Error).message },
      { status: 500 }
    );
  }
}
