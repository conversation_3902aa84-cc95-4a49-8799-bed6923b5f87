import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/suppliers/[id]/products - Get all products for a supplier
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { id: supplierId } = await params;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { 
        id: true, 
        name: true, 
        contactPerson: true,
        phone: true,
        email: true,
        address: true 
      }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get query parameters for filtering
    const url = new URL(request.url);
    const activeOnly = url.searchParams.get("active") === "true";
    const preferredOnly = url.searchParams.get("preferred") === "true";
    const search = url.searchParams.get("search") || "";
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Build filter conditions
    const where: any = { supplierId };
    
    if (activeOnly) {
      where.isActive = true;
    }
    
    if (preferredOnly) {
      where.isPreferred = true;
    }

    // Add product search filter
    if (search) {
      where.product = {
        OR: [
          { name: { contains: search, mode: "insensitive" } },
          { sku: { contains: search, mode: "insensitive" } },
          { barcode: { contains: search, mode: "insensitive" } },
        ]
      };
    }

    // Get supplier products with pagination
    const [supplierProducts, total] = await Promise.all([
      prisma.productSupplier.findMany({
        where,
        include: {
          product: {
            include: {
              category: {
                select: {
                  id: true,
                  name: true,
                }
              },
              unit: {
                select: {
                  id: true,
                  name: true,
                  abbreviation: true,
                }
              },
              storeStock: {
                select: {
                  quantity: true,
                  minThreshold: true,
                  maxThreshold: true,
                }
              },
              warehouseStock: {
                select: {
                  quantity: true,
                  minThreshold: true,
                  maxThreshold: true,
                }
              }
            }
          }
        },
        skip,
        take: limit,
        orderBy: [
          { isPreferred: 'desc' }, // Preferred products first
          { product: { name: 'asc' } }, // Then alphabetically
        ],
      }),
      prisma.productSupplier.count({ where }),
    ]);

    // Calculate summary statistics
    const stats = {
      totalProducts: total,
      activeProducts: supplierProducts.filter(sp => sp.isActive).length,
      preferredProducts: supplierProducts.filter(sp => sp.isPreferred).length,
      averagePurchasePrice: supplierProducts.length > 0 
        ? supplierProducts.reduce((sum, sp) => sum + Number(sp.purchasePrice), 0) / supplierProducts.length
        : 0,
      totalInventoryValue: supplierProducts.reduce((sum, sp) => {
        const storeQty = sp.product.storeStock?.quantity || 0;
        const warehouseQty = sp.product.warehouseStock?.quantity || 0;
        const totalQty = Number(storeQty) + Number(warehouseQty);
        return sum + (totalQty * Number(sp.purchasePrice));
      }, 0)
    };

    // Transform the data to match frontend expectations for PO creation
    const transformedProducts = supplierProducts.map(sp => ({
      // Product fields at top level (for PO creation compatibility)
      id: sp.product.id, // Use product ID as the main ID
      name: sp.product.name,
      sku: sp.product.sku,
      basePrice: Number(sp.product.basePrice),
      purchasePrice: Number(sp.purchasePrice), // Fallback for legacy compatibility
      category: sp.product.category,
      unit: sp.product.unit,

      // Supplier-specific information nested under supplierInfo
      supplierInfo: {
        id: sp.id, // ProductSupplier relationship ID
        productId: sp.productId,
        supplierId: sp.supplierId,
        supplierProductCode: sp.supplierProductCode,
        supplierProductName: sp.supplierProductName,
        purchasePrice: Number(sp.purchasePrice),
        minimumOrderQuantity: sp.minimumOrderQuantity ? Number(sp.minimumOrderQuantity) : null,
        leadTimeDays: sp.leadTimeDays,
        isPreferred: sp.isPreferred,
        isActive: sp.isActive,
        notes: sp.notes,
        createdAt: sp.createdAt.toISOString(),
        updatedAt: sp.updatedAt.toISOString(),
      }
    }));

    return NextResponse.json({
      supplier,
      products: transformedProducts,
      stats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching supplier products:", error);
    return NextResponse.json(
      { error: "Failed to fetch supplier products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/suppliers/[id]/products - Add multiple products to a supplier (bulk operation)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check permissions
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    const { id: supplierId } = await params;

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, name: true }
    });

    if (!supplier) {
      return NextResponse.json(
        { error: "Supplier not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();
    const { products } = body;

    if (!Array.isArray(products) || products.length === 0) {
      return NextResponse.json(
        { error: "Products array is required and must not be empty" },
        { status: 400 }
      );
    }

    // Validate each product entry
    const validProducts = [];
    const errors = [];

    for (let i = 0; i < products.length; i++) {
      const product = products[i];
      
      // Basic validation
      if (!product.productId || !product.purchasePrice) {
        errors.push(`Product ${i + 1}: productId and purchasePrice are required`);
        continue;
      }

      if (typeof product.purchasePrice !== 'number' || product.purchasePrice <= 0) {
        errors.push(`Product ${i + 1}: purchasePrice must be a positive number`);
        continue;
      }

      // Check if product exists
      const existingProduct = await prisma.product.findUnique({
        where: { id: product.productId },
        select: { id: true, name: true }
      });

      if (!existingProduct) {
        errors.push(`Product ${i + 1}: Product not found`);
        continue;
      }

      // Check if relationship already exists
      const existingRelationship = await prisma.productSupplier.findUnique({
        where: {
          productId_supplierId: {
            productId: product.productId,
            supplierId
          }
        }
      });

      if (existingRelationship) {
        errors.push(`Product ${i + 1}: Relationship already exists for ${existingProduct.name}`);
        continue;
      }

      validProducts.push({
        productId: product.productId,
        supplierId,
        supplierProductCode: product.supplierProductCode || null,
        supplierProductName: product.supplierProductName || null,
        purchasePrice: product.purchasePrice,
        minimumOrderQuantity: product.minimumOrderQuantity || null,
        leadTimeDays: product.leadTimeDays || null,
        isPreferred: product.isPreferred || false,
        isActive: product.isActive !== false, // Default to true
        notes: product.notes || null,
      });
    }

    if (errors.length > 0) {
      return NextResponse.json(
        { error: "Validation failed", issues: errors },
        { status: 400 }
      );
    }

    // Create all product-supplier relationships in a transaction
    const createdRelationships = await prisma.$transaction(async (tx) => {
      const results = [];
      
      for (const productData of validProducts) {
        // If this product is set as preferred, unset other preferred suppliers for this product
        if (productData.isPreferred) {
          await tx.productSupplier.updateMany({
            where: {
              productId: productData.productId,
              isPreferred: true
            },
            data: {
              isPreferred: false
            }
          });
        }

        const created = await tx.productSupplier.create({
          data: productData,
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              }
            }
          }
        });

        results.push(created);
      }

      return results;
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "BULK_ADD_SUPPLIER_PRODUCTS",
        details: `Added ${createdRelationships.length} products to supplier ${supplier.name}`,
      },
    });

    return NextResponse.json({
      message: `Successfully added ${createdRelationships.length} products to supplier`,
      relationships: createdRelationships,
      count: createdRelationships.length
    }, { status: 201 });
  } catch (error) {
    console.error("Error adding products to supplier:", error);
    return NextResponse.json(
      { error: "Failed to add products to supplier", message: (error as Error).message },
      { status: 500 }
    );
  }
}
