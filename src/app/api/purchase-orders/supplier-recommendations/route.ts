import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { SupplierSelectionEngine } from '@/lib/supplier-selection-engine';

// Schema for PO supplier recommendation request
const poSupplierRecommendationSchema = z.object({
  items: z.array(z.object({
    productId: z.string().min(1, { message: "Product ID is required" }),
    quantity: z.number().positive({ message: "Quantity must be positive" }),
  })).min(1, { message: "At least one item is required" }),
});

/**
 * POST /api/purchase-orders/supplier-recommendations
 * Get supplier recommendations for purchase order items
 * This endpoint helps users select the best suppliers when creating POs
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = poSupplierRecommendationSchema.parse(body);

    // Get supplier recommendations for all items
    const recommendations = await SupplierSelectionEngine.getBulkSupplierRecommendations(
      validatedData.items
    );

    // Group recommendations by supplier to suggest optimal PO splitting
    const supplierGroups = new Map<string, {
      supplier: { id: string; name: string };
      items: Array<{
        productId: string;
        productName: string;
        quantity: number;
        recommendation: any;
      }>;
      totalScore: number;
      totalValue: number;
      confidence: 'high' | 'medium' | 'low';
      warnings: string[];
    }>();

    // Process each recommendation
    for (const rec of recommendations) {
      const supplier = rec.recommendedSupplier;
      const supplierId = supplier.supplierId;

      if (!supplierGroups.has(supplierId)) {
        supplierGroups.set(supplierId, {
          supplier: {
            id: supplier.supplierId,
            name: supplier.supplierName,
          },
          items: [],
          totalScore: 0,
          totalValue: 0,
          confidence: 'high',
          warnings: [],
        });
      }

      const group = supplierGroups.get(supplierId)!;
      group.items.push({
        productId: rec.productId,
        productName: rec.productName,
        quantity: rec.requestedQuantity,
        recommendation: rec,
      });

      // Update group metrics
      group.totalScore += supplier.totalScore;
      group.totalValue += supplier.purchasePrice * rec.requestedQuantity;
      
      // Downgrade confidence if any item has low confidence
      if (rec.recommendation.confidence === 'low') {
        group.confidence = 'low';
      } else if (rec.recommendation.confidence === 'medium' && group.confidence === 'high') {
        group.confidence = 'medium';
      }

      // Collect warnings
      group.warnings.push(...rec.recommendation.warnings);
    }

    // Calculate average scores for each supplier group
    for (const group of supplierGroups.values()) {
      group.totalScore = Math.round(group.totalScore / group.items.length);
      // Remove duplicate warnings
      group.warnings = [...new Set(group.warnings)];
    }

    // Sort supplier groups by total score
    const sortedGroups = Array.from(supplierGroups.values()).sort(
      (a, b) => b.totalScore - a.totalScore
    );

    // Generate PO splitting recommendations
    const poSplittingRecommendations = generatePOSplittingRecommendations(sortedGroups);

    return NextResponse.json({
      itemRecommendations: recommendations,
      supplierGroups: sortedGroups,
      poSplittingRecommendations,
      summary: {
        totalItems: validatedData.items.length,
        recommendationsFound: recommendations.length,
        uniqueSuppliers: supplierGroups.size,
        averageConfidence: calculateAverageConfidence(recommendations),
        totalEstimatedValue: Array.from(supplierGroups.values()).reduce(
          (sum, group) => sum + group.totalValue, 0
        ),
      },
    });
  } catch (error) {
    console.error("Error getting PO supplier recommendations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to get supplier recommendations", message: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * Generate recommendations for how to split the PO across suppliers
 */
function generatePOSplittingRecommendations(supplierGroups: any[]): {
  strategy: 'single' | 'split';
  reasoning: string[];
  recommendations: Array<{
    supplierId: string;
    supplierName: string;
    items: string[];
    estimatedValue: number;
    confidence: string;
  }>;
} {
  if (supplierGroups.length === 0) {
    return {
      strategy: 'single',
      reasoning: ['No supplier recommendations available'],
      recommendations: [],
    };
  }

  if (supplierGroups.length === 1) {
    return {
      strategy: 'single',
      reasoning: ['All items available from single supplier', 'Simplifies ordering and delivery tracking'],
      recommendations: supplierGroups.map(group => ({
        supplierId: group.supplier.id,
        supplierName: group.supplier.name,
        items: group.items.map((item: any) => item.productName),
        estimatedValue: group.totalValue,
        confidence: group.confidence,
      })),
    };
  }

  // Check if splitting makes sense
  const topSupplier = supplierGroups[0];
  const secondSupplier = supplierGroups[1];
  const scoreGap = topSupplier.totalScore - secondSupplier.totalScore;

  if (scoreGap > 20 || topSupplier.items.length >= supplierGroups.reduce((sum, g) => sum + g.items.length, 0) * 0.7) {
    return {
      strategy: 'single',
      reasoning: [
        `Primary supplier (${topSupplier.supplier.name}) has significantly better scores`,
        'Single supplier reduces complexity and shipping costs',
      ],
      recommendations: [
        {
          supplierId: topSupplier.supplier.id,
          supplierName: topSupplier.supplier.name,
          items: topSupplier.items.map((item: any) => item.productName),
          estimatedValue: topSupplier.totalValue,
          confidence: topSupplier.confidence,
        },
      ],
    };
  }

  return {
    strategy: 'split',
    reasoning: [
      'Multiple suppliers offer competitive advantages',
      'Splitting reduces supply risk and may optimize costs',
      'Consider supplier capacity and minimum order quantities',
    ],
    recommendations: supplierGroups.slice(0, 3).map(group => ({
      supplierId: group.supplier.id,
      supplierName: group.supplier.name,
      items: group.items.map((item: any) => item.productName),
      estimatedValue: group.totalValue,
      confidence: group.confidence,
    })),
  };
}

/**
 * Calculate average confidence across all recommendations
 */
function calculateAverageConfidence(recommendations: any[]): string {
  if (recommendations.length === 0) return 'low';

  const confidenceScores = recommendations.map(rec => {
    switch (rec.recommendation.confidence) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 1;
    }
  });

  const average = confidenceScores.reduce((sum, score) => sum + score, 0) / confidenceScores.length;

  if (average >= 2.5) return 'high';
  if (average >= 1.5) return 'medium';
  return 'low';
}
