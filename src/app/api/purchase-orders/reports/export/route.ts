import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import * as XLSX from 'xlsx';
import { parse } from 'json2csv';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Only allow SUPER_ADMIN and WAREHOUSE_ADMIN to export reports
    if (!["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get("type") || "pending";
    const format = searchParams.get("format") || "xlsx";
    const poNumber = searchParams.get("poNumber");
    const supplierId = searchParams.get("supplierId");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");
    const minAmount = searchParams.get("minAmount");
    const maxAmount = searchParams.get("maxAmount");

    // Fetch report data
    const reportData = await fetchReportData(reportType, poNumber, supplierId, startDate, endDate, minAmount, maxAmount);

    // Generate file based on requested format
    if (format === "xlsx") {
      return exportToExcel(reportData, reportType);
    } else if (format === "pdf") {
      return exportToPdf(reportData, reportType);
    } else {
      // CSV format (default fallback)
      return exportToCsv(reportData, reportType);
    }
  } catch (error) {
    console.error("Error exporting purchase order report:", error);
    return NextResponse.json(
      { error: "Failed to export purchase order report", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Fetch report data from the reports API
async function fetchReportData(
  reportType: string,
  poNumber: string | null,
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build query parameters
  const queryParams = new URLSearchParams();
  queryParams.append("type", reportType);

  if (poNumber) queryParams.append("poNumber", poNumber);
  if (supplierId) queryParams.append("supplierId", supplierId);
  if (startDate) queryParams.append("startDate", startDate);
  if (endDate) queryParams.append("endDate", endDate);
  if (minAmount) queryParams.append("minAmount", minAmount);
  if (maxAmount) queryParams.append("maxAmount", maxAmount);

  // Generate the requested report directly using Prisma
  const { prisma } = await import('@/lib/prisma');

  // Generate the requested report
  switch (reportType) {
    case "pending":
      return await generatePendingPOReportData(prisma, poNumber, supplierId, startDate, endDate, minAmount, maxAmount);
    case "received":
      return await generateReceivedPOReportData(prisma, poNumber, supplierId, startDate, endDate, minAmount, maxAmount);
    case "overdue":
      return await generateOverduePOReportData(prisma, poNumber, supplierId, startDate, endDate, minAmount, maxAmount);
    default:
      throw new Error("Invalid report type");
  }
}

// Export to Excel format
async function exportToExcel(reportData: any, reportType: string) {
  const workbook = XLSX.utils.book_new();

  // Create main data sheet
  let mainData: any[] = [];
  
  switch (reportType) {
    case "pending":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Approved By": po.approvedBy?.name || "Not Approved",
        "Days Pending": po.daysPending,
      }));
      break;
    case "received":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Received Date": po.receivedAt ? new Date(po.receivedAt).toLocaleDateString() : "",
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Receiving Count": po.receivingCount,
        "Fulfillment Time (Days)": po.fulfillmentTime || "",
      }));
      break;
    case "overdue":
      mainData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": formatCurrency(po.total),
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Days Overdue": po.daysOverdue,
        "Severity": po.severity.toUpperCase(),
      }));
      break;
  }

  const mainSheet = XLSX.utils.json_to_sheet(mainData);
  XLSX.utils.book_append_sheet(workbook, mainSheet, "Purchase Orders");

  // Create summary sheet
  const summaryData: any[] = [];
  summaryData.push({ "Metric": "Total Purchase Orders", "Value": reportData.summary.totalPOs });
  summaryData.push({ "Metric": "Total Value", "Value": formatCurrency(reportData.summary.totalValue) });
  summaryData.push({ "Metric": "Average Value", "Value": formatCurrency(reportData.summary.avgValue) });

  if (reportType === "pending") {
    Object.entries(reportData.summary.statusBreakdown).forEach(([status, count]) => {
      summaryData.push({ "Metric": `${status} Count`, "Value": count });
    });
  } else if (reportType === "received") {
    summaryData.push({ "Metric": "Fully Received", "Value": reportData.summary.fullyReceived });
    summaryData.push({ "Metric": "Partially Received", "Value": reportData.summary.partiallyReceived });
    summaryData.push({ "Metric": "Fulfillment Rate (%)", "Value": `${reportData.summary.fulfillmentRate.toFixed(1)}%` });
  } else if (reportType === "overdue") {
    summaryData.push({ "Metric": "Average Days Overdue", "Value": reportData.summary.avgDaysOverdue.toFixed(1) });
    Object.entries(reportData.summary.severityBreakdown).forEach(([severity, count]) => {
      summaryData.push({ "Metric": `${severity.toUpperCase()} Severity`, "Value": count });
    });
  }

  const summarySheet = XLSX.utils.json_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(workbook, summarySheet, "Summary");

  // Create supplier breakdown sheet
  const supplierData = Object.entries(reportData.summary.supplierBreakdown).map(([supplier, data]: [string, any]) => ({
    "Supplier": supplier,
    "Count": data.count,
    "Total Value": formatCurrency(data.totalValue),
    "Average Value": formatCurrency(data.totalValue / data.count),
    ...(reportType === "overdue" && { "Avg Days Overdue": data.avgDaysOverdue?.toFixed(1) || "0" }),
  }));

  const supplierSheet = XLSX.utils.json_to_sheet(supplierData);
  XLSX.utils.book_append_sheet(workbook, supplierSheet, "Supplier Breakdown");

  // Convert to buffer
  const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

  // Return as downloadable file
  return new NextResponse(buffer, {
    headers: {
      "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "Content-Disposition": `attachment; filename="po_${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx"`,
    },
  });
}

// Export to CSV format
async function exportToCsv(reportData: any, reportType: string) {
  let csvData: any[] = [];
  
  switch (reportType) {
    case "pending":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Approved By": po.approvedBy?.name || "Not Approved",
        "Days Pending": po.daysPending,
      }));
      break;
    case "received":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Received Date": po.receivedAt ? new Date(po.receivedAt).toLocaleDateString() : "",
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Receiving Count": po.receivingCount,
        "Fulfillment Time (Days)": po.fulfillmentTime || "",
      }));
      break;
    case "overdue":
      csvData = reportData.purchaseOrders.map((po: any) => ({
        "PO ID": po.id.slice(-8).toUpperCase(),
        "Order Date": new Date(po.orderDate).toLocaleDateString(),
        "Supplier": po.supplier.name,
        "Contact Person": po.supplier.contactPerson || "",
        "Phone": po.supplier.phone || "",
        "Status": po.status,
        "Total Amount": po.total,
        "Item Count": po.itemCount,
        "Created By": po.createdBy.name,
        "Days Overdue": po.daysOverdue,
        "Severity": po.severity.toUpperCase(),
      }));
      break;
  }

  // Convert to CSV
  const csv = parse(csvData, {
    fields: Object.keys(csvData[0] || {})
  });

  // Return as downloadable file
  return new NextResponse(csv, {
    headers: {
      "Content-Type": "text/csv",
      "Content-Disposition": `attachment; filename="po_${reportType}_report_${new Date().toISOString().split('T')[0]}.csv"`,
    },
  });
}

// Export to PDF format (placeholder - would need PDF library)
async function exportToPdf(reportData: any, reportType: string) {
  // For now, return CSV as fallback
  return exportToCsv(reportData, reportType);
}

// Helper function to format currency
function formatCurrency(value: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
}

// Generate pending POs report data
async function generatePendingPOReportData(
  prisma: any,
  poNumber: string | null,
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build where clause
  const where: any = {
    status: {
      in: ['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'ORDERED']
    }
  };

  if (poNumber) {
    where.id = {
      contains: poNumber,
      mode: 'insensitive'
    };
  }

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    where.orderDate = {};
    if (startDate) where.orderDate.gte = new Date(startDate);
    if (endDate) where.orderDate.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch pending POs
  const pendingPOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      approvedBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
    },
    orderBy: {
      orderDate: 'asc',
    },
  });

  // Calculate summary statistics
  const totalPOs = pendingPOs.length;
  const totalValue = pendingPOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Group by status
  const statusBreakdown = pendingPOs.reduce((acc, po) => {
    acc[po.status] = (acc[po.status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Group by supplier
  const supplierBreakdown = pendingPOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);
    return acc;
  }, {} as Record<string, { count: number; totalValue: number }>);

  return {
    reportType: "pending",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      statusBreakdown,
      supplierBreakdown,
    },
    purchaseOrders: pendingPOs.map(po => ({
      id: po.id,
      orderDate: po.orderDate,
      supplier: po.supplier,
      status: po.status,
      total: Number(po.total),
      itemCount: po.items.length,
      createdBy: po.createdBy,
      approvedBy: po.approvedBy,
      approvedAt: po.approvedAt,
      daysPending: Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)),
    })),
  };
}

// Generate received POs report data
async function generateReceivedPOReportData(
  prisma: any,
  poNumber: string | null,
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Build where clause
  const where: any = {
    status: {
      in: ['PARTIALLY_RECEIVED', 'RECEIVED']
    }
  };

  if (poNumber) {
    where.id = {
      contains: poNumber,
      mode: 'insensitive'
    };
  }

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    where.receivedAt = {};
    if (startDate) where.receivedAt.gte = new Date(startDate);
    if (endDate) where.receivedAt.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch received POs
  const receivedPOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
      receivings: {
        include: {
          receivedBy: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      },
    },
    orderBy: {
      receivedAt: 'desc',
    },
  });

  // Calculate summary statistics
  const totalPOs = receivedPOs.length;
  const totalValue = receivedPOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Calculate fulfillment metrics
  const fullyReceived = receivedPOs.filter(po => po.status === 'RECEIVED').length;
  const partiallyReceived = receivedPOs.filter(po => po.status === 'PARTIALLY_RECEIVED').length;

  // Group by supplier
  const supplierBreakdown = receivedPOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);
    return acc;
  }, {} as Record<string, { count: number; totalValue: number }>);

  return {
    reportType: "received",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      fullyReceived,
      partiallyReceived,
      fulfillmentRate: totalPOs > 0 ? (fullyReceived / totalPOs) * 100 : 0,
      supplierBreakdown,
    },
    purchaseOrders: receivedPOs.map(po => ({
      id: po.id,
      orderDate: po.orderDate,
      receivedAt: po.receivedAt,
      supplier: po.supplier,
      status: po.status,
      total: Number(po.total),
      itemCount: po.items.length,
      createdBy: po.createdBy,
      receivingCount: po.receivings.length,
      lastReceiving: po.receivings[po.receivings.length - 1],
      fulfillmentTime: po.receivedAt && po.orderDate
        ? Math.floor((new Date(po.receivedAt).getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24))
        : null,
    })),
  };
}

// Generate overdue POs report data
async function generateOverduePOReportData(
  prisma: any,
  poNumber: string | null,
  supplierId: string | null,
  startDate: string | null,
  endDate: string | null,
  minAmount: string | null,
  maxAmount: string | null
) {
  // Calculate overdue date (POs ordered more than 7 days ago that haven't been received)
  const overdueDate = new Date();
  overdueDate.setDate(overdueDate.getDate() - 7);

  // Build where clause
  const where: any = {
    status: {
      in: ['ORDERED', 'PARTIALLY_RECEIVED']
    },
    orderDate: {
      lt: overdueDate
    }
  };

  if (poNumber) {
    where.id = {
      contains: poNumber,
      mode: 'insensitive'
    };
  }

  if (supplierId) {
    where.supplierId = supplierId;
  }

  if (startDate || endDate) {
    if (startDate) where.orderDate.gte = new Date(startDate);
    if (endDate) where.orderDate.lte = new Date(endDate);
  }

  if (minAmount || maxAmount) {
    where.total = {};
    if (minAmount) where.total.gte = parseFloat(minAmount);
    if (maxAmount) where.total.lte = parseFloat(maxAmount);
  }

  // Fetch overdue POs
  const overduePOs = await prisma.purchaseOrder.findMany({
    where,
    include: {
      supplier: {
        select: {
          id: true,
          name: true,
          contactPerson: true,
          phone: true,
          email: true,
        },
      },
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
      items: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
        },
      },
    },
    orderBy: {
      orderDate: 'asc',
    },
  });

  // Calculate summary statistics
  const totalPOs = overduePOs.length;
  const totalValue = overduePOs.reduce((sum, po) => sum + Number(po.total), 0);
  const avgValue = totalPOs > 0 ? totalValue / totalPOs : 0;

  // Calculate overdue metrics
  const avgDaysOverdue = totalPOs > 0
    ? overduePOs.reduce((sum, po) => {
        const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
        return sum + Math.max(0, daysOverdue);
      }, 0) / totalPOs
    : 0;

  // Group by supplier
  const supplierBreakdown = overduePOs.reduce((acc, po) => {
    const supplierName = po.supplier.name;
    if (!acc[supplierName]) {
      acc[supplierName] = { count: 0, totalValue: 0, avgDaysOverdue: 0 };
    }
    acc[supplierName].count += 1;
    acc[supplierName].totalValue += Number(po.total);

    const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
    acc[supplierName].avgDaysOverdue = (acc[supplierName].avgDaysOverdue * (acc[supplierName].count - 1) + Math.max(0, daysOverdue)) / acc[supplierName].count;

    return acc;
  }, {} as Record<string, { count: number; totalValue: number; avgDaysOverdue: number }>);

  // Group by severity (days overdue)
  const severityBreakdown = overduePOs.reduce((acc, po) => {
    const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
    const severity = daysOverdue <= 3 ? 'mild' : daysOverdue <= 7 ? 'moderate' : 'severe';
    acc[severity] = (acc[severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    reportType: "overdue",
    generatedAt: new Date(),
    summary: {
      totalPOs,
      totalValue,
      avgValue,
      avgDaysOverdue,
      severityBreakdown,
      supplierBreakdown,
    },
    purchaseOrders: overduePOs.map(po => {
      const daysOverdue = Math.floor((new Date().getTime() - new Date(po.orderDate).getTime()) / (1000 * 60 * 60 * 24)) - 7;
      const severity = daysOverdue <= 3 ? 'mild' : daysOverdue <= 7 ? 'moderate' : 'severe';

      return {
        id: po.id,
        orderDate: po.orderDate,
        supplier: po.supplier,
        status: po.status,
        total: Number(po.total),
        itemCount: po.items.length,
        createdBy: po.createdBy,
        daysOverdue: Math.max(0, daysOverdue),
        severity,
      };
    }),
  };
}
