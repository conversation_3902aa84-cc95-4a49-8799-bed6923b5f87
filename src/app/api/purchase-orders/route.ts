import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';
import { createPOApprovalNotifications } from '@/lib/notifications';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Purchase order item schema for validation
const purchaseOrderItemSchema = z.object({
  productId: z.string().min(1, { message: "Product is required" }),
  productSupplierId: z.string().optional(), // Reference to ProductSupplier relationship
  quantity: z.number().positive({ message: "Quantity must be positive" }),
  unitPrice: z.number().positive({ message: "Unit price must be positive" }),
});

// Purchase order item schema for draft (relaxed validation)
const purchaseOrderItemDraftSchema = z.object({
  productId: z.string().optional(),
  productSupplierId: z.string().optional(),
  quantity: z.number().min(0).optional(),
  unitPrice: z.number().min(0).optional(),
});

// Purchase order schema for validation
const purchaseOrderSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required" }),
  orderDate: z.string().min(1, { message: "Order date is required" }).transform(val => new Date(val)),
  tax: z.number().min(0, { message: "Tax must be non-negative" }).default(0),
  taxPercentage: z.number().min(0, { message: "Tax percentage must be non-negative" }).max(100, { message: "Tax percentage cannot exceed 100%" }).optional(),
  expectedDeliveryDate: z.string().datetime().optional().nullable().transform(val => val ? new Date(val) : null),
  notes: z.string().optional(),
  items: z.array(purchaseOrderItemSchema).min(1, { message: "At least one item is required" }),
});

// Purchase order schema for draft (relaxed validation)
const purchaseOrderDraftSchema = z.object({
  supplierId: z.string().min(1, { message: "Supplier is required even for drafts" }), // Still require supplier
  orderDate: z.string().optional().transform(val => val ? new Date(val) : new Date()),
  tax: z.number().min(0, { message: "Tax must be non-negative" }).default(0),
  taxPercentage: z.number().min(0, { message: "Tax percentage must be non-negative" }).max(100, { message: "Tax percentage cannot exceed 100%" }).optional(),
  expectedDeliveryDate: z.string().datetime().optional().nullable().transform(val => val ? new Date(val) : null),
  notes: z.string().optional(),
  items: z.array(purchaseOrderItemDraftSchema).optional().default([]),
  isDraft: z.boolean().default(true),
});

// GET /api/purchase-orders - List purchase orders
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const supplierId = url.searchParams.get('supplierId');
    const status = url.searchParams.get('status');
    const priority = url.searchParams.get('priority');
    const search = url.searchParams.get('search');
    const dateFrom = url.searchParams.get('dateFrom');
    const dateTo = url.searchParams.get('dateTo');
    const overdue = url.searchParams.get('overdue') === 'true';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    if (supplierId) {
      where.supplierId = supplierId;
    }

    if (status) {
      where.status = status;
    }

    if (priority) {
      where.priority = priority;
    }

    // Date range filtering
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // Overdue filtering
    if (overdue) {
      where.expectedDeliveryDate = {
        lt: new Date(),
      };
      where.status = {
        in: ['ORDERED', 'SHIPPED', 'PARTIALLY_RECEIVED'],
      };
    }

    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { notes: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [purchaseOrders, total] = await Promise.all([
      prisma.purchaseOrder.findMany({
        where,
        include: {
          supplier: true,
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          approvedBy: {
            select: { id: true, name: true, email: true },
          },
          items: {
            include: {
              product: true,
            },
          },
          invoices: {
            select: { id: true, invoiceNumber: true, status: true },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.purchaseOrder.count({ where }),
    ]);

    return NextResponse.json({
      purchaseOrders,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching purchase orders:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/purchase-orders - Create purchase order
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    console.log("Received body:", body);

    // Check if this is a draft save
    const isDraft = body.isDraft === true;

    // Use appropriate schema based on draft status
    const validatedData = isDraft
      ? purchaseOrderDraftSchema.parse(body)
      : purchaseOrderSchema.parse(body);
    console.log("Validated data:", validatedData);

    // Verify supplier exists (required for both drafts and regular orders)
    const supplier = await prisma.supplier.findUnique({
      where: { id: validatedData.supplierId },
    });

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // For non-draft orders, perform full validation
    if (!isDraft) {
      // Verify all products exist
      const productIds = validatedData.items.map(item => item.productId);
      const products = await prisma.product.findMany({
        where: { id: { in: productIds } },
      });

      if (products.length !== productIds.length) {
        return NextResponse.json({ error: 'One or more products not found' }, { status: 404 });
      }

      // Validate supplier-product relationships for items that specify productSupplierId
      const itemsWithSupplierInfo = validatedData.items.filter(item => item.productSupplierId);
      if (itemsWithSupplierInfo.length > 0) {
        const supplierProductIds = itemsWithSupplierInfo.map(item => item.productSupplierId!);
        const supplierProducts = await prisma.productSupplier.findMany({
          where: {
            id: { in: supplierProductIds },
            supplierId: validatedData.supplierId,
            isActive: true
          },
        });

        if (supplierProducts.length !== itemsWithSupplierInfo.length) {
          return NextResponse.json({
            error: 'One or more products are not available from the selected supplier or supplier-product relationships are inactive'
          }, { status: 400 });
        }
      }
    } else {
      // For drafts, only verify products that are provided
      const validItems = validatedData.items.filter(item => item.productId);
      if (validItems.length > 0) {
        const productIds = validItems.map(item => item.productId!);
        const products = await prisma.product.findMany({
          where: { id: { in: productIds } },
        });

        if (products.length !== productIds.length) {
          return NextResponse.json({ error: 'One or more products not found' }, { status: 404 });
        }
      }
    }

    // Calculate totals (handle incomplete data for drafts)
    const validItems = validatedData.items.filter(item =>
      item.productId && item.quantity && item.unitPrice
    );
    const subtotal = validItems.reduce((sum, item) =>
      sum + ((item.quantity || 0) * (item.unitPrice || 0)), 0
    );
    const total = subtotal + (validatedData.tax || 0);

    // Get ProductSupplier information for each item
    const itemsWithSupplierInfo = await Promise.all(
      validItems.map(async (item) => {
        let productSupplier = null;

        // If productSupplierId is provided, use it directly
        if (item.productSupplierId) {
          productSupplier = await prisma.productSupplier.findUnique({
            where: { id: item.productSupplierId },
            select: {
              id: true,
              supplierProductCode: true,
            }
          });
        } else {
          // Otherwise, try to find the ProductSupplier relationship by product and supplier
          productSupplier = await prisma.productSupplier.findUnique({
            where: {
              productId_supplierId: {
                productId: item.productId!,
                supplierId: validatedData.supplierId
              }
            },
            select: {
              id: true,
              supplierProductCode: true,
            }
          });
        }

        return {
          productId: item.productId!,
          productSupplierId: productSupplier?.id || null,
          supplierProductCode: productSupplier?.supplierProductCode || null,
          quantity: item.quantity!,
          unitPrice: item.unitPrice!,
          subtotal: (item.quantity! * item.unitPrice!),
        };
      })
    );

    // Create purchase order with items
    const purchaseOrder = await prisma.purchaseOrder.create({
      data: {
        supplierId: validatedData.supplierId,
        orderDate: validatedData.orderDate,
        subtotal,
        tax: validatedData.tax || 0,
        taxPercentage: validatedData.taxPercentage,
        expectedDeliveryDate: validatedData.expectedDeliveryDate,
        total,
        notes: validatedData.notes,
        status: 'DRAFT', // All new POs start as DRAFT
        createdById: auth.user.id,
        items: {
          create: itemsWithSupplierInfo,
        },
      },
      include: {
        supplier: true,
        createdBy: {
          select: { id: true, name: true, email: true },
        },
        items: {
          include: {
            product: {
              include: {
                category: true,
                unit: true,
              },
            },
          },
        },
      },
    });

    // Create approval notifications if PO is submitted for approval
    console.log(`[PO Creation] isDraft: ${isDraft}, PO ID: ${purchaseOrder.id}, User ID: ${auth.user.id}`);
    if (!isDraft) {
      try {
        console.log(`[PO Creation] Creating approval notifications for PO ${purchaseOrder.id}`);
        const notifications = await createPOApprovalNotifications(purchaseOrder.id, auth.user.id);
        console.log(`[PO Creation] Successfully created ${notifications.length} notifications`);
      } catch (notificationError) {
        console.error('[PO Creation] Error creating approval notifications:', notificationError);
        // Don't fail the request if notifications fail
      }
    } else {
      console.log(`[PO Creation] Skipping notifications for draft PO`);
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'CREATE_PURCHASE_ORDER',
        details: `Created ${isDraft ? 'draft ' : ''}purchase order for supplier: ${supplier.name}`,
      },
    });

    return NextResponse.json(purchaseOrder, { status: 201 });
  } catch (error) {
    console.error('Error creating purchase order:', error);

    if (error instanceof z.ZodError) {
      console.error('Zod validation error:', error.issues);
      return NextResponse.json(
        { error: 'Validation failed', issues: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
