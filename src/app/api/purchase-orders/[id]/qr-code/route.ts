import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import QRCode from 'qrcode';

// GET /api/purchase-orders/[id]/qr-code - Generate QR code for PO with RECEIVED status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'json'; // 'json' or 'image'

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Get PO with all related data
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                barcode: true,
              },
            },
            productSupplier: {
              select: {
                supplierProductCode: true,
                supplierProductName: true,
              },
            },
          },
        },
        stockBatches: {
          where: {
            status: 'ACTIVE',
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                barcode: true,
              },
            },
            productSupplier: {
              include: {
                supplier: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
          orderBy: {
            receivedDate: 'desc',
          },
        },
      },
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Check if PO has RECEIVED status
    if (purchaseOrder.status !== 'RECEIVED') {
      return NextResponse.json(
        { error: 'QR code can only be generated for Purchase Orders with RECEIVED status' },
        { status: 400 }
      );
    }

    // Build QR code data structure
    const qrData = {
      type: 'PURCHASE_ORDER_BATCH_INFO',
      version: '1.0',
      generatedAt: new Date().toISOString(),
      purchaseOrder: {
        id: purchaseOrder.id,
        orderDate: purchaseOrder.orderDate.toISOString(),
        status: purchaseOrder.status,
        total: Number(purchaseOrder.total),
        supplier: {
          name: purchaseOrder.supplier.name,
          contactPerson: purchaseOrder.supplier.contactPerson,
          phone: purchaseOrder.supplier.phone,
        },
      },
      batches: purchaseOrder.stockBatches.map(batch => ({
        id: batch.id,
        batchNumber: batch.batchNumber,
        receivedDate: batch.receivedDate.toISOString(),
        expiryDate: batch.expiryDate?.toISOString() || null,
        quantity: Number(batch.quantity),
        remainingQuantity: Number(batch.remainingQuantity),
        purchasePrice: Number(batch.purchasePrice),
        product: {
          id: batch.product.id,
          name: batch.product.name,
          sku: batch.product.sku,
          barcode: batch.product.barcode,
        },
        supplier: {
          name: batch.productSupplier.supplier.name,
        },
        notes: batch.notes,
      })),
      summary: {
        totalBatches: purchaseOrder.stockBatches.length,
        totalProducts: [...new Set(purchaseOrder.stockBatches.map(b => b.productId))].length,
        totalValue: purchaseOrder.stockBatches.reduce((sum, batch) => 
          sum + (Number(batch.quantity) * Number(batch.purchasePrice)), 0
        ),
      },
      humanReadable: {
        poNumber: `PO-${purchaseOrder.id.slice(-8).toUpperCase()}`,
        supplier: purchaseOrder.supplier.name,
        receivedDate: purchaseOrder.receivedAt?.toLocaleDateString() || 'N/A',
        batchCount: purchaseOrder.stockBatches.length,
        productList: purchaseOrder.stockBatches.map(batch => 
          `${batch.product.name} (${batch.batchNumber || 'No batch'})`
        ).slice(0, 5), // Limit to first 5 for readability
      },
    };

    if (format === 'image') {
      // Generate QR code as image
      const qrCodeDataURL = await QRCode.toDataURL(JSON.stringify(qrData), {
        errorCorrectionLevel: 'M',
        type: 'image/png',
        quality: 0.92,
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF',
        },
        width: 512,
      });

      // Return the image data URL
      return NextResponse.json({
        qrCodeImage: qrCodeDataURL,
        data: qrData,
      });
    }

    // Return JSON format with QR data
    return NextResponse.json({
      data: qrData,
      qrString: JSON.stringify(qrData),
    });

  } catch (error) {
    console.error('Error generating QR code for PO:', error);
    return NextResponse.json(
      { error: 'Failed to generate QR code' },
      { status: 500 }
    );
  }
}
