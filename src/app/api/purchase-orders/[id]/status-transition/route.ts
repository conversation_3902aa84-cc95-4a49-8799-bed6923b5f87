import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { POStatus, POStatusChangeReason } from '@prisma/client';
import { isValidTransition } from '@/lib/po-status-management';
import { notifyPOStatusChange } from '@/lib/notifications/integrations/purchase-order-integration';
import { calculatePerformanceMetrics } from "@/lib/po-analytics";

// Status transition schema
const statusTransitionSchema = z.object({
  toStatus: z.enum([
    'DRAFT',
    'PENDING_APPROVAL',
    'APPROVED',
    'ORDERED',
    'SHIPPED',
    'PARTIALLY_RECEIVED',
    'RECEIVED',
    'CANCELLED',
    'OVERDUE',
    'ON_HOLD',
    'EXPEDITED',
  ]),
  reason: z.enum([
    'BUSINESS_REQUIREMENT',
    'SUPPLIER_REQUEST',
    'INVENTORY_SHORTAGE',
    'QUALITY_ISSUE',
    'DELIVERY_DELAY',
    'PRICE_CHANGE',
    'MANAGEMENT_DECISION',
    'SYSTEM_ERROR',
    'CUSTOMER_REQUEST',
    'OPERATIONAL_CHANGE',
    'OTHER',
  ]),
  notes: z.string().optional(),
  expectedDeliveryDate: z.string().datetime().optional().nullable(),
  holdReason: z.string().optional().nullable(),
  holdUntil: z.string().datetime().optional().nullable(),
});

// POST /api/purchase-orders/[id]/status-transition - Change PO status with validation
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = statusTransitionSchema.parse(body);

    // Get current PO
    const existingPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        supplier: true,
        items: true,
        statusHistory: {
          orderBy: { createdAt: "desc" },
          take: 1,
        }
      },
    });

    if (!existingPO) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Validate transition
    if (!isValidTransition(existingPO.status, validatedData.toStatus, auth.user.role)) {
      return NextResponse.json({
        error: `Invalid status transition from ${existingPO.status} to ${validatedData.toStatus}`,
      }, { status: 400 });
    }

    // Calculate time in current status
    const timeInCurrentStatus = existingPO.statusHistory[0]
      ? Math.floor((Date.now() - existingPO.statusHistory[0].createdAt.getTime()) / (1000 * 60))
      : 0;

    // Calculate performance metrics
    const {
      performanceScore,
      qualityScore,
      supplierScore,
      hasDelays,
      delayReason,
      batchMetrics
    } = await calculatePerformanceMetrics(existingPO, validatedData.toStatus);

    // Perform the status transition in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Prepare update data
      const updateData: any = {
        status: validatedData.toStatus,
        updatedAt: new Date(),
        lastTransitionAt: new Date(),
        timeInCurrentStatus,
        performanceScore,
        qualityScore,
        supplierScore,
        hasDelays,
        delayReason,
        ...batchMetrics
      };

      // Handle specific status transitions
      if (validatedData.toStatus === 'APPROVED' && existingPO.status === 'PENDING_APPROVAL') {
        updateData.approvedById = auth.user.id;
        updateData.approvedAt = new Date();
      }

      if (validatedData.toStatus === 'RECEIVED') {
        updateData.receivedAt = new Date();
      }

      if (validatedData.expectedDeliveryDate) {
        updateData.expectedDeliveryDate = new Date(validatedData.expectedDeliveryDate);
      }

      // Note: holdReason and holdUntil fields don't exist in the current schema
      // If needed, they should be added to the database schema first

      // Update the purchase order
      const updatedPO = await tx.purchaseOrder.update({
        where: { id },
        data: updateData,
        include: {
          supplier: true,
          createdBy: {
            select: { id: true, name: true, email: true },
          },
          approvedBy: {
            select: { id: true, name: true, email: true },
          },
          items: {
            include: {
              product: true,
            },
          },
          statusHistory: {
            orderBy: { createdAt: "desc" },
            take: 5
          }
        },
      });

      // Create status history record with analytics data
      await tx.pOStatusHistory.create({
        data: {
          purchaseOrderId: id,
          fromStatus: existingPO.status,
          toStatus: validatedData.toStatus,
          reason: validatedData.reason,
          notes: validatedData.notes,
          metadata: {
            timeInStatus: timeInCurrentStatus,
            performanceScore,
            qualityScore,
            supplierScore,
            ...batchMetrics
          },
          createdById: auth.user.id,
        },
      });

      return updatedPO;
    });

    // Create notifications for status changes using new notification system
    try {
      await notifyPOStatusChange(
        id,
        existingPO.status,
        validatedData.toStatus,
        auth.user.id,
        {
          reason: validatedData.reason,
          notes: validatedData.notes,
          expectedDeliveryDate: validatedData.expectedDeliveryDate,
          supplierName: existingPO.supplier.name,
          total: Number(existingPO.total),
          poNumber: existingPO.id.slice(-8).toUpperCase(),
        }
      );
      console.log(`✅ Notifications sent for PO ${id} status change: ${existingPO.status} → ${validatedData.toStatus}`);
    } catch (notificationError) {
      console.error('❌ Error creating status notifications:', notificationError);
      // Don't fail the request if notifications fail
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'CHANGE_PO_STATUS',
        details: `Changed PO status from ${existingPO.status} to ${validatedData.toStatus} for supplier: ${existingPO.supplier.name}`,
      },
    });

    return NextResponse.json(result);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        error: 'Validation failed',
        details: error.errors,
      }, { status: 400 });
    }

    console.error('Error changing PO status:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return NextResponse.json({
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
