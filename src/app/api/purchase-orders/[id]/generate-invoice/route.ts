import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';
import { Decimal } from '@prisma/client/runtime/library';

// Validation schema for generating invoice from PO
const generateInvoiceSchema = z.object({
  invoiceNumber: z.string().min(1, "Invoice number is required"),
  invoiceDate: z.string().datetime("Invalid invoice date"),
  dueDate: z.string().datetime("Invalid due date").optional(),
  taxPercentage: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  attachmentUrl: z.string().url().optional(),
  includeAllItems: z.boolean().default(true),
  selectedItems: z.array(z.object({
    purchaseOrderItemId: z.string(),
    quantity: z.number().positive().optional(),
    unitPrice: z.number().positive().optional(),
  })).optional(),
});

// POST /api/purchase-orders/[id]/generate-invoice - Generate invoice from PO
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check permissions
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id: purchaseOrderId } = await params;
    const body = await request.json();
    const validatedData = generateInvoiceSchema.parse(body);

    // Verify purchase order exists and is in correct status
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id: purchaseOrderId },
      include: {
        supplier: true,
        items: {
          include: {
            product: {
              include: {
                unit: true
              }
            }
          }
        },
        invoices: {
          select: { id: true, invoiceNumber: true, status: true }
        }
      }
    });

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    if (!['RECEIVED', 'PARTIALLY_RECEIVED'].includes(purchaseOrder.status)) {
      return NextResponse.json({ 
        error: 'Purchase order must be received before generating invoice' 
      }, { status: 400 });
    }

    // Check if invoice number already exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { invoiceNumber: validatedData.invoiceNumber }
    });

    if (existingInvoice) {
      return NextResponse.json({ 
        error: 'Invoice number already exists' 
      }, { status: 400 });
    }

    // Determine which items to include
    let itemsToInclude;
    
    if (validatedData.includeAllItems) {
      // Include all received items
      itemsToInclude = purchaseOrder.items
        .filter(item => Number(item.receivedQuantity) > 0)
        .map(item => ({
          purchaseOrderItemId: item.id,
          productId: item.productId,
          description: item.product.name,
          quantity: Number(item.receivedQuantity),
          unitPrice: Number(item.unitPrice),
        }));
    } else {
      // Include only selected items
      if (!validatedData.selectedItems || validatedData.selectedItems.length === 0) {
        return NextResponse.json({ 
          error: 'Selected items are required when not including all items' 
        }, { status: 400 });
      }

      itemsToInclude = validatedData.selectedItems.map(selectedItem => {
        const poItem = purchaseOrder.items.find(item => item.id === selectedItem.purchaseOrderItemId);
        
        if (!poItem) {
          throw new Error(`Purchase order item ${selectedItem.purchaseOrderItemId} not found`);
        }

        const quantity = selectedItem.quantity || Number(poItem.receivedQuantity);
        const unitPrice = selectedItem.unitPrice || Number(poItem.unitPrice);

        if (quantity > Number(poItem.receivedQuantity)) {
          throw new Error(`Quantity ${quantity} exceeds received quantity ${Number(poItem.receivedQuantity)} for item ${poItem.product.name}`);
        }

        return {
          purchaseOrderItemId: poItem.id,
          productId: poItem.productId,
          description: poItem.product.name,
          quantity,
          unitPrice,
        };
      });
    }

    if (itemsToInclude.length === 0) {
      return NextResponse.json({ 
        error: 'No items available for invoicing' 
      }, { status: 400 });
    }

    // Calculate totals
    let subtotal = 0;
    const invoiceItems = itemsToInclude.map(item => {
      const itemSubtotal = item.quantity * item.unitPrice;
      subtotal += itemSubtotal;
      return {
        ...item,
        subtotal: itemSubtotal,
      };
    });

    const taxPercentage = validatedData.taxPercentage || Number(purchaseOrder.taxPercentage) || 0;
    const tax = (subtotal * taxPercentage) / 100;
    const total = subtotal + tax;

    // Create invoice with items
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber: validatedData.invoiceNumber,
        purchaseOrderId: purchaseOrderId,
        supplierId: purchaseOrder.supplierId,
        invoiceDate: new Date(validatedData.invoiceDate),
        dueDate: validatedData.dueDate ? new Date(validatedData.dueDate) : null,
        subtotal: new Decimal(subtotal),
        tax: new Decimal(tax),
        taxPercentage: new Decimal(taxPercentage),
        total: new Decimal(total),
        notes: validatedData.notes,
        attachmentUrl: validatedData.attachmentUrl,
        createdById: auth.user.id,
        items: {
          create: invoiceItems.map(item => ({
            purchaseOrderItemId: item.purchaseOrderItemId,
            productId: item.productId,
            description: item.description,
            quantity: new Decimal(item.quantity),
            unitPrice: new Decimal(item.unitPrice),
            subtotal: new Decimal(item.subtotal),
          }))
        }
      },
      include: {
        supplier: {
          select: { id: true, name: true, email: true, phone: true, address: true }
        },
        purchaseOrder: {
          select: { id: true, orderDate: true, status: true }
        },
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        items: {
          include: {
            product: {
              select: { 
                id: true, 
                name: true, 
                sku: true,
                unit: {
                  select: { name: true, abbreviation: true }
                }
              }
            },
            purchaseOrderItem: {
              select: {
                id: true,
                quantity: true,
                receivedQuantity: true,
                unitPrice: true
              }
            }
          }
        }
      }
    });

    // Transform decimal fields for response
    const transformedInvoice = {
      ...invoice,
      subtotal: Number(invoice.subtotal),
      tax: Number(invoice.tax),
      taxPercentage: Number(invoice.taxPercentage),
      total: Number(invoice.total),
      paidAmount: Number(invoice.paidAmount),
      items: invoice.items.map(item => ({
        ...item,
        quantity: Number(item.quantity),
        unitPrice: Number(item.unitPrice),
        subtotal: Number(item.subtotal),
        purchaseOrderItem: item.purchaseOrderItem ? {
          ...item.purchaseOrderItem,
          quantity: Number(item.purchaseOrderItem.quantity),
          receivedQuantity: Number(item.purchaseOrderItem.receivedQuantity),
          unitPrice: Number(item.purchaseOrderItem.unitPrice),
        } : null,
      })),
    };

    return NextResponse.json({
      invoice: transformedInvoice,
      message: 'Invoice generated successfully from purchase order'
    }, { status: 201 });

  } catch (error) {
    console.error('Error generating invoice from PO:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation failed', details: error.errors },
        { status: 400 }
      );
    }

    if (error.message.includes('not found') || error.message.includes('exceeds')) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate invoice' },
      { status: 500 }
    );
  }
}
