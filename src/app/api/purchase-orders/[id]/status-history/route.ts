import { NextRequest, NextResponse } from 'next/server';
import { jwtVerify } from 'jose';
import { prisma } from '@/lib/prisma';
import { calculatePerformanceMetrics } from '@/lib/po-analytics';
import { POStatus, POStatusChangeReason, type PurchaseOrder, type StockBatch, type PurchaseOrderItem } from '@prisma/client';
import type { UserRole } from '@/auth';

interface StatusHistoryEntry {
  id: string;
  fromStatus: POStatus;
  toStatus: POStatus;
  reason: POStatusChangeReason;
  notes?: string | null;
  createdAt: Date;
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  metadata?: any;
  performanceData?: {
    timeInStatus: number;
    performanceScore: number;
    qualityScore: number;
    supplierScore: number;
    batchMetrics: {
      totalBatches: number;
      activeBatches: number;
      expiringBatches: number;
      expiredBatches: number;
    };
  };
}

type POWithBatches = PurchaseOrder & {
  items: PurchaseOrderItem[];
  stockBatches: StockBatch[];
};

// GET /api/purchase-orders/[id]/status-history - Get PO status history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the session token from cookies
    const token = request.cookies.get("session-token");

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verify the token
    let user;
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      user = {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      };
    } catch (error) {
      console.error('JWT verification failed:', error);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(user.role as UserRole);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { id } = await params;

    // Get PO with items and batches
    const purchaseOrder = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        stockBatches: true,
      },
    }) as POWithBatches | null;

    if (!purchaseOrder) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Get status history with performance data
    const statusHistory = await prisma.pOStatusHistory.findMany({
      where: { purchaseOrderId: id },
      include: {
        createdBy: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { createdAt: 'desc' },
    });

    // Calculate performance metrics for each status change
    const enrichedHistory = await Promise.all(
      statusHistory.map(async (entry) => {
        try {
          const performanceData = await calculatePerformanceMetrics(purchaseOrder, entry.toStatus);
          return {
            ...entry,
            performanceData,
          };
        } catch (error) {
          console.error('Error calculating performance metrics for entry:', entry.id, error);
          return {
            ...entry,
            performanceData: null,
          };
        }
      })
    );

    return NextResponse.json({
      purchaseOrderId: id,
      currentStatus: purchaseOrder.status,
      statusHistory: enrichedHistory,
    });
  } catch (error) {
    console.error('Error fetching PO status history:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });
    return NextResponse.json({
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
