import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { verifyAuthToken } from '@/lib/auth-utils';
import { createPOStatusNotifications } from '@/lib/notifications';

// Receiving item schema for validation
const receivingItemSchema = z.object({
  purchaseOrderItemId: z.string().min(1, { message: "Purchase order item ID is required" }),
  receivedQuantity: z.number().min(0, { message: "Received quantity must be non-negative" }),
  discrepancyReason: z.string().optional(),
  notes: z.string().optional(),
  // Batch information
  batchNumber: z.string().optional(),
  expiryDate: z.string().datetime().optional().nullable(),
  batchNotes: z.string().optional(),
});

// Purchase order receiving schema for validation
const purchaseOrderReceivingSchema = z.object({
  notes: z.string().optional(),
  discrepancyReason: z.string().optional(),
  items: z.array(receivingItemSchema).min(1, { message: "At least one item is required" }),
});

// POST /api/purchase-orders/[id]/receive - Process partial receiving
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    console.log("Received receiving data:", body);

    const validatedData = purchaseOrderReceivingSchema.parse(body);
    console.log("Validated receiving data:", validatedData);

    // Check if purchase order exists and can be received
    const existingPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      include: { 
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!existingPO) {
      return NextResponse.json({ error: 'Purchase order not found' }, { status: 404 });
    }

    // Check if PO can be received
    if (!['APPROVED', 'ORDERED', 'PARTIALLY_RECEIVED'].includes(existingPO.status)) {
      return NextResponse.json({ 
        error: 'Purchase order must be APPROVED, ORDERED, or PARTIALLY_RECEIVED to receive items' 
      }, { status: 400 });
    }

    // Validate receiving items
    const poItemIds = existingPO.items.map(item => item.id);
    const receivingItemIds = validatedData.items.map(item => item.purchaseOrderItemId);
    
    const invalidItemIds = receivingItemIds.filter(id => !poItemIds.includes(id));
    if (invalidItemIds.length > 0) {
      return NextResponse.json({ 
        error: `Invalid purchase order item IDs: ${invalidItemIds.join(', ')}` 
      }, { status: 400 });
    }

    // Validate received quantities don't exceed ordered quantities
    for (const receivingItem of validatedData.items) {
      const poItem = existingPO.items.find(item => item.id === receivingItem.purchaseOrderItemId);
      if (!poItem) continue;

      const currentReceived = Number(poItem.receivedQuantity || 0);
      const newReceived = receivingItem.receivedQuantity;
      const totalReceived = currentReceived + newReceived;
      const ordered = Number(poItem.quantity);

      if (totalReceived > ordered) {
        return NextResponse.json({ 
          error: `Cannot receive ${newReceived} of ${poItem.product.name}. ` +
                 `Already received ${currentReceived}, ordered ${ordered}. ` +
                 `Maximum additional quantity: ${ordered - currentReceived}` 
        }, { status: 400 });
      }
    }

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create receiving record
      const receiving = await tx.purchaseOrderReceiving.create({
        data: {
          purchaseOrderId: id,
          receivedById: auth.user!.id,
          notes: validatedData.notes,
          discrepancyReason: validatedData.discrepancyReason,
          items: {
            create: validatedData.items.map(item => {
              const poItem = existingPO.items.find(poi => poi.id === item.purchaseOrderItemId)!;
              const discrepancyQuantity = item.receivedQuantity - Number(poItem.quantity);
              
              return {
                purchaseOrderItemId: item.purchaseOrderItemId,
                receivedQuantity: item.receivedQuantity,
                discrepancyQuantity: discrepancyQuantity > 0 ? discrepancyQuantity : 0,
                discrepancyReason: item.discrepancyReason,
                notes: item.notes,
              };
            }),
          },
        },
        include: {
          items: true,
          receivedBy: {
            select: { id: true, name: true, email: true },
          },
        },
      });

      // Update purchase order item received quantities
      for (const receivingItem of validatedData.items) {
        if (receivingItem.receivedQuantity > 0) {
          await tx.purchaseOrderItem.update({
            where: { id: receivingItem.purchaseOrderItemId },
            data: {
              receivedQuantity: {
                increment: receivingItem.receivedQuantity,
              },
            },
          });
        }
      }

      // Update warehouse stock and create stock batches for received items
      for (const receivingItem of validatedData.items) {
        if (receivingItem.receivedQuantity > 0) {
          const poItem = existingPO.items.find(item => item.id === receivingItem.purchaseOrderItemId)!;

          // Get or create warehouse stock
          const warehouseStock = await tx.warehouseStock.upsert({
            where: { productId: poItem.productId },
            update: {
              quantity: {
                increment: receivingItem.receivedQuantity,
              },
              lastUpdated: new Date(),
            },
            create: {
              productId: poItem.productId,
              quantity: receivingItem.receivedQuantity,
              minThreshold: 0,
              lastUpdated: new Date(),
            },
          });

          // Create StockBatch record for traceability with enhanced batch information
          let stockBatch = null;
          if (poItem.productSupplierId) {
            // Generate batch number if not provided
            const batchNumber = receivingItem.batchNumber ||
              `PO-${id.slice(-8).toUpperCase()}-${Date.now()}`;

            // Parse expiry date if provided
            const expiryDate = receivingItem.expiryDate ?
              new Date(receivingItem.expiryDate) : null;

            // Combine notes
            const batchNotes = [
              `Received from PO: ${id.slice(-8).toUpperCase()}`,
              receivingItem.batchNotes,
              receivingItem.notes
            ].filter(Boolean).join(' | ');

            stockBatch = await tx.stockBatch.create({
              data: {
                productId: poItem.productId,
                productSupplierId: poItem.productSupplierId,
                batchNumber: batchNumber,
                receivedDate: new Date(),
                expiryDate: expiryDate,
                quantity: receivingItem.receivedQuantity,
                remainingQuantity: receivingItem.receivedQuantity,
                purchasePrice: poItem.unitPrice,
                purchaseOrderId: id,
                warehouseStockId: warehouseStock.id,
                status: 'ACTIVE',
                notes: batchNotes,
              },
            });
          }

          // Create stock history entry with batch and supplier information
          await tx.stockHistory.create({
            data: {
              productId: poItem.productId,
              productSupplierId: poItem.productSupplierId,
              batchId: stockBatch?.id,
              warehouseStockId: warehouseStock.id,
              previousQuantity: Number(warehouseStock.quantity) - receivingItem.receivedQuantity,
              newQuantity: Number(warehouseStock.quantity),
              changeQuantity: receivingItem.receivedQuantity,
              source: "PURCHASE",
              referenceId: receiving.id,
              referenceType: "PurchaseOrderReceiving",
              notes: `Partial receiving for PO: ${id.slice(-8).toUpperCase()}${stockBatch ? ` - Batch: ${stockBatch.batchNumber}` : ''}`,
              userId: auth.user!.id,
            },
          });
        }
      }

      // Check if all items are fully received and update PO status
      const updatedPO = await tx.purchaseOrder.findUnique({
        where: { id },
        include: { items: true },
      });

      if (updatedPO) {
        const allItemsFullyReceived = updatedPO.items.every(item => 
          Number(item.receivedQuantity) >= Number(item.quantity)
        );

        const anyItemsReceived = updatedPO.items.some(item => 
          Number(item.receivedQuantity) > 0
        );

        let newStatus = existingPO.status;
        let receivedAt = existingPO.receivedAt;

        if (allItemsFullyReceived) {
          newStatus = 'RECEIVED';
          receivedAt = new Date();
        } else if (anyItemsReceived) {
          newStatus = 'PARTIALLY_RECEIVED';
        }

        if (newStatus !== existingPO.status) {
          await tx.purchaseOrder.update({
            where: { id },
            data: {
              status: newStatus,
              receivedAt: receivedAt,
              lastTransitionAt: new Date(),
            },
          });

          // Create status history entry for the status change
          await tx.pOStatusHistory.create({
            data: {
              purchaseOrderId: id,
              fromStatus: existingPO.status,
              toStatus: newStatus,
              reason: 'OPERATIONAL_CHANGE',
              notes: `Status automatically updated to ${newStatus} after receiving items`,
              metadata: {
                triggeredBy: 'RECEIVING_PROCESS',
                itemsReceived: validatedData.items.length,
              },
              createdById: auth.user!.id,
            },
          });
        }
      }

      return receiving;
    });

    // Create notifications for receiving status changes
    const finalPO = await prisma.purchaseOrder.findUnique({
      where: { id },
      select: { status: true },
    });

    if (finalPO && finalPO.status !== existingPO.status) {
      try {
        await createPOStatusNotifications(id, finalPO.status, auth.user.id);
      } catch (notificationError) {
        console.error('Error creating receiving notifications:', notificationError);
        // Don't fail the request if notifications fail
      }
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: 'RECEIVE_PURCHASE_ORDER',
        details: `Processed receiving for purchase order: ${existingPO.supplier.name} - ${validatedData.items.length} items`,
      },
    });

    console.log("Receiving processed successfully:", result.id);

    return NextResponse.json({
      message: 'Receiving processed successfully',
      receiving: result,
    });

  } catch (error) {
    console.error('Error processing receiving:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
