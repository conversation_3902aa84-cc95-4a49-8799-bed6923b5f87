import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { qualityScheduler } from '@/lib/quality-scheduler';

// GET /api/quality-scheduler - Get scheduler status and jobs
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const jobId = searchParams.get('jobId');

    if (action === 'jobs') {
      // Get all jobs
      const jobs = qualityScheduler.getJobs();
      return NextResponse.json({
        success: true,
        jobs,
        totalJobs: jobs.length,
        enabledJobs: jobs.filter(j => j.enabled).length,
      });
    } else if (action === 'job' && jobId) {
      // Get specific job
      const job = qualityScheduler.getJob(jobId);
      if (!job) {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      return NextResponse.json({
        success: true,
        job,
      });
    } else {
      // Get scheduler status
      const jobs = qualityScheduler.getJobs();
      return NextResponse.json({
        success: true,
        status: 'running', // Simplified - in production, track actual status
        totalJobs: jobs.length,
        enabledJobs: jobs.filter(j => j.enabled).length,
        recentJobs: jobs
          .filter(j => j.lastRun)
          .sort((a, b) => new Date(b.lastRun!).getTime() - new Date(a.lastRun!).getTime())
          .slice(0, 5),
      });
    }
  } catch (error) {
    console.error('Error in quality scheduler GET:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get scheduler information',
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

// POST /api/quality-scheduler - Control scheduler and jobs
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admins can control scheduler
    const canControl = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!canControl) {
      return NextResponse.json({ error: 'Insufficient permissions to control scheduler' }, { status: 403 });
    }

    const body = await request.json();
    const action = body.action;
    const jobId = body.jobId;

    switch (action) {
      case 'initialize':
        await qualityScheduler.initialize();
        return NextResponse.json({
          success: true,
          message: 'Quality scheduler initialized successfully',
          initializedBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
          initializedAt: new Date(),
        });

      case 'start':
        qualityScheduler.start();
        return NextResponse.json({
          success: true,
          message: 'Quality scheduler started',
          startedBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
          startedAt: new Date(),
        });

      case 'stop':
        qualityScheduler.stop();
        return NextResponse.json({
          success: true,
          message: 'Quality scheduler stopped',
          stoppedBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
          stoppedAt: new Date(),
        });

      case 'trigger_job':
        if (!jobId) {
          return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
        }
        
        console.log(`Job ${jobId} triggered manually by ${auth.user.name} (${auth.user.role})`);
        const result = await qualityScheduler.triggerJob(jobId);
        
        return NextResponse.json({
          success: true,
          message: 'Job triggered successfully',
          result,
          triggeredBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
          triggeredAt: new Date(),
        });

      case 'enable_job':
        if (!jobId) {
          return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
        }
        
        await qualityScheduler.setJobEnabled(jobId, true);
        return NextResponse.json({
          success: true,
          message: 'Job enabled successfully',
          jobId,
          enabledBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
        });

      case 'disable_job':
        if (!jobId) {
          return NextResponse.json({ error: 'Job ID is required' }, { status: 400 });
        }
        
        await qualityScheduler.setJobEnabled(jobId, false);
        return NextResponse.json({
          success: true,
          message: 'Job disabled successfully',
          jobId,
          disabledBy: {
            id: auth.user.id,
            name: auth.user.name,
            role: auth.user.role,
          },
        });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in quality scheduler POST:', error);
    return NextResponse.json(
      { 
        error: 'Failed to control scheduler',
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
