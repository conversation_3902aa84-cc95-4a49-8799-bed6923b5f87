import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Drawer session schema for validation
const drawerSessionSchema = z.object({
  drawerId: z.string(),
  terminalId: z.string().min(1, "Terminal is required"), // Made required
  businessDate: z.string().transform((val) => new Date(val)),
  openingBalance: z.number().min(0),
});

// GET /api/drawer-sessions - Get all drawer sessions
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view drawer sessions
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");
    const userId = searchParams.get("userId");
    const drawerId = searchParams.get("drawerId");
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build filter
    const filter: any = {};
    if (status) {
      filter.status = status;
    }
    if (userId) {
      filter.userId = userId;
    }
    if (drawerId) {
      filter.drawerId = drawerId;
    }

    // For cashiers, only show their own sessions
    if (auth.user.role === "CASHIER") {
      filter.userId = auth.user.id;
    }

    // Get drawer sessions with pagination
    const [sessions, totalCount] = await Promise.all([
      prisma.drawerSession.findMany({
        where: filter,
        include: {
          drawer: true,
          terminal: true,
          user: {
            select: {
              id: true,
              name: true,
            },
          },
          _count: {
            select: {
              transactions: true,
            },
          },
        },
        orderBy: {
          openedAt: "desc",
        },
        skip,
        take: limit,
      }),
      prisma.drawerSession.count({ where: filter }),
    ]);

    return NextResponse.json({
      sessions,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching drawer sessions:", error);
    return NextResponse.json(
      { error: "Failed to fetch drawer sessions", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/drawer-sessions - Create a new drawer session
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only cashiers, finance admins, and super admins can open drawer sessions
    const allowedRoles = ["CASHIER", "SUPER_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Only cashiers and admins can open drawer sessions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = drawerSessionSchema.parse(body);

    // Check if drawer exists and is active
    const drawer = await prisma.cashDrawer.findUnique({
      where: { id: validatedData.drawerId },
    });

    if (!drawer) {
      return NextResponse.json(
        { error: "Cash drawer not found" },
        { status: 404 }
      );
    }

    if (!drawer.isActive) {
      return NextResponse.json(
        { error: "Cash drawer is not active" },
        { status: 400 }
      );
    }

    // Check if terminal exists and is active
    const terminal = await prisma.terminal.findUnique({
      where: { id: validatedData.terminalId },
      include: { drawer: true },
    });

    if (!terminal) {
      return NextResponse.json(
        { error: "Terminal not found" },
        { status: 404 }
      );
    }

    if (!terminal.isActive) {
      return NextResponse.json(
        { error: "Terminal is not active" },
        { status: 400 }
      );
    }

    // Enforce terminal-drawer assignment (Best Practice #1)
    if (terminal.drawerId !== validatedData.drawerId) {
      return NextResponse.json(
        {
          error: "Terminal and drawer are not assigned to each other",
          message: `Terminal "${terminal.name}" is not assigned to drawer "${drawer.name}". Please assign them first in the Terminal Map.`
        },
        { status: 400 }
      );
    }

    // Check if user already has an open drawer session (Best Practice #2)
    const existingUserSession = await prisma.drawerSession.findFirst({
      where: {
        userId: auth.user.id,
        status: "OPEN",
      },
    });

    if (existingUserSession) {
      return NextResponse.json(
        {
          error: "You already have an open drawer session",
          message: "Please close your current drawer session before opening a new one",
          sessionId: existingUserSession.id
        },
        { status: 400 }
      );
    }

    // Check if terminal already has an open session (Best Practice #3)
    const existingTerminalSession = await prisma.drawerSession.findFirst({
      where: {
        terminalId: validatedData.terminalId,
        status: "OPEN",
      },
      include: {
        user: { select: { name: true } },
        drawer: { select: { name: true } }
      }
    });

    if (existingTerminalSession) {
      return NextResponse.json(
        {
          error: "Terminal is already in use",
          message: `Terminal "${terminal.name}" is currently being used by ${existingTerminalSession.user.name} with drawer "${existingTerminalSession.drawer.name}"`
        },
        { status: 400 }
      );
    }

    // Calculate shift number for the day (Best Practice #4)
    const startOfDay = new Date(validatedData.businessDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(validatedData.businessDate);
    endOfDay.setHours(23, 59, 59, 999);

    const sessionsToday = await prisma.drawerSession.count({
      where: {
        terminalId: validatedData.terminalId,
        drawerId: validatedData.drawerId,
        businessDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
    });

    const shiftNumber = sessionsToday + 1;

    // Find the most recent session for this terminal/drawer combination for session chaining
    const previousSession = await prisma.drawerSession.findFirst({
      where: {
        terminalId: validatedData.terminalId,
        drawerId: validatedData.drawerId,
        status: "CLOSED",
      },
      orderBy: {
        closedAt: "desc",
      },
      include: {
        user: {
          select: {
            name: true,
          },
        },
      },
    });

    // Create drawer session with enhanced tracking
    const session = await prisma.drawerSession.create({
      data: {
        drawerId: validatedData.drawerId,
        userId: auth.user.id,
        terminalId: validatedData.terminalId,
        businessDate: validatedData.businessDate,
        openingBalance: validatedData.openingBalance,
        shiftNumber: shiftNumber,
        previousSessionId: previousSession?.id,
      },
      include: {
        drawer: true,
        terminal: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
        previousSession: {
          select: {
            id: true,
            closedAt: true,
            actualClosingBalance: true,
            user: { select: { name: true } }
          }
        }
      },
    });

    // Log activity with enhanced details
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "OPEN_DRAWER_SESSION",
        details: `Opened drawer session #${shiftNumber} for ${drawer.name} at ${terminal.name} with opening balance ${validatedData.openingBalance}${previousSession ? ` (following session by ${previousSession.user?.name || 'unknown user'})` : ' (first session of the day)'}`,
      },
    });

    return NextResponse.json({ session }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating drawer session:", error);
    return NextResponse.json(
      { error: "Failed to create drawer session", message: (error as Error).message },
      { status: 500 }
    );
  }
}
