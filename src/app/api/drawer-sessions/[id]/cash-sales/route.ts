import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/drawer-sessions/[id]/cash-sales - Get cash sales for a specific drawer session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get drawer session
    const session = await prisma.drawerSession.findUnique({
      where: { id },
      include: {
        drawer: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!session) {
      return NextResponse.json(
        { error: "Drawer session not found" },
        { status: 404 }
      );
    }

    // Check permissions - only the session owner or finance/super admin can view
    const hasPermission =
      session.userId === auth.user.id ||
      ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);

    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - You cannot view this drawer session" },
        { status: 403 }
      );
    }

    // Get all cash transactions for this drawer session
    const transactions = await prisma.transaction.findMany({
      where: {
        drawerSessionId: session.id,
        paymentMethod: "CASH",
        status: {
          not: "VOIDED",
        },
      },
      select: {
        id: true,
        total: true,
        createdAt: true,
      },
    });

    // Calculate total cash sales
    const totalCashSales = transactions.reduce((sum, transaction) => sum + Number(transaction.total), 0);

    return NextResponse.json({
      sessionId: session.id,
      drawerName: session.drawer.name,
      cashierName: session.user.name,
      openingBalance: Number(session.openingBalance),
      totalCashSales,
      transactionCount: transactions.length,
      expectedBalance: Number(session.openingBalance) + totalCashSales,
      transactions: transactions.map(t => ({
        id: t.id,
        total: Number(t.total),
        createdAt: t.createdAt,
      })),
    });
  } catch (error) {
    console.error("Error fetching drawer session cash sales:", error);
    return NextResponse.json(
      { error: "Failed to fetch cash sales", message: (error as Error).message },
      { status: 500 }
    );
  }
}
