import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/drawer-sessions/[id] - Get a specific drawer session
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view drawer sessions
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get drawer session
    const session = await prisma.drawerSession.findUnique({
      where: { id },
      include: {
        drawer: true,
        user: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!session) {
      return NextResponse.json(
        { error: "Drawer session not found" },
        { status: 404 }
      );
    }

    // For cashiers, only allow viewing their own sessions
    if (auth.user.role === "CASHIER" && session.userId !== auth.user.id) {
      return NextResponse.json(
        { error: "Unauthorized - You can only view your own drawer sessions" },
        { status: 403 }
      );
    }

    // Get transactions for this session
    const transactions = await prisma.transaction.findMany({
      where: {
        drawerSessionId: session.id,
        status: {
          not: "VOIDED",
        },
      },
      include: {
        cashier: {
          select: {
            id: true,
            name: true,
          },
        },
        customer: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        transactionDate: "desc",
      },
    });

    // Calculate totals
    const totalCashSales = transactions
      .filter(t => t.paymentMethod === "CASH")
      .reduce((sum, t) => sum + Number(t.total), 0);

    const totalDebitSales = transactions
      .filter(t => t.paymentMethod === "DEBIT")
      .reduce((sum, t) => sum + Number(t.total), 0);

    const totalQrisSales = transactions
      .filter(t => t.paymentMethod === "QRIS")
      .reduce((sum, t) => sum + Number(t.total), 0);

    const totalSales = totalCashSales + totalDebitSales + totalQrisSales;

    // Calculate expected closing balance
    const expectedClosingBalance = Number(session.openingBalance) + totalCashSales;

    return NextResponse.json({
      session: {
        ...session,
        expectedClosingBalance: session.expectedClosingBalance || expectedClosingBalance,
      },
      transactions,
      stats: {
        totalTransactions: transactions.length,
        totalSales,
        totalCashSales,
        totalDebitSales,
        totalQrisSales,
        expectedClosingBalance,
      },
    });
  } catch (error) {
    console.error("Error fetching drawer session:", error);
    return NextResponse.json(
      { error: "Failed to fetch drawer session", message: (error as Error).message },
      { status: 500 }
    );
  }
}
