import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a quality improvement plan
const createQualityImprovementSchema = z.object({
  supplierId: z.string().min(1, 'Supplier ID is required'),
  qualityIssueId: z.string().optional(),
  improvementType: z.enum([
    'PROCESS_IMPROVEMENT',
    'TRAINING',
    'EQUIPMENT_UPGRADE',
    'SUPPLIER_AUDIT',
    'QUALITY_CONTROL_ENHANCEMENT',
    'CORRECTIVE_ACTION',
    'PREVENTIVE_ACTION',
    'SUPPLIER_COLLABORATION',
    'OTHER'
  ]),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  targetMetric: z.string().optional(),
  currentValue: z.number().optional(),
  targetValue: z.number().optional(),
  startDate: z.string().transform((val) => new Date(val)),
  targetCompletionDate: z.string().transform((val) => new Date(val)),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
  assignedTo: z.string().optional(),
  notes: z.string().optional(),
});

// Schema for updating a quality improvement plan
const updateQualityImprovementSchema = z.object({
  status: z.enum(['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED']).optional(),
  progress: z.number().min(0).max(100).optional(),
  actualCompletionDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  effectivenessScore: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
});

// GET /api/quality-improvements - List quality improvement plans with filtering
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const supplierId = searchParams.get('supplierId');
    const improvementType = searchParams.get('improvementType');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) where.status = status;
    if (priority) where.priority = priority;
    if (supplierId) where.supplierId = supplierId;
    if (improvementType) where.improvementType = improvementType;
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [qualityImprovements, total] = await Promise.all([
      prisma.supplierQualityImprovement.findMany({
        where,
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
            },
          },
          qualityIssue: {
            select: {
              id: true,
              issueType: true,
              severity: true,
              description: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          creator: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          approver: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          actions: {
            include: {
              assignee: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
        },
        orderBy: [
          { priority: 'desc' },
          { startDate: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.supplierQualityImprovement.count({ where }),
    ]);

    return NextResponse.json({
      qualityImprovements,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching quality improvements:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/quality-improvements - Create new quality improvement plan
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canCreateImprovement = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canCreateImprovement) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();

    // Clean up empty string values that should be undefined
    if (body.qualityIssueId === '') {
      body.qualityIssueId = undefined;
    }
    if (body.assignedTo === '') {
      body.assignedTo = undefined;
    }

    const validatedData = createQualityImprovementSchema.parse(body);

    // Verify supplier exists
    const supplier = await prisma.supplier.findUnique({
      where: { id: validatedData.supplierId },
      select: { id: true, name: true },
    });

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // Verify quality issue exists if provided
    if (validatedData.qualityIssueId) {
      const qualityIssue = await prisma.qualityIssue.findUnique({
        where: { id: validatedData.qualityIssueId },
        select: { id: true, supplierId: true },
      });

      if (!qualityIssue) {
        return NextResponse.json({ error: 'Quality issue not found' }, { status: 404 });
      }

      if (qualityIssue.supplierId !== validatedData.supplierId) {
        return NextResponse.json({ error: 'Quality issue does not belong to the specified supplier' }, { status: 400 });
      }
    }

    // Verify assignee exists if provided
    if (validatedData.assignedTo) {
      const assignee = await prisma.user.findUnique({
        where: { id: validatedData.assignedTo },
        select: { id: true, name: true },
      });

      if (!assignee) {
        return NextResponse.json({ error: 'Assignee not found' }, { status: 404 });
      }
    }

    // Create quality improvement plan
    const newQualityImprovement = await prisma.supplierQualityImprovement.create({
      data: {
        ...validatedData,
        createdBy: auth.user.id,
      },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
          },
        },
        qualityIssue: {
          select: {
            id: true,
            issueType: true,
            severity: true,
            description: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        creator: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
    });

    // TODO: Send notification to assignee and supplier
    // This will be implemented in the notification service

    return NextResponse.json(newQualityImprovement, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }

    // Handle Prisma foreign key constraint errors
    if (error && typeof error === 'object' && 'code' in error) {
      if (error.code === 'P2003') {
        const meta = (error as any).meta;
        if (meta?.field_name === 'qualityIssueId') {
          return NextResponse.json({ error: 'Selected quality issue does not exist or is invalid' }, { status: 400 });
        }
        if (meta?.field_name === 'supplierId') {
          return NextResponse.json({ error: 'Selected supplier does not exist or is invalid' }, { status: 400 });
        }
        if (meta?.field_name === 'assignedTo') {
          return NextResponse.json({ error: 'Selected assignee does not exist or is invalid' }, { status: 400 });
        }
        return NextResponse.json({ error: 'Invalid reference to related data' }, { status: 400 });
      }
    }

    console.error('Error creating quality improvement:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
