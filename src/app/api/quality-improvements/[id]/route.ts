import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for updating a quality improvement plan
const updateQualityImprovementSchema = z.object({
  status: z.enum(['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED']).optional(),
  progress: z.number().min(0).max(100).optional(),
  actualCompletionDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  effectivenessScore: z.number().min(0).max(100).optional(),
  notes: z.string().optional(),
  targetCompletionDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  assignedTo: z.string().optional(),
});

// GET /api/quality-improvements/[id] - Get specific quality improvement plan
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const qualityImprovement = await prisma.supplierQualityImprovement.findUnique({
      where: { id },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        qualityIssue: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
              },
            },
            batch: {
              select: {
                id: true,
                batchNumber: true,
                receivedDate: true,
                expiryDate: true,
              },
            },
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
        creator: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        approver: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        actions: {
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!qualityImprovement) {
      return NextResponse.json({ error: 'Quality improvement plan not found' }, { status: 404 });
    }

    return NextResponse.json(qualityImprovement);
  } catch (error) {
    console.error('Error fetching quality improvement:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/quality-improvements/[id] - Update quality improvement plan
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canUpdateImprovement = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canUpdateImprovement) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateQualityImprovementSchema.parse(body);

    // Check if quality improvement exists
    const existingImprovement = await prisma.supplierQualityImprovement.findUnique({
      where: { id },
      select: { id: true, status: true, assignedTo: true },
    });

    if (!existingImprovement) {
      return NextResponse.json({ error: 'Quality improvement plan not found' }, { status: 404 });
    }

    // Verify assignee exists if provided
    if (validatedData.assignedTo) {
      const assignee = await prisma.user.findUnique({
        where: { id: validatedData.assignedTo },
        select: { id: true, name: true },
      });

      if (!assignee) {
        return NextResponse.json({ error: 'Assignee not found' }, { status: 404 });
      }
    }

    // Prepare update data
    const updateData: any = { ...validatedData };
    
    // If status is being changed to COMPLETED, set completion date if not provided
    if (validatedData.status === 'COMPLETED' && !validatedData.actualCompletionDate) {
      updateData.actualCompletionDate = new Date();
    }

    // Update quality improvement plan
    const updatedQualityImprovement = await prisma.supplierQualityImprovement.update({
      where: { id },
      data: updateData,
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
          },
        },
        qualityIssue: {
          select: {
            id: true,
            issueType: true,
            severity: true,
            description: true,
          },
        },
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        creator: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        approver: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        actions: {
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    // TODO: Send notifications for status changes
    // This will be implemented in the notification service

    return NextResponse.json(updatedQualityImprovement);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating quality improvement:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/quality-improvements/[id] - Delete quality improvement plan
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only SUPER_ADMIN can delete
    if (auth.user.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Only SUPER_ADMIN can delete quality improvement plans' }, { status: 403 });
    }

    // Check if quality improvement exists
    const existingImprovement = await prisma.supplierQualityImprovement.findUnique({
      where: { id },
      select: { id: true, status: true, title: true },
    });

    if (!existingImprovement) {
      return NextResponse.json({ error: 'Quality improvement plan not found' }, { status: 404 });
    }

    // Don't allow deletion of completed improvements (for audit trail)
    if (existingImprovement.status === 'COMPLETED') {
      return NextResponse.json({ 
        error: 'Cannot delete completed quality improvement plans. Consider marking as cancelled instead.' 
      }, { status: 400 });
    }

    // Delete the quality improvement plan (this will cascade to actions)
    await prisma.supplierQualityImprovement.delete({
      where: { id },
    });

    return NextResponse.json({
      message: 'Quality improvement plan deleted successfully',
      deletedId: id,
      deletedTitle: existingImprovement.title,
      deletedBy: {
        id: auth.user.id,
        name: auth.user.name,
        role: auth.user.role,
      },
      deletedAt: new Date(),
    });
  } catch (error) {
    console.error('Error deleting quality improvement:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
