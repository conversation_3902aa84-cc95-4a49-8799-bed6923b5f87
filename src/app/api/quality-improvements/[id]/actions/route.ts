import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a quality improvement action
const createQualityActionSchema = z.object({
  actionType: z.enum([
    'SUPPLIER_MEETING',
    'PROCESS_REVIEW',
    'TRAINING_SESSION',
    'AUDIT',
    'DOCUMENTATION_UPDATE',
    'SYSTEM_CHANGE',
    'MONITORING_SETUP',
    'FOLLOW_UP',
    'OTHER'
  ]),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  assignedTo: z.string().optional(),
  dueDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  notes: z.string().optional(),
});

// Schema for updating a quality improvement action
const updateQualityActionSchema = z.object({
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
  completedAt: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  notes: z.string().optional(),
  assignedTo: z.string().optional(),
  dueDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
});

// GET /api/quality-improvements/[id]/actions - Get actions for a quality improvement plan
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Verify quality improvement exists
    const qualityImprovement = await prisma.supplierQualityImprovement.findUnique({
      where: { id },
      select: { id: true, title: true },
    });

    if (!qualityImprovement) {
      return NextResponse.json({ error: 'Quality improvement plan not found' }, { status: 404 });
    }

    // Get actions for the quality improvement plan
    const actions = await prisma.qualityImprovementAction.findMany({
      where: { qualityImprovementId: id },
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
      },
      orderBy: [
        { status: 'asc' }, // Pending first
        { dueDate: 'asc' }, // Then by due date
        { createdAt: 'desc' }, // Then by creation date
      ],
    });

    // Calculate summary statistics
    const summary = {
      total: actions.length,
      pending: actions.filter(a => a.status === 'PENDING').length,
      inProgress: actions.filter(a => a.status === 'IN_PROGRESS').length,
      completed: actions.filter(a => a.status === 'COMPLETED').length,
      cancelled: actions.filter(a => a.status === 'CANCELLED').length,
      overdue: actions.filter(a => 
        a.dueDate && 
        a.status !== 'COMPLETED' && 
        a.status !== 'CANCELLED' && 
        new Date(a.dueDate) < new Date()
      ).length,
    };

    return NextResponse.json({
      qualityImprovementId: id,
      qualityImprovementTitle: qualityImprovement.title,
      actions,
      summary,
    });
  } catch (error) {
    console.error('Error fetching quality improvement actions:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/quality-improvements/[id]/actions - Create new action for quality improvement plan
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canCreateAction = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canCreateAction) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createQualityActionSchema.parse(body);

    // Verify quality improvement exists
    const qualityImprovement = await prisma.supplierQualityImprovement.findUnique({
      where: { id },
      select: { id: true, title: true, status: true },
    });

    if (!qualityImprovement) {
      return NextResponse.json({ error: 'Quality improvement plan not found' }, { status: 404 });
    }

    // Don't allow adding actions to completed or cancelled plans
    if (qualityImprovement.status === 'COMPLETED' || qualityImprovement.status === 'CANCELLED') {
      return NextResponse.json({ 
        error: `Cannot add actions to ${qualityImprovement.status.toLowerCase()} quality improvement plans` 
      }, { status: 400 });
    }

    // Verify assignee exists if provided
    if (validatedData.assignedTo) {
      const assignee = await prisma.user.findUnique({
        where: { id: validatedData.assignedTo },
        select: { id: true, name: true },
      });

      if (!assignee) {
        return NextResponse.json({ error: 'Assignee not found' }, { status: 404 });
      }
    }

    // Create quality improvement action
    const newAction = await prisma.qualityImprovementAction.create({
      data: {
        qualityImprovementId: id,
        ...validatedData,
      },
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
        qualityImprovement: {
          select: {
            id: true,
            title: true,
            supplier: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    // Update quality improvement progress after adding new action
    await updateQualityImprovementProgress(id);

    // TODO: Send notification to assignee
    // This will be implemented in the notification service

    return NextResponse.json(newAction, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error creating quality improvement action:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Update quality improvement progress based on completed actions
 */
async function updateQualityImprovementProgress(qualityImprovementId: string): Promise<void> {
  try {
    // Get all actions for the quality improvement
    const actions = await prisma.qualityImprovementAction.findMany({
      where: { qualityImprovementId },
      select: { status: true },
    });

    if (actions.length === 0) {
      // If no actions, set progress to 0
      await prisma.supplierQualityImprovement.update({
        where: { id: qualityImprovementId },
        data: { progress: 0 },
      });
      return;
    }

    // Calculate progress percentage
    const completedActions = actions.filter(a => a.status === 'COMPLETED').length;
    const progress = Math.round((completedActions / actions.length) * 100);

    // Update the quality improvement progress
    await prisma.supplierQualityImprovement.update({
      where: { id: qualityImprovementId },
      data: { progress },
    });

    console.log(`Updated progress for quality improvement ${qualityImprovementId}: ${completedActions}/${actions.length} = ${progress}%`);
  } catch (error) {
    console.error('Error updating quality improvement progress:', error);
    // Don't throw error to avoid breaking the main operation
  }
}

// PATCH /api/quality-improvements/[id]/actions/[actionId] - Update action (handled by separate endpoint)
// DELETE /api/quality-improvements/[id]/actions/[actionId] - Delete action (handled by separate endpoint)
