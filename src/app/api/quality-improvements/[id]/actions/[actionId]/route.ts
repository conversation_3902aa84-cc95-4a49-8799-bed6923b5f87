import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for updating a quality improvement action
const updateQualityActionSchema = z.object({
  actionType: z.enum([
    'SUPPLIER_MEETING',
    'PROCESS_REVIEW',
    'TRAINING_SESSION',
    'AUDIT',
    'DOCUMENTATION_UPDATE',
    'SYSTEM_CHANGE',
    'MONITORING_SETUP',
    'FOLLOW_UP',
    'OTHER'
  ]).optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED']).optional(),
  completedAt: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  notes: z.string().optional(),
  assignedTo: z.string().optional(),
  dueDate: z.string().transform((val) => val ? new Date(val) : undefined).optional(),
  title: z.string().optional(),
  description: z.string().optional(),
});

// GET /api/quality-improvements/[id]/actions/[actionId] - Get specific action
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; actionId: string }> }
) {
  try {
    const { id, actionId } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const action = await prisma.qualityImprovementAction.findUnique({
      where: { id: actionId },
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
        qualityImprovement: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
              },
            },
            creator: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
        },
      },
    });

    if (!action) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 });
    }

    // Verify action belongs to the specified quality improvement
    if (action.qualityImprovementId !== id) {
      return NextResponse.json({ error: 'Action does not belong to the specified quality improvement plan' }, { status: 400 });
    }

    return NextResponse.json(action);
  } catch (error) {
    console.error('Error fetching quality improvement action:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/quality-improvements/[id]/actions/[actionId] - Update action
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; actionId: string }> }
) {
  try {
    const { id, actionId } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canUpdateAction = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canUpdateAction) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateQualityActionSchema.parse(body);

    // Check if action exists and belongs to the quality improvement
    const existingAction = await prisma.qualityImprovementAction.findUnique({
      where: { id: actionId },
      select: { 
        id: true, 
        qualityImprovementId: true, 
        status: true,
        assignedTo: true,
      },
    });

    if (!existingAction) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 });
    }

    if (existingAction.qualityImprovementId !== id) {
      return NextResponse.json({ error: 'Action does not belong to the specified quality improvement plan' }, { status: 400 });
    }

    // Verify assignee exists if provided
    if (validatedData.assignedTo) {
      const assignee = await prisma.user.findUnique({
        where: { id: validatedData.assignedTo },
        select: { id: true, name: true },
      });

      if (!assignee) {
        return NextResponse.json({ error: 'Assignee not found' }, { status: 404 });
      }
    }

    // Prepare update data
    const updateData: any = { ...validatedData };
    
    // If status is being changed to COMPLETED, set completion date if not provided
    if (validatedData.status === 'COMPLETED' && !validatedData.completedAt) {
      updateData.completedAt = new Date();
    }

    // Update action
    const updatedAction = await prisma.qualityImprovementAction.update({
      where: { id: actionId },
      data: updateData,
      include: {
        assignee: {
          select: {
            id: true,
            name: true,
            role: true,
            email: true,
          },
        },
        qualityImprovement: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
              },
            },
          },
        },
      },
    });

    // Update quality improvement progress whenever action status changes
    if (validatedData.status) {
      await updateQualityImprovementProgress(id);
    }

    // TODO: Send notifications for status changes
    // This will be implemented in the notification service

    return NextResponse.json(updatedAction);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating quality improvement action:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/quality-improvements/[id]/actions/[actionId] - Delete action
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; actionId: string }> }
) {
  try {
    const { id, actionId } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only SUPER_ADMIN and WAREHOUSE_ADMIN can delete
    const canDeleteAction = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!canDeleteAction) {
      return NextResponse.json({ error: 'Insufficient permissions to delete actions' }, { status: 403 });
    }

    // Check if action exists and belongs to the quality improvement
    const existingAction = await prisma.qualityImprovementAction.findUnique({
      where: { id: actionId },
      select: { 
        id: true, 
        qualityImprovementId: true, 
        status: true,
        title: true,
      },
    });

    if (!existingAction) {
      return NextResponse.json({ error: 'Action not found' }, { status: 404 });
    }

    if (existingAction.qualityImprovementId !== id) {
      return NextResponse.json({ error: 'Action does not belong to the specified quality improvement plan' }, { status: 400 });
    }

    // Don't allow deletion of completed actions (for audit trail)
    if (existingAction.status === 'COMPLETED') {
      return NextResponse.json({ 
        error: 'Cannot delete completed actions. Consider marking as cancelled instead.' 
      }, { status: 400 });
    }

    // Delete the action
    await prisma.qualityImprovementAction.delete({
      where: { id: actionId },
    });

    // Update quality improvement progress
    await updateQualityImprovementProgress(id);

    return NextResponse.json({
      message: 'Action deleted successfully',
      deletedId: actionId,
      deletedTitle: existingAction.title,
      deletedBy: {
        id: auth.user.id,
        name: auth.user.name,
        role: auth.user.role,
      },
      deletedAt: new Date(),
    });
  } catch (error) {
    console.error('Error deleting quality improvement action:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

/**
 * Update quality improvement progress based on completed actions
 */
async function updateQualityImprovementProgress(qualityImprovementId: string): Promise<void> {
  try {
    // Get all actions for the quality improvement
    const actions = await prisma.qualityImprovementAction.findMany({
      where: { qualityImprovementId },
      select: { status: true },
    });

    if (actions.length === 0) {
      return;
    }

    // Calculate progress percentage
    const completedActions = actions.filter(a => a.status === 'COMPLETED').length;
    const progress = Math.round((completedActions / actions.length) * 100);

    // Update the quality improvement progress
    await prisma.supplierQualityImprovement.update({
      where: { id: qualityImprovementId },
      data: { progress },
    });

    console.log(`Updated quality improvement ${qualityImprovementId} progress to ${progress}%`);
  } catch (error) {
    console.error('Error updating quality improvement progress:', error);
  }
}
