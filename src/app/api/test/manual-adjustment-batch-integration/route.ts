import { NextRequest, NextResponse } from "next/server";
import { runAllManualAdjustmentTests } from "@/lib/test-manual-adjustment-batch-integration";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/test/manual-adjustment-batch-integration - Run manual adjustment batch integration tests
export async function GET(request: NextRequest) {
  try {
    // Only allow in development environment
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: "Test endpoints are not available in production" },
        { status: 403 }
      );
    }

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to run tests
    if (auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can run integration tests" },
        { status: 403 }
      );
    }

    console.log(`🧪 Manual Adjustment Batch Integration Test initiated by ${auth.user.name} (${auth.user.email})`);

    // Run the tests
    const testResults = await runAllManualAdjustmentTests();

    return NextResponse.json({
      message: "Manual adjustment batch integration tests completed",
      timestamp: new Date().toISOString(),
      initiatedBy: {
        id: auth.user.id,
        name: auth.user.name,
        email: auth.user.email
      },
      results: testResults
    });

  } catch (error) {
    console.error("Error running manual adjustment batch integration tests:", error);
    return NextResponse.json(
      { 
        error: "Failed to run manual adjustment batch integration tests", 
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
