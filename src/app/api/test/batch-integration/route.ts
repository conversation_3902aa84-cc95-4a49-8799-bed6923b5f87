import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { testBatchIntegration, quickIntegrityCheck } from "@/lib/test-batch-integration";

// GET /api/test/batch-integration - Run batch integration tests
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to run tests
    if (auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can run integration tests" },
        { status: 403 }
      );
    }

    // Only allow in development environment
    if (process.env.NODE_ENV === "production") {
      return NextResponse.json(
        { error: "Integration tests are not available in production" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testType = searchParams.get("type") || "full";

    let result;

    if (testType === "quick") {
      console.log("Running quick integrity check...");
      result = await quickIntegrityCheck();
    } else {
      console.log("Running full batch integration test...");
      result = await testBatchIntegration();
    }

    return NextResponse.json({
      testType,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      result
    });

  } catch (error) {
    console.error("Error running batch integration test:", error);
    return NextResponse.json(
      { 
        error: "Failed to run batch integration test", 
        message: (error as Error).message,
        stack: process.env.NODE_ENV === "development" ? (error as Error).stack : undefined
      },
      { status: 500 }
    );
  }
}
