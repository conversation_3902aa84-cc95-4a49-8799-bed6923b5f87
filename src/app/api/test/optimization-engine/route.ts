import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { InventoryOptimizationEngine } from "@/lib/inventory-optimization";

// GET /api/test/optimization-engine - Test the optimization engine
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to run tests
    if (auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can run optimization tests" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const testType = searchParams.get("type") || "basic";

    console.log(`[Optimization Test] Running ${testType} test for user ${auth.user.id}`);

    let testResults: any = {};

    if (testType === "basic") {
      // Test basic optimization engine functionality
      console.log("[Optimization Test] Testing basic optimization engine...");
      
      const startTime = Date.now();
      const analysis = await InventoryOptimizationEngine.generateRecommendations(undefined, '30days');
      const endTime = Date.now();

      testResults = {
        testType: "basic",
        executionTime: `${endTime - startTime}ms`,
        summary: analysis.summary,
        recommendationTypes: analysis.recommendations.reduce((acc, rec) => {
          acc[rec.type] = (acc[rec.type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        priorityDistribution: analysis.recommendations.reduce((acc, rec) => {
          acc[rec.priority] = (acc[rec.priority] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        sampleRecommendations: analysis.recommendations.slice(0, 3).map(rec => ({
          id: rec.id,
          type: rec.type,
          priority: rec.priority,
          title: rec.title,
          estimatedImpact: rec.impact.financial
        })),
        insights: analysis.insights,
        status: "success"
      };

    } else if (testType === "performance") {
      // Test performance with different time ranges
      console.log("[Optimization Test] Testing performance across time ranges...");
      
      const timeRanges = ['30days', '90days', '6months'] as const;
      const performanceResults = [];

      for (const timeRange of timeRanges) {
        const startTime = Date.now();
        const analysis = await InventoryOptimizationEngine.generateRecommendations(undefined, timeRange);
        const endTime = Date.now();

        performanceResults.push({
          timeRange,
          executionTime: endTime - startTime,
          recommendationCount: analysis.recommendations.length,
          estimatedSavings: analysis.summary.estimatedSavings
        });
      }

      testResults = {
        testType: "performance",
        results: performanceResults,
        status: "success"
      };

    } else if (testType === "category") {
      // Test category-specific optimization
      console.log("[Optimization Test] Testing category-specific optimization...");
      
      // Get first category for testing
      const { prisma } = await import("@/auth");
      const categories = await prisma.category.findMany({
        take: 2,
        select: { id: true, name: true }
      });

      const categoryResults = [];

      for (const category of categories) {
        const startTime = Date.now();
        const analysis = await InventoryOptimizationEngine.generateRecommendations(category.id, '90days');
        const endTime = Date.now();

        categoryResults.push({
          categoryId: category.id,
          categoryName: category.name,
          executionTime: endTime - startTime,
          recommendationCount: analysis.recommendations.length,
          estimatedSavings: analysis.summary.estimatedSavings
        });
      }

      testResults = {
        testType: "category",
        results: categoryResults,
        status: "success"
      };

    } else {
      return NextResponse.json(
        { error: "Invalid test type. Valid types: basic, performance, category" },
        { status: 400 }
      );
    }

    console.log(`[Optimization Test] ${testType} test completed successfully`);

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      testResults
    });

  } catch (error) {
    console.error("Error running optimization engine test:", error);
    return NextResponse.json(
      { 
        error: "Failed to run optimization engine test", 
        message: (error as Error).message,
        stack: process.env.NODE_ENV === "development" ? (error as Error).stack : undefined
      },
      { status: 500 }
    );
  }
}

// POST /api/test/optimization-engine - Run comprehensive test suite
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to run tests
    if (auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized - Only SUPER_ADMIN can run optimization tests" },
        { status: 403 }
      );
    }

    console.log(`[Optimization Test Suite] Running comprehensive test suite for user ${auth.user.id}`);

    const testSuite = [];
    const overallStartTime = Date.now();

    // Test 1: Basic functionality
    try {
      const basicTest = await InventoryOptimizationEngine.generateRecommendations(undefined, '30days');
      testSuite.push({
        test: "basic_functionality",
        status: "passed",
        recommendationCount: basicTest.recommendations.length,
        estimatedSavings: basicTest.summary.estimatedSavings
      });
    } catch (error) {
      testSuite.push({
        test: "basic_functionality",
        status: "failed",
        error: (error as Error).message
      });
    }

    // Test 2: Empty category handling
    try {
      const emptyTest = await InventoryOptimizationEngine.generateRecommendations("non-existent-category", '30days');
      testSuite.push({
        test: "empty_category_handling",
        status: "passed",
        recommendationCount: emptyTest.recommendations.length
      });
    } catch (error) {
      testSuite.push({
        test: "empty_category_handling",
        status: "failed",
        error: (error as Error).message
      });
    }

    // Test 3: Different time ranges
    try {
      const timeRangeTests = await Promise.all([
        InventoryOptimizationEngine.generateRecommendations(undefined, '30days'),
        InventoryOptimizationEngine.generateRecommendations(undefined, '90days'),
        InventoryOptimizationEngine.generateRecommendations(undefined, '6months')
      ]);

      testSuite.push({
        test: "time_range_handling",
        status: "passed",
        results: timeRangeTests.map((test, index) => ({
          timeRange: ['30days', '90days', '6months'][index],
          recommendationCount: test.recommendations.length
        }))
      });
    } catch (error) {
      testSuite.push({
        test: "time_range_handling",
        status: "failed",
        error: (error as Error).message
      });
    }

    const overallEndTime = Date.now();
    const passedTests = testSuite.filter(test => test.status === "passed").length;
    const totalTests = testSuite.length;

    console.log(`[Optimization Test Suite] Completed: ${passedTests}/${totalTests} tests passed`);

    return NextResponse.json({
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      testSuite: {
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        executionTime: `${overallEndTime - overallStartTime}ms`,
        tests: testSuite
      }
    });

  } catch (error) {
    console.error("Error running optimization test suite:", error);
    return NextResponse.json(
      { 
        error: "Failed to run optimization test suite", 
        message: (error as Error).message,
        stack: process.env.NODE_ENV === "development" ? (error as Error).stack : undefined
      },
      { status: 500 }
    );
  }
}
