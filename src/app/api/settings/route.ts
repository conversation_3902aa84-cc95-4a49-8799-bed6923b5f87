import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/settings - Get all system settings
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get all settings
    const settings = await prisma.systemSetting.findMany();

    // Convert to a more usable format
    const formattedSettings = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    return NextResponse.json({ settings: formattedSettings });
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/settings - Update system settings
export async function PATCH(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow SUPER_ADMIN to update settings
    if (auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "Unauthorized. Only Super Admins can update system settings." },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    if (!body.settings || typeof body.settings !== "object") {
      return NextResponse.json(
        { error: "Invalid request. Settings object is required." },
        { status: 400 }
      );
    }

    // Update settings
    const updatePromises = Object.entries(body.settings).map(async ([key, value]) => {
      return prisma.systemSetting.upsert({
        where: { key },
        update: {
          value: String(value),
          updatedAt: new Date()
        },
        create: {
          key,
          value: String(value),
          description: body.descriptions?.[key] || null
        }
      });
    });

    await Promise.all(updatePromises);

    // Log the activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_SETTINGS",
        details: `Updated system settings: ${Object.keys(body.settings).join(", ")}`
      }
    });

    // Get updated settings
    const updatedSettings = await prisma.systemSetting.findMany();
    const formattedSettings = updatedSettings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, string>);

    return NextResponse.json({
      success: true,
      settings: formattedSettings
    });
  } catch (error) {
    console.error("Error updating settings:", error);
    return NextResponse.json(
      { error: "Failed to update settings", message: (error as Error).message },
      { status: 500 }
    );
  }
}
