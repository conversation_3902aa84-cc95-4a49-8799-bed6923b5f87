import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";
import { generateSupplierPerformanceReport } from "@/lib/scheduled-reports";
import { calculateSupplierQualityMetrics } from "@/lib/supplier-quality-metrics";
import { calculateSupplierPricingMetrics } from "@/lib/pricing-analytics";
import { PrismaClient } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 401,
      user: null
    };
  }

  try {
    // Verify the token
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Invalid token",
      status: 401,
      user: null
    };
  }
}

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if user has permission to view reports
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const supplierId = searchParams.get("supplierId");
    const evaluationType = searchParams.get("type") || "comprehensive";
    const timeRange = searchParams.get("timeRange") || "quarterly";

    // Calculate date range
    const endDate = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case "monthly":
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case "quarterly":
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case "yearly":
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 3);
    }

    if (supplierId) {
      // Single supplier evaluation
      const evaluation = await generateComprehensiveSupplierEvaluation(
        supplierId,
        startDate,
        endDate,
        evaluationType
      );
      
      return NextResponse.json({
        success: true,
        evaluation
      });
    } else {
      // All suppliers evaluation
      const evaluation = await generateAllSuppliersEvaluation(
        startDate,
        endDate,
        evaluationType
      );
      
      return NextResponse.json({
        success: true,
        evaluation
      });
    }

  } catch (error) {
    console.error("Error generating supplier evaluation:", error);
    return NextResponse.json(
      { 
        error: "Failed to generate supplier evaluation",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

/**
 * Generate comprehensive evaluation for a single supplier
 */
async function generateComprehensiveSupplierEvaluation(
  supplierId: string,
  startDate: Date,
  endDate: Date,
  evaluationType: string
) {
  // Get supplier information
  const supplier = await prisma.supplier.findUnique({
    where: { id: supplierId },
    include: {
      productSuppliers: {
        where: { isActive: true },
        include: {
          product: {
            select: { id: true, name: true, sku: true }
          }
        }
      },
      purchaseOrders: {
        where: {
          orderDate: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          items: true
        }
      }
    }
  });

  if (!supplier) {
    throw new Error("Supplier not found");
  }

  // Get comprehensive metrics
  const [qualityMetrics, pricingMetrics] = await Promise.all([
    calculateSupplierQualityMetrics(supplierId, startDate, endDate),
    calculateSupplierPricingMetrics(supplierId, startDate, endDate)
  ]);

  // Calculate delivery performance
  const deliveryMetrics = calculateDeliveryMetrics(supplier.purchaseOrders);
  
  // Calculate relationship health
  const relationshipHealth = calculateRelationshipHealth(supplier, qualityMetrics, pricingMetrics, deliveryMetrics);

  // Generate overall evaluation score
  const overallScore = calculateOverallEvaluationScore(
    qualityMetrics.metrics.qualityScore,
    pricingMetrics.metrics.competitivenessScore,
    deliveryMetrics.onTimeDeliveryRate,
    relationshipHealth.overallScore
  );

  // Generate recommendations
  const recommendations = generateEvaluationRecommendations(
    qualityMetrics,
    pricingMetrics,
    deliveryMetrics,
    relationshipHealth,
    overallScore
  );

  return {
    reportType: "supplier_evaluation",
    evaluationType,
    generatedAt: new Date(),
    dateRange: { startDate, endDate },
    supplier: {
      id: supplier.id,
      name: supplier.name,
      contactPerson: supplier.contactPerson,
      email: supplier.email,
      phone: supplier.phone
    },
    metrics: {
      quality: qualityMetrics.metrics,
      pricing: pricingMetrics.metrics,
      delivery: deliveryMetrics,
      relationship: relationshipHealth
    },
    overallScore,
    riskLevel: determineRiskLevel(overallScore, qualityMetrics.riskLevel),
    recommendations,
    actionItems: generateActionItems(recommendations, overallScore)
  };
}

/**
 * Generate evaluation for all suppliers
 */
async function generateAllSuppliersEvaluation(
  startDate: Date,
  endDate: Date,
  evaluationType: string
) {
  const suppliers = await prisma.supplier.findMany({
    where: { isActive: true },
    select: { id: true, name: true }
  });

  const evaluations = await Promise.all(
    suppliers.map(supplier => 
      generateComprehensiveSupplierEvaluation(supplier.id, startDate, endDate, evaluationType)
    )
  );

  // Calculate summary statistics
  const summary = {
    totalSuppliers: evaluations.length,
    averageScore: evaluations.reduce((sum, e) => sum + e.overallScore, 0) / evaluations.length,
    topPerformers: evaluations
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, 5),
    underPerformers: evaluations
      .sort((a, b) => a.overallScore - b.overallScore)
      .slice(0, 5),
    riskDistribution: {
      low: evaluations.filter(e => e.riskLevel === 'low').length,
      medium: evaluations.filter(e => e.riskLevel === 'medium').length,
      high: evaluations.filter(e => e.riskLevel === 'high').length
    }
  };

  return {
    reportType: "all_suppliers_evaluation",
    evaluationType,
    generatedAt: new Date(),
    dateRange: { startDate, endDate },
    summary,
    evaluations: evaluations.sort((a, b) => b.overallScore - a.overallScore)
  };
}

/**
 * Calculate delivery performance metrics
 */
function calculateDeliveryMetrics(purchaseOrders: any[]) {
  const receivedOrders = purchaseOrders.filter(po => po.receivedAt);
  
  if (receivedOrders.length === 0) {
    return {
      onTimeDeliveryRate: 100,
      averageDeliveryTime: 0,
      totalOrders: purchaseOrders.length,
      receivedOrders: 0
    };
  }

  const deliveryTimes = receivedOrders.map(po => {
    const orderDate = new Date(po.orderDate);
    const receivedDate = new Date(po.receivedAt);
    return Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
  });

  const averageDeliveryTime = deliveryTimes.reduce((sum, time) => sum + time, 0) / deliveryTimes.length;
  const onTimeDeliveries = deliveryTimes.filter(time => time <= 7).length; // Assuming 7 days is on-time
  const onTimeDeliveryRate = (onTimeDeliveries / receivedOrders.length) * 100;

  return {
    onTimeDeliveryRate,
    averageDeliveryTime,
    totalOrders: purchaseOrders.length,
    receivedOrders: receivedOrders.length
  };
}

/**
 * Calculate relationship health score
 */
function calculateRelationshipHealth(supplier: any, qualityMetrics: any, pricingMetrics: any, deliveryMetrics: any) {
  const communicationScore = 85; // Placeholder - would need actual communication tracking
  const reliabilityScore = (deliveryMetrics.onTimeDeliveryRate + qualityMetrics.metrics.qualityScore) / 2;
  const flexibilityScore = 80; // Placeholder - would need order modification tracking
  const overallScore = (communicationScore + reliabilityScore + flexibilityScore) / 3;

  return {
    communicationScore,
    reliabilityScore,
    flexibilityScore,
    overallScore,
    riskLevel: overallScore > 80 ? 'low' : overallScore > 60 ? 'medium' : 'high'
  };
}

/**
 * Calculate overall evaluation score
 */
function calculateOverallEvaluationScore(
  qualityScore: number,
  competitivenessScore: number,
  deliveryRate: number,
  relationshipScore: number
): number {
  return Math.round(
    (qualityScore * 0.3) +
    (competitivenessScore * 0.25) +
    (deliveryRate * 0.25) +
    (relationshipScore * 0.2)
  );
}

/**
 * Generate evaluation recommendations
 */
function generateEvaluationRecommendations(
  qualityMetrics: any,
  pricingMetrics: any,
  deliveryMetrics: any,
  relationshipHealth: any,
  overallScore: number
): string[] {
  const recommendations: string[] = [];

  if (qualityMetrics.metrics.qualityScore < 70) {
    recommendations.push("Implement quality improvement program with supplier");
  }
  
  if (pricingMetrics.metrics.competitivenessScore < 60) {
    recommendations.push("Negotiate better pricing terms or consider alternative suppliers");
  }
  
  if (deliveryMetrics.onTimeDeliveryRate < 80) {
    recommendations.push("Discuss delivery performance and establish improvement targets");
  }
  
  if (relationshipHealth.overallScore < 70) {
    recommendations.push("Strengthen supplier relationship through regular communication");
  }
  
  if (overallScore < 60) {
    recommendations.push("Consider supplier replacement or major contract renegotiation");
  }

  return recommendations;
}

/**
 * Generate action items based on recommendations
 */
function generateActionItems(recommendations: string[], overallScore: number): string[] {
  const actionItems: string[] = [];
  
  if (recommendations.length > 0) {
    actionItems.push("Schedule supplier review meeting within 30 days");
  }
  
  if (overallScore < 70) {
    actionItems.push("Develop supplier improvement plan with specific KPIs");
  }
  
  if (overallScore < 50) {
    actionItems.push("Initiate supplier replacement evaluation process");
  }

  return actionItems;
}

/**
 * Determine overall risk level
 */
function determineRiskLevel(overallScore: number, qualityRiskLevel: string): 'low' | 'medium' | 'high' {
  if (overallScore < 50 || qualityRiskLevel === 'high') return 'high';
  if (overallScore < 70 || qualityRiskLevel === 'medium') return 'medium';
  return 'low';
}
