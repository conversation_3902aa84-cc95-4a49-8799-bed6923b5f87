import { NextRequest, NextResponse } from "next/server";
import { jwtVerify } from "jose";
import {
  createScheduledReport,
  getScheduledReports,
  processDueReports
} from "@/lib/scheduled-reports";
import { z } from "zod";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 401,
      user: null
    };
  }

  try {
    // Verify the token
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Invalid token",
      status: 401,
      user: null
    };
  }
}

const createScheduledReportSchema = z.object({
  type: z.enum(['weekly', 'monthly', 'quarterly']),
  reportCategory: z.enum(['performance', 'cost', 'quality', 'relationship']),
  recipients: z.array(z.string().email()),
  parameters: z.record(z.any()).optional().default({})
});

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if user has permission to view reports
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "process") {
      // Process due reports
      const results = await processDueReports();
      return NextResponse.json({
        success: true,
        processed: results.length,
        results
      });
    } else {
      // Get all scheduled reports
      const reports = await getScheduledReports();
      return NextResponse.json({
        success: true,
        reports
      });
    }

  } catch (error) {
    console.error("Error handling scheduled reports:", error);
    return NextResponse.json(
      { 
        error: "Failed to handle scheduled reports",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Check if user has permission to create reports
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role)) {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createScheduledReportSchema.parse(body);

    const report = await createScheduledReport(
      validatedData.type,
      validatedData.reportCategory,
      validatedData.recipients,
      validatedData.parameters
    );

    return NextResponse.json({
      success: true,
      report
    }, { status: 201 });

  } catch (error) {
    console.error("Error creating scheduled report:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        error: "Failed to create scheduled report",
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
