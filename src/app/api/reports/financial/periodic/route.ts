import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import { 
  format, 
  startOfWeek, 
  endOfWeek, 
  startOfMonth, 
  endOfMonth, 
  subWeeks, 
  subMonths,
  parseISO,
  eachDayOfInterval,
  eachWeekOfInterval,
  eachMonthOfInterval
} from "date-fns";

export interface PeriodicSalesReport {
  reportType: "weekly" | "monthly";
  period: {
    start: string;
    end: string;
    label: string;
  };
  summary: {
    totalRevenue: number;
    totalProfit: number;
    totalTransactions: number;
    totalItems: number;
    averageOrderValue: number;
    profitMargin: number;
    averageDailyRevenue: number;
    averageDailyTransactions: number;
  };
  periodComparison: {
    previousPeriod: {
      start: string;
      end: string;
      label: string;
    };
    revenueChange: number;
    profitChange: number;
    transactionChange: number;
    averageOrderValueChange: number;
  };
  dailyBreakdown: Array<{
    date: string;
    revenue: number;
    profit: number;
    transactions: number;
    averageOrderValue: number;
  }>;
  categoryPerformance: Array<{
    categoryId: string;
    categoryName: string;
    revenue: number;
    profit: number;
    transactions: number;
    profitMargin: number;
  }>;
  paymentMethodTrends: Array<{
    method: string;
    totalAmount: number;
    transactionCount: number;
    percentage: number;
    averageAmount: number;
  }>;
  topPerformingProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    quantitySold: number;
    revenue: number;
    profit: number;
    profitMargin: number;
  }>;
  cashierSummary: Array<{
    cashierId: string;
    cashierName: string;
    totalTransactions: number;
    totalRevenue: number;
    averageOrderValue: number;
    workingDays: number;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has financial reporting access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const reportType = searchParams.get("type") as "weekly" | "monthly" || "weekly";
    const dateParam = searchParams.get("date");
    
    // Default to current period if no date provided
    const referenceDate = dateParam ? parseISO(dateParam) : new Date();
    
    let startDate: Date, endDate: Date, previousStartDate: Date, previousEndDate: Date;
    let periodLabel: string, previousPeriodLabel: string;

    if (reportType === "weekly") {
      startDate = startOfWeek(referenceDate, { weekStartsOn: 1 }); // Monday start
      endDate = endOfWeek(referenceDate, { weekStartsOn: 1 });
      previousStartDate = startOfWeek(subWeeks(referenceDate, 1), { weekStartsOn: 1 });
      previousEndDate = endOfWeek(subWeeks(referenceDate, 1), { weekStartsOn: 1 });
      periodLabel = `Week of ${format(startDate, "MMM dd, yyyy")}`;
      previousPeriodLabel = `Week of ${format(previousStartDate, "MMM dd, yyyy")}`;
    } else {
      startDate = startOfMonth(referenceDate);
      endDate = endOfMonth(referenceDate);
      previousStartDate = startOfMonth(subMonths(referenceDate, 1));
      previousEndDate = endOfMonth(subMonths(referenceDate, 1));
      periodLabel = format(startDate, "MMMM yyyy");
      previousPeriodLabel = format(previousStartDate, "MMMM yyyy");
    }

    // Fetch current period transactions
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "COMPLETED",
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
                productSuppliers: {
                  where: { isPreferred: true },
                  include: { supplier: true },
                },
              },
            },
          },
        },
        cashier: true,
      },
    });

    // Fetch previous period transactions for comparison
    const previousTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: previousStartDate,
          lte: previousEndDate,
        },
        status: "COMPLETED",
      },
      select: {
        total: true,
        subtotal: true,
        items: {
          select: {
            quantity: true,
            unitPrice: true,
            product: {
              select: {
                productSuppliers: {
                  where: { isPreferred: true },
                  select: { purchasePrice: true },
                },
              },
            },
          },
        },
      },
    });

    // Calculate current period metrics
    const totalRevenue = transactions.reduce((sum, t) => sum + Number(t.total), 0);
    const totalTransactions = transactions.length;
    const totalItems = transactions.reduce((sum, t) => 
      sum + t.items.reduce((itemSum, item) => itemSum + Number(item.quantity), 0), 0
    );
    
    let totalProfit = 0;
    transactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const revenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const cost = Number(purchasePrice) * Number(item.quantity);
        totalProfit += revenue - cost;
      });
    });

    const averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
    
    // Calculate period length for averages
    const periodDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
    const averageDailyRevenue = totalRevenue / periodDays;
    const averageDailyTransactions = totalTransactions / periodDays;

    // Calculate previous period metrics for comparison
    const previousRevenue = previousTransactions.reduce((sum, t) => sum + Number(t.total), 0);
    const previousTransactionCount = previousTransactions.length;
    let previousProfit = 0;
    previousTransactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const revenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const cost = Number(purchasePrice) * Number(item.quantity);
        previousProfit += revenue - cost;
      });
    });

    const previousAverageOrderValue = previousTransactionCount > 0 ? previousRevenue / previousTransactionCount : 0;

    const revenueChange = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const profitChange = previousProfit > 0 ? ((totalProfit - previousProfit) / previousProfit) * 100 : 0;
    const transactionChange = previousTransactionCount > 0 ? ((totalTransactions - previousTransactionCount) / previousTransactionCount) * 100 : 0;
    const averageOrderValueChange = previousAverageOrderValue > 0 ? ((averageOrderValue - previousAverageOrderValue) / previousAverageOrderValue) * 100 : 0;

    // Daily breakdown
    const dailyData = new Map<string, { revenue: number; profit: number; transactions: number }>();
    transactions.forEach(transaction => {
      const dateKey = format(transaction.createdAt, "yyyy-MM-dd");
      const existing = dailyData.get(dateKey) || { revenue: 0, profit: 0, transactions: 0 };
      
      let transactionProfit = 0;
      transaction.items.forEach(item => {
        const revenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const cost = Number(purchasePrice) * Number(item.quantity);
        transactionProfit += revenue - cost;
      });

      dailyData.set(dateKey, {
        revenue: existing.revenue + Number(transaction.total),
        profit: existing.profit + transactionProfit,
        transactions: existing.transactions + 1,
      });
    });

    const dailyBreakdown = eachDayOfInterval({ start: startDate, end: endDate }).map(date => {
      const dateKey = format(date, "yyyy-MM-dd");
      const data = dailyData.get(dateKey) || { revenue: 0, profit: 0, transactions: 0 };
      return {
        date: dateKey,
        revenue: Math.round(data.revenue * 100) / 100,
        profit: Math.round(data.profit * 100) / 100,
        transactions: data.transactions,
        averageOrderValue: data.transactions > 0 ? Math.round((data.revenue / data.transactions) * 100) / 100 : 0,
      };
    });

    // Category performance
    const categoryData = new Map<string, {
      categoryId: string;
      categoryName: string;
      revenue: number;
      profit: number;
      transactions: Set<string>;
    }>();

    transactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const categoryId = item.product.categoryId || "uncategorized";
        const categoryName = item.product.category?.name || "Uncategorized";
        const existing = categoryData.get(categoryId) || {
          categoryId,
          categoryName,
          revenue: 0,
          profit: 0,
          transactions: new Set<string>(),
        };

        const itemRevenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const itemCost = Number(purchasePrice) * Number(item.quantity);
        const itemProfit = itemRevenue - itemCost;

        existing.transactions.add(transaction.id);
        categoryData.set(categoryId, {
          ...existing,
          revenue: existing.revenue + itemRevenue,
          profit: existing.profit + itemProfit,
        });
      });
    });

    const categoryPerformance = Array.from(categoryData.values()).map(category => ({
      categoryId: category.categoryId,
      categoryName: category.categoryName,
      revenue: Math.round(category.revenue * 100) / 100,
      profit: Math.round(category.profit * 100) / 100,
      transactions: category.transactions.size,
      profitMargin: category.revenue > 0 ? Math.round((category.profit / category.revenue) * 10000) / 100 : 0,
    })).sort((a, b) => b.revenue - a.revenue);

    // Payment method trends
    const paymentMethodData = new Map<string, { totalAmount: number; transactionCount: number }>();
    transactions.forEach(transaction => {
      const method = transaction.paymentMethod;
      const existing = paymentMethodData.get(method) || { totalAmount: 0, transactionCount: 0 };
      paymentMethodData.set(method, {
        totalAmount: existing.totalAmount + Number(transaction.total),
        transactionCount: existing.transactionCount + 1,
      });
    });

    const paymentMethodTrends = Array.from(paymentMethodData.entries()).map(([method, data]) => ({
      method,
      totalAmount: Math.round(data.totalAmount * 100) / 100,
      transactionCount: data.transactionCount,
      percentage: totalRevenue > 0 ? Math.round((data.totalAmount / totalRevenue) * 10000) / 100 : 0,
      averageAmount: data.transactionCount > 0 ? Math.round((data.totalAmount / data.transactionCount) * 100) / 100 : 0,
    }));

    // Top performing products
    const productData = new Map<string, {
      productId: string;
      productName: string;
      sku: string;
      quantitySold: number;
      revenue: number;
      profit: number;
    }>();

    transactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const productId = item.productId;
        const existing = productData.get(productId) || {
          productId,
          productName: item.product.name,
          sku: item.product.sku,
          quantitySold: 0,
          revenue: 0,
          profit: 0,
        };

        const itemRevenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const itemCost = Number(purchasePrice) * Number(item.quantity);
        const itemProfit = itemRevenue - itemCost;

        productData.set(productId, {
          ...existing,
          quantitySold: existing.quantitySold + Number(item.quantity),
          revenue: existing.revenue + itemRevenue,
          profit: existing.profit + itemProfit,
        });
      });
    });

    const topPerformingProducts = Array.from(productData.values())
      .map(product => ({
        ...product,
        revenue: Math.round(product.revenue * 100) / 100,
        profit: Math.round(product.profit * 100) / 100,
        profitMargin: product.revenue > 0 ? Math.round((product.profit / product.revenue) * 10000) / 100 : 0,
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 20);

    // Cashier summary
    const cashierData = new Map<string, {
      cashierId: string;
      cashierName: string;
      totalTransactions: number;
      totalRevenue: number;
      workingDays: Set<string>;
    }>();

    transactions.forEach(transaction => {
      const cashierId = transaction.cashierId;
      const existing = cashierData.get(cashierId) || {
        cashierId,
        cashierName: transaction.cashier.name,
        totalTransactions: 0,
        totalRevenue: 0,
        workingDays: new Set<string>(),
      };

      const workingDay = format(transaction.createdAt, "yyyy-MM-dd");
      existing.workingDays.add(workingDay);

      cashierData.set(cashierId, {
        ...existing,
        totalTransactions: existing.totalTransactions + 1,
        totalRevenue: existing.totalRevenue + Number(transaction.total),
      });
    });

    const cashierSummary = Array.from(cashierData.values()).map(cashier => ({
      cashierId: cashier.cashierId,
      cashierName: cashier.cashierName,
      totalTransactions: cashier.totalTransactions,
      totalRevenue: Math.round(cashier.totalRevenue * 100) / 100,
      averageOrderValue: cashier.totalTransactions > 0 ? Math.round((cashier.totalRevenue / cashier.totalTransactions) * 100) / 100 : 0,
      workingDays: cashier.workingDays.size,
    }));

    const report: PeriodicSalesReport = {
      reportType,
      period: {
        start: format(startDate, "yyyy-MM-dd"),
        end: format(endDate, "yyyy-MM-dd"),
        label: periodLabel,
      },
      summary: {
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalProfit: Math.round(totalProfit * 100) / 100,
        totalTransactions,
        totalItems,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        profitMargin: Math.round(profitMargin * 100) / 100,
        averageDailyRevenue: Math.round(averageDailyRevenue * 100) / 100,
        averageDailyTransactions: Math.round(averageDailyTransactions * 100) / 100,
      },
      periodComparison: {
        previousPeriod: {
          start: format(previousStartDate, "yyyy-MM-dd"),
          end: format(previousEndDate, "yyyy-MM-dd"),
          label: previousPeriodLabel,
        },
        revenueChange: Math.round(revenueChange * 100) / 100,
        profitChange: Math.round(profitChange * 100) / 100,
        transactionChange: Math.round(transactionChange * 100) / 100,
        averageOrderValueChange: Math.round(averageOrderValueChange * 100) / 100,
      },
      dailyBreakdown,
      categoryPerformance,
      paymentMethodTrends,
      topPerformingProducts,
      cashierSummary,
    };

    return NextResponse.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error generating periodic sales report:", error);
    return NextResponse.json(
      {
        error: "Failed to generate periodic sales report",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
