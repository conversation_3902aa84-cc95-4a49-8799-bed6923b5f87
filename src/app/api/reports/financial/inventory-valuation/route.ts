import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import { format, parseISO, subDays, subMonths } from "date-fns";

export interface InventoryValuationReport {
  reportDate: string;
  valuationMethod: "FIFO" | "LIFO" | "WEIGHTED_AVERAGE";
  summary: {
    totalInventoryValue: number;
    totalStoreValue: number;
    totalWarehouseValue: number;
    totalProducts: number;
    totalBatches: number;
    averageValuePerProduct: number;
    averageValuePerBatch: number;
  };
  locationBreakdown: {
    store: {
      totalValue: number;
      productCount: number;
      batchCount: number;
      averageValuePerProduct: number;
    };
    warehouse: {
      totalValue: number;
      productCount: number;
      batchCount: number;
      averageValuePerProduct: number;
    };
  };
  categoryValuation: Array<{
    categoryId: string;
    categoryName: string;
    totalValue: number;
    productCount: number;
    batchCount: number;
    percentage: number;
    averageValuePerProduct: number;
  }>;
  supplierValuation: Array<{
    supplierId: string;
    supplierName: string;
    totalValue: number;
    productCount: number;
    batchCount: number;
    percentage: number;
    averageValuePerProduct: number;
  }>;
  agingAnalysis: {
    fresh: { value: number; percentage: number; threshold: string }; // 0-30 days
    moderate: { value: number; percentage: number; threshold: string }; // 31-90 days
    old: { value: number; percentage: number; threshold: string }; // 91-180 days
    veryOld: { value: number; percentage: number; threshold: string }; // 181-365 days
    expired: { value: number; percentage: number; threshold: string }; // >365 days or past expiry
  };
  topValueProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    category: string;
    totalValue: number;
    totalQuantity: number;
    averageUnitValue: number;
    batchCount: number;
  }>;
  lowValueProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    category: string;
    totalValue: number;
    totalQuantity: number;
    averageUnitValue: number;
    batchCount: number;
  }>;
  expiryAnalysis: Array<{
    productId: string;
    productName: string;
    sku: string;
    batchId: string;
    batchNumber: string;
    expiryDate: string | null;
    daysToExpiry: number | null;
    quantity: number;
    value: number;
    location: "STORE" | "WAREHOUSE";
    riskLevel: "LOW" | "MEDIUM" | "HIGH" | "EXPIRED";
  }>;
  movementAnalysis: Array<{
    productId: string;
    productName: string;
    sku: string;
    totalValue: number;
    lastSaleDate: string | null;
    daysSinceLastSale: number | null;
    turnoverRate: number;
    movementCategory: "FAST" | "MEDIUM" | "SLOW" | "DEAD";
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has financial reporting access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN", "WAREHOUSE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const valuationMethod = searchParams.get("method") as "FIFO" | "LIFO" | "WEIGHTED_AVERAGE" || "FIFO";
    const reportDate = new Date();

    // Fetch all active stock batches with product and supplier information
    const stockBatches = await prisma.stockBatch.findMany({
      where: {
        status: "ACTIVE",
        remainingQuantity: {
          gt: 0,
        },
      },
      include: {
        product: {
          include: {
            category: true,
            productSuppliers: {
              where: { isPreferred: true },
              include: { supplier: true },
            },
          },
        },
        productSupplier: {
          include: { supplier: true },
        },
        storeStock: true,
        warehouseStock: true,
      },
    });

    // Calculate valuation based on method
    let totalInventoryValue = 0;
    let totalStoreValue = 0;
    let totalWarehouseValue = 0;

    // Data structures for analysis
    const productData = new Map<string, {
      productId: string;
      productName: string;
      sku: string;
      category: string;
      totalValue: number;
      totalQuantity: number;
      batchCount: number;
      batches: Array<{
        batchId: string;
        quantity: number;
        value: number;
        location: "STORE" | "WAREHOUSE";
        receivedDate: Date;
        expiryDate: Date | null;
      }>;
    }>();

    const categoryData = new Map<string, {
      categoryId: string;
      categoryName: string;
      totalValue: number;
      productIds: Set<string>;
      batchCount: number;
    }>();

    const supplierData = new Map<string, {
      supplierId: string;
      supplierName: string;
      totalValue: number;
      productIds: Set<string>;
      batchCount: number;
    }>();

    // Process each batch
    stockBatches.forEach(batch => {
      const quantity = Number(batch.remainingQuantity);
      let unitValue = 0;

      // Determine unit value based on valuation method
      switch (valuationMethod) {
        case "FIFO":
        case "LIFO":
          // Use the actual purchase price from the batch
          unitValue = Number(batch.purchasePrice || 0);
          break;
        case "WEIGHTED_AVERAGE":
          // Use weighted average of all batches for this product
          // For simplicity, we'll use the current batch price
          // In a full implementation, this would calculate across all batches
          unitValue = Number(batch.purchasePrice || 0);
          break;
      }

      const batchValue = quantity * unitValue;
      totalInventoryValue += batchValue;

      // Determine location and update location totals
      const location = batch.storeStock ? "STORE" : "WAREHOUSE";
      if (location === "STORE") {
        totalStoreValue += batchValue;
      } else {
        totalWarehouseValue += batchValue;
      }

      // Update product data
      const productId = batch.productId;
      const productEntry = productData.get(productId) || {
        productId,
        productName: batch.product.name,
        sku: batch.product.sku,
        category: batch.product.category?.name || "Uncategorized",
        totalValue: 0,
        totalQuantity: 0,
        batchCount: 0,
        batches: [],
      };

      productEntry.totalValue += batchValue;
      productEntry.totalQuantity += quantity;
      productEntry.batchCount += 1;
      productEntry.batches.push({
        batchId: batch.id,
        quantity,
        value: batchValue,
        location,
        receivedDate: batch.receivedDate,
        expiryDate: batch.expiryDate,
      });
      productData.set(productId, productEntry);

      // Update category data
      const categoryId = batch.product.categoryId || "uncategorized";
      const categoryName = batch.product.category?.name || "Uncategorized";
      const categoryEntry = categoryData.get(categoryId) || {
        categoryId,
        categoryName,
        totalValue: 0,
        productIds: new Set<string>(),
        batchCount: 0,
      };

      categoryEntry.totalValue += batchValue;
      categoryEntry.productIds.add(productId);
      categoryEntry.batchCount += 1;
      categoryData.set(categoryId, categoryEntry);

      // Update supplier data
      const supplier = batch.productSupplier?.supplier || batch.product.productSuppliers[0]?.supplier;
      if (supplier) {
        const supplierId = supplier.id;
        const supplierEntry = supplierData.get(supplierId) || {
          supplierId,
          supplierName: supplier.name,
          totalValue: 0,
          productIds: new Set<string>(),
          batchCount: 0,
        };

        supplierEntry.totalValue += batchValue;
        supplierEntry.productIds.add(productId);
        supplierEntry.batchCount += 1;
        supplierData.set(supplierId, supplierEntry);
      }
    });

    // Calculate summary metrics
    const totalProducts = productData.size;
    const totalBatches = stockBatches.length;
    const averageValuePerProduct = totalProducts > 0 ? totalInventoryValue / totalProducts : 0;
    const averageValuePerBatch = totalBatches > 0 ? totalInventoryValue / totalBatches : 0;

    // Location breakdown
    const storeProductCount = Array.from(productData.values()).filter(p => 
      p.batches.some(b => b.location === "STORE")
    ).length;
    const warehouseProductCount = Array.from(productData.values()).filter(p => 
      p.batches.some(b => b.location === "WAREHOUSE")
    ).length;
    const storeBatchCount = stockBatches.filter(b => b.storeStock).length;
    const warehouseBatchCount = stockBatches.filter(b => b.warehouseStock).length;

    const locationBreakdown = {
      store: {
        totalValue: Math.round(totalStoreValue * 100) / 100,
        productCount: storeProductCount,
        batchCount: storeBatchCount,
        averageValuePerProduct: storeProductCount > 0 ? Math.round((totalStoreValue / storeProductCount) * 100) / 100 : 0,
      },
      warehouse: {
        totalValue: Math.round(totalWarehouseValue * 100) / 100,
        productCount: warehouseProductCount,
        batchCount: warehouseBatchCount,
        averageValuePerProduct: warehouseProductCount > 0 ? Math.round((totalWarehouseValue / warehouseProductCount) * 100) / 100 : 0,
      },
    };

    // Category valuation
    const categoryValuation = Array.from(categoryData.values()).map(category => ({
      categoryId: category.categoryId,
      categoryName: category.categoryName,
      totalValue: Math.round(category.totalValue * 100) / 100,
      productCount: category.productIds.size,
      batchCount: category.batchCount,
      percentage: totalInventoryValue > 0 ? Math.round((category.totalValue / totalInventoryValue) * 10000) / 100 : 0,
      averageValuePerProduct: category.productIds.size > 0 ? Math.round((category.totalValue / category.productIds.size) * 100) / 100 : 0,
    })).sort((a, b) => b.totalValue - a.totalValue);

    // Supplier valuation
    const supplierValuation = Array.from(supplierData.values()).map(supplier => ({
      supplierId: supplier.supplierId,
      supplierName: supplier.supplierName,
      totalValue: Math.round(supplier.totalValue * 100) / 100,
      productCount: supplier.productIds.size,
      batchCount: supplier.batchCount,
      percentage: totalInventoryValue > 0 ? Math.round((supplier.totalValue / totalInventoryValue) * 10000) / 100 : 0,
      averageValuePerProduct: supplier.productIds.size > 0 ? Math.round((supplier.totalValue / supplier.productIds.size) * 100) / 100 : 0,
    })).sort((a, b) => b.totalValue - a.totalValue);

    // Aging analysis
    const now = new Date();
    const thirtyDaysAgo = subDays(now, 30);
    const ninetyDaysAgo = subDays(now, 90);
    const oneEightyDaysAgo = subDays(now, 180);
    const oneYearAgo = subDays(now, 365);

    let freshValue = 0;
    let moderateValue = 0;
    let oldValue = 0;
    let veryOldValue = 0;
    let expiredValue = 0;

    stockBatches.forEach(batch => {
      const batchValue = Number(batch.remainingQuantity) * Number(batch.purchasePrice || 0);
      const receivedDate = batch.receivedDate;
      const expiryDate = batch.expiryDate;

      // Check if expired
      if (expiryDate && expiryDate < now) {
        expiredValue += batchValue;
      } else if (receivedDate >= thirtyDaysAgo) {
        freshValue += batchValue;
      } else if (receivedDate >= ninetyDaysAgo) {
        moderateValue += batchValue;
      } else if (receivedDate >= oneEightyDaysAgo) {
        oldValue += batchValue;
      } else if (receivedDate >= oneYearAgo) {
        veryOldValue += batchValue;
      } else {
        expiredValue += batchValue;
      }
    });

    const agingAnalysis = {
      fresh: {
        value: Math.round(freshValue * 100) / 100,
        percentage: totalInventoryValue > 0 ? Math.round((freshValue / totalInventoryValue) * 10000) / 100 : 0,
        threshold: "0-30 days"
      },
      moderate: {
        value: Math.round(moderateValue * 100) / 100,
        percentage: totalInventoryValue > 0 ? Math.round((moderateValue / totalInventoryValue) * 10000) / 100 : 0,
        threshold: "31-90 days"
      },
      old: {
        value: Math.round(oldValue * 100) / 100,
        percentage: totalInventoryValue > 0 ? Math.round((oldValue / totalInventoryValue) * 10000) / 100 : 0,
        threshold: "91-180 days"
      },
      veryOld: {
        value: Math.round(veryOldValue * 100) / 100,
        percentage: totalInventoryValue > 0 ? Math.round((veryOldValue / totalInventoryValue) * 10000) / 100 : 0,
        threshold: "181-365 days"
      },
      expired: {
        value: Math.round(expiredValue * 100) / 100,
        percentage: totalInventoryValue > 0 ? Math.round((expiredValue / totalInventoryValue) * 10000) / 100 : 0,
        threshold: ">365 days or expired"
      },
    };

    // Top and low value products
    const productAnalysis = Array.from(productData.values()).map(product => ({
      ...product,
      totalValue: Math.round(product.totalValue * 100) / 100,
      averageUnitValue: product.totalQuantity > 0 ? Math.round((product.totalValue / product.totalQuantity) * 100) / 100 : 0,
    })).sort((a, b) => b.totalValue - a.totalValue);

    const topValueProducts = productAnalysis.slice(0, 10);
    const lowValueProducts = productAnalysis.slice(-10).reverse();

    // Expiry analysis
    const expiryAnalysis = stockBatches
      .filter(batch => batch.expiryDate)
      .map(batch => {
        const daysToExpiry = batch.expiryDate ? Math.ceil((batch.expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)) : null;
        let riskLevel: "LOW" | "MEDIUM" | "HIGH" | "EXPIRED" = "LOW";

        if (daysToExpiry !== null) {
          if (daysToExpiry < 0) {
            riskLevel = "EXPIRED";
          } else if (daysToExpiry <= 7) {
            riskLevel = "HIGH";
          } else if (daysToExpiry <= 30) {
            riskLevel = "MEDIUM";
          }
        }

        return {
          productId: batch.productId,
          productName: batch.product.name,
          sku: batch.product.sku,
          batchId: batch.id,
          batchNumber: batch.batchNumber,
          expiryDate: batch.expiryDate ? format(batch.expiryDate, "yyyy-MM-dd") : null,
          daysToExpiry,
          quantity: Number(batch.remainingQuantity),
          value: Math.round(Number(batch.remainingQuantity) * Number(batch.purchasePrice || 0) * 100) / 100,
          location: batch.storeStock ? "STORE" as const : "WAREHOUSE" as const,
          riskLevel,
        };
      })
      .sort((a, b) => (a.daysToExpiry || 0) - (b.daysToExpiry || 0));

    const report: InventoryValuationReport = {
      reportDate: format(reportDate, "yyyy-MM-dd"),
      valuationMethod,
      summary: {
        totalInventoryValue: Math.round(totalInventoryValue * 100) / 100,
        totalStoreValue: Math.round(totalStoreValue * 100) / 100,
        totalWarehouseValue: Math.round(totalWarehouseValue * 100) / 100,
        totalProducts,
        totalBatches,
        averageValuePerProduct: Math.round(averageValuePerProduct * 100) / 100,
        averageValuePerBatch: Math.round(averageValuePerBatch * 100) / 100,
      },
      locationBreakdown,
      categoryValuation,
      supplierValuation,
      agingAnalysis,
      topValueProducts,
      lowValueProducts,
      expiryAnalysis,
      movementAnalysis: [], // Will be implemented separately due to complexity
    };

    return NextResponse.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error generating inventory valuation report:", error);
    return NextResponse.json(
      {
        error: "Failed to generate inventory valuation report",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
