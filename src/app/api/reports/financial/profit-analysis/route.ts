import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import { format, parseISO, startOfDay, endOfDay } from "date-fns";

export interface ProfitMarginAnalysis {
  period: {
    start: string;
    end: string;
  };
  overallMetrics: {
    totalRevenue: number;
    totalCost: number;
    totalProfit: number;
    overallProfitMargin: number;
    averageProfitPerTransaction: number;
    averageProfitPerItem: number;
  };
  productAnalysis: Array<{
    productId: string;
    productName: string;
    sku: string;
    category: string;
    quantitySold: number;
    revenue: number;
    cost: number;
    profit: number;
    profitMargin: number;
    averageSellingPrice: number;
    averageCost: number;
    profitPerUnit: number;
  }>;
  categoryAnalysis: Array<{
    categoryId: string;
    categoryName: string;
    productCount: number;
    totalRevenue: number;
    totalCost: number;
    totalProfit: number;
    profitMargin: number;
    averageProfitPerProduct: number;
  }>;
  supplierAnalysis: Array<{
    supplierId: string;
    supplierName: string;
    productCount: number;
    totalRevenue: number;
    totalCost: number;
    totalProfit: number;
    profitMargin: number;
    averageProfitPerProduct: number;
  }>;
  timeSeriesAnalysis: Array<{
    date: string;
    revenue: number;
    cost: number;
    profit: number;
    profitMargin: number;
    transactionCount: number;
  }>;
  profitMarginDistribution: {
    highMargin: { count: number; percentage: number; threshold: number }; // >30%
    mediumMargin: { count: number; percentage: number; threshold: string }; // 15-30%
    lowMargin: { count: number; percentage: number; threshold: string }; // 5-15%
    veryLowMargin: { count: number; percentage: number; threshold: string }; // <5%
    negativemargin: { count: number; percentage: number }; // <0%
  };
  topProfitableProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    totalProfit: number;
    profitMargin: number;
    quantitySold: number;
  }>;
  leastProfitableProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    totalProfit: number;
    profitMargin: number;
    quantitySold: number;
  }>;
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has financial reporting access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const startDateParam = searchParams.get("startDate");
    const endDateParam = searchParams.get("endDate");
    
    // Default to last 30 days if no dates provided
    const endDate = endDateParam ? endOfDay(parseISO(endDateParam)) : endOfDay(new Date());
    const startDate = startDateParam ? startOfDay(parseISO(startDateParam)) : startOfDay(new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000));

    // Fetch transactions with detailed product and supplier information
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "COMPLETED",
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
                productSuppliers: {
                  where: { isPreferred: true },
                  include: { supplier: true },
                },
              },
            },
            stockBatch: {
              include: {
                productSupplier: {
                  include: { supplier: true },
                },
              },
            },
          },
        },
      },
    });

    // Calculate overall metrics
    let totalRevenue = 0;
    let totalCost = 0;
    let totalProfit = 0;
    let totalItems = 0;

    // Data structures for analysis
    const productData = new Map<string, {
      productId: string;
      productName: string;
      sku: string;
      category: string;
      quantitySold: number;
      revenue: number;
      cost: number;
      profit: number;
    }>();

    const categoryData = new Map<string, {
      categoryId: string;
      categoryName: string;
      productIds: Set<string>;
      revenue: number;
      cost: number;
      profit: number;
    }>();

    const supplierData = new Map<string, {
      supplierId: string;
      supplierName: string;
      productIds: Set<string>;
      revenue: number;
      cost: number;
      profit: number;
    }>();

    const dailyData = new Map<string, {
      revenue: number;
      cost: number;
      profit: number;
      transactionCount: number;
    }>();

    // Process transactions
    transactions.forEach(transaction => {
      const transactionDate = format(transaction.createdAt, "yyyy-MM-dd");
      const dailyEntry = dailyData.get(transactionDate) || {
        revenue: 0,
        cost: 0,
        profit: 0,
        transactionCount: 0,
      };
      dailyEntry.transactionCount += 1;

      transaction.items.forEach(item => {
        const quantity = Number(item.quantity);
        const unitPrice = Number(item.unitPrice);
        const itemRevenue = unitPrice * quantity;

        // Get cost from batch or product supplier
        let unitCost = 0;
        if (item.stockBatch?.productSupplier?.purchasePrice) {
          unitCost = Number(item.stockBatch.productSupplier.purchasePrice);
        } else if (item.product.productSuppliers[0]?.purchasePrice) {
          unitCost = Number(item.product.productSuppliers[0].purchasePrice);
        }

        const itemCost = unitCost * quantity;
        const itemProfit = itemRevenue - itemCost;

        // Update totals
        totalRevenue += itemRevenue;
        totalCost += itemCost;
        totalProfit += itemProfit;
        totalItems += quantity;

        // Update daily data
        dailyEntry.revenue += itemRevenue;
        dailyEntry.cost += itemCost;
        dailyEntry.profit += itemProfit;
        dailyData.set(transactionDate, dailyEntry);

        // Update product data
        const productId = item.productId;
        const productEntry = productData.get(productId) || {
          productId,
          productName: item.product.name,
          sku: item.product.sku,
          category: item.product.category?.name || "Uncategorized",
          quantitySold: 0,
          revenue: 0,
          cost: 0,
          profit: 0,
        };

        productEntry.quantitySold += quantity;
        productEntry.revenue += itemRevenue;
        productEntry.cost += itemCost;
        productEntry.profit += itemProfit;
        productData.set(productId, productEntry);

        // Update category data
        const categoryId = item.product.categoryId || "uncategorized";
        const categoryName = item.product.category?.name || "Uncategorized";
        const categoryEntry = categoryData.get(categoryId) || {
          categoryId,
          categoryName,
          productIds: new Set<string>(),
          revenue: 0,
          cost: 0,
          profit: 0,
        };

        categoryEntry.productIds.add(productId);
        categoryEntry.revenue += itemRevenue;
        categoryEntry.cost += itemCost;
        categoryEntry.profit += itemProfit;
        categoryData.set(categoryId, categoryEntry);

        // Update supplier data
        const supplier = item.stockBatch?.productSupplier?.supplier || item.product.productSuppliers[0]?.supplier;
        if (supplier) {
          const supplierId = supplier.id;
          const supplierEntry = supplierData.get(supplierId) || {
            supplierId,
            supplierName: supplier.name,
            productIds: new Set<string>(),
            revenue: 0,
            cost: 0,
            profit: 0,
          };

          supplierEntry.productIds.add(productId);
          supplierEntry.revenue += itemRevenue;
          supplierEntry.cost += itemCost;
          supplierEntry.profit += itemProfit;
          supplierData.set(supplierId, supplierEntry);
        }
      });
    });

    // Calculate overall metrics
    const overallProfitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;
    const averageProfitPerTransaction = transactions.length > 0 ? totalProfit / transactions.length : 0;
    const averageProfitPerItem = totalItems > 0 ? totalProfit / totalItems : 0;

    // Process product analysis
    const productAnalysis = Array.from(productData.values()).map(product => ({
      ...product,
      profitMargin: product.revenue > 0 ? (product.profit / product.revenue) * 100 : 0,
      averageSellingPrice: product.quantitySold > 0 ? product.revenue / product.quantitySold : 0,
      averageCost: product.quantitySold > 0 ? product.cost / product.quantitySold : 0,
      profitPerUnit: product.quantitySold > 0 ? product.profit / product.quantitySold : 0,
      revenue: Math.round(product.revenue * 100) / 100,
      cost: Math.round(product.cost * 100) / 100,
      profit: Math.round(product.profit * 100) / 100,
    })).sort((a, b) => b.profit - a.profit);

    // Process category analysis
    const categoryAnalysis = Array.from(categoryData.values()).map(category => ({
      categoryId: category.categoryId,
      categoryName: category.categoryName,
      productCount: category.productIds.size,
      totalRevenue: Math.round(category.revenue * 100) / 100,
      totalCost: Math.round(category.cost * 100) / 100,
      totalProfit: Math.round(category.profit * 100) / 100,
      profitMargin: category.revenue > 0 ? Math.round((category.profit / category.revenue) * 10000) / 100 : 0,
      averageProfitPerProduct: category.productIds.size > 0 ? Math.round((category.profit / category.productIds.size) * 100) / 100 : 0,
    })).sort((a, b) => b.totalProfit - a.totalProfit);

    // Process supplier analysis
    const supplierAnalysis = Array.from(supplierData.values()).map(supplier => ({
      supplierId: supplier.supplierId,
      supplierName: supplier.supplierName,
      productCount: supplier.productIds.size,
      totalRevenue: Math.round(supplier.revenue * 100) / 100,
      totalCost: Math.round(supplier.cost * 100) / 100,
      totalProfit: Math.round(supplier.profit * 100) / 100,
      profitMargin: supplier.revenue > 0 ? Math.round((supplier.profit / supplier.revenue) * 10000) / 100 : 0,
      averageProfitPerProduct: supplier.productIds.size > 0 ? Math.round((supplier.profit / supplier.productIds.size) * 100) / 100 : 0,
    })).sort((a, b) => b.totalProfit - a.totalProfit);

    // Process time series analysis
    const timeSeriesAnalysis = Array.from(dailyData.entries()).map(([date, data]) => ({
      date,
      revenue: Math.round(data.revenue * 100) / 100,
      cost: Math.round(data.cost * 100) / 100,
      profit: Math.round(data.profit * 100) / 100,
      profitMargin: data.revenue > 0 ? Math.round((data.profit / data.revenue) * 10000) / 100 : 0,
      transactionCount: data.transactionCount,
    })).sort((a, b) => a.date.localeCompare(b.date));

    // Calculate profit margin distribution
    const totalProducts = productAnalysis.length;
    let highMarginCount = 0;
    let mediumMarginCount = 0;
    let lowMarginCount = 0;
    let veryLowMarginCount = 0;
    let negativeMarginCount = 0;

    productAnalysis.forEach(product => {
      if (product.profitMargin < 0) {
        negativeMarginCount++;
      } else if (product.profitMargin < 5) {
        veryLowMarginCount++;
      } else if (product.profitMargin < 15) {
        lowMarginCount++;
      } else if (product.profitMargin < 30) {
        mediumMarginCount++;
      } else {
        highMarginCount++;
      }
    });

    const profitMarginDistribution = {
      highMargin: {
        count: highMarginCount,
        percentage: totalProducts > 0 ? Math.round((highMarginCount / totalProducts) * 10000) / 100 : 0,
        threshold: 30
      },
      mediumMargin: {
        count: mediumMarginCount,
        percentage: totalProducts > 0 ? Math.round((mediumMarginCount / totalProducts) * 10000) / 100 : 0,
        threshold: "15-30%"
      },
      lowMargin: {
        count: lowMarginCount,
        percentage: totalProducts > 0 ? Math.round((lowMarginCount / totalProducts) * 10000) / 100 : 0,
        threshold: "5-15%"
      },
      veryLowMargin: {
        count: veryLowMarginCount,
        percentage: totalProducts > 0 ? Math.round((veryLowMarginCount / totalProducts) * 10000) / 100 : 0,
        threshold: "<5%"
      },
      negativemargin: {
        count: negativeMarginCount,
        percentage: totalProducts > 0 ? Math.round((negativeMarginCount / totalProducts) * 10000) / 100 : 0
      },
    };

    // Top and least profitable products
    const topProfitableProducts = productAnalysis
      .slice(0, 10)
      .map(product => ({
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        totalProfit: product.profit,
        profitMargin: product.profitMargin,
        quantitySold: product.quantitySold,
      }));

    const leastProfitableProducts = productAnalysis
      .slice(-10)
      .reverse()
      .map(product => ({
        productId: product.productId,
        productName: product.productName,
        sku: product.sku,
        totalProfit: product.profit,
        profitMargin: product.profitMargin,
        quantitySold: product.quantitySold,
      }));

    const report: ProfitMarginAnalysis = {
      period: {
        start: format(startDate, "yyyy-MM-dd"),
        end: format(endDate, "yyyy-MM-dd"),
      },
      overallMetrics: {
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalCost: Math.round(totalCost * 100) / 100,
        totalProfit: Math.round(totalProfit * 100) / 100,
        overallProfitMargin: Math.round(overallProfitMargin * 100) / 100,
        averageProfitPerTransaction: Math.round(averageProfitPerTransaction * 100) / 100,
        averageProfitPerItem: Math.round(averageProfitPerItem * 100) / 100,
      },
      productAnalysis,
      categoryAnalysis,
      supplierAnalysis,
      timeSeriesAnalysis,
      profitMarginDistribution,
      topProfitableProducts,
      leastProfitableProducts,
    };

    return NextResponse.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error generating profit margin analysis:", error);
    return NextResponse.json(
      {
        error: "Failed to generate profit margin analysis",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
