import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/auth";
import { format, startOfDay, endOfDay, subDays, parseISO } from "date-fns";

export interface DailySalesReport {
  reportDate: string;
  summary: {
    totalRevenue: number;
    totalProfit: number;
    totalTransactions: number;
    totalItems: number;
    averageOrderValue: number;
    profitMargin: number;
  };
  paymentMethodBreakdown: Array<{
    method: string;
    count: number;
    amount: number;
    percentage: number;
  }>;
  hourlyBreakdown: Array<{
    hour: number;
    revenue: number;
    transactions: number;
    averageOrderValue: number;
  }>;
  topProducts: Array<{
    productId: string;
    productName: string;
    sku: string;
    quantitySold: number;
    revenue: number;
    profit: number;
  }>;
  cashierPerformance: Array<{
    cashierId: string;
    cashierName: string;
    transactions: number;
    revenue: number;
    averageOrderValue: number;
  }>;
  previousDayComparison: {
    revenueChange: number;
    transactionChange: number;
    profitChange: number;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    const { user } = auth;
    const userRole = user.role;

    // Check if user has financial reporting access
    const hasAccess = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole);
    if (!hasAccess) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const dateParam = searchParams.get("date");
    
    // Default to today if no date provided
    const reportDate = dateParam ? parseISO(dateParam) : new Date();
    const startDate = startOfDay(reportDate);
    const endDate = endOfDay(reportDate);
    
    // Previous day for comparison
    const previousDate = subDays(reportDate, 1);
    const previousStartDate = startOfDay(previousDate);
    const previousEndDate = endOfDay(previousDate);

    // Fetch transactions for the report date
    const transactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: "COMPLETED",
      },
      include: {
        items: {
          include: {
            product: {
              include: {
                category: true,
                productSuppliers: {
                  where: { isPreferred: true },
                  include: { supplier: true },
                },
              },
            },
            stockBatch: true,
          },
        },
        cashier: true,
      },
    });

    // Fetch previous day transactions for comparison
    const previousTransactions = await prisma.transaction.findMany({
      where: {
        createdAt: {
          gte: previousStartDate,
          lte: previousEndDate,
        },
        status: "COMPLETED",
      },
      select: {
        total: true,
        subtotal: true,
        items: {
          select: {
            quantity: true,
            unitPrice: true,
            product: {
              select: {
                productSuppliers: {
                  where: { isPreferred: true },
                  select: { purchasePrice: true },
                },
              },
            },
          },
        },
      },
    });

    // Calculate summary metrics
    const totalRevenue = transactions.reduce((sum, t) => sum + Number(t.total), 0);
    const totalTransactions = transactions.length;
    const totalItems = transactions.reduce((sum, t) => 
      sum + t.items.reduce((itemSum, item) => itemSum + Number(item.quantity), 0), 0
    );
    
    // Calculate profit
    let totalProfit = 0;
    transactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const revenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const cost = Number(purchasePrice) * Number(item.quantity);
        totalProfit += revenue - cost;
      });
    });

    const averageOrderValue = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
    const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue) * 100 : 0;

    // Calculate previous day metrics for comparison
    const previousRevenue = previousTransactions.reduce((sum, t) => sum + Number(t.total), 0);
    const previousTransactionCount = previousTransactions.length;
    let previousProfit = 0;
    previousTransactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const revenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const cost = Number(purchasePrice) * Number(item.quantity);
        previousProfit += revenue - cost;
      });
    });

    const revenueChange = previousRevenue > 0 ? ((totalRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const transactionChange = previousTransactionCount > 0 ? ((totalTransactions - previousTransactionCount) / previousTransactionCount) * 100 : 0;
    const profitChange = previousProfit > 0 ? ((totalProfit - previousProfit) / previousProfit) * 100 : 0;

    // Payment method breakdown
    const paymentMethods = new Map<string, { count: number; amount: number }>();
    transactions.forEach(transaction => {
      const method = transaction.paymentMethod;
      const existing = paymentMethods.get(method) || { count: 0, amount: 0 };
      paymentMethods.set(method, {
        count: existing.count + 1,
        amount: existing.amount + Number(transaction.total),
      });
    });

    const paymentMethodBreakdown = Array.from(paymentMethods.entries()).map(([method, data]) => ({
      method,
      count: data.count,
      amount: data.amount,
      percentage: totalRevenue > 0 ? (data.amount / totalRevenue) * 100 : 0,
    }));

    // Hourly breakdown
    const hourlyData = new Map<number, { revenue: number; transactions: number }>();
    transactions.forEach(transaction => {
      const hour = transaction.createdAt.getHours();
      const existing = hourlyData.get(hour) || { revenue: 0, transactions: 0 };
      hourlyData.set(hour, {
        revenue: existing.revenue + Number(transaction.total),
        transactions: existing.transactions + 1,
      });
    });

    const hourlyBreakdown = Array.from({ length: 24 }, (_, hour) => {
      const data = hourlyData.get(hour) || { revenue: 0, transactions: 0 };
      return {
        hour,
        revenue: data.revenue,
        transactions: data.transactions,
        averageOrderValue: data.transactions > 0 ? data.revenue / data.transactions : 0,
      };
    });

    // Top products
    const productData = new Map<string, { 
      productId: string; 
      productName: string; 
      sku: string; 
      quantitySold: number; 
      revenue: number; 
      profit: number; 
    }>();

    transactions.forEach(transaction => {
      transaction.items.forEach(item => {
        const productId = item.productId;
        const existing = productData.get(productId) || {
          productId,
          productName: item.product.name,
          sku: item.product.sku,
          quantitySold: 0,
          revenue: 0,
          profit: 0,
        };

        const itemRevenue = Number(item.unitPrice) * Number(item.quantity);
        const purchasePrice = item.product.productSuppliers[0]?.purchasePrice || 0;
        const itemCost = Number(purchasePrice) * Number(item.quantity);
        const itemProfit = itemRevenue - itemCost;

        productData.set(productId, {
          ...existing,
          quantitySold: existing.quantitySold + Number(item.quantity),
          revenue: existing.revenue + itemRevenue,
          profit: existing.profit + itemProfit,
        });
      });
    });

    const topProducts = Array.from(productData.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Cashier performance
    const cashierData = new Map<string, { 
      cashierId: string; 
      cashierName: string; 
      transactions: number; 
      revenue: number; 
    }>();

    transactions.forEach(transaction => {
      const cashierId = transaction.cashierId;
      const existing = cashierData.get(cashierId) || {
        cashierId,
        cashierName: transaction.cashier.name,
        transactions: 0,
        revenue: 0,
      };

      cashierData.set(cashierId, {
        ...existing,
        transactions: existing.transactions + 1,
        revenue: existing.revenue + Number(transaction.total),
      });
    });

    const cashierPerformance = Array.from(cashierData.values()).map(cashier => ({
      ...cashier,
      averageOrderValue: cashier.transactions > 0 ? cashier.revenue / cashier.transactions : 0,
    }));

    const report: DailySalesReport = {
      reportDate: format(reportDate, "yyyy-MM-dd"),
      summary: {
        totalRevenue: Math.round(totalRevenue * 100) / 100,
        totalProfit: Math.round(totalProfit * 100) / 100,
        totalTransactions,
        totalItems,
        averageOrderValue: Math.round(averageOrderValue * 100) / 100,
        profitMargin: Math.round(profitMargin * 100) / 100,
      },
      paymentMethodBreakdown,
      hourlyBreakdown,
      topProducts,
      cashierPerformance,
      previousDayComparison: {
        revenueChange: Math.round(revenueChange * 100) / 100,
        transactionChange: Math.round(transactionChange * 100) / 100,
        profitChange: Math.round(profitChange * 100) / 100,
      },
    };

    return NextResponse.json({
      success: true,
      data: report,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error generating daily sales report:", error);
    return NextResponse.json(
      {
        error: "Failed to generate daily sales report",
        success: false,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
