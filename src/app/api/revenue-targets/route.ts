import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// GET /api/revenue-targets - Get all revenue targets
export async function GET(request: NextRequest) {
  try {
    console.log('[Revenue Targets API] GET request received');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN or FINANCE_ADMIN)
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(authResult.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const active = searchParams.get("active");
    const targetType = searchParams.get("targetType");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Build where clause
    const where: any = {};
    
    if (active !== null) {
      where.isActive = active === "true";
    }
    
    if (targetType) {
      where.targetType = targetType;
    }
    
    if (startDate && endDate) {
      where.startDate = { gte: new Date(startDate) };
      where.endDate = { lte: new Date(endDate) };
    }

    const targets = await prisma.revenueTarget.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: [
        { startDate: 'desc' },
        { createdAt: 'desc' }
      ]
    });

    console.log(`[Revenue Targets API] Found ${targets.length} targets`);

    return NextResponse.json({
      success: true,
      data: targets,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Revenue Targets API] Error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch revenue targets',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// POST /api/revenue-targets - Create new revenue target
export async function POST(request: NextRequest) {
  try {
    console.log('[Revenue Targets API] POST request received');

    // Verify authentication
    const authResult = await verifyAuthToken(request);
    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user has permission (SUPER_ADMIN or FINANCE_ADMIN)
    if (!['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(authResult.user.role)) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const { name, description, targetType, startDate, endDate, amount, isActive } = body;

    // Validate required fields
    if (!name || !targetType || !startDate || !endDate || !amount) {
      return NextResponse.json(
        { error: "Missing required fields: name, targetType, startDate, endDate, amount" },
        { status: 400 }
      );
    }

    // Validate date range
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (start >= end) {
      return NextResponse.json(
        { error: "Start date must be before end date" },
        { status: 400 }
      );
    }

    // Check for overlapping targets of the same type
    const overlapping = await prisma.revenueTarget.findFirst({
      where: {
        targetType,
        isActive: true,
        OR: [
          {
            AND: [
              { startDate: { lte: start } },
              { endDate: { gte: start } }
            ]
          },
          {
            AND: [
              { startDate: { lte: end } },
              { endDate: { gte: end } }
            ]
          },
          {
            AND: [
              { startDate: { gte: start } },
              { endDate: { lte: end } }
            ]
          }
        ]
      }
    });

    if (overlapping) {
      return NextResponse.json(
        { error: `Overlapping ${targetType.toLowerCase()} target already exists for this period` },
        { status: 400 }
      );
    }

    // Create the revenue target
    const target = await prisma.revenueTarget.create({
      data: {
        name,
        description,
        targetType,
        startDate: start,
        endDate: end,
        amount: parseFloat(amount),
        isActive: isActive !== false,
        createdBy: authResult.user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    console.log('[Revenue Targets API] Target created:', target.id);

    return NextResponse.json({
      success: true,
      data: target,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Revenue Targets API] Error creating target:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create revenue target',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
