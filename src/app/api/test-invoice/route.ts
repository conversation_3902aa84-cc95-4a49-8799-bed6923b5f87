import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { prisma } from '@/lib/prisma';

// Simple test endpoint to verify basic functionality
export async function POST(request: NextRequest) {
  console.log('🧪 Test invoice endpoint called');
  
  try {
    // Test 1: Basic response
    console.log('✅ Test endpoint is working');
    
    // Test 2: Authentication
    console.log('🔐 Testing authentication...');
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      console.log('❌ Authentication failed:', auth.error);
      return NextResponse.json({ 
        error: 'Authentication failed',
        details: auth.error,
        test: 'auth'
      }, { status: auth.status });
    }
    console.log('✅ Authentication successful:', auth.user.email);
    
    // Test 3: Database connection
    console.log('🗄️  Testing database connection...');
    const supplierCount = await prisma.supplier.count();
    const productCount = await prisma.product.count();
    console.log(`✅ Database connected - Suppliers: ${supplierCount}, Products: ${productCount}`);
    
    // Test 4: Request body parsing
    console.log('📥 Testing request body parsing...');
    const body = await request.json();
    console.log('✅ Request body parsed:', JSON.stringify(body, null, 2));
    
    // Test 5: Basic validation
    console.log('🔍 Testing basic validation...');
    if (!body.testField) {
      console.log('⚠️  Missing testField in request body');
    } else {
      console.log('✅ Test field found:', body.testField);
    }
    
    return NextResponse.json({
      success: true,
      message: 'Test endpoint working correctly',
      auth: {
        user: auth.user.email,
        role: auth.user.role
      },
      database: {
        suppliers: supplierCount,
        products: productCount
      },
      requestBody: body,
      timestamp: new Date().toISOString()
    }, { status: 200 });
    
  } catch (error) {
    console.error('❌ Test endpoint error:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'Test endpoint failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET endpoint for basic health check
export async function GET(request: NextRequest) {
  console.log('🏥 Health check endpoint called');
  
  try {
    // Basic health check
    const dbHealth = await prisma.$queryRaw`SELECT 1 as health`;
    
    return NextResponse.json({
      status: 'healthy',
      database: 'connected',
      timestamp: new Date().toISOString(),
      dbHealth
    }, { status: 200 });
    
  } catch (error) {
    console.error('❌ Health check failed:', error);
    
    return NextResponse.json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
