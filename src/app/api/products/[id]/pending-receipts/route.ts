import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/[id]/pending-receipts - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/[id]/pending-receipts - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// GET /api/products/[id]/pending-receipts - Get PO items with pending receipts for a product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params;
    console.log("[API] GET /api/products/[id]/pending-receipts - Start, Product ID:", productId);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true, sku: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    console.log("[API] Fetching pending receipt items for product:", productId);

    // Get PO items that have pending receipts (receivedQuantity < quantity)
    // Only from approved purchase orders
    const pendingItems = await prisma.purchaseOrderItem.findMany({
      where: {
        productId: productId,
        purchaseOrder: {
          status: {
            in: ['APPROVED', 'PARTIALLY_RECEIVED'] // Only approved POs can be received
          }
        },
        // Only items with remaining quantity to receive
        // We'll filter this in JavaScript since Prisma doesn't support field comparisons in this way
      },
      include: {
        purchaseOrder: {
          select: {
            id: true,
            orderDate: true,
            status: true,
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true
              }
            }
          }
        },
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            unit: {
              select: {
                id: true,
                name: true,
                abbreviation: true
              }
            }
          }
        },
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true
              }
            }
          }
        }
      },
      orderBy: {
        purchaseOrder: {
          orderDate: 'desc'
        }
      }
    });

    // Calculate remaining quantities and format response
    const itemsWithRemaining = pendingItems
      .map(item => {
        const orderedQty = Number(item.quantity);
        const receivedQty = Number(item.receivedQuantity || 0);
        const remainingQty = orderedQty - receivedQty;

        return {
          id: item.id,
          purchaseOrderId: item.purchaseOrderId,
          productId: item.productId,
          quantity: orderedQty,
          receivedQuantity: receivedQty,
          remainingQuantity: remainingQty,
          unitPrice: Number(item.unitPrice),
          subtotal: Number(item.subtotal),
          purchaseOrder: {
            id: item.purchaseOrder.id,
            orderDate: item.purchaseOrder.orderDate,
            status: item.purchaseOrder.status,
            orderNumber: `PO-${item.purchaseOrder.id.slice(-8).toUpperCase()}`, // Generate display number
            supplier: item.purchaseOrder.supplier
          },
          product: item.product,
          productSupplier: item.productSupplier
        };
      })
      .filter(item => item.remainingQuantity > 0); // Only items with remaining quantity

    console.log("[API] Found", itemsWithRemaining.length, "pending receipt items for product:", productId);

    return NextResponse.json({
      product,
      items: itemsWithRemaining,
      total: itemsWithRemaining.length
    });

  } catch (error) {
    console.error("[API] Error fetching pending receipts:", error);
    return NextResponse.json(
      { error: "Failed to fetch pending receipts", message: (error as Error).message },
      { status: 500 }
    );
  }
}
