import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { BatchStatus } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/[id]/batches - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/[id]/batches - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Validation schema for creating a new batch for a specific product
const createProductBatchSchema = z.object({
  productSupplierId: z.string({
    required_error: "Product Supplier ID is required",
  }),
  purchaseOrderItemId: z.string({
    required_error: "Purchase Order Item ID is required - batches can only be created when receiving goods from a Purchase Order",
  }),
  batchNumber: z.string().optional().nullable(),
  receivedDate: z.string().datetime().optional(),
  expiryDate: z.string().datetime().optional().nullable(),
  quantity: z.number().positive({
    message: "Quantity must be positive",
  }),
  purchasePrice: z.number().positive({
    message: "Purchase price must be positive",
  }),
  purchaseOrderId: z.string().optional().nullable(),
  warehouseStockId: z.string().optional().nullable(),
  storeStockId: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
});

// GET /api/products/[id]/batches - Get all batches for a product
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params;
    console.log("[API] GET /api/products/[id]/batches - Start, Product ID:", productId);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view inventory
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { 
        id: true, 
        name: true, 
        sku: true,
        unit: {
          select: {
            id: true,
            name: true,
            abbreviation: true
          }
        }
      }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status") as BatchStatus | null;
    const supplierId = searchParams.get("supplierId");
    const sortBy = searchParams.get("sortBy") || "receivedDate";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Build where clause
    const where: any = {
      productId: productId
    };

    if (status) {
      where.status = status;
    }

    if (supplierId) {
      where.productSupplier = {
        supplierId: supplierId
      };
    }

    // Build order by clause
    const orderBy: any = {};
    if (sortBy === "supplier.name") {
      orderBy.productSupplier = { supplier: { name: sortOrder } };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    console.log("[API] Fetching batches for product with filters:", { where, orderBy });

    // Get batches for the product
    const batches = await prisma.stockBatch.findMany({
      where,
      include: {
        productSupplier: {
          include: {
            supplier: {
              select: {
                id: true,
                name: true,
                contactPerson: true,
                phone: true,
                email: true
              }
            }
          }
        },
        purchaseOrder: {
          select: {
            id: true,
            orderDate: true,
            status: true
          }
        },
        storeStock: {
          select: {
            id: true,
            quantity: true
          }
        },
        warehouseStock: {
          select: {
            id: true,
            quantity: true
          }
        }
      },
      orderBy,
    });

    console.log("[API] Found", batches.length, "batches for product:", productId);

    return NextResponse.json({
      product,
      batches
    });
  } catch (error) {
    console.error("[API] Error fetching product batches:", error);
    return NextResponse.json(
      { error: "Failed to fetch product batches", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/products/[id]/batches - Create batch for specific product
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: productId } = await params;
    console.log("[API] POST /api/products/[id]/batches - Start, Product ID:", productId);

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to create stock batches
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { id: true, name: true }
    });

    if (!product) {
      return NextResponse.json(
        { error: "Product not found" },
        { status: 404 }
      );
    }

    // Get request body
    const body = await request.json();
    console.log("[API] Request body:", body);

    // Validate batch data
    const validationResult = createProductBatchSchema.safeParse(body);
    if (!validationResult.success) {
      console.error("[API] Validation failed:", validationResult.error.issues);
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const {
      productSupplierId,
      purchaseOrderItemId,
      batchNumber,
      receivedDate,
      expiryDate,
      quantity,
      purchasePrice,
      purchaseOrderId,
      warehouseStockId,
      storeStockId,
      notes
    } = validationResult.data;

    // Verify product supplier relationship exists and matches the product
    const productSupplier = await prisma.productSupplier.findUnique({
      where: { id: productSupplierId },
      include: {
        supplier: {
          select: { id: true, name: true }
        }
      }
    });

    if (!productSupplier || productSupplier.productId !== productId) {
      return NextResponse.json(
        { error: "Product supplier relationship not found or does not match the product" },
        { status: 404 }
      );
    }

    // Verify purchase order item exists and validate receipt quantities
    const purchaseOrderItem = await prisma.purchaseOrderItem.findUnique({
      where: { id: purchaseOrderItemId },
      include: {
        purchaseOrder: {
          select: {
            id: true,
            status: true,
            orderDate: true,
            supplier: {
              select: { id: true, name: true }
            }
          }
        },
        product: {
          select: { id: true, name: true }
        }
      }
    });

    if (!purchaseOrderItem) {
      return NextResponse.json(
        { error: "Purchase order item not found" },
        { status: 404 }
      );
    }

    // Verify the PO item matches the specified product
    if (purchaseOrderItem.productId !== productId) {
      return NextResponse.json(
        { error: "Purchase order item does not match the specified product" },
        { status: 400 }
      );
    }

    // Check if purchase order is approved and ready for receipt
    if (!['APPROVED', 'PARTIALLY_RECEIVED'].includes(purchaseOrderItem.purchaseOrder.status)) {
      return NextResponse.json(
        { error: `Purchase order status is '${purchaseOrderItem.purchaseOrder.status}'. Only approved purchase orders can be received.` },
        { status: 400 }
      );
    }

    // Calculate remaining quantity to receive
    const orderedQuantity = Number(purchaseOrderItem.quantity);
    const receivedQuantity = Number(purchaseOrderItem.receivedQuantity || 0);
    const remainingQuantity = orderedQuantity - receivedQuantity;

    // Validate that we're not receiving more than what's remaining
    if (quantity > remainingQuantity) {
      return NextResponse.json(
        {
          error: `Cannot receive ${quantity} units. Only ${remainingQuantity} units remaining to receive from this purchase order item.`,
          details: {
            ordered: orderedQuantity,
            received: receivedQuantity,
            remaining: remainingQuantity,
            attempting: quantity
          }
        },
        { status: 400 }
      );
    }

    // If no remaining quantity, reject the request
    if (remainingQuantity <= 0) {
      return NextResponse.json(
        { error: "This purchase order item has already been fully received" },
        { status: 400 }
      );
    }

    console.log("[API] PO validation passed for product batch creation:", {
      poItemId: purchaseOrderItemId,
      ordered: orderedQuantity,
      received: receivedQuantity,
      remaining: remainingQuantity,
      receiving: quantity
    });

    console.log("[API] Creating new stock batch for product:", productId);

    // Use transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the stock batch
      const newBatch = await tx.stockBatch.create({
        data: {
          productId,
          productSupplierId,
          batchNumber,
          receivedDate: receivedDate ? new Date(receivedDate) : new Date(),
          expiryDate: expiryDate ? new Date(expiryDate) : null,
          quantity,
          remainingQuantity: quantity,
          purchasePrice,
          purchaseOrderId: purchaseOrderItem.purchaseOrder.id,
          warehouseStockId,
          storeStockId,
          notes,
          status: BatchStatus.ACTIVE
        },
        include: {
          productSupplier: {
            include: {
              supplier: {
                select: {
                  id: true,
                  name: true,
                  contactPerson: true
                }
              }
            }
          }
        }
      });

      // Update the purchase order item's received quantity
      const newReceivedQuantity = receivedQuantity + quantity;
      await tx.purchaseOrderItem.update({
        where: { id: purchaseOrderItemId },
        data: {
          receivedQuantity: newReceivedQuantity
        }
      });

      // Update purchase order status if all items are fully received
      const allPOItems = await tx.purchaseOrderItem.findMany({
        where: { purchaseOrderId: purchaseOrderItem.purchaseOrder.id },
        select: {
          quantity: true,
          receivedQuantity: true
        }
      });

      const allFullyReceived = allPOItems.every(item =>
        Number(item.receivedQuantity || 0) >= Number(item.quantity)
      );

      const hasPartialReceipts = allPOItems.some(item =>
        Number(item.receivedQuantity || 0) > 0
      );

      if (allFullyReceived) {
        await tx.purchaseOrder.update({
          where: { id: purchaseOrderItem.purchaseOrder.id },
          data: {
            status: 'RECEIVED',
            receivedAt: new Date()
          }
        });
      } else if (hasPartialReceipts) {
        await tx.purchaseOrder.update({
          where: { id: purchaseOrderItem.purchaseOrder.id },
          data: { status: 'PARTIALLY_RECEIVED' }
        });
      }

      return newBatch;
    });

    console.log("[API] Stock batch created successfully for product:", productId, "Batch ID:", result.id);
    console.log("[API] PO item received quantity updated:", {
      poItemId: purchaseOrderItemId,
      newReceivedQuantity: receivedQuantity + quantity
    });

    return NextResponse.json({
      message: "Stock batch created successfully and purchase order updated",
      batch: result,
      purchaseOrderUpdate: {
        itemId: purchaseOrderItemId,
        newReceivedQuantity: receivedQuantity + quantity,
        remainingQuantity: remainingQuantity - quantity
      }
    }, { status: 201 });

  } catch (error) {
    console.error("[API] Error creating product batch:", error);
    return NextResponse.json(
      { error: "Failed to create product batch", message: (error as Error).message },
      { status: 500 }
    );
  }
}
