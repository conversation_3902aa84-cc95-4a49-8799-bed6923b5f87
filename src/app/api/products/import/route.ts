import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import * as XLSX from 'xlsx';
import { parse as csvParse } from 'csv-parse/sync';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/import - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/import - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get import mode (create, update, or upsert)
    const searchParams = request.nextUrl.searchParams;
    const mode = searchParams.get("mode") || "upsert";
    
    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 });
    }

    // Read file content
    const fileBuffer = await file.arrayBuffer();
    const fileData = new Uint8Array(fileBuffer);
    
    // Parse file based on type
    let products = [];
    const fileType = file.name.split('.').pop()?.toLowerCase();
    
    if (fileType === 'xlsx' || fileType === 'xls') {
      const workbook = XLSX.read(fileData, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      products = XLSX.utils.sheet_to_json(worksheet);
    } else if (fileType === 'csv') {
      const csvContent = new TextDecoder().decode(fileData);
      products = csvParse(csvContent, { 
        columns: true,
        skip_empty_lines: true 
      });
    } else {
      return NextResponse.json({ 
        error: "Unsupported file format. Please upload .xlsx, .xls, or .csv file" 
      }, { status: 400 });
    }

    // Validate data structure
    const validationResult = await validateProductData(products);
    if (!validationResult.valid) {
      return NextResponse.json({ 
        error: "Invalid data structure", 
        details: validationResult.errors 
      }, { status: 400 });
    }

    // Process products based on mode
    const result = await processProducts(products, mode);

    return NextResponse.json({
      success: true,
      message: `Successfully imported ${result.created} products, updated ${result.updated} products, and encountered ${result.errors} errors.`,
      details: result.details
    });
  } catch (error) {
    console.error("Error importing products:", error);
    return NextResponse.json(
      { error: "Failed to import products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Helper function to validate product data
async function validateProductData(products: any[]) {
  const errors = [];
  const requiredFields = ['name', 'sku', 'unitId', 'basePrice'];
  
  // Check if array is empty
  if (products.length === 0) {
    return { valid: false, errors: ['File contains no product data'] };
  }
  
  // Validate each product
  for (let i = 0; i < products.length; i++) {
    const product = products[i];
    const rowErrors = [];
    
    // Check required fields
    for (const field of requiredFields) {
      if (!product[field]) {
        rowErrors.push(`Missing required field: ${field}`);
      }
    }
    
    // Validate numeric fields
    const numericFields = ['basePrice', 'purchasePrice', 'optionalPrice1', 'optionalPrice2', 'stockQuantity', 'minThreshold'];
    for (const field of numericFields) {
      if (product[field] && isNaN(Number(product[field]))) {
        rowErrors.push(`Field ${field} must be a number`);
      }
    }
    
    // Validate foreign keys
    if (product.unitId) {
      const unit = await prisma.unit.findUnique({ where: { id: product.unitId } });
      if (!unit) {
        rowErrors.push(`Unit with ID ${product.unitId} does not exist`);
      }
    }
    
    if (product.categoryId) {
      const category = await prisma.category.findUnique({ where: { id: product.categoryId } });
      if (!category) {
        rowErrors.push(`Category with ID ${product.categoryId} does not exist`);
      }
    }
    
    // Note: supplierId validation removed - suppliers now managed via ProductSupplier relationships
    
    // Add row errors if any
    if (rowErrors.length > 0) {
      errors.push({
        row: i + 2, // +2 because of 0-indexing and header row
        sku: product.sku || `Row ${i + 2}`,
        errors: rowErrors
      });
    }
  }
  
  return { valid: errors.length === 0, errors };
}

// Helper function to process products
async function processProducts(products: any[], mode: string) {
  const result = {
    created: 0,
    updated: 0,
    errors: 0,
    details: [] as any[]
  };
  
  for (const productData of products) {
    try {
      // Prepare product data
      const product = {
        name: productData.name,
        description: productData.description || null,
        sku: productData.sku,
        barcode: productData.barcode || null,
        categoryId: productData.categoryId || null,
        unitId: productData.unitId,
        supplierId: productData.supplierId || null,
        basePrice: parseFloat(productData.basePrice),
        purchasePrice: productData.purchasePrice ? parseFloat(productData.purchasePrice) : null,
        optionalPrice1: productData.optionalPrice1 ? parseFloat(productData.optionalPrice1) : null,
        optionalPrice2: productData.optionalPrice2 ? parseFloat(productData.optionalPrice2) : null,
        expiryDate: productData.expiryDate ? new Date(productData.expiryDate) : null,
        imageUrl: productData.imageUrl || null,
        active: productData.active === 'true' || productData.active === true,
      };
      
      // Process based on mode
      if (mode === 'create') {
        // Create new product
        const newProduct = await prisma.product.create({
          data: product
        });
        
        // Create store stock if provided
        if (productData.stockQuantity) {
          await prisma.storeStock.create({
            data: {
              productId: newProduct.id,
              quantity: parseFloat(productData.stockQuantity),
              minThreshold: parseFloat(productData.minThreshold || '0'),
            }
          });
        }
        
        result.created++;
        result.details.push({
          sku: product.sku,
          status: 'created',
          id: newProduct.id
        });
      } else if (mode === 'update') {
        // Find existing product by SKU
        const existingProduct = await prisma.product.findUnique({
          where: { sku: product.sku }
        });
        
        if (existingProduct) {
          // Update product
          await prisma.product.update({
            where: { id: existingProduct.id },
            data: product
          });
          
          // Update or create store stock
          if (productData.stockQuantity) {
            await prisma.storeStock.upsert({
              where: { productId: existingProduct.id },
              update: {
                quantity: parseFloat(productData.stockQuantity),
                minThreshold: parseFloat(productData.minThreshold || '0'),
              },
              create: {
                productId: existingProduct.id,
                quantity: parseFloat(productData.stockQuantity),
                minThreshold: parseFloat(productData.minThreshold || '0'),
              }
            });
          }
          
          result.updated++;
          result.details.push({
            sku: product.sku,
            status: 'updated',
            id: existingProduct.id
          });
        } else {
          throw new Error(`Product with SKU ${product.sku} not found`);
        }
      } else {
        // Upsert mode (default)
        const existingProduct = await prisma.product.findUnique({
          where: { sku: product.sku }
        });
        
        if (existingProduct) {
          // Update product
          await prisma.product.update({
            where: { id: existingProduct.id },
            data: product
          });
          
          // Update or create store stock
          if (productData.stockQuantity) {
            await prisma.storeStock.upsert({
              where: { productId: existingProduct.id },
              update: {
                quantity: parseFloat(productData.stockQuantity),
                minThreshold: parseFloat(productData.minThreshold || '0'),
              },
              create: {
                productId: existingProduct.id,
                quantity: parseFloat(productData.stockQuantity),
                minThreshold: parseFloat(productData.minThreshold || '0'),
              }
            });
          }
          
          result.updated++;
          result.details.push({
            sku: product.sku,
            status: 'updated',
            id: existingProduct.id
          });
        } else {
          // Create new product
          const newProduct = await prisma.product.create({
            data: product
          });
          
          // Create store stock if provided
          if (productData.stockQuantity) {
            await prisma.storeStock.create({
              data: {
                productId: newProduct.id,
                quantity: parseFloat(productData.stockQuantity),
                minThreshold: parseFloat(productData.minThreshold || '0'),
              }
            });
          }
          
          result.created++;
          result.details.push({
            sku: product.sku,
            status: 'created',
            id: newProduct.id
          });
        }
      }
    } catch (error) {
      console.error(`Error processing product ${productData.sku}:`, error);
      result.errors++;
      result.details.push({
        sku: productData.sku,
        status: 'error',
        error: (error as Error).message
      });
    }
  }
  
  return result;
}
