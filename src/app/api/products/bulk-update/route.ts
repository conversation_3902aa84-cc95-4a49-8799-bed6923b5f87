import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { z } from "zod";
import { Prisma } from "@prisma/client";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/bulk-update - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await import("jose").then(({ jwtVerify }) =>
      jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      )
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/bulk-update - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

// Bulk update schema for validation
const bulkUpdateSchema = z.object({
  productIds: z.array(z.string()).optional(),
  categoryId: z.string().optional(),
  updateType: z.enum(["FIXED", "PERCENTAGE"]),
  updateField: z.enum(["basePrice"]),
  value: z.number(),
  operation: z.enum(["INCREASE", "DECREASE", "SET"]),
}).refine(data => data.productIds || data.categoryId, {
  message: "Either productIds or categoryId must be provided",
  path: ["productIds"],
});

// POST /api/products/bulk-update - Bulk update product prices
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update products
    const hasPermission = ["SUPER_ADMIN", "WAREHOUSE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate bulk update data
    const validationResult = bulkUpdateSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", issues: validationResult.error.issues },
        { status: 400 }
      );
    }

    const { productIds, categoryId, updateType, updateField, value, operation } = validationResult.data;

    // Get products based on productIds or categoryId
    let products = [];
    let updateSource = "";

    if (productIds && productIds.length > 0) {
      // Get products by IDs, excluding those with temporary prices
      products = await prisma.product.findMany({
        where: {
          id: { in: productIds },
          temporaryPrice: null // Exclude products with temporary prices
        },
      });

      if (products.length !== productIds.length) {
        return NextResponse.json(
          { error: "Some products were not found or have temporary discounts applied (products with temporary discounts cannot be updated)" },
          { status: 400 }
        );
      }

      updateSource = `selected products (${products.length})`;
    } else if (categoryId) {
      // Get products by category
      const category = await prisma.category.findUnique({
        where: { id: categoryId },
        include: { _count: { select: { products: true } } }
      });

      if (!category) {
        return NextResponse.json(
          { error: "Category not found" },
          { status: 400 }
        );
      }

      products = await prisma.product.findMany({
        where: {
          categoryId: categoryId,
          active: true,
          temporaryPrice: null // Exclude products with temporary prices
        },
      });

      if (products.length === 0) {
        return NextResponse.json(
          { error: "No eligible products found in this category. Products may be inactive or have temporary discounts applied." },
          { status: 400 }
        );
      }

      updateSource = `category "${category.name}" (${products.length} products)`;
    }

    // Perform bulk update
    const updatedProducts = [];
    const updateDetails = [];

    for (const product of products) {
      const currentValue = Number(product[updateField] || 0);
      let newValue: number;

      if (operation === "SET") {
        newValue = value;
      } else if (operation === "INCREASE") {
        if (updateType === "PERCENTAGE") {
          newValue = currentValue * (1 + value / 100);
        } else {
          newValue = currentValue + value;
        }
      } else if (operation === "DECREASE") {
        if (updateType === "PERCENTAGE") {
          newValue = currentValue * (1 - value / 100);
        } else {
          newValue = currentValue - value;
        }
      } else {
        newValue = currentValue;
      }

      // Ensure price is not negative
      newValue = Math.max(0, newValue);

      // Update product
      let updatedProduct;

      // Use standard Prisma update for all fields
      updatedProduct = await prisma.product.update({
        where: { id: product.id },
        data: { [updateField]: newValue },
      });

      updatedProducts.push(updatedProduct);
      updateDetails.push(`${product.name} (${product.sku}): ${currentValue} → ${newValue}`);
    }

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "BULK_UPDATE_PRODUCTS",
        details: `Bulk updated ${updateSource} - ${updateField} ${operation} by ${value} ${updateType === "PERCENTAGE" ? "%" : ""}`,
      },
    });

    return NextResponse.json({
      success: true,
      message: `Successfully updated ${products.length} products`,
      updatedProducts,
    });
  } catch (error) {
    console.error("Error bulk updating products:", error);
    return NextResponse.json(
      { error: "Failed to bulk update products", message: (error as Error).message },
      { status: 500 }
    );
  }
}
