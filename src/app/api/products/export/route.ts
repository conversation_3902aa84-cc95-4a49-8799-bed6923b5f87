import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";
import * as XLSX from 'xlsx';
import { parse } from 'json2csv';
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    console.log("[API] /api/products/export - No session token found");
    return {
      authenticated: false,
      error: "Unauthorized - No session token",
      status: 403,
      user: null
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string
      }
    };
  } catch (error) {
    console.error("[API] /api/products/export - JWT verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized - Invalid token",
      status: 403,
      user: null
    };
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: auth.error }, { status: auth.status });
    }

    // Get format from query params (default to csv)
    const searchParams = request.nextUrl.searchParams;
    const format = searchParams.get("format") || "csv";

    // Fetch all products with related data
    const products = await prisma.product.findMany({
      include: {
        category: true,
        unit: true,
        storeStock: true,
      },
    });

    // Transform data for export (flatten nested objects)
    const exportData = products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description || "",
      sku: product.sku,
      barcode: product.barcode || "",
      categoryId: product.categoryId || "",
      categoryName: product.category?.name || "",
      unitId: product.unitId,
      unitName: product.unit.name,
      unitAbbreviation: product.unit.abbreviation,
      // Note: supplier fields removed - now using ProductSupplier relationships
      supplierId: "", // Legacy field - no longer available
      supplierName: "", // Legacy field - now use ProductSupplier relationships
      basePrice: product.basePrice.toString(),
      purchasePrice: product.purchasePrice?.toString() || "",
      expiryDate: product.expiryDate ? product.expiryDate.toISOString() : "",
      imageUrl: product.imageUrl || "",
      createdAt: product.createdAt.toISOString(),
      updatedAt: product.updatedAt.toISOString(),
      active: product.active ? "true" : "false",
      stockQuantity: product.storeStock?.quantity.toString() || "0",
      minThreshold: product.storeStock?.minThreshold.toString() || "0",
    }));

    // Generate file based on requested format
    if (format === "xlsx") {
      // Create Excel workbook
      const worksheet = XLSX.utils.json_to_sheet(exportData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Products");

      // Convert to buffer
      const buffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });

      // Return as downloadable file
      return new NextResponse(buffer, {
        headers: {
          "Content-Type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
          "Content-Disposition": `attachment; filename="products_${new Date().toISOString().split('T')[0]}.xlsx"`,
        },
      });
    } else if (format === "pdf") {
      // Create PDF document
      const pdfBuffer = await generateProductsPdf(products);

      // Return as downloadable file
      return new NextResponse(pdfBuffer, {
        headers: {
          "Content-Type": "application/pdf",
          "Content-Disposition": `attachment; filename="products_${new Date().toISOString().split('T')[0]}.pdf"`,
        },
      });
    } else {
      // CSV format (default)
      const csvData = parse(exportData, {
        fields: Object.keys(exportData[0] || {})
      });

      // Return as downloadable file
      return new NextResponse(csvData, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="products_${new Date().toISOString().split('T')[0]}.csv"`,
        },
      });
    }
  } catch (error) {
    console.error("Error exporting products:", error);
    return NextResponse.json(
      { error: "Failed to export products", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// Helper function to generate PDF from products data
async function generateProductsPdf(products: any[]) {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();

  // Add a page to the document
  const page = pdfDoc.addPage([842, 595]); // A4 landscape

  // Embed the standard font
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Set page margins
  const margin = 50;
  const pageWidth = page.getWidth() - margin * 2;
  const pageHeight = page.getHeight() - margin * 2;

  // Add title
  page.drawText('Products Inventory Report', {
    x: margin,
    y: pageHeight - 20,
    size: 16,
    font: boldFont,
    color: rgb(0, 0, 0),
  });

  // Add date
  const currentDate = new Date().toLocaleDateString();
  page.drawText(`Generated on: ${currentDate}`, {
    x: margin,
    y: pageHeight - 40,
    size: 10,
    font,
    color: rgb(0.3, 0.3, 0.3),
  });

  // Define table columns
  const columns = [
    { header: 'Name', width: 150, property: 'name' },
    { header: 'SKU', width: 80, property: 'sku' },
    { header: 'Category', width: 100, property: (p: any) => p.category?.name || 'Uncategorized' },
    { header: 'Unit', width: 60, property: (p: any) => p.unit.abbreviation },
    { header: 'Base Price', width: 80, property: (p: any) => formatCurrency(p.basePrice) },
    { header: 'Stock', width: 60, property: (p: any) => p.storeStock?.quantity.toString() || '0' },
    { header: 'Status', width: 60, property: (p: any) => p.active ? 'Active' : 'Inactive' },
  ];

  // Calculate total width
  const totalWidth = columns.reduce((sum, col) => sum + col.width, 0);

  // Draw table header
  let x = margin;
  let y = pageHeight - 70;

  // Draw header background
  page.drawRectangle({
    x,
    y: y - 5,
    width: totalWidth,
    height: 25,
    color: rgb(0.9, 0.9, 0.9),
    borderColor: rgb(0.7, 0.7, 0.7),
    borderWidth: 1,
  });

  // Draw header text
  columns.forEach(column => {
    page.drawText(column.header, {
      x: x + 5,
      y: y,
      size: 10,
      font: boldFont,
      color: rgb(0, 0, 0),
    });
    x += column.width;
  });

  // Draw table rows
  y -= 30;
  let rowCount = 0;
  const rowHeight = 25;
  const maxRowsPerPage = Math.floor((pageHeight - 100) / rowHeight);

  for (const product of products) {
    // Check if we need a new page
    if (rowCount >= maxRowsPerPage) {
      // Add a new page
      const newPage = pdfDoc.addPage([842, 595]);
      y = pageHeight - 40;
      rowCount = 0;

      // Draw header on new page
      x = margin;

      // Draw header background
      newPage.drawRectangle({
        x,
        y: y - 5,
        width: totalWidth,
        height: 25,
        color: rgb(0.9, 0.9, 0.9),
        borderColor: rgb(0.7, 0.7, 0.7),
        borderWidth: 1,
      });

      // Draw header text
      columns.forEach(column => {
        newPage.drawText(column.header, {
          x: x + 5,
          y: y,
          size: 10,
          font: boldFont,
          color: rgb(0, 0, 0),
        });
        x += column.width;
      });

      y -= 30;
    }

    // Get current page
    const currentPage = pdfDoc.getPages()[pdfDoc.getPageCount() - 1];

    // Draw row background (alternating colors)
    currentPage.drawRectangle({
      x: margin,
      y: y - 5,
      width: totalWidth,
      height: rowHeight,
      color: rowCount % 2 === 0 ? rgb(1, 1, 1) : rgb(0.95, 0.95, 0.95),
      borderColor: rgb(0.9, 0.9, 0.9),
      borderWidth: 1,
    });

    // Draw row data
    x = margin;
    columns.forEach(column => {
      const value = typeof column.property === 'function'
        ? column.property(product)
        : product[column.property]?.toString() || '';

      currentPage.drawText(value, {
        x: x + 5,
        y: y,
        size: 9,
        font,
        color: rgb(0, 0, 0),
        maxWidth: column.width - 10,
      });
      x += column.width;
    });

    y -= rowHeight;
    rowCount++;
  }

  // Add footer with page numbers
  const pageCount = pdfDoc.getPageCount();
  for (let i = 0; i < pageCount; i++) {
    const page = pdfDoc.getPage(i);
    page.drawText(`Page ${i + 1} of ${pageCount}`, {
      x: page.getWidth() - margin - 100,
      y: margin / 2,
      size: 8,
      font,
      color: rgb(0.5, 0.5, 0.5),
    });
  }

  // Serialize the PDFDocument to bytes
  const pdfBytes = await pdfDoc.save();

  return pdfBytes;
}

// Helper function to format currency
function formatCurrency(value: number | string): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(numValue);
}
