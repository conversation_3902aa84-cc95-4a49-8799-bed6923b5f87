import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";
import { z } from "zod";

const prisma = new PrismaClient();

// Validation schema for store info
const storeInfoSchema = z.object({
  storeName: z.string().min(1, "Store name is required").max(100, "Store name must be less than 100 characters"),
  phone: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  email: z.string().email("Invalid email format").optional().nullable().or(z.literal("")),
  website: z.string().url("Invalid website URL").optional().nullable().or(z.literal("")),
  taxId: z.string().optional().nullable(),
  logoUrl: z.string().url("Invalid logo URL").optional().nullable().or(z.literal(""))
});

// GET /api/store-info - Get store information
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get store information
    let storeInfo = await prisma.storeInfo.findFirst({
      where: { id: "default-store" }
    });

    // If no store info exists, create default
    if (!storeInfo) {
      storeInfo = await prisma.storeInfo.create({
        data: {
          id: "default-store",
          storeName: "Next POS Store",
          phone: null,
          address: null,
          email: null,
          website: null,
          taxId: null,
          logoUrl: null
        }
      });
    }

    return NextResponse.json({ storeInfo });
  } catch (error) {
    console.error("Error fetching store info:", error);
    return NextResponse.json(
      { error: "Failed to fetch store information", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PUT /api/store-info - Update store information
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only allow ADMIN and SUPER_ADMIN to update store info
    if (!["ADMIN", "SUPER_ADMIN"].includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized. Only Admins can update store information." },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate input
    const validationResult = storeInfoSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: validationResult.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const validatedData = validationResult.data;

    // Convert empty strings to null for optional fields
    const updateData = {
      storeName: validatedData.storeName,
      phone: validatedData.phone || null,
      address: validatedData.address || null,
      email: validatedData.email || null,
      website: validatedData.website || null,
      taxId: validatedData.taxId || null,
      logoUrl: validatedData.logoUrl || null,
      updatedAt: new Date()
    };

    // Update or create store info
    const storeInfo = await prisma.storeInfo.upsert({
      where: { id: "default-store" },
      update: updateData,
      create: {
        id: "default-store",
        ...updateData
      }
    });

    // Log the activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_STORE_INFO",
        details: `Updated store information: ${validatedData.storeName}`
      }
    });

    return NextResponse.json({
      success: true,
      storeInfo
    });
  } catch (error) {
    console.error("Error updating store info:", error);
    return NextResponse.json(
      { error: "Failed to update store information", message: (error as Error).message },
      { status: 500 }
    );
  }
}
