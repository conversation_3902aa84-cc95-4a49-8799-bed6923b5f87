import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { QualityMonitoringService } from '@/lib/quality-monitoring-service';

// GET /api/quality-monitoring - Get quality monitoring status and run monitoring
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'run') {
      // Run quality monitoring
      console.log('Running quality monitoring...');
      const result = await QualityMonitoringService.runQualityMonitoring();
      
      return NextResponse.json({
        success: true,
        message: 'Quality monitoring completed successfully',
        result,
      });
    } else {
      // Get current thresholds and status
      const thresholds = await QualityMonitoringService.getQualityThresholds();
      
      return NextResponse.json({
        success: true,
        thresholds,
        status: 'ready',
        lastRun: null, // TODO: Implement last run tracking
      });
    }
  } catch (error) {
    console.error('Error in quality monitoring:', error);
    return NextResponse.json(
      { 
        error: 'Failed to process quality monitoring request',
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}

// POST /api/quality-monitoring - Trigger quality monitoring manually
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admins can trigger monitoring
    const canTriggerMonitoring = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!canTriggerMonitoring) {
      return NextResponse.json({ error: 'Insufficient permissions to trigger monitoring' }, { status: 403 });
    }

    console.log(`Quality monitoring triggered manually by ${auth.user.name} (${auth.user.role})`);
    
    // Run quality monitoring
    const result = await QualityMonitoringService.runQualityMonitoring();
    
    // Log the monitoring result
    console.log(`Quality monitoring completed: ${result.alertsGenerated} alerts, ${result.escalationsTriggered} escalations`);
    
    return NextResponse.json({
      success: true,
      message: 'Quality monitoring triggered successfully',
      triggeredBy: {
        id: auth.user.id,
        name: auth.user.name,
        role: auth.user.role,
      },
      triggeredAt: new Date(),
      result,
    });
  } catch (error) {
    console.error('Error triggering quality monitoring:', error);
    return NextResponse.json(
      { 
        error: 'Failed to trigger quality monitoring',
        message: (error as Error).message 
      },
      { status: 500 }
    );
  }
}
