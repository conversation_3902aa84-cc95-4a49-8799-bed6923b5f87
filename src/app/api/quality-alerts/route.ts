import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { QualityAlertSystem } from '@/lib/quality-alert-system';
import { prisma } from '@/lib/prisma';

// GET /api/quality-alerts - Get stored quality alerts
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'run_monitoring') {
      // Run quality monitoring and return results
      console.log('Running quality monitoring via API...');
      const result = await QualityAlertSystem.runQualityMonitoring();

      return NextResponse.json({
        success: true,
        message: 'Quality monitoring completed',
        result,
        triggeredBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        },
        triggeredAt: new Date(),
      });
    } else {
      // Get stored quality alerts from database
      const status = searchParams.get('status');
      const severity = searchParams.get('severity');
      const supplierId = searchParams.get('supplierId');
      const alertType = searchParams.get('alertType');
      const page = parseInt(searchParams.get('page') || '1');
      const limit = parseInt(searchParams.get('limit') || '50');
      const offset = (page - 1) * limit;

      // Build filter conditions
      const where: any = {};
      if (status) where.status = status;
      if (severity) where.severity = severity;
      if (supplierId) where.supplierId = supplierId;
      if (alertType) where.alertType = alertType;

      // Get alerts with related data
      const [alerts, totalCount] = await Promise.all([
        prisma.qualityAlert.findMany({
          where,
          include: {
            supplier: {
              select: { id: true, name: true }
            },
            product: {
              select: { id: true, name: true, sku: true }
            },
            batch: {
              select: { id: true, batchNumber: true }
            },
            acknowledger: {
              select: { id: true, name: true }
            },
            resolver: {
              select: { id: true, name: true }
            },
            dismisser: {
              select: { id: true, name: true }
            }
          },
          orderBy: [
            { severity: 'desc' },
            { triggeredAt: 'desc' }
          ],
          skip: offset,
          take: limit
        }),
        prisma.qualityAlert.count({ where })
      ]);

      return NextResponse.json({
        success: true,
        alerts,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / limit)
        },
        summary: {
          total: totalCount,
          active: await prisma.qualityAlert.count({ where: { status: 'ACTIVE' } }),
          acknowledged: await prisma.qualityAlert.count({ where: { status: 'ACKNOWLEDGED' } }),
          resolved: await prisma.qualityAlert.count({ where: { status: 'RESOLVED' } }),
          dismissed: await prisma.qualityAlert.count({ where: { status: 'DISMISSED' } })
        }
      });
    }
  } catch (error) {
    console.error('Error in quality alerts API:', error);
    return NextResponse.json(
      {
        error: 'Failed to process quality alerts request',
        message: (error as Error).message
      },
      { status: 500 }
    );
  }
}

// POST /api/quality-alerts - Trigger monitoring or acknowledge alerts
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const action = body.action;

    if (action === 'run_monitoring') {
      // Only admins can trigger monitoring
      const canTrigger = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
      if (!canTrigger) {
        return NextResponse.json({ error: 'Insufficient permissions to trigger monitoring' }, { status: 403 });
      }

      console.log(`Quality monitoring triggered by ${auth.user.name} (${auth.user.role})`);
      const result = await QualityAlertSystem.runQualityMonitoring();

      return NextResponse.json({
        success: true,
        message: 'Quality monitoring triggered successfully',
        result,
        triggeredBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        },
        triggeredAt: new Date(),
      });
    } else if (action === 'acknowledge') {
      // Acknowledge an alert
      const { alertId } = body;
      if (!alertId) {
        return NextResponse.json({ error: 'Alert ID is required' }, { status: 400 });
      }

      const updatedAlert = await prisma.qualityAlert.update({
        where: { id: alertId },
        data: {
          status: 'ACKNOWLEDGED',
          acknowledgedAt: new Date(),
          acknowledgedBy: auth.user.id
        },
        include: {
          supplier: { select: { id: true, name: true } },
          product: { select: { id: true, name: true, sku: true } },
          acknowledger: { select: { id: true, name: true } }
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Alert acknowledged successfully',
        alert: updatedAlert,
        acknowledgedBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        }
      });
    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in quality alerts POST:', error);
    return NextResponse.json(
      {
        error: 'Failed to process quality alerts request',
        message: (error as Error).message
      },
      { status: 500 }
    );
  }
}

// PATCH /api/quality-alerts - Resolve alerts
export async function PATCH(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { alertId, action, resolutionNotes } = body;

    if (!alertId) {
      return NextResponse.json({ error: 'Alert ID is required' }, { status: 400 });
    }

    if (action === 'resolve') {
      const updatedAlert = await prisma.qualityAlert.update({
        where: { id: alertId },
        data: {
          status: 'RESOLVED',
          resolvedAt: new Date(),
          resolvedBy: auth.user.id,
          metadata: resolutionNotes ? {
            resolutionNotes,
            resolvedBy: auth.user.name,
            resolvedAt: new Date()
          } : undefined
        },
        include: {
          supplier: { select: { id: true, name: true } },
          product: { select: { id: true, name: true, sku: true } },
          resolver: { select: { id: true, name: true } }
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Alert resolved successfully',
        alert: updatedAlert,
        resolvedBy: {
          id: auth.user.id,
          name: auth.user.name,
          role: auth.user.role,
        }
      });
    } else {
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in quality alerts PATCH:', error);
    return NextResponse.json(
      {
        error: 'Failed to resolve quality alert',
        message: (error as Error).message
      },
      { status: 500 }
    );
  }
}

// DELETE /api/quality-alerts - Dismiss alerts
export async function DELETE(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions - only admins can dismiss alerts
    const canDismiss = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!canDismiss) {
      return NextResponse.json({ error: 'Insufficient permissions to dismiss alerts' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const alertId = searchParams.get('alertId');

    if (!alertId) {
      return NextResponse.json({ error: 'Alert ID is required' }, { status: 400 });
    }

    const updatedAlert = await prisma.qualityAlert.update({
      where: { id: alertId },
      data: {
        status: 'DISMISSED',
        dismissedAt: new Date(),
        dismissedBy: auth.user.id
      },
      include: {
        supplier: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, sku: true } },
        dismisser: { select: { id: true, name: true } }
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Alert dismissed successfully',
      alert: updatedAlert,
      dismissedBy: {
        id: auth.user.id,
        name: auth.user.name,
        role: auth.user.role,
      }
    });
  } catch (error) {
    console.error('Error in quality alerts DELETE:', error);
    return NextResponse.json(
      {
        error: 'Failed to dismiss quality alert',
        message: (error as Error).message
      },
      { status: 500 }
    );
  }
}
