import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { verifyAuthToken } from "@/lib/auth-utils";
import { notifyLowStock } from "@/lib/notifications";
import { EVENT_TYPES } from "@/lib/events/event-system";

/**
 * Debug endpoint to test the complete notification workflow
 * This endpoint will:
 * 1. Check notification templates in database
 * 2. Test the complete notification flow
 * 3. Verify template rendering
 * 4. Show detailed logs
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.success || !auth.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`🔍 Debug notification workflow started by ${auth.user.name}`);

    // Step 1: Check notification templates
    console.log('\n📋 Step 1: Checking notification templates...');
    const templates = await prisma.notificationTemplate.findMany({
      where: {
        eventType: {
          in: ['inventory.low_stock', 'inventory.out_of_stock']
        }
      }
    });

    console.log(`Found ${templates.length} inventory notification templates:`);
    templates.forEach(template => {
      console.log(`  - ${template.name} (${template.eventType})`);
      console.log(`    Title: "${template.titleTemplate}"`);
      console.log(`    Message: "${template.messageTemplate}"`);
      console.log(`    Variables: ${JSON.stringify(template.variables)}`);
    });

    // Step 2: Get a test product
    console.log('\n🛍️ Step 2: Finding test product...');
    const testProduct = await prisma.product.findFirst({
      include: {
        storeStock: true,
        category: true
      }
    });

    if (!testProduct) {
      return NextResponse.json({ 
        error: "No products found for testing",
        templates: templates.map(t => ({
          eventType: t.eventType,
          name: t.name,
          titleTemplate: t.titleTemplate,
          messageTemplate: t.messageTemplate,
          variables: t.variables
        }))
      }, { status: 400 });
    }

    console.log(`Test product: ${testProduct.name} (ID: ${testProduct.id})`);
    console.log(`Current stock: ${testProduct.storeStock?.quantity || 0}`);
    console.log(`Min threshold: ${testProduct.minThreshold || 0}`);

    // Step 3: Test notification creation
    console.log('\n🔔 Step 3: Testing notification creation...');
    const testCurrentStock = 5;
    const testMinThreshold = 10;

    console.log(`Triggering low stock notification with:`);
    console.log(`  - Product: ${testProduct.name}`);
    console.log(`  - Current Stock: ${testCurrentStock}`);
    console.log(`  - Min Threshold: ${testMinThreshold}`);

    // Count notifications before
    const notificationsBefore = await prisma.notification.count();
    console.log(`Notifications before: ${notificationsBefore}`);

    // Trigger the notification
    await notifyLowStock(
      testProduct.id,
      testProduct.name,
      testCurrentStock,
      testMinThreshold,
      {
        sku: testProduct.sku,
        category: testProduct.category?.name,
        testRun: true,
        debugWorkflow: true
      }
    );

    // Wait a moment for async processing
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Count notifications after
    const notificationsAfter = await prisma.notification.count();
    console.log(`Notifications after: ${notificationsAfter}`);

    // Step 4: Check created notifications
    console.log('\n📬 Step 4: Checking created notifications...');
    const recentNotifications = await prisma.notification.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 5000) // Last 5 seconds
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`Found ${recentNotifications.length} recent notifications:`);
    recentNotifications.forEach(notification => {
      console.log(`  - ${notification.title}`);
      console.log(`    Message: "${notification.message}"`);
      console.log(`    Type: ${notification.type}`);
      console.log(`    Event Type: ${notification.eventType}`);
      console.log(`    Metadata: ${JSON.stringify(notification.metadata)}`);
    });

    // Step 5: Check notification events
    console.log('\n📊 Step 5: Checking notification events...');
    const recentEvents = await prisma.notificationEvent.findMany({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 5000) // Last 5 seconds
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`Found ${recentEvents.length} recent events:`);
    recentEvents.forEach(event => {
      console.log(`  - ${event.eventType} (${event.eventId})`);
      console.log(`    Source: ${event.sourceType}/${event.sourceId}`);
      console.log(`    Processed: ${event.processed}`);
      console.log(`    Payload: ${JSON.stringify(event.payload)}`);
    });

    return NextResponse.json({
      success: true,
      debug: {
        templates: templates.map(t => ({
          eventType: t.eventType,
          name: t.name,
          titleTemplate: t.titleTemplate,
          messageTemplate: t.messageTemplate,
          variables: t.variables
        })),
        testProduct: {
          id: testProduct.id,
          name: testProduct.name,
          sku: testProduct.sku,
          currentStock: testProduct.storeStock?.quantity || 0,
          minThreshold: testProduct.minThreshold || 0,
          category: testProduct.category?.name
        },
        testParameters: {
          currentStock: testCurrentStock,
          minThreshold: testMinThreshold
        },
        notificationCounts: {
          before: notificationsBefore,
          after: notificationsAfter,
          created: notificationsAfter - notificationsBefore
        },
        recentNotifications: recentNotifications.map(n => ({
          id: n.id,
          title: n.title,
          message: n.message,
          type: n.type,
          eventType: n.eventType,
          createdAt: n.createdAt,
          metadata: n.metadata
        })),
        recentEvents: recentEvents.map(e => ({
          id: e.id,
          eventType: e.eventType,
          eventId: e.eventId,
          sourceType: e.sourceType,
          sourceId: e.sourceId,
          processed: e.processed,
          createdAt: e.createdAt,
          payload: e.payload
        }))
      }
    });

  } catch (error) {
    console.error('❌ Debug workflow error:', error);
    return NextResponse.json({
      error: "Debug workflow failed",
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
