import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access conversations.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// GET /api/conversations - Get all conversations for the current user
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get conversations where the user is a participant
    const conversations = await prisma.conversation.findMany({
      where: {
        participants: {
          some: {
            userId: auth.user.id,
            leftAt: null, // Only include active participants
          },
        },
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
        },
        messages: {
          orderBy: {
            createdAt: "desc",
          },
          take: 1, // Get only the latest message
          include: {
            sender: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        starredBy: {
          where: {
            userId: auth.user.id,
          },
          select: {
            id: true,
          },
        },
      },
      orderBy: {
        updatedAt: "desc", // Most recent conversations first
      },
    });

    // Format the response
    const formattedConversations = conversations.map((conversation) => {
      const otherParticipants = conversation.participants
        .filter((p) => p.userId !== auth.user.id)
        .map((p) => p.user);

      return {
        id: conversation.id,
        title: conversation.title || otherParticipants.map((p) => p.name).join(", "),
        participants: conversation.participants.map((p) => p.user),
        latestMessage: conversation.messages[0] || null,
        isStarred: conversation.starredBy.length > 0,
        updatedAt: conversation.updatedAt,
        createdAt: conversation.createdAt,
      };
    });

    return NextResponse.json({ conversations: formattedConversations });
  } catch (error) {
    console.error("Error getting conversations:", error);
    return NextResponse.json(
      { error: "Failed to get conversations", message: error.message },
      { status: 500 }
    );
  }
}

// POST /api/conversations - Create a new conversation
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.participantIds || !Array.isArray(body.participantIds) || body.participantIds.length === 0) {
      return NextResponse.json(
        { error: "At least one participant ID is required" },
        { status: 400 }
      );
    }

    // Make sure the current user is included in the participants
    const participantIds = [...new Set([...body.participantIds, auth.user.id])];

    // For one-on-one conversations, check if a conversation already exists
    let conversation;

    // Check if a conversation already exists with the same participants
    // First, find all conversations where the current user is a participant
    const userConversations = await prisma.conversation.findMany({
      where: {
        participants: {
          some: {
            userId: auth.user.id
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
        },
      },
    });

    // Then filter to find conversations with exactly the same participants
    const matchingConversations = userConversations.filter(conv => {
      // Get all participant IDs in this conversation
      const convParticipantIds = conv.participants.map(p => p.userId);

      // Check if the conversation has exactly the same participants
      return (
        // Same number of participants
        convParticipantIds.length === participantIds.length &&
        // All requested participants are in this conversation
        participantIds.every(id => convParticipantIds.includes(id)) &&
        // All conversation participants are in the requested participants
        convParticipantIds.every(id => participantIds.includes(id))
      );
    });

    // If a matching conversation is found, use it
    if (matchingConversations.length > 0) {
      conversation = matchingConversations[0];
    }

    // If no existing conversation was found, create a new one
    if (!conversation) {
      conversation = await prisma.conversation.create({
        data: {
          title: body.title || null,
          participants: {
            create: participantIds.map((userId) => ({
              userId,
            })),
          },
        },
        include: {
          participants: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
          },
        },
      });
    }

    // Format the response
    const formattedConversation = {
      id: conversation.id,
      title: conversation.title || conversation.participants
        .filter((p) => p.userId !== auth.user.id)
        .map((p) => p.user.name)
        .join(", "),
      participants: conversation.participants.map((p) => p.user),
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,
    };

    return NextResponse.json({ conversation: formattedConversation }, { status: 201 });
  } catch (error) {
    console.error("Error creating conversation:", error);
    return NextResponse.json(
      { error: "Failed to create conversation", message: error.message },
      { status: 500 }
    );
  }
}
