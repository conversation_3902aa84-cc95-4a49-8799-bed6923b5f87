import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access messages.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// POST /api/conversations/[id]/messages - Send a message in a conversation
export async function POST(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user is a participant in this conversation
    const participant = await prisma.participant.findUnique({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    if (!participant) {
      return NextResponse.json(
        { error: "You are not a participant in this conversation" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate required fields
    if (!body.content || typeof body.content !== "string" || body.content.trim() === "") {
      return NextResponse.json(
        { error: "Message content is required" },
        { status: 400 }
      );
    }

    // Get other participants in the conversation
    const otherParticipants = await prisma.participant.findMany({
      where: {
        conversationId: id,
        userId: {
          not: auth.user.id,
        },
        leftAt: null, // Only active participants
      },
      select: {
        userId: true,
      },
    });

    // Create message
    const message = await prisma.message.create({
      data: {
        conversationId: id,
        senderId: auth.user.id,
        content: body.content.trim(),
      },
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    // Update conversation's updatedAt timestamp
    await prisma.conversation.update({
      where: {
        id: id,
      },
      data: {
        updatedAt: new Date(),
      },
    });

    // Create notifications for other participants
    const notificationPromises = otherParticipants.map((participant) => {
      return prisma.notification.create({
        data: {
          userId: participant.userId,
          title: `New message from ${auth.user.name}`,
          message: body.content.length > 50 ? `${body.content.substring(0, 50)}...` : body.content,
          type: "MESSAGE",
        },
      });
    });

    await Promise.all(notificationPromises);

    return NextResponse.json({ message }, { status: 201 });
  } catch (error) {
    console.error("Error sending message:", error);
    return NextResponse.json(
      { error: "Failed to send message", message: error.message },
      { status: 500 }
    );
  }
}
