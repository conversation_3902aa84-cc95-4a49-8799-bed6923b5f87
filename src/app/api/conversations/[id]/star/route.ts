import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to star conversations.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// POST /api/conversations/[id]/star - Star a conversation
export async function POST(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user is a participant in this conversation
    const participant = await prisma.participant.findUnique({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    if (!participant) {
      return NextResponse.json(
        { error: "You are not a participant in this conversation" },
        { status: 403 }
      );
    }

    // Check if conversation is already starred
    const existingStar = await prisma.starredConversation.findUnique({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    if (existingStar) {
      return NextResponse.json(
        { error: "Conversation is already starred" },
        { status: 400 }
      );
    }

    // Star the conversation
    const starredConversation = await prisma.starredConversation.create({
      data: {
        userId: auth.user.id,
        conversationId: id,
      },
    });

    // Create activity log
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "STAR_CONVERSATION",
        details: `Starred conversation ${id}`,
      },
    });

    return NextResponse.json({ success: true, starredConversation });
  } catch (error) {
    console.error("Error starring conversation:", error);
    return NextResponse.json(
      { error: "Failed to star conversation", message: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/conversations/[id]/star - Unstar a conversation
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if conversation is starred
    const existingStar = await prisma.starredConversation.findUnique({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    if (!existingStar) {
      return NextResponse.json(
        { error: "Conversation is not starred" },
        { status: 400 }
      );
    }

    // Unstar the conversation
    await prisma.starredConversation.delete({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    // Create activity log
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UNSTAR_CONVERSATION",
        details: `Unstarred conversation ${id}`,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error unstarring conversation:", error);
    return NextResponse.json(
      { error: "Failed to unstar conversation", message: error.message },
      { status: 500 }
    );
  }
}
