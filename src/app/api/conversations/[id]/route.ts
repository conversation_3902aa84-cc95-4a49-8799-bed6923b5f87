import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access conversations.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// GET /api/conversations/[id] - Get a specific conversation with messages
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50", 10);
    const page = parseInt(searchParams.get("page") || "1", 10);
    const skip = (page - 1) * limit;

    // Check if user is a participant in this conversation
    const participant = await prisma.participant.findUnique({
      where: {
        userId_conversationId: {
          userId: auth.user.id,
          conversationId: id,
        },
      },
    });

    if (!participant) {
      return NextResponse.json(
        { error: "You are not a participant in this conversation" },
        { status: 403 }
      );
    }

    // Get conversation with participants and messages
    const conversation = await prisma.conversation.findUnique({
      where: {
        id: id,
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
        },
        starredBy: {
          where: {
            userId: auth.user.id,
          },
          select: {
            id: true,
          },
        },
      },
    });

    if (!conversation) {
      return NextResponse.json(
        { error: "Conversation not found" },
        { status: 404 }
      );
    }

    // Get total message count for pagination
    const totalMessages = await prisma.message.count({
      where: {
        conversationId: id,
      },
    });

    // Get messages with pagination
    const messages = await prisma.message.findMany({
      where: {
        conversationId: id,
      },
      orderBy: {
        createdAt: "desc", // Most recent messages first
      },
      skip,
      take: limit,
      include: {
        sender: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
    });

    // Mark unread messages as read
    await prisma.message.updateMany({
      where: {
        conversationId: id,
        receiverId: auth.user.id,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    // Format the response
    const formattedConversation = {
      id: conversation.id,
      title: conversation.title || conversation.participants
        .filter((p) => p.userId !== auth.user.id)
        .map((p) => p.user.name)
        .join(", "),
      participants: conversation.participants.map((p) => p.user),
      messages: messages.reverse(), // Reverse to get chronological order
      isStarred: conversation.starredBy.length > 0,
      pagination: {
        total: totalMessages,
        page,
        limit,
        pages: Math.ceil(totalMessages / limit),
      },
      createdAt: conversation.createdAt,
      updatedAt: conversation.updatedAt,

    };

    return NextResponse.json({ conversation: formattedConversation });
  } catch (error) {
    console.error("Error getting conversation:", error);
    return NextResponse.json(
      { error: "Failed to get conversation", message: error.message },
      { status: 500 }
    );
  }
}
