import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { notifyLowStock, notifyOutOfStock } from "@/lib/notifications";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Test inventory notification endpoint called');

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      console.log('❌ Test endpoint: Authentication failed');
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`👤 Test endpoint: Authenticated as ${auth.user.name} (${auth.user.role})`);

    const { productId, testType, currentStock, minThreshold } = await request.json();
    console.log(`📋 Test endpoint: Received request for product ${productId}, test type: ${testType}`);

    // Verify the product exists
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: { 
        storeStock: true,
        category: true,
        unit: true
      }
    });

    if (!product) {
      console.log(`❌ Test endpoint: Product ${productId} not found`);
      return NextResponse.json({ 
        error: "Product not found",
        productId 
      }, { status: 404 });
    }

    console.log(`✅ Test endpoint: Found product ${product.name} (${product.sku})`);

    // Trigger the appropriate notification based on test type
    console.log(`🔔 Test endpoint: Triggering ${testType} notification for product ${productId}`);
    
    const testMetadata = {
      sku: product.sku,
      category: product.category?.name,
      unit: product.unit?.name,
      currentStoreStock: product.storeStock?.quantity || 0,
      testMode: true,
      triggeredBy: auth.user.name,
      triggeredAt: new Date().toISOString(),
    };

    if (testType === 'low_stock') {
      const stockLevel = currentStock !== undefined ? currentStock : (product.storeStock?.quantity || 0);
      const threshold = minThreshold !== undefined ? minThreshold : (product.minThreshold || 10);
      
      await notifyLowStock(product.id, product.name, stockLevel, threshold, testMetadata);
      console.log(`✅ Test endpoint: Low stock notification triggered for ${product.name}`);
      
    } else if (testType === 'out_of_stock') {
      await notifyOutOfStock(product.id, product.name, testMetadata);
      console.log(`✅ Test endpoint: Out of stock notification triggered for ${product.name}`);
      
    } else {
      return NextResponse.json({ 
        error: "Invalid test type. Use 'low_stock' or 'out_of_stock'" 
      }, { status: 400 });
    }

    // Check if notifications were created
    const recentNotifications = await prisma.notification.findMany({
      where: {
        eventType: testType === 'low_stock' ? 'inventory.low_stock' : 'inventory.out_of_stock',
        createdAt: {
          gte: new Date(Date.now() - 60000) // Last minute
        }
      },
      include: {
        user: { select: { name: true, role: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`📊 Test endpoint: Found ${recentNotifications.length} recent notifications`);

    return NextResponse.json({ 
      success: true, 
      message: `Test ${testType} notification triggered successfully`,
      details: {
        productId,
        productName: product.name,
        testType,
        currentStock: currentStock !== undefined ? currentStock : product.storeStock?.quantity,
        minThreshold: minThreshold !== undefined ? minThreshold : product.minThreshold,
        triggeredBy: auth.user.name,
        recentNotifications: recentNotifications.map(n => ({
          id: n.id,
          title: n.title,
          user: n.user.name,
          userRole: n.user.role,
          deliveryMethods: n.deliveryMethods,
          createdAt: n.createdAt
        }))
      }
    });

  } catch (error) {
    console.error("❌ Test inventory notification endpoint error:", error);
    console.error("Error stack:", error.stack);
    
    return NextResponse.json({ 
      error: "Test failed", 
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

// GET endpoint to check system status and get sample products
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check system components
    const [templates, preferences, events, notifications, products] = await Promise.all([
      prisma.notificationTemplate.count(),
      prisma.notificationPreference.count(),
      prisma.notificationEvent.count(),
      prisma.notification.count(),
      prisma.product.count()
    ]);

    // Get sample products for testing
    const sampleProducts = await prisma.product.findMany({
      include: { 
        storeStock: true,
        category: true,
        unit: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    // Get products with low stock for testing
    const lowStockProducts = await prisma.product.findMany({
      where: {
        storeStock: {
          quantity: {
            lte: prisma.product.fields.minThreshold
          }
        }
      },
      include: { 
        storeStock: true,
        category: true
      },
      take: 3
    });

    // Get the specific low stock template to debug template variable issues
    const lowStockTemplate = await prisma.notificationTemplate.findUnique({
      where: { eventType: 'inventory.low_stock' }
    });

    return NextResponse.json({
      systemStatus: {
        templates,
        preferences,
        events,
        notifications,
        products
      },
      lowStockTemplate: lowStockTemplate ? {
        eventType: lowStockTemplate.eventType,
        name: lowStockTemplate.name,
        titleTemplate: lowStockTemplate.titleTemplate,
        messageTemplate: lowStockTemplate.messageTemplate,
        variables: lowStockTemplate.variables
      } : null,
      sampleProducts: sampleProducts.map(p => ({
        id: p.id,
        name: p.name,
        sku: p.sku,
        currentStock: p.storeStock?.quantity || 0,
        minThreshold: p.minThreshold || 0,
        category: p.category?.name
      })),
      lowStockProducts: lowStockProducts.map(p => ({
        id: p.id,
        name: p.name,
        sku: p.sku,
        currentStock: p.storeStock?.quantity || 0,
        minThreshold: p.minThreshold || 0,
        category: p.category?.name
      })),
      testInstructions: {
        message: "Use POST to this endpoint with { productId, testType, currentStock?, minThreshold? } to test inventory notifications",
        testTypes: ["low_stock", "out_of_stock"],
        examples: [
          {
            productId: sampleProducts[0]?.id || "your-product-id",
            testType: "low_stock",
            currentStock: 5,
            minThreshold: 10
          },
          {
            productId: sampleProducts[0]?.id || "your-product-id",
            testType: "out_of_stock"
          }
        ]
      }
    });

  } catch (error) {
    console.error("Test inventory status endpoint error:", error);
    return NextResponse.json({ 
      error: "Status check failed", 
      details: error.message 
    }, { status: 500 });
  }
}
