import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/lib/prisma";

// GET /api/notifications/status - Check notification system status
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking notification system status...');

    // Check authentication (but allow any authenticated user to check status)
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { 
          error: auth.error || "Authentication failed",
          details: "Please log in to check notification system status"
        },
        { status: auth.status || 401 }
      );
    }

    console.log(`👤 User authenticated: ${auth.user.id} (${auth.user.role})`);

    const status = {
      authenticated: true,
      user: {
        id: auth.user.id,
        role: auth.user.role,
        canInitialize: auth.user.role === 'SUPER_ADMIN'
      },
      database: {
        connected: false,
        tablesExist: false,
        templateCount: 0,
        preferenceCount: 0,
        eventCount: 0
      },
      system: {
        initialized: false,
        hasTemplates: false,
        hasPreferences: false
      }
    };

    // Test database connection
    try {
      await prisma.$connect();
      status.database.connected = true;
      console.log('✅ Database connection successful');
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      return NextResponse.json({
        ...status,
        error: "Database connection failed",
        details: dbError.message
      });
    }

    // Check if notification tables exist and get counts
    try {
      const preferenceCount = await prisma.notificationPreference.count();
      const eventCount = await prisma.notificationEvent.count();

      status.database.tablesExist = true;
      status.database.preferenceCount = preferenceCount;
      status.database.eventCount = eventCount;

      // Templates are now hardcoded in the notification engine, so we consider them as always available
      const templateCount = 8; // Number of hardcoded templates in the system (5 original + 3 new financial)
      status.database.templateCount = templateCount;
      status.system.hasTemplates = true; // Templates are hardcoded
      status.system.hasPreferences = preferenceCount > 0;
      status.system.initialized = preferenceCount > 0; // Only need preferences to be initialized

      console.log(`📊 Templates: ${templateCount} (hardcoded), Preferences: ${preferenceCount}, Events: ${eventCount}`);

    } catch (tableError) {
      console.error('❌ Error accessing notification tables:', tableError);
      status.database.tablesExist = false;

      return NextResponse.json({
        ...status,
        error: "Notification tables not found",
        details: "The notification system database tables don't exist. Please run the database migration.",
        suggestion: "Run: npx prisma migrate dev --name add-notification-system",
        tableError: tableError.message
      });
    }

    // Get sample data if available
    let sampleData = {};
    if (status.system.hasTemplates) {
      // Templates are hardcoded, so provide sample data directly
      sampleData.templates = [
        { eventType: 'po.status.changed', name: 'Purchase Order Status Changed', defaultPriority: 'NORMAL' },
        { eventType: 'po.approved', name: 'Purchase Order Approved', defaultPriority: 'HIGH' },
        { eventType: 'inventory.low_stock', name: 'Low Stock Alert', defaultPriority: 'HIGH' },
        { eventType: 'cash_audit.discrepancy_detected', name: 'Cash Reconciliation Discrepancy Alert', defaultPriority: 'HIGH' },
        { eventType: 'invoice.approved', name: 'Invoice Approval Notification', defaultPriority: 'NORMAL' }
      ];
    }

    if (status.system.hasPreferences) {
      try {
        const samplePreferences = await prisma.notificationPreference.findMany({
          take: 3,
          select: {
            eventType: true,
            enabled: true,
            deliveryMethods: true,
            user: {
              select: {
                name: true,
                role: true
              }
            }
          }
        });
        sampleData.preferences = samplePreferences;
      } catch (error) {
        console.warn('Could not fetch sample preferences:', error.message);
      }
    }

    console.log('✅ Notification system status check completed');

    return NextResponse.json({
      success: true,
      status,
      sampleData,
      recommendations: getRecommendations(status)
    });

  } catch (error) {
    console.error("❌ Error checking notification system status:", error);
    return NextResponse.json(
      { 
        error: "Failed to check notification system status", 
        details: error.message,
        stack: error.stack
      },
      { status: 500 }
    );
  } finally {
    try {
      await prisma.$disconnect();
    } catch (disconnectError) {
      console.error('❌ Error disconnecting from database:', disconnectError);
    }
  }
}

function getRecommendations(status: any): string[] {
  const recommendations = [];

  if (!status.database.connected) {
    recommendations.push("Fix database connection issues");
  }

  if (!status.database.tablesExist) {
    recommendations.push("Run database migration: npx prisma migrate dev --name add-notification-system");
  }

  if (!status.system.hasTemplates) {
    recommendations.push("Initialize notification templates using the initialization button");
  }

  if (!status.system.hasPreferences) {
    recommendations.push("Create user notification preferences using the initialization button");
  }

  if (!status.user.canInitialize && !status.system.initialized) {
    recommendations.push("Contact a SUPER_ADMIN to initialize the notification system");
  }

  if (status.system.initialized) {
    recommendations.push("Notification system is ready to use!");
  }

  return recommendations;
}
