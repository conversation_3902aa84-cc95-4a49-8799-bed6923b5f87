import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access notifications.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );
    
    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// POST /api/notifications/clear-all - Clear all notifications for the current user
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Delete all notifications for the current user
    const result = await prisma.notification.deleteMany({
      where: {
        userId: auth.user?.id,
      },
    });

    return NextResponse.json({
      success: true,
      count: result.count,
      message: `Cleared ${result.count} notifications`,
    });
  } catch (error) {
    console.error("Error clearing all notifications:", error);
    return NextResponse.json(
      { error: "Failed to clear all notifications", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
