import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { prisma } from "@/lib/prisma";

// POST /api/notifications/init - Initialize notification system
export async function POST(request: NextRequest) {
  try {
    console.log('🚀 Starting notification system initialization...');

    // Check authentication
    console.log('🔐 Verifying authentication...');
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      console.log('❌ Authentication failed:', auth.error);
      return NextResponse.json(
        {
          error: auth.error || "Authentication failed",
          details: "Please log in and try again"
        },
        { status: auth.status || 401 }
      );
    }

    // Only allow super admins to initialize the system
    console.log(`👤 User authenticated: ${auth.user.id} (${auth.user.role})`);
    if (auth.user.role !== 'SUPER_ADMIN') {
      console.log('❌ Insufficient permissions - user role:', auth.user.role);
      return NextResponse.json(
        {
          error: "Insufficient permissions",
          details: "Only SUPER_ADMIN users can initialize the notification system",
          userRole: auth.user.role
        },
        { status: 403 }
      );
    }

    console.log('✅ Authentication and permissions verified');

    // Test database connection and check if tables exist
    console.log('🗄️ Testing database connection...');
    try {
      await prisma.$connect();
      console.log('✅ Database connection successful');
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError);
      return NextResponse.json(
        {
          error: "Database connection failed",
          details: "Unable to connect to the database. Please check your database configuration.",
          dbError: dbError.message
        },
        { status: 500 }
      );
    }

    // Check if notification tables exist
    console.log('📋 Checking notification tables...');
    try {
      const existingPreferences = await prisma.notificationPreference.count();
      console.log(`📊 Found ${existingPreferences} existing preferences`);

      const existingEvents = await prisma.notificationEvent.count();
      console.log(`📊 Found ${existingEvents} existing events`);

    } catch (tableError) {
      console.error('❌ Error accessing notification tables:', tableError);
      return NextResponse.json(
        {
          error: "Notification tables not found",
          details: "The notification system database tables don't exist. Please run the database migration first.",
          suggestion: "Run: npx prisma migrate dev --name add-notification-system",
          tableError: tableError.message
        },
        { status: 500 }
      );
    }

    // Create default templates if none exist
    const defaultTemplates = [
      {
        eventType: 'po.status.changed',
        name: 'Purchase Order Status Changed',
        description: 'Notification when a purchase order status changes',
        titleTemplate: 'Purchase Order Status Updated',
        messageTemplate: 'Purchase Order #{{poId}} status changed from {{fromStatus}} to {{toStatus}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
        variables: {
          poId: 'Purchase Order ID',
          fromStatus: 'Previous status',
          toStatus: 'New status'
        }
      },
      {
        eventType: 'po.approved',
        name: 'Purchase Order Approved',
        description: 'Notification when a purchase order is approved',
        titleTemplate: 'Purchase Order Approved',
        messageTemplate: 'Purchase Order #{{poId}} has been approved by {{approvedBy}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'HIGH',
        variables: {
          poId: 'Purchase Order ID',
          approvedBy: 'User who approved the order'
        }
      },
      {
        eventType: 'po.rejected',
        name: 'Purchase Order Rejected',
        description: 'Notification when a purchase order is rejected',
        titleTemplate: 'Purchase Order Rejected',
        messageTemplate: 'Purchase Order #{{poId}} has been rejected by {{rejectedBy}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'HIGH',
        variables: {
          poId: 'Purchase Order ID',
          rejectedBy: 'User who rejected the order'
        }
      },
      {
        eventType: 'inventory.low_stock',
        name: 'Low Stock Alert',
        description: 'Notification when product stock is low',
        titleTemplate: 'Low Stock Alert',
        messageTemplate: 'Product {{productName}} is running low ({{currentStock}} remaining, minimum: {{minThreshold}})',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
        variables: {
          productName: 'Product name',
          currentStock: 'Current stock level',
          minThreshold: 'Minimum stock threshold'
        }
      },
      {
        eventType: 'inventory.out_of_stock',
        name: 'Out of Stock Alert',
        description: 'Notification when product is out of stock',
        titleTemplate: 'Out of Stock Alert',
        messageTemplate: 'Product {{productName}} is now out of stock',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'CRITICAL',
        variables: {
          productName: 'Product name'
        }
      },
      {
        eventType: 'cash_audit.discrepancy_detected',
        name: 'Cash Reconciliation Discrepancy Alert',
        description: 'Notification when a cash reconciliation discrepancy is detected',
        titleTemplate: 'Cash Reconciliation Discrepancy Alert',
        messageTemplate: 'Cash discrepancy of {{discrepancyAmountFormatted}} detected by {{cashierName}} ({{cashierRole}}) on {{businessDate}} at {{drawerLocation}}. Category: {{discrepancyCategory}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'HIGH',
        variables: {
          discrepancyAmountFormatted: 'Discrepancy amount in IDR format',
          cashierName: 'Name of the cashier',
          cashierRole: 'Role of the cashier',
          businessDate: 'Business date',
          drawerLocation: 'Cash drawer location',
          discrepancyCategory: 'Category of discrepancy'
        }
      },
      {
        eventType: 'invoice.approved',
        name: 'Invoice Approval Notification',
        description: 'Notification when an invoice is approved',
        titleTemplate: 'Invoice Approved',
        messageTemplate: 'Invoice {{invoiceNumber}} from {{supplierName}} for {{totalAmountFormatted}} has been approved by {{approverName}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
        variables: {
          invoiceNumber: 'Invoice number',
          supplierName: 'Supplier name',
          totalAmountFormatted: 'Total amount in IDR format',
          approverName: 'Name of the approver'
        }
      },
      {
        eventType: 'invoice.payment_made',
        name: 'Invoice Payment Confirmation',
        description: 'Notification when a payment is made against an invoice',
        titleTemplate: 'Invoice Payment Confirmation',
        messageTemplate: 'Payment of {{paymentAmountFormatted}} made for Invoice {{invoiceNumber}} via {{paymentMethod}}{{remainingBalance ? ". Remaining balance: " + remainingBalanceFormatted : ""}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
        variables: {
          paymentAmountFormatted: 'Payment amount in IDR format',
          invoiceNumber: 'Invoice number',
          paymentMethod: 'Payment method used',
          remainingBalanceFormatted: 'Remaining balance in IDR format (if applicable)'
        }
      }
    ];

    console.log('📝 Templates are now hardcoded in the notification engine...');
    let templatesCreated = defaultTemplates.length; // Count of hardcoded templates (now 8 total)
    let templateErrors = [];

    // Create default preferences for existing users
    console.log('👥 Creating default preferences for existing users...');

    let users = [];
    try {
      users = await prisma.user.findMany({
        select: { id: true, name: true, email: true, role: true }
      });
      console.log(`📊 Found ${users.length} users`);
    } catch (userError) {
      console.error('❌ Failed to fetch users:', userError);
      return NextResponse.json(
        {
          error: "Failed to fetch users",
          details: "Unable to retrieve users from database to create preferences",
          userError: userError.message
        },
        { status: 500 }
      );
    }

    let preferencesCreated = 0;
    let preferenceErrors = [];

    for (const user of users) {
      console.log(`👤 Processing preferences for user: ${user.name} (${user.role})`);

      for (const template of defaultTemplates) {
        try {
          const result = await prisma.notificationPreference.upsert({
            where: {
              userId_eventType: {
                userId: user.id,
                eventType: template.eventType
              }
            },
            update: {
              enabled: true,
              deliveryMethods: template.defaultDeliveryMethods,
              frequency: 'IMMEDIATE'
            },
            create: {
              userId: user.id,
              eventType: template.eventType,
              enabled: true,
              deliveryMethods: template.defaultDeliveryMethods,
              frequency: 'IMMEDIATE'
            }
          });

          preferencesCreated++;
          console.log(`✅ Created preference: ${user.name} -> ${template.eventType}`);

        } catch (error) {
          console.error(`❌ Failed to create preference for user ${user.name}, template ${template.eventType}:`, error);
          preferenceErrors.push({
            userName: user.name,
            userId: user.id,
            eventType: template.eventType,
            error: error.message
          });
        }
      }
    }

    if (preferenceErrors.length > 0) {
      console.error('❌ Preference creation errors:', preferenceErrors);
    }

    console.log('🎉 Notification system initialization completed!');
    console.log(`📊 Final stats: ${templatesCreated} templates, ${preferencesCreated} preferences for ${users.length} users`);

    const hasErrors = templateErrors.length > 0 || preferenceErrors.length > 0;

    return NextResponse.json({
      success: true,
      message: "Notification system initialized successfully",
      stats: {
        templatesCreated,
        preferencesCreated,
        usersProcessed: users.length,
        templateErrors: templateErrors.length,
        preferenceErrors: preferenceErrors.length
      },
      ...(hasErrors && {
        warnings: {
          templateErrors,
          preferenceErrors
        }
      })
    });

  } catch (error) {
    console.error("❌ Critical error during notification system initialization:", error);
    console.error("Error stack:", error.stack);

    return NextResponse.json(
      {
        error: "Failed to initialize notification system",
        details: error.message,
        errorType: error.constructor.name,
        stack: error.stack
      },
      { status: 500 }
    );
  } finally {
    // Ensure database connection is closed
    try {
      await prisma.$disconnect();
      console.log('🔌 Database connection closed');
    } catch (disconnectError) {
      console.error('❌ Error disconnecting from database:', disconnectError);
    }
  }
}
