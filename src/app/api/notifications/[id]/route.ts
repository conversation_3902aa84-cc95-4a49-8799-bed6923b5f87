import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/auth";
import { jwtVerify } from "jose";

// Helper function to verify the authentication token
async function verifyAuthToken(request: NextRequest) {
  // Get the session token from cookies
  const token = request.cookies.get("session-token");

  // Check if token exists
  if (!token) {
    return {
      authenticated: false,
      error: "Unauthorized. You must be logged in to access notifications.",
      status: 401
    };
  }

  // Verify the token
  try {
    const { payload } = await jwtVerify(
      token.value,
      new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
    );

    return {
      authenticated: true,
      user: {
        id: payload.id as string,
        name: payload.name as string,
        email: payload.email as string,
        role: payload.role as string,
      }
    };
  } catch (error) {
    console.error("Token verification failed:", error);
    return {
      authenticated: false,
      error: "Unauthorized. Invalid authentication token.",
      status: 401
    };
  }
}

// GET /api/notifications/[id] - Get a specific notification
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get notification
    const notification = await prisma.notification.findUnique({
      where: {
        id: id,
      },
    });

    // Check if notification exists
    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to view this notification
    if (notification.userId !== auth.user.id && auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to view this notification" },
        { status: 403 }
      );
    }

    return NextResponse.json({ notification });
  } catch (error) {
    console.error("Error getting notification:", error);
    return NextResponse.json(
      { error: "Failed to get notification", message: error.message },
      { status: 500 }
    );
  }
}

// PATCH /api/notifications/[id] - Update a notification (mark as read)
export async function PATCH(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get notification
    const notification = await prisma.notification.findUnique({
      where: {
        id: id,
      },
    });

    // Check if notification exists
    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to update this notification
    if (notification.userId !== auth.user.id && auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to update this notification" },
        { status: 403 }
      );
    }

    // Get request body
    const body = await request.json();

    // Update notification
    const updatedNotification = await prisma.notification.update({
      where: {
        id: id,
      },
      data: {
        isRead: body.isRead !== undefined ? body.isRead : notification.isRead,
      },
    });

    return NextResponse.json({ notification: updatedNotification });
  } catch (error) {
    console.error("Error updating notification:", error);
    return NextResponse.json(
      { error: "Failed to update notification", message: error.message },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - Delete a notification
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  const { id } = context.params;
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get notification
    const notification = await prisma.notification.findUnique({
      where: {
        id: id,
      },
    });

    // Check if notification exists
    if (!notification) {
      return NextResponse.json(
        { error: "Notification not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to delete this notification
    if (notification.userId !== auth.user.id && auth.user.role !== "SUPER_ADMIN") {
      return NextResponse.json(
        { error: "You don't have permission to delete this notification" },
        { status: 403 }
      );
    }

    // Delete notification
    await prisma.notification.delete({
      where: {
        id: id,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting notification:", error);
    return NextResponse.json(
      { error: "Failed to delete notification", message: error.message },
      { status: 500 }
    );
  }
}
