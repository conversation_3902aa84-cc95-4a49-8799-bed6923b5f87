import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { notifyPOStatusChange } from "@/lib/notifications/integrations/purchase-order-integration";
import { prisma } from "@/lib/prisma";

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Test PO notification endpoint called');

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      console.log('❌ Test endpoint: Authentication failed');
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`👤 Test endpoint: Authenticated as ${auth.user.name} (${auth.user.role})`);

    const { poId, fromStatus, toStatus } = await request.json();
    console.log(`📋 Test endpoint: Received request for PO ${poId}: ${fromStatus} → ${toStatus}`);

    // Verify the PO exists
    const po = await prisma.purchaseOrder.findUnique({
      where: { id: poId },
      include: { supplier: true }
    });

    if (!po) {
      console.log(`❌ Test endpoint: PO ${poId} not found`);
      return NextResponse.json({ 
        error: "Purchase Order not found",
        poId 
      }, { status: 404 });
    }

    console.log(`✅ Test endpoint: Found PO ${poId} for supplier ${po.supplier.name}`);

    // Trigger the notification
    console.log(`🔔 Test endpoint: Triggering notification for PO ${poId}`);
    
    await notifyPOStatusChange(poId, fromStatus, toStatus, auth.user.id, {
      reason: 'TEST',
      notes: 'Manual test of notification system via test endpoint',
      supplierName: po.supplier.name,
      total: Number(po.total),
      poNumber: po.id.slice(-8).toUpperCase(),
      testMode: true,
    });

    console.log(`✅ Test endpoint: Notification triggered successfully for PO ${poId}`);

    // Check if notifications were created
    const recentNotifications = await prisma.notification.findMany({
      where: {
        eventType: 'po.status.changed',
        createdAt: {
          gte: new Date(Date.now() - 60000) // Last minute
        }
      },
      include: {
        user: { select: { name: true, role: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log(`📊 Test endpoint: Found ${recentNotifications.length} recent notifications`);

    return NextResponse.json({ 
      success: true, 
      message: "Test notification triggered successfully",
      details: {
        poId,
        fromStatus,
        toStatus,
        triggeredBy: auth.user.name,
        recentNotifications: recentNotifications.map(n => ({
          id: n.id,
          title: n.title,
          user: n.user.name,
          userRole: n.user.role,
          deliveryMethods: n.deliveryMethods,
          createdAt: n.createdAt
        }))
      }
    });

  } catch (error) {
    console.error("❌ Test notification endpoint error:", error);
    console.error("Error stack:", error.stack);
    
    return NextResponse.json({ 
      error: "Test failed", 
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}

// GET endpoint to check system status
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check system components
    const [preferences, events, notifications, pos] = await Promise.all([
      prisma.notificationPreference.count(),
      prisma.notificationEvent.count(),
      prisma.notification.count(),
      prisma.purchaseOrder.count()
    ]);

    // Get a sample PO for testing
    const samplePO = await prisma.purchaseOrder.findFirst({
      include: { supplier: true },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({
      systemStatus: {
        preferences,
        events,
        notifications,
        purchaseOrders: pos
      },
      samplePO: samplePO ? {
        id: samplePO.id,
        status: samplePO.status,
        supplier: samplePO.supplier.name,
        total: samplePO.total
      } : null,
      testInstructions: {
        message: "Use POST to this endpoint with { poId, fromStatus, toStatus } to test notifications",
        example: {
          poId: samplePO?.id || "your-po-id",
          fromStatus: "DRAFT",
          toStatus: "PENDING_APPROVAL"
        }
      }
    });

  } catch (error) {
    console.error("Test status endpoint error:", error);
    return NextResponse.json({ 
      error: "Status check failed", 
      details: error.message 
    }, { status: 500 });
  }
}
