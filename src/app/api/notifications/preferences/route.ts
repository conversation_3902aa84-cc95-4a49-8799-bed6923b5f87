import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/notifications/preferences - Get user notification preferences
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting GET /api/notifications/preferences');
    
    // Dynamic import with detailed logging
    console.log('📦 Attempting to import notifications module...');
    const notificationsModule = await import("@/lib/notifications");
    console.log('✅ Notifications module imported');
    console.log('Available exports:', Object.keys(notificationsModule));
    
    const { preferences } = notificationsModule;
    
    if (!preferences) {
      console.error('❌ preferences object not found in module');
      console.log('Module structure:', notificationsModule);
      return NextResponse.json(
        { 
          error: "Notification preferences module not available", 
          debug: {
            moduleExports: Object.keys(notificationsModule),
            preferencesType: typeof preferences
          }
        },
        { status: 500 }
      );
    }
    
    console.log('✅ preferences object found');
    console.log('preferences methods:', Object.keys(preferences));

    // Check authentication
    console.log('🔐 Verifying authentication...');
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }
    
    console.log('✅ Authentication successful for user:', auth.user.id);

    // Get user preferences
    console.log('📋 Fetching user preferences...');
    const userPreferences = await preferences.getForUser(auth.user.id);
    console.log('✅ User preferences fetched successfully');

    return NextResponse.json({
      success: true,
      preferences: userPreferences,
    });
  } catch (error) {
    console.error("❌ Error in GET /api/notifications/preferences:", error);
    console.error("Stack trace:", error.stack);
    return NextResponse.json(
      { 
        error: "Failed to fetch notification preferences", 
        message: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}

// PUT /api/notifications/preferences - Update user notification preferences
export async function PUT(request: NextRequest) {
  try {
    console.log('🔍 Starting PUT /api/notifications/preferences');
    
    // Dynamic import with detailed logging
    console.log('📦 Attempting to import notifications module...');
    const notificationsModule = await import("@/lib/notifications");
    console.log('✅ Notifications module imported');
    
    const { preferences } = notificationsModule;
    
    if (!preferences) {
      console.error('❌ preferences object not found in module');
      return NextResponse.json(
        { 
          error: "Notification preferences module not available",
          debug: {
            moduleExports: Object.keys(notificationsModule),
            preferencesType: typeof preferences
          }
        },
        { status: 500 }
      );
    }

    // Check authentication
    console.log('🔐 Verifying authentication...');
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }
    
    console.log('✅ Authentication successful for user:', auth.user.id);

    // Get request body
    console.log('📝 Parsing request body...');
    const body = await request.json();
    console.log('Request body received:', body);

    // Validate request body
    if (!body.preferences || !Array.isArray(body.preferences)) {
      console.log('❌ Invalid request body structure');
      return NextResponse.json(
        { error: "Invalid request body. Expected 'preferences' array." },
        { status: 400 }
      );
    }

    // Update user preferences
    console.log('💾 Updating user preferences...');
    await preferences.updateForUser(auth.user.id, body.preferences);
    console.log('✅ User preferences updated successfully');

    // Get updated preferences
    console.log('📋 Fetching updated preferences...');
    const updatedPreferences = await preferences.getForUser(auth.user.id);
    console.log('✅ Updated preferences fetched successfully');

    return NextResponse.json({
      success: true,
      message: "Notification preferences updated successfully",
      preferences: updatedPreferences,
    });
  } catch (error) {
    console.error("❌ Error in PUT /api/notifications/preferences:", error);
    console.error("Stack trace:", error.stack);
    return NextResponse.json(
      { 
        error: "Failed to update notification preferences", 
        message: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}

// POST /api/notifications/preferences - Bulk enable/disable notifications
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Starting POST /api/notifications/preferences');
    
    // Dynamic import with detailed logging
    console.log('📦 Attempting to import notifications module...');
    const notificationsModule = await import("@/lib/notifications");
    console.log('✅ Notifications module imported');
    
    const { preferences } = notificationsModule;
    
    if (!preferences) {
      console.error('❌ preferences object not found in module');
      return NextResponse.json(
        { 
          error: "Notification preferences module not available",
          debug: {
            moduleExports: Object.keys(notificationsModule),
            preferencesType: typeof preferences
          }
        },
        { status: 500 }
      );
    }

    // Check authentication
    console.log('🔐 Verifying authentication...');
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }
    
    console.log('✅ Authentication successful for user:', auth.user.id);

    // Get request body to check action
    console.log('📝 Parsing request body...');
    const body = await request.json();
    console.log('Request body received:', body);

    if (body.action === 'disable-all') {
      console.log('🚫 Disabling all notifications...');
      await preferences.disableAll(auth.user.id);
      console.log('✅ All notifications disabled successfully');
      
      return NextResponse.json({
        success: true,
        message: "All notifications disabled successfully",
      });
    } else if (body.action === 'enable-all') {
      console.log('✅ Enabling all notifications...');
      await preferences.enableAll(auth.user.id);
      console.log('✅ All notifications enabled successfully');
      
      return NextResponse.json({
        success: true,
        message: "All notifications enabled successfully",
      });
    } else {
      console.log('❌ Invalid action received:', body.action);
      return NextResponse.json(
        { error: "Invalid action. Expected 'disable-all' or 'enable-all'." },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("❌ Error in POST /api/notifications/preferences:", error);
    console.error("Stack trace:", error.stack);
    return NextResponse.json(
      { 
        error: "Failed to update notification preferences", 
        message: error.message,
        stack: error.stack 
      },
      { status: 500 }
    );
  }
}
