import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// GET /api/users/current/drawer - Get current user's assigned drawer and terminal
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Only cashiers should have drawers
    if (auth.user.role !== "CASHIER") {
      return NextResponse.json(
        { error: "Only cashiers can have assigned drawers" },
        { status: 403 }
      );
    }

    // Get user's assigned drawer
    const user = await prisma.user.findUnique({
      where: { id: auth.user.id },
      include: {
        cashDrawer: {
          include: {
            terminal: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    if (!user.cashDrawer) {
      return NextResponse.json({
        drawer: null,
        message: "No drawer assigned to this user",
      });
    }

    // Check if drawer is active
    if (!user.cashDrawer.isActive) {
      return NextResponse.json({
        drawer: null,
        message: "Assigned drawer is not active",
      });
    }

    // Check if terminal is assigned and active
    if (!user.cashDrawer.terminal) {
      return NextResponse.json({
        drawer: null,
        message: "Drawer is not assigned to any terminal",
      });
    }

    if (!user.cashDrawer.terminal.isActive) {
      return NextResponse.json({
        drawer: null,
        message: "Assigned terminal is not active",
      });
    }

    return NextResponse.json({
      drawer: {
        id: user.cashDrawer.id,
        name: user.cashDrawer.name,
        isActive: user.cashDrawer.isActive,
        terminal: {
          id: user.cashDrawer.terminal.id,
          name: user.cashDrawer.terminal.name,
          isActive: user.cashDrawer.terminal.isActive,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching user drawer:", error);
    return NextResponse.json(
      { error: "Failed to fetch drawer information", message: (error as Error).message },
      { status: 500 }
    );
  }
}
