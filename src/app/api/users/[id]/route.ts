import { NextRequest, NextResponse } from "next/server";
import { hashPassword, prisma } from "@/auth";
import { z } from "zod";
import { jwtVerify } from "jose";

// Helper function to find an available terminal for drawer assignment
async function findAvailableTerminal() {
  return await prisma.terminal.findFirst({
    where: {
      isActive: true,
      drawerId: null, // Terminal without assigned drawer
    },
    orderBy: {
      createdAt: "asc", // First available terminal
    },
  });
}

// Helper function to deactivate drawer and unassign from terminal
async function deactivateDrawerAndUnassignTerminal(userId: string, actorUserId: string) {
  const result = {
    drawerDeactivated: false,
    unassignedTerminal: null as any,
    drawerName: null as string | null,
  };

  try {
    // Find the cashier's drawer
    const cashierDrawer = await prisma.cashDrawer.findUnique({
      where: { userId },
      include: {
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    if (cashierDrawer) {
      // Unassign drawer from terminal if assigned
      if (cashierDrawer.terminal) {
        await prisma.terminal.update({
          where: { id: cashierDrawer.terminal.id },
          data: { drawerId: null },
        });
        result.unassignedTerminal = cashierDrawer.terminal;
      }

      // Deactivate the drawer
      await prisma.cashDrawer.update({
        where: { id: cashierDrawer.id },
        data: { isActive: false },
      });

      result.drawerDeactivated = true;
      result.drawerName = cashierDrawer.name;

      // Log drawer deactivation activity
      try {
        await prisma.activityLog.create({
          data: {
            userId: actorUserId,
            action: "ROLE_CHANGE_DEACTIVATE_DRAWER",
            details: `Role change: Deactivated drawer "${cashierDrawer.name}"${result.unassignedTerminal ? ` and unassigned from terminal "${result.unassignedTerminal.name}"` : ""}`,
          },
        });
      } catch (logError) {
        console.error("Error logging drawer deactivation activity:", logError);
      }
    }
  } catch (error) {
    console.error("Error deactivating drawer:", error);
    throw error;
  }

  return result;
}

// Helper function to reactivate drawer and assign to available terminal
async function reactivateDrawerAndAssignTerminal(userId: string, actorUserId: string) {
  const result = {
    drawerReactivated: false,
    assignedTerminal: null as any,
    drawerName: null as string | null,
  };

  try {
    // Find the user's existing drawer
    const existingDrawer = await prisma.cashDrawer.findUnique({
      where: { userId },
      include: {
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
      },
    });

    if (existingDrawer) {
      // Reactivate the drawer
      await prisma.cashDrawer.update({
        where: { id: existingDrawer.id },
        data: { isActive: true },
      });

      result.drawerReactivated = true;
      result.drawerName = existingDrawer.name;

      // Try to assign to an available terminal if not already assigned
      if (!existingDrawer.terminal) {
        const availableTerminal = await findAvailableTerminal();
        if (availableTerminal) {
          await prisma.terminal.update({
            where: { id: availableTerminal.id },
            data: { drawerId: existingDrawer.id },
          });
          result.assignedTerminal = availableTerminal;
        }
      }

      // Log drawer reactivation activity
      try {
        await prisma.activityLog.create({
          data: {
            userId: actorUserId,
            action: "ROLE_CHANGE_REACTIVATE_DRAWER",
            details: `Role change: Reactivated drawer "${existingDrawer.name}"${result.assignedTerminal ? ` and assigned to terminal "${result.assignedTerminal.name}"` : ""}`,
          },
        });
      } catch (logError) {
        console.error("Error logging drawer reactivation activity:", logError);
      }
    }
  } catch (error) {
    console.error("Error reactivating drawer:", error);
    throw error;
  }

  return result;
}

// Helper function to create new drawer for cashier
async function createDrawerForCashier(userId: string, userName: string, actorUserId: string) {
  const result = {
    drawerCreated: false,
    assignedTerminal: null as any,
    drawerName: null as string | null,
  };

  try {
    // Find an available terminal for auto-assignment
    const availableTerminal = await findAvailableTerminal();

    // Create cash drawer with cashier's name
    const drawer = await prisma.cashDrawer.create({
      data: {
        name: `${userName}'s Drawer`,
        location: null,
        isActive: true,
        userId: userId,
      },
    });

    result.drawerCreated = true;
    result.drawerName = drawer.name;

    // Auto-assign to available terminal if one exists
    if (availableTerminal) {
      await prisma.terminal.update({
        where: { id: availableTerminal.id },
        data: { drawerId: drawer.id },
      });
      result.assignedTerminal = availableTerminal;
    }

    // Log drawer creation activity
    try {
      await prisma.activityLog.create({
        data: {
          userId: actorUserId,
          action: "ROLE_CHANGE_CREATE_DRAWER",
          details: `Role change: Created drawer "${drawer.name}"${result.assignedTerminal ? ` and assigned to terminal "${result.assignedTerminal.name}"` : ""}`,
        },
      });
    } catch (logError) {
      console.error("Error logging drawer creation activity:", logError);
    }
  } catch (error) {
    console.error("Error creating drawer for cashier:", error);
    throw error;
  }

  return result;
}

// GET /api/users/[id] - Get a specific user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/users/[id] GET - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let userId = "";
    let userRole = "";
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/users/[id] GET - Token verified, user role:", payload.role);

      userId = payload.id as string;
      userRole = payload.role as string;

      // Check if user has appropriate role or is the user themselves
      const isDeveloper = userRole === "DEVELOPER";
      const isSuperAdmin = userRole === "SUPER_ADMIN";

      if (!isDeveloper && !isSuperAdmin && userId !== params.id) {
        console.log("[API] /api/users/[id] GET - User does not have permission:", userRole, userId, params.id);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }
    } catch (jwtError) {
      console.error("[API] /api/users/[id] GET - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { id: params.id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Non-developers cannot view developer accounts
    const isDeveloper = userRole === "DEVELOPER";
    if (!isDeveloper && user.role === "DEVELOPER") {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ user });
  } catch (error: any) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { error: "Failed to fetch user", message: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// User update schema
const updateUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  email: z.string().email({ message: "Invalid email address" }).optional(),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }).optional(),
  role: z.enum(["DEVELOPER", "SUPER_ADMIN", "CASHIER", "FINANCE_ADMIN", "WAREHOUSE_ADMIN", "MARKETING"]).optional(),
  active: z.boolean().optional(),
});

// PATCH /api/users/[id] - Update a user
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/users/[id] PATCH - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let userId = "";
    let userRole = "";
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/users/[id] PATCH - Token verified, user role:", payload.role);

      userId = payload.id as string;
      userRole = payload.role as string;

      if (!userId) {
        console.log("[API] /api/users/[id] PATCH - No user ID in token");
        return NextResponse.json(
          { error: "Unauthorized - Invalid token" },
          { status: 403 }
        );
      }
    } catch (jwtError) {
      console.error("[API] /api/users/[id] PATCH - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate input data
    const validationResult = updateUserSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check user roles
    const isDeveloper = userRole === "DEVELOPER";
    const isSuperAdmin = userRole === "SUPER_ADMIN";

    // Only allow DEVELOPER or SUPER_ADMIN to change roles or active status
    if ((data.role || data.active !== undefined) && !isDeveloper && !isSuperAdmin) {
      console.log("[API] /api/users/[id] PATCH - Non-admin trying to change role/status:", userRole);
      return NextResponse.json(
        { error: "Only administrators can change roles or active status" },
        { status: 403 }
      );
    }

    // Only developers can set or change to developer role
    if (data.role === "DEVELOPER" && !isDeveloper) {
      console.log("[API] /api/users/[id] PATCH - Non-developer trying to set developer role:", userRole);
      return NextResponse.json(
        { error: "Unauthorized - Cannot set developer role" },
        { status: 403 }
      );
    }

    // Only allow users to update their own info or DEVELOPER/SUPER_ADMIN to update any user
    if (userId !== params.id && !isDeveloper && !isSuperAdmin) {
      console.log("[API] /api/users/[id] PATCH - User trying to update another user:", userId, params.id);
      return NextResponse.json(
        { error: "You can only update your own information" },
        { status: 403 }
      );
    }

    // Non-developers cannot update developer accounts
    if (!isDeveloper && existingUser.role === "DEVELOPER") {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if email is being changed and if it's already in use
    if (data.email && data.email !== existingUser.email) {
      const emailExists = await prisma.user.findUnique({
        where: { email: data.email },
      });

      if (emailExists) {
        return NextResponse.json(
          { error: "Email already in use" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (data.name) updateData.name = data.name;
    if (data.email) updateData.email = data.email;
    if (data.password) updateData.password = await hashPassword(data.password);
    if (data.role) updateData.role = data.role;
    if (data.active !== undefined) updateData.active = data.active;

    // Use transaction for atomic operations
    const result = await prisma.$transaction(async (tx) => {
      // Update user
      const user = await tx.user.update({
        where: { id: params.id },
        data: updateData,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          active: true,
          updatedAt: true,
        },
      });

      // Handle drawer lifecycle management based on role changes and status changes
      let drawerOperationResult = {
        drawerDeactivated: false,
        drawerReactivated: false,
        drawerCreated: false,
        unassignedTerminal: null as any,
        assignedTerminal: null as any,
        drawerName: null as string | null,
      };

      // Scenario 1: Cashier role removal (CASHIER → Other Role)
      if (existingUser.role === "CASHIER" && data.role && data.role !== "CASHIER") {
        try {
          const result = await deactivateDrawerAndUnassignTerminal(params.id, userId);
          drawerOperationResult = { ...drawerOperationResult, ...result };
        } catch (error) {
          console.error("Error handling cashier role removal:", error);
          // Don't fail the transaction, but log the error
        }
      }
      // Scenario 2: Cashier role restoration (Other Role → CASHIER)
      else if (existingUser.role !== "CASHIER" && data.role === "CASHIER") {
        try {
          // Check if user has existing drawer
          const existingDrawer = await tx.cashDrawer.findUnique({
            where: { userId: params.id },
          });

          if (existingDrawer) {
            // Reactivate existing drawer
            const result = await reactivateDrawerAndAssignTerminal(params.id, userId);
            drawerOperationResult = { ...drawerOperationResult, ...result };
          } else {
            // Create new drawer for new cashier
            const result = await createDrawerForCashier(params.id, user.name, userId);
            drawerOperationResult = { ...drawerOperationResult, ...result };
          }
        } catch (error) {
          console.error("Error handling cashier role restoration:", error);
          // Don't fail the transaction, but log the error
        }
      }
      // Scenario 3: Cashier deactivation (existing logic)
      else if (existingUser.role === "CASHIER" && data.active === false && existingUser.active === true) {
        try {
          const result = await deactivateDrawerAndUnassignTerminal(params.id, userId);
          drawerOperationResult = { ...drawerOperationResult, ...result };
        } catch (error) {
          console.error("Error handling cashier deactivation:", error);
          // Don't fail the transaction, but log the error
        }
      }

      return { user, drawerOperationResult };
    });

    const { user, drawerOperationResult } = result;

    // Create a detailed log message about what was changed
    const changedFields = [];
    if (data.name && data.name !== existingUser.name) changedFields.push("name");
    if (data.email && data.email !== existingUser.email) changedFields.push("email");
    if (data.password) changedFields.push("password");
    if (data.role && data.role !== existingUser.role) changedFields.push(`role from ${existingUser.role} to ${data.role}`);
    if (data.active !== undefined && data.active !== existingUser.active)
      changedFields.push(`status to ${data.active ? "active" : "inactive"}`);

    // Build drawer operation details
    const drawerDetails = [];
    if (drawerOperationResult.drawerDeactivated) {
      drawerDetails.push(`deactivated drawer "${drawerOperationResult.drawerName}"`);
    }
    if (drawerOperationResult.drawerReactivated) {
      drawerDetails.push(`reactivated drawer "${drawerOperationResult.drawerName}"`);
    }
    if (drawerOperationResult.drawerCreated) {
      drawerDetails.push(`created drawer "${drawerOperationResult.drawerName}"`);
    }
    if (drawerOperationResult.unassignedTerminal) {
      drawerDetails.push(`unassigned from terminal "${drawerOperationResult.unassignedTerminal.name}"`);
    }
    if (drawerOperationResult.assignedTerminal) {
      drawerDetails.push(`assigned to terminal "${drawerOperationResult.assignedTerminal.name}"`);
    }

    const changeDetails = changedFields.length > 0
      ? `Updated ${changedFields.join(", ")} for user: ${user.email}${drawerDetails.length > 0 ? ` and ${drawerDetails.join(", ")}` : ""}`
      : `Updated user: ${user.email}${drawerDetails.length > 0 ? ` and ${drawerDetails.join(", ")}` : ""}`;

    // Try to log activity, but don't fail if it doesn't work
    try {
      await prisma.activityLog.create({
        data: {
          userId: userId,
          action: "UPDATE_USER",
          details: changeDetails,
        },
      });
    } catch (logError) {
      // Just log the error but don't fail the request
      console.error("Error creating activity log for user update:", logError);
      // Continue with the response
    }

    return NextResponse.json({ user });
  } catch (error: any) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Failed to update user", message: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete a user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/users/[id] DELETE - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let userId = "";
    let userRole = "";
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/users/[id] DELETE - Token verified, user role:", payload.role);

      userId = payload.id as string;
      userRole = payload.role as string;

      // Check if user has appropriate role
      const isDeveloper = userRole === "DEVELOPER";
      const isSuperAdmin = userRole === "SUPER_ADMIN";

      if (!isDeveloper && !isSuperAdmin) {
        console.log("[API] /api/users/[id] DELETE - User does not have sufficient permissions:", userRole);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }
    } catch (jwtError) {
      console.error("[API] /api/users/[id] DELETE - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: params.id },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Don't allow users to deactivate themselves
    if (userId === params.id) {
      return NextResponse.json(
        { error: "You cannot deactivate your own account" },
        { status: 400 }
      );
    }

    // Non-developers cannot deactivate developer accounts
    const isDeveloperRole = userRole === "DEVELOPER";
    if (!isDeveloperRole && existingUser.role === "DEVELOPER") {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Toggle the user's active status
    const newActiveStatus = !existingUser.active;

    const user = await prisma.user.update({
      where: { id: params.id },
      data: { active: newActiveStatus },
    });

    // Try to log activity, but don't fail if it doesn't work
    try {
      await prisma.activityLog.create({
        data: {
          userId: userId,
          action: newActiveStatus ? "ACTIVATE_USER" : "DEACTIVATE_USER",
          details: `${newActiveStatus ? 'Activated' : 'Deactivated'} user: ${user.email} (${user.name}) with role ${user.role}`,
        },
      });
    } catch (logError) {
      // Just log the error but don't fail the request
      console.error("Error creating activity log for user status change:", logError);
      // Continue with the response
    }

    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error toggling user active status:", error);
    return NextResponse.json(
      { error: "Failed to update user status", message: error?.message || String(error) },
      { status: 500 }
    );
  }
}
