import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// GET /api/users/[id]/drawer - Get a user's assigned drawer
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params
    const { id } = await params;

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view drawer information
    const allowedRoles = ["CASHIER", "SUPER_ADMIN", "FINANCE_ADMIN"];
    if (!allowedRoles.includes(auth.user.role)) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions to view drawer information" },
        { status: 403 }
      );
    }

    // Cashiers can only view their own drawer
    if (auth.user.role === "CASHIER" && auth.user.id !== id) {
      return NextResponse.json(
        { error: "Unauthorized - You can only view your own drawer" },
        { status: 403 }
      );
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Get the user's assigned drawer
    const drawer = await prisma.cashDrawer.findUnique({
      where: { userId: id },
      include: {
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
            isActive: true,
          },
        },
      },
    });

    if (!drawer) {
      return NextResponse.json(
        {
          user: {
            id: user.id,
            name: user.name,
            role: user.role,
          },
          drawer: null,
          message: user.role === "CASHIER" ? "No drawer assigned to this cashier" : "This user is not a cashier"
        }
      );
    }

    return NextResponse.json({
      user: {
        id: user.id,
        name: user.name,
        role: user.role,
      },
      drawer: {
        id: drawer.id,
        name: drawer.name,
        location: drawer.location,
        isActive: drawer.isActive,
        createdAt: drawer.createdAt,
        updatedAt: drawer.updatedAt,
        terminal: drawer.terminal,
      },
    });
  } catch (error) {
    console.error("Error fetching user drawer:", error);
    return NextResponse.json(
      { error: "Failed to fetch user drawer", message: (error as Error).message },
      { status: 500 }
    );
  }
}
