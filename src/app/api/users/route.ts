import { NextRequest, NextResponse } from "next/server";
import { hashPassword, auth, prisma } from "@/auth";
import { z } from "zod";
import { jwtVerify } from "jose";

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/users - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/users - Token verified, user role:", payload.role);

      // For chat functionality, we allow all authenticated users to fetch the user list
      // But for user management, only SUPER_ADMIN can access
      const isForChat = request.nextUrl.searchParams.get("for") === "chat";
      const rolesParam = request.nextUrl.searchParams.get("roles");

      if (!isForChat && !rolesParam && payload.role !== "SUPER_ADMIN") {
        console.log("[API] /api/users - User does not have SUPER_ADMIN role:", payload.role);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }

      // Determine if we should filter out developer accounts
      const isDeveloper = payload.role === "DEVELOPER";

      // Build where clause
      let whereClause: any = {};

      // Role filtering
      if (rolesParam) {
        const allowedRoles = rolesParam.split(',').filter(role =>
          ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN', 'CASHIER', 'MARKETING'].includes(role)
        );
        if (allowedRoles.length > 0) {
          whereClause.role = { in: allowedRoles };
        }
      } else if (!isDeveloper) {
        // Only developers can see other developer accounts
        whereClause.NOT = { role: "DEVELOPER" };
      }

      // Only developers can see other developer accounts
      const users = await prisma.user.findMany({
        where: whereClause,
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          active: true,
          createdAt: true,
          updatedAt: true,
          cashDrawer: {
            select: {
              id: true,
              name: true,
              isActive: true,
              terminal: {
                select: {
                  id: true,
                  name: true,
                  location: true,
                },
              },
            },
          },
        },
      });

      return NextResponse.json({ users });
    } catch (jwtError) {
      console.error("[API] /api/users - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }
  } catch (error: any) {
    console.error("[API] Error fetching users:", error);
    return NextResponse.json(
      { error: "Failed to fetch users", message: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// User creation schema
const createUserSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
  role: z.enum(["DEVELOPER", "SUPER_ADMIN", "CASHIER", "FINANCE_ADMIN", "WAREHOUSE_ADMIN", "MARKETING"]),
});

// POST /api/users - Create a new user
export async function POST(request: NextRequest) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/users POST - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let isDeveloper = false;
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/users POST - Token verified, user role:", payload.role);

      // Check if user has appropriate role
      isDeveloper = payload.role === "DEVELOPER";
      const isSuperAdmin = payload.role === "SUPER_ADMIN";

      if (!isDeveloper && !isSuperAdmin) {
        console.log("[API] /api/users POST - User does not have sufficient permissions:", payload.role);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }

      // We don't need userId anymore since we're using the created user's ID for the activity log
    } catch (jwtError) {
      console.error("[API] /api/users POST - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    const data = await request.json();

    // Validate input data
    const validationResult = createUserSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Validation failed", details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: data.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "Email already in use" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(data.password);

    // Only developers can create developer accounts (isDeveloper was set during token verification)

    // If trying to create a developer account but not a developer
    if (data.role === "DEVELOPER" && !isDeveloper) {
      return NextResponse.json(
        { error: "Unauthorized - Cannot create developer accounts" },
        { status: 403 }
      );
    }

    // Create user and automatically create drawer for cashiers
    const user = await prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: hashedPassword,
        role: data.role,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        active: true,
        createdAt: true,
      },
    });

    let drawer = null;
    let autoAssignedTerminal = null;

    // If the user is a cashier, automatically create a drawer
    if (data.role === "CASHIER") {
      try {
        // Find an available terminal for auto-assignment
        const availableTerminal = await prisma.terminal.findFirst({
          where: {
            isActive: true,
            drawerId: null, // Terminal without assigned drawer
          },
          orderBy: {
            createdAt: "asc", // First available terminal
          },
        });

        // Create cash drawer with cashier's name
        drawer = await prisma.cashDrawer.create({
          data: {
            name: `${data.name}'s Drawer`,
            location: null,
            isActive: true,
            userId: user.id, // Link drawer to user
          },
          include: {
            terminal: {
              select: {
                id: true,
                name: true,
                location: true,
              },
            },
          },
        });

        // Auto-assign to available terminal if one exists and drawer is active
        if (availableTerminal && drawer.isActive) {
          await prisma.terminal.update({
            where: { id: availableTerminal.id },
            data: { drawerId: drawer.id },
          });

          autoAssignedTerminal = availableTerminal;

          // Log auto-assignment activity
          try {
            await prisma.activityLog.create({
              data: {
                userId: user.id,
                action: "AUTO_ASSIGN_DRAWER",
                details: `Auto-assigned drawer "${drawer.name}" to terminal "${availableTerminal.name}"`,
              },
            });
          } catch (logError) {
            console.error("Error logging auto-assignment activity:", logError);
          }
        }

        // Log drawer creation activity
        try {
          await prisma.activityLog.create({
            data: {
              userId: user.id,
              action: "CREATE_CASH_DRAWER",
              details: `Created cash drawer: ${drawer.name}${autoAssignedTerminal ? ` (auto-assigned to ${autoAssignedTerminal.name})` : ""}`,
            },
          });
        } catch (logError) {
          console.error("Error logging drawer creation activity:", logError);
        }
      } catch (drawerError) {
        console.error("Error creating drawer for cashier:", drawerError);
        // Don't fail user creation if drawer creation fails
      }
    }

    // Log user creation activity
    try {
      await prisma.activityLog.create({
        data: {
          userId: user.id,
          action: "CREATE_USER",
          details: `Created user: ${user.name} (${user.email}) with role ${user.role}${drawer ? ` and drawer "${drawer.name}"` : ""}`,
        },
      });
    } catch (logError) {
      console.error("Error logging user creation activity:", logError);
    }

    return NextResponse.json({
      user,
      drawer: drawer ? {
        id: drawer.id,
        name: drawer.name,
        autoAssignedTerminal: autoAssignedTerminal ? {
          id: autoAssignedTerminal.id,
          name: autoAssignedTerminal.name,
          location: autoAssignedTerminal.location,
        } : null,
      } : null,
    }, { status: 201 });
  } catch (error: any) {
    console.error("Error creating user:", error);
    return NextResponse.json(
      { error: "Failed to create user", message: error?.message || String(error) },
      { status: 500 }
    );
  }
}
