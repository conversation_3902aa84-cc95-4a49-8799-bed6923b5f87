import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();



// GET /api/cash-drawers - Get all cash drawers
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view cash drawers
    const hasPermission = auth.authenticated && auth.user && ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const isActive = searchParams.get("isActive");

    // Build filter
    const filter: any = {};
    if (isActive !== null) {
      filter.isActive = isActive === "true";
    }

    // Get cash drawers
    const drawers = await prisma.cashDrawer.findMany({
      where: filter,
      orderBy: {
        createdAt: "desc",
      },
      include: {
        terminal: {
          select: {
            id: true,
            name: true,
            location: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            active: true,
          },
        },
        _count: {
          select: {
            drawerSessions: true,
          },
        },
      },
    });

    return NextResponse.json({ drawers });
  } catch (error) {
    console.error("Error fetching cash drawers:", error);
    return NextResponse.json(
      { error: "Failed to fetch cash drawers", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// POST /api/cash-drawers - Manual drawer creation disabled
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Manual drawer creation is now disabled in favor of automatic creation
    return NextResponse.json(
      {
        error: "Manual drawer creation is disabled",
        message: "Cash drawers are now automatically created when new cashiers are added to the system. Please use the user management page to add cashiers."
      },
      { status: 403 }
    );
  } catch (error) {
    console.error("Error in cash drawer POST endpoint:", error);
    return NextResponse.json(
      { error: "Failed to process request", message: (error as Error).message },
      { status: 500 }
    );
  }
}






