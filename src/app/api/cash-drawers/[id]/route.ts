import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PrismaClient } from "@prisma/client";
import { verifyAuthToken } from "@/lib/auth-utils";

const prisma = new PrismaClient();

// Drawer schema for validation
const drawerSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }).optional(),
  location: z.string().optional(),
  isActive: z.boolean().optional(),
});

// GET /api/cash-drawers/[id] - Get a specific cash drawer
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to view cash drawers
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN", "CASHIER"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get cash drawer
    const drawer = await prisma.cashDrawer.findUnique({
      where: { id: params.id },
      include: {
        drawerSessions: {
          orderBy: {
            openedAt: "desc",
          },
          take: 10,
          include: {
            user: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!drawer) {
      return NextResponse.json(
        { error: "Cash drawer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ drawer });
  } catch (error) {
    console.error("Error fetching cash drawer:", error);
    return NextResponse.json(
      { error: "Failed to fetch cash drawer", message: (error as Error).message },
      { status: 500 }
    );
  }
}

// PATCH /api/cash-drawers/[id] - Update a cash drawer
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Check if user has permission to update cash drawers
    const hasPermission = ["SUPER_ADMIN", "FINANCE_ADMIN"].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json(
        { error: "Unauthorized - Insufficient permissions" },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = drawerSchema.parse(body);

    // Check if drawer exists
    const existingDrawer = await prisma.cashDrawer.findUnique({
      where: { id: params.id },
      include: {
        drawerSessions: {
          where: {
            status: "OPEN",
          },
        },
      },
    });

    if (!existingDrawer) {
      return NextResponse.json(
        { error: "Cash drawer not found" },
        { status: 404 }
      );
    }

    // If trying to deactivate a drawer with open sessions, prevent it
    if (validatedData.isActive === false && existingDrawer.drawerSessions.length > 0) {
      return NextResponse.json(
        {
          error: "Cannot deactivate drawer with open sessions",
          openSessions: existingDrawer.drawerSessions.length
        },
        { status: 400 }
      );
    }

    // If deactivating a drawer, automatically unassign it from any terminal
    if (validatedData.isActive === false && existingDrawer.isActive === true) {
      // Find any terminal assigned to this drawer
      const assignedTerminal = await prisma.terminal.findFirst({
        where: { drawerId: params.id },
      });

      if (assignedTerminal) {
        // Unassign the terminal
        await prisma.terminal.update({
          where: { id: assignedTerminal.id },
          data: { drawerId: null },
        });

        // Log the automatic unassignment
        await prisma.activityLog.create({
          data: {
            userId: auth.user.id,
            action: "AUTO_UNASSIGN_TERMINAL",
            details: `Automatically unassigned terminal "${assignedTerminal.name}" from drawer "${existingDrawer.name}" due to drawer deactivation`,
          },
        });
      }
    }

    // Update cash drawer
    const drawer = await prisma.cashDrawer.update({
      where: { id: params.id },
      data: validatedData,
    });

    // Log activity
    await prisma.activityLog.create({
      data: {
        userId: auth.user.id,
        action: "UPDATE_CASH_DRAWER",
        details: `Updated cash drawer: ${drawer.name}`,
      },
    });

    return NextResponse.json({ drawer });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error updating cash drawer:", error);
    return NextResponse.json(
      { error: "Failed to update cash drawer", message: (error as Error).message },
      { status: 500 }
    );
  }
}
