import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a quality issue
const createQualityIssueSchema = z.object({
  returnItemId: z.string().optional(),
  batchId: z.string().optional(),
  productId: z.string().min(1, 'Product ID is required'),
  supplierId: z.string().min(1, 'Supplier ID is required'),
  issueType: z.enum([
    'DEFECTIVE_PRODUCT',
    'PACKAGING_DAMAGE',
    'WRONG_SPECIFICATION',
    'CONTAMINATION',
    'EXPIRY_ISSUE',
    'QUANTITY_DISCREPANCY',
    'QUALITY_DEGRADATION',
    'CUSTOMER_COMPLAINT',
    'OTHER'
  ]),
  severity: z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).default('MEDIUM'),
  description: z.string().min(1, 'Description is required'),
  defectCategory: z.string().optional(),
  affectedQuantity: z.number().positive('Affected quantity must be greater than 0'),
});

// GET /api/quality-issues - List quality issues with filtering
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const severity = searchParams.get('severity');
    const supplierId = searchParams.get('supplierId');
    const issueType = searchParams.get('issueType');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) where.status = status;
    if (severity) where.severity = severity;
    if (supplierId) where.supplierId = supplierId;
    if (issueType) where.issueType = issueType;
    
    if (search) {
      where.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { defectCategory: { contains: search, mode: 'insensitive' } },
        { product: { name: { contains: search, mode: 'insensitive' } } },
        { supplier: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    const [qualityIssues, total] = await Promise.all([
      prisma.qualityIssue.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true,
            },
          },
          batch: {
            select: {
              id: true,
              batchNumber: true,
              receivedDate: true,
              expiryDate: true,
            },
          },
          returnItem: {
            include: {
              return: {
                select: {
                  id: true,
                  reason: true,
                  returnDate: true,
                },
              },
            },
          },
          reporter: {
            select: {
              id: true,
              name: true,
            },
          },
          resolver: {
            select: {
              id: true,
              name: true,
            },
          },
          escalations: {
            include: {
              escalatedToUser: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        orderBy: [
          { severity: 'desc' },
          { reportedAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      prisma.qualityIssue.count({ where }),
    ]);

    return NextResponse.json({
      qualityIssues,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching quality issues:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/quality-issues - Create new quality issue
export async function POST(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canCreateQualityIssue = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canCreateQualityIssue) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = createQualityIssueSchema.parse(body);

    // Verify product and supplier exist
    const [product, supplier] = await Promise.all([
      prisma.product.findUnique({
        where: { id: validatedData.productId },
        select: { id: true, name: true },
      }),
      prisma.supplier.findUnique({
        where: { id: validatedData.supplierId },
        select: { id: true, name: true },
      }),
    ]);

    if (!product) {
      return NextResponse.json({ error: 'Product not found' }, { status: 404 });
    }

    if (!supplier) {
      return NextResponse.json({ error: 'Supplier not found' }, { status: 404 });
    }

    // Verify batch exists if provided
    if (validatedData.batchId) {
      const batch = await prisma.stockBatch.findUnique({
        where: { id: validatedData.batchId },
        select: { id: true, productId: true },
      });

      if (!batch) {
        return NextResponse.json({ error: 'Batch not found' }, { status: 404 });
      }

      if (batch.productId !== validatedData.productId) {
        return NextResponse.json({ error: 'Batch does not belong to the specified product' }, { status: 400 });
      }
    }

    // Verify return item exists if provided
    if (validatedData.returnItemId) {
      const returnItem = await prisma.returnItem.findUnique({
        where: { id: validatedData.returnItemId },
        select: { id: true, productId: true },
      });

      if (!returnItem) {
        return NextResponse.json({ error: 'Return item not found' }, { status: 404 });
      }

      if (returnItem.productId !== validatedData.productId) {
        return NextResponse.json({ error: 'Return item does not belong to the specified product' }, { status: 400 });
      }
    }

    // Create quality issue
    const newQualityIssue = await prisma.qualityIssue.create({
      data: {
        ...validatedData,
        reportedBy: auth.user.id,
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
          },
        },
        batch: {
          select: {
            id: true,
            batchNumber: true,
            receivedDate: true,
            expiryDate: true,
          },
        },
        returnItem: {
          include: {
            return: {
              select: {
                id: true,
                reason: true,
                returnDate: true,
              },
            },
          },
        },
        reporter: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // TODO: Trigger automatic escalation check
    // This will be implemented in the escalation service

    return NextResponse.json(newQualityIssue, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error creating quality issue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
