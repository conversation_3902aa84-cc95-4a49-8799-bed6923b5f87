import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for escalating a quality issue
const escalateQualityIssueSchema = z.object({
  escalatedTo: z.string().min(1, 'Escalated to user ID is required'),
  escalationReason: z.string().min(1, 'Escalation reason is required'),
  responseRequired: z.boolean().default(true),
  responseDeadline: z.string().optional().transform((val) => val ? new Date(val) : undefined),
});

// POST /api/quality-issues/[id]/escalate - Escalate quality issue
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canEscalate = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canEscalate) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = escalateQualityIssueSchema.parse(body);

    // Check if quality issue exists
    const qualityIssue = await prisma.qualityIssue.findUnique({
      where: { id },
      include: {
        supplier: {
          select: {
            id: true,
            name: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!qualityIssue) {
      return NextResponse.json({ error: 'Quality issue not found' }, { status: 404 });
    }

    // Verify escalation target user exists
    const escalationTarget = await prisma.user.findUnique({
      where: { id: validatedData.escalatedTo },
      select: { id: true, name: true, role: true },
    });

    if (!escalationTarget) {
      return NextResponse.json({ error: 'Escalation target user not found' }, { status: 404 });
    }

    // Check if user has appropriate role for escalation
    const validEscalationRoles = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'];
    if (!validEscalationRoles.includes(escalationTarget.role)) {
      return NextResponse.json({ 
        error: 'Escalation target must have appropriate role (SUPER_ADMIN, WAREHOUSE_ADMIN, or FINANCE_ADMIN)' 
      }, { status: 400 });
    }

    // Use transaction to update quality issue and create escalation
    const result = await prisma.$transaction(async (tx) => {
      // Increment escalation level and update quality issue status
      const updatedQualityIssue = await tx.qualityIssue.update({
        where: { id },
        data: {
          escalationLevel: qualityIssue.escalationLevel + 1,
          status: 'ESCALATED',
          escalatedAt: new Date(),
          escalatedBy: auth.user!.id,
        },
      });

      // Create escalation record
      const escalation = await tx.qualityEscalation.create({
        data: {
          qualityIssueId: id,
          escalationLevel: updatedQualityIssue.escalationLevel,
          escalatedTo: validatedData.escalatedTo,
          escalatedBy: auth.user!.id,
          escalationReason: validatedData.escalationReason,
          responseRequired: validatedData.responseRequired,
          responseDeadline: validatedData.responseDeadline,
        },
        include: {
          escalatedToUser: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
          escalatedByUser: {
            select: {
              id: true,
              name: true,
              role: true,
            },
          },
        },
      });

      return { updatedQualityIssue, escalation };
    });

    // TODO: Send notification to escalation target
    // This will be implemented in the notification service

    return NextResponse.json({
      message: 'Quality issue escalated successfully',
      qualityIssue: result.updatedQualityIssue,
      escalation: result.escalation,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error escalating quality issue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// GET /api/quality-issues/[id]/escalate - Get escalation history
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Check if quality issue exists
    const qualityIssue = await prisma.qualityIssue.findUnique({
      where: { id },
      select: { id: true },
    });

    if (!qualityIssue) {
      return NextResponse.json({ error: 'Quality issue not found' }, { status: 404 });
    }

    // Get escalation history
    const escalations = await prisma.qualityEscalation.findMany({
      where: { qualityIssueId: id },
      include: {
        escalatedToUser: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        escalatedByUser: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        responder: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
      },
      orderBy: {
        escalatedAt: 'desc',
      },
    });

    return NextResponse.json({
      qualityIssueId: id,
      escalations,
      totalEscalations: escalations.length,
      currentEscalationLevel: escalations.length > 0 ? escalations[0].escalationLevel : 0,
    });
  } catch (error) {
    console.error('Error fetching escalation history:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
