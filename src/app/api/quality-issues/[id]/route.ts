import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for updating a quality issue
const updateQualityIssueSchema = z.object({
  status: z.enum(['OPEN', 'IN_PROGRESS', 'RESOLVED', 'CLOSED', 'ESCALATED']).optional(),
  resolutionNotes: z.string().optional(),
  escalationLevel: z.number().min(0).optional(),
});

// GET /api/quality-issues/[id] - Get specific quality issue
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const qualityIssue = await prisma.qualityIssue.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            barcode: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
            phone: true,
            email: true,
          },
        },
        batch: {
          include: {
            productSupplier: {
              select: {
                id: true,
                supplierProductCode: true,
                supplierProductName: true,
                purchasePrice: true,
              },
            },
          },
        },
        returnItem: {
          include: {
            return: {
              include: {
                customer: {
                  select: {
                    id: true,
                    name: true,
                    phone: true,
                  },
                },
                transaction: {
                  select: {
                    id: true,
                    transactionDate: true,
                  },
                },
              },
            },
          },
        },
        reporter: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        resolver: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        escalator: {
          select: {
            id: true,
            name: true,
            role: true,
          },
        },
        escalations: {
          include: {
            escalatedToUser: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
            escalatedByUser: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
            responder: {
              select: {
                id: true,
                name: true,
                role: true,
              },
            },
          },
          orderBy: {
            escalatedAt: 'desc',
          },
        },
        improvements: {
          include: {
            assignee: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!qualityIssue) {
      return NextResponse.json({ error: 'Quality issue not found' }, { status: 404 });
    }

    return NextResponse.json(qualityIssue);
  } catch (error) {
    console.error('Error fetching quality issue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PATCH /api/quality-issues/[id] - Update quality issue
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canUpdateQualityIssue = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!canUpdateQualityIssue) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateQualityIssueSchema.parse(body);

    // Check if quality issue exists
    const existingQualityIssue = await prisma.qualityIssue.findUnique({
      where: { id },
      select: { id: true, status: true },
    });

    if (!existingQualityIssue) {
      return NextResponse.json({ error: 'Quality issue not found' }, { status: 404 });
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedData.status !== undefined) {
      updateData.status = validatedData.status;
      
      // If resolving, set resolver and resolved date
      if (validatedData.status === 'RESOLVED' || validatedData.status === 'CLOSED') {
        updateData.resolvedBy = auth.user.id;
        updateData.resolvedAt = new Date();
      }
    }

    if (validatedData.resolutionNotes !== undefined) {
      updateData.resolutionNotes = validatedData.resolutionNotes;
    }

    if (validatedData.escalationLevel !== undefined) {
      updateData.escalationLevel = validatedData.escalationLevel;
      if (validatedData.escalationLevel > 0) {
        updateData.escalatedAt = new Date();
        updateData.escalatedBy = auth.user.id;
      }
    }

    // Update quality issue
    const updatedQualityIssue = await prisma.qualityIssue.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
            contactPerson: true,
          },
        },
        batch: {
          select: {
            id: true,
            batchNumber: true,
            receivedDate: true,
            expiryDate: true,
          },
        },
        returnItem: {
          include: {
            return: {
              select: {
                id: true,
                reason: true,
                returnDate: true,
              },
            },
          },
        },
        reporter: {
          select: {
            id: true,
            name: true,
          },
        },
        resolver: {
          select: {
            id: true,
            name: true,
          },
        },
        escalations: {
          include: {
            escalatedToUser: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(updatedQualityIssue);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error updating quality issue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
