import { NextRequest, NextResponse } from 'next/server';
import { verifyAuthToken } from '@/lib/auth-utils';
import { SupplierSelectionEngine } from '@/lib/supplier-selection-engine';

/**
 * GET /api/supplier-recommendations/metrics/[supplierId]
 * Get detailed metrics for a specific supplier
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ supplierId: string }> }
) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    const { supplierId } = await params;

    if (!supplierId) {
      return NextResponse.json(
        { error: "Supplier ID is required" },
        { status: 400 }
      );
    }

    // Get detailed supplier metrics
    const metrics = await SupplierSelectionEngine.getSupplierMetrics(supplierId);

    return NextResponse.json({
      supplierId,
      metrics,
    });
  } catch (error) {
    console.error("Error getting supplier metrics:", error);
    return NextResponse.json(
      { error: "Failed to get supplier metrics", message: (error as Error).message },
      { status: 500 }
    );
  }
}
