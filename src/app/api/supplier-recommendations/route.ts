import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { verifyAuthToken } from '@/lib/auth-utils';
import { SupplierSelectionEngine } from '@/lib/supplier-selection-engine';

// Schema for bulk supplier recommendation request
const bulkRecommendationSchema = z.object({
  items: z.array(z.object({
    productId: z.string().min(1, { message: "Product ID is required" }),
    quantity: z.number().positive({ message: "Quantity must be positive" }),
  })).min(1, { message: "At least one item is required" }),
});

// Schema for single product recommendation request
const singleRecommendationSchema = z.object({
  productId: z.string().min(1, { message: "Product ID is required" }),
  quantity: z.number().positive({ message: "Quantity must be positive" }),
});

/**
 * GET /api/supplier-recommendations
 * Get supplier recommendation for a single product
 * Query params: productId, quantity
 */
export async function GET(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    const quantityStr = searchParams.get('quantity');

    if (!productId || !quantityStr) {
      return NextResponse.json(
        { error: "productId and quantity query parameters are required" },
        { status: 400 }
      );
    }

    const quantity = parseInt(quantityStr, 10);
    if (isNaN(quantity) || quantity <= 0) {
      return NextResponse.json(
        { error: "quantity must be a positive number" },
        { status: 400 }
      );
    }

    // Get supplier recommendation
    const recommendation = await SupplierSelectionEngine.getSupplierRecommendation(
      productId,
      quantity
    );

    if (!recommendation) {
      return NextResponse.json(
        { error: "No suppliers found for this product" },
        { status: 404 }
      );
    }

    return NextResponse.json(recommendation);
  } catch (error) {
    console.error("Error getting supplier recommendation:", error);
    return NextResponse.json(
      { error: "Failed to get supplier recommendation", message: (error as Error).message },
      { status: 500 }
    );
  }
}

/**
 * POST /api/supplier-recommendations
 * Get supplier recommendations for multiple products
 */
export async function POST(request: NextRequest) {
  try {
    // Verify authentication
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: auth.error || 'Unauthorized' }, { status: 401 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = bulkRecommendationSchema.parse(body);

    // Get bulk supplier recommendations
    const recommendations = await SupplierSelectionEngine.getBulkSupplierRecommendations(
      validatedData.items
    );

    return NextResponse.json({
      recommendations,
      summary: {
        totalProducts: validatedData.items.length,
        recommendationsFound: recommendations.length,
        highConfidenceRecommendations: recommendations.filter(r => r.recommendation.confidence === 'high').length,
        mediumConfidenceRecommendations: recommendations.filter(r => r.recommendation.confidence === 'medium').length,
        lowConfidenceRecommendations: recommendations.filter(r => r.recommendation.confidence === 'low').length,
      },
    });
  } catch (error) {
    console.error("Error getting bulk supplier recommendations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to get supplier recommendations", message: (error as Error).message },
      { status: 500 }
    );
  }
}
