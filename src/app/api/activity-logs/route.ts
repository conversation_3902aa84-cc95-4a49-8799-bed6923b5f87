import { NextRequest, NextResponse } from "next/server";
import { auth, prisma } from "@/auth";
import { jwtVerify } from "jose";

// GET /api/activity-logs - Get activity logs with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Get the token from the request cookies directly
    const token = request.cookies.get("session-token");

    if (!token) {
      console.log("[API] /api/activity-logs - No session token found");
      return NextResponse.json(
        { error: "Unauthorized - No session token" },
        { status: 403 }
      );
    }

    // Verify the token
    let userRole = "";
    try {
      const { payload } = await jwtVerify(
        token.value,
        new TextEncoder().encode(process.env.NEXTAUTH_SECRET || "fallback-secret")
      );

      console.log("[API] /api/activity-logs - Token verified, user role:", payload.role);

      userRole = payload.role as string;

      // Check if user has admin role
      if (!["SUPER_ADMIN", "FINANCE_ADMIN"].includes(userRole)) {
        console.log("[API] /api/activity-logs - User does not have required role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized - Insufficient permissions" },
          { status: 403 }
        );
      }
    } catch (jwtError) {
      console.error("[API] /api/activity-logs - JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Unauthorized - Invalid token" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const userId = searchParams.get("userId");
    const action = searchParams.get("action");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    // Log all query parameters for debugging
    console.log("[API] /api/activity-logs - Query parameters:", {
      page,
      limit,
      userId,
      action,
      startDate,
      endDate,
      allParams: Object.fromEntries(searchParams.entries())
    });

    // Build filter conditions
    const where: any = {};

    if (userId) {
      where.userId = userId;
    }

    if (action) {
      where.action = action;
      console.log("[API] /api/activity-logs - Filtering by action:", action);
    }

    if (startDate || endDate) {
      try {
        // Import and use the processDateRange function for consistent date handling
        const { processDateRange } = await import("@/lib/utils");
        const { start, end } = processDateRange(startDate, endDate);

        where.timestamp = {};

        if (start) {
          where.timestamp.gte = start;
          console.log("[API] /api/activity-logs - Using start date:", start.toISOString());
        }

        if (end) {
          where.timestamp.lte = end;
          console.log("[API] /api/activity-logs - Using end date:", end.toISOString());
        }

        // Log the processed dates for debugging
        console.log("[API] /api/activity-logs - Date filter applied:", {
          startDate: startDate ? new Date(startDate).toISOString() : null,
          endDate: endDate ? new Date(endDate).toISOString() : null,
          processedStart: start?.toISOString(),
          processedEnd: end?.toISOString(),
        });
      } catch (error) {
        console.error("[API] /api/activity-logs - Error processing date range:", error);
      }
    }

    // Get total count for pagination
    const total = await prisma.activityLog.count({ where });

    // Log the where clause for debugging
    console.log("[API] /api/activity-logs - Where clause:", JSON.stringify(where, null, 2));

    // Get paginated logs
    const logs = await prisma.activityLog.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
      },
      orderBy: {
        timestamp: "desc",
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Log the first few results for debugging
    if (logs.length > 0) {
      console.log("[API] /api/activity-logs - First log timestamp:", new Date(logs[0].timestamp).toISOString());
      console.log("[API] /api/activity-logs - First log action:", logs[0].action);
      console.log("[API] /api/activity-logs - Number of logs returned:", logs.length);

      // If filtering by action, verify the actions in the results
      if (action) {
        const actionCounts = logs.reduce((acc, log) => {
          acc[log.action] = (acc[log.action] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        console.log("[API] /api/activity-logs - Action counts in results:", actionCounts);
      }
    } else {
      console.log("[API] /api/activity-logs - No logs found matching the criteria");
    }

    // Get unique actions for filtering
    const actions = await prisma.activityLog.groupBy({
      by: ["action"],
      orderBy: {
        _count: {
          action: "desc",
        },
      },
    });

    return NextResponse.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
      filters: {
        actions: actions.map(a => a.action),
      },
    });
  } catch (error) {
    console.error("Error fetching activity logs:", error);
    return NextResponse.json(
      { error: "Failed to fetch activity logs", message: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
