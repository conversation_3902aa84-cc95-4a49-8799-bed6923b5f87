import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for creating a return
const createReturnSchema = z.object({
  transactionId: z.string().min(1, 'Transaction ID is required'),
  customerId: z.string().nullable(), // Allow null for walk-in customers
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    batchId: z.string().optional(), // NEW: Optional batch ID for traceability
    quantity: z.number().positive('Quantity must be greater than 0'),
    unitPrice: z.number().positive('Unit price must be greater than 0'),
    subtotal: z.number().positive('Subtotal must be greater than 0'),
    defectType: z.string().optional(), // NEW: Type of defect if applicable
    defectNotes: z.string().optional(), // NEW: Additional notes about the defect
  })).min(1, 'At least one item is required'),
});

// GET /api/returns - List returns with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (status) {
      where.status = status;
    }
    if (search) {
      where.OR = [
        { reason: { contains: search, mode: 'insensitive' } },
        { customer: { name: { contains: search, mode: 'insensitive' } } },
        { transaction: { id: { contains: search, mode: 'insensitive' } } },
        // Also search for "walk-in" when customer is null
        ...(search.toLowerCase().includes('walk') ? [{ customerId: null }] : []),
      ];
    }

    const [returns, total] = await Promise.all([
      prisma.return.findMany({
        where,
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: true,
              batch: {
                include: {
                  productSupplier: {
                    include: {
                      supplier: true
                    }
                  }
                }
              }
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.return.count({ where }),
    ]);

    return NextResponse.json({
      returns,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching returns:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/returns - Create new return
export async function POST(request: NextRequest) {
  try {
    console.log('[API] POST /api/returns - Start');

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      console.log('[API] Authentication failed');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const canCreateReturn = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'CASHIER'].includes(auth.user.role);
    if (!canCreateReturn) {
      console.log('[API] Insufficient permissions for role:', auth.user.role);
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    console.log('[API] Request body:', JSON.stringify(body, null, 2));

    const validatedData = createReturnSchema.parse(body);
    console.log('[API] Validation successful');

    // Verify transaction exists and is completed
    const transaction = await prisma.transaction.findUnique({
      where: { id: validatedData.transactionId },
      include: {
        items: {
          include: {
            stockBatch: {
              include: {
                productSupplier: {
                  include: {
                    supplier: true
                  }
                }
              }
            }
          }
        }
      },
    });

    if (!transaction) {
      return NextResponse.json({ error: 'Transaction not found' }, { status: 404 });
    }

    if (transaction.status !== 'COMPLETED') {
      return NextResponse.json({ error: 'Can only return items from completed transactions' }, { status: 400 });
    }

    // Validate return items against transaction items and auto-populate batch information
    const enhancedReturnItems = [];
    for (const returnItem of validatedData.items) {
      const transactionItems = transaction.items.filter(item => item.productId === returnItem.productId);
      if (transactionItems.length === 0) {
        return NextResponse.json({
          error: `Product ${returnItem.productId} was not in the original transaction`
        }, { status: 400 });
      }

      // Calculate total quantity for this product in the transaction
      const totalTransactionQuantity = transactionItems.reduce((sum, item) => sum + Number(item.quantity), 0);
      if (returnItem.quantity > totalTransactionQuantity) {
        return NextResponse.json({
          error: `Return quantity exceeds original purchase quantity for product ${returnItem.productId}`
        }, { status: 400 });
      }

      // If batch ID is not provided, try to auto-populate from transaction items
      let batchId = returnItem.batchId;
      if (!batchId && transactionItems.length === 1 && transactionItems[0].batchId) {
        // If there's only one transaction item for this product and it has a batch, use it
        batchId = transactionItems[0].batchId;
        console.log(`[API] Auto-populated batch ID ${batchId} for product ${returnItem.productId}`);
      }

      enhancedReturnItems.push({
        ...returnItem,
        batchId: batchId || null,
      });
    }

    // Calculate total
    const total = enhancedReturnItems.reduce((sum, item) => sum + item.subtotal, 0);

    // Create return with items
    console.log('[API] Creating return with data:', {
      transactionId: validatedData.transactionId,
      customerId: validatedData.customerId,
      reason: validatedData.reason,
      notes: validatedData.notes,
      total,
      itemsCount: enhancedReturnItems.length,
      itemsWithBatches: enhancedReturnItems.filter(item => item.batchId).length,
    });

    const newReturn = await prisma.return.create({
      data: {
        transactionId: validatedData.transactionId,
        customerId: validatedData.customerId, // Allow null for walk-in customers
        reason: validatedData.reason,
        notes: validatedData.notes,
        total,
        items: {
          create: enhancedReturnItems,
        },
      },
      include: {
        customer: true,
        transaction: true,
        items: {
          include: {
            product: true,
            batch: {
              include: {
                productSupplier: {
                  include: {
                    supplier: true
                  }
                }
              }
            }
          },
        },
      },
    });

    // Create quality issues for items with defects
    for (const item of newReturn.items) {
      if (item.defectType && item.batch?.productSupplier?.supplier) {
        try {
          await prisma.qualityIssue.create({
            data: {
              returnItemId: item.id,
              batchId: item.batchId,
              productId: item.productId,
              supplierId: item.batch.productSupplier.supplier.id,
              issueType: mapDefectTypeToIssueType(item.defectType),
              severity: determineSeverityFromDefect(item.defectType, Number(item.quantity)),
              description: `Return defect: ${item.defectType}${item.defectNotes ? ` - ${item.defectNotes}` : ''}`,
              defectCategory: item.defectType,
              affectedQuantity: Number(item.quantity),
              reportedBy: auth.user.id,
            },
          });
          console.log(`[API] Created quality issue for defective return item ${item.id}`);
        } catch (error) {
          console.error(`[API] Error creating quality issue for return item ${item.id}:`, error);
        }
      }
    }

    console.log('[API] Return created successfully:', newReturn.id);
    return NextResponse.json(newReturn, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error('[API] Validation error:', error.errors);
      // Provide more user-friendly error messages
      const errorMessages = error.errors.map(err => {
        if (err.path.includes('reason')) {
          return 'Reason for return is required';
        }
        if (err.path.includes('items')) {
          if (err.message.includes('At least one item')) {
            return 'At least one item is required for the return';
          }
          return 'Please check the return item details';
        }
        if (err.path.includes('transactionId')) {
          return 'Transaction ID is required';
        }
        return err.message;
      });
      return NextResponse.json({
        error: 'Validation error',
        message: errorMessages[0] || 'Please check your input data',
        details: error.errors
      }, { status: 400 });
    }
    console.error('[API] Error creating return:', error);
    console.error('[API] Error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Helper function to map defect type to quality issue type
function mapDefectTypeToIssueType(defectType: string): string {
  const mapping: Record<string, string> = {
    'PACKAGING_DAMAGE': 'PACKAGING_DAMAGE',
    'PRODUCT_DEFECT': 'DEFECTIVE_PRODUCT',
    'WRONG_ITEM': 'WRONG_SPECIFICATION',
    'EXPIRED': 'EXPIRY_ISSUE',
    'CONTAMINATED': 'CONTAMINATION',
    'QUALITY_ISSUE': 'QUALITY_DEGRADATION',
    'DAMAGED': 'DEFECTIVE_PRODUCT',
    'BROKEN': 'DEFECTIVE_PRODUCT',
    'SPOILED': 'QUALITY_DEGRADATION',
    'INCORRECT_QUANTITY': 'QUANTITY_DISCREPANCY',
  };
  return mapping[defectType] || 'OTHER';
}

// Helper function to determine severity based on defect type and quantity
function determineSeverityFromDefect(defectType: string, quantity: number): string {
  // High severity defects
  const highSeverityDefects = ['CONTAMINATED', 'EXPIRED', 'SPOILED'];
  if (highSeverityDefects.includes(defectType)) {
    return 'HIGH';
  }

  // Critical severity for large quantities
  if (quantity > 10) {
    return 'CRITICAL';
  }

  // Medium severity defects
  const mediumSeverityDefects = ['PRODUCT_DEFECT', 'QUALITY_ISSUE', 'DAMAGED', 'BROKEN'];
  if (mediumSeverityDefects.includes(defectType)) {
    return 'MEDIUM';
  }

  // Default to low severity
  return 'LOW';
}
