import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for rejecting a return
const rejectReturnSchema = z.object({
  reason: z.string().min(1, 'Rejection reason is required'),
});

// POST /api/returns/[id]/reject - Reject return with reason
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body = await request.json();
    const { reason } = rejectReturnSchema.parse(body);

    // Check if return exists and is pending
    const existingReturn = await prisma.return.findUnique({
      where: { id: params.id },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'PENDING') {
      return NextResponse.json({ 
        error: 'Can only reject pending returns' 
      }, { status: 400 });
    }

    // Update return status to REJECTED with rejection reason
    const updatedReturn = await prisma.return.update({
      where: { id: params.id },
      data: { 
        status: 'REJECTED',
        notes: existingReturn.notes 
          ? `${existingReturn.notes}\n\nRejection reason: ${reason}`
          : `Rejection reason: ${reason}`,
      },
      include: {
        customer: true,
        transaction: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    return NextResponse.json(updatedReturn);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Validation error', details: error.errors }, { status: 400 });
    }
    console.error('Error rejecting return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
