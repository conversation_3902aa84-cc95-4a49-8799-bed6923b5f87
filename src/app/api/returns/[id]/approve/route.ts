import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for approval with disposition
const approveReturnSchema = z.object({
  disposition: z.enum(['RETURN_TO_STOCK', 'DO_NOT_RETURN_TO_STOCK']),
  dispositionReason: z.string().optional(),
  addToSupplierQueue: z.boolean().optional().default(false),
});

// POST /api/returns/[id]/approve - Approve return with disposition (no stock adjustment)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const { disposition, dispositionReason, addToSupplierQueue } = approveReturnSchema.parse(body);

    // Get return with items (including supplier data for potential supplier return creation)
    const returnRecord = await prisma.return.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: {
              include: {
                supplier: true,
              },
            },
          },
        },
        transaction: true,
      },
    });

    if (!returnRecord) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (returnRecord.status !== 'PENDING') {
      return NextResponse.json({ 
        error: 'Can only approve pending returns' 
      }, { status: 400 });
    }

    // Start transaction to update return status with disposition (no stock adjustment)
    const result = await prisma.$transaction(async (tx) => {
      // Update return status to APPROVED with disposition
      const updatedReturn = await tx.return.update({
        where: { id },
        data: {
          status: 'APPROVED',
          disposition: disposition,
          dispositionReason: dispositionReason,
          addToSupplierQueue: addToSupplierQueue || false,
        },
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: {
                include: {
                  supplier: true,
                },
              },
            },
          },
        },
      });

      // If disposition is DO_NOT_RETURN_TO_STOCK and user chose to add to supplier queue, create supplier return queue entries
      if (disposition === 'DO_NOT_RETURN_TO_STOCK' && addToSupplierQueue) {
        console.log('[APPROVE] Creating supplier return entries for addToSupplierQueue=true');

        // Filter items that have suppliers (use updatedReturn which has the supplier data)
        const itemsWithSuppliers = updatedReturn.items.filter(item => item.product.supplier);
        console.log(`[APPROVE] Found ${itemsWithSuppliers.length} items with suppliers out of ${updatedReturn.items.length} total items`);

        if (itemsWithSuppliers.length === 0) {
          console.log('[APPROVE] No items with suppliers found, skipping supplier return creation');
          return updatedReturn;
        }

        // Group items by supplier
        const itemsBySupplier = new Map();

        for (const item of itemsWithSuppliers) {
          if (item.product.supplier) {
            const supplierId = item.product.supplier.id;
            if (!itemsBySupplier.has(supplierId)) {
              itemsBySupplier.set(supplierId, {
                supplier: item.product.supplier,
                items: [],
              });
            }
            itemsBySupplier.get(supplierId).items.push(item);
          }
        }

        console.log(`[APPROVE] Found ${itemsBySupplier.size} suppliers with items to return`);

        // Create supplier return entries for each supplier
        const supplierReturns = [];
        for (const [supplierId, supplierData] of itemsBySupplier) {
          const supplierReturnTotal = supplierData.items.reduce(
            (sum: number, item: any) => sum + Number(item.subtotal),
            0
          );

          console.log(`[APPROVE] Creating supplier return for supplier ${supplierId} with total ${supplierReturnTotal}`);

          const supplierReturn = await tx.supplierReturn.create({
            data: {
              supplierId: supplierId,
              reason: `Defective items from customer return: ${returnRecord.reason}`,
              total: supplierReturnTotal,
              status: 'PENDING',
              notes: `Auto-generated from customer return #${returnRecord.id}. Disposition reason: ${dispositionReason || 'Not specified'}`,
            },
          });

          console.log(`[APPROVE] Created supplier return ${supplierReturn.id}`);

          // Create supplier return items
          for (const item of supplierData.items) {
            await tx.supplierReturnItem.create({
              data: {
                supplierReturnId: supplierReturn.id,
                productId: item.productId,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                subtotal: item.subtotal,
              },
            });
          }

          console.log(`[APPROVE] Created ${supplierData.items.length} supplier return items`);
          supplierReturns.push(supplierReturn);
        }

        // Link the customer return to the first supplier return (if multiple suppliers, they're all related)
        if (supplierReturns.length > 0) {
          console.log(`[APPROVE] Linking customer return to supplier return ${supplierReturns[0].id}`);
          await tx.return.update({
            where: { id },
            data: { supplierReturnQueueId: supplierReturns[0].id },
          });
        }
      }

      return updatedReturn;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error approving return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
