import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// POST /api/returns/[id]/complete - Mark return as completed
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Check if return exists and is approved
    const existingReturn = await prisma.return.findUnique({
      where: { id: params.id },
      include: {
        transaction: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: 'Can only complete approved returns' 
      }, { status: 400 });
    }

    // Start transaction to update return and handle stock adjustments based on disposition
    const result = await prisma.$transaction(async (tx) => {
      // Update return status to COMPLETED
      const updatedReturn = await tx.return.update({
        where: { id: params.id },
        data: { status: 'COMPLETED' },
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      // Handle stock adjustments based on disposition
      if (existingReturn.disposition === 'RETURN_TO_STOCK') {
        // Add items back to inventory
        for (const item of existingReturn.items) {
          // Update store stock (add returned quantity back)
          const storeStock = await tx.storeStock.findUnique({
            where: { productId: item.productId },
          });

          if (storeStock) {
            await tx.storeStock.update({
              where: { productId: item.productId },
              data: {
                quantity: { increment: item.quantity },
                lastUpdated: new Date(),
              },
            });

            // Create stock adjustment record
            await tx.stockAdjustment.create({
              data: {
                productId: item.productId,
                storeStockId: storeStock.id,
                previousQuantity: storeStock.quantity,
                newQuantity: storeStock.quantity.add(item.quantity),
                adjustmentQuantity: item.quantity,
                reason: 'RETURN',
                notes: `Return completed - returned to stock: ${existingReturn.reason}`,
                userId: auth.user.id,
              },
            });

            // Create stock history record
            await tx.stockHistory.create({
              data: {
                productId: item.productId,
                storeStockId: storeStock.id,
                previousQuantity: storeStock.quantity,
                newQuantity: storeStock.quantity.add(item.quantity),
                changeQuantity: item.quantity,
                source: 'RETURN',
                referenceId: existingReturn.id,
                referenceType: 'Return',
                notes: `Return completed - returned to stock: ${existingReturn.reason}`,
                userId: auth.user.id,
              },
            });
          }
        }
      }
      // If disposition is DO_NOT_RETURN_TO_STOCK, items are already in supplier return queue
      // No stock adjustment needed

      // Check if all items from the original transaction have been returned
      const allReturns = await tx.return.findMany({
        where: {
          transactionId: existingReturn.transactionId,
          status: 'COMPLETED',
        },
        include: {
          items: true,
        },
      });

      const originalTransaction = await tx.transaction.findUnique({
        where: { id: existingReturn.transactionId },
        include: { items: true },
      });

      if (originalTransaction) {
        // Calculate total returned quantities for each product
        const returnedQuantities: Record<string, number> = {};
        allReturns.forEach(returnRecord => {
          returnRecord.items.forEach(item => {
            returnedQuantities[item.productId] = 
              (returnedQuantities[item.productId] || 0) + Number(item.quantity);
          });
        });

        // Check if all items have been fully returned
        const allItemsReturned = originalTransaction.items.every(item => {
          const returnedQty = returnedQuantities[item.productId] || 0;
          return returnedQty >= Number(item.quantity);
        });

        // Update transaction status if all items returned
        if (allItemsReturned) {
          await tx.transaction.update({
            where: { id: existingReturn.transactionId },
            data: { status: 'RETURNED' },
          });
        }
      }

      return updatedReturn;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error completing return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
