import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';

const prisma = new PrismaClient();

// POST /api/returns/[id]/add-to-supplier-queue - Add completed return to supplier return queue
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    console.log(`[ADD_TO_SUPPLIER_QUEUE] Processing return ${id}`);

    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Check if return exists and is completed with DO_NOT_RETURN_TO_STOCK disposition
    const existingReturn = await prisma.return.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            product: {
              include: {
                productSuppliers: {
                  where: {
                    isActive: true
                  },
                  include: {
                    supplier: true
                  }
                },
              },
            },
          },
        },
      },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'COMPLETED') {
      return NextResponse.json({ 
        error: 'Can only add completed returns to supplier queue' 
      }, { status: 400 });
    }

    if (existingReturn.disposition !== 'DO_NOT_RETURN_TO_STOCK') {
      return NextResponse.json({ 
        error: 'Can only add returns with DO_NOT_RETURN_TO_STOCK disposition to supplier queue' 
      }, { status: 400 });
    }

    if (existingReturn.supplierReturnQueueId) {
      return NextResponse.json({ 
        error: 'Return is already added to supplier return queue' 
      }, { status: 400 });
    }

    // Note: Supplier check updated for new ProductSupplier relationship model
    // Check if any items have suppliers via ProductSupplier relationships
    const itemsWithSuppliers = existingReturn.items.filter(item =>
      item.product.productSuppliers && item.product.productSuppliers.length > 0
    );
    if (itemsWithSuppliers.length === 0) {
      return NextResponse.json({
        error: 'No items in this return have associated suppliers'
      }, { status: 400 });
    }

    const result = await prisma.$transaction(async (tx) => {
      // Group items by supplier
      const itemsBySupplier = new Map();

      for (const item of itemsWithSuppliers) {
        // Use ProductSupplier relationships to group by supplier
        if (item.product.productSuppliers && item.product.productSuppliers.length > 0) {
          // Use the preferred supplier or first supplier if no preferred
          const preferredSupplier = item.product.productSuppliers.find(ps => ps.isPreferred) ||
                                   item.product.productSuppliers[0];

          const supplierId = preferredSupplier.supplier.id;
          if (!itemsBySupplier.has(supplierId)) {
            itemsBySupplier.set(supplierId, {
              supplier: preferredSupplier.supplier,
              items: [],
            });
          }
          itemsBySupplier.get(supplierId).items.push(item);
        }
      }

      // Create supplier return entries for each supplier
      const supplierReturns = [];
      for (const [supplierId, supplierData] of itemsBySupplier) {
        const supplierReturnTotal = supplierData.items.reduce(
          (sum: number, item: any) => sum + Number(item.subtotal),
          0
        );

        const supplierReturn = await tx.supplierReturn.create({
          data: {
            supplierId: supplierId,
            reason: `Defective items from customer return: ${existingReturn.reason}`,
            total: supplierReturnTotal,
            status: 'PENDING',
            notes: `Added post-completion from customer return #${existingReturn.id}. Disposition reason: ${existingReturn.dispositionReason || 'Not specified'}`,
          },
        });

        // Create supplier return items
        for (const item of supplierData.items) {
          await tx.supplierReturnItem.create({
            data: {
              supplierReturnId: supplierReturn.id,
              productId: item.productId,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              subtotal: item.subtotal,
            },
          });
        }

        supplierReturns.push(supplierReturn);
      }

      // Update the return to link to the first supplier return (if multiple suppliers, they're all related)
      const updatedReturn = await tx.return.update({
        where: { id },
        data: {
          supplierReturnQueueId: supplierReturns[0]?.id,
          addToSupplierQueue: true,
        },
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: {
                include: {
                  productSuppliers: {
                    where: {
                      isActive: true
                    },
                    include: {
                      supplier: true
                    }
                  },
                },
              },
            },
          },
          supplierReturnQueue: {
            include: {
              supplier: true,
            },
          },
        },
      });

      return {
        return: updatedReturn,
        supplierReturns: supplierReturns,
      };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error adding return to supplier queue:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
