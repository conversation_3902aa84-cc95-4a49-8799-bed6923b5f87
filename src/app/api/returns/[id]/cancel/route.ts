import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { verifyAuthToken } from '@/lib/auth-utils';
import { z } from 'zod';

// Create a singleton instance of PrismaClient to prevent too many connections
const globalForPrisma = globalThis as unknown as { prisma: PrismaClient };
const prisma = globalForPrisma.prisma || new PrismaClient();
if (process.env.NODE_ENV !== "production") globalForPrisma.prisma = prisma;

// Schema for cancellation reason
const cancelReturnSchema = z.object({
  reason: z.string().min(1, 'Cancellation reason is required'),
});

// POST /api/returns/[id]/cancel - Cancel approved return before completion
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await verifyAuthToken(request);
    if (!auth.authenticated || !auth.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check permissions
    const hasPermission = ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'].includes(auth.user.role);
    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const { reason } = cancelReturnSchema.parse(body);

    // Check if return exists and is approved
    const existingReturn = await prisma.return.findUnique({
      where: { id: params.id },
      include: {
        items: {
          include: {
            product: true,
          },
        },
      },
    });

    if (!existingReturn) {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    if (existingReturn.status !== 'APPROVED') {
      return NextResponse.json({ 
        error: 'Can only cancel approved returns that have not been completed' 
      }, { status: 400 });
    }

    // Start transaction to cancel return and clean up supplier return queue if needed
    const result = await prisma.$transaction(async (tx) => {
      // If there's a linked supplier return queue, remove it
      if (existingReturn.supplierReturnQueueId) {
        // Delete supplier return items first
        await tx.supplierReturnItem.deleteMany({
          where: { supplierReturnId: existingReturn.supplierReturnQueueId },
        });

        // Delete supplier return
        await tx.supplierReturn.delete({
          where: { id: existingReturn.supplierReturnQueueId },
        });
      }

      // Update return status to REJECTED with cancellation reason
      const updatedReturn = await tx.return.update({
        where: { id: params.id },
        data: { 
          status: 'REJECTED',
          notes: existingReturn.notes 
            ? `${existingReturn.notes}\n\nCancelled after approval: ${reason}`
            : `Cancelled after approval: ${reason}`,
          supplierReturnQueueId: null,
        },
        include: {
          customer: true,
          transaction: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      return updatedReturn;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error cancelling return:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
