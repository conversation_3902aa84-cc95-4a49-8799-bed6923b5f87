"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useClientAuth } from "@/hooks/use-client-auth";
import { useSettings } from "@/contexts/settings-context";
import { useStoreInfo } from "@/contexts/store-info-context";
import { MessageSquare, Store, AlertCircle, Bell, ChevronRight } from "lucide-react";
import Link from "next/link";

export default function SettingsPage() {
  const { user } = useClientAuth();
  const { settings, updateSettings, isLoading, error } = useSettings();
  const { storeInfo, updateStoreInfo, isLoading: storeLoading, error: storeError } = useStoreInfo();
  const [success, setSuccess] = useState<string | null>(null);
  const [storeSuccess, setStoreSuccess] = useState<string | null>(null);
  const [localSettings, setLocalSettings] = useState<Record<string, string>>({});
  const [localStoreInfo, setLocalStoreInfo] = useState({
    storeName: "",
    phone: "",
    address: "",
    email: "",
    website: "",
    taxId: "",
  });
  const [isSaving, setIsSaving] = useState(false);
  const [isSavingStore, setIsSavingStore] = useState(false);

  // Check if user is super admin or admin
  const isSuperAdmin = user?.role === "SUPER_ADMIN";
  const isAdmin = user?.role === "ADMIN" || user?.role === "SUPER_ADMIN";

  // Initialize local settings from global settings
  useEffect(() => {
    console.log("Settings changed:", settings);
    setLocalSettings({ ...settings });
  }, [settings]);

  // Initialize local store info from global store info
  useEffect(() => {
    if (storeInfo) {
      setLocalStoreInfo({
        storeName: storeInfo.storeName || "",
        phone: storeInfo.phone || "",
        address: storeInfo.address || "",
        email: storeInfo.email || "",
        website: storeInfo.website || "",
        taxId: storeInfo.taxId || "",
      });
    }
  }, [storeInfo]);

  // Handle toggle change
  const handleToggleChange = (key: string, value: boolean) => {
    console.log("Toggle changed:", key, value);
    const newSettings = {
      ...localSettings,
      [key]: value.toString(),
    };
    console.log("New local settings:", newSettings);
    setLocalSettings(newSettings);
  };

  // Handle store info input change
  const handleStoreInfoChange = (field: string, value: string) => {
    setLocalStoreInfo((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    try {
      setIsSaving(true);
      setSuccess(null);

      await updateSettings(localSettings);

      setSuccess("Settings updated successfully");
    } catch (err: any) {
      console.error("Error saving settings:", err);
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save store info
  const handleSaveStoreInfo = async () => {
    try {
      setIsSavingStore(true);
      setStoreSuccess(null);

      // Validate required fields
      if (!localStoreInfo.storeName.trim()) {
        throw new Error("Store name is required");
      }

      // Validate email format if provided
      if (localStoreInfo.email && localStoreInfo.email.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(localStoreInfo.email)) {
          throw new Error("Please enter a valid email address");
        }
      }

      // Validate website URL if provided
      if (localStoreInfo.website && localStoreInfo.website.trim()) {
        try {
          new URL(localStoreInfo.website);
        } catch {
          throw new Error("Please enter a valid website URL");
        }
      }

      await updateStoreInfo(localStoreInfo);

      setStoreSuccess("Store information updated successfully");
    } catch (err: any) {
      console.error("Error saving store info:", err);
    } finally {
      setIsSavingStore(false);
    }
  };

  // If not super admin, show limited view
  if (!isSuperAdmin) {
    return (
      <MainLayout>
        <PageHeader title="Settings" description="View and manage system settings" />

        <Alert variant="info" className="mb-6">
          <AlertTitle>Limited Access</AlertTitle>
          <AlertDescription>
            Only Super Admins can modify system settings. Please contact your administrator for any
            changes.
          </AlertDescription>
        </Alert>

        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
            <CardDescription>General information about the system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">Next POS</h3>
                <p className="text-sm text-muted-foreground">
                  A modern point of sale and inventory management system
                </p>
              </div>
              <div>
                <h4 className="font-medium">Version</h4>
                <p className="text-sm text-muted-foreground">1.0.0</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader title="Settings" description="Configure system settings and optional features" />

      {error && (
        <Alert variant="destructive" className="mb-4">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="success" className="mb-4">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      {storeError && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Store Information Error</AlertTitle>
          <AlertDescription>{storeError}</AlertDescription>
        </Alert>
      )}

      {storeSuccess && (
        <Alert variant="success" className="mb-4">
          <AlertTitle>Success</AlertTitle>
          <AlertDescription>{storeSuccess}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6">
        {/* Quick Access to Notification Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Preferences
            </CardTitle>
            <CardDescription>
              Customize how and when you receive notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">
                  Configure notification delivery methods, frequency, and quiet hours for different types of events.
                </p>
              </div>
              <Button asChild variant="outline">
                <Link href="/settings/notifications" className="flex items-center gap-2">
                  Configure
                  <ChevronRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Optional Features</CardTitle>
            <CardDescription>Enable or disable optional system features</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between space-x-4">
              <div className="flex items-center space-x-3">
                <MessageSquare className="h-5 w-5 text-muted-foreground" />
                <div>
                  <Label htmlFor="enable-chat" className="text-base">
                    Chat System
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Enable the chat system for internal communication between users
                  </p>
                </div>
              </div>
              <Switch
                id="enable-chat"
                checked={localSettings.enableChat === "true"}
                onCheckedChange={(checked: boolean) => handleToggleChange("enableChat", checked)}
                disabled={isLoading || isSaving}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button onClick={handleSaveSettings} disabled={isLoading || isSaving}>
              {isSaving ? "Saving..." : "Save Settings"}
            </Button>
          </CardFooter>
        </Card>

        {/* Store Information Card - Only show to admins */}
        {isAdmin && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Store className="h-5 w-5" />
                <span>Store Information</span>
              </CardTitle>
              <CardDescription>
                Configure your store details for receipts and system display
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="store-name">Store Name *</Label>
                  <Input
                    id="store-name"
                    value={localStoreInfo.storeName}
                    onChange={(e) => handleStoreInfoChange("storeName", e.target.value)}
                    placeholder="Enter store name"
                    disabled={storeLoading || isSavingStore}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="store-phone">Phone Number</Label>
                  <Input
                    id="store-phone"
                    value={localStoreInfo.phone}
                    onChange={(e) => handleStoreInfoChange("phone", e.target.value)}
                    placeholder="Enter phone number"
                    disabled={storeLoading || isSavingStore}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="store-email">Email Address</Label>
                  <Input
                    id="store-email"
                    type="email"
                    value={localStoreInfo.email}
                    onChange={(e) => handleStoreInfoChange("email", e.target.value)}
                    placeholder="Enter email address"
                    disabled={storeLoading || isSavingStore}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="store-website">Website</Label>
                  <Input
                    id="store-website"
                    type="url"
                    value={localStoreInfo.website}
                    onChange={(e) => handleStoreInfoChange("website", e.target.value)}
                    placeholder="https://example.com"
                    disabled={storeLoading || isSavingStore}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="store-tax-id">Tax ID</Label>
                  <Input
                    id="store-tax-id"
                    value={localStoreInfo.taxId}
                    onChange={(e) => handleStoreInfoChange("taxId", e.target.value)}
                    placeholder="Enter tax identification number"
                    disabled={storeLoading || isSavingStore}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="store-address">Address</Label>
                <Textarea
                  id="store-address"
                  value={localStoreInfo.address}
                  onChange={(e) => handleStoreInfoChange("address", e.target.value)}
                  placeholder="Enter store address"
                  rows={3}
                  disabled={storeLoading || isSavingStore}
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleSaveStoreInfo}
                disabled={storeLoading || isSavingStore || !localStoreInfo.storeName.trim()}
              >
                {isSavingStore ? "Saving..." : "Save Store Information"}
              </Button>
            </CardFooter>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle>System Information</CardTitle>
            <CardDescription>General information about the system</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h3 className="text-lg font-medium">Next POS</h3>
                <p className="text-sm text-muted-foreground">
                  A modern point of sale and inventory management system
                </p>
              </div>
              <div>
                <h4 className="font-medium">Version</h4>
                <p className="text-sm text-muted-foreground">1.0.0</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
