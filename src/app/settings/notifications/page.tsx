"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { NotificationPreferences } from "@/components/notifications/NotificationPreferences";
import { useClientAuth } from "@/hooks/use-client-auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Bell, Info } from "lucide-react";
import Link from "next/link";

export default function NotificationPreferencesPage() {
  const { user, loading } = useClientAuth();

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (!user) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Access Denied
              </CardTitle>
              <CardDescription>
                You must be logged in to access notification preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button asChild className="w-full">
                <Link href="/login">Login</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <PageHeader
          title="Notification Preferences"
          description="Customize how and when you receive notifications"
        >
          <Button variant="outline" asChild>
            <Link href="/settings" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Settings
            </Link>
          </Button>
        </PageHeader>

        {/* Info Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Configure your notification preferences to control which notifications you receive and how they are delivered. 
            Changes are saved automatically and take effect immediately.
          </AlertDescription>
        </Alert>

        {/* User Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Notification Settings for {user.name}
            </CardTitle>
            <CardDescription>
              Role: {user.role} • Email: {user.email}
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Notification Preferences Component */}
        <NotificationPreferences />
      </div>
    </MainLayout>
  );
}
