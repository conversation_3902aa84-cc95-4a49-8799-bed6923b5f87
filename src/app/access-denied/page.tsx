"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Home } from "lucide-react";
import Link from "next/link";
import { useClientAuth } from "@/hooks/use-client-auth";

export default function AccessDeniedPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useClientAuth();
  const [countdown, setCountdown] = useState(5);

  // Get resource and required role from URL params
  const resource = searchParams.get("resource") || "page";
  const requiredRole = searchParams.get("requiredRole") || "appropriate role";

  // Format resource name for display
  const resourceName = resource === "pos" ? "Point of Sale (POS)" : resource;

  // Redirect after countdown
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    } else {
      // Redirect to dashboard or home based on user role
      if (user?.role === "CASHIER") {
        router.push("/transactions");
      } else {
        router.push("/dashboard");
      }
    }
  }, [countdown, router, user?.role]);

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-red-600">Access Denied</CardTitle>
          <CardDescription>
            You don't have permission to access this resource.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="rounded-md bg-red-50 p-4 text-sm text-red-800">
            <p>
              The <strong>{resourceName}</strong> can only be accessed by users with the{" "}
              <strong>{requiredRole}</strong> role.
            </p>
            <p className="mt-2">
              Your current role is <strong>{user?.role || "Unknown"}</strong>.
            </p>
          </div>
          <p className="text-center text-sm text-muted-foreground">
            Redirecting in {countdown} seconds...
          </p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Dashboard
            </Link>
          </Button>
          <Button asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Home
            </Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
