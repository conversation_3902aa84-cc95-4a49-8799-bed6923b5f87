@import "tailwindcss";
@import "tw-animate-css";
@import "./fonts.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    overflow-x: hidden;
    margin-right: calc(-1 * (100vw - 100%));
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Toast notifications styling */
.toast-item {
  margin-bottom: 8px !important;
  z-index: 1000 !important;
}

/* Center-top toast positioning adjustments - High specificity to override defaults */
[data-sonner-toaster][data-position="top-center"],
[data-sonner-toaster][data-position*="top-center"],
.toaster[data-position="top-center"],
.toaster[data-position*="top-center"] {
  position: fixed !important;
  top: 80px !important; /* Position below header */
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 420px !important;
  z-index: 1000 !important;
}

/* Force override any conflicting positioning */
[data-sonner-toaster]:not([data-position*="right"]):not([data-position*="left"]):not([data-position*="bottom"]) {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 768px) {
  [data-sonner-toaster][data-position="top-center"],
  [data-sonner-toaster][data-position*="top-center"],
  .toaster[data-position="top-center"],
  .toaster[data-position*="top-center"] {
    top: 70px !important; /* Slightly higher on mobile */
    max-width: calc(100vw - 32px) !important; /* Account for mobile padding */
    padding: 0 16px !important;
  }

  [data-sonner-toaster]:not([data-position*="right"]):not([data-position*="left"]):not([data-position*="bottom"]) {
    top: 70px !important;
    max-width: calc(100vw - 32px) !important;
  }
}

html {
  overflow-x: hidden;
  margin-right: calc(-1 * (100vw - 100%));
}

/* Ensure error toasts are always on top */
[data-sonner-toast][data-type="error"] {
  z-index: 1001 !important;
}

/* Add spacing between toasts */
[data-sonner-toaster] [data-sonner-toast] + [data-sonner-toast] {
  margin-top: 8px !important;
}

/* Additional fallback rules to ensure center positioning */
[data-sonner-toaster] {
  position: fixed !important;
}

/* Override any potential right positioning */
[data-sonner-toaster][data-position="top-right"] {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 420px !important;
}

/* Ensure all toasts use center positioning regardless of original position */
body [data-sonner-toaster] {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 420px !important;
}

/* Additional high-specificity rules to override any potential conflicts */
html body [data-sonner-toaster],
html body .toaster {
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  right: auto !important;
  transform: translateX(-50%) !important;
  width: auto !important;
  max-width: 420px !important;
  z-index: 1000 !important;
}

/* Mobile responsive override */
@media (max-width: 768px) {
  html body [data-sonner-toaster],
  html body .toaster {
    top: 70px !important;
    max-width: calc(100vw - 32px) !important;
  }
}

/* Hide scrollbar but keep functionality */
.scrollbar-none {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

.scrollbar-none::-webkit-scrollbar {
  display: none;             /* Chrome, Safari and Opera */
}

/* Prevent horizontal overflow on main content areas only */
main, .main-content, [data-main-content] {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Table responsive behavior */
.table-container {
  width: 100% !important;
  overflow-x: auto !important;
  max-width: 100% !important;
}

/* Ensure cards and containers respect viewport width - exclude sidebar */
.card:not(.sidebar-card), [data-card]:not(.sidebar-card) {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Grid containers should not overflow - exclude sidebar navigation */
main .grid,
main [class*="grid-cols"],
.content-area .grid,
.content-area [class*="grid-cols"] {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

/* Flex containers should wrap appropriately - exclude sidebar and main layout */
main .flex:not(.layout-flex),
.content-area .flex:not(.layout-flex) {
  flex-wrap: wrap !important;
}

/* Text truncation for long content */
.truncate-responsive {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  max-width: 100% !important;
}

/* Specific rules for supplier detail page content */
.supplier-detail-content {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

.supplier-detail-content .grid,
.supplier-detail-content [class*="grid-cols"] {
  max-width: 100% !important;
  overflow-x: hidden !important;
}

.supplier-detail-content .flex {
  flex-wrap: wrap !important;
}

/* Sidebar specific rules to prevent interference from global styles */
.sidebar, [data-sidebar] {
  flex-shrink: 0 !important;
  width: 240px !important;
  max-width: 240px !important;
  min-width: 240px !important;
  overflow-x: visible !important;
}

/* Ensure main layout flex containers maintain their intended behavior */
.layout-flex {
  flex-wrap: nowrap !important;
}

/* Prevent global overflow rules from affecting the main layout */
.layout-flex, .layout-flex * {
  max-width: none !important;
}

/* Ensure sidebar navigation items don't wrap */
.sidebar .flex, .sidebar [class*="flex"] {
  flex-wrap: nowrap !important;
}
