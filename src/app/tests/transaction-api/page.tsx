"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { runAllTransactionTests } from "@/utils/transactionApiTests";
import { CheckCircle, XCircle, ChevronDown, ChevronRight, Clock } from "lucide-react";

interface TestResult {
  name: string;
  success: boolean;
  status: number;
  message: string;
  duration: number;
  response?: any;
  error?: any;
}

export default function TransactionApiTestsPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedTests, setExpandedTests] = useState<Record<string, boolean>>({});

  const runTests = async () => {
    setIsLoading(true);
    setTestResults([]);

    try {
      const results = await runAllTransactionTests();
      setTestResults(results);
    } catch (error) {
      console.error("Error running tests:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTestDetails = (testName: string) => {
    setExpandedTests((prev) => ({
      ...prev,
      [testName]: !prev[testName],
    }));
  };

  // Calculate test summary
  const totalTests = testResults.length;
  const passedTests = testResults.filter((test) => test.success).length;
  const failedTests = totalTests - passedTests;

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-2">Transaction API Tests</h1>
      <p className="text-muted-foreground mb-6">Test the transaction management API routes</p>

      <Button
        onClick={runTests}
        disabled={isLoading}
        className="mb-8 bg-black text-white hover:bg-gray-800"
      >
        {isLoading ? (
          <>
            <Clock className="mr-2 h-4 w-4 animate-spin" />
            Running Tests...
          </>
        ) : (
          "Run API Tests"
        )}
      </Button>

      {testResults.length > 0 && (
        <>
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Test Summary</CardTitle>
              <CardDescription>Results of the API tests</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="bg-muted p-4 rounded-md text-center">
                  <div className="text-3xl font-bold">{totalTests}</div>
                  <div className="text-muted-foreground">Total Tests</div>
                </div>
                <div className="bg-green-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-green-600">{passedTests}</div>
                  <div className="text-green-600">Passed</div>
                </div>
                <div className="bg-red-50 p-4 rounded-md text-center">
                  <div className="text-3xl font-bold text-red-600">{failedTests}</div>
                  <div className="text-red-600">Failed</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
              <CardDescription>Detailed results of each API test</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="all">
                <TabsList className="mb-4">
                  <TabsTrigger value="all">All ({totalTests})</TabsTrigger>
                  <TabsTrigger value="passed">Passed ({passedTests})</TabsTrigger>
                  <TabsTrigger value="failed">Failed ({failedTests})</TabsTrigger>
                </TabsList>

                <TabsContent value="all">
                  {renderTestList(testResults, expandedTests, toggleTestDetails)}
                </TabsContent>

                <TabsContent value="passed">
                  {renderTestList(
                    testResults.filter((test) => test.success),
                    expandedTests,
                    toggleTestDetails
                  )}
                </TabsContent>

                <TabsContent value="failed">
                  {renderTestList(
                    testResults.filter((test) => !test.success),
                    expandedTests,
                    toggleTestDetails
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}

function renderTestList(
  tests: TestResult[],
  expandedTests: Record<string, boolean>,
  toggleTestDetails: (testName: string) => void
) {
  if (tests.length === 0) {
    return <p className="text-muted-foreground">No tests in this category</p>;
  }

  return (
    <div className="space-y-2">
      {tests.map((test, index) => (
        <div key={index} className="border rounded-md overflow-hidden">
          <div
            className={`flex items-center justify-between p-3 cursor-pointer ${
              test.success ? "bg-green-50" : "bg-red-50"
            }`}
            onClick={() => toggleTestDetails(test.name)}
          >
            <div className="flex items-center">
              {test.success ? (
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <span className="font-medium">{test.name}</span>
            </div>
            <div className="flex items-center">
              <span className="text-sm text-muted-foreground mr-2">{test.duration}ms</span>
              {expandedTests[test.name] ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </div>
          </div>

          {expandedTests[test.name] && (
            <div className="p-3 bg-white border-t">
              <h4 className="font-medium mb-2">Response Details</h4>
              <div className="text-sm mb-2">
                <span className="font-medium">Status:</span> {test.status}
              </div>
              <div className="text-sm mb-2">
                <span className="font-medium">Message:</span> {test.message}
              </div>
              <Separator className="my-2" />
              <h4 className="font-medium mb-2">Response Data</h4>
              <pre className="bg-muted p-2 rounded-md text-xs overflow-auto max-h-60">
                {JSON.stringify(test.response || test.error, null, 2)}
              </pre>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
