"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  ArrowLeft,
  AlertCircle,
  ArrowUpCircle,
  CheckCircle2,
  Clock,
  User,
  Building2,
  Package,
  Calendar,
  Edit,
  Save,
  X
} from "lucide-react";
import { toast } from "sonner";

interface QualityIssue {
  id: string;
  issueType: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED' | 'ESCALATED';
  defectCategory?: string;
  affectedQuantity: number;
  reportedAt: string;
  resolvedAt?: string;
  escalationLevel: number;
  resolutionNotes?: string;
  product: {
    id: string;
    name: string;
    sku: string;
    barcode?: string;
  };
  supplier: {
    id: string;
    name: string;
    contactPerson: string;
    phone?: string;
    email?: string;
  };
  batch?: {
    id: string;
    batchNumber: string;
    receivedDate: string;
    expiryDate?: string;
    productSupplier?: {
      supplierProductCode?: string;
      supplierProductName?: string;
      purchasePrice: number;
    };
  };
  returnItem?: {
    return: {
      id: string;
      reason: string;
      returnDate: string;
      customer?: {
        id: string;
        name?: string;
        phone?: string;
      };
      transaction: {
        id: string;
        transactionDate: string;
      };
    };
  };
  reporter?: {
    id: string;
    name: string;
    role: string;
  };
  resolver?: {
    id: string;
    name: string;
    role: string;
  };
  escalator?: {
    id: string;
    name: string;
    role: string;
  };
  escalations?: Array<{
    escalationLevel: number;
    escalatedAt: string;
    escalationReason: string;
    status: string;
    escalatedToUser?: {
      id: string;
      name: string;
      role: string;
    };
    escalatedByUser?: {
      id: string;
      name: string;
      role: string;
    };
    responder?: {
      id: string;
      name: string;
      role: string;
    };
    responseNotes?: string;
    respondedAt?: string;
  }>;
  improvements?: Array<{
    id: string;
    title: string;
    status: string;
    assignee?: {
      id: string;
      name?: string;
    };
  }>;
}

export default function QualityIssueDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [qualityIssue, setQualityIssue] = useState<QualityIssue | null>(null);
  const [loading, setLoading] = useState(true);
  const [editing, setEditing] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [editData, setEditData] = useState({
    status: "",
    resolutionNotes: "",
  });

  // Unwrap params using React.use()
  const { id } = use(params);

  useEffect(() => {
    fetchQualityIssue();
  }, [id]);

  const fetchQualityIssue = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/quality-issues/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality issue');
      }
      const data = await response.json();
      setQualityIssue(data);
      setEditData({
        status: data.status,
        resolutionNotes: data.resolutionNotes || "",
      });
    } catch (error) {
      console.error('Error fetching quality issue:', error);
      toast.error('Failed to load quality issue');
    } finally {
      setLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/quality-issues/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      });

      if (!response.ok) {
        throw new Error('Failed to update quality issue');
      }

      const updatedIssue = await response.json();
      setQualityIssue(updatedIssue);
      setEditing(false);
      toast.success('Quality issue updated successfully');
    } catch (error) {
      console.error('Error updating quality issue:', error);
      toast.error('Failed to update quality issue');
    } finally {
      setUpdating(false);
    }
  };

  const handleEscalate = () => {
    router.push(`/quality/issues/${id}/escalate`);
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ESCALATED': return 'bg-red-100 text-red-800 border-red-200';
      case 'RESOLVED': return 'bg-green-100 text-green-800 border-green-200';
      case 'CLOSED': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatIssueType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  if (!qualityIssue) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold">Quality Issue Not Found</h2>
          <p className="text-muted-foreground mt-2">The requested quality issue could not be found.</p>
          <Button className="mt-4" onClick={() => router.push('/quality/issues')}>
            Back to Quality Issues
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Quality Issue Details"
        description={`${formatIssueType(qualityIssue.issueType)} - ${qualityIssue.product.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            {!editing && (
              <>
                <Button variant="outline" onClick={() => setEditing(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
                {qualityIssue.status !== 'ESCALATED' && qualityIssue.status !== 'CLOSED' && (
                  <Button variant="outline" onClick={handleEscalate}>
                    <ArrowUpCircle className="mr-2 h-4 w-4" />
                    Escalate
                  </Button>
                )}
              </>
            )}
            {editing && (
              <>
                <Button variant="outline" onClick={() => setEditing(false)}>
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button onClick={handleUpdate} disabled={updating}>
                  {updating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save
                    </>
                  )}
                </Button>
              </>
            )}
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Issue Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Issue Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge className={getSeverityColor(qualityIssue.severity)}>
                  {qualityIssue.severity}
                </Badge>
                <Badge className={getStatusColor(qualityIssue.status)}>
                  {qualityIssue.status.replace('_', ' ')}
                </Badge>
                {qualityIssue.escalationLevel > 0 && (
                  <Badge variant="outline">
                    <ArrowUpCircle className="mr-1 h-3 w-3" />
                    Escalation Level {qualityIssue.escalationLevel}
                  </Badge>
                )}
              </div>

              <div>
                <h4 className="font-semibold mb-2">Description</h4>
                <p className="text-muted-foreground">{qualityIssue.description}</p>
              </div>

              {qualityIssue.defectCategory && (
                <div>
                  <h4 className="font-semibold mb-2">Defect Category</h4>
                  <p className="text-muted-foreground">{qualityIssue.defectCategory}</p>
                </div>
              )}

              <div>
                <h4 className="font-semibold mb-2">Affected Quantity</h4>
                <p className="text-muted-foreground">{qualityIssue.affectedQuantity} units</p>
              </div>

              {editing ? (
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Status</label>
                    <Select value={editData.status} onValueChange={(value) => setEditData(prev => ({ ...prev, status: value }))}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="OPEN">Open</SelectItem>
                        <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                        <SelectItem value="RESOLVED">Resolved</SelectItem>
                        <SelectItem value="CLOSED">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <label className="text-sm font-medium">Resolution Notes</label>
                    <Textarea
                      className="mt-1"
                      placeholder="Add resolution notes..."
                      value={editData.resolutionNotes}
                      onChange={(e) => setEditData(prev => ({ ...prev, resolutionNotes: e.target.value }))}
                      rows={3}
                    />
                  </div>
                </div>
              ) : (
                qualityIssue.resolutionNotes && (
                  <div>
                    <h4 className="font-semibold mb-2">Resolution Notes</h4>
                    <p className="text-muted-foreground">{qualityIssue.resolutionNotes}</p>
                  </div>
                )
              )}
            </CardContent>
          </Card>

          {/* Product & Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle>Product & Supplier Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Product
                  </h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Name:</strong> {qualityIssue.product.name}</p>
                    <p><strong>SKU:</strong> {qualityIssue.product.sku}</p>
                    {qualityIssue.product.barcode && (
                      <p><strong>Barcode:</strong> {qualityIssue.product.barcode}</p>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Supplier
                  </h4>
                  <div className="space-y-1 text-sm">
                    <p><strong>Name:</strong> {qualityIssue.supplier.name}</p>
                    <p><strong>Contact:</strong> {qualityIssue.supplier.contactPerson}</p>
                    {qualityIssue.supplier.phone && (
                      <p><strong>Phone:</strong> {qualityIssue.supplier.phone}</p>
                    )}
                    {qualityIssue.supplier.email && (
                      <p><strong>Email:</strong> {qualityIssue.supplier.email}</p>
                    )}
                  </div>
                </div>
              </div>

              {qualityIssue.batch && (
                <div>
                  <h4 className="font-semibold mb-2">Batch Information</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p><strong>Batch Number:</strong> {qualityIssue.batch.batchNumber}</p>
                      <p><strong>Received:</strong> {new Date(qualityIssue.batch.receivedDate).toLocaleDateString()}</p>
                      {qualityIssue.batch.expiryDate && (
                        <p><strong>Expiry:</strong> {new Date(qualityIssue.batch.expiryDate).toLocaleDateString()}</p>
                      )}
                    </div>
                    {qualityIssue.batch.productSupplier && (
                      <div>
                        {qualityIssue.batch.productSupplier.supplierProductCode && (
                          <p><strong>Supplier Code:</strong> {qualityIssue.batch.productSupplier.supplierProductCode}</p>
                        )}
                        {qualityIssue.batch.productSupplier.supplierProductName && (
                          <p><strong>Supplier Name:</strong> {qualityIssue.batch.productSupplier.supplierProductName}</p>
                        )}
                        <p><strong>Purchase Price:</strong> Rp {qualityIssue.batch.productSupplier.purchasePrice.toLocaleString()}</p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Timeline
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Issue Reported</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(qualityIssue.reportedAt).toLocaleString()}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      by {qualityIssue.reporter?.name || 'Unknown Reporter'}
                    </p>
                  </div>
                </div>

                {qualityIssue.escalations?.filter(escalation => escalation).map((escalation, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Escalated to Level {escalation.escalationLevel}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(escalation.escalatedAt).toLocaleString()}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        to {escalation.escalatedToUser?.name || 'Unknown User'} by {escalation.escalatedByUser?.name || 'Unknown User'}
                      </p>
                      {escalation.escalationReason && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Reason: {escalation.escalationReason}
                        </p>
                      )}
                    </div>
                  </div>
                ))}

                {qualityIssue.resolvedAt && (
                  <div className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Issue Resolved</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(qualityIssue.resolvedAt).toLocaleString()}
                      </p>
                      {qualityIssue.resolver && (
                        <p className="text-xs text-muted-foreground">
                          by {qualityIssue.resolver?.name || 'Unknown Resolver'}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Related Information */}
          {qualityIssue.returnItem && (
            <Card>
              <CardHeader>
                <CardTitle>Related Return</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <p><strong>Return ID:</strong> {qualityIssue.returnItem.return.id}</p>
                <p><strong>Reason:</strong> {qualityIssue.returnItem.return.reason}</p>
                <p><strong>Date:</strong> {new Date(qualityIssue.returnItem.return.returnDate).toLocaleDateString()}</p>
                {qualityIssue.returnItem.return.customer && (
                  <p><strong>Customer:</strong> {qualityIssue.returnItem.return.customer?.name || 'Unknown Customer'}</p>
                )}
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => router.push(`/returns/${qualityIssue.returnItem!.return.id}`)}
                >
                  View Return
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Quality Improvements */}
          {qualityIssue.improvements?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle2 className="h-5 w-5" />
                  Quality Improvements
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {qualityIssue.improvements?.filter(improvement => improvement).map((improvement) => (
                  <div key={improvement.id} className="p-2 border rounded">
                    <p className="text-sm font-medium">{improvement.title}</p>
                    <p className="text-xs text-muted-foreground">Status: {improvement.status}</p>
                    {improvement.assignee && (
                      <p className="text-xs text-muted-foreground">
                        Assigned to: {improvement.assignee?.name || 'Unknown Assignee'}
                      </p>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="mt-1"
                      onClick={() => router.push(`/quality/improvements/${improvement.id}`)}
                    >
                      View Plan
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
