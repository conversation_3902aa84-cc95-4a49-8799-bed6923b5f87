"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { ArrowLeft, ArrowUpCircle, AlertCircle, User, CalendarIcon, Clock } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface QualityIssue {
  id: string;
  issueType: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  status: string;
  escalationLevel: number;
  product: {
    id: string;
    name: string;
    sku: string;
  };
  supplier: {
    id: string;
    name: string;
  };
  reporter: {
    id: string;
    name: string;
  };
  reportedAt: string;
}

interface User {
  id: string;
  name: string;
  role: string;
  email: string;
}

export default function EscalateQualityIssuePage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [qualityIssue, setQualityIssue] = useState<QualityIssue | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    escalatedTo: "",
    escalationReason: "",
    responseRequired: true,
    responseDeadline: "",
  });
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [selectedTime, setSelectedTime] = useState("09:00");

  // Unwrap params using React.use()
  const { id } = use(params);

  useEffect(() => {
    fetchQualityIssue();
    fetchUsers();
  }, [id]);

  const fetchQualityIssue = async () => {
    try {
      const response = await fetch(`/api/quality-issues/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality issue');
      }
      const data = await response.json();
      setQualityIssue(data);
    } catch (error) {
      console.error('Error fetching quality issue:', error);
      toast.error('Failed to load quality issue');
      router.back();
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users?roles=SUPER_ADMIN,WAREHOUSE_ADMIN,FINANCE_ADMIN');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateTimeChange = (date: Date | undefined, time?: string) => {
    if (date) {
      const timeToUse = time || selectedTime;
      const [hours, minutes] = timeToUse.split(':');
      const dateTime = new Date(date);
      dateTime.setHours(parseInt(hours), parseInt(minutes));

      setFormData(prev => ({
        ...prev,
        responseDeadline: dateTime.toISOString()
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        responseDeadline: ""
      }));
    }
  };

  const clearDateTime = () => {
    setSelectedDate(undefined);
    setSelectedTime("09:00");
    setShowCalendar(false);
    handleDateTimeChange(undefined);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.escalatedTo || !formData.escalationReason) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(`/api/quality-issues/${id}/escalate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          responseDeadline: formData.responseDeadline || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to escalate quality issue');
      }

      const result = await response.json();
      toast.success('Quality issue escalated successfully');
      router.push(`/quality/issues/${id}`);
    } catch (error) {
      console.error('Error escalating quality issue:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to escalate quality issue');
    } finally {
      setSubmitting(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatIssueType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (loading || !qualityIssue) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Escalate Quality Issue"
        description={`${formatIssueType(qualityIssue.issueType)} - ${qualityIssue.product.name}`}
        actions={
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Issue Summary */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Issue Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2">
                  <Badge className={getSeverityColor(qualityIssue.severity)}>
                    {qualityIssue.severity}
                  </Badge>
                  {qualityIssue.escalationLevel > 0 && (
                    <Badge variant="outline">
                      <ArrowUpCircle className="mr-1 h-3 w-3" />
                      Level {qualityIssue.escalationLevel}
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Product</p>
                  <p className="text-sm text-muted-foreground">
                    {qualityIssue.product.name} ({qualityIssue.product.sku})
                  </p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Supplier</p>
                  <p className="text-sm text-muted-foreground">{qualityIssue.supplier.name}</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Reported By</p>
                  <p className="text-sm text-muted-foreground">{qualityIssue.reporter.name}</p>
                </div>
                
                <div className="space-y-1">
                  <p className="text-sm font-medium">Description</p>
                  <p className="text-sm text-muted-foreground">{qualityIssue.description}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Escalation Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowUpCircle className="h-5 w-5" />
                Escalation Details
              </CardTitle>
              <CardDescription>
                Escalate this quality issue to a higher authority for resolution
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="escalatedTo">Escalate To *</Label>
                  <Select value={formData.escalatedTo} onValueChange={(value) => handleInputChange('escalatedTo', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select user to escalate to" />
                    </SelectTrigger>
                    <SelectContent>
                      {users.map((user) => (
                        <SelectItem key={user.id} value={user.id}>
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>{user.name} ({user.role})</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="escalationReason">Escalation Reason *</Label>
                  <Textarea
                    id="escalationReason"
                    placeholder="Explain why this issue needs to be escalated..."
                    rows={4}
                    value={formData.escalationReason}
                    onChange={(e) => handleInputChange('escalationReason', e.target.value)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Response Deadline (Optional)</Label>
                  <div className="space-y-2">
                    <Button
                      type="button"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !selectedDate && "text-muted-foreground"
                      )}
                      onClick={() => setShowCalendar(!showCalendar)}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {selectedDate ? (
                        <span>
                          {format(selectedDate, "PPP")} at {selectedTime}
                        </span>
                      ) : (
                        "Pick a deadline date and time"
                      )}
                    </Button>

                    {selectedDate && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={clearDateTime}
                        className="w-full"
                      >
                        Clear deadline
                      </Button>
                    )}

                    {showCalendar && (
                      <div className="border rounded-md p-3 bg-popover space-y-3">
                        <Calendar
                          mode="single"
                          selected={selectedDate}
                          onSelect={(date) => {
                            setSelectedDate(date);
                            if (date) {
                              handleDateTimeChange(date, selectedTime);
                            }
                          }}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />

                        {selectedDate && (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium flex items-center gap-2">
                              <Clock className="h-4 w-4" />
                              Time
                            </Label>
                            <div className="grid grid-cols-2 gap-2">
                              <div className="space-y-1">
                                <Label className="text-xs text-muted-foreground">Hour</Label>
                                <select
                                  value={selectedTime.split(':')[0]}
                                  onChange={(e) => {
                                    const newTime = `${e.target.value}:${selectedTime.split(':')[1]}`;
                                    setSelectedTime(newTime);
                                    handleDateTimeChange(selectedDate, newTime);
                                  }}
                                  className="w-full p-2 border rounded-md bg-background text-sm"
                                >
                                  {Array.from({ length: 24 }, (_, i) => {
                                    const hour = i.toString().padStart(2, '0');
                                    return (
                                      <option key={hour} value={hour}>
                                        {hour}
                                      </option>
                                    );
                                  })}
                                </select>
                              </div>
                              <div className="space-y-1">
                                <Label className="text-xs text-muted-foreground">Minute</Label>
                                <select
                                  value={selectedTime.split(':')[1]}
                                  onChange={(e) => {
                                    const newTime = `${selectedTime.split(':')[0]}:${e.target.value}`;
                                    setSelectedTime(newTime);
                                    handleDateTimeChange(selectedDate, newTime);
                                  }}
                                  className="w-full p-2 border rounded-md bg-background text-sm"
                                >
                                  <option value="00">00</option>
                                  <option value="15">15</option>
                                  <option value="30">30</option>
                                  <option value="45">45</option>
                                </select>
                              </div>
                            </div>
                          </div>
                        )}

                        <Button
                          type="button"
                          size="sm"
                          onClick={() => setShowCalendar(false)}
                          className="w-full"
                        >
                          Done
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex justify-end space-x-4">
                  <Button type="button" variant="outline" onClick={() => router.back()}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={submitting}>
                    {submitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Escalating...
                      </>
                    ) : (
                      <>
                        <ArrowUpCircle className="mr-2 h-4 w-4" />
                        Escalate Issue
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
