"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { AlertCircle, Plus, Search, Filter, Eye, ArrowUpCircle, Calendar, User, Building2 } from "lucide-react";
import { toast } from "sonner";
import { formatCurrency } from "@/lib/utils";

interface QualityIssue {
  id: string;
  issueType: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  status: 'OPEN' | 'IN_PROGRESS' | 'RESOLVED' | 'CLOSED' | 'ESCALATED';
  affectedQuantity: number;
  reportedAt: string;
  escalationLevel: number;
  product: {
    id: string;
    name: string;
    sku: string;
  };
  supplier: {
    id: string;
    name: string;
    contactPerson: string;
  };
  batch?: {
    id: string;
    batchNumber: string;
    receivedDate: string;
  };
  returnItem?: {
    return: {
      id: string;
      reason: string;
      returnDate: string;
    };
  };
  reporter: {
    id: string;
    name: string;
  };
  resolver?: {
    id: string;
    name: string;
  };
  escalations: Array<{
    escalatedToUser: {
      id: string;
      name: string;
    };
  }>;
}

interface QualityIssuesResponse {
  qualityIssues: QualityIssue[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export default function QualityIssuesPage() {
  const router = useRouter();
  const [qualityIssues, setQualityIssues] = useState<QualityIssue[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  const [issueTypeFilter, setIssueTypeFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchQualityIssues = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
        ...(severityFilter && severityFilter !== "all" && { severity: severityFilter }),
        ...(issueTypeFilter && issueTypeFilter !== "all" && { issueType: issueTypeFilter }),
      });

      const response = await fetch(`/api/quality-issues?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality issues');
      }

      const data: QualityIssuesResponse = await response.json();
      setQualityIssues(data.qualityIssues);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching quality issues:', error);
      toast.error('Failed to load quality issues');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualityIssues();
  }, [currentPage, searchTerm, statusFilter, severityFilter, issueTypeFilter]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ESCALATED': return 'bg-red-100 text-red-800 border-red-200';
      case 'RESOLVED': return 'bg-green-100 text-green-800 border-green-200';
      case 'CLOSED': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatIssueType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <MainLayout>
      <PageHeader
        title="Quality Issues"
        description="Track and manage quality problems across suppliers and products"
        actions={
          <Button onClick={() => router.push('/quality/issues/create')}>
            <Plus className="mr-2 h-4 w-4" />
            Report Issue
          </Button>
        }
      />

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search issues..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="OPEN">Open</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="ESCALATED">Escalated</SelectItem>
                <SelectItem value="RESOLVED">Resolved</SelectItem>
                <SelectItem value="CLOSED">Closed</SelectItem>
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Severities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="CRITICAL">Critical</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={issueTypeFilter} onValueChange={setIssueTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="DEFECTIVE_PRODUCT">Defective Product</SelectItem>
                <SelectItem value="PACKAGING_DAMAGE">Packaging Damage</SelectItem>
                <SelectItem value="WRONG_SPECIFICATION">Wrong Specification</SelectItem>
                <SelectItem value="CONTAMINATION">Contamination</SelectItem>
                <SelectItem value="EXPIRY_ISSUE">Expiry Issue</SelectItem>
                <SelectItem value="QUALITY_DEGRADATION">Quality Degradation</SelectItem>
                <SelectItem value="CUSTOMER_COMPLAINT">Customer Complaint</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all");
                setSeverityFilter("all");
                setIssueTypeFilter("all");
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quality Issues Table */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Quality Issues</CardTitle>
          <CardDescription>
            {qualityIssues.length} issues found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Issue</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Reported</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {qualityIssues.map((issue) => (
                    <TableRow key={issue.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{formatIssueType(issue.issueType)}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {issue.description}
                          </div>
                          {issue.escalationLevel > 0 && (
                            <Badge variant="outline" className="text-xs">
                              <ArrowUpCircle className="mr-1 h-3 w-3" />
                              Level {issue.escalationLevel}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{issue.product.name}</div>
                          <div className="text-sm text-muted-foreground">SKU: {issue.product.sku}</div>
                          {issue.batch && (
                            <div className="text-xs text-muted-foreground">
                              Batch: {issue.batch.batchNumber}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{issue.supplier.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {issue.supplier.contactPerson}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getSeverityColor(issue.severity)}>
                          {issue.severity}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(issue.status)}>
                          {issue.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="text-sm">
                            {new Date(issue.reportedAt).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            by {issue.reporter.name}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/quality/issues/${issue.id}`)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </MainLayout>
  );
}
