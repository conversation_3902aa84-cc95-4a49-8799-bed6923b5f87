"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Shield,
  Save,
  AlertTriangle,
  TrendingUp,
  RotateCcw,
  Settings,
  Bell,
  Target,
  Activity
} from "lucide-react";
import { toast } from "sonner";

interface QualityThreshold {
  id: string;
  metricType: string;
  thresholdType: 'WARNING' | 'CRITICAL';
  value: number;
  operator: 'GREATER_THAN' | 'LESS_THAN' | 'EQUALS';
  enabled: boolean;
  description: string;
  alertFrequency: 'IMMEDIATE' | 'HOURLY' | 'DAILY';
  escalationLevel: number;
  notificationRoles: string[];
}

interface QualityThresholds {
  returnRateWarning: number;
  returnRateCritical: number;
  defectRateWarning: number;
  defectRateCritical: number;
  qualityScoreWarning: number;
  qualityScoreCritical: number;
  resolutionTimeWarning: number;
  resolutionTimeCritical: number;
  batchReturnRateWarning: number;
  batchReturnRateCritical: number;
  supplierRiskScoreWarning: number;
  supplierRiskScoreCritical: number;
  alertsEnabled: boolean;
  escalationEnabled: boolean;
  notificationFrequency: 'IMMEDIATE' | 'HOURLY' | 'DAILY';
  monitoringInterval: number;
}

export default function QualityThresholdsPage() {
  const [thresholds, setThresholds] = useState<QualityThresholds>({
    returnRateWarning: 5.0,
    returnRateCritical: 10.0,
    defectRateWarning: 2.0,
    defectRateCritical: 5.0,
    qualityScoreWarning: 70.0,
    qualityScoreCritical: 60.0,
    resolutionTimeWarning: 7.0,
    resolutionTimeCritical: 14.0,
    batchReturnRateWarning: 15.0,
    batchReturnRateCritical: 25.0,
    supplierRiskScoreWarning: 70.0,
    supplierRiskScoreCritical: 85.0,
    alertsEnabled: true,
    escalationEnabled: true,
    notificationFrequency: 'IMMEDIATE',
    monitoringInterval: 60,
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    fetchQualityThresholds();
  }, []);

  const fetchQualityThresholds = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/quality-thresholds');
      if (response.ok) {
        const data = await response.json();
        // Map API response to frontend interface structure
        if (data.thresholds) {
          setThresholds({
            returnRateWarning: data.thresholds.returnRateThreshold || 5.0,
            returnRateCritical: data.thresholds.returnRateThreshold ? data.thresholds.returnRateThreshold * 2 : 10.0,
            defectRateWarning: data.thresholds.defectRateThreshold || 2.0,
            defectRateCritical: data.thresholds.defectRateThreshold ? data.thresholds.defectRateThreshold * 2.5 : 5.0,
            qualityScoreWarning: data.thresholds.qualityScoreThreshold || 70.0,
            qualityScoreCritical: data.thresholds.qualityScoreThreshold ? data.thresholds.qualityScoreThreshold - 10 : 60.0,
            resolutionTimeWarning: data.thresholds.autoEscalationLevel1Days || 7.0,
            resolutionTimeCritical: data.thresholds.autoEscalationLevel2Days || 14.0,
            batchReturnRateWarning: 15.0, // Not in API, use default
            batchReturnRateCritical: 25.0, // Not in API, use default
            supplierRiskScoreWarning: 70.0, // Not in API, use default
            supplierRiskScoreCritical: 85.0, // Not in API, use default
            alertsEnabled: data.thresholds.notificationEnabled ?? true,
            escalationEnabled: data.thresholds.escalationEnabled ?? true,
            notificationFrequency: 'IMMEDIATE' as const, // Not in API, use default
            monitoringInterval: data.thresholds.monitoringPeriodDays ? data.thresholds.monitoringPeriodDays * 24 * 60 : 60, // Convert days to minutes
          });
        }
      }
    } catch (error) {
      console.error('Error fetching quality thresholds:', error);
      toast.error('Failed to load quality thresholds');
    } finally {
      setLoading(false);
    }
  };

  const handleThresholdChange = (field: keyof QualityThresholds, value: any) => {
    setThresholds(prev => ({ ...prev, [field]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Map frontend structure to API structure
      const apiPayload = {
        returnRateThreshold: thresholds.returnRateWarning,
        defectRateThreshold: thresholds.defectRateWarning,
        qualityScoreThreshold: thresholds.qualityScoreWarning,
        returnValueThreshold: 1000000, // Default value as not configurable in UI
        escalationEnabled: thresholds.escalationEnabled,
        autoEscalationLevel1Days: Math.round(thresholds.resolutionTimeWarning),
        autoEscalationLevel2Days: Math.round(thresholds.resolutionTimeCritical),
        notificationEnabled: thresholds.alertsEnabled,
        monitoringPeriodDays: Math.round(thresholds.monitoringInterval / (24 * 60)), // Convert minutes to days
      };

      const response = await fetch('/api/quality-thresholds', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(apiPayload),
      });

      if (!response.ok) {
        throw new Error('Failed to save quality thresholds');
      }

      toast.success('Quality thresholds saved successfully');
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving quality thresholds:', error);
      toast.error('Failed to save quality thresholds');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefaults = () => {
    setThresholds({
      returnRateWarning: 5.0,
      returnRateCritical: 10.0,
      defectRateWarning: 2.0,
      defectRateCritical: 5.0,
      qualityScoreWarning: 70.0,
      qualityScoreCritical: 60.0,
      resolutionTimeWarning: 7.0,
      resolutionTimeCritical: 14.0,
      batchReturnRateWarning: 15.0,
      batchReturnRateCritical: 25.0,
      supplierRiskScoreWarning: 70.0,
      supplierRiskScoreCritical: 85.0,
      alertsEnabled: true,
      escalationEnabled: true,
      notificationFrequency: 'IMMEDIATE',
      monitoringInterval: 60,
    });
    setHasChanges(true);
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Quality Thresholds"
        description="Configure quality monitoring thresholds and alert settings"
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={resetToDefaults}>
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset to Defaults
            </Button>
            <Button onClick={handleSave} disabled={saving || !hasChanges}>
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        }
      />

      {hasChanges && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm text-yellow-800">You have unsaved changes</span>
          </div>
        </div>
      )}

      {/* Return Rate Thresholds */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            Return Rate Thresholds
          </CardTitle>
          <CardDescription>
            Configure thresholds for supplier return rates (percentage)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="returnRateWarning">Warning Threshold (%)</Label>
              <Input
                id="returnRateWarning"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.returnRateWarning}
                onChange={(e) => handleThresholdChange('returnRateWarning', parseFloat(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Trigger warning alerts when return rate exceeds this value
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="returnRateCritical">Critical Threshold (%)</Label>
              <Input
                id="returnRateCritical"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.returnRateCritical}
                onChange={(e) => handleThresholdChange('returnRateCritical', parseFloat(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Trigger critical alerts when return rate exceeds this value
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Defect Rate Thresholds */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Defect Rate Thresholds
          </CardTitle>
          <CardDescription>
            Configure thresholds for supplier defect rates (percentage)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="defectRateWarning">Warning Threshold (%)</Label>
              <Input
                id="defectRateWarning"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.defectRateWarning}
                onChange={(e) => handleThresholdChange('defectRateWarning', parseFloat(e.target.value))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="defectRateCritical">Critical Threshold (%)</Label>
              <Input
                id="defectRateCritical"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.defectRateCritical}
                onChange={(e) => handleThresholdChange('defectRateCritical', parseFloat(e.target.value))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quality Score Thresholds */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Quality Score Thresholds
          </CardTitle>
          <CardDescription>
            Configure thresholds for supplier quality scores (0-100)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="qualityScoreWarning">Warning Threshold</Label>
              <Input
                id="qualityScoreWarning"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.qualityScoreWarning}
                onChange={(e) => handleThresholdChange('qualityScoreWarning', parseFloat(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Trigger warning when quality score falls below this value
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="qualityScoreCritical">Critical Threshold</Label>
              <Input
                id="qualityScoreCritical"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.qualityScoreCritical}
                onChange={(e) => handleThresholdChange('qualityScoreCritical', parseFloat(e.target.value))}
              />
              <p className="text-xs text-muted-foreground">
                Trigger critical alerts when quality score falls below this value
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Resolution Time Thresholds */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Resolution Time Thresholds
          </CardTitle>
          <CardDescription>
            Configure thresholds for quality issue resolution times (days)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="resolutionTimeWarning">Warning Threshold (days)</Label>
              <Input
                id="resolutionTimeWarning"
                type="number"
                step="0.1"
                min="0"
                value={thresholds.resolutionTimeWarning}
                onChange={(e) => handleThresholdChange('resolutionTimeWarning', parseFloat(e.target.value))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="resolutionTimeCritical">Critical Threshold (days)</Label>
              <Input
                id="resolutionTimeCritical"
                type="number"
                step="0.1"
                min="0"
                value={thresholds.resolutionTimeCritical}
                onChange={(e) => handleThresholdChange('resolutionTimeCritical', parseFloat(e.target.value))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Batch Return Rate Thresholds */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Batch Return Rate Thresholds
          </CardTitle>
          <CardDescription>
            Configure thresholds for individual batch return rates (percentage)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="batchReturnRateWarning">Warning Threshold (%)</Label>
              <Input
                id="batchReturnRateWarning"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.batchReturnRateWarning}
                onChange={(e) => handleThresholdChange('batchReturnRateWarning', parseFloat(e.target.value))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="batchReturnRateCritical">Critical Threshold (%)</Label>
              <Input
                id="batchReturnRateCritical"
                type="number"
                step="0.1"
                min="0"
                max="100"
                value={thresholds.batchReturnRateCritical}
                onChange={(e) => handleThresholdChange('batchReturnRateCritical', parseFloat(e.target.value))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alert Configuration */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Alert Configuration
          </CardTitle>
          <CardDescription>
            Configure alert behavior and notification settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Alerts</Label>
                  <p className="text-xs text-muted-foreground">
                    Enable automatic quality alerts
                  </p>
                </div>
                <Switch
                  checked={thresholds.alertsEnabled}
                  onCheckedChange={(checked) => handleThresholdChange('alertsEnabled', checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Enable Escalation</Label>
                  <p className="text-xs text-muted-foreground">
                    Enable automatic escalation workflows
                  </p>
                </div>
                <Switch
                  checked={thresholds.escalationEnabled}
                  onCheckedChange={(checked) => handleThresholdChange('escalationEnabled', checked)}
                />
              </div>
            </div>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="notificationFrequency">Notification Frequency</Label>
                <Select 
                  value={thresholds.notificationFrequency} 
                  onValueChange={(value: 'IMMEDIATE' | 'HOURLY' | 'DAILY') => handleThresholdChange('notificationFrequency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="IMMEDIATE">Immediate</SelectItem>
                    <SelectItem value="HOURLY">Hourly</SelectItem>
                    <SelectItem value="DAILY">Daily</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="monitoringInterval">Monitoring Interval (minutes)</Label>
                <Input
                  id="monitoringInterval"
                  type="number"
                  min="1"
                  max="1440"
                  value={thresholds.monitoringInterval}
                  onChange={(e) => handleThresholdChange('monitoringInterval', parseInt(e.target.value))}
                />
                <p className="text-xs text-muted-foreground">
                  How often to check thresholds (1-1440 minutes)
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Status */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Current Configuration Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <Badge variant={thresholds.alertsEnabled ? "default" : "secondary"}>
                {thresholds.alertsEnabled ? "Enabled" : "Disabled"}
              </Badge>
              <p className="text-sm text-muted-foreground mt-1">Alerts</p>
            </div>
            <div className="text-center">
              <Badge variant={thresholds.escalationEnabled ? "default" : "secondary"}>
                {thresholds.escalationEnabled ? "Enabled" : "Disabled"}
              </Badge>
              <p className="text-sm text-muted-foreground mt-1">Escalation</p>
            </div>
            <div className="text-center">
              <Badge variant="outline">
                {thresholds.notificationFrequency}
              </Badge>
              <p className="text-sm text-muted-foreground mt-1">Notifications</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </MainLayout>
  );
}
