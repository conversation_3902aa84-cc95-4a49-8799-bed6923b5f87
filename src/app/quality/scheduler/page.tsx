"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Clock,
  Play,
  Pause,
  RefreshCw,
  Settings,
  Activity,
  CheckCircle2,
  AlertCircle,
  Calendar,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface ScheduledJob {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  schedule: string;
  jobType: 'quality_monitoring' | 'trend_analysis' | 'alert_check' | 'report_generation';
  parameters: Record<string, any>;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  failureCount: number;
  lastError?: string;
}

interface SchedulerStatus {
  status: 'running' | 'stopped';
  totalJobs: number;
  enabledJobs: number;
  recentJobs: Array<{
    id: string;
    name: string;
    lastRun: string;
    status: string;
  }>;
}

interface JobExecutionResult {
  jobId: string;
  jobName: string;
  startTime: string;
  endTime: string;
  duration: number;
  status: 'success' | 'failure' | 'partial';
  result?: any;
  error?: string;
  metrics: {
    alertsGenerated?: number;
    suppliersAnalyzed?: number;
    issuesFound?: number;
  };
}

export default function QualitySchedulerPage() {
  const [jobs, setJobs] = useState<ScheduledJob[]>([]);
  const [schedulerStatus, setSchedulerStatus] = useState<SchedulerStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [executionResults, setExecutionResults] = useState<JobExecutionResult[]>([]);

  useEffect(() => {
    fetchSchedulerData();
    // Set up polling for real-time updates
    const interval = setInterval(fetchSchedulerData, 30000); // Poll every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const fetchSchedulerData = async () => {
    try {
      setLoading(true);
      const [statusResponse, jobsResponse] = await Promise.all([
        fetch('/api/quality-scheduler'),
        fetch('/api/quality-scheduler?action=jobs'),
      ]);

      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        setSchedulerStatus(statusData);
      }

      if (jobsResponse.ok) {
        const jobsData = await jobsResponse.json();
        setJobs(jobsData.jobs || []);
      }
    } catch (error) {
      console.error('Error fetching scheduler data:', error);
      toast.error('Failed to load scheduler data');
    } finally {
      setLoading(false);
    }
  };

  const initializeScheduler = async () => {
    try {
      const response = await fetch('/api/quality-scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'initialize' }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Quality scheduler initialized successfully');
        fetchSchedulerData();
      } else {
        throw new Error('Failed to initialize scheduler');
      }
    } catch (error) {
      console.error('Error initializing scheduler:', error);
      toast.error('Failed to initialize scheduler');
    }
  };

  const startScheduler = async () => {
    try {
      const response = await fetch('/api/quality-scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'start' }),
      });

      if (response.ok) {
        toast.success('Quality scheduler started');
        fetchSchedulerData();
      } else {
        throw new Error('Failed to start scheduler');
      }
    } catch (error) {
      console.error('Error starting scheduler:', error);
      toast.error('Failed to start scheduler');
    }
  };

  const stopScheduler = async () => {
    try {
      const response = await fetch('/api/quality-scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'stop' }),
      });

      if (response.ok) {
        toast.success('Quality scheduler stopped');
        fetchSchedulerData();
      } else {
        throw new Error('Failed to stop scheduler');
      }
    } catch (error) {
      console.error('Error stopping scheduler:', error);
      toast.error('Failed to stop scheduler');
    }
  };

  const triggerJob = async (jobId: string) => {
    try {
      const response = await fetch('/api/quality-scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'trigger_job', jobId }),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success(`Job "${data.result.jobName}" executed successfully`);
        setExecutionResults(prev => [data.result, ...prev.slice(0, 9)]); // Keep last 10 results
        fetchSchedulerData();
      } else {
        throw new Error('Failed to trigger job');
      }
    } catch (error) {
      console.error('Error triggering job:', error);
      toast.error('Failed to trigger job');
    }
  };

  const toggleJobEnabled = async (jobId: string, enabled: boolean) => {
    try {
      const response = await fetch('/api/quality-scheduler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          action: enabled ? 'enable_job' : 'disable_job', 
          jobId 
        }),
      });

      if (response.ok) {
        toast.success(`Job ${enabled ? 'enabled' : 'disabled'} successfully`);
        fetchSchedulerData();
      } else {
        throw new Error(`Failed to ${enabled ? 'enable' : 'disable'} job`);
      }
    } catch (error) {
      console.error(`Error ${enabled ? 'enabling' : 'disabling'} job:`, error);
      toast.error(`Failed to ${enabled ? 'enable' : 'disable'} job`);
    }
  };

  const getJobTypeIcon = (jobType: string) => {
    switch (jobType) {
      case 'quality_monitoring': return <Activity className="h-4 w-4 text-blue-600" />;
      case 'trend_analysis': return <RefreshCw className="h-4 w-4 text-green-600" />;
      case 'alert_check': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'report_generation': return <Calendar className="h-4 w-4 text-purple-600" />;
      default: return <Settings className="h-4 w-4 text-gray-600" />;
    }
  };

  const getJobTypeColor = (jobType: string) => {
    switch (jobType) {
      case 'quality_monitoring': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'trend_analysis': return 'bg-green-100 text-green-800 border-green-200';
      case 'alert_check': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'report_generation': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatJobType = (jobType: string) => {
    return jobType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600';
      case 'stopped': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Quality Scheduler"
        description="Manage automated quality monitoring and analysis jobs"
        actions={
          <div className="flex gap-2">
            {schedulerStatus?.status === 'stopped' ? (
              <>
                <Button variant="outline" onClick={initializeScheduler}>
                  <Settings className="mr-2 h-4 w-4" />
                  Initialize
                </Button>
                <Button onClick={startScheduler}>
                  <Play className="mr-2 h-4 w-4" />
                  Start Scheduler
                </Button>
              </>
            ) : (
              <Button variant="outline" onClick={stopScheduler}>
                <Pause className="mr-2 h-4 w-4" />
                Stop Scheduler
              </Button>
            )}
          </div>
        }
      />

      {/* Scheduler Status */}
      {schedulerStatus && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Scheduler Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${getStatusColor(schedulerStatus.status)}`}>
                  {schedulerStatus.status.toUpperCase()}
                </div>
                <p className="text-sm text-muted-foreground">Status</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">{schedulerStatus.totalJobs}</div>
                <p className="text-sm text-muted-foreground">Total Jobs</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{schedulerStatus.enabledJobs}</div>
                <p className="text-sm text-muted-foreground">Enabled Jobs</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{schedulerStatus.recentJobs.length}</div>
                <p className="text-sm text-muted-foreground">Recent Runs</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Scheduled Jobs */}
      <Card>
        <CardHeader>
          <CardTitle>Scheduled Jobs</CardTitle>
          <CardDescription>
            Configure and manage automated quality monitoring jobs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Job</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Schedule</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Run</TableHead>
                <TableHead>Next Run</TableHead>
                <TableHead>Performance</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {jobs.map((job) => (
                <TableRow key={job.id}>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="font-medium">{job.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {job.description}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getJobTypeIcon(job.jobType)}
                      <Badge className={getJobTypeColor(job.jobType)}>
                        {formatJobType(job.jobType)}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm font-mono">{job.schedule}</div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={job.enabled}
                        onCheckedChange={(checked) => toggleJobEnabled(job.id, checked)}
                      />
                      <span className="text-sm">
                        {job.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {job.lastRun ? (
                      <div className="space-y-1">
                        <div className="text-sm">
                          {new Date(job.lastRun).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(job.lastRun).toLocaleTimeString()}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">Never</span>
                    )}
                  </TableCell>
                  <TableCell>
                    {job.nextRun ? (
                      <div className="space-y-1">
                        <div className="text-sm">
                          {new Date(job.nextRun).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(job.nextRun).toLocaleTimeString()}
                        </div>
                      </div>
                    ) : (
                      <span className="text-sm text-muted-foreground">Not scheduled</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="text-sm">
                        <span className="text-green-600">{job.runCount}</span> runs
                      </div>
                      {job.failureCount > 0 && (
                        <div className="text-sm">
                          <span className="text-red-600">{job.failureCount}</span> failures
                        </div>
                      )}
                      {job.lastError && (
                        <div className="text-xs text-red-600 truncate max-w-32" title={job.lastError}>
                          {job.lastError}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => triggerJob(job.id)}
                      disabled={!job.enabled}
                    >
                      <Zap className="mr-2 h-4 w-4" />
                      Run Now
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Recent Execution Results */}
      {executionResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Execution Results</CardTitle>
            <CardDescription>
              Results from manually triggered jobs
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {executionResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{result.jobName}</div>
                    <div className="flex items-center gap-2">
                      {result.status === 'success' ? (
                        <CheckCircle2 className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertCircle className="h-4 w-4 text-red-600" />
                      )}
                      <Badge variant={result.status === 'success' ? 'default' : 'destructive'}>
                        {result.status.toUpperCase()}
                      </Badge>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Duration:</span>
                      <div className="font-medium">{result.duration}ms</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Started:</span>
                      <div className="font-medium">
                        {new Date(result.startTime).toLocaleTimeString()}
                      </div>
                    </div>
                    {result.metrics.alertsGenerated !== undefined && (
                      <div>
                        <span className="text-muted-foreground">Alerts:</span>
                        <div className="font-medium">{result.metrics.alertsGenerated}</div>
                      </div>
                    )}
                    {result.metrics.suppliersAnalyzed !== undefined && (
                      <div>
                        <span className="text-muted-foreground">Suppliers:</span>
                        <div className="font-medium">{result.metrics.suppliersAnalyzed}</div>
                      </div>
                    )}
                  </div>
                  {result.error && (
                    <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      Error: {result.error}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </MainLayout>
  );
}
