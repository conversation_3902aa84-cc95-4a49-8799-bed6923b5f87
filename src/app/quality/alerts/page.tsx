"use client";

import { useState, useEffect } from "react";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import {
  Bell,
  RefreshCw,
  AlertTriangle,
  CheckCircle2,
  Clock,
  Zap,
  Activity,
  Settings,
  X
} from "lucide-react";
import { toast } from "sonner";

interface QualityAlert {
  id: string;
  alertType: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  message: string;
  supplierId?: string;
  productId?: string;
  batchId?: string;
  threshold?: number;
  currentValue?: number;
  triggeredAt: string;
  acknowledgedAt?: string;
  acknowledgedBy?: {
    id: string;
    name: string;
  };
  resolvedAt?: string;
  resolvedBy?: {
    id: string;
    name: string;
  };
  dismissedAt?: string;
  dismissedBy?: {
    id: string;
    name: string;
  };
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED' | 'ESCALATED';
  escalationLevel: number;
  metadata?: Record<string, any>;
  ruleId?: string;
  supplier?: {
    id: string;
    name: string;
  };
  product?: {
    id: string;
    name: string;
    sku: string;
  };
  batch?: {
    id: string;
    batchNumber: string;
  };
}

interface AlertSystemStatus {
  isRunning: boolean;
  lastRun?: string;
  totalAlerts: number;
  activeAlerts: number;
  acknowledgedAlerts: number;
  resolvedAlerts: number;
  systemHealth: 'HEALTHY' | 'WARNING' | 'ERROR';
  monitoringEnabled: boolean;
  nextScheduledRun?: string;
}

export default function QualityAlertsPage() {
  const [alerts, setAlerts] = useState<QualityAlert[]>([]);
  const [systemStatus, setSystemStatus] = useState<AlertSystemStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  useEffect(() => {
    fetchAlerts();
    fetchSystemStatus();
    // Set up polling for real-time updates
    const interval = setInterval(() => {
      fetchAlerts();
      fetchSystemStatus();
    }, 30000); // Poll every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const fetchAlerts = async () => {
    try {
      const params = new URLSearchParams({
        limit: "50",
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
        ...(severityFilter && severityFilter !== "all" && { severity: severityFilter }),
        ...(typeFilter && typeFilter !== "all" && { alertType: typeFilter }),
      });

      const response = await fetch(`/api/quality-alerts?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAlerts(data.alerts || []);
      }
    } catch (error) {
      console.error('Error fetching quality alerts:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch('/api/quality-alerts');
      if (response.ok) {
        const data = await response.json();
        if (data.summary) {
          setSystemStatus({
            isRunning: true,
            lastRun: new Date().toISOString(),
            totalAlerts: data.summary.total,
            activeAlerts: data.summary.active,
            acknowledgedAlerts: data.summary.acknowledged,
            resolvedAlerts: data.summary.resolved,
            systemHealth: 'HEALTHY' as const,
            monitoringEnabled: true,
          });
        }
      }
    } catch (error) {
      console.error('Error fetching system status:', error);
    }
  };

  const runQualityMonitoring = async () => {
    try {
      const response = await fetch('/api/quality-alerts?action=run_monitoring');
      if (response.ok) {
        const data = await response.json();
        toast.success('Quality monitoring completed successfully');
        fetchAlerts();
        fetchSystemStatus();
      } else {
        throw new Error('Failed to run quality monitoring');
      }
    } catch (error) {
      console.error('Error running quality monitoring:', error);
      toast.error('Failed to run quality monitoring');
    }
  };

  const acknowledgeAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/quality-alerts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'acknowledge',
          alertId: alertId,
        }),
      });
      if (response.ok) {
        toast.success('Alert acknowledged');
        fetchAlerts();
      } else {
        throw new Error('Failed to acknowledge alert');
      }
    } catch (error) {
      console.error('Error acknowledging alert:', error);
      toast.error('Failed to acknowledge alert');
    }
  };

  const resolveAlert = async (alertId: string) => {
    try {
      const response = await fetch('/api/quality-alerts', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'resolve',
          alertId: alertId,
        }),
      });
      if (response.ok) {
        toast.success('Alert resolved');
        fetchAlerts();
      } else {
        throw new Error('Failed to resolve alert');
      }
    } catch (error) {
      console.error('Error resolving alert:', error);
      toast.error('Failed to resolve alert');
    }
  };

  const dismissAlert = async (alertId: string) => {
    try {
      const response = await fetch(`/api/quality-alerts?alertId=${alertId}`, {
        method: 'DELETE',
      });
      if (response.ok) {
        toast.success('Alert dismissed');
        fetchAlerts();
      } else {
        throw new Error('Failed to dismiss alert');
      }
    } catch (error) {
      console.error('Error dismissing alert:', error);
      toast.error('Failed to dismiss alert');
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-red-100 text-red-800 border-red-200';
      case 'ACKNOWLEDGED': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'RESOLVED': return 'bg-green-100 text-green-800 border-green-200';
      case 'DISMISSED': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'ESCALATED': return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'ACKNOWLEDGED': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'RESOLVED': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'DISMISSED': return <CheckCircle2 className="h-4 w-4 text-gray-600" />;
      case 'ESCALATED': return <Zap className="h-4 w-4 text-purple-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getSystemHealthColor = (health: string) => {
    switch (health) {
      case 'HEALTHY': return 'text-green-600';
      case 'WARNING': return 'text-yellow-600';
      case 'ERROR': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const formatAlertType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const filteredAlerts = alerts.filter(alert => {
    if (statusFilter && statusFilter !== "all" && alert.status !== statusFilter) return false;
    if (severityFilter && severityFilter !== "all" && alert.severity !== severityFilter) return false;
    if (typeFilter && typeFilter !== "all" && alert.alertType !== typeFilter) return false;
    return true;
  });

  return (
    <MainLayout>
      <PageHeader
        title="Quality Alert System"
        description="Monitor and manage quality alerts in real-time"
        actions={
          <Button variant="outline" onClick={runQualityMonitoring}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Run Monitoring
          </Button>
        }
      />

      {/* System Status */}
      {systemStatus && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${getSystemHealthColor(systemStatus.systemHealth)}`}>
                  {systemStatus.systemHealth}
                </div>
                <p className="text-sm text-muted-foreground">System Health</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {systemStatus.activeAlerts}
                </div>
                <p className="text-sm text-muted-foreground">Active Alerts</p>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {systemStatus.totalAlerts}
                </div>
                <p className="text-sm text-muted-foreground">Total Alerts</p>
              </div>
              <div className="text-center">
                <Badge variant={systemStatus.monitoringEnabled ? "default" : "secondary"}>
                  {systemStatus.monitoringEnabled ? "Enabled" : "Disabled"}
                </Badge>
                <p className="text-sm text-muted-foreground">Monitoring</p>
              </div>
            </div>
            {systemStatus.lastRun && (
              <div className="mt-4 text-sm text-muted-foreground text-center">
                Last run: {new Date(systemStatus.lastRun).toLocaleString()}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Alert Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="ACKNOWLEDGED">Acknowledged</SelectItem>
                <SelectItem value="RESOLVED">Resolved</SelectItem>
                <SelectItem value="DISMISSED">Dismissed</SelectItem>
                <SelectItem value="ESCALATED">Escalated</SelectItem>
              </SelectContent>
            </Select>
            <Select value={severityFilter} onValueChange={setSeverityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Severities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="CRITICAL">Critical</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="RETURN_RATE_THRESHOLD">Return Rate</SelectItem>
                <SelectItem value="DEFECT_RATE_THRESHOLD">Defect Rate</SelectItem>
                <SelectItem value="QUALITY_SCORE_THRESHOLD">Quality Score</SelectItem>
                <SelectItem value="BATCH_RETURN_RATE">Batch Return Rate</SelectItem>
                <SelectItem value="RESOLUTION_TIME_THRESHOLD">Resolution Time</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setStatusFilter("all");
                setSeverityFilter("all");
                setTypeFilter("all");
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Alerts Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Quality Alerts
          </CardTitle>
          <CardDescription>
            {filteredAlerts.length} alerts found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Alert</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Supplier/Product</TableHead>
                  <TableHead>Threshold</TableHead>
                  <TableHead>Triggered</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAlerts.map((alert) => (
                  <TableRow key={alert.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">{formatAlertType(alert.alertType)}</div>
                        <div className="text-sm text-muted-foreground line-clamp-2">
                          {alert.message}
                        </div>
                        {alert.escalationLevel > 0 && (
                          <Badge variant="outline" className="text-xs">
                            <Zap className="mr-1 h-3 w-3" />
                            Level {alert.escalationLevel}
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(alert.severity)}>
                        {alert.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(alert.status)}
                        <Badge className={getStatusColor(alert.status)}>
                          {alert.status}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {alert.supplier && (
                          <div className="font-medium">{alert.supplier.name}</div>
                        )}
                        {alert.product && (
                          <div className="text-sm text-muted-foreground">{alert.product.name}</div>
                        )}
                        {alert.batch && (
                          <div className="text-xs text-muted-foreground">
                            Batch: {alert.batch.batchNumber}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {alert.threshold && (
                          <div className="text-sm">
                            Threshold: {alert.threshold}
                          </div>
                        )}
                        {alert.currentValue && (
                          <div className="text-sm font-medium">
                            Current: {alert.currentValue}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm">
                          {new Date(alert.triggeredAt).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(alert.triggeredAt).toLocaleTimeString()}
                        </div>
                        {alert.acknowledgedAt && (
                          <div className="text-xs text-green-600">
                            Ack: {new Date(alert.acknowledgedAt).toLocaleDateString()}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1 flex-wrap">
                        {alert.status === 'ACTIVE' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => acknowledgeAlert(alert.id)}
                          >
                            Acknowledge
                          </Button>
                        )}
                        {(alert.status === 'ACTIVE' || alert.status === 'ACKNOWLEDGED') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => resolveAlert(alert.id)}
                          >
                            Resolve
                          </Button>
                        )}
                        {(alert.status === 'ACTIVE' || alert.status === 'ACKNOWLEDGED') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => dismissAlert(alert.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-3 w-3 mr-1" />
                            Dismiss
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </MainLayout>
  );
}
