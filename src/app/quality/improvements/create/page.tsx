"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon, ArrowLeft, Save, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
}

interface QualityIssue {
  id: string;
  issueType: string;
  severity: string;
  description: string;
  supplier: {
    id: string;
    name: string;
  };
}

interface User {
  id: string;
  name: string;
  role: string;
}

export default function CreateQualityImprovementPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [qualityIssues, setQualityIssues] = useState<QualityIssue[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [startDate, setStartDate] = useState<Date>();
  const [targetDate, setTargetDate] = useState<Date>();
  const [formData, setFormData] = useState({
    supplierId: "",
    qualityIssueId: "",
    improvementType: "",
    title: "",
    description: "",
    targetMetric: "",
    currentValue: "",
    targetValue: "",
    priority: "MEDIUM",
    assignedTo: "",
    notes: "",
  });

  useEffect(() => {
    fetchSuppliers();
    fetchQualityIssues();
    fetchUsers();
  }, []);

  const fetchSuppliers = async () => {
    try {
      const response = await fetch('/api/suppliers?limit=100');
      if (response.ok) {
        const data = await response.json();
        setSuppliers(data.suppliers || []);
      }
    } catch (error) {
      console.error('Error fetching suppliers:', error);
    }
  };

  const fetchQualityIssues = async () => {
    try {
      const response = await fetch('/api/quality-issues?status=OPEN&status=IN_PROGRESS&limit=100');
      if (response.ok) {
        const data = await response.json();
        setQualityIssues(data.qualityIssues || []);
      }
    } catch (error) {
      console.error('Error fetching quality issues:', error);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users?limit=100');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.supplierId || !formData.improvementType || !formData.title || !formData.description || !startDate || !targetDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/quality-improvements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          qualityIssueId: formData.qualityIssueId || undefined,
          assignedTo: formData.assignedTo || undefined,
          currentValue: formData.currentValue ? parseFloat(formData.currentValue) : undefined,
          targetValue: formData.targetValue ? parseFloat(formData.targetValue) : undefined,
          startDate: startDate.toISOString(),
          targetCompletionDate: targetDate.toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create quality improvement plan');
      }

      const improvement = await response.json();
      toast.success('Quality improvement plan created successfully');
      router.push(`/quality/improvements/${improvement.id}`);
    } catch (error) {
      console.error('Error creating quality improvement plan:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create quality improvement plan');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const filteredQualityIssues = qualityIssues.filter(issue => 
    !formData.supplierId || issue.supplier.id === formData.supplierId
  );

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button variant="outline" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Quality Improvement Plan</h1>
          <p className="text-muted-foreground">
            Design a comprehensive plan to improve supplier quality performance
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Improvement Plan Details
            </CardTitle>
            <CardDescription>
              Define the scope and objectives of the quality improvement initiative
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="supplierId">Supplier *</Label>
                <Select value={formData.supplierId} onValueChange={(value) => handleInputChange('supplierId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select supplier" />
                  </SelectTrigger>
                  <SelectContent>
                    {suppliers.map((supplier) => (
                      <SelectItem key={supplier.id} value={supplier.id}>
                        {supplier.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="qualityIssueId">Related Quality Issue (Optional)</Label>
                <Select value={formData.qualityIssueId} onValueChange={(value) => handleInputChange('qualityIssueId', value === 'none' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select quality issue" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None - General improvement</SelectItem>
                    {filteredQualityIssues.map((issue) => (
                      <SelectItem key={issue.id} value={issue.id}>
                        {issue.issueType.replace(/_/g, ' ')} - {issue.severity}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="improvementType">Improvement Type *</Label>
                <Select value={formData.improvementType} onValueChange={(value) => handleInputChange('improvementType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select improvement type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PROCESS_IMPROVEMENT">Process Improvement</SelectItem>
                    <SelectItem value="TRAINING">Training</SelectItem>
                    <SelectItem value="EQUIPMENT_UPGRADE">Equipment Upgrade</SelectItem>
                    <SelectItem value="SUPPLIER_AUDIT">Supplier Audit</SelectItem>
                    <SelectItem value="QUALITY_CONTROL_ENHANCEMENT">Quality Control Enhancement</SelectItem>
                    <SelectItem value="CORRECTIVE_ACTION">Corrective Action</SelectItem>
                    <SelectItem value="PREVENTIVE_ACTION">Preventive Action</SelectItem>
                    <SelectItem value="SUPPLIER_COLLABORATION">Supplier Collaboration</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="priority">Priority *</Label>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="LOW">Low</SelectItem>
                    <SelectItem value="MEDIUM">Medium</SelectItem>
                    <SelectItem value="HIGH">High</SelectItem>
                    <SelectItem value="CRITICAL">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                placeholder="Enter improvement plan title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                placeholder="Describe the improvement plan objectives and approach..."
                rows={4}
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="targetMetric">Target Metric (Optional)</Label>
                <Input
                  id="targetMetric"
                  placeholder="e.g., Return Rate, Defect Rate"
                  value={formData.targetMetric}
                  onChange={(e) => handleInputChange('targetMetric', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="currentValue">Current Value (Optional)</Label>
                <Input
                  id="currentValue"
                  type="number"
                  step="0.01"
                  placeholder="Current metric value"
                  value={formData.currentValue}
                  onChange={(e) => handleInputChange('currentValue', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="targetValue">Target Value (Optional)</Label>
                <Input
                  id="targetValue"
                  type="number"
                  step="0.01"
                  placeholder="Target metric value"
                  value={formData.targetValue}
                  onChange={(e) => handleInputChange('targetValue', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Start Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Pick start date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Target Completion Date *</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !targetDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {targetDate ? format(targetDate, "PPP") : "Pick target date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={targetDate}
                      onSelect={setTargetDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assignedTo">Assigned To (Optional)</Label>
                <Select value={formData.assignedTo} onValueChange={(value) => handleInputChange('assignedTo', value === 'none' ? '' : value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select assignee" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None - Unassigned</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name} ({user.role})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes (Optional)</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes or considerations..."
                rows={3}
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Create Improvement Plan
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
