"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Progress } from "@/components/ui/progress";
import {
  TrendingUp,
  Plus,
  Search,
  Filter,
  Eye,
  Calendar,
  User,
  Building2,
  Target,
  CheckCircle2,
  Clock,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";

interface QualityImprovement {
  id: string;
  title: string;
  description: string;
  improvementType: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  progress: number;
  startDate: string;
  targetCompletionDate: string;
  actualCompletionDate?: string;
  effectivenessScore?: number;
  targetMetric?: string;
  currentValue?: number;
  targetValue?: number;
  supplier: {
    id: string;
    name: string;
    contactPerson: string;
  };
  qualityIssue?: {
    id: string;
    issueType: string;
    severity: string;
  };
  assignee?: {
    id: string;
    name: string;
    role: string;
  };
  creator: {
    id: string;
    name: string;
    role: string;
  };
  approver?: {
    id: string;
    name: string;
    role: string;
  };
  actions: Array<{
    id: string;
    title: string;
    status: string;
    dueDate?: string;
  }>;
}

interface QualityImprovementsResponse {
  qualityImprovements: QualityImprovement[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export default function QualityImprovementsPage() {
  const router = useRouter();
  const [improvements, setImprovements] = useState<QualityImprovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchQualityImprovements = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && statusFilter !== "all" && { status: statusFilter }),
        ...(priorityFilter && priorityFilter !== "all" && { priority: priorityFilter }),
        ...(typeFilter && typeFilter !== "all" && { improvementType: typeFilter }),
      });

      const response = await fetch(`/api/quality-improvements?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality improvements');
      }

      const data: QualityImprovementsResponse = await response.json();
      setImprovements(data.qualityImprovements);
      setTotalPages(data.pagination.pages);
    } catch (error) {
      console.error('Error fetching quality improvements:', error);
      toast.error('Failed to load quality improvements');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualityImprovements();
  }, [currentPage, searchTerm, statusFilter, priorityFilter, typeFilter]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'COMPLETED': return 'bg-green-100 text-green-800 border-green-200';
      case 'ON_HOLD': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CANCELLED': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatImprovementType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'IN_PROGRESS': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'ON_HOLD': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'CANCELLED': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <Calendar className="h-4 w-4 text-blue-600" />;
    }
  };

  const isOverdue = (targetDate: string, status: string) => {
    if (status === 'COMPLETED' || status === 'CANCELLED') return false;
    return new Date(targetDate) < new Date();
  };

  return (
    <MainLayout>
      <PageHeader
        title="Quality Improvements"
        description="Manage quality improvement plans and track their effectiveness"
        actions={
          <Button onClick={() => router.push('/quality/improvements/create')}>
            <Plus className="mr-2 h-4 w-4" />
            Create Improvement Plan
          </Button>
        }
      />

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search improvements..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="PLANNED">Planned</SelectItem>
                <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="ON_HOLD">On Hold</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="CRITICAL">Critical</SelectItem>
                <SelectItem value="HIGH">High</SelectItem>
                <SelectItem value="MEDIUM">Medium</SelectItem>
                <SelectItem value="LOW">Low</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="PROCESS_IMPROVEMENT">Process Improvement</SelectItem>
                <SelectItem value="TRAINING">Training</SelectItem>
                <SelectItem value="EQUIPMENT_UPGRADE">Equipment Upgrade</SelectItem>
                <SelectItem value="SUPPLIER_AUDIT">Supplier Audit</SelectItem>
                <SelectItem value="QUALITY_CONTROL_ENHANCEMENT">Quality Control Enhancement</SelectItem>
                <SelectItem value="CORRECTIVE_ACTION">Corrective Action</SelectItem>
                <SelectItem value="PREVENTIVE_ACTION">Preventive Action</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setSearchTerm("");
                setStatusFilter("all");
                setPriorityFilter("all");
                setTypeFilter("all");
              }}
            >
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quality Improvements Table */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Quality Improvement Plans</CardTitle>
          <CardDescription>
            {improvements.length} improvement plans found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Improvement Plan</TableHead>
                    <TableHead>Supplier</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Target Date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {improvements.map((improvement) => (
                    <TableRow key={improvement.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{improvement.title}</div>
                          <div className="text-sm text-muted-foreground line-clamp-2">
                            {improvement.description}
                          </div>
                          {improvement.effectivenessScore && (
                            <div className="flex items-center gap-2">
                              <Target className="h-3 w-3 text-green-600" />
                              <span className="text-xs text-green-600">
                                Effectiveness: {improvement.effectivenessScore}%
                              </span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium">{improvement.supplier.name}</div>
                          <div className="text-sm text-muted-foreground">
                            {improvement.supplier.contactPerson}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {formatImprovementType(improvement.improvementType)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(improvement.status)}
                          <Badge className={getStatusColor(improvement.status)}>
                            {improvement.status.replace('_', ' ')}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(improvement.priority)}>
                          {improvement.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">{improvement.progress}%</span>
                          </div>
                          <Progress value={improvement.progress} className="w-20" />
                          <div className="text-xs text-muted-foreground">
                            {improvement.actions.filter(a => a.status === 'COMPLETED').length} / {improvement.actions.length} actions
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className={`text-sm ${isOverdue(improvement.targetCompletionDate, improvement.status) ? 'text-red-600 font-medium' : ''}`}>
                            {new Date(improvement.targetCompletionDate).toLocaleDateString()}
                          </div>
                          {isOverdue(improvement.targetCompletionDate, improvement.status) && (
                            <div className="text-xs text-red-600">Overdue</div>
                          )}
                          {improvement.actualCompletionDate && (
                            <div className="text-xs text-green-600">
                              Completed: {new Date(improvement.actualCompletionDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/quality/improvements/${improvement.id}`)}
                        >
                          <Eye className="mr-2 h-4 w-4" />
                          View
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="flex items-center px-4">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </MainLayout>
  );
}
