"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";


import {
  ArrowLeft,
  TrendingUp,
  CheckCircle2,
  Clock,
  User,
  Building2,
  Target,
  Calendar,
  Edit,
  Plus,
  AlertCircle,
  Activity,
  Play,
  CheckCircle,
  XCircle
} from "lucide-react";
import { toast } from "sonner";

interface QualityImprovement {
  id: string;
  title: string;
  description: string;
  improvementType: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  progress: number;
  startDate: string;
  targetCompletionDate: string;
  actualCompletionDate?: string;
  effectivenessScore?: number;
  targetMetric?: string;
  currentValue?: number;
  targetValue?: number;
  notes?: string;
  supplier: {
    id: string;
    name: string;
    contactPerson: string;
    phone?: string;
    email?: string;
  };
  qualityIssue?: {
    id: string;
    issueType: string;
    severity: string;
    description: string;
  };
  assignee?: {
    id: string;
    name: string;
    role: string;
    email?: string;
  };
  creator: {
    id: string;
    name: string;
    role: string;
  };
  approver?: {
    id: string;
    name: string;
    role: string;
  };
  actions: Array<{
    id: string;
    actionType: string;
    title: string;
    description: string;
    status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
    dueDate?: string;
    completedAt?: string;
    assignee?: {
      id: string;
      name: string;
      role: string;
    };
  }>;
}

export default function QualityImprovementDetailPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [improvement, setImprovement] = useState<QualityImprovement | null>(null);
  const [loading, setLoading] = useState(true);

  // Action status update state
  const [statusLoading, setStatusLoading] = useState(false);

  // Unwrap params using React.use()
  const { id } = use(params);

  useEffect(() => {
    fetchQualityImprovement();
  }, [id]);

  const fetchQualityImprovement = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/quality-improvements/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality improvement');
      }
      const data = await response.json();
      setImprovement(data);
    } catch (error) {
      console.error('Error fetching quality improvement:', error);
      toast.error('Failed to load quality improvement');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectStatusUpdate = async (action: any, newStatus: string) => {
    try {
      setStatusLoading(true);
      const response = await fetch(`/api/quality-improvements/${id}/actions/${action.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update action status');
      }

      toast.success(`Action marked as ${newStatus.replace('_', ' ').toLowerCase()}`);

      // Refresh the improvement data
      await fetchQualityImprovement();
    } catch (error) {
      console.error('Error updating action status:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update action status');
    } finally {
      setStatusLoading(false);
    }
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PLANNED': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'IN_PROGRESS': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'COMPLETED': return 'bg-green-100 text-green-800 border-green-200';
      case 'ON_HOLD': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'CANCELLED': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'HIGH': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'LOW': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getActionStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'IN_PROGRESS': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'CANCELLED': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return <Activity className="h-4 w-4 text-blue-600" />;
    }
  };

  const formatImprovementType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const isOverdue = (targetDate: string, status: string) => {
    if (status === 'COMPLETED' || status === 'CANCELLED') return false;
    return new Date(targetDate) < new Date();
  };

  const getAvailableStatusTransitions = (currentStatus: string) => {
    const transitions = {
      'PENDING': ['IN_PROGRESS', 'CANCELLED'],
      'IN_PROGRESS': ['COMPLETED', 'CANCELLED'],
      'COMPLETED': [], // No transitions from completed
      'CANCELLED': ['PENDING'] // Can reopen cancelled actions
    };
    return transitions[currentStatus as keyof typeof transitions] || [];
  };

  const getStatusButtonConfig = (status: string) => {
    const configs = {
      'IN_PROGRESS': {
        icon: <Play className="h-3 w-3" />,
        label: 'Start',
        variant: 'default' as const,
        className: 'bg-blue-600 hover:bg-blue-700'
      },
      'COMPLETED': {
        icon: <CheckCircle className="h-3 w-3" />,
        label: 'Complete',
        variant: 'default' as const,
        className: 'bg-green-600 hover:bg-green-700'
      },
      'CANCELLED': {
        icon: <XCircle className="h-3 w-3" />,
        label: 'Cancel',
        variant: 'destructive' as const,
        className: ''
      },
      'PENDING': {
        icon: <Activity className="h-3 w-3" />,
        label: 'Reopen',
        variant: 'outline' as const,
        className: ''
      }
    };
    return configs[status as keyof typeof configs];
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  if (!improvement) {
    return (
      <MainLayout>
        <div className="text-center py-8">
          <h2 className="text-2xl font-bold">Quality Improvement Plan Not Found</h2>
          <p className="text-muted-foreground mt-2">The requested improvement plan could not be found.</p>
          <Button className="mt-4" onClick={() => router.push('/quality/improvements')}>
            Back to Quality Improvements
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title={improvement.title}
        description={`${formatImprovementType(improvement.improvementType)} - ${improvement.supplier.name}`}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <Button variant="outline" onClick={() => router.push(`/quality/improvements/${improvement.id}/edit`)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </Button>
            <Button onClick={() => router.push(`/quality/improvements/${improvement.id}/actions/create`)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Action
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Improvement Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Improvement Overview
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Badge className={getStatusColor(improvement.status)}>
                  {improvement.status.replace('_', ' ')}
                </Badge>
                <Badge className={getPriorityColor(improvement.priority)}>
                  {improvement.priority}
                </Badge>
                {improvement.effectivenessScore && (
                  <Badge variant="outline">
                    <Target className="mr-1 h-3 w-3" />
                    Effectiveness: {improvement.effectivenessScore}%
                  </Badge>
                )}
              </div>

              <div>
                <h4 className="font-semibold mb-2">Description</h4>
                <p className="text-muted-foreground">{improvement.description}</p>
              </div>

              {improvement.notes && (
                <div>
                  <h4 className="font-semibold mb-2">Notes</h4>
                  <p className="text-muted-foreground">{improvement.notes}</p>
                </div>
              )}

              {improvement.targetMetric && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <h4 className="font-semibold mb-2">Target Metric</h4>
                    <p className="text-muted-foreground">{improvement.targetMetric}</p>
                  </div>
                  {improvement.currentValue !== undefined && (
                    <div>
                      <h4 className="font-semibold mb-2">Current Value</h4>
                      <p className="text-muted-foreground">{improvement.currentValue}</p>
                    </div>
                  )}
                  {improvement.targetValue !== undefined && (
                    <div>
                      <h4 className="font-semibold mb-2">Target Value</h4>
                      <p className="text-muted-foreground">{improvement.targetValue}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Progress & Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Progress & Timeline</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">Overall Progress</span>
                  <span className="text-sm font-medium">{improvement.progress}%</span>
                </div>
                <Progress value={improvement.progress} className="h-3" />
                <div className="text-xs text-muted-foreground mt-1">
                  {improvement.actions.filter(a => a.status === 'COMPLETED').length} of {improvement.actions.length} actions completed
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Timeline
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Start Date:</span>
                      <span>{new Date(improvement.startDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Target Date:</span>
                      <span className={isOverdue(improvement.targetCompletionDate, improvement.status) ? 'text-red-600 font-medium' : ''}>
                        {new Date(improvement.targetCompletionDate).toLocaleDateString()}
                        {isOverdue(improvement.targetCompletionDate, improvement.status) && ' (Overdue)'}
                      </span>
                    </div>
                    {improvement.actualCompletionDate && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Completed:</span>
                        <span className="text-green-600">{new Date(improvement.actualCompletionDate).toLocaleDateString()}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Key Metrics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Actions:</span>
                      <span>{improvement.actions.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Completed:</span>
                      <span className="text-green-600">{improvement.actions.filter(a => a.status === 'COMPLETED').length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">In Progress:</span>
                      <span className="text-yellow-600">{improvement.actions.filter(a => a.status === 'IN_PROGRESS').length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Pending:</span>
                      <span className="text-blue-600">{improvement.actions.filter(a => a.status === 'PENDING').length}</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Items */}
          <Card>
            <CardHeader>
              <CardTitle>Action Items</CardTitle>
              <CardDescription>
                Specific actions to achieve the improvement objectives
              </CardDescription>
            </CardHeader>
            <CardContent>
              {improvement.actions.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">No action items have been created yet.</p>
                  <Button 
                    className="mt-4" 
                    onClick={() => router.push(`/quality/improvements/${improvement.id}/actions/create`)}
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add First Action
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {improvement.actions.map((action) => (
                    <div key={action.id} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getActionStatusIcon(action.status)}
                          <h4 className="font-medium">{action.title}</h4>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className={getStatusColor(action.status)}>
                            {action.status.replace('_', ' ')}
                          </Badge>
                        </div>
                      </div>

                      {/* Action Status Buttons */}
                      {getAvailableStatusTransitions(action.status).length > 0 && (
                        <div className="flex items-center gap-2 pt-2 border-t">
                          <span className="text-xs text-muted-foreground">Quick actions:</span>
                          <div className="flex gap-1">
                            {getAvailableStatusTransitions(action.status).map((status) => {
                              const config = getStatusButtonConfig(status);
                              if (!config) return null;

                              return (
                                <Button
                                  key={status}
                                  size="sm"
                                  variant={config.variant}
                                  className={`h-7 px-2 text-xs ${config.className}`}
                                  onClick={() => handleDirectStatusUpdate(action, status)}
                                  disabled={statusLoading}
                                >
                                  {config.icon}
                                  <span className="ml-1">{config.label}</span>
                                </Button>
                              );
                            })}
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-7 px-2 text-xs"
                              onClick={() => router.push(`/quality/improvements/${id}/actions/${action.id}/edit`)}
                              disabled={statusLoading}
                            >
                              <Edit className="h-3 w-3" />
                              <span className="ml-1">Edit</span>
                            </Button>
                          </div>
                        </div>
                      )}
                      <p className="text-sm text-muted-foreground">{action.description}</p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <div className="flex items-center gap-4">
                          <span>Type: {action.actionType.replace('_', ' ')}</span>
                          {action.assignee && (
                            <span>Assigned to: {action.assignee.name}</span>
                          )}
                        </div>
                        <div className="flex items-center gap-4">
                          {action.dueDate && (
                            <span>Due: {new Date(action.dueDate).toLocaleDateString()}</span>
                          )}
                          {action.completedAt && (
                            <span className="text-green-600">
                              Completed: {new Date(action.completedAt).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Supplier Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Name:</span>
                <p className="text-muted-foreground">{improvement.supplier.name}</p>
              </div>
              <div>
                <span className="font-medium">Contact Person:</span>
                <p className="text-muted-foreground">{improvement.supplier.contactPerson}</p>
              </div>
              {improvement.supplier.phone && (
                <div>
                  <span className="font-medium">Phone:</span>
                  <p className="text-muted-foreground">{improvement.supplier.phone}</p>
                </div>
              )}
              {improvement.supplier.email && (
                <div>
                  <span className="font-medium">Email:</span>
                  <p className="text-muted-foreground">{improvement.supplier.email}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Team Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Team Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div>
                <span className="font-medium">Created by:</span>
                <p className="text-muted-foreground">{improvement.creator.name} ({improvement.creator.role})</p>
              </div>
              {improvement.assignee && (
                <div>
                  <span className="font-medium">Assigned to:</span>
                  <p className="text-muted-foreground">{improvement.assignee.name} ({improvement.assignee.role})</p>
                </div>
              )}
              {improvement.approver && (
                <div>
                  <span className="font-medium">Approved by:</span>
                  <p className="text-muted-foreground">{improvement.approver.name} ({improvement.approver.role})</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Related Quality Issue */}
          {improvement.qualityIssue && (
            <Card>
              <CardHeader>
                <CardTitle>Related Quality Issue</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div>
                  <span className="font-medium">Type:</span>
                  <p className="text-muted-foreground">{improvement.qualityIssue.issueType.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="font-medium">Severity:</span>
                  <Badge className={getPriorityColor(improvement.qualityIssue.severity)}>
                    {improvement.qualityIssue.severity}
                  </Badge>
                </div>
                <div>
                  <span className="font-medium">Description:</span>
                  <p className="text-muted-foreground">{improvement.qualityIssue.description}</p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-2"
                  onClick={() => router.push(`/quality/issues/${improvement.qualityIssue!.id}`)}
                >
                  View Issue
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>


    </MainLayout>
  );
}
