"use client";

import React, { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { ArrowLeft, CalendarIcon, Save, User, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface QualityImprovement {
  id: string;
  title: string;
  description: string;
  improvementType: string;
  status: 'PLANNED' | 'IN_PROGRESS' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED';
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  progress: number;
  startDate: string;
  targetCompletionDate: string;
  actualCompletionDate?: string;
  effectivenessScore?: number;
  targetMetric?: string;
  currentValue?: number;
  targetValue?: number;
  notes?: string;
  supplier: {
    id: string;
    name: string;
  };
  assignee?: {
    id: string;
    name: string;
    role: string;
  };
}

interface User {
  id: string;
  name: string;
  role: string;
  email: string;
}

export default function EditQualityImprovementPage({ params }: { params: { id: string } }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [improvement, setImprovement] = useState<QualityImprovement | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [targetDate, setTargetDate] = useState<Date>();
  const [actualDate, setActualDate] = useState<Date>();
  const [showTargetCalendar, setShowTargetCalendar] = useState(false);
  const [showActualCalendar, setShowActualCalendar] = useState(false);
  const [formData, setFormData] = useState({
    status: "",
    progress: "",
    effectivenessScore: "",
    assignedTo: "",
    notes: "",
  });

  // Unwrap params using React.use()
  const { id } = use(params);

  useEffect(() => {
    fetchImprovement();
    fetchUsers();
  }, [id]);

  const fetchImprovement = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/quality-improvements/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quality improvement');
      }
      const data = await response.json();
      setImprovement(data);
      
      // Populate form with existing data
      setFormData({
        status: data.status,
        progress: data.progress.toString(),
        effectivenessScore: data.effectivenessScore?.toString() || "",
        assignedTo: data.assignee?.id || "unassigned",
        notes: data.notes || "",
      });
      
      // Set dates
      if (data.targetCompletionDate) {
        setTargetDate(new Date(data.targetCompletionDate));
      }
      if (data.actualCompletionDate) {
        setActualDate(new Date(data.actualCompletionDate));
      }
    } catch (error) {
      console.error('Error fetching quality improvement:', error);
      toast.error('Failed to load quality improvement');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users?roles=SUPER_ADMIN,WAREHOUSE_ADMIN,FINANCE_ADMIN');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.status) {
      toast.error('Please select a status');
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/quality-improvements/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: formData.status,
          progress: formData.progress ? parseInt(formData.progress) : undefined,
          effectivenessScore: formData.effectivenessScore ? parseInt(formData.effectivenessScore) : undefined,
          assignedTo: formData.assignedTo === "unassigned" ? undefined : formData.assignedTo,
          notes: formData.notes || undefined,
          targetCompletionDate: targetDate?.toISOString(),
          actualCompletionDate: actualDate?.toISOString(),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update quality improvement');
      }

      const updatedImprovement = await response.json();
      toast.success('Quality improvement updated successfully');
      router.push(`/quality/improvements/${id}`);
    } catch (error) {
      console.error('Error updating quality improvement:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to update quality improvement');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatImprovementType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (!improvement) {
    return (
      <MainLayout>
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <PageHeader
        title="Edit Quality Improvement"
        description={`${formatImprovementType(improvement.improvementType)} - ${improvement.supplier.name}`}
        actions={
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        }
      />

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Improvement Details
            </CardTitle>
            <CardDescription>
              Update the status and progress of this quality improvement plan
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Read-only information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Title</Label>
                <p className="text-sm">{improvement.title}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Type</Label>
                <p className="text-sm">{formatImprovementType(improvement.improvementType)}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Supplier</Label>
                <p className="text-sm">{improvement.supplier.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-muted-foreground">Priority</Label>
                <p className="text-sm">{improvement.priority}</p>
              </div>
            </div>

            {/* Editable fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="PLANNED">Planned</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                    <SelectItem value="ON_HOLD">On Hold</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assignedTo">Assigned To</Label>
                <Select value={formData.assignedTo} onValueChange={(value) => handleInputChange('assignedTo', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select assignee (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="unassigned">Unassigned</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id}>
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4" />
                          {user.name} ({user.role})
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="progress">Progress (%)</Label>
                <Input
                  id="progress"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="Enter progress percentage"
                  value={formData.progress}
                  onChange={(e) => handleInputChange('progress', e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="effectivenessScore">Effectiveness Score (%)</Label>
                <Input
                  id="effectivenessScore"
                  type="number"
                  min="0"
                  max="100"
                  placeholder="Enter effectiveness score"
                  value={formData.effectivenessScore}
                  onChange={(e) => handleInputChange('effectivenessScore', e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Target Completion Date</Label>
                <Popover open={showTargetCalendar} onOpenChange={setShowTargetCalendar}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !targetDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {targetDate ? format(targetDate, "PPP") : "Select target date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={targetDate}
                      onSelect={(date) => {
                        setTargetDate(date);
                        setShowTargetCalendar(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Actual Completion Date</Label>
                <Popover open={showActualCalendar} onOpenChange={setShowActualCalendar}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !actualDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {actualDate ? format(actualDate, "PPP") : "Select actual date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={actualDate}
                      onSelect={(date) => {
                        setActualDate(date);
                        setShowActualCalendar(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes or updates..."
                rows={4}
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={() => router.back()}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Updating...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Update Improvement
              </>
            )}
          </Button>
        </div>
      </form>
    </MainLayout>
  );
}
