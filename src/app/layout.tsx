import type { Metada<PERSON> } from "next";
import { <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { SettingsProvider } from "@/contexts/settings-context";
import { StoreInfoProvider } from "@/contexts/store-info-context";
import { Toaster } from "@/components/ui/sonner";

// We're using locally hosted Inter font instead of Geist Sans
// See fonts.css for the font declarations

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Next POS - Modern Point of Sale System",
  description: "A modern point of sale and inventory management system",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistMono.variable} font-sans antialiased`}>
        <SettingsProvider>
          <StoreInfoProvider>
            {children}
            <Toaster
              position="top-center"
              richColors
              closeButton
              duration={3000}
              toastOptions={{
                style: {
                  marginBottom: "8px"
                },
                className: "toast-item",
              }}
            />
          </StoreInfoProvider>
        </SettingsProvider>
      </body>
    </html>
  );
}
