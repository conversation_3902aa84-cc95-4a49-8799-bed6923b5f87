"use client";

import { useState } from "react";

export default function TestDeviceControl() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const authorizeDevice = async (userId: string) => {
    setLoading(true);
    try {
      const response = await fetch("/api/test-authorize-device", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId }),
      });
      
      const data = await response.json();
      setResult(data);
      console.log("Authorize result:", data);
    } catch (error) {
      console.error("Error authorizing device:", error);
      setResult({ error: "Failed to authorize device" });
    } finally {
      setLoading(false);
    }
  };

  const revokeDevice = async (userId: string) => {
    setLoading(true);
    try {
      const response = await fetch("/api/test-authorize-device", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ userId }),
      });
      
      const data = await response.json();
      setResult(data);
      console.log("Revoke result:", data);
    } catch (error) {
      console.error("Error revoking device:", error);
      setResult({ error: "Failed to revoke device" });
    } finally {
      setLoading(false);
    }
  };

  const testDeviceAuth = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/test-device-auth");
      const data = await response.json();
      setResult(data);
      console.log("Device auth test result:", data);
    } catch (error) {
      console.error("Error testing device auth:", error);
      setResult({ error: "Failed to test device auth" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Device Authorization Test Control Panel
        </h1>

        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Device Control Actions</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="space-y-4">
              <h3 className="font-medium text-gray-700">Jane (CASHIER)</h3>
              <div className="space-y-2">
                <button
                  onClick={() => authorizeDevice("cmbardui9002wcj64hw76ebq0")}
                  disabled={loading}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  Authorize Jane's Device
                </button>
                <button
                  onClick={() => revokeDevice("cmbardui9002wcj64hw76ebq0")}
                  disabled={loading}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
                >
                  Revoke Jane's Device
                </button>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="font-medium text-gray-700">SUPER_ADMIN</h3>
              <div className="space-y-2">
                <button
                  onClick={() => authorizeDevice("cmb22whmb0000ul3o1imdwvoe")}
                  disabled={loading}
                  className="w-full bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50"
                >
                  Authorize SUPER_ADMIN's Device
                </button>
                <button
                  onClick={() => revokeDevice("cmb22whmb0000ul3o1imdwvoe")}
                  disabled={loading}
                  className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 disabled:opacity-50"
                >
                  Revoke SUPER_ADMIN's Device
                </button>
              </div>
            </div>
          </div>

          <div className="border-t pt-4">
            <button
              onClick={testDeviceAuth}
              disabled={loading}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
            >
              Test Current Device Authorization
            </button>
          </div>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4">Result</h2>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
          <h3 className="font-medium text-yellow-800 mb-2">Instructions:</h3>
          <ol className="list-decimal list-inside text-sm text-yellow-700 space-y-1">
            <li>Use "Authorize Jane's Device" to allow Jane to access POS</li>
            <li>Use "Revoke Jane's Device" to block Jane from POS (should show modal)</li>
            <li>Use "Test Current Device Authorization" to check current status</li>
            <li>Since both browsers use the same device fingerprint, actions affect both Firefox and Edge</li>
          </ol>
        </div>
      </div>
    </div>
  );
}
