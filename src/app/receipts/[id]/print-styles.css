/* Print-specific styles for compact receipt layout */
@media print {
  @page {
    size: A4;
    margin: 0.5in;
  }
  
  body {
    margin: 0;
    padding: 0;
    font-size: 12px;
    line-height: 1.2;
  }
  
  .container {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* Text size utilities */
  .print\:text-xs {
    font-size: 10px !important;
  }
  
  .print\:text-sm {
    font-size: 12px !important;
  }
  
  .print\:text-lg {
    font-size: 16px !important;
  }
  
  /* Padding utilities */
  .print\:py-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  
  .print\:pt-1 {
    padding-top: 0.25rem !important;
  }
  
  .print\:pt-2 {
    padding-top: 0.5rem !important;
  }
  
  .print\:pb-2 {
    padding-bottom: 0.5rem !important;
  }
  
  .print\:px-0 {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  
  /* Margin utilities */
  .print\:mb-0 {
    margin-bottom: 0 !important;
  }
  
  .print\:mb-1 {
    margin-bottom: 0.25rem !important;
  }
  
  .print\:mt-0 {
    margin-top: 0 !important;
  }
  
  .print\:mt-1 {
    margin-top: 0.25rem !important;
  }
  
  .print\:m-0 {
    margin: 0 !important;
  }
  
  /* Font weight utilities */
  .print\:font-normal {
    font-weight: normal !important;
  }
  
  /* Display utilities */
  .print\:hidden {
    display: none !important;
  }
  
  /* Spacing utilities */
  .print\:space-y-2 > * + * {
    margin-top: 0.5rem !important;
  }
  
  /* Border utilities */
  .print\:border-none {
    border: none !important;
  }
  
  /* Shadow utilities */
  .print\:shadow-none {
    box-shadow: none !important;
  }
  
  /* Layout utilities */
  .print\:p-0 {
    padding: 0 !important;
  }
  
  /* Additional print optimizations */
  .print\:break-inside-avoid {
    break-inside: avoid !important;
  }
  
  .print\:page-break-inside-avoid {
    page-break-inside: avoid !important;
  }
}

/* Ensure print styles are applied correctly */
@media print {
  /* Hide print button and navigation */
  .print\:hidden,
  button,
  .no-print {
    display: none !important;
  }
  
  /* Optimize table printing */
  table {
    break-inside: avoid;
  }
  
  tr {
    break-inside: avoid;
    break-after: auto;
  }
  
  /* Ensure proper text rendering */
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
}
