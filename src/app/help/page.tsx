"use client";

import { MainLayout } from "@/components/layout/MainLayout";
import { PageHeader } from "@/components/layout/PageHeader";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  HelpCircle,
  ShoppingCart,
  Users,
  Package,
  Settings,
  MessageSquare,
  DollarSign,
  Receipt,
  Monitor,
  Info,
} from "lucide-react";

export default function HelpPage() {
  return (
    <MainLayout>
      <PageHeader
        title="Help & Documentation"
        description="Get help with using the Next POS system"
      />

      <div className="grid gap-6">
        <Alert>
          <Info className="h-4 w-4" />
          <AlertTitle>Getting Started</AlertTitle>
          <AlertDescription>
            Welcome to Next POS! This help page provides guidance on using the various features of
            the system. If you need additional assistance, please contact your system administrator.
          </AlertDescription>
        </Alert>

        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                Point of Sale (POS)
              </CardTitle>
              <CardDescription>Process sales transactions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Basic Operations</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Search for products by name, SKU, or barcode</li>
                  <li>• Add items to cart and adjust quantities</li>
                  <li>• Apply discounts and select price types</li>
                  <li>• Process payments (cash, card, credit)</li>
                  <li>• Print receipts automatically</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium">Cash Drawer Management</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Open drawer session before starting sales</li>
                  <li>• Monitor cash balance throughout the day</li>
                  <li>• Close drawer session at end of shift</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Customer Management
              </CardTitle>
              <CardDescription>Manage customer information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Customer Features</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Create new customer profiles</li>
                  <li>• Search and select existing customers</li>
                  <li>• Set customer types (Regular, Friend, Family)</li>
                  <li>• View customer transaction history</li>
                  <li>• Apply customer-specific pricing</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Inventory Management
              </CardTitle>
              <CardDescription>Manage products and stock</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Product Management</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Add and edit product information</li>
                  <li>• Set multiple price points</li>
                  <li>• Configure automatic discounts</li>
                  <li>• Manage product categories and units</li>
                  <li>• Track stock levels and adjustments</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                Transaction Management
              </CardTitle>
              <CardDescription>View and manage sales transactions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Transaction Features</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• View transaction history and details</li>
                  <li>• Search transactions by date, customer, or amount</li>
                  <li>• Print receipts for past transactions</li>
                  <li>• View payment methods and status</li>
                  <li>• Track daily sales summaries</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                System Settings
                <Badge variant="secondary">Admin Only</Badge>
              </CardTitle>
              <CardDescription>Configure system features</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Available Settings</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Enable/disable chat system</li>
                  <li>• Configure optional features</li>
                  <li>• Manage user permissions</li>
                  <li>• System backup and restore</li>
                </ul>
              </div>
              <Alert className="mt-3">
                <AlertDescription className="text-xs">
                  Settings are only accessible to Super Admin users. Contact your administrator to
                  enable features like the chat system.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Chat System
                <Badge variant="outline">Optional</Badge>
              </CardTitle>
              <CardDescription>Internal communication system</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium">Chat Features</h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Start conversations with other users</li>
                  <li>• Send and receive real-time messages</li>
                  <li>• Star important conversations</li>
                  <li>• View conversation history</li>
                </ul>
              </div>
              <Alert className="mt-3">
                <AlertDescription className="text-xs">
                  The chat system must be enabled by a Super Admin in the Settings page before it
                  becomes available to users.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>User Roles & Permissions</CardTitle>
            <CardDescription>Understanding different user access levels</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <h4 className="font-medium flex items-center gap-2">
                  <Badge variant="default">Super Admin</Badge>
                </h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Full system access</li>
                  <li>• User management</li>
                  <li>• System settings and configuration</li>
                  <li>• Chat management</li>
                  <li>• Backup and restore</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium flex items-center gap-2">
                  <Badge variant="secondary">Finance Admin</Badge>
                </h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Financial reports and analytics</li>
                  <li>• Cash drawer management</li>
                  <li>• Transaction oversight</li>
                  <li>• Backup access</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium flex items-center gap-2">
                  <Badge variant="outline">Cashier</Badge>
                </h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• Point of sale operations</li>
                  <li>• Customer management</li>
                  <li>• Transaction processing</li>
                  <li>• Cash drawer operations</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium flex items-center gap-2">
                  <Badge variant="destructive">Developer</Badge>
                </h4>
                <ul className="text-sm text-muted-foreground mt-1 space-y-1">
                  <li>• API testing tools</li>
                  <li>• System diagnostics</li>
                  <li>• Development features</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Need More Help?</CardTitle>
            <CardDescription>Additional support resources</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div>
                <h4 className="font-medium">Contact Support</h4>
                <p className="text-sm text-muted-foreground">
                  For technical issues or questions not covered in this help guide, please contact
                  your system administrator or IT support team.
                </p>
              </div>
              <div>
                <h4 className="font-medium">System Information</h4>
                <p className="text-sm text-muted-foreground">
                  Next POS v1.0.0 - A modern point of sale and inventory management system
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
