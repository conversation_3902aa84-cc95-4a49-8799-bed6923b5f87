import { prisma } from "@/auth";
import { differenceInDays, format, getMonth, getQuarter, subMonths, startOfMonth, endOfMonth } from "date-fns";

export interface SeasonalPrediction {
  supplierId: string;
  supplierName: string;
  season: 'Q1' | 'Q2' | 'Q3' | 'Q4' | 'Ramadan' | 'Christmas' | 'Chinese_New_Year';
  seasonPeriod: {
    start: Date;
    end: Date;
    description: string;
  };
  predictions: {
    qualityScore: {
      predicted: number;
      confidence: number;
      historicalAverage: number;
      trend: 'improving' | 'declining' | 'stable';
      riskLevel: 'low' | 'medium' | 'high';
    };
    deliveryPerformance: {
      predictedDelayDays: number;
      onTimeDeliveryRate: number;
      confidence: number;
      historicalPattern: {
        averageDelay: number;
        worstDelay: number;
        bestDelay: number;
      };
    };
    pricingTrends: {
      expectedPriceChange: number; // percentage
      volatility: number;
      confidence: number;
      historicalPriceData: {
        averagePrice: number;
        priceRange: { min: number; max: number };
        volatilityScore: number;
      };
    };
    capacityConstraints: {
      expectedCapacityUtilization: number; // percentage
      riskOfShortages: 'low' | 'medium' | 'high';
      confidence: number;
      historicalCapacityIssues: number;
    };
  };
  recommendations: string[];
  riskFactors: string[];
  opportunities: string[];
  historicalData: {
    yearsAnalyzed: number;
    dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
    seasonalOccurrences: number;
  };
}

export interface SeasonalAnalysisReport {
  supplierId: string;
  supplierName: string;
  analysisDate: Date;
  upcomingSeasons: SeasonalPrediction[];
  overallRiskAssessment: {
    riskLevel: 'low' | 'medium' | 'high';
    primaryConcerns: string[];
    mitigationStrategies: string[];
  };
  strategicRecommendations: {
    priority: 'high' | 'medium' | 'low';
    category: 'quality' | 'delivery' | 'pricing' | 'capacity';
    action: string;
    expectedImpact: string;
    timeframe: string;
  }[];
}

/**
 * Seasonal Supplier Performance Prediction Engine
 * Analyzes historical patterns to predict supplier performance during different seasons
 */
export class SeasonalSupplierPredictionEngine {

  /**
   * Generate seasonal predictions for a supplier
   */
  static async generateSeasonalPredictions(supplierId: string): Promise<SeasonalAnalysisReport> {
    // Get comprehensive supplier data
    const supplier = await this.getSupplierHistoricalData(supplierId);
    
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Define seasons to analyze
    const seasons = this.defineSeasons();
    
    // Generate predictions for each season
    const predictions: SeasonalPrediction[] = [];
    
    for (const season of seasons) {
      const prediction = await this.generateSeasonPrediction(supplier, season);
      predictions.push(prediction);
    }

    // Calculate overall risk assessment
    const overallRiskAssessment = this.calculateOverallRisk(predictions);
    
    // Generate strategic recommendations
    const strategicRecommendations = this.generateStrategicRecommendations(predictions, supplier);

    return {
      supplierId: supplier.id,
      supplierName: supplier.name,
      analysisDate: new Date(),
      upcomingSeasons: predictions,
      overallRiskAssessment,
      strategicRecommendations
    };
  }

  /**
   * Get comprehensive historical data for supplier
   */
  private static async getSupplierHistoricalData(supplierId: string) {
    const lookbackMonths = 24; // 2 years of data for seasonal analysis
    const startDate = subMonths(new Date(), lookbackMonths);

    return await prisma.supplier.findUnique({
      where: { id: supplierId },
      include: {
        purchaseOrders: {
          where: {
            orderDate: { gte: startDate }
          },
          include: {
            items: {
              include: {
                product: {
                  select: { id: true, name: true, category: { select: { name: true } } }
                }
              }
            },
            stockBatches: true
          }
        },
        productSuppliers: {
          where: { isActive: true },
          include: {
            stockBatches: {
              where: {
                receivedDate: { gte: startDate }
              },
              orderBy: { receivedDate: 'desc' }
            }
          }
        }
      }
    });
  }

  /**
   * Define seasons for analysis
   */
  private static defineSeasons() {
    const currentYear = new Date().getFullYear();
    
    return [
      {
        name: 'Q1' as const,
        start: new Date(currentYear, 0, 1), // January 1
        end: new Date(currentYear, 2, 31), // March 31
        description: 'Q1 - New Year period, post-holiday recovery'
      },
      {
        name: 'Q2' as const,
        start: new Date(currentYear, 3, 1), // April 1
        end: new Date(currentYear, 5, 30), // June 30
        description: 'Q2 - Spring season, Ramadan period'
      },
      {
        name: 'Q3' as const,
        start: new Date(currentYear, 6, 1), // July 1
        end: new Date(currentYear, 8, 30), // September 30
        description: 'Q3 - Summer season, back-to-school'
      },
      {
        name: 'Q4' as const,
        start: new Date(currentYear, 9, 1), // October 1
        end: new Date(currentYear, 11, 31), // December 31
        description: 'Q4 - Holiday season, year-end rush'
      },
      {
        name: 'Ramadan' as const,
        start: new Date(currentYear, 3, 10), // Approximate Ramadan start
        end: new Date(currentYear, 4, 10), // Approximate Ramadan end
        description: 'Ramadan - Fasting month, altered consumption patterns'
      },
      {
        name: 'Christmas' as const,
        start: new Date(currentYear, 11, 15), // December 15
        end: new Date(currentYear + 1, 0, 7), // January 7
        description: 'Christmas/New Year - Holiday season peak'
      }
    ];
  }

  /**
   * Generate prediction for a specific season
   */
  private static async generateSeasonPrediction(
    supplier: any,
    season: any
  ): Promise<SeasonalPrediction> {
    
    // Filter historical data for this season across multiple years
    const seasonalData = this.extractSeasonalData(supplier, season);
    
    // Analyze quality patterns
    const qualityPrediction = this.predictSeasonalQuality(seasonalData);
    
    // Analyze delivery patterns
    const deliveryPrediction = this.predictSeasonalDelivery(seasonalData);
    
    // Analyze pricing patterns
    const pricingPrediction = this.predictSeasonalPricing(seasonalData);
    
    // Analyze capacity patterns
    const capacityPrediction = this.predictSeasonalCapacity(seasonalData);
    
    // Generate recommendations and risk factors
    const { recommendations, riskFactors, opportunities } = this.generateSeasonalInsights(
      season,
      qualityPrediction,
      deliveryPrediction,
      pricingPrediction,
      capacityPrediction
    );

    return {
      supplierId: supplier.id,
      supplierName: supplier.name,
      season: season.name,
      seasonPeriod: {
        start: season.start,
        end: season.end,
        description: season.description
      },
      predictions: {
        qualityScore: qualityPrediction,
        deliveryPerformance: deliveryPrediction,
        pricingTrends: pricingPrediction,
        capacityConstraints: capacityPrediction
      },
      recommendations,
      riskFactors,
      opportunities,
      historicalData: {
        yearsAnalyzed: seasonalData.yearsAnalyzed,
        dataQuality: seasonalData.dataQuality,
        seasonalOccurrences: seasonalData.occurrences
      }
    };
  }

  /**
   * Extract historical data for specific season
   */
  private static extractSeasonalData(supplier: any, season: any) {
    const seasonalPOs: any[] = [];
    const seasonalBatches: any[] = [];
    const yearsFound = new Set<number>();

    // Extract purchase orders from the season across multiple years
    supplier.purchaseOrders.forEach((po: any) => {
      const poDate = new Date(po.orderDate);
      const poMonth = getMonth(poDate);
      const poYear = poDate.getFullYear();
      
      // Check if PO falls within seasonal pattern
      if (this.isDateInSeason(poDate, season)) {
        seasonalPOs.push(po);
        yearsFound.add(poYear);
        
        // Add associated batches
        if (po.stockBatches) {
          seasonalBatches.push(...po.stockBatches);
        }
      }
    });

    // Determine data quality based on years of data
    const yearsAnalyzed = yearsFound.size;
    const dataQuality = yearsAnalyzed >= 2 ? 'good' : 
                       yearsAnalyzed >= 1 ? 'fair' : 'poor';

    return {
      purchaseOrders: seasonalPOs,
      stockBatches: seasonalBatches,
      yearsAnalyzed,
      dataQuality: dataQuality as 'excellent' | 'good' | 'fair' | 'poor',
      occurrences: seasonalPOs.length
    };
  }

  /**
   * Check if a date falls within a season pattern
   */
  private static isDateInSeason(date: Date, season: any): boolean {
    const month = getMonth(date) + 1; // getMonth returns 0-11, we want 1-12
    const quarter = getQuarter(date);

    switch (season.name) {
      case 'Q1': return quarter === 1;
      case 'Q2': return quarter === 2;
      case 'Q3': return quarter === 3;
      case 'Q4': return quarter === 4;
      case 'Ramadan': return month >= 4 && month <= 5; // Approximate
      case 'Christmas': return month === 12 || month === 1;
      default: return false;
    }
  }

  /**
   * Predict seasonal quality performance
   */
  private static predictSeasonalQuality(seasonalData: any) {
    if (seasonalData.stockBatches.length === 0) {
      return {
        predicted: 75, // Default assumption
        confidence: 20,
        historicalAverage: 75,
        trend: 'stable' as const,
        riskLevel: 'medium' as const
      };
    }

    // Calculate quality score based on batch consistency and delivery performance
    const totalBatches = seasonalData.stockBatches.length;
    const totalPOs = seasonalData.purchaseOrders.length;

    // Calculate quality score based on actual data
    let qualityScore = this.calculateQualityScore(seasonalData, totalBatches, totalPOs);
    
    // Analyze trend over years
    const yearlyQuality = this.calculateYearlyQualityTrend(seasonalData.stockBatches);
    const trend = this.determineTrend(yearlyQuality);
    
    const confidence = seasonalData.dataQuality === 'good' ? 80 : 
                      seasonalData.dataQuality === 'fair' ? 60 : 40;
    
    const riskLevel = qualityScore >= 85 ? 'low' : 
                     qualityScore >= 70 ? 'medium' : 'high';

    return {
      predicted: Math.round(qualityScore),
      confidence,
      historicalAverage: Math.round(qualityScore),
      trend,
      riskLevel
    };
  }

  /**
   * Predict seasonal delivery performance
   */
  private static predictSeasonalDelivery(seasonalData: any) {
    if (seasonalData.purchaseOrders.length === 0) {
      return {
        predictedDelayDays: 3,
        onTimeDeliveryRate: 80,
        confidence: 20,
        historicalPattern: {
          averageDelay: 3,
          worstDelay: 7,
          bestDelay: 0
        }
      };
    }

    const deliveryDelays: number[] = [];
    let onTimeDeliveries = 0;

    seasonalData.purchaseOrders.forEach((po: any) => {
      if (po.expectedDeliveryDate && po.receivedDate) {
        const delay = differenceInDays(new Date(po.receivedDate), new Date(po.expectedDeliveryDate));
        deliveryDelays.push(Math.max(0, delay));

        if (delay <= 1) { // Consider 1 day tolerance as "on time"
          onTimeDeliveries++;
        }
      }
    });

    const averageDelay = deliveryDelays.length > 0 ?
      deliveryDelays.reduce((sum, delay) => sum + delay, 0) / deliveryDelays.length : 0;

    const onTimeRate = seasonalData.purchaseOrders.length > 0 ?
      (onTimeDeliveries / seasonalData.purchaseOrders.length) * 100 : 80;

    const confidence = seasonalData.dataQuality === 'good' ? 85 :
                      seasonalData.dataQuality === 'fair' ? 65 : 45;

    return {
      predictedDelayDays: Math.round(averageDelay),
      onTimeDeliveryRate: Math.round(onTimeRate),
      confidence,
      historicalPattern: {
        averageDelay: Math.round(averageDelay),
        worstDelay: deliveryDelays.length > 0 ? Math.max(...deliveryDelays) : 0,
        bestDelay: deliveryDelays.length > 0 ? Math.min(...deliveryDelays) : 0
      }
    };
  }

  /**
   * Predict seasonal pricing trends
   */
  private static predictSeasonalPricing(seasonalData: any) {
    if (seasonalData.stockBatches.length === 0) {
      return {
        expectedPriceChange: 0,
        volatility: 10,
        confidence: 20,
        historicalPriceData: {
          averagePrice: 0,
          priceRange: { min: 0, max: 0 },
          volatilityScore: 10
        }
      };
    }

    // Extract prices from batches
    const prices = seasonalData.stockBatches.map((batch: any) => Number(batch.purchasePrice));
    const averagePrice = prices.reduce((sum: number, price: number) => sum + price, 0) / prices.length;
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);

    // Calculate price volatility
    const priceVariance = prices.reduce((sum: number, price: number) =>
      sum + Math.pow(price - averagePrice, 2), 0) / prices.length;
    const volatilityScore = Math.sqrt(priceVariance) / averagePrice * 100;

    // Predict price change based on historical pattern
    const yearlyPrices = this.calculateYearlyPriceTrend(seasonalData.stockBatches);
    const expectedPriceChange = this.calculatePriceChangeExpectation(yearlyPrices);

    const confidence = seasonalData.dataQuality === 'good' ? 75 :
                      seasonalData.dataQuality === 'fair' ? 55 : 35;

    return {
      expectedPriceChange: Math.round(expectedPriceChange * 100) / 100,
      volatility: Math.round(volatilityScore),
      confidence,
      historicalPriceData: {
        averagePrice: Math.round(averagePrice),
        priceRange: { min: Math.round(minPrice), max: Math.round(maxPrice) },
        volatilityScore: Math.round(volatilityScore)
      }
    };
  }

  /**
   * Predict seasonal capacity constraints
   */
  private static predictSeasonalCapacity(seasonalData: any) {
    if (seasonalData.purchaseOrders.length === 0) {
      return {
        expectedCapacityUtilization: 70,
        riskOfShortages: 'medium' as const,
        confidence: 20,
        historicalCapacityIssues: 0
      };
    }

    // Analyze order fulfillment patterns
    let partialFulfillments = 0;
    let totalOrders = seasonalData.purchaseOrders.length;

    seasonalData.purchaseOrders.forEach((po: any) => {
      const orderedQuantity = po.items.reduce((sum: number, item: any) => sum + Number(item.quantity), 0);
      const receivedQuantity = po.items.reduce((sum: number, item: any) => sum + Number(item.receivedQuantity || 0), 0);

      if (receivedQuantity < orderedQuantity * 0.95) { // Less than 95% fulfilled
        partialFulfillments++;
      }
    });

    const capacityUtilization = totalOrders > 0 ?
      ((totalOrders - partialFulfillments) / totalOrders) * 100 : 70;

    const riskOfShortages = capacityUtilization >= 90 ? 'low' :
                           capacityUtilization >= 75 ? 'medium' : 'high';

    const confidence = seasonalData.dataQuality === 'good' ? 70 :
                      seasonalData.dataQuality === 'fair' ? 50 : 30;

    return {
      expectedCapacityUtilization: Math.round(capacityUtilization),
      riskOfShortages,
      confidence,
      historicalCapacityIssues: partialFulfillments
    };
  }

  /**
   * Calculate quality score based on actual data
   */
  private static calculateQualityScore(
    seasonalData: any,
    totalBatches: number,
    totalPOs: number
  ): number {
    let qualityScore = 50; // Base score

    // Data availability impact
    if (totalBatches === 0 && totalPOs === 0) {
      return 75; // Default neutral score when no data
    }

    // Sample size impact
    if (totalBatches >= 20) qualityScore += 20; // Excellent sample size
    else if (totalBatches >= 10) qualityScore += 15; // Good sample size
    else if (totalBatches >= 5) qualityScore += 10; // Fair sample size
    else qualityScore += 5; // Limited sample size

    // Purchase order performance impact
    if (totalPOs > 0) {
      const receivedPOs = seasonalData.purchaseOrders.filter((po: any) => po.receivedAt).length;
      const onTimeRate = receivedPOs / totalPOs;

      if (onTimeRate >= 0.9) qualityScore += 15; // Excellent delivery
      else if (onTimeRate >= 0.8) qualityScore += 10; // Good delivery
      else if (onTimeRate >= 0.7) qualityScore += 5; // Fair delivery
      else qualityScore -= 5; // Poor delivery
    }

    // Batch consistency impact (based on expiry management)
    if (totalBatches > 0) {
      const expiredBatches = seasonalData.stockBatches.filter((batch: any) =>
        batch.status === 'EXPIRED'
      ).length;
      const expiryRate = expiredBatches / totalBatches;

      if (expiryRate === 0) qualityScore += 10; // No expired batches
      else if (expiryRate <= 0.05) qualityScore += 5; // Low expiry rate
      else if (expiryRate > 0.2) qualityScore -= 10; // High expiry rate
    }

    // Time span consistency (longer history = more reliable score)
    const timeSpanMonths = this.calculateTimeSpanMonths(seasonalData);
    if (timeSpanMonths >= 12) qualityScore += 10; // Full year of data
    else if (timeSpanMonths >= 6) qualityScore += 5; // Half year of data
    else if (timeSpanMonths < 3) qualityScore -= 5; // Limited history

    return Math.max(60, Math.min(95, Math.round(qualityScore)));
  }

  /**
   * Calculate time span in months for the seasonal data
   */
  private static calculateTimeSpanMonths(seasonalData: any): number {
    const allDates = [
      ...seasonalData.stockBatches.map((batch: any) => new Date(batch.receivedDate)),
      ...seasonalData.purchaseOrders.map((po: any) => new Date(po.orderDate))
    ];

    if (allDates.length === 0) return 0;

    const earliest = new Date(Math.min(...allDates.map(d => d.getTime())));
    const latest = new Date(Math.max(...allDates.map(d => d.getTime())));

    const diffTime = latest.getTime() - earliest.getTime();
    const diffMonths = diffTime / (1000 * 60 * 60 * 24 * 30.44); // Average days per month

    return Math.round(diffMonths * 10) / 10; // Round to 1 decimal place
  }

  /**
   * Generate seasonal insights
   */
  private static generateSeasonalInsights(
    season: any,
    quality: any,
    delivery: any,
    pricing: any,
    capacity: any
  ) {
    const recommendations: string[] = [];
    const riskFactors: string[] = [];
    const opportunities: string[] = [];

    // Quality insights
    if (quality.riskLevel === 'high') {
      riskFactors.push(`High quality risk during ${season.name} - implement additional quality checks`);
      recommendations.push('Increase quality inspection frequency during this season');
    } else if (quality.riskLevel === 'low') {
      opportunities.push(`Consistent quality during ${season.name} - consider volume increases`);
    }

    // Delivery insights
    if (delivery.onTimeDeliveryRate < 70) {
      riskFactors.push(`Poor delivery performance during ${season.name} - average ${delivery.predictedDelayDays} days delay`);
      recommendations.push('Plan for extended lead times and increase safety stock');
    } else if (delivery.onTimeDeliveryRate > 90) {
      opportunities.push(`Excellent delivery reliability during ${season.name}`);
    }

    // Pricing insights
    if (Math.abs(pricing.expectedPriceChange) > 10) {
      const direction = pricing.expectedPriceChange > 0 ? 'increase' : 'decrease';
      riskFactors.push(`Significant price ${direction} expected during ${season.name} (${pricing.expectedPriceChange}%)`);
      recommendations.push(`Negotiate fixed pricing or adjust procurement timing for ${season.name}`);
    }

    if (pricing.volatility > 20) {
      riskFactors.push(`High price volatility during ${season.name} - budget carefully`);
    }

    // Capacity insights
    if (capacity.riskOfShortages === 'high') {
      riskFactors.push(`High shortage risk during ${season.name} - supplier capacity constraints likely`);
      recommendations.push('Identify backup suppliers or place orders early for this season');
    } else if (capacity.riskOfShortages === 'low') {
      opportunities.push(`Good supplier capacity during ${season.name} - opportunity for larger orders`);
    }

    // Season-specific insights
    if (season.name === 'Ramadan') {
      recommendations.push('Adjust order timing for Ramadan schedule changes');
      recommendations.push('Monitor consumption pattern changes during fasting period');
    }

    if (season.name === 'Christmas') {
      recommendations.push('Plan for holiday season demand spikes');
      recommendations.push('Ensure adequate inventory before holiday closures');
    }

    return { recommendations, riskFactors, opportunities };
  }

  /**
   * Calculate yearly quality trend
   */
  private static calculateYearlyQualityTrend(batches: any[]) {
    const yearlyData = new Map<number, { total: number }>();

    batches.forEach(batch => {
      const year = new Date(batch.receivedDate).getFullYear();
      if (!yearlyData.has(year)) {
        yearlyData.set(year, { total: 0 });
      }

      const data = yearlyData.get(year)!;
      data.total++;
    });

    return Array.from(yearlyData.entries()).map(([year, data]) => ({
      year,
      qualityScore: data.total > 0 ? 80 + (data.total > 10 ? 5 : 0) : 75 // Simple scoring based on batch volume
    })).sort((a, b) => a.year - b.year);
  }

  /**
   * Calculate yearly price trend
   */
  private static calculateYearlyPriceTrend(batches: any[]) {
    const yearlyPrices = new Map<number, number[]>();

    batches.forEach(batch => {
      const year = new Date(batch.receivedDate).getFullYear();
      if (!yearlyPrices.has(year)) {
        yearlyPrices.set(year, []);
      }
      yearlyPrices.get(year)!.push(Number(batch.purchasePrice));
    });

    return Array.from(yearlyPrices.entries()).map(([year, prices]) => ({
      year,
      averagePrice: prices.reduce((sum, price) => sum + price, 0) / prices.length
    })).sort((a, b) => a.year - b.year);
  }

  /**
   * Determine trend direction
   */
  private static determineTrend(yearlyData: any[]): 'improving' | 'declining' | 'stable' {
    if (yearlyData.length < 2) return 'stable';

    const recent = yearlyData.slice(-2);
    const change = recent[1].qualityScore - recent[0].qualityScore;

    if (change > 5) return 'improving';
    if (change < -5) return 'declining';
    return 'stable';
  }

  /**
   * Calculate expected price change
   */
  private static calculatePriceChangeExpectation(yearlyPrices: any[]): number {
    if (yearlyPrices.length < 2) return 0;

    const recent = yearlyPrices.slice(-2);
    const priceChange = ((recent[1].averagePrice - recent[0].averagePrice) / recent[0].averagePrice) * 100;

    return priceChange;
  }

  /**
   * Calculate overall risk assessment
   */
  private static calculateOverallRisk(predictions: SeasonalPrediction[]) {
    const highRiskSeasons = predictions.filter(p =>
      p.predictions.qualityScore.riskLevel === 'high' ||
      p.predictions.capacityConstraints.riskOfShortages === 'high' ||
      p.predictions.deliveryPerformance.onTimeDeliveryRate < 70
    );

    const riskLevel = highRiskSeasons.length >= 2 ? 'high' :
                     highRiskSeasons.length === 1 ? 'medium' : 'low';

    const primaryConcerns = highRiskSeasons.map(season =>
      `${season.season}: ${season.riskFactors.join(', ')}`
    );

    const mitigationStrategies = [
      'Diversify supplier base for high-risk seasons',
      'Implement early warning systems for seasonal issues',
      'Develop contingency plans for capacity constraints',
      'Negotiate seasonal pricing agreements'
    ];

    return {
      riskLevel,
      primaryConcerns,
      mitigationStrategies
    };
  }

  /**
   * Generate strategic recommendations
   */
  private static generateStrategicRecommendations(predictions: SeasonalPrediction[], supplier: any) {
    const recommendations: any[] = [];

    // Quality-based recommendations
    const qualityRiskSeasons = predictions.filter(p => p.predictions.qualityScore.riskLevel === 'high');
    if (qualityRiskSeasons.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'quality' as const,
        action: `Implement enhanced quality controls for ${qualityRiskSeasons.map(s => s.season).join(', ')}`,
        expectedImpact: 'Reduce return rates and improve customer satisfaction',
        timeframe: 'Before next occurrence'
      });
    }

    // Delivery-based recommendations
    const deliveryRiskSeasons = predictions.filter(p => p.predictions.deliveryPerformance.onTimeDeliveryRate < 75);
    if (deliveryRiskSeasons.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'delivery' as const,
        action: `Plan extended lead times for ${deliveryRiskSeasons.map(s => s.season).join(', ')}`,
        expectedImpact: 'Prevent stockouts and maintain service levels',
        timeframe: '30-60 days before season'
      });
    }

    // Pricing-based recommendations
    const pricingVolatileSeasons = predictions.filter(p => p.predictions.pricingTrends.volatility > 20);
    if (pricingVolatileSeasons.length > 0) {
      recommendations.push({
        priority: 'medium' as const,
        category: 'pricing' as const,
        action: `Negotiate fixed pricing agreements for volatile seasons`,
        expectedImpact: 'Stabilize costs and improve budget predictability',
        timeframe: '90 days before season'
      });
    }

    // Capacity-based recommendations
    const capacityRiskSeasons = predictions.filter(p => p.predictions.capacityConstraints.riskOfShortages === 'high');
    if (capacityRiskSeasons.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        category: 'capacity' as const,
        action: `Secure backup suppliers for high-risk seasons`,
        expectedImpact: 'Ensure supply continuity during peak demand',
        timeframe: '60-90 days before season'
      });
    }

    return recommendations;
  }
}
