import { prisma } from "@/auth";
import { calculateSupplierQualityMetrics } from "@/lib/supplier-quality-metrics";

export interface QualityThresholds {
  returnRateThreshold: number;
  defectRateThreshold: number;
  qualityScoreThreshold: number;
  returnValueThreshold: number;
  escalationEnabled: boolean;
  autoEscalationLevel1Days: number;
  autoEscalationLevel2Days: number;
  notificationEnabled: boolean;
  monitoringPeriodDays: number;
}

export interface QualityAlert {
  supplierId: string;
  supplierName: string;
  alertType: 'RETURN_RATE_EXCEEDED' | 'DEFECT_RATE_EXCEEDED' | 'QUALITY_SCORE_LOW' | 'RETURN_VALUE_HIGH';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  currentValue: number;
  thresholdValue: number;
  message: string;
  recommendedActions: string[];
  affectedProducts?: Array<{
    productId: string;
    productName: string;
    returnRate: number;
    defectCount: number;
  }>;
}

export interface QualityMonitoringResult {
  monitoringDate: Date;
  suppliersMonitored: number;
  alertsGenerated: number;
  alerts: QualityAlert[];
  escalationsTriggered: number;
  summary: {
    highRiskSuppliers: number;
    mediumRiskSuppliers: number;
    lowRiskSuppliers: number;
    totalReturnValue: number;
    averageQualityScore: number;
  };
}

/**
 * Quality Monitoring Service
 * Monitors supplier quality metrics and triggers alerts/escalations
 */
export class QualityMonitoringService {
  
  /**
   * Get current quality thresholds from system settings
   */
  static async getQualityThresholds(): Promise<QualityThresholds> {
    const thresholdSettings = await prisma.systemSetting.findMany({
      where: {
        key: {
          in: [
            'quality_return_rate_threshold',
            'quality_defect_rate_threshold',
            'quality_score_threshold',
            'quality_return_value_threshold',
            'quality_escalation_enabled',
            'quality_auto_escalation_level1_days',
            'quality_auto_escalation_level2_days',
            'quality_notification_enabled',
            'quality_monitoring_period_days',
          ],
        },
      },
    });

    // Default values
    const defaults: QualityThresholds = {
      returnRateThreshold: 5,
      defectRateThreshold: 2,
      qualityScoreThreshold: 70,
      returnValueThreshold: 1000000,
      escalationEnabled: true,
      autoEscalationLevel1Days: 3,
      autoEscalationLevel2Days: 7,
      notificationEnabled: true,
      monitoringPeriodDays: 30,
    };

    // Apply settings
    for (const setting of thresholdSettings) {
      switch (setting.key) {
        case 'quality_return_rate_threshold':
          defaults.returnRateThreshold = parseFloat(setting.value);
          break;
        case 'quality_defect_rate_threshold':
          defaults.defectRateThreshold = parseFloat(setting.value);
          break;
        case 'quality_score_threshold':
          defaults.qualityScoreThreshold = parseFloat(setting.value);
          break;
        case 'quality_return_value_threshold':
          defaults.returnValueThreshold = parseFloat(setting.value);
          break;
        case 'quality_escalation_enabled':
          defaults.escalationEnabled = setting.value === 'true';
          break;
        case 'quality_auto_escalation_level1_days':
          defaults.autoEscalationLevel1Days = parseInt(setting.value);
          break;
        case 'quality_auto_escalation_level2_days':
          defaults.autoEscalationLevel2Days = parseInt(setting.value);
          break;
        case 'quality_notification_enabled':
          defaults.notificationEnabled = setting.value === 'true';
          break;
        case 'quality_monitoring_period_days':
          defaults.monitoringPeriodDays = parseInt(setting.value);
          break;
      }
    }

    return defaults;
  }

  /**
   * Run quality monitoring for all active suppliers
   */
  static async runQualityMonitoring(): Promise<QualityMonitoringResult> {
    const thresholds = await this.getQualityThresholds();
    const monitoringDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - thresholds.monitoringPeriodDays);

    // Get all active suppliers
    const suppliers = await prisma.supplier.findMany({
      where: { isActive: true },
      select: { id: true, name: true },
    });

    const alerts: QualityAlert[] = [];
    let escalationsTriggered = 0;
    let totalReturnValue = 0;
    let totalQualityScore = 0;
    let highRiskSuppliers = 0;
    let mediumRiskSuppliers = 0;
    let lowRiskSuppliers = 0;

    // Monitor each supplier
    for (const supplier of suppliers) {
      try {
        const qualityAnalysis = await calculateSupplierQualityMetrics(
          supplier.id,
          startDate,
          monitoringDate
        );

        const metrics = qualityAnalysis.metrics;
        totalReturnValue += metrics.returnValue;
        totalQualityScore += metrics.qualityScore;

        // Check thresholds and generate alerts
        const supplierAlerts = await this.checkSupplierThresholds(
          supplier,
          metrics,
          thresholds
        );

        alerts.push(...supplierAlerts);

        // Categorize risk level
        if (qualityAnalysis.riskLevel === 'high') {
          highRiskSuppliers++;
        } else if (qualityAnalysis.riskLevel === 'medium') {
          mediumRiskSuppliers++;
        } else {
          lowRiskSuppliers++;
        }

        // Trigger escalations if enabled
        if (thresholds.escalationEnabled && supplierAlerts.length > 0) {
          const escalations = await this.triggerAutoEscalations(
            supplier.id,
            supplierAlerts,
            thresholds
          );
          escalationsTriggered += escalations;
        }

      } catch (error) {
        console.error(`Error monitoring supplier ${supplier.id}:`, error);
      }
    }

    const result: QualityMonitoringResult = {
      monitoringDate,
      suppliersMonitored: suppliers.length,
      alertsGenerated: alerts.length,
      alerts,
      escalationsTriggered,
      summary: {
        highRiskSuppliers,
        mediumRiskSuppliers,
        lowRiskSuppliers,
        totalReturnValue,
        averageQualityScore: suppliers.length > 0 ? totalQualityScore / suppliers.length : 0,
      },
    };

    // Log monitoring result
    console.log(`Quality monitoring completed: ${alerts.length} alerts generated for ${suppliers.length} suppliers`);

    return result;
  }

  /**
   * Check supplier metrics against thresholds
   */
  private static async checkSupplierThresholds(
    supplier: { id: string; name: string },
    metrics: any,
    thresholds: QualityThresholds
  ): Promise<QualityAlert[]> {
    const alerts: QualityAlert[] = [];

    // Check return rate threshold
    if (metrics.returnRate > thresholds.returnRateThreshold) {
      alerts.push({
        supplierId: supplier.id,
        supplierName: supplier.name,
        alertType: 'RETURN_RATE_EXCEEDED',
        severity: this.getSeverity(metrics.returnRate, thresholds.returnRateThreshold),
        currentValue: metrics.returnRate,
        thresholdValue: thresholds.returnRateThreshold,
        message: `Return rate (${metrics.returnRate.toFixed(1)}%) exceeds threshold (${thresholds.returnRateThreshold}%)`,
        recommendedActions: [
          'Review product quality with supplier',
          'Implement additional quality checks',
          'Consider supplier audit',
        ],
      });
    }

    // Check defect rate threshold
    if (metrics.defectRate > thresholds.defectRateThreshold) {
      alerts.push({
        supplierId: supplier.id,
        supplierName: supplier.name,
        alertType: 'DEFECT_RATE_EXCEEDED',
        severity: this.getSeverity(metrics.defectRate, thresholds.defectRateThreshold),
        currentValue: metrics.defectRate,
        thresholdValue: thresholds.defectRateThreshold,
        message: `Defect rate (${metrics.defectRate.toFixed(1)}%) exceeds threshold (${thresholds.defectRateThreshold}%)`,
        recommendedActions: [
          'Investigate defect root causes',
          'Implement corrective actions',
          'Enhance supplier quality control',
        ],
      });
    }

    // Check quality score threshold
    if (metrics.qualityScore < thresholds.qualityScoreThreshold) {
      alerts.push({
        supplierId: supplier.id,
        supplierName: supplier.name,
        alertType: 'QUALITY_SCORE_LOW',
        severity: this.getSeverity(thresholds.qualityScoreThreshold - metrics.qualityScore, 10),
        currentValue: metrics.qualityScore,
        thresholdValue: thresholds.qualityScoreThreshold,
        message: `Quality score (${metrics.qualityScore}) below threshold (${thresholds.qualityScoreThreshold})`,
        recommendedActions: [
          'Schedule supplier performance review',
          'Develop quality improvement plan',
          'Consider supplier training',
        ],
      });
    }

    // Check return value threshold
    if (metrics.returnValue > thresholds.returnValueThreshold) {
      alerts.push({
        supplierId: supplier.id,
        supplierName: supplier.name,
        alertType: 'RETURN_VALUE_HIGH',
        severity: this.getSeverity(metrics.returnValue / thresholds.returnValueThreshold, 1),
        currentValue: metrics.returnValue,
        thresholdValue: thresholds.returnValueThreshold,
        message: `Return value (${(metrics.returnValue / 1000000).toFixed(1)}M IDR) exceeds threshold (${(thresholds.returnValueThreshold / 1000000).toFixed(1)}M IDR)`,
        recommendedActions: [
          'Review supplier contract terms',
          'Implement financial penalties',
          'Consider supplier replacement',
        ],
      });
    }

    return alerts;
  }

  /**
   * Determine alert severity based on threshold breach magnitude
   */
  private static getSeverity(currentValue: number, thresholdValue: number): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const ratio = currentValue / thresholdValue;
    if (ratio >= 3) return 'CRITICAL';
    if (ratio >= 2) return 'HIGH';
    if (ratio >= 1.5) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Trigger automatic escalations for quality issues
   */
  private static async triggerAutoEscalations(
    supplierId: string,
    alerts: QualityAlert[],
    thresholds: QualityThresholds
  ): Promise<number> {
    let escalationsTriggered = 0;

    for (const alert of alerts) {
      try {
        // Check if there's already an open quality issue for this alert type
        const existingIssue = await prisma.qualityIssue.findFirst({
          where: {
            supplierId: supplierId,
            issueType: this.mapAlertTypeToIssueType(alert.alertType),
            status: {
              in: ['OPEN', 'IN_PROGRESS', 'ESCALATED']
            }
          },
          orderBy: {
            reportedAt: 'desc'
          }
        });

        if (existingIssue) {
          // Check if escalation is needed based on age
          const daysSinceReported = Math.floor(
            (new Date().getTime() - existingIssue.reportedAt.getTime()) / (1000 * 60 * 60 * 24)
          );

          let shouldEscalate = false;
          if (existingIssue.escalationLevel === 0 && daysSinceReported >= thresholds.autoEscalationLevel1Days) {
            shouldEscalate = true;
          } else if (existingIssue.escalationLevel === 1 && daysSinceReported >= thresholds.autoEscalationLevel2Days) {
            shouldEscalate = true;
          }

          if (shouldEscalate && alert.severity === 'HIGH' || alert.severity === 'CRITICAL') {
            // Auto-escalate existing issue
            await prisma.qualityIssue.update({
              where: { id: existingIssue.id },
              data: {
                escalationLevel: existingIssue.escalationLevel + 1,
                status: 'ESCALATED',
                escalatedAt: new Date(),
              }
            });
            escalationsTriggered++;
          }
        } else if (alert.severity === 'HIGH' || alert.severity === 'CRITICAL') {
          // Create new quality issue for high/critical alerts
          await prisma.qualityIssue.create({
            data: {
              productId: await this.getRepresentativeProductId(supplierId),
              supplierId: supplierId,
              issueType: this.mapAlertTypeToIssueType(alert.alertType),
              severity: alert.severity,
              description: alert.message,
              affectedQuantity: 1, // Placeholder
              reportedBy: await this.getSystemUserId(),
            }
          });
          escalationsTriggered++;
        }
      } catch (error) {
        console.error(`Error processing escalation for alert ${alert.alertType}:`, error);
      }
    }

    return escalationsTriggered;
  }

  /**
   * Map alert type to quality issue type
   */
  private static mapAlertTypeToIssueType(alertType: string): string {
    const mapping: Record<string, string> = {
      'RETURN_RATE_EXCEEDED': 'QUALITY_DEGRADATION',
      'DEFECT_RATE_EXCEEDED': 'DEFECTIVE_PRODUCT',
      'QUALITY_SCORE_LOW': 'QUALITY_DEGRADATION',
      'RETURN_VALUE_HIGH': 'CUSTOMER_COMPLAINT',
    };
    return mapping[alertType] || 'OTHER';
  }

  /**
   * Get a representative product ID for the supplier
   */
  private static async getRepresentativeProductId(supplierId: string): Promise<string> {
    const productSupplier = await prisma.productSupplier.findFirst({
      where: { supplierId, isActive: true },
      select: { productId: true }
    });
    return productSupplier?.productId || '';
  }

  /**
   * Get system user ID for automated actions
   */
  private static async getSystemUserId(): Promise<string> {
    const systemUser = await prisma.user.findFirst({
      where: { role: 'SUPER_ADMIN' },
      select: { id: true }
    });
    return systemUser?.id || '';
  }
}
