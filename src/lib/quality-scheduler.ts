import { prisma } from "@/auth";
import { QualityAlertSystem } from "@/lib/quality-alert-system";
import { QualityMonitoringService } from "@/lib/quality-monitoring-service";

export interface ScheduledJob {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  schedule: string; // Cron-like expression
  jobType: 'quality_monitoring' | 'trend_analysis' | 'alert_check' | 'report_generation';
  parameters: Record<string, any>;
  lastRun?: string;
  nextRun?: string;
  runCount: number;
  failureCount: number;
  lastError?: string;
}

export interface JobExecutionResult {
  jobId: string;
  jobName: string;
  startTime: string;
  endTime: string;
  duration: number; // milliseconds
  status: 'success' | 'failure' | 'partial';
  result?: any;
  error?: string;
  metrics: {
    alertsGenerated?: number;
    suppliersAnalyzed?: number;
    issuesFound?: number;
  };
}

/**
 * Quality Scheduler Service
 * Manages automated quality monitoring and analysis jobs
 */
export class QualityScheduler {
  private static instance: QualityScheduler;
  private jobs: Map<string, ScheduledJob> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private isRunning: boolean = false;

  private constructor() {}

  static getInstance(): QualityScheduler {
    if (!QualityScheduler.instance) {
      QualityScheduler.instance = new QualityScheduler();
    }
    return QualityScheduler.instance;
  }

  /**
   * Initialize the scheduler with default jobs
   */
  async initialize(): Promise<void> {
    console.log('Initializing Quality Scheduler...');

    // Load existing jobs from database
    await this.loadJobsFromDatabase();

    // Create default jobs if none exist
    if (this.jobs.size === 0) {
      await this.createDefaultJobs();
    }

    // Start the scheduler
    this.start();

    console.log(`Quality Scheduler initialized with ${this.jobs.size} jobs`);
  }

  /**
   * Start the scheduler
   */
  start(): void {
    if (this.isRunning) {
      console.log('Quality Scheduler is already running');
      return;
    }

    this.isRunning = true;
    console.log('Starting Quality Scheduler...');

    // Schedule all enabled jobs
    for (const job of this.jobs.values()) {
      if (job.enabled) {
        this.scheduleJob(job);
      }
    }

    console.log('Quality Scheduler started successfully');
  }

  /**
   * Stop the scheduler
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    console.log('Stopping Quality Scheduler...');

    // Clear all timers
    for (const timer of this.timers.values()) {
      clearTimeout(timer);
    }
    this.timers.clear();

    console.log('Quality Scheduler stopped');
  }

  /**
   * Load jobs from database
   */
  private async loadJobsFromDatabase(): Promise<void> {
    const jobSettings = await prisma.systemSetting.findMany({
      where: {
        key: { startsWith: 'quality_job_' }
      }
    });

    for (const setting of jobSettings) {
      try {
        const job = JSON.parse(setting.value) as ScheduledJob;
        this.jobs.set(job.id, job);
      } catch (error) {
        console.error(`Error loading job ${setting.key}:`, error);
      }
    }
  }

  /**
   * Create default scheduled jobs
   */
  private async createDefaultJobs(): Promise<void> {
    const defaultJobs: Omit<ScheduledJob, 'id'>[] = [
      {
        name: 'Daily Quality Monitoring',
        description: 'Run comprehensive quality monitoring daily at 6 AM',
        enabled: true,
        schedule: '0 6 * * *', // Daily at 6 AM
        jobType: 'quality_monitoring',
        parameters: {},
        runCount: 0,
        failureCount: 0,
      },
      {
        name: 'Hourly Alert Check',
        description: 'Check for quality alerts every hour during business hours',
        enabled: true,
        schedule: '0 8-18 * * 1-5', // Every hour from 8 AM to 6 PM, Monday to Friday
        jobType: 'alert_check',
        parameters: {},
        runCount: 0,
        failureCount: 0,
      },
      {
        name: 'Weekly Trend Analysis',
        description: 'Generate comprehensive trend analysis every Monday',
        enabled: true,
        schedule: '0 7 * * 1', // Every Monday at 7 AM
        jobType: 'trend_analysis',
        parameters: { timeframe: 90 },
        runCount: 0,
        failureCount: 0,
      },
      {
        name: 'Monthly Quality Report',
        description: 'Generate monthly quality reports on the 1st of each month',
        enabled: true,
        schedule: '0 8 1 * *', // 1st of every month at 8 AM
        jobType: 'report_generation',
        parameters: { reportType: 'monthly' },
        runCount: 0,
        failureCount: 0,
      },
    ];

    for (const jobData of defaultJobs) {
      const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const job: ScheduledJob = { ...jobData, id: jobId };
      
      // Save to database
      await prisma.systemSetting.create({
        data: {
          key: `quality_job_${jobId}`,
          value: JSON.stringify(job),
          description: `Quality scheduler job: ${job.name}`,
        }
      });

      this.jobs.set(jobId, job);
    }

    console.log(`Created ${defaultJobs.length} default quality jobs`);
  }

  /**
   * Schedule a job
   */
  private scheduleJob(job: ScheduledJob): void {
    // Calculate next run time based on schedule
    const nextRun = this.calculateNextRun(job.schedule);
    const delay = nextRun.getTime() - Date.now();

    if (delay > 0) {
      const timer = setTimeout(async () => {
        await this.executeJob(job);
        // Reschedule for next run
        if (job.enabled && this.isRunning) {
          this.scheduleJob(job);
        }
      }, delay);

      this.timers.set(job.id, timer);
      
      // Update next run time
      job.nextRun = nextRun.toISOString();
      this.updateJobInDatabase(job);

      console.log(`Scheduled job "${job.name}" to run at ${nextRun.toISOString()}`);
    }
  }

  /**
   * Execute a job
   */
  private async executeJob(job: ScheduledJob): Promise<JobExecutionResult> {
    const startTime = new Date();
    console.log(`Executing job: ${job.name}`);

    const result: JobExecutionResult = {
      jobId: job.id,
      jobName: job.name,
      startTime: startTime.toISOString(),
      endTime: '',
      duration: 0,
      status: 'success',
      metrics: {},
    };

    try {
      switch (job.jobType) {
        case 'quality_monitoring':
          result.result = await QualityMonitoringService.runQualityMonitoring();
          result.metrics.alertsGenerated = result.result.alertsGenerated;
          result.metrics.suppliersAnalyzed = result.result.suppliersMonitored;
          break;

        case 'alert_check':
          result.result = await QualityAlertSystem.runQualityMonitoring();
          result.metrics.alertsGenerated = result.result.alertsTriggered;
          break;

        case 'trend_analysis':
          // This would run trend analysis for all suppliers
          result.result = { message: 'Trend analysis completed' };
          break;

        case 'report_generation':
          // This would generate and distribute reports
          result.result = { message: 'Report generation completed' };
          break;

        default:
          throw new Error(`Unknown job type: ${job.jobType}`);
      }

      job.runCount++;
      job.lastRun = startTime.toISOString();
      job.lastError = undefined;

    } catch (error) {
      console.error(`Error executing job ${job.name}:`, error);
      result.status = 'failure';
      result.error = (error as Error).message;
      job.failureCount++;
      job.lastError = (error as Error).message;
    }

    const endTime = new Date();
    result.endTime = endTime.toISOString();
    result.duration = endTime.getTime() - startTime.getTime();

    // Update job in database
    await this.updateJobInDatabase(job);

    // Log execution result
    await this.logJobExecution(result);

    console.log(`Job "${job.name}" completed in ${result.duration}ms with status: ${result.status}`);

    return result;
  }

  /**
   * Calculate next run time based on cron-like schedule
   * Simplified implementation - in production, use a proper cron parser
   */
  private calculateNextRun(schedule: string): Date {
    // This is a simplified implementation
    // In production, use a library like 'node-cron' or 'cron-parser'
    
    const now = new Date();
    const nextRun = new Date(now);
    
    // For simplicity, just add intervals based on common patterns
    if (schedule.includes('* * *')) {
      // Daily - add 1 day
      nextRun.setDate(nextRun.getDate() + 1);
    } else if (schedule.includes('8-18')) {
      // Hourly during business hours - add 1 hour
      nextRun.setHours(nextRun.getHours() + 1);
    } else if (schedule.includes('* * 1')) {
      // Weekly - add 7 days
      nextRun.setDate(nextRun.getDate() + 7);
    } else if (schedule.includes('1 * *')) {
      // Monthly - add 1 month
      nextRun.setMonth(nextRun.getMonth() + 1);
    } else {
      // Default - add 1 hour
      nextRun.setHours(nextRun.getHours() + 1);
    }

    return nextRun;
  }

  /**
   * Update job in database
   */
  private async updateJobInDatabase(job: ScheduledJob): Promise<void> {
    try {
      const setting = await prisma.systemSetting.findFirst({
        where: { key: `quality_job_${job.id}` }
      });

      if (setting) {
        await prisma.systemSetting.update({
          where: { id: setting.id },
          data: { value: JSON.stringify(job) }
        });
      }
    } catch (error) {
      console.error(`Error updating job ${job.id} in database:`, error);
    }
  }

  /**
   * Log job execution result
   */
  private async logJobExecution(result: JobExecutionResult): Promise<void> {
    try {
      // In a real implementation, this would save to a job execution log table
      console.log(`Job execution logged: ${result.jobName} - ${result.status}`);
    } catch (error) {
      console.error('Error logging job execution:', error);
    }
  }

  /**
   * Get all jobs
   */
  getJobs(): ScheduledJob[] {
    return Array.from(this.jobs.values());
  }

  /**
   * Get job by ID
   */
  getJob(jobId: string): ScheduledJob | undefined {
    return this.jobs.get(jobId);
  }

  /**
   * Enable/disable a job
   */
  async setJobEnabled(jobId: string, enabled: boolean): Promise<void> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    job.enabled = enabled;
    await this.updateJobInDatabase(job);

    if (enabled && this.isRunning) {
      this.scheduleJob(job);
    } else {
      const timer = this.timers.get(jobId);
      if (timer) {
        clearTimeout(timer);
        this.timers.delete(jobId);
      }
    }
  }

  /**
   * Manually trigger a job
   */
  async triggerJob(jobId: string): Promise<JobExecutionResult> {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error(`Job ${jobId} not found`);
    }

    return await this.executeJob(job);
  }
}

// Export singleton instance
export const qualityScheduler = QualityScheduler.getInstance();
