import { prisma } from "@/auth";
import { calculateSupplierQualityMetrics } from "./supplier-quality-metrics";
import { calculateSupplierPricingMetrics } from "./pricing-analytics";

export interface RelationshipHealth {
  overallScore: number; // 0-100
  communicationScore: number; // Based on response times, contact frequency
  reliabilityScore: number; // Based on delivery performance
  flexibilityScore: number; // Based on order modifications, urgent requests
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
  healthTrend: 'improving' | 'declining' | 'stable';
  keyMetrics: {
    totalOrders: number;
    onTimeDeliveryRate: number;
    qualityScore: number;
    priceCompetitiveness: number;
    contractCompliance: number;
    responsiveness: number;
  };
  riskFactors: Array<{
    factor: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    impact: number; // 0-100
  }>;
  strengthAreas: Array<{
    area: string;
    score: number;
    description: string;
  }>;
}

export interface SupplierRelationshipAnalysis {
  supplierId: string;
  supplierName: string;
  timeRange: string;
  relationshipHealth: RelationshipHealth;
  historicalTrend: Array<{
    month: string;
    overallScore: number;
    communicationScore: number;
    reliabilityScore: number;
    flexibilityScore: number;
  }>;
  comparisonToAverage: {
    overallScore: number;
    industryAverage: number;
    percentile: number;
  };
}

/**
 * Calculate comprehensive relationship health for a supplier
 */
export async function calculateSupplierRelationshipHealth(
  supplierId: string,
  startDate?: Date,
  endDate?: Date
): Promise<SupplierRelationshipAnalysis> {
  const dateFilter: any = {};
  if (startDate) dateFilter.gte = startDate;
  if (endDate) dateFilter.lte = endDate;

  // Get supplier information
  const supplier = await prisma.supplier.findUnique({
    where: { id: supplierId },
    include: {
      purchaseOrders: {
        where: {
          orderDate: Object.keys(dateFilter).length > 0 ? dateFilter : undefined
        },
        include: {
          items: true,
          statusHistory: true
        }
      },
      productSuppliers: {
        where: { isActive: true },
        include: {
          product: {
            select: { id: true, name: true }
          }
        }
      }
    }
  });

  if (!supplier) {
    throw new Error("Supplier not found");
  }

  // Get quality and pricing metrics
  const [qualityMetrics, pricingMetrics] = await Promise.all([
    calculateSupplierQualityMetrics(supplierId, startDate, endDate),
    calculateSupplierPricingMetrics(supplierId, startDate, endDate)
  ]);

  // Calculate communication score
  const communicationScore = calculateCommunicationScore(supplier);

  // Calculate reliability score
  const reliabilityScore = calculateReliabilityScore(supplier, qualityMetrics);

  // Calculate flexibility score
  const flexibilityScore = calculateFlexibilityScore(supplier);

  // Calculate overall score
  const overallScore = Math.round(
    (communicationScore * 0.25) +
    (reliabilityScore * 0.35) +
    (flexibilityScore * 0.20) +
    (qualityMetrics.metrics.qualityScore * 0.20)
  );

  // Determine risk level
  const riskLevel = determineRiskLevel(overallScore, qualityMetrics.riskLevel);

  // Calculate key metrics
  const keyMetrics = calculateKeyMetrics(supplier, qualityMetrics, pricingMetrics);

  // Identify risk factors
  const riskFactors = identifyRiskFactors(supplier, qualityMetrics, pricingMetrics, keyMetrics);

  // Identify strength areas
  const strengthAreas = identifyStrengthAreas(communicationScore, reliabilityScore, flexibilityScore, qualityMetrics);

  // Generate recommendations
  const recommendations = generateRelationshipRecommendations(
    communicationScore,
    reliabilityScore,
    flexibilityScore,
    overallScore,
    riskFactors
  );

  // Calculate health trend
  const healthTrend = calculateHealthTrend(supplier.purchaseOrders);

  // Calculate historical trend
  const historicalTrend = calculateHistoricalTrend(supplier.purchaseOrders);

  // Calculate comparison to average
  const comparisonToAverage = await calculateComparisonToAverage(overallScore);

  const relationshipHealth: RelationshipHealth = {
    overallScore,
    communicationScore,
    reliabilityScore,
    flexibilityScore,
    riskLevel,
    recommendations,
    healthTrend,
    keyMetrics,
    riskFactors,
    strengthAreas
  };

  return {
    supplierId,
    supplierName: supplier.name,
    timeRange: `${startDate?.toISOString().split('T')[0] || 'All time'} - ${endDate?.toISOString().split('T')[0] || 'Present'}`,
    relationshipHealth,
    historicalTrend,
    comparisonToAverage
  };
}

/**
 * Calculate communication score based on supplier interactions
 */
function calculateCommunicationScore(supplier: any): number {
  // Base score
  let score = 80;

  // Check if contact information is complete
  if (!supplier.email) score -= 10;
  if (!supplier.phone) score -= 10;
  if (!supplier.contactPerson) score -= 5;

  // Check recent order activity (proxy for communication)
  const recentOrders = supplier.purchaseOrders.filter((po: any) => {
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    return new Date(po.orderDate) >= thirtyDaysAgo;
  });

  if (recentOrders.length === 0) score -= 15;
  else if (recentOrders.length > 5) score += 10;

  return Math.max(0, Math.min(100, score));
}

/**
 * Calculate reliability score based on delivery and quality performance
 */
function calculateReliabilityScore(supplier: any, qualityMetrics: any): number {
  const receivedOrders = supplier.purchaseOrders.filter((po: any) => po.receivedAt);
  
  if (receivedOrders.length === 0) return 50; // Neutral score for no data

  // Calculate on-time delivery rate
  const onTimeDeliveries = receivedOrders.filter((po: any) => {
    const orderDate = new Date(po.orderDate);
    const receivedDate = new Date(po.receivedAt);
    const deliveryDays = Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
    return deliveryDays <= 7; // Assuming 7 days is on-time
  });

  const onTimeRate = (onTimeDeliveries.length / receivedOrders.length) * 100;
  
  // Combine delivery performance with quality score
  return Math.round((onTimeRate * 0.6) + (qualityMetrics.metrics.qualityScore * 0.4));
}

/**
 * Calculate flexibility score based on order modifications and responsiveness
 */
function calculateFlexibilityScore(supplier: any): number {
  // Base score - this would be enhanced with actual order modification tracking
  let score = 75;

  // Check for order cancellations (negative impact)
  const cancelledOrders = supplier.purchaseOrders.filter((po: any) => po.status === 'CANCELLED');
  const cancellationRate = (cancelledOrders.length / supplier.purchaseOrders.length) * 100;
  
  if (cancellationRate > 10) score -= 20;
  else if (cancellationRate > 5) score -= 10;

  // Check for order variety (positive impact)
  const uniqueProducts = new Set(
    supplier.purchaseOrders.flatMap((po: any) => po.items.map((item: any) => item.productId))
  ).size;

  if (uniqueProducts > 20) score += 15;
  else if (uniqueProducts > 10) score += 10;
  else if (uniqueProducts > 5) score += 5;

  return Math.max(0, Math.min(100, score));
}

/**
 * Calculate key metrics for relationship health
 */
function calculateKeyMetrics(supplier: any, qualityMetrics: any, pricingMetrics: any) {
  const receivedOrders = supplier.purchaseOrders.filter((po: any) => po.receivedAt);
  const onTimeDeliveries = receivedOrders.filter((po: any) => {
    const orderDate = new Date(po.orderDate);
    const receivedDate = new Date(po.receivedAt);
    const deliveryDays = Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
    return deliveryDays <= 7;
  });

  const onTimeDeliveryRate = receivedOrders.length > 0 
    ? (onTimeDeliveries.length / receivedOrders.length) * 100 
    : 0;

  return {
    totalOrders: supplier.purchaseOrders.length,
    onTimeDeliveryRate,
    qualityScore: qualityMetrics.metrics.qualityScore,
    priceCompetitiveness: pricingMetrics.metrics.competitivenessScore,
    contractCompliance: 85, // Placeholder - would need contract tracking
    responsiveness: 80 // Placeholder - would need response time tracking
  };
}

/**
 * Identify risk factors in the supplier relationship
 */
function identifyRiskFactors(supplier: any, qualityMetrics: any, pricingMetrics: any, keyMetrics: any) {
  const riskFactors: any[] = [];

  if (qualityMetrics.metrics.returnRate > 5) {
    riskFactors.push({
      factor: "High Return Rate",
      severity: qualityMetrics.metrics.returnRate > 10 ? 'high' : 'medium',
      description: `Return rate of ${qualityMetrics.metrics.returnRate.toFixed(1)}% exceeds acceptable threshold`,
      impact: Math.min(100, qualityMetrics.metrics.returnRate * 10)
    });
  }

  if (pricingMetrics.metrics.competitivenessScore < 60) {
    riskFactors.push({
      factor: "Poor Price Competitiveness",
      severity: pricingMetrics.metrics.competitivenessScore < 40 ? 'high' : 'medium',
      description: `Pricing is ${(100 - pricingMetrics.metrics.competitivenessScore).toFixed(1)}% above market average`,
      impact: 100 - pricingMetrics.metrics.competitivenessScore
    });
  }

  if (keyMetrics.onTimeDeliveryRate < 80) {
    riskFactors.push({
      factor: "Poor Delivery Performance",
      severity: keyMetrics.onTimeDeliveryRate < 60 ? 'high' : 'medium',
      description: `On-time delivery rate of ${keyMetrics.onTimeDeliveryRate.toFixed(1)}% is below expectations`,
      impact: 100 - keyMetrics.onTimeDeliveryRate
    });
  }

  if (supplier.purchaseOrders.length < 5) {
    riskFactors.push({
      factor: "Limited Order History",
      severity: 'low',
      description: "Insufficient order history to establish reliable performance patterns",
      impact: 20
    });
  }

  return riskFactors;
}

/**
 * Identify strength areas in the supplier relationship
 */
function identifyStrengthAreas(communicationScore: number, reliabilityScore: number, flexibilityScore: number, qualityMetrics: any) {
  const strengthAreas: any[] = [];

  if (communicationScore > 85) {
    strengthAreas.push({
      area: "Communication",
      score: communicationScore,
      description: "Excellent communication channels and responsiveness"
    });
  }

  if (reliabilityScore > 85) {
    strengthAreas.push({
      area: "Reliability",
      score: reliabilityScore,
      description: "Consistent delivery performance and quality standards"
    });
  }

  if (flexibilityScore > 85) {
    strengthAreas.push({
      area: "Flexibility",
      score: flexibilityScore,
      description: "Adaptable to changing requirements and order modifications"
    });
  }

  if (qualityMetrics.metrics.qualityScore > 85) {
    strengthAreas.push({
      area: "Quality",
      score: qualityMetrics.metrics.qualityScore,
      description: "High quality products with minimal returns or defects"
    });
  }

  return strengthAreas;
}

/**
 * Generate relationship improvement recommendations
 */
function generateRelationshipRecommendations(
  communicationScore: number,
  reliabilityScore: number,
  flexibilityScore: number,
  overallScore: number,
  riskFactors: any[]
): string[] {
  const recommendations: string[] = [];

  if (communicationScore < 70) {
    recommendations.push("Establish regular communication schedule with supplier contact person");
  }

  if (reliabilityScore < 70) {
    recommendations.push("Implement delivery performance improvement plan with specific KPIs");
  }

  if (flexibilityScore < 70) {
    recommendations.push("Discuss order modification procedures and emergency response capabilities");
  }

  if (riskFactors.length > 2) {
    recommendations.push("Schedule comprehensive supplier review meeting to address multiple risk factors");
  }

  if (overallScore < 60) {
    recommendations.push("Consider supplier development program or alternative supplier evaluation");
  }

  if (overallScore > 85) {
    recommendations.push("Explore opportunities for strategic partnership and expanded collaboration");
  }

  return recommendations;
}

/**
 * Calculate health trend based on recent performance
 */
function calculateHealthTrend(purchaseOrders: any[]): 'improving' | 'declining' | 'stable' {
  if (purchaseOrders.length < 4) return 'stable';

  // Sort orders by date
  const sortedOrders = purchaseOrders.sort((a, b) => 
    new Date(a.orderDate).getTime() - new Date(b.orderDate).getTime()
  );

  // Compare recent vs older performance
  const recentOrders = sortedOrders.slice(-Math.ceil(sortedOrders.length / 2));
  const olderOrders = sortedOrders.slice(0, Math.floor(sortedOrders.length / 2));

  const recentPerformance = calculateOrdersPerformance(recentOrders);
  const olderPerformance = calculateOrdersPerformance(olderOrders);

  const improvement = recentPerformance - olderPerformance;

  if (improvement > 5) return 'improving';
  if (improvement < -5) return 'declining';
  return 'stable';
}

/**
 * Calculate performance score for a set of orders
 */
function calculateOrdersPerformance(orders: any[]): number {
  if (orders.length === 0) return 0;

  const receivedOrders = orders.filter(po => po.receivedAt);
  if (receivedOrders.length === 0) return 50;

  const onTimeDeliveries = receivedOrders.filter(po => {
    const orderDate = new Date(po.orderDate);
    const receivedDate = new Date(po.receivedAt);
    const deliveryDays = Math.ceil((receivedDate.getTime() - orderDate.getTime()) / (1000 * 60 * 60 * 24));
    return deliveryDays <= 7;
  });

  return (onTimeDeliveries.length / receivedOrders.length) * 100;
}

/**
 * Calculate historical trend data
 */
function calculateHistoricalTrend(purchaseOrders: any[]) {
  const monthlyData = new Map<string, any[]>();

  purchaseOrders.forEach(po => {
    const month = new Date(po.orderDate).toISOString().substring(0, 7);
    if (!monthlyData.has(month)) {
      monthlyData.set(month, []);
    }
    monthlyData.get(month)!.push(po);
  });

  return Array.from(monthlyData.entries())
    .map(([month, orders]) => ({
      month,
      overallScore: calculateOrdersPerformance(orders),
      communicationScore: 80, // Placeholder
      reliabilityScore: calculateOrdersPerformance(orders),
      flexibilityScore: 75 // Placeholder
    }))
    .sort((a, b) => a.month.localeCompare(b.month));
}

/**
 * Calculate comparison to industry average
 */
async function calculateComparisonToAverage(overallScore: number) {
  // This would typically query industry benchmarks
  const industryAverage = 75; // Placeholder
  const percentile = overallScore > industryAverage ? 75 : 25; // Simplified

  return {
    overallScore,
    industryAverage,
    percentile
  };
}

/**
 * Determine risk level based on overall score and other factors
 */
function determineRiskLevel(overallScore: number, qualityRiskLevel: string): 'low' | 'medium' | 'high' {
  if (overallScore < 50 || qualityRiskLevel === 'high') return 'high';
  if (overallScore < 70 || qualityRiskLevel === 'medium') return 'medium';
  return 'low';
}
