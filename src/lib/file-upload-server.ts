// Server-side file upload utilities
// This file should only be imported in server-side code (API routes)

import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { validateFile } from './file-upload';

export interface FileUploadResult {
  success: boolean;
  filePath?: string;
  fileName?: string;
  error?: string;
}

/**
 * Generate unique filename with timestamp and UUID
 */
export function generateFileName(originalName: string, prefix: string = 'payment-proof'): string {
  const extension = path.extname(originalName);
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const uuid = uuidv4().slice(0, 8);
  return `${prefix}-${timestamp}-${uuid}${extension}`;
}

/**
 * Ensure upload directory exists
 */
export async function ensureUploadDirectory(uploadPath: string): Promise<void> {
  if (!existsSync(uploadPath)) {
    await mkdir(uploadPath, { recursive: true });
  }
}

/**
 * Save file to local storage (server-side only)
 */
export async function saveFileToLocal(
  file: File, 
  uploadDir: string = 'uploads/payment-proofs',
  prefix: string = 'payment-proof'
): Promise<FileUploadResult> {
  try {
    // Validate file
    const validation = validateFile(file);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.error
      };
    }

    // Generate unique filename
    const fileName = generateFileName(file.name, prefix);
    
    // Create full upload path
    const uploadPath = path.join(process.cwd(), 'public', uploadDir);
    await ensureUploadDirectory(uploadPath);
    
    // Full file path
    const filePath = path.join(uploadPath, fileName);
    
    // Convert File to Buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Write file
    await writeFile(filePath, buffer);
    
    // Return relative path for URL
    const relativePath = `/${uploadDir}/${fileName}`;
    
    return {
      success: true,
      filePath: relativePath,
      fileName: fileName
    };
    
  } catch (error) {
    console.error('Error saving file:', error);
    return {
      success: false,
      error: 'Failed to save file'
    };
  }
}

/**
 * Get file extension from MIME type
 */
export function getExtensionFromMimeType(mimeType: string): string {
  const mimeToExt: Record<string, string> = {
    'image/jpeg': '.jpg',
    'image/jpg': '.jpg',
    'image/png': '.png',
    'application/pdf': '.pdf'
  };
  
  return mimeToExt[mimeType] || '.bin';
}
