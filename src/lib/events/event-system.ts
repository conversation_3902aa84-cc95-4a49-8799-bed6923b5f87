/**
 * Core Event System for Modular Notifications
 * 
 * This system provides a centralized event-driven architecture for notifications.
 * Features:
 * - Event registration and emission
 * - Async event processing
 * - Event persistence
 * - Type-safe event handling
 */

import { prisma } from '@/lib/prisma';

// Global flag to ensure notification system is initialized
let notificationSystemInitialized = false;

// Base event interface
export interface BaseEvent {
  eventType: string;
  eventId: string;
  sourceId?: string;
  sourceType?: string;
  payload: Record<string, any>;
  timestamp?: Date;
  userId?: string; // Optional user context
}

// Event handler interface
export interface EventHandler {
  eventType: string;
  handler: (event: BaseEvent) => Promise<void>;
  priority?: number; // Lower numbers = higher priority
}

// Event system class
class EventSystem {
  private handlers: Map<string, EventHandler[]> = new Map();
  private isProcessing = false;

  /**
   * Register an event handler
   */
  registerHandler(handler: EventHandler): void {
    const eventType = handler.eventType;
    
    if (!this.handlers.has(eventType)) {
      this.handlers.set(eventType, []);
    }
    
    const handlers = this.handlers.get(eventType)!;
    handlers.push(handler);
    
    // Sort by priority (lower number = higher priority)
    handlers.sort((a, b) => (a.priority || 100) - (b.priority || 100));
    
    console.log(`Event handler registered for: ${eventType}`);
  }

  /**
   * Emit an event
   */
  async emit(event: BaseEvent): Promise<void> {
    try {
      // Ensure notification system is initialized before processing any events
      this.ensureNotificationSystemInitialized();

      // Generate unique event ID if not provided
      if (!event.eventId) {
        event.eventId = `${event.eventType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      }

      // Set timestamp if not provided
      if (!event.timestamp) {
        event.timestamp = new Date();
      }

      console.log(`Emitting event: ${event.eventType} (${event.eventId})`);

      // Persist event to database
      await this.persistEvent(event);

      // Process event immediately
      await this.processEvent(event);

    } catch (error) {
      console.error(`Error emitting event ${event.eventType}:`, error);
      throw error;
    }
  }

  /**
   * Ensure notification system is initialized
   */
  private ensureNotificationSystemInitialized(): void {
    if (!notificationSystemInitialized) {
      console.log('🔧 Initializing notification system from event system...');

      try {
        // Register notification handlers directly here to avoid circular imports
        this.registerNotificationHandlers();

        notificationSystemInitialized = true;
        console.log('✅ Notification system initialized from event system');
      } catch (error) {
        console.error('❌ Failed to initialize notification system:', error);
        // Don't throw here - let the event processing continue even if notifications fail
      }
    }
  }

  /**
   * Register notification handlers for all event types
   */
  private registerNotificationHandlers(): void {
    // Import the notification engine dynamically to avoid circular imports
    const eventTypes = Object.values(EVENT_TYPES);

    eventTypes.forEach(eventType => {
      this.registerHandler({
        eventType,
        handler: async (event) => {
          // Dynamically import and use the notification engine
          try {
            const { notificationEngine } = await import('@/lib/notifications/notification-engine');
            await notificationEngine.processEvent(event);
          } catch (error) {
            console.error(`Error processing notification for event ${event.eventType}:`, error);
          }
        },
        priority: 10, // Lower priority so other handlers can run first
      });
    });

    console.log(`📋 Registered notification handlers for ${eventTypes.length} event types`);
  }

  /**
   * Persist event to database
   */
  private async persistEvent(event: BaseEvent): Promise<void> {
    try {
      await prisma.notificationEvent.create({
        data: {
          eventType: event.eventType,
          eventId: event.eventId,
          sourceId: event.sourceId,
          sourceType: event.sourceType,
          payload: event.payload,
          processed: false,
        },
      });
    } catch (error) {
      console.error(`Error persisting event ${event.eventId}:`, error);
      // Don't throw here - event processing should continue even if persistence fails
    }
  }

  /**
   * Process a single event
   */
  private async processEvent(event: BaseEvent): Promise<void> {
    const handlers = this.handlers.get(event.eventType) || [];
    
    if (handlers.length === 0) {
      console.warn(`No handlers registered for event type: ${event.eventType}`);
      return;
    }

    console.log(`Processing event ${event.eventId} with ${handlers.length} handlers`);

    // Execute all handlers for this event type
    const promises = handlers.map(async (handler) => {
      try {
        await handler.handler(event);
        console.log(`Handler executed successfully for event ${event.eventId}`);
      } catch (error) {
        console.error(`Handler failed for event ${event.eventId}:`, error);
        // Continue processing other handlers even if one fails
      }
    });

    await Promise.allSettled(promises);

    // Mark event as processed
    try {
      await prisma.notificationEvent.update({
        where: { eventId: event.eventId },
        data: { 
          processed: true,
          processedAt: new Date(),
        },
      });
    } catch (error) {
      console.error(`Error marking event ${event.eventId} as processed:`, error);
    }
  }

  /**
   * Process unprocessed events (for recovery/batch processing)
   */
  async processUnprocessedEvents(): Promise<void> {
    if (this.isProcessing) {
      console.log('Event processing already in progress');
      return;
    }

    this.isProcessing = true;

    try {
      const unprocessedEvents = await prisma.notificationEvent.findMany({
        where: { processed: false },
        orderBy: { createdAt: 'asc' },
        take: 100, // Process in batches
      });

      console.log(`Processing ${unprocessedEvents.length} unprocessed events`);

      for (const dbEvent of unprocessedEvents) {
        const event: BaseEvent = {
          eventType: dbEvent.eventType,
          eventId: dbEvent.eventId,
          sourceId: dbEvent.sourceId || undefined,
          sourceType: dbEvent.sourceType || undefined,
          payload: dbEvent.payload as Record<string, any>,
          timestamp: dbEvent.createdAt,
        };

        await this.processEvent(event);
      }
    } catch (error) {
      console.error('Error processing unprocessed events:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get registered event types
   */
  getRegisteredEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get handlers for an event type
   */
  getHandlers(eventType: string): EventHandler[] {
    return this.handlers.get(eventType) || [];
  }

  /**
   * Clear all handlers (mainly for testing)
   */
  clearHandlers(): void {
    this.handlers.clear();
  }
}

// Singleton instance
export const eventSystem = new EventSystem();

// Convenience functions
export const emitEvent = (event: BaseEvent) => eventSystem.emit(event);
export const registerEventHandler = (handler: EventHandler) => eventSystem.registerHandler(handler);

// Event type constants
export const EVENT_TYPES = {
  // Purchase Order Events
  PO_STATUS_CHANGED: 'po.status.changed',
  PO_CREATED: 'po.created',
  PO_APPROVED: 'po.approved',
  PO_REJECTED: 'po.rejected',
  PO_RECEIVED: 'po.received',
  PO_OVERDUE: 'po.overdue',
  
  // Inventory Events
  INVENTORY_LOW_STOCK: 'inventory.low_stock',
  INVENTORY_OUT_OF_STOCK: 'inventory.out_of_stock',
  BATCH_EXPIRING: 'inventory.batch.expiring',
  BATCH_EXPIRED: 'inventory.batch.expired',
  STOCK_ADJUSTMENT_PENDING: 'inventory.adjustment.pending',
  STOCK_ADJUSTMENT_APPROVED: 'inventory.adjustment.approved',
  STOCK_ADJUSTMENT_REJECTED: 'inventory.adjustment.rejected',
  
  // Cash Management Events
  CASH_AUDIT_ALERT: 'cash.audit.alert',
  CASH_RECONCILIATION_REQUIRED: 'cash.reconciliation.required',
  CASH_DISCREPANCY_DETECTED: 'cash.discrepancy.detected',

  // Financial Events
  CASH_RECONCILIATION_DISCREPANCY: 'cash_audit.discrepancy_detected',
  INVOICE_APPROVED: 'invoice.approved',
  INVOICE_PAYMENT_MADE: 'invoice.payment_made',

  // Quality Control Events
  QUALITY_ISSUE_REPORTED: 'quality.issue.reported',
  QUALITY_ISSUE_ESCALATED: 'quality.issue.escalated',
  QUALITY_THRESHOLD_BREACHED: 'quality.threshold.breached',
  QUALITY_IMPROVEMENT_DUE: 'quality.improvement.due',
  SUPPLIER_QUALITY_ALERT: 'quality.supplier.alert',

  // Quality Improvement Events
  QUALITY_IMPROVEMENT_CREATED: 'quality.improvement.created',
  QUALITY_IMPROVEMENT_STATUS_CHANGED: 'quality.improvement.status.changed',
  QUALITY_IMPROVEMENT_COMPLETED: 'quality.improvement.completed',
  QUALITY_ACTION_ASSIGNED: 'quality.action.assigned',
  QUALITY_ACTION_OVERDUE: 'quality.action.overdue',

  // Revenue Events
  REVENUE_TARGET_ACHIEVED: 'revenue.target.achieved',
  REVENUE_TARGET_MISSED: 'revenue.target.missed',
  
  // System Events
  SYSTEM_MAINTENANCE: 'system.maintenance',
  USER_ACTION_REQUIRED: 'user.action.required',

  // User Events
  USER_LOGIN: 'user.login',
  USER_LOGOUT: 'user.logout',
  USER_CREATED: 'user.created',

  // PO Suggestion Events
  PO_SUGGESTION_CREATED: 'po.suggestion.created',
  PO_SUGGESTIONS_BATCH: 'po.suggestions.batch',
  PO_SUGGESTIONS_DAILY_SUMMARY: 'po.suggestions.daily_summary',
  PO_CREATED_FROM_SUGGESTION: 'po.created.from_suggestion',
  PO_CREATED_BULK_FROM_SUGGESTIONS: 'po.created.bulk_from_suggestions',
} as const;

export type EventType = typeof EVENT_TYPES[keyof typeof EVENT_TYPES];
