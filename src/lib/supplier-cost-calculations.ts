import { prisma } from "@/auth";
import { Decimal } from "@prisma/client/runtime/library";

export interface SupplierCostBreakdown {
  supplierId: string;
  supplierName: string;
  totalQuantity: number;
  totalValue: number;
  averageCost: number;
  batchCount: number;
  oldestBatch?: Date;
  newestBatch?: Date;
}

export interface WeightedAverageCost {
  productId: string;
  weightedAverageCost: number;
  totalQuantity: number;
  totalValue: number;
  supplierBreakdown: SupplierCostBreakdown[];
}

export interface InventoryValuationWithSuppliers {
  productId: string;
  productName: string;
  productSku: string;
  category: string;
  unit: string;
  storeQuantity: number;
  warehouseQuantity: number;
  totalQuantity: number;
  weightedAverageCost: number;
  storeValue: number;
  warehouseValue: number;
  totalValue: number;
  supplierBreakdown: SupplierCostBreakdown[];
}

/**
 * Calculate weighted average cost for a product across all suppliers
 */
export async function calculateWeightedAverageCost(productId: string): Promise<WeightedAverageCost> {
  // Get all active batches for the product
  const batches = await prisma.stockBatch.findMany({
    where: {
      productId,
      status: 'ACTIVE',
      remainingQuantity: { gt: 0 }
    },
    include: {
      productSupplier: {
        include: {
          supplier: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }
    }
  });

  if (batches.length === 0) {
    return {
      productId,
      weightedAverageCost: 0,
      totalQuantity: 0,
      totalValue: 0,
      supplierBreakdown: []
    };
  }

  // Group batches by supplier
  const supplierGroups = new Map<string, {
    supplier: { id: string; name: string };
    batches: typeof batches;
  }>();

  batches.forEach(batch => {
    const supplierId = batch.productSupplier.supplier.id;
    if (!supplierGroups.has(supplierId)) {
      supplierGroups.set(supplierId, {
        supplier: batch.productSupplier.supplier,
        batches: []
      });
    }
    supplierGroups.get(supplierId)!.batches.push(batch);
  });

  // Calculate supplier breakdown
  const supplierBreakdown: SupplierCostBreakdown[] = [];
  let totalQuantity = 0;
  let totalValue = 0;

  for (const [supplierId, group] of supplierGroups) {
    const supplierQuantity = group.batches.reduce(
      (sum, batch) => sum + Number(batch.remainingQuantity), 
      0
    );
    const supplierValue = group.batches.reduce(
      (sum, batch) => sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 
      0
    );
    const averageCost = supplierQuantity > 0 ? supplierValue / supplierQuantity : 0;

    const batchDates = group.batches.map(b => b.receivedDate);
    const oldestBatch = new Date(Math.min(...batchDates.map(d => d.getTime())));
    const newestBatch = new Date(Math.max(...batchDates.map(d => d.getTime())));

    supplierBreakdown.push({
      supplierId,
      supplierName: group.supplier.name,
      totalQuantity: supplierQuantity,
      totalValue: supplierValue,
      averageCost,
      batchCount: group.batches.length,
      oldestBatch,
      newestBatch
    });

    totalQuantity += supplierQuantity;
    totalValue += supplierValue;
  }

  const weightedAverageCost = totalQuantity > 0 ? totalValue / totalQuantity : 0;

  return {
    productId,
    weightedAverageCost,
    totalQuantity,
    totalValue,
    supplierBreakdown
  };
}

/**
 * Calculate inventory valuation with supplier breakdown for all products
 */
export async function calculateInventoryValuationWithSuppliers(
  categoryId?: string
): Promise<InventoryValuationWithSuppliers[]> {
  // Build query filter
  const where: any = { active: true };
  if (categoryId) {
    where.categoryId = categoryId;
  }

  // Get all products with stock information
  const products = await prisma.product.findMany({
    where,
    include: {
      category: true,
      unit: true,
      storeStock: true,
      warehouseStock: true
    },
    orderBy: {
      name: 'asc'
    }
  });

  const results: InventoryValuationWithSuppliers[] = [];

  for (const product of products) {
    const storeQuantity = product.storeStock ? Number(product.storeStock.quantity) : 0;
    const warehouseQuantity = product.warehouseStock ? Number(product.warehouseStock.quantity) : 0;
    const totalQuantity = storeQuantity + warehouseQuantity;

    // Calculate weighted average cost
    const costData = await calculateWeightedAverageCost(product.id);

    // Calculate values using weighted average cost
    const storeValue = storeQuantity * costData.weightedAverageCost;
    const warehouseValue = warehouseQuantity * costData.weightedAverageCost;
    const totalValue = storeValue + warehouseValue;

    results.push({
      productId: product.id,
      productName: product.name,
      productSku: product.sku,
      category: product.category?.name || 'Uncategorized',
      unit: product.unit.abbreviation,
      storeQuantity,
      warehouseQuantity,
      totalQuantity,
      weightedAverageCost: costData.weightedAverageCost,
      storeValue,
      warehouseValue,
      totalValue,
      supplierBreakdown: costData.supplierBreakdown
    });
  }

  return results;
}

/**
 * Get supplier cost analysis for a specific supplier
 */
export async function getSupplierCostAnalysis(supplierId: string, startDate?: Date, endDate?: Date) {
  const dateFilter: any = {};
  if (startDate) dateFilter.gte = startDate;
  if (endDate) dateFilter.lte = endDate;

  // Get all batches for this supplier
  const batches = await prisma.stockBatch.findMany({
    where: {
      productSupplier: {
        supplierId
      },
      ...(Object.keys(dateFilter).length > 0 && { receivedDate: dateFilter })
    },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          sku: true,
          category: {
            select: {
              name: true
            }
          },
          unit: {
            select: {
              abbreviation: true
            }
          }
        }
      },
      productSupplier: {
        include: {
          supplier: {
            select: {
              id: true,
              name: true,
              contactPerson: true
            }
          }
        }
      }
    },
    orderBy: {
      receivedDate: 'desc'
    }
  });

  // Calculate summary statistics
  const totalBatches = batches.length;
  const totalQuantityReceived = batches.reduce((sum, batch) => sum + Number(batch.quantity), 0);
  const totalValueReceived = batches.reduce(
    (sum, batch) => sum + (Number(batch.quantity) * Number(batch.purchasePrice)), 
    0
  );
  const averageCostPerUnit = totalQuantityReceived > 0 ? totalValueReceived / totalQuantityReceived : 0;

  // Current inventory value from this supplier
  const activeBatches = batches.filter(batch => batch.status === 'ACTIVE' && Number(batch.remainingQuantity) > 0);
  const currentInventoryQuantity = activeBatches.reduce((sum, batch) => sum + Number(batch.remainingQuantity), 0);
  const currentInventoryValue = activeBatches.reduce(
    (sum, batch) => sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 
    0
  );

  // Group by product for product-level analysis
  const productGroups = new Map();
  batches.forEach(batch => {
    const productId = batch.product.id;
    if (!productGroups.has(productId)) {
      productGroups.set(productId, {
        product: batch.product,
        batches: [],
        totalQuantity: 0,
        totalValue: 0,
        currentQuantity: 0,
        currentValue: 0
      });
    }
    
    const group = productGroups.get(productId);
    group.batches.push(batch);
    group.totalQuantity += Number(batch.quantity);
    group.totalValue += Number(batch.quantity) * Number(batch.purchasePrice);
    
    if (batch.status === 'ACTIVE' && Number(batch.remainingQuantity) > 0) {
      group.currentQuantity += Number(batch.remainingQuantity);
      group.currentValue += Number(batch.remainingQuantity) * Number(batch.purchasePrice);
    }
  });

  const productAnalysis = Array.from(productGroups.values()).map(group => ({
    productId: group.product.id,
    productName: group.product.name,
    productSku: group.product.sku,
    category: group.product.category?.name || 'Uncategorized',
    unit: group.product.unit.abbreviation,
    batchCount: group.batches.length,
    totalQuantityReceived: group.totalQuantity,
    totalValueReceived: group.totalValue,
    averageCost: group.totalQuantity > 0 ? group.totalValue / group.totalQuantity : 0,
    currentQuantity: group.currentQuantity,
    currentValue: group.currentValue,
    turnoverRate: group.totalQuantity > 0 ? ((group.totalQuantity - group.currentQuantity) / group.totalQuantity) * 100 : 0
  }));

  return {
    supplier: batches[0]?.productSupplier.supplier || null,
    summary: {
      totalBatches,
      totalQuantityReceived,
      totalValueReceived,
      averageCostPerUnit,
      currentInventoryQuantity,
      currentInventoryValue,
      inventoryTurnover: totalQuantityReceived > 0 ? ((totalQuantityReceived - currentInventoryQuantity) / totalQuantityReceived) * 100 : 0
    },
    productAnalysis,
    batches: batches.map(batch => ({
      id: batch.id,
      batchNumber: batch.batchNumber,
      receivedDate: batch.receivedDate,
      expiryDate: batch.expiryDate,
      quantity: Number(batch.quantity),
      remainingQuantity: Number(batch.remainingQuantity),
      purchasePrice: Number(batch.purchasePrice),
      status: batch.status,
      product: batch.product
    }))
  };
}
