import { NextRequest } from "next/server";
import { verifyAuthToken } from "@/lib/auth";

export interface EnhancedAuthOptions {
  requireDeviceAuth?: boolean;
  sessionType?: "user_session" | "admin_session";
  sensitiveAction?: boolean;
}

export interface SecurityContext {
  deviceId?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionType?: string;
}

export interface EnhancedAuthResult {
  authenticated: boolean;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  securityContext?: SecurityContext;
}

export async function enhancedAuth(
  request: NextRequest,
  options: EnhancedAuthOptions = {}
): Promise<EnhancedAuthResult> {
  try {
    // Use the existing auth verification
    const auth = await verifyAuthToken(request);
    
    if (!auth.authenticated || !auth.user) {
      return { authenticated: false };
    }

    // Create security context
    const securityContext: SecurityContext = {
      ipAddress: request.ip || request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
      sessionType: options.sessionType || 'user_session',
    };

    // For now, we'll just return the basic auth result with security context
    // In a full implementation, you would add device authorization checks here
    return {
      authenticated: true,
      user: auth.user,
      securityContext,
    };
  } catch (error) {
    console.error('Enhanced auth error:', error);
    return { authenticated: false };
  }
}
