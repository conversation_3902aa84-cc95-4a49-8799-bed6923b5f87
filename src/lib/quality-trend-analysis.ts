import { prisma } from "@/auth";
import { calculateSupplierQualityMetrics } from "@/lib/supplier-quality-metrics";
import { QualityMonitoringService } from "@/lib/quality-monitoring-service";
import { 
  notifyQualityThresholdBreached, 
  notifySupplierQualityAlert,
  notifyQualityIssueEscalated 
} from "@/lib/notifications";

export interface QualityTrendPoint {
  date: string;
  returnRate: number;
  defectRate: number;
  qualityScore: number;
  returnValue: number;
  customerSatisfaction: number;
}

export interface QualityTrendAnalysis {
  supplierId: string;
  supplierName: string;
  analysisType: 'supplier' | 'product' | 'global';
  timeframe: string;
  currentMetrics: {
    returnRate: number;
    defectRate: number;
    qualityScore: number;
    returnValue: number;
    trend: 'improving' | 'declining' | 'stable';
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  };
  trendData: QualityTrendPoint[];
  alerts: QualityAlert[];
  predictions: {
    nextMonthQualityScore: number;
    nextMonthReturnRate: number;
    riskOfDeclining: number; // 0-100 percentage
    recommendedActions: string[];
  };
  thresholdBreaches: Array<{
    metric: string;
    currentValue: number;
    thresholdValue: number;
    breachDate: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
}

export interface QualityAlert {
  id: string;
  type: 'trend_declining' | 'threshold_breach' | 'anomaly_detected' | 'prediction_warning';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  metric: string;
  currentValue: number;
  expectedValue?: number;
  thresholdValue?: number;
  confidence: number; // 0-100 percentage
  recommendedActions: string[];
  createdAt: string;
  expiresAt?: string;
}

export interface GlobalQualityTrends {
  overallTrend: 'improving' | 'declining' | 'stable';
  averageQualityScore: number;
  totalSuppliers: number;
  suppliersAtRisk: number;
  criticalAlerts: number;
  trendAnalysis: {
    returnRateTrend: number; // Percentage change
    defectRateTrend: number;
    qualityScoreTrend: number;
    customerSatisfactionTrend: number;
  };
  topConcerns: Array<{
    type: 'supplier' | 'product' | 'defect_type';
    name: string;
    severity: 'high' | 'critical';
    impact: string;
    recommendation: string;
  }>;
}

/**
 * Quality Trend Analysis Service
 * Provides comprehensive quality trend monitoring and predictive analytics
 */
export class QualityTrendAnalysisService {

  /**
   * Analyze quality trends for a specific supplier
   */
  static async analyzeSupplierTrends(
    supplierId: string,
    timeframeDays: number = 90
  ): Promise<QualityTrendAnalysis> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframeDays);

    // Get supplier information
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { id: true, name: true }
    });

    if (!supplier) {
      throw new Error('Supplier not found');
    }

    // Generate trend data points (weekly intervals)
    const trendData = await this.generateTrendDataPoints(
      supplierId,
      startDate,
      endDate,
      'weekly'
    );

    // Get current metrics
    const currentMetrics = await calculateSupplierQualityMetrics(
      supplierId,
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
      new Date()
    );

    // Analyze trends
    const trendAnalysis = this.analyzeTrendDirection(trendData);
    
    // Generate alerts
    const alerts = await this.generateQualityAlerts(
      supplierId,
      supplier.name,
      currentMetrics.metrics,
      trendData
    );

    // Generate predictions
    const predictions = this.generatePredictions(trendData, currentMetrics.metrics);

    // Check threshold breaches
    const thresholdBreaches = await this.checkThresholdBreaches(
      supplierId,
      currentMetrics.metrics
    );

    return {
      supplierId,
      supplierName: supplier.name,
      analysisType: 'supplier',
      timeframe: `${timeframeDays} days`,
      currentMetrics: {
        returnRate: currentMetrics.metrics.returnRate,
        defectRate: currentMetrics.metrics.defectRate,
        qualityScore: currentMetrics.metrics.qualityScore,
        returnValue: currentMetrics.metrics.returnValue,
        trend: trendAnalysis.overallTrend,
        riskLevel: currentMetrics.riskLevel,
      },
      trendData,
      alerts,
      predictions,
      thresholdBreaches,
    };
  }

  /**
   * Generate global quality trends across all suppliers
   */
  static async analyzeGlobalTrends(timeframeDays: number = 90): Promise<GlobalQualityTrends> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframeDays);

    // Get all active suppliers
    const suppliers = await prisma.supplier.findMany({
      where: { isActive: true },
      select: { id: true, name: true }
    });

    let totalQualityScore = 0;
    let suppliersAtRisk = 0;
    let criticalAlerts = 0;
    const supplierTrends: any[] = [];

    // Analyze each supplier
    for (const supplier of suppliers) {
      try {
        const metrics = await calculateSupplierQualityMetrics(
          supplier.id,
          startDate,
          endDate
        );

        totalQualityScore += metrics.metrics.qualityScore;
        
        if (metrics.riskLevel === 'high' || metrics.riskLevel === 'critical') {
          suppliersAtRisk++;
        }

        supplierTrends.push({
          supplierId: supplier.id,
          supplierName: supplier.name,
          qualityScore: metrics.metrics.qualityScore,
          returnRate: metrics.metrics.returnRate,
          defectRate: metrics.metrics.defectRate,
          riskLevel: metrics.riskLevel,
        });

      } catch (error) {
        console.error(`Error analyzing supplier ${supplier.id}:`, error);
      }
    }

    const averageQualityScore = suppliers.length > 0 ? totalQualityScore / suppliers.length : 0;

    // Calculate trend changes using historical comparison
    const trendAnalysis = await this.calculateGlobalTrendChanges(
      supplierTrends,
      timeframeDays
    );

    // Identify top concerns
    const topConcerns = this.identifyTopConcerns(supplierTrends);

    // Determine overall trend
    const overallTrend = this.determineOverallTrend(trendAnalysis);

    return {
      overallTrend,
      averageQualityScore,
      totalSuppliers: suppliers.length,
      suppliersAtRisk,
      criticalAlerts,
      trendAnalysis,
      topConcerns,
    };
  }

  /**
   * Generate trend data points for analysis
   */
  private static async generateTrendDataPoints(
    supplierId: string,
    startDate: Date,
    endDate: Date,
    interval: 'daily' | 'weekly' | 'monthly'
  ): Promise<QualityTrendPoint[]> {
    const points: QualityTrendPoint[] = [];
    const current = new Date(startDate);
    
    // Calculate interval increment
    const incrementDays = interval === 'daily' ? 1 : interval === 'weekly' ? 7 : 30;

    while (current <= endDate) {
      const periodEnd = new Date(current);
      periodEnd.setDate(periodEnd.getDate() + incrementDays);

      try {
        const metrics = await calculateSupplierQualityMetrics(
          supplierId,
          current,
          periodEnd
        );

        points.push({
          date: current.toISOString().split('T')[0],
          returnRate: metrics.metrics.returnRate,
          defectRate: metrics.metrics.defectRate,
          qualityScore: metrics.metrics.qualityScore,
          returnValue: metrics.metrics.returnValue,
          customerSatisfaction: metrics.metrics.customerSatisfaction,
        });

      } catch (error) {
        console.error(`Error calculating metrics for period ${current.toISOString()}:`, error);
      }

      current.setDate(current.getDate() + incrementDays);
    }

    return points;
  }

  /**
   * Analyze trend direction from data points
   */
  private static analyzeTrendDirection(trendData: QualityTrendPoint[]): {
    overallTrend: 'improving' | 'declining' | 'stable';
    qualityScoreTrend: number;
    returnRateTrend: number;
  } {
    if (trendData.length < 2) {
      return {
        overallTrend: 'stable',
        qualityScoreTrend: 0,
        returnRateTrend: 0,
      };
    }

    // Calculate linear regression for quality score and return rate
    const qualityScoreTrend = this.calculateTrendSlope(
      trendData.map((point, index) => ({ x: index, y: point.qualityScore }))
    );

    const returnRateTrend = this.calculateTrendSlope(
      trendData.map((point, index) => ({ x: index, y: point.returnRate }))
    );

    // Determine overall trend
    let overallTrend: 'improving' | 'declining' | 'stable' = 'stable';
    if (qualityScoreTrend > 1 && returnRateTrend < -0.5) {
      overallTrend = 'improving';
    } else if (qualityScoreTrend < -1 || returnRateTrend > 0.5) {
      overallTrend = 'declining';
    }

    return {
      overallTrend,
      qualityScoreTrend,
      returnRateTrend,
    };
  }

  /**
   * Calculate trend slope using linear regression
   */
  private static calculateTrendSlope(points: Array<{ x: number; y: number }>): number {
    if (points.length < 2) return 0;

    const n = points.length;
    const sumX = points.reduce((sum, p) => sum + p.x, 0);
    const sumY = points.reduce((sum, p) => sum + p.y, 0);
    const sumXY = points.reduce((sum, p) => sum + p.x * p.y, 0);
    const sumXX = points.reduce((sum, p) => sum + p.x * p.x, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    return isNaN(slope) ? 0 : slope;
  }

  /**
   * Generate quality alerts based on trends and thresholds
   */
  private static async generateQualityAlerts(
    supplierId: string,
    supplierName: string,
    currentMetrics: any,
    trendData: QualityTrendPoint[]
  ): Promise<QualityAlert[]> {
    const alerts: QualityAlert[] = [];
    const thresholds = await QualityMonitoringService.getQualityThresholds();

    // Check for declining trends
    const trendAnalysis = this.analyzeTrendDirection(trendData);
    if (trendAnalysis.overallTrend === 'declining') {
      const trendConfidence = this.calculateTrendConfidence(trendData, 'trend');
      alerts.push({
        id: `trend_declining_${supplierId}`,
        type: 'trend_declining',
        severity: 'medium',
        title: 'Quality trend declining',
        description: `${supplierName} shows declining quality trend over recent periods`,
        metric: 'quality_score',
        currentValue: currentMetrics.qualityScore,
        confidence: trendConfidence,
        recommendedActions: [
          'Schedule supplier quality review',
          'Implement corrective action plan',
          'Increase inspection frequency'
        ],
        createdAt: new Date().toISOString(),
      });
    }

    // Check threshold breaches
    if (currentMetrics.returnRate > thresholds.returnRateThreshold) {
      const thresholdConfidence = this.calculateThresholdConfidence(
        currentMetrics.returnRate,
        thresholds.returnRateThreshold,
        trendData
      );
      alerts.push({
        id: `threshold_return_rate_${supplierId}`,
        type: 'threshold_breach',
        severity: currentMetrics.returnRate > thresholds.returnRateThreshold * 2 ? 'high' : 'medium',
        title: 'Return rate threshold exceeded',
        description: `Return rate (${currentMetrics.returnRate.toFixed(1)}%) exceeds threshold (${thresholds.returnRateThreshold}%)`,
        metric: 'return_rate',
        currentValue: currentMetrics.returnRate,
        thresholdValue: thresholds.returnRateThreshold,
        confidence: thresholdConfidence,
        recommendedActions: [
          'Investigate root causes',
          'Review supplier processes',
          'Consider supplier audit'
        ],
        createdAt: new Date().toISOString(),
      });
    }

    return alerts;
  }

  /**
   * Generate predictions based on trend analysis
   */
  private static generatePredictions(
    trendData: QualityTrendPoint[],
    currentMetrics: any
  ): any {
    const trendAnalysis = this.analyzeTrendDirection(trendData);
    
    // Simple linear extrapolation for next month
    const nextMonthQualityScore = Math.max(0, Math.min(100, 
      currentMetrics.qualityScore + trendAnalysis.qualityScoreTrend * 4
    ));
    
    const nextMonthReturnRate = Math.max(0, 
      currentMetrics.returnRate + trendAnalysis.returnRateTrend * 4
    );

    // Calculate risk of declining (based on trend direction and volatility)
    let riskOfDeclining = 0;
    if (trendAnalysis.overallTrend === 'declining') {
      riskOfDeclining = 70;
    } else if (trendAnalysis.overallTrend === 'stable') {
      riskOfDeclining = 30;
    } else {
      riskOfDeclining = 10;
    }

    const recommendedActions = [];
    if (nextMonthQualityScore < 70) {
      recommendedActions.push('Implement immediate quality improvement plan');
    }
    if (nextMonthReturnRate > 5) {
      recommendedActions.push('Review and strengthen quality control processes');
    }
    if (riskOfDeclining > 50) {
      recommendedActions.push('Schedule proactive supplier engagement');
    }

    return {
      nextMonthQualityScore,
      nextMonthReturnRate,
      riskOfDeclining,
      recommendedActions,
    };
  }

  /**
   * Check for threshold breaches
   */
  private static async checkThresholdBreaches(
    supplierId: string,
    currentMetrics: any
  ): Promise<any[]> {
    const thresholds = await QualityMonitoringService.getQualityThresholds();
    const breaches: any[] = [];

    const checks = [
      {
        metric: 'return_rate',
        current: currentMetrics.returnRate,
        threshold: thresholds.returnRateThreshold,
        name: 'Return Rate'
      },
      {
        metric: 'defect_rate',
        current: currentMetrics.defectRate,
        threshold: thresholds.defectRateThreshold,
        name: 'Defect Rate'
      },
      {
        metric: 'quality_score',
        current: currentMetrics.qualityScore,
        threshold: thresholds.qualityScoreThreshold,
        name: 'Quality Score',
        inverted: true // Lower values are worse
      },
      {
        metric: 'return_value',
        current: currentMetrics.returnValue,
        threshold: thresholds.returnValueThreshold,
        name: 'Return Value'
      }
    ];

    for (const check of checks) {
      const isBreached = check.inverted 
        ? check.current < check.threshold
        : check.current > check.threshold;

      if (isBreached) {
        const severity = this.determineBreachSeverity(check.current, check.threshold, check.inverted);
        breaches.push({
          metric: check.metric,
          currentValue: check.current,
          thresholdValue: check.threshold,
          breachDate: new Date().toISOString(),
          severity,
        });
      }
    }

    return breaches;
  }

  /**
   * Determine breach severity
   */
  private static determineBreachSeverity(
    current: number,
    threshold: number,
    inverted: boolean = false
  ): 'low' | 'medium' | 'high' | 'critical' {
    const ratio = inverted ? threshold / current : current / threshold;
    
    if (ratio >= 3) return 'critical';
    if (ratio >= 2) return 'high';
    if (ratio >= 1.5) return 'medium';
    return 'low';
  }

  /**
   * Identify top concerns from supplier trends
   */
  private static identifyTopConcerns(supplierTrends: any[]): any[] {
    const concerns: any[] = [];

    // Find suppliers with critical risk levels
    const criticalSuppliers = supplierTrends
      .filter(s => s.riskLevel === 'critical')
      .sort((a, b) => a.qualityScore - b.qualityScore)
      .slice(0, 3);

    for (const supplier of criticalSuppliers) {
      concerns.push({
        type: 'supplier',
        name: supplier.supplierName,
        severity: 'critical',
        impact: `Quality score: ${supplier.qualityScore}, Return rate: ${supplier.returnRate.toFixed(1)}%`,
        recommendation: 'Immediate quality audit and corrective action required',
      });
    }

    return concerns;
  }

  /**
   * Calculate confidence score for trend analysis based on data quality and sample size
   */
  private static calculateTrendConfidence(
    trendData: QualityTrendPoint[],
    analysisType: 'trend' | 'threshold'
  ): number {
    let confidence = 50; // Base confidence

    // Sample size impact (more data points = higher confidence)
    const dataPoints = trendData.length;
    if (dataPoints >= 12) confidence += 30; // 3+ months of weekly data
    else if (dataPoints >= 8) confidence += 20; // 2+ months
    else if (dataPoints >= 4) confidence += 10; // 1+ month
    else confidence -= 20; // Very limited data

    // Data consistency impact (lower variance = higher confidence)
    if (dataPoints >= 3) {
      const qualityScores = trendData.map(point => point.qualityScore);
      const mean = qualityScores.reduce((sum, score) => sum + score, 0) / qualityScores.length;
      const variance = qualityScores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / qualityScores.length;
      const standardDeviation = Math.sqrt(variance);

      // Lower standard deviation = more consistent data = higher confidence
      if (standardDeviation < 5) confidence += 15;
      else if (standardDeviation < 10) confidence += 10;
      else if (standardDeviation > 20) confidence -= 15;
    }

    // Data recency impact (more recent data = higher confidence)
    if (dataPoints > 0) {
      const latestDate = new Date(trendData[trendData.length - 1].date);
      const daysSinceLatest = Math.floor((Date.now() - latestDate.getTime()) / (1000 * 60 * 60 * 24));

      if (daysSinceLatest <= 7) confidence += 10; // Very recent
      else if (daysSinceLatest <= 30) confidence += 5; // Recent
      else if (daysSinceLatest > 90) confidence -= 10; // Stale data
    }

    return Math.max(10, Math.min(95, Math.round(confidence)));
  }

  /**
   * Calculate confidence score for threshold breach analysis
   */
  private static calculateThresholdConfidence(
    currentValue: number,
    threshold: number,
    trendData: QualityTrendPoint[]
  ): number {
    let confidence = 70; // Base confidence for threshold breaches (higher than trends)

    // Breach magnitude impact (larger breach = higher confidence)
    const breachRatio = currentValue / threshold;
    if (breachRatio >= 2.0) confidence += 20; // Significant breach
    else if (breachRatio >= 1.5) confidence += 10; // Moderate breach
    else if (breachRatio < 1.2) confidence -= 10; // Marginal breach

    // Historical consistency (if breach is consistent over time)
    if (trendData.length >= 3) {
      const recentBreaches = trendData.slice(-3).filter(point =>
        point.returnRate > threshold
      ).length;

      if (recentBreaches >= 2) confidence += 15; // Consistent pattern
      else if (recentBreaches === 0) confidence -= 10; // Isolated incident
    }

    // Data quality from sample size
    const dataPoints = trendData.length;
    if (dataPoints >= 8) confidence += 10;
    else if (dataPoints < 3) confidence -= 15;

    return Math.max(15, Math.min(98, Math.round(confidence)));
  }

  /**
   * Calculate global trend changes using historical comparison
   */
  private static async calculateGlobalTrendChanges(
    supplierTrends: any[],
    timeframeDays: number
  ): Promise<{
    returnRateTrend: number;
    defectRateTrend: number;
    qualityScoreTrend: number;
    customerSatisfactionTrend: number;
  }> {
    // Calculate comparison period (previous period of same length)
    const currentEndDate = new Date();
    const currentStartDate = new Date();
    currentStartDate.setDate(currentStartDate.getDate() - timeframeDays);

    const previousEndDate = new Date(currentStartDate);
    const previousStartDate = new Date(previousEndDate);
    previousStartDate.setDate(previousStartDate.getDate() - timeframeDays);

    // Get metrics for previous period
    let previousTotalQualityScore = 0;
    let previousTotalReturnRate = 0;
    let previousTotalDefectRate = 0;
    let previousTotalSatisfaction = 0;
    let previousSuppliersCount = 0;

    for (const supplier of supplierTrends) {
      try {
        const previousMetrics = await calculateSupplierQualityMetrics(
          supplier.supplierId,
          previousStartDate,
          previousEndDate
        );

        previousTotalQualityScore += previousMetrics.metrics.qualityScore;
        previousTotalReturnRate += previousMetrics.metrics.returnRate;
        previousTotalDefectRate += previousMetrics.metrics.defectRate;
        previousTotalSatisfaction += previousMetrics.metrics.customerSatisfaction;
        previousSuppliersCount++;
      } catch (error) {
        console.warn(`Could not get previous metrics for supplier ${supplier.supplierId}:`, error);
      }
    }

    // Calculate averages for previous period
    const previousAvgQualityScore = previousSuppliersCount > 0 ? previousTotalQualityScore / previousSuppliersCount : 0;
    const previousAvgReturnRate = previousSuppliersCount > 0 ? previousTotalReturnRate / previousSuppliersCount : 0;
    const previousAvgDefectRate = previousSuppliersCount > 0 ? previousTotalDefectRate / previousSuppliersCount : 0;
    const previousAvgSatisfaction = previousSuppliersCount > 0 ? previousTotalSatisfaction / previousSuppliersCount : 0;

    // Calculate current averages
    const currentAvgQualityScore = supplierTrends.length > 0
      ? supplierTrends.reduce((sum, s) => sum + s.qualityScore, 0) / supplierTrends.length
      : 0;
    const currentAvgReturnRate = supplierTrends.length > 0
      ? supplierTrends.reduce((sum, s) => sum + s.returnRate, 0) / supplierTrends.length
      : 0;
    const currentAvgDefectRate = supplierTrends.length > 0
      ? supplierTrends.reduce((sum, s) => sum + s.defectRate, 0) / supplierTrends.length
      : 0;

    // Calculate percentage changes
    const qualityScoreTrend = previousAvgQualityScore > 0
      ? ((currentAvgQualityScore - previousAvgQualityScore) / previousAvgQualityScore) * 100
      : 0;
    const returnRateTrend = previousAvgReturnRate > 0
      ? ((currentAvgReturnRate - previousAvgReturnRate) / previousAvgReturnRate) * 100
      : 0;
    const defectRateTrend = previousAvgDefectRate > 0
      ? ((currentAvgDefectRate - previousAvgDefectRate) / previousAvgDefectRate) * 100
      : 0;
    const customerSatisfactionTrend = previousAvgSatisfaction > 0
      ? ((currentAvgSatisfaction - previousAvgSatisfaction) / previousAvgSatisfaction) * 100
      : 0;

    return {
      returnRateTrend: Math.round(returnRateTrend * 100) / 100,
      defectRateTrend: Math.round(defectRateTrend * 100) / 100,
      qualityScoreTrend: Math.round(qualityScoreTrend * 100) / 100,
      customerSatisfactionTrend: Math.round(customerSatisfactionTrend * 100) / 100,
    };
  }

  /**
   * Determine overall trend from analysis
   */
  private static determineOverallTrend(trendAnalysis: any): 'improving' | 'declining' | 'stable' {
    const { qualityScoreTrend, returnRateTrend } = trendAnalysis;

    if (qualityScoreTrend > 1 && returnRateTrend < -0.5) {
      return 'improving';
    } else if (qualityScoreTrend < -1 || returnRateTrend > 0.5) {
      return 'declining';
    }

    return 'stable';
  }
}
