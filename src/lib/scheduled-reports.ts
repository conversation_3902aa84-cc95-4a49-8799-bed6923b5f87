import { prisma } from "@/auth";
import { calculateSupplierQualityMetrics, calculateAllSuppliersQualityMetrics } from "./supplier-quality-metrics";
import { calculateSupplierPricingMetrics } from "./pricing-analytics";

export interface ScheduledReport {
  id: string;
  type: 'monthly' | 'quarterly' | 'weekly';
  reportCategory: 'performance' | 'cost' | 'quality' | 'relationship';
  recipients: string[]; // Email addresses
  isActive: boolean;
  lastGenerated: Date | null;
  nextScheduled: Date;
  parameters: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReportGenerationResult {
  reportId: string;
  success: boolean;
  generatedAt: Date;
  filePath?: string;
  error?: string;
  recipients: string[];
}

/**
 * Generate a supplier performance report
 */
export async function generateSupplierPerformanceReport(
  reportType: 'monthly' | 'quarterly' | 'weekly',
  parameters: Record<string, any> = {}
): Promise<any> {
  const endDate = new Date();
  let startDate = new Date();

  // Calculate date range based on report type
  switch (reportType) {
    case 'weekly':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'monthly':
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'quarterly':
      startDate.setMonth(startDate.getMonth() - 3);
      break;
  }

  // Get all suppliers or specific supplier if specified
  const supplierId = parameters.supplierId;
  
  if (supplierId) {
    // Single supplier report
    const [qualityMetrics, pricingMetrics] = await Promise.all([
      calculateSupplierQualityMetrics(supplierId, startDate, endDate),
      calculateSupplierPricingMetrics(supplierId, startDate, endDate)
    ]);

    return {
      reportType: 'supplier_performance',
      period: reportType,
      generatedAt: new Date(),
      dateRange: { startDate, endDate },
      supplier: {
        id: supplierId,
        name: qualityMetrics.supplierName
      },
      qualityMetrics: qualityMetrics.metrics,
      pricingMetrics: pricingMetrics.metrics,
      recommendations: [
        ...qualityMetrics.recommendations,
        ...pricingMetrics.recommendations
      ],
      riskLevel: qualityMetrics.riskLevel,
      competitivenessLevel: pricingMetrics.competitivenessLevel
    };
  } else {
    // All suppliers report
    const qualityMetrics = await calculateAllSuppliersQualityMetrics(startDate, endDate);
    
    return {
      reportType: 'all_suppliers_performance',
      period: reportType,
      generatedAt: new Date(),
      dateRange: { startDate, endDate },
      summary: {
        totalSuppliers: qualityMetrics.length,
        averageQualityScore: qualityMetrics.reduce((sum, s) => sum + s.metrics.qualityScore, 0) / qualityMetrics.length,
        highRiskSuppliers: qualityMetrics.filter(s => s.riskLevel === 'high').length,
        topPerformers: qualityMetrics.slice(0, 5),
        underPerformers: qualityMetrics.slice(-5)
      },
      suppliers: qualityMetrics
    };
  }
}

/**
 * Generate a supplier cost analysis report
 */
export async function generateSupplierCostReport(
  reportType: 'monthly' | 'quarterly' | 'weekly',
  parameters: Record<string, any> = {}
): Promise<any> {
  const endDate = new Date();
  let startDate = new Date();

  switch (reportType) {
    case 'weekly':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case 'monthly':
      startDate.setMonth(startDate.getMonth() - 1);
      break;
    case 'quarterly':
      startDate.setMonth(startDate.getMonth() - 3);
      break;
  }

  // Get cost analysis data
  const suppliers = await prisma.supplier.findMany({
    where: { isActive: true },
    include: {
      productSuppliers: {
        where: { isActive: true },
        include: {
          stockBatches: {
            where: {
              receivedDate: {
                gte: startDate,
                lte: endDate
              }
            }
          },
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              category: {
                select: { name: true }
              }
            }
          }
        }
      }
    }
  });

  const supplierCostData = suppliers.map(supplier => {
    const allBatches = supplier.productSuppliers.flatMap(ps => ps.stockBatches);
    const totalValue = allBatches.reduce((sum, batch) => 
      sum + (Number(batch.quantity) * Number(batch.purchasePrice)), 0
    );
    const totalQuantity = allBatches.reduce((sum, batch) => sum + Number(batch.quantity), 0);
    const averageCost = totalQuantity > 0 ? totalValue / totalQuantity : 0;

    return {
      supplierId: supplier.id,
      supplierName: supplier.name,
      totalValue,
      totalQuantity,
      averageCost,
      batchCount: allBatches.length,
      productCount: supplier.productSuppliers.length
    };
  }).filter(s => s.totalValue > 0);

  return {
    reportType: 'supplier_cost_analysis',
    period: reportType,
    generatedAt: new Date(),
    dateRange: { startDate, endDate },
    summary: {
      totalSuppliers: supplierCostData.length,
      totalSpending: supplierCostData.reduce((sum, s) => sum + s.totalValue, 0),
      averageOrderValue: supplierCostData.reduce((sum, s) => sum + s.averageCost, 0) / supplierCostData.length,
      topSpenders: supplierCostData.sort((a, b) => b.totalValue - a.totalValue).slice(0, 5)
    },
    suppliers: supplierCostData.sort((a, b) => b.totalValue - a.totalValue)
  };
}

/**
 * Create a new scheduled report
 */
export async function createScheduledReport(
  type: 'monthly' | 'quarterly' | 'weekly',
  reportCategory: 'performance' | 'cost' | 'quality' | 'relationship',
  recipients: string[],
  parameters: Record<string, any> = {}
): Promise<ScheduledReport> {
  const nextScheduled = calculateNextScheduledDate(type);

  const report = await prisma.scheduledReport.create({
    data: {
      type,
      reportCategory,
      recipients,
      isActive: true,
      nextScheduled,
      parameters,
      lastGenerated: null
    }
  });

  return {
    id: report.id,
    type: report.type as 'monthly' | 'quarterly' | 'weekly',
    reportCategory: report.reportCategory as 'performance' | 'cost' | 'quality' | 'relationship',
    recipients: report.recipients as string[],
    isActive: report.isActive,
    lastGenerated: report.lastGenerated,
    nextScheduled: report.nextScheduled,
    parameters: report.parameters as Record<string, any>,
    createdAt: report.createdAt,
    updatedAt: report.updatedAt
  };
}

/**
 * Calculate the next scheduled date based on report type
 */
function calculateNextScheduledDate(type: 'monthly' | 'quarterly' | 'weekly'): Date {
  const now = new Date();
  const nextDate = new Date(now);

  switch (type) {
    case 'weekly':
      nextDate.setDate(now.getDate() + 7);
      break;
    case 'monthly':
      nextDate.setMonth(now.getMonth() + 1);
      nextDate.setDate(1); // First day of next month
      break;
    case 'quarterly':
      nextDate.setMonth(now.getMonth() + 3);
      nextDate.setDate(1); // First day of quarter
      break;
  }

  // Set to 9 AM for business hours
  nextDate.setHours(9, 0, 0, 0);
  return nextDate;
}

/**
 * Get all scheduled reports
 */
export async function getScheduledReports(): Promise<ScheduledReport[]> {
  const reports = await prisma.scheduledReport.findMany({
    orderBy: { createdAt: 'desc' }
  });

  return reports.map(report => ({
    id: report.id,
    type: report.type as 'monthly' | 'quarterly' | 'weekly',
    reportCategory: report.reportCategory as 'performance' | 'cost' | 'quality' | 'relationship',
    recipients: report.recipients as string[],
    isActive: report.isActive,
    lastGenerated: report.lastGenerated,
    nextScheduled: report.nextScheduled,
    parameters: report.parameters as Record<string, any>,
    createdAt: report.createdAt,
    updatedAt: report.updatedAt
  }));
}

/**
 * Process due scheduled reports
 */
export async function processDueReports(): Promise<ReportGenerationResult[]> {
  const now = new Date();
  
  const dueReports = await prisma.scheduledReport.findMany({
    where: {
      isActive: true,
      nextScheduled: {
        lte: now
      }
    }
  });

  const results: ReportGenerationResult[] = [];

  for (const report of dueReports) {
    try {
      let reportData;
      
      switch (report.reportCategory) {
        case 'performance':
        case 'quality':
          reportData = await generateSupplierPerformanceReport(
            report.type as 'monthly' | 'quarterly' | 'weekly',
            report.parameters as Record<string, any>
          );
          break;
        case 'cost':
          reportData = await generateSupplierCostReport(
            report.type as 'monthly' | 'quarterly' | 'weekly',
            report.parameters as Record<string, any>
          );
          break;
        default:
          throw new Error(`Unsupported report category: ${report.reportCategory}`);
      }

      // Update the scheduled report
      const nextScheduled = calculateNextScheduledDate(report.type as 'monthly' | 'quarterly' | 'weekly');
      await prisma.scheduledReport.update({
        where: { id: report.id },
        data: {
          lastGenerated: now,
          nextScheduled
        }
      });

      // TODO: Send email to recipients with report data
      // This would integrate with your email service

      results.push({
        reportId: report.id,
        success: true,
        generatedAt: now,
        recipients: report.recipients as string[]
      });

    } catch (error) {
      console.error(`Failed to generate report ${report.id}:`, error);
      
      results.push({
        reportId: report.id,
        success: false,
        generatedAt: now,
        error: (error as Error).message,
        recipients: report.recipients as string[]
      });
    }
  }

  return results;
}
