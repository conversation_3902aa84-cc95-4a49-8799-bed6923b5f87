// Analytics data types and interfaces

export interface DateRange {
  from: Date | undefined;
  to: Date | undefined;
}

export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

export interface SalesTrendData extends ChartDataPoint {
  revenue: number;
  transactions: number;
  averageOrderValue: number;
}

export interface ProductPerformanceData {
  id: string;
  name: string;
  revenue: number;
  quantity: number;
  category: string;
  profit: number;
}

export interface PaymentMethodData {
  method: string;
  amount: number;
  count: number;
  percentage: number;
}

export interface HourlyPatternData {
  hour: number;
  revenue: number;
  transactions: number;
  averageOrderValue: number;
}

export interface CashierPerformanceData {
  id: string;
  name: string;
  revenue: number;
  transactions: number;
  averageOrderValue: number;
  efficiency: number;
}

export interface CategoryPerformanceData {
  id: string;
  name: string;
  revenue: number;
  quantity: number;
  percentage: number;
}

export interface DrawerSessionData {
  id: string;
  cashierName: string;
  terminalName?: string;
  drawerName?: string;
  openedAt: string;
  closedAt?: string;
  openingBalance: number;
  closingBalance?: number;
  expectedClosingBalance?: number;
  discrepancy?: number;
  transactionCount: number;
  status: 'OPEN' | 'CLOSED' | 'RECONCILED';
  businessDate: string;
}

export interface TransactionVolumeData {
  range: string;
  count: number;
  percentage: number;
}

export interface RevenueSummary {
  totalRevenue: number;
  totalTransactions: number;
  averageOrderValue: number;
  activeCustomers: number;
  revenueGrowth: number;
  transactionGrowth: number;
  aovGrowth: number;
  customerGrowth: number;
}

export interface AnalyticsFilters {
  dateRange: DateRange;
  cashierIds?: string[];
  categoryIds?: string[];
  paymentMethods?: string[];
  terminalIds?: string[];
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'pie' | 'donut' | 'area' | 'scatter';
  title: string;
  subtitle?: string;
  dataKey: string;
  xAxisKey?: string;
  yAxisKey?: string;
  colors?: string[];
  height?: number;
  showLegend?: boolean;
  showTooltip?: boolean;
  showGrid?: boolean;
}

export interface ExportOptions {
  format: 'png' | 'pdf' | 'csv';
  filename?: string;
  includeData?: boolean;
}

// API Response types
export interface AnalyticsApiResponse<T> {
  data: T;
  success: boolean;
  error?: string;
  timestamp: string;
}

export interface SalesTrendsResponse extends AnalyticsApiResponse<SalesTrendData[]> {}
export interface RevenueSummaryResponse extends AnalyticsApiResponse<RevenueSummary> {}
export interface ProductPerformanceResponse extends AnalyticsApiResponse<ProductPerformanceData[]> {}
export interface PaymentMethodsResponse extends AnalyticsApiResponse<PaymentMethodData[]> {}
export interface HourlyPatternsResponse extends AnalyticsApiResponse<HourlyPatternData[]> {}
export interface CashierPerformanceResponse extends AnalyticsApiResponse<CashierPerformanceData[]> {}
export interface CategoryPerformanceResponse extends AnalyticsApiResponse<CategoryPerformanceData[]> {}
export interface DrawerSessionsResponse extends AnalyticsApiResponse<DrawerSessionData[]> {}

// Operational Metrics Types
export interface CashOperationMetrics {
  drawerSessions: {
    totalSessions: number;
    averageSessionDuration: number; // in minutes
    sessionsPerDay: number;
    utilizationRate: number; // percentage
  };
  cashDiscrepancies: {
    totalDiscrepancies: number;
    averageDiscrepancy: number;
    discrepancyTrend: number; // percentage change
    largeDiscrepanciesCount: number;
  };
  reconciliationPerformance: {
    pendingCount: number;
    averageResolutionTime: number; // in hours
    resolutionRate: number; // percentage
    overdueCount: number;
  };
  cashFlow: {
    totalCashIn: number;
    totalCashOut: number;
    netCashFlow: number;
    averageOpeningBalance: number;
  };
}

export interface SystemPerformanceMetrics {
  userActivity: {
    totalLogins: number;
    averageSessionDuration: number; // in minutes
    peakHourActivity: number;
    activeUsersCount: number;
  };
  transactionProcessing: {
    averageProcessingTime: number; // in seconds
    transactionsPerHour: number;
    peakHourTransactions: number;
    processingEfficiency: number; // percentage
  };
  errorRates: {
    failedTransactions: number;
    errorRate: number; // percentage
    systemIssues: number;
    recoveryTime: number; // in minutes
  };
  peakLoadAnalysis: {
    busiestHour: number;
    concurrentUsers: number;
    systemLoad: number; // percentage
    responseTime: number; // in ms
  };
}

export interface InventoryOperationMetrics {
  stockAdjustments: {
    totalAdjustments: number;
    adjustmentFrequency: number; // per day
    reasonBreakdown: Array<{
      reason: string;
      count: number;
      percentage: number;
    }>;
    adjustmentTrend: number; // percentage change
  };
  stockMovement: {
    fastMovingItems: number;
    slowMovingItems: number;
    turnoverVelocity: number;
    stockoutEvents: number;
  };
  lowStockAlerts: {
    totalAlerts: number;
    criticalAlerts: number;
    averageResponseTime: number; // in hours
    restockEfficiency: number; // percentage
  };
  transferEfficiency: {
    totalTransfers: number;
    completionRate: number; // percentage
    averageProcessingTime: number; // in hours
    transferAccuracy: number; // percentage
  };
}

export interface AuditComplianceMetrics {
  securityAlerts: {
    totalAlerts: number;
    highSeverityAlerts: number;
    averageResolutionTime: number; // in hours
    alertTrend: number; // percentage change
  };
  complianceScores: {
    reconciliationAccuracy: number; // percentage
    auditTrailCompleteness: number; // percentage
    complianceRating: string; // A, B, C, D, F
    improvementTrend: number; // percentage change
  };
  riskIndicators: {
    largeDiscrepancyFrequency: number;
    unusualTransactionPatterns: number;
    riskScore: number; // 0-100
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  };
  resolutionEfficiency: {
    averageResolutionTime: number; // in hours
    firstTimeResolutionRate: number; // percentage
    escalationRate: number; // percentage
    customerSatisfaction: number; // percentage
  };
}

export interface PerformanceBenchmarks {
  dailyOperationalScore: {
    overallScore: number; // 0-100
    scoreBreakdown: {
      cashManagement: number;
      systemPerformance: number;
      inventoryOps: number;
      compliance: number;
    };
    grade: string; // A+, A, B+, B, C+, C, D, F
  };
  trendComparisons: {
    weekOverWeek: number; // percentage change
    monthOverMonth: number; // percentage change
    yearOverYear: number; // percentage change
    trendDirection: 'IMPROVING' | 'STABLE' | 'DECLINING';
  };
  targetVsActual: {
    revenueTarget: number;
    actualRevenue: number;
    targetAchievement: number; // percentage
    performanceGap: number;
  };
  efficiencyRatios: {
    revenuePerHour: number;
    transactionsPerCashier: number;
    costPerTransaction: number;
    profitMarginRatio: number;
  };
}

export interface OperationalMetricsData {
  cashOperations: CashOperationMetrics;
  systemPerformance: SystemPerformanceMetrics;
  inventoryOps: InventoryOperationMetrics;
  auditCompliance: AuditComplianceMetrics;
  performanceBenchmarks: PerformanceBenchmarks;
  summary: {
    totalMetrics: number;
    alertsRequiringAttention: number;
    overallHealthScore: number; // 0-100
    lastUpdated: string;
  };
}

export interface OperationalMetricsResponse extends AnalyticsApiResponse<OperationalMetricsData> {}
export interface TransactionVolumeResponse extends AnalyticsApiResponse<TransactionVolumeData[]> {}

// Chart component props
export interface ChartComponentProps {
  data: any[];
  config?: Partial<ChartConfig>;
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  onExport?: () => void;
  className?: string;
}

// Filter component props
export interface FilterComponentProps {
  filters: AnalyticsFilters;
  onFiltersChange: (filters: AnalyticsFilters) => void;
  availableCashiers?: { id: string; name: string }[];
  availableCategories?: { id: string; name: string }[];
  availableTerminals?: { id: string; name: string }[];
}


