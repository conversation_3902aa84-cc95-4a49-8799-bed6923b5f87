import { prisma } from '@/lib/prisma';
import { SupplierSelectionEngine } from '@/lib/supplier-selection-engine';
import { DemandForecastingEngine } from '@/lib/demand-forecasting';
import { notify, EVENT_TYPES } from '@/lib/notifications';
import { addDays, differenceInDays } from 'date-fns';

export interface POSuggestion {
  id: string;
  productId: string;
  productName: string;
  productSku: string;
  currentStock: number;
  reorderPoint: number;
  daysUntilStockout: number;
  urgencyLevel: 'critical' | 'high' | 'medium' | 'low';
  recommendedSupplier: {
    supplierId: string;
    supplierName: string;
    productSupplierId: string;
    score: number;
    reasoning: string[];
    purchasePrice: number;
    leadTimeDays?: number;
    minimumOrderQuantity?: number;
    isPreferred: boolean;
  };
  suggestedQuantity: number;
  estimatedCost: number;
  demandForecast: {
    predictedDemand: number;
    confidenceLevel: number;
    seasonalFactor: number;
  };
  createdAt: Date;
  status: 'pending' | 'approved' | 'rejected' | 'po_created';
  metadata: {
    triggerReason: string;
    stockoutDate: Date;
    safetyStockDays: number;
    leadTimeBuffer: number;
  };
}

export interface POSuggestionSummary {
  totalSuggestions: number;
  criticalCount: number;
  highPriorityCount: number;
  totalEstimatedCost: number;
  productsAtRisk: number;
  averageUrgency: number;
  topSuppliers: Array<{
    supplierId: string;
    supplierName: string;
    productCount: number;
    totalValue: number;
  }>;
}

/**
 * Automatic Purchase Order Generation Engine
 * Monitors inventory levels and generates PO suggestions when reorder points are hit
 */
export class AutoPOGenerationEngine {
  // Configuration constants - adjusted for more reasonable calculations
  private static readonly SAFETY_STOCK_DAYS = 10;        // Reduced from 15 to 10 days
  private static readonly LEAD_TIME_BUFFER_DAYS = 5;     // Reduced from 7 to 5 days
  private static readonly FORECAST_PERIOD_DAYS = 30;     // Keep 30 days for forecast
  private static readonly MAX_ORDER_DAYS = 90;           // Maximum 3 months of stock
  private static readonly MIN_ORDER_DAYS = 7;            // Minimum 1 week of stock
  private static readonly MAX_DAILY_CONSUMPTION = 1000;  // Cap for data validation

  /**
   * Analyze all products and generate PO suggestions for those hitting reorder points
   */
  static async generatePOSuggestions(categoryId?: string): Promise<{
    suggestions: POSuggestion[];
    summary: POSuggestionSummary;
  }> {
    console.log('🔄 Starting automatic PO suggestion generation...');

    try {
      // Get products that need analysis
      const products = await this.getProductsForAnalysis(categoryId);
      console.log(`📦 Analyzing ${products.length} products for reorder suggestions`);

      const suggestions: POSuggestion[] = [];

      // Analyze each product
      for (const product of products) {
        try {
          const suggestion = await this.analyzeProductForPOSuggestion(product);
          if (suggestion) {
            suggestions.push(suggestion);
          }
        } catch (error) {
          console.error(`Error analyzing product ${product.name}:`, error);
        }
      }

      // Sort suggestions by urgency and estimated cost
      suggestions.sort((a, b) => {
        const urgencyOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        if (urgencyOrder[a.urgencyLevel] !== urgencyOrder[b.urgencyLevel]) {
          return urgencyOrder[b.urgencyLevel] - urgencyOrder[a.urgencyLevel];
        }
        return b.estimatedCost - a.estimatedCost;
      });

      // Generate summary
      const summary = this.generateSummary(suggestions);

      console.log(`✅ Generated ${suggestions.length} PO suggestions`);
      
      // Send notifications for critical and high priority suggestions
      await this.sendNotifications(suggestions);

      return { suggestions, summary };
    } catch (error) {
      console.error('Error generating PO suggestions:', error);
      throw error;
    }
  }

  /**
   * Get products that need reorder analysis
   */
  private static async getProductsForAnalysis(categoryId?: string) {
    const whereClause: any = {
      active: true,
      // Only analyze products with suppliers
      productSuppliers: {
        some: {
          isActive: true,
        },
      },
    };

    if (categoryId) {
      whereClause.categoryId = categoryId;
    }

    return await prisma.product.findMany({
      where: whereClause,
      include: {
        category: true,
        unit: true,
        storeStock: true,
        warehouseStock: true,
        productSuppliers: {
          where: { isActive: true },
          include: {
            supplier: true,
          },
        },
        stockBatches: {
          where: {
            remainingQuantity: { gt: 0 },
          },
          orderBy: {
            receivedDate: 'asc', // FIFO order
          },
        },
        transactionItems: {
          where: {
            transaction: {
              createdAt: {
                gte: addDays(new Date(), -90), // Last 90 days for consumption analysis
              },
            },
          },
          include: {
            transaction: true,
          },
        },
      },
    });
  }

  /**
   * Analyze a single product for PO suggestion
   */
  private static async analyzeProductForPOSuggestion(product: any): Promise<POSuggestion | null> {
    console.log(`\n🔍 Analyzing product: ${product.name} (SKU: ${product.sku})`);

    // Calculate current stock
    const currentStock = Number(product.storeStock?.quantity || 0) +
                        Number(product.warehouseStock?.quantity || 0);

    console.log(`📦 Current stock: Store=${product.storeStock?.quantity || 0}, Warehouse=${product.warehouseStock?.quantity || 0}, Total=${currentStock}`);

    // Calculate consumption rate
    const consumptionAnalysis = this.analyzeConsumptionRate(product.transactionItems);

    if (consumptionAnalysis.averageDailyConsumption <= 0) {
      console.log(`⏭️  Skipping ${product.name} - no consumption history`);
      return null;
    }

    // Calculate reorder point
    const reorderPoint = this.calculateReorderPoint(
      consumptionAnalysis.averageDailyConsumption,
      this.SAFETY_STOCK_DAYS,
      this.LEAD_TIME_BUFFER_DAYS
    );

    console.log(`🎯 Reorder point: ${reorderPoint} (${this.SAFETY_STOCK_DAYS + this.LEAD_TIME_BUFFER_DAYS} days coverage)`);

    // Check if product needs reordering
    if (currentStock > reorderPoint) {
      console.log(`✅ Stock sufficient: ${currentStock} > ${reorderPoint} - no reorder needed`);
      return null; // Stock is sufficient
    }

    // Calculate days until stockout
    const daysUntilStockout = currentStock / consumptionAnalysis.averageDailyConsumption;
    console.log(`⏰ Days until stockout: ${daysUntilStockout.toFixed(1)} days`);

    // Get demand forecast
    const demandForecast = await this.getDemandForecast(product, consumptionAnalysis);

    // Get unit price for constraint validation (use first available supplier)
    const unitPrice = product.productSuppliers.length > 0
      ? Number(product.productSuppliers[0].purchasePrice || 50000)
      : 50000;

    console.log(`💰 Using unit price: ${unitPrice.toLocaleString()} IDR for quantity calculation`);

    // Calculate suggested quantity
    const suggestedQuantity = this.calculateSuggestedQuantity(
      demandForecast.predictedDemand,
      currentStock,
      consumptionAnalysis.averageDailyConsumption,
      unitPrice
    );

    // Get optimal supplier recommendation
    const supplierRecommendation = await SupplierSelectionEngine.getSupplierRecommendation(
      product.id,
      suggestedQuantity
    );

    if (!supplierRecommendation) {
      console.warn(`No supplier recommendation found for product ${product.name}`);
      return null;
    }

    // Calculate urgency level
    const urgencyLevel = this.calculateUrgencyLevel(daysUntilStockout, consumptionAnalysis.trend);

    // Calculate estimated cost
    const estimatedCost = supplierRecommendation.recommendedSupplier.purchasePrice * suggestedQuantity;

    const suggestion: POSuggestion = {
      id: `po-suggestion-${product.id}-${Date.now()}`,
      productId: product.id,
      productName: product.name,
      productSku: product.sku,
      currentStock,
      reorderPoint,
      daysUntilStockout: Math.max(0, daysUntilStockout),
      urgencyLevel,
      recommendedSupplier: {
        supplierId: supplierRecommendation.recommendedSupplier.supplierId,
        supplierName: supplierRecommendation.recommendedSupplier.supplierName,
        productSupplierId: supplierRecommendation.recommendedSupplier.productSupplierId,
        score: supplierRecommendation.recommendedSupplier.totalScore,
        reasoning: supplierRecommendation.recommendedSupplier.reasoning,
        purchasePrice: supplierRecommendation.recommendedSupplier.purchasePrice,
        leadTimeDays: supplierRecommendation.recommendedSupplier.leadTimeDays,
        minimumOrderQuantity: supplierRecommendation.recommendedSupplier.minimumOrderQuantity,
        isPreferred: supplierRecommendation.recommendedSupplier.isPreferred,
      },
      suggestedQuantity,
      estimatedCost,
      demandForecast: {
        predictedDemand: demandForecast.predictedDemand,
        confidenceLevel: demandForecast.confidenceLevel,
        seasonalFactor: demandForecast.seasonalFactor,
      },
      createdAt: new Date(),
      status: 'pending',
      metadata: {
        triggerReason: `Stock below reorder point (${currentStock} < ${reorderPoint})`,
        stockoutDate: addDays(new Date(), Math.floor(daysUntilStockout)),
        safetyStockDays: this.SAFETY_STOCK_DAYS,
        leadTimeBuffer: this.LEAD_TIME_BUFFER_DAYS,
      },
    };

    return suggestion;
  }

  /**
   * Analyze consumption rate from transaction history
   */
  private static analyzeConsumptionRate(transactionItems: any[]) {
    console.log(`📊 Analyzing consumption rate:`);
    console.log(`  📝 Transaction items: ${transactionItems.length}`);

    if (transactionItems.length === 0) {
      console.log(`  ⚠️  No transaction history found`);
      return {
        averageDailyConsumption: 0,
        totalConsumption: 0,
        trend: 'stable' as const,
        confidence: 0,
      };
    }

    // Calculate total consumption
    const totalConsumption = transactionItems.reduce(
      (sum, item) => sum + Number(item.quantity), 0
    );

    console.log(`  📦 Total consumption: ${totalConsumption}`);

    // Calculate days covered
    const oldestTransaction = transactionItems.reduce(
      (oldest, item) =>
        item.transaction.createdAt < oldest ? item.transaction.createdAt : oldest,
      transactionItems[0].transaction.createdAt
    );

    const daysCovered = Math.max(1, differenceInDays(new Date(), oldestTransaction));
    const rawAverageDailyConsumption = totalConsumption / daysCovered;

    console.log(`  📅 Days covered: ${daysCovered}`);
    console.log(`  📈 Raw average daily consumption: ${rawAverageDailyConsumption}`);

    // Add validation for unreasonable consumption rates
    let averageDailyConsumption = rawAverageDailyConsumption;

    // Cap extremely high daily consumption (likely data error)
    if (averageDailyConsumption > this.MAX_DAILY_CONSUMPTION) {
      console.log(`  ⚠️  Capping excessive daily consumption from ${averageDailyConsumption} to ${this.MAX_DAILY_CONSUMPTION}`);
      averageDailyConsumption = this.MAX_DAILY_CONSUMPTION;
    }

    // Handle very low consumption (less than 1 per week)
    const minMeaningfulConsumption = 1 / 7; // 1 unit per week
    if (averageDailyConsumption < minMeaningfulConsumption && averageDailyConsumption > 0) {
      console.log(`  📉 Very low consumption detected: ${averageDailyConsumption}, using minimum meaningful rate`);
      averageDailyConsumption = minMeaningfulConsumption;
    }

    console.log(`  ✅ Final average daily consumption: ${averageDailyConsumption}`);

    // Simple trend analysis (compare first half vs second half)
    const midPoint = Math.floor(transactionItems.length / 2);
    const firstHalf = transactionItems.slice(0, midPoint);
    const secondHalf = transactionItems.slice(midPoint);

    const firstHalfAvg = firstHalf.length > 0 ? 
      firstHalf.reduce((sum, item) => sum + Number(item.quantity), 0) / firstHalf.length : 0;
    const secondHalfAvg = secondHalf.length > 0 ? 
      secondHalf.reduce((sum, item) => sum + Number(item.quantity), 0) / secondHalf.length : 0;

    let trend: 'increasing' | 'stable' | 'decreasing' = 'stable';
    if (secondHalfAvg > firstHalfAvg * 1.1) {
      trend = 'increasing';
    } else if (secondHalfAvg < firstHalfAvg * 0.9) {
      trend = 'decreasing';
    }

    const confidence = Math.min(100, (transactionItems.length / 30) * 100); // Higher confidence with more data points

    return {
      averageDailyConsumption,
      totalConsumption,
      trend,
      confidence,
    };
  }

  /**
   * Calculate reorder point based on consumption and lead times
   */
  private static calculateReorderPoint(
    averageDailyConsumption: number,
    safetyStockDays: number,
    leadTimeBufferDays: number
  ): number {
    return Math.ceil(averageDailyConsumption * (safetyStockDays + leadTimeBufferDays));
  }

  /**
   * Get demand forecast for a product
   */
  private static async getDemandForecast(product: any, consumptionAnalysis: any) {
    try {
      // Use existing demand forecasting if available
      if (product.productSuppliers.length > 0) {
        const supplier = product.productSuppliers[0].supplier;
        const forecast = await DemandForecastingEngine.generateProductDemandForecast(
          product,
          supplier,
          '30days'
        );
        return {
          predictedDemand: forecast.predictedDemand,
          confidenceLevel: forecast.confidenceLevel,
          seasonalFactor: forecast.seasonalFactors.find(sf => sf.month === new Date().getMonth() + 1)?.adjustmentFactor || 1.0,
        };
      }
    } catch (error) {
      console.warn(`Could not get demand forecast for ${product.name}, using fallback calculation:`, error);
    }

    // Fallback calculation - more conservative approach
    console.log(`📊 Using fallback demand forecast for ${product.name}`);
    console.log(`  📈 Average daily consumption: ${consumptionAnalysis.averageDailyConsumption}`);
    console.log(`  📅 Forecast period: ${this.FORECAST_PERIOD_DAYS} days`);

    // Use a more conservative multiplier for fallback to prevent excessive quantities
    const conservativeMultiplier = 0.8; // 20% reduction for uncertainty
    const rawPredictedDemand = consumptionAnalysis.averageDailyConsumption * this.FORECAST_PERIOD_DAYS;
    const predictedDemand = Math.ceil(rawPredictedDemand * conservativeMultiplier);

    console.log(`  🔢 Raw predicted demand: ${rawPredictedDemand}`);
    console.log(`  🛡️  Conservative multiplier: ${conservativeMultiplier}`);
    console.log(`  🎯 Final predicted demand: ${predictedDemand}`);

    return {
      predictedDemand,
      confidenceLevel: Math.min(consumptionAnalysis.confidence, 60), // Lower confidence for fallback
      seasonalFactor: 1.0,
    };
  }

  /**
   * Calculate suggested order quantity
   */
  private static calculateSuggestedQuantity(
    predictedDemand: number,
    currentStock: number,
    averageDailyConsumption: number,
    unitPrice: number = 50000 // Default unit price for calculation
  ): number {
    console.log(`🧮 Calculating suggested quantity:`);
    console.log(`  📊 Predicted demand (30 days): ${predictedDemand}`);
    console.log(`  📦 Current stock: ${currentStock}`);
    console.log(`  📈 Average daily consumption: ${averageDailyConsumption}`);
    console.log(`  💰 Unit price: ${unitPrice.toLocaleString()} IDR`);

    // Calculate safety stock (additional buffer beyond predicted demand)
    const safetyStock = Math.ceil(averageDailyConsumption * this.SAFETY_STOCK_DAYS);

    // Calculate lead time buffer (time to receive new stock)
    const leadTimeBuffer = Math.ceil(averageDailyConsumption * this.LEAD_TIME_BUFFER_DAYS);

    console.log(`  🛡️  Safety stock (${this.SAFETY_STOCK_DAYS} days): ${safetyStock}`);
    console.log(`  ⏱️  Lead time buffer (${this.LEAD_TIME_BUFFER_DAYS} days): ${leadTimeBuffer}`);

    // FIXED: Don't double-count - predicted demand already covers forecast period
    // Only add safety stock and lead time buffer if current stock is very low
    let totalNeeded: number;

    if (currentStock <= averageDailyConsumption * 3) {
      // Very low stock - need full coverage
      totalNeeded = predictedDemand + safetyStock + leadTimeBuffer;
      console.log(`  ⚠️  Very low stock - using full calculation`);
    } else {
      // Normal stock levels - more conservative approach
      totalNeeded = predictedDemand + Math.ceil((safetyStock + leadTimeBuffer) * 0.5);
      console.log(`  ✅ Normal stock - using conservative calculation`);
    }

    const suggestedQuantity = Math.max(0, totalNeeded - currentStock);

    console.log(`  🎯 Total needed: ${totalNeeded}`);
    console.log(`  📋 Raw suggested quantity: ${suggestedQuantity}`);

    // Add reasonable upper and lower bounds
    const maxReasonableQuantity = Math.ceil(averageDailyConsumption * this.MAX_ORDER_DAYS);
    const minReasonableQuantity = Math.ceil(averageDailyConsumption * this.MIN_ORDER_DAYS);

    // CRITICAL: Calculate maximum quantity based on unit price to prevent database constraint violations
    const maxDecimalValue = 99999999.99; // Database constraint @db.Decimal(10, 2)
    const maxQuantityByPrice = Math.floor(maxDecimalValue / unitPrice);

    console.log(`  🔒 Max quantity by time (${this.MAX_ORDER_DAYS} days): ${maxReasonableQuantity}`);
    console.log(`  💰 Max quantity by price (${maxDecimalValue.toLocaleString()} ÷ ${unitPrice.toLocaleString()}): ${maxQuantityByPrice}`);

    // Use the more restrictive limit
    const effectiveMaxQuantity = Math.min(maxReasonableQuantity, maxQuantityByPrice);
    console.log(`  ⚖️  Effective max quantity: ${effectiveMaxQuantity}`);

    let finalQuantity = Math.ceil(suggestedQuantity);

    // Apply upper bound (most restrictive of time-based or price-based)
    if (finalQuantity > effectiveMaxQuantity) {
      const reason = maxQuantityByPrice < maxReasonableQuantity ? 'price constraint' : 'time constraint';
      console.log(`  🔒 Capping quantity from ${finalQuantity} to max ${effectiveMaxQuantity} (${reason})`);
      finalQuantity = effectiveMaxQuantity;
    }

    // Apply lower bound (only if we actually need to order)
    if (finalQuantity > 0 && finalQuantity < minReasonableQuantity) {
      // But don't exceed price constraint even for minimum
      const safeMinQuantity = Math.min(minReasonableQuantity, maxQuantityByPrice);
      if (safeMinQuantity !== minReasonableQuantity) {
        console.log(`  ⚠️  Adjusting min quantity from ${minReasonableQuantity} to ${safeMinQuantity} due to price constraint`);
      }
      console.log(`  📈 Increasing quantity from ${finalQuantity} to min ${safeMinQuantity} (${this.MIN_ORDER_DAYS} days)`);
      finalQuantity = safeMinQuantity;
    }

    console.log(`  ✨ Final suggested quantity: ${finalQuantity}`);

    return finalQuantity;
  }

  /**
   * Calculate urgency level based on days until stockout
   */
  private static calculateUrgencyLevel(
    daysUntilStockout: number,
    trend: 'increasing' | 'stable' | 'decreasing'
  ): 'critical' | 'high' | 'medium' | 'low' {
    // Adjust urgency based on trend
    let adjustedDays = daysUntilStockout;
    if (trend === 'increasing') {
      adjustedDays *= 0.8; // More urgent if demand is increasing
    } else if (trend === 'decreasing') {
      adjustedDays *= 1.2; // Less urgent if demand is decreasing
    }

    if (adjustedDays <= 3) return 'critical';
    if (adjustedDays <= 7) return 'high';
    if (adjustedDays <= 14) return 'medium';
    return 'low';
  }

  /**
   * Generate summary statistics
   */
  private static generateSummary(suggestions: POSuggestion[]): POSuggestionSummary {
    const totalSuggestions = suggestions.length;
    const criticalCount = suggestions.filter(s => s.urgencyLevel === 'critical').length;
    const highPriorityCount = suggestions.filter(s => s.urgencyLevel === 'high').length;
    const totalEstimatedCost = suggestions.reduce((sum, s) => sum + s.estimatedCost, 0);
    const productsAtRisk = suggestions.filter(s => s.daysUntilStockout <= 7).length;

    // Calculate average urgency (weighted)
    const urgencyWeights = { critical: 4, high: 3, medium: 2, low: 1 };
    const totalUrgencyScore = suggestions.reduce(
      (sum, s) => sum + urgencyWeights[s.urgencyLevel], 0
    );
    const averageUrgency = totalSuggestions > 0 ? totalUrgencyScore / totalSuggestions : 0;

    // Group by supplier
    const supplierGroups = new Map<string, { name: string; count: number; value: number }>();
    suggestions.forEach(s => {
      const key = s.recommendedSupplier.supplierId;
      if (!supplierGroups.has(key)) {
        supplierGroups.set(key, {
          name: s.recommendedSupplier.supplierName,
          count: 0,
          value: 0,
        });
      }
      const group = supplierGroups.get(key)!;
      group.count++;
      group.value += s.estimatedCost;
    });

    const topSuppliers = Array.from(supplierGroups.entries())
      .map(([supplierId, data]) => ({
        supplierId,
        supplierName: data.name,
        productCount: data.count,
        totalValue: data.value,
      }))
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 5);

    return {
      totalSuggestions,
      criticalCount,
      highPriorityCount,
      totalEstimatedCost,
      productsAtRisk,
      averageUrgency,
      topSuppliers,
    };
  }

  /**
   * Send notifications for critical and high priority suggestions
   */
  private static async sendNotifications(suggestions: POSuggestion[]) {
    const criticalSuggestions = suggestions.filter(s => s.urgencyLevel === 'critical');
    const highPrioritySuggestions = suggestions.filter(s => s.urgencyLevel === 'high');

    // Send notifications for critical suggestions
    for (const suggestion of criticalSuggestions) {
      await this.sendPOSuggestionNotification(suggestion, 'critical');
    }

    // Send batch notification for high priority suggestions
    if (highPrioritySuggestions.length > 0) {
      await this.sendBatchPOSuggestionNotification(highPrioritySuggestions, 'high');
    }

    // Send daily summary if there are any suggestions
    if (suggestions.length > 0) {
      await this.sendDailySummaryNotification(suggestions);
    }
  }

  /**
   * Send notification for individual PO suggestion
   */
  private static async sendPOSuggestionNotification(suggestion: POSuggestion, priority: 'critical' | 'high') {
    await notify({
      eventType: 'po.suggestion.created',
      sourceId: suggestion.productId,
      sourceType: 'product',
      payload: {
        suggestionId: suggestion.id,
        productId: suggestion.productId,
        productName: suggestion.productName,
        productSku: suggestion.productSku,
        currentStock: suggestion.currentStock,
        daysUntilStockout: suggestion.daysUntilStockout,
        urgencyLevel: suggestion.urgencyLevel,
        supplierName: suggestion.recommendedSupplier.supplierName,
        suggestedQuantity: suggestion.suggestedQuantity,
        estimatedCost: suggestion.estimatedCost,
        priority,
      },
    });
  }

  /**
   * Send batch notification for multiple suggestions
   */
  private static async sendBatchPOSuggestionNotification(suggestions: POSuggestion[], priority: 'high') {
    await notify({
      eventType: 'po.suggestions.batch',
      sourceId: 'batch',
      sourceType: 'system',
      payload: {
        suggestionCount: suggestions.length,
        totalEstimatedCost: suggestions.reduce((sum, s) => sum + s.estimatedCost, 0),
        products: suggestions.map(s => ({
          productName: s.productName,
          daysUntilStockout: s.daysUntilStockout,
          supplierName: s.recommendedSupplier.supplierName,
        })),
        priority,
      },
    });
  }

  /**
   * Send daily summary notification
   */
  private static async sendDailySummaryNotification(suggestions: POSuggestion[]) {
    const summary = this.generateSummary(suggestions);

    await notify({
      eventType: 'po.suggestions.daily_summary',
      sourceId: 'daily_summary',
      sourceType: 'system',
      payload: {
        date: new Date().toISOString().split('T')[0],
        summary,
        topUrgentProducts: suggestions
          .filter(s => s.urgencyLevel === 'critical' || s.urgencyLevel === 'high')
          .slice(0, 5)
          .map(s => ({
            productName: s.productName,
            urgencyLevel: s.urgencyLevel,
            daysUntilStockout: s.daysUntilStockout,
            supplierName: s.recommendedSupplier.supplierName,
          })),
      },
    });
  }
}
