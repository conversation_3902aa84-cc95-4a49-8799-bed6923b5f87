/**
 * Browser detection and user agent parsing utilities
 */

export interface BrowserInfo {
  browser: string;
  version: string;
  os: string;
  device: string;
  readable: string; // Human-readable format like "Chrome 137.0 on Windows 10"
}

/**
 * Parse user agent string into readable browser information
 */
export function parseBrowserInfo(userAgent: string): BrowserInfo {
  if (!userAgent || userAgent === "unknown") {
    return {
      browser: "Unknown",
      version: "",
      os: "Unknown",
      device: "Unknown",
      readable: "Unknown Browser"
    };
  }

  // Browser detection
  let browser = "Unknown";
  let version = "";

  // Chrome (must be before Safari since Chrome includes Safari in UA)
  if (userAgent.includes("Chrome/") && !userAgent.includes("Edg/")) {
    browser = "Chrome";
    const match = userAgent.match(/Chrome\/([0-9.]+)/);
    version = match ? match[1].split('.')[0] + "." + match[1].split('.')[1] : "";
  }
  // Edge
  else if (userAgent.includes("Edg/")) {
    browser = "Edge";
    const match = userAgent.match(/Edg\/([0-9.]+)/);
    version = match ? match[1].split('.')[0] + "." + match[1].split('.')[1] : "";
  }
  // Firefox
  else if (userAgent.includes("Firefox/")) {
    browser = "Firefox";
    const match = userAgent.match(/Firefox\/([0-9.]+)/);
    version = match ? match[1].split('.')[0] + "." + match[1].split('.')[1] : "";
  }
  // Safari (must be after Chrome check)
  else if (userAgent.includes("Safari/") && !userAgent.includes("Chrome/")) {
    browser = "Safari";
    const match = userAgent.match(/Version\/([0-9.]+)/);
    version = match ? match[1].split('.')[0] + "." + match[1].split('.')[1] : "";
  }
  // Internet Explorer
  else if (userAgent.includes("MSIE") || userAgent.includes("Trident/")) {
    browser = "Internet Explorer";
    const match = userAgent.match(/(?:MSIE |rv:)([0-9.]+)/);
    version = match ? match[1].split('.')[0] : "";
  }

  // Operating System detection
  let os = "Unknown";
  if (userAgent.includes("Windows NT 10.0")) {
    os = "Windows 10";
  } else if (userAgent.includes("Windows NT 6.3")) {
    os = "Windows 8.1";
  } else if (userAgent.includes("Windows NT 6.2")) {
    os = "Windows 8";
  } else if (userAgent.includes("Windows NT 6.1")) {
    os = "Windows 7";
  } else if (userAgent.includes("Windows")) {
    os = "Windows";
  } else if (userAgent.includes("Mac OS X")) {
    const match = userAgent.match(/Mac OS X ([0-9_]+)/);
    if (match) {
      const macVersion = match[1].replace(/_/g, '.');
      os = `macOS ${macVersion}`;
    } else {
      os = "macOS";
    }
  } else if (userAgent.includes("Linux")) {
    os = "Linux";
  } else if (userAgent.includes("Android")) {
    const match = userAgent.match(/Android ([0-9.]+)/);
    os = match ? `Android ${match[1]}` : "Android";
  } else if (userAgent.includes("iPhone") || userAgent.includes("iPad")) {
    const match = userAgent.match(/OS ([0-9_]+)/);
    if (match) {
      const iosVersion = match[1].replace(/_/g, '.');
      os = `iOS ${iosVersion}`;
    } else {
      os = "iOS";
    }
  }

  // Device type detection
  let device = "Desktop";
  if (userAgent.includes("Mobile") || userAgent.includes("Android")) {
    device = "Mobile";
  } else if (userAgent.includes("Tablet") || userAgent.includes("iPad")) {
    device = "Tablet";
  }

  // Create readable format
  const readable = version 
    ? `${browser} ${version} on ${os}`
    : `${browser} on ${os}`;

  return {
    browser,
    version,
    os,
    device,
    readable
  };
}

/**
 * Extract enhanced security context with browser information
 */
export function extractEnhancedSecurityContext(userAgent: string, ipAddress: string) {
  const browserInfo = parseBrowserInfo(userAgent);
  
  return {
    userAgent,
    ipAddress,
    browserInfo,
    // Additional context that might be useful
    timestamp: new Date().toISOString(),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  };
}

/**
 * Format browser info for display in tables
 */
export function formatBrowserForDisplay(userAgent: string): string {
  const info = parseBrowserInfo(userAgent);
  return info.readable;
}

/**
 * Get browser icon/emoji based on browser type
 */
export function getBrowserIcon(browser: string): string {
  switch (browser.toLowerCase()) {
    case 'chrome':
      return '🌐';
    case 'firefox':
      return '🦊';
    case 'safari':
      return '🧭';
    case 'edge':
      return '🔷';
    case 'internet explorer':
      return '🌍';
    default:
      return '💻';
  }
}

/**
 * Determine if browser/device combination is suspicious
 */
export function isSuspiciousBrowser(userAgent: string): boolean {
  // Flag very old browsers or suspicious patterns
  if (userAgent.includes("MSIE 6") || 
      userAgent.includes("MSIE 7") ||
      userAgent.includes("MSIE 8") ||
      userAgent.length < 20 ||
      !userAgent.includes("Mozilla")) {
    return true;
  }
  
  return false;
}
