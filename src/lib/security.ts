import { PrismaClient } from "@prisma/client";
import { SecurityContext } from "./enhanced-auth";

const prisma = new PrismaClient();

export interface SecurityEventData {
  action: string;
  resource: string;
  resourceId?: string;
  metadata?: Record<string, any>;
}

export async function logSecurityEvent(
  securityContext: SecurityContext,
  eventData: SecurityEventData
): Promise<void> {
  try {
    // For now, just log to console
    // In a full implementation, you would save to a security audit log table
    console.log('Security Event:', {
      timestamp: new Date().toISOString(),
      context: securityContext,
      event: eventData,
    });

    // You could also save to database if you have a security_events table
    // await prisma.securityEvent.create({
    //   data: {
    //     action: eventData.action,
    //     resource: eventData.resource,
    //     resourceId: eventData.resourceId,
    //     metadata: eventData.metadata,
    //     ipAddress: securityContext.ipAddress,
    //     userAgent: securityContext.userAgent,
    //     sessionType: securityContext.sessionType,
    //   },
    // });
  } catch (error) {
    console.error('Error logging security event:', error);
  }
}
