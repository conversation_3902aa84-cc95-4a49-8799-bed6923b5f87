import { prisma } from "@/auth";
import { BatchManagementService } from "./batch-management";

/**
 * Test script to verify batch integration functionality
 * This should be run in a development environment only
 */
export async function testBatchIntegration() {
  console.log("🧪 Starting Batch Integration Test...");

  try {
    // 1. Find a product with existing batches
    const productWithBatches = await prisma.product.findFirst({
      where: {
        stockBatches: {
          some: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 0 }
          }
        }
      },
      include: {
        stockBatches: {
          where: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 0 }
          },
          orderBy: { receivedDate: 'asc' }
        },
        storeStock: true,
        warehouseStock: true
      }
    });

    if (!productWithBatches) {
      console.log("❌ No products with active batches found. Please create some batches first.");
      return;
    }

    console.log(`✅ Found product: ${productWithBatches.name} (${productWithBatches.sku})`);
    console.log(`   Active batches: ${productWithBatches.stockBatches.length}`);

    // 2. Test FIFO batch selection
    console.log("\n🔍 Testing FIFO batch selection...");
    
    const storeBatches = productWithBatches.stockBatches.filter(b => b.storeStockId);
    const warehouseBatches = productWithBatches.stockBatches.filter(b => b.warehouseStockId);

    if (storeBatches.length > 0) {
      const testQuantity = Math.min(5, Number(storeBatches[0].remainingQuantity));
      
      const batchSelection = await BatchManagementService.selectBatchesForConsumption({
        productId: productWithBatches.id,
        requiredQuantity: testQuantity,
        location: 'store'
      });

      console.log(`   Requested: ${testQuantity} units from store`);
      console.log(`   Selected batches: ${batchSelection.consumedBatches.length}`);
      console.log(`   Total available: ${batchSelection.totalConsumed}`);
      console.log(`   Sufficient stock: ${!batchSelection.insufficientStock}`);

      // Verify FIFO order
      if (batchSelection.consumedBatches.length > 1) {
        const firstBatch = await prisma.stockBatch.findUnique({
          where: { id: batchSelection.consumedBatches[0].batchId },
          select: { receivedDate: true }
        });
        const secondBatch = await prisma.stockBatch.findUnique({
          where: { id: batchSelection.consumedBatches[1].batchId },
          select: { receivedDate: true }
        });

        if (firstBatch && secondBatch) {
          const fifoCorrect = firstBatch.receivedDate <= secondBatch.receivedDate;
          console.log(`   FIFO order correct: ${fifoCorrect ? '✅' : '❌'}`);
        }
      }
    }

    // 3. Test stock-batch integrity validation
    console.log("\n🔍 Testing stock-batch integrity validation...");
    
    if (productWithBatches.storeStock) {
      const storeIntegrity = await BatchManagementService.validateStockBatchIntegrity(
        productWithBatches.id,
        'store'
      );
      
      console.log(`   Store stock integrity: ${storeIntegrity.isValid ? '✅' : '❌'}`);
      console.log(`   Stock quantity: ${storeIntegrity.stockQuantity}`);
      console.log(`   Batch total: ${storeIntegrity.batchTotalQuantity}`);
      console.log(`   Difference: ${storeIntegrity.difference}`);
    }

    if (productWithBatches.warehouseStock) {
      const warehouseIntegrity = await BatchManagementService.validateStockBatchIntegrity(
        productWithBatches.id,
        'warehouse'
      );
      
      console.log(`   Warehouse stock integrity: ${warehouseIntegrity.isValid ? '✅' : '❌'}`);
      console.log(`   Stock quantity: ${warehouseIntegrity.stockQuantity}`);
      console.log(`   Batch total: ${warehouseIntegrity.batchTotalQuantity}`);
      console.log(`   Difference: ${warehouseIntegrity.difference}`);
    }

    // 4. Test batch consumption simulation (without actually executing)
    console.log("\n🔍 Testing batch consumption simulation...");
    
    if (storeBatches.length > 0) {
      const testQuantity = Math.min(3, Number(storeBatches[0].remainingQuantity));
      
      console.log(`   Simulating consumption of ${testQuantity} units from store...`);
      
      const beforeBatches = await prisma.stockBatch.findMany({
        where: {
          productId: productWithBatches.id,
          storeStockId: { not: null },
          status: 'ACTIVE'
        },
        select: { id: true, remainingQuantity: true }
      });

      console.log(`   Before: ${beforeBatches.length} active batches`);
      console.log(`   Total remaining: ${beforeBatches.reduce((sum, b) => sum + Number(b.remainingQuantity), 0)}`);

      // This is just a simulation - we're not actually executing the consumption
      const simulatedResult = await BatchManagementService.selectBatchesForConsumption({
        productId: productWithBatches.id,
        requiredQuantity: testQuantity,
        location: 'store'
      });

      console.log(`   Would consume from ${simulatedResult.consumedBatches.length} batches`);
      simulatedResult.consumedBatches.forEach((consumption, index) => {
        console.log(`     Batch ${index + 1}: ${consumption.consumedQuantity} units (${consumption.remainingQuantity} would remain)`);
      });
    }

    console.log("\n✅ Batch Integration Test completed successfully!");
    
    return {
      success: true,
      productTested: {
        id: productWithBatches.id,
        name: productWithBatches.name,
        sku: productWithBatches.sku
      },
      batchCount: productWithBatches.stockBatches.length
    };

  } catch (error) {
    console.error("❌ Batch Integration Test failed:", error);
    return {
      success: false,
      error: (error as Error).message
    };
  }
}

/**
 * Quick integrity check for all products
 */
export async function quickIntegrityCheck() {
  console.log("🔍 Running quick integrity check...");

  try {
    const products = await prisma.product.findMany({
      where: {
        OR: [
          { storeStock: { isNot: null } },
          { warehouseStock: { isNot: null } }
        ]
      },
      select: {
        id: true,
        name: true,
        sku: true,
        storeStock: { select: { quantity: true } },
        warehouseStock: { select: { quantity: true } }
      }
    });

    let totalChecked = 0;
    let integrityIssues = 0;

    for (const product of products) {
      if (product.storeStock) {
        totalChecked++;
        const storeIntegrity = await BatchManagementService.validateStockBatchIntegrity(
          product.id,
          'store'
        );
        if (!storeIntegrity.isValid) {
          integrityIssues++;
          console.log(`❌ ${product.name} (Store): Stock=${storeIntegrity.stockQuantity}, Batches=${storeIntegrity.batchTotalQuantity}`);
        }
      }

      if (product.warehouseStock) {
        totalChecked++;
        const warehouseIntegrity = await BatchManagementService.validateStockBatchIntegrity(
          product.id,
          'warehouse'
        );
        if (!warehouseIntegrity.isValid) {
          integrityIssues++;
          console.log(`❌ ${product.name} (Warehouse): Stock=${warehouseIntegrity.stockQuantity}, Batches=${warehouseIntegrity.batchTotalQuantity}`);
        }
      }
    }

    console.log(`\n📊 Integrity Check Results:`);
    console.log(`   Total checked: ${totalChecked}`);
    console.log(`   Issues found: ${integrityIssues}`);
    console.log(`   Success rate: ${totalChecked > 0 ? Math.round(((totalChecked - integrityIssues) / totalChecked) * 100) : 100}%`);

    return {
      totalChecked,
      integrityIssues,
      successRate: totalChecked > 0 ? Math.round(((totalChecked - integrityIssues) / totalChecked) * 100) : 100
    };

  } catch (error) {
    console.error("❌ Quick integrity check failed:", error);
    throw error;
  }
}
