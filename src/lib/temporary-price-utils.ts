/**
 * Utility functions for handling temporary prices
 */
import { prisma } from "@/auth";

/**
 * Check for and remove expired temporary prices
 * 
 * @param userId The ID of the user performing the check (for activity logging)
 * @param productId Optional product ID to check a specific product
 * @returns Object containing the number of expired prices removed and their details
 */
export async function checkAndRemoveExpiredTemporaryPrices(userId: string, productId?: string) {
  try {
    const now = new Date();
    
    // Query to find expired temporary prices
    const whereClause = {
      endDate: {
        lt: now
      },
      ...(productId ? { productId } : {})
    };
    
    // Find expired temporary prices
    const expiredPrices = await prisma.temporaryPrice.findMany({
      where: whereClause,
      include: {
        product: {
          select: {
            name: true,
            sku: true
          }
        }
      }
    });
    
    if (expiredPrices.length === 0) {
      return { 
        removed: 0,
        details: []
      };
    }
    
    // Remove expired temporary prices
    await prisma.temporaryPrice.deleteMany({
      where: whereClause
    });
    
    // Log activity
    await prisma.activityLog.create({
      data: {
        userId,
        action: "REMOVE_EXPIRED_TEMPORARY_PRICES",
        details: `Removed ${expiredPrices.length} expired temporary prices`,
      },
    });
    
    // Return details about removed prices
    return {
      removed: expiredPrices.length,
      details: expiredPrices.map(price => ({
        id: price.id,
        productId: price.productId,
        productName: price.product.name,
        productSku: price.product.sku,
        endDate: price.endDate
      }))
    };
  } catch (error) {
    console.error("Error checking for expired temporary prices:", error);
    return {
      removed: 0,
      details: [],
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}
