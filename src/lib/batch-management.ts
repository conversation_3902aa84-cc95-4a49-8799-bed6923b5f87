import { prisma } from "@/auth";
import { Decimal } from "@prisma/client/runtime/library";

export interface BatchConsumptionResult {
  consumedBatches: {
    batchId: string;
    consumedQuantity: number;
    remainingQuantity: number;
  }[];
  totalConsumed: number;
  insufficientStock: boolean;
}

export interface BatchSelectionOptions {
  productId: string;
  requiredQuantity: number;
  location: 'store' | 'warehouse';
  excludeExpired?: boolean;
}

/**
 * FIFO Batch Selection Service
 * Selects batches for consumption using First-In-First-Out logic
 */
export class BatchManagementService {
  
  /**
   * Select batches for consumption using FIFO logic
   */
  static async selectBatchesForConsumption(
    options: BatchSelectionOptions
  ): Promise<BatchConsumptionResult> {
    const { productId, requiredQuantity, location, excludeExpired = true } = options;
    
    // Build where clause based on location
    const whereClause: any = {
      productId,
      status: 'ACTIVE',
      remainingQuantity: { gt: 0 }
    };

    if (location === 'store') {
      whereClause.storeStockId = { not: null };
    } else {
      whereClause.warehouseStockId = { not: null };
    }

    if (excludeExpired) {
      whereClause.OR = [
        { expiryDate: null },
        { expiryDate: { gt: new Date() } }
      ];
    }

    // Get available batches ordered by FIFO (earliest received first)
    const availableBatches = await prisma.stockBatch.findMany({
      where: whereClause,
      orderBy: [
        { receivedDate: 'asc' },
        { createdAt: 'asc' }
      ],
      select: {
        id: true,
        remainingQuantity: true,
        receivedDate: true,
        expiryDate: true
      }
    });

    const consumedBatches: BatchConsumptionResult['consumedBatches'] = [];
    let remainingToConsume = requiredQuantity;
    let totalConsumed = 0;

    for (const batch of availableBatches) {
      if (remainingToConsume <= 0) break;

      const batchAvailable = Number(batch.remainingQuantity);
      const consumeFromBatch = Math.min(remainingToConsume, batchAvailable);
      
      consumedBatches.push({
        batchId: batch.id,
        consumedQuantity: consumeFromBatch,
        remainingQuantity: batchAvailable - consumeFromBatch
      });

      totalConsumed += consumeFromBatch;
      remainingToConsume -= consumeFromBatch;
    }

    return {
      consumedBatches,
      totalConsumed,
      insufficientStock: remainingToConsume > 0
    };
  }

  /**
   * Execute batch consumption and update database
   */
  static async executeBatchConsumption(
    consumptionResult: BatchConsumptionResult,
    transactionContext: any,
    metadata: {
      source: string;
      referenceId?: string;
      referenceType?: string;
      notes?: string;
      userId: string;
    }
  ): Promise<void> {
    const tx = transactionContext;

    for (const consumption of consumptionResult.consumedBatches) {
      // Update batch remaining quantity
      await tx.stockBatch.update({
        where: { id: consumption.batchId },
        data: {
          remainingQuantity: consumption.remainingQuantity,
          status: consumption.remainingQuantity === 0 ? 'SOLD_OUT' : 'ACTIVE'
        }
      });

      // Create stock history record for batch consumption
      await tx.stockHistory.create({
        data: {
          productId: await this.getBatchProductId(consumption.batchId, tx),
          batchId: consumption.batchId,
          previousQuantity: consumption.remainingQuantity + consumption.consumedQuantity,
          newQuantity: consumption.remainingQuantity,
          changeQuantity: -consumption.consumedQuantity,
          source: metadata.source as any,
          referenceId: metadata.referenceId,
          referenceType: metadata.referenceType,
          notes: metadata.notes || `Batch consumption: ${consumption.consumedQuantity} units`,
          userId: metadata.userId
        }
      });
    }
  }

  /**
   * Helper to get product ID from batch
   */
  private static async getBatchProductId(batchId: string, tx: any): Promise<string> {
    const batch = await tx.stockBatch.findUnique({
      where: { id: batchId },
      select: { productId: true }
    });
    return batch?.productId || '';
  }

  /**
   * Validate stock-batch integrity for a product
   */
  static async validateStockBatchIntegrity(productId: string, location: 'store' | 'warehouse'): Promise<{
    isValid: boolean;
    stockQuantity: number;
    batchTotalQuantity: number;
    difference: number;
  }> {
    // Get stock quantity
    let stockQuantity = 0;
    if (location === 'store') {
      const storeStock = await prisma.storeStock.findUnique({
        where: { productId },
        select: { quantity: true }
      });
      stockQuantity = Number(storeStock?.quantity || 0);
    } else {
      const warehouseStock = await prisma.warehouseStock.findUnique({
        where: { productId },
        select: { quantity: true }
      });
      stockQuantity = Number(warehouseStock?.quantity || 0);
    }

    // Get total batch quantities
    const whereClause: any = {
      productId,
      status: 'ACTIVE'
    };

    if (location === 'store') {
      whereClause.storeStockId = { not: null };
    } else {
      whereClause.warehouseStockId = { not: null };
    }

    const batches = await prisma.stockBatch.findMany({
      where: whereClause,
      select: { remainingQuantity: true }
    });

    const batchTotalQuantity = batches.reduce(
      (sum, batch) => sum + Number(batch.remainingQuantity), 
      0
    );

    const difference = stockQuantity - batchTotalQuantity;

    return {
      isValid: Math.abs(difference) < 0.01, // Allow for small decimal precision differences
      stockQuantity,
      batchTotalQuantity,
      difference
    };
  }

  /**
   * Transfer batches between locations (for stock transfers)
   */
  static async transferBatches(
    productId: string,
    quantity: number,
    fromLocation: 'store' | 'warehouse',
    toLocation: 'store' | 'warehouse',
    userId: string,
    transactionContext: any,
    referenceId?: string
  ): Promise<BatchConsumptionResult> {
    const tx = transactionContext;

    // Select batches from source location using FIFO
    const consumptionResult = await this.selectBatchesForConsumption({
      productId,
      requiredQuantity: quantity,
      location: fromLocation
    });

    if (consumptionResult.insufficientStock) {
      throw new Error(`Insufficient batch stock for transfer. Required: ${quantity}, Available: ${consumptionResult.totalConsumed}`);
    }

    // Get destination stock IDs
    let toStoreStockId: string | null = null;
    let toWarehouseStockId: string | null = null;

    if (toLocation === 'store') {
      const storeStock = await tx.storeStock.findUnique({
        where: { productId },
        select: { id: true }
      });
      toStoreStockId = storeStock?.id || null;
    } else {
      const warehouseStock = await tx.warehouseStock.findUnique({
        where: { productId },
        select: { id: true }
      });
      toWarehouseStockId = warehouseStock?.id || null;
    }

    // Process each consumed batch
    for (const consumption of consumptionResult.consumedBatches) {
      const batch = await tx.stockBatch.findUnique({
        where: { id: consumption.batchId },
        select: {
          productSupplierId: true,
          batchNumber: true,
          receivedDate: true,
          expiryDate: true,
          purchasePrice: true,
          purchaseOrderId: true,
          notes: true
        }
      });

      if (!batch) continue;

      // Update source batch
      await tx.stockBatch.update({
        where: { id: consumption.batchId },
        data: {
          remainingQuantity: consumption.remainingQuantity,
          status: consumption.remainingQuantity === 0 ? 'SOLD_OUT' : 'ACTIVE'
        }
      });

      // Create new batch in destination location if quantity was consumed
      if (consumption.consumedQuantity > 0) {
        await tx.stockBatch.create({
          data: {
            productId,
            productSupplierId: batch.productSupplierId,
            batchNumber: batch.batchNumber,
            receivedDate: batch.receivedDate,
            expiryDate: batch.expiryDate,
            quantity: consumption.consumedQuantity,
            remainingQuantity: consumption.consumedQuantity,
            purchasePrice: batch.purchasePrice,
            purchaseOrderId: batch.purchaseOrderId,
            storeStockId: toStoreStockId,
            warehouseStockId: toWarehouseStockId,
            status: 'ACTIVE',
            notes: `Transferred from ${fromLocation} - ${batch.notes || ''}`.trim()
          }
        });
      }

      // Create stock history for source
      await tx.stockHistory.create({
        data: {
          productId,
          batchId: consumption.batchId,
          previousQuantity: consumption.remainingQuantity + consumption.consumedQuantity,
          newQuantity: consumption.remainingQuantity,
          changeQuantity: -consumption.consumedQuantity,
          source: 'TRANSFER',
          referenceId,
          referenceType: 'StockTransfer',
          notes: `Transfer to ${toLocation}: ${consumption.consumedQuantity} units`,
          userId
        }
      });
    }

    return consumptionResult;
  }

  /**
   * Get batch information for transaction items
   */
  static async getBatchesForTransactionItems(
    items: { productId: string; quantity: number }[],
    location: 'store' | 'warehouse' = 'store'
  ): Promise<{ productId: string; batchId: string; quantity: number }[]> {
    const result: { productId: string; batchId: string; quantity: number }[] = [];

    for (const item of items) {
      const consumptionResult = await this.selectBatchesForConsumption({
        productId: item.productId,
        requiredQuantity: item.quantity,
        location
      });

      for (const consumption of consumptionResult.consumedBatches) {
        result.push({
          productId: item.productId,
          batchId: consumption.batchId,
          quantity: consumption.consumedQuantity
        });
      }
    }

    return result;
  }

  /**
   * Process batch adjustments for manual stock adjustments
   * Handles both positive (additions) and negative (reductions) adjustments
   */
  static async processBatchAdjustment(
    productId: string,
    adjustmentQuantity: number,
    location: 'store' | 'warehouse',
    transactionContext: any,
    metadata: {
      source: string;
      referenceId?: string;
      referenceType?: string;
      notes?: string;
      userId: string;
      reason?: string;
    }
  ): Promise<{
    success: boolean;
    batchesAffected: number;
    totalAdjusted: number;
    insufficientStock?: boolean;
  }> {
    const tx = transactionContext;

    if (adjustmentQuantity === 0) {
      return {
        success: true,
        batchesAffected: 0,
        totalAdjusted: 0
      };
    }

    // Handle negative adjustments (reductions) using FIFO
    if (adjustmentQuantity < 0) {
      const reductionQuantity = Math.abs(adjustmentQuantity);

      const consumptionResult = await this.selectBatchesForConsumption({
        productId,
        requiredQuantity: reductionQuantity,
        location
      });

      if (consumptionResult.insufficientStock) {
        return {
          success: false,
          batchesAffected: 0,
          totalAdjusted: 0,
          insufficientStock: true
        };
      }

      // Execute batch consumption for the reduction
      await this.executeBatchConsumption(
        consumptionResult,
        tx,
        {
          source: metadata.source,
          referenceId: metadata.referenceId,
          referenceType: metadata.referenceType,
          notes: metadata.notes || `Manual adjustment: ${metadata.reason || 'Stock reduction'}`,
          userId: metadata.userId
        }
      );

      return {
        success: true,
        batchesAffected: consumptionResult.consumedBatches.length,
        totalAdjusted: -consumptionResult.totalConsumed
      };
    }

    // Handle positive adjustments (additions)
    // For positive adjustments, we need to create new batch entries or update existing ones
    // This is more complex and should typically be handled through proper receiving processes
    // For now, we'll log this as a limitation
    console.warn(`Positive batch adjustments (+${adjustmentQuantity}) for product ${productId} require manual batch creation`);

    return {
      success: true,
      batchesAffected: 0,
      totalAdjusted: 0
    };
  }
}
