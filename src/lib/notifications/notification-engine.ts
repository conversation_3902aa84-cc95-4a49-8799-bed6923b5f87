/**
 * Notification Engine - Core notification processing system
 * 
 * This engine handles:
 * - Template-based notification generation
 * - User preference filtering
 * - Multi-channel delivery
 * - Notification scheduling and batching
 */

import { prisma } from '@/lib/prisma';
import { BaseEvent, registerEvent<PERSON><PERSON>ler, EVENT_TYPES } from '@/lib/events/event-system';

// Global flag to ensure initialization happens only once
let engineInitialized = false;

// Notification delivery methods
export interface NotificationDeliveryConfig {
  inApp?: boolean;
  toast?: boolean;
  email?: boolean;
  sms?: boolean;
  push?: boolean;
}

// Notification data structure
export interface NotificationData {
  userId: string;
  title: string;
  message: string;
  type: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  actionUrl?: string;
  metadata?: Record<string, any>;
  deliveryMethods?: NotificationDeliveryConfig;
  expiresAt?: Date;
  eventType?: string;
  eventId?: string;
}

// Template variable context
export interface TemplateContext {
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  event?: BaseEvent;
  data?: Record<string, any>;
}

class NotificationEngine {


  /**
   * Process an event and generate notifications
   */
  async processEvent(event: BaseEvent): Promise<void> {
    try {
      console.log(`🔔 Processing notification event: ${event.eventType}`);
      console.log(`📋 Event details:`, JSON.stringify(event, null, 2));

      // Get notification template for this event type
      const template = this.getTemplate(event.eventType);
      if (!template) {
        console.log(`❌ No notification template found for event type: ${event.eventType}`);
        return;
      }

      console.log(`✅ Using template for event ${event.eventType}`);

      // Determine target users for this event
      const targetUsers = await this.getTargetUsers(event);
      if (targetUsers.length === 0) {
        console.log(`❌ No target users found for event: ${event.eventType}`);
        return;
      }

      console.log(`👥 Generating notifications for ${targetUsers.length} users:`, targetUsers.map(u => `${u.name} (${u.role})`));

      // Generate notifications for each target user
      const notifications = await Promise.all(
        targetUsers.map(user => this.generateNotification(event, template, user))
      );

      // Filter out null notifications (user preferences, etc.)
      const validNotifications = notifications.filter(n => n !== null) as NotificationData[];

      if (validNotifications.length === 0) {
        console.log(`❌ No valid notifications generated for event: ${event.eventType} (all filtered out by preferences)`);
        return;
      }

      console.log(`✅ Generated ${validNotifications.length} valid notifications`);

      // Create notifications in database
      await this.createNotifications(validNotifications);

      // Deliver notifications through various channels
      await this.deliverNotifications(validNotifications);

      console.log(`🎉 Successfully processed ${validNotifications.length} notifications for event: ${event.eventType}`);

    } catch (error) {
      console.error(`❌ Error processing notification event ${event.eventType}:`, error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  /**
   * Get notification template for event type
   * Using hardcoded templates since template management was removed
   */
  private getTemplate(eventType: string) {
    const templates: Record<string, any> = {
      'po.status.changed': {
        titleTemplate: 'Purchase Order Status Updated',
        messageTemplate: 'Purchase Order #{{data.poNumber}} status changed from {{data.fromStatus}} to {{data.toStatus}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'po.approved': {
        titleTemplate: 'Purchase Order Approved',
        messageTemplate: 'Purchase Order #{{data.poNumber}} has been approved by {{data.approvedBy}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'po.rejected': {
        titleTemplate: 'Purchase Order Rejected',
        messageTemplate: 'Purchase Order #{{data.poNumber}} has been rejected. Reason: {{data.rejectionReason}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
      },
      'inventory.low_stock': {
        titleTemplate: 'Low Stock Alert',
        messageTemplate: 'Product {{data.productName}} is running low. Current stock: {{data.currentStock}}, Minimum: {{data.minThreshold}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'inventory.out_of_stock': {
        titleTemplate: 'Out of Stock Alert',
        messageTemplate: 'Product {{data.productName}} is out of stock',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
      },
      'cash.audit.alert': {
        titleTemplate: 'Cash Audit Alert',
        messageTemplate: 'Cash audit alert: {{data.alertType}} - {{data.message}}. Discrepancy: {{data.discrepancyAmount}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'URGENT',
      },
      'cash.discrepancy.detected': {
        titleTemplate: 'Cash Discrepancy Detected',
        messageTemplate: 'Cash discrepancy of {{data.discrepancyAmount}} detected at {{data.drawerName}} by {{data.cashierName}} on {{data.businessDate}}. Category: {{data.discrepancyCategory}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
      },

      // Financial Events
      'cash_audit.discrepancy_detected': {
        titleTemplate: 'Cash Reconciliation Discrepancy Alert',
        messageTemplate: 'Cash discrepancy of {{data.discrepancyAmountFormatted}} detected by {{data.cashierName}} ({{data.cashierRole}}) on {{data.businessDate}} at {{data.drawerLocation}}. Category: {{data.discrepancyCategory}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'HIGH',
      },
      'invoice.approved': {
        titleTemplate: 'Invoice Approved',
        messageTemplate: 'Invoice {{data.invoiceNumber}} from {{data.supplierName}} for {{data.totalAmountFormatted}} has been approved by {{data.approverName}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'invoice.payment_made': {
        titleTemplate: 'Invoice Payment Confirmation',
        messageTemplate: 'Payment of {{data.paymentAmountFormatted}} made for Invoice {{data.invoiceNumber}} via {{data.paymentMethod}}{{data.remainingBalance ? ". Remaining balance: " + data.remainingBalanceFormatted : ""}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'inventory.adjustment.pending': {
        titleTemplate: 'Stock Adjustment Pending Approval',
        messageTemplate: '{{data.requestedBy}} requested a stock adjustment for {{data.productName}} ({{data.adjustmentQuantity > 0 ? "+" : ""}}{{data.adjustmentQuantity}}) - {{data.reason}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'inventory.adjustment.approved': {
        titleTemplate: 'Stock Adjustment Approved',
        messageTemplate: 'Your stock adjustment for {{data.productName}} has been approved by {{data.approvedBy}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },
      'inventory.adjustment.rejected': {
        titleTemplate: 'Stock Adjustment Rejected',
        messageTemplate: 'Your stock adjustment for {{data.productName}} has been rejected by {{data.rejectedBy}}. Reason: {{data.rejectionReason}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
      },

      // PO Suggestion Events
      'po.suggestion.created': {
        titleTemplate: '🚨 {{data.urgencyLevel}} Priority: Reorder {{data.productName}}',
        messageTemplate: '{{data.productName}} ({{data.productSku}}) needs reordering. Current stock: {{data.currentStock}}, will run out in {{data.daysUntilStockout}} days. Recommended supplier: {{data.supplierName}} ({{data.suggestedQuantity}} units, {{data.estimatedCost}} IDR)',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
      },

      'po.suggestions.batch': {
        titleTemplate: '📦 {{data.suggestionCount}} Products Need Reordering',
        messageTemplate: '{{data.suggestionCount}} products require purchase orders with total estimated cost of {{data.totalEstimatedCost}} IDR. Review and create purchase orders to prevent stockouts.',
        defaultDeliveryMethods: ['IN_APP'],
        defaultPriority: 'NORMAL',
      },

      'po.suggestions.daily_summary': {
        titleTemplate: '📊 Daily PO Suggestions Summary - {{data.date}}',
        messageTemplate: 'Generated {{data.summary.totalSuggestions}} PO suggestions ({{data.summary.criticalCount}} critical, {{data.summary.highPriorityCount}} high priority). Total estimated cost: {{data.summary.totalEstimatedCost}} IDR.',
        defaultDeliveryMethods: ['IN_APP'],
        defaultPriority: 'NORMAL',
      },

      'po.created.from_suggestion': {
        titleTemplate: '✅ PO Created from Suggestion',
        messageTemplate: 'Purchase Order {{data.poNumber}} has been created from automatic suggestion by {{data.createdBy}}. Total: {{data.total}} IDR.',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
      },

      'po.created.bulk_from_suggestions': {
        titleTemplate: '✅ Bulk PO Created from Suggestions',
        messageTemplate: 'Purchase Order {{data.poNumber}} has been created from {{data.suggestionCount}} automatic suggestions by {{data.createdBy}}. Total: {{data.total}} IDR.',
        defaultDeliveryMethods: ['IN_APP'],
        defaultPriority: 'NORMAL',
      },
    };

    return templates[eventType] || {
      titleTemplate: 'System Notification',
      messageTemplate: 'A system event occurred: {{event.eventType}}',
      defaultDeliveryMethods: ['IN_APP'],
      defaultPriority: 'NORMAL',
    };
  }

  /**
   * Determine target users for an event
   */
  private async getTargetUsers(event: BaseEvent): Promise<any[]> {
    // This is a simplified implementation
    // In a real system, you'd have more sophisticated targeting logic
    
    switch (event.eventType) {
      case 'po.status.changed':
      case 'po.approved':
      case 'po.rejected':
        return this.getPOTargetUsers(event);
      
      case 'inventory.low_stock':
      case 'inventory.out_of_stock':
        return this.getInventoryTargetUsers(event);

      case 'inventory.adjustment.pending':
        return this.getStockAdjustmentApprovalTargetUsers(event);

      case 'inventory.adjustment.approved':
      case 'inventory.adjustment.rejected':
        return this.getStockAdjustmentResponseTargetUsers(event);

      case 'cash.audit.alert':
      case 'cash.discrepancy.detected':
      case 'cash_audit.discrepancy_detected':
      case 'invoice.approved':
      case 'invoice.payment_made':
        return this.getFinancialTargetUsers(event);

      case 'po.suggestion.created':
      case 'po.suggestions.batch':
      case 'po.suggestions.daily_summary':
      case 'po.created.from_suggestion':
      case 'po.created.bulk_from_suggestions':
        return this.getPOSuggestionTargetUsers(event);

      default:
        // For system-wide events, target all active users
        return await prisma.user.findMany({
          where: { active: true },
          select: { id: true, name: true, email: true, role: true },
        });
    }
  }

  /**
   * Get target users for Purchase Order events
   */
  private async getPOTargetUsers(event: BaseEvent): Promise<any[]> {
    const users: any[] = [];

    // Get PO details if available
    if (event.sourceId) {
      try {
        const po = await prisma.purchaseOrder.findUnique({
          where: { id: event.sourceId },
          include: {
            createdBy: { select: { id: true, name: true, email: true, role: true } },
            approvedBy: { select: { id: true, name: true, email: true, role: true } },
          },
        });

        if (po) {
          // Add PO creator
          if (po.createdBy) {
            users.push(po.createdBy);
          }

          // Add approver if different from creator
          if (po.approvedBy && po.approvedBy.id !== po.createdBy?.id) {
            users.push(po.approvedBy);
          }
        }
      } catch (error) {
        console.error('Error fetching PO details:', error);
      }
    }

    // Add users with relevant roles
    const roleUsers = await prisma.user.findMany({
      where: {
        role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'] },
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });

    // Merge and deduplicate users
    const allUsers = [...users, ...roleUsers];
    const uniqueUsers = allUsers.filter((user, index, self) => 
      index === self.findIndex(u => u.id === user.id)
    );

    return uniqueUsers;
  }

  /**
   * Get target users for inventory events
   */
  private async getInventoryTargetUsers(event: BaseEvent): Promise<any[]> {
    return await prisma.user.findMany({
      where: {
        role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN'] },
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });
  }

  /**
   * Get target users for cash audit events
   */
  private async getCashAuditTargetUsers(event: BaseEvent): Promise<any[]> {
    return await prisma.user.findMany({
      where: {
        role: { in: ['SUPER_ADMIN', 'FINANCE_ADMIN'] },
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });
  }

  /**
   * Get target users for financial events (role-based access control)
   * Only SUPER_ADMIN and FINANCE_ADMIN roles can receive financial notifications
   */
  private async getFinancialTargetUsers(event: BaseEvent): Promise<any[]> {
    return await prisma.user.findMany({
      where: {
        role: { in: ['SUPER_ADMIN', 'FINANCE_ADMIN'] },
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });
  }

  /**
   * Get target users for PO suggestion events
   */
  private async getPOSuggestionTargetUsers(event: BaseEvent): Promise<any[]> {
    // PO suggestions should target procurement and inventory management roles
    return await prisma.user.findMany({
      where: {
        role: { in: ['SUPER_ADMIN', 'WAREHOUSE_ADMIN', 'FINANCE_ADMIN'] },
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });
  }

  /**
   * Get target users for stock adjustment approval requests
   */
  private async getStockAdjustmentApprovalTargetUsers(event: BaseEvent): Promise<any[]> {
    // Target only SUPER_ADMIN users for approval requests
    return await prisma.user.findMany({
      where: {
        role: 'SUPER_ADMIN',
        active: true,
      },
      select: { id: true, name: true, email: true, role: true },
    });
  }

  /**
   * Get target users for stock adjustment responses (approved/rejected)
   */
  private async getStockAdjustmentResponseTargetUsers(event: BaseEvent): Promise<any[]> {
    // Target the user who requested the adjustment
    const requestedByEmail = event.payload.requestedByEmail;
    if (!requestedByEmail) {
      console.warn('No requestedByEmail in stock adjustment response event payload');
      return [];
    }

    const user = await prisma.user.findUnique({
      where: { email: requestedByEmail },
      select: { id: true, name: true, email: true, role: true },
    });

    return user ? [user] : [];
  }

  /**
   * Generate notification for a specific user
   */
  private async generateNotification(
    event: BaseEvent,
    template: any,
    user: any
  ): Promise<NotificationData | null> {
    try {
      // Check user preferences
      const preferences = await this.getUserPreferences(user.id, event.eventType);
      if (!preferences.enabled) {
        console.log(`Notifications disabled for user ${user.id} and event ${event.eventType}`);
        return null;
      }

      // Create template context
      const context: TemplateContext = {
        user,
        event,
        data: event.payload,
      };

      // Render title and message from templates
      const title = this.renderTemplate(template.titleTemplate, context);
      const message = this.renderTemplate(template.messageTemplate, context);

      // Determine delivery methods based on user preferences and template defaults
      const deliveryMethods = this.mergeDeliveryMethods(
        template.defaultDeliveryMethods || [],
        preferences.deliveryMethods || []
      );

      return {
        userId: user.id,
        title,
        message,
        type: this.mapEventTypeToNotificationType(event.eventType),
        priority: template.defaultPriority || 'NORMAL',
        actionUrl: this.generateActionUrl(event),
        metadata: {
          eventType: event.eventType,
          eventId: event.eventId,
          sourceId: event.sourceId,
          sourceType: event.sourceType,
          ...event.payload,
        },
        deliveryMethods: this.convertDeliveryMethods(deliveryMethods),
        eventType: event.eventType,
        eventId: event.eventId,
      };

    } catch (error) {
      console.error(`Error generating notification for user ${user.id}:`, error);
      return null;
    }
  }

  /**
   * Get user notification preferences
   */
  private async getUserPreferences(userId: string, eventType: string) {
    try {
      const preference = await prisma.notificationPreference.findUnique({
        where: {
          userId_eventType: {
            userId,
            eventType,
          },
        },
      });

      // Return default preferences if none found
      return preference || {
        enabled: true,
        deliveryMethods: ['IN_APP', 'TOAST'],
        frequency: 'IMMEDIATE',
      };
    } catch (error) {
      console.error(`Error fetching preferences for user ${userId}:`, error);
      return {
        enabled: true,
        deliveryMethods: ['IN_APP', 'TOAST'],
        frequency: 'IMMEDIATE',
      };
    }
  }

  /**
   * Render template with context variables
   */
  private renderTemplate(template: string, context: TemplateContext): string {
    let rendered = template;

    console.log(`🎨 Rendering template: "${template}"`);
    console.log(`📋 Template context:`, JSON.stringify(context, null, 2));

    // Simple template variable replacement
    // In a production system, you might want to use a more sophisticated template engine

    // Replace user variables
    if (context.user) {
      rendered = rendered.replace(/\{\{user\.name\}\}/g, context.user.name || '');
      rendered = rendered.replace(/\{\{user\.email\}\}/g, context.user.email || '');
      rendered = rendered.replace(/\{\{user\.role\}\}/g, context.user.role || '');
    }

    // Replace event variables
    if (context.event) {
      rendered = rendered.replace(/\{\{event\.eventType\}\}/g, context.event.eventType || '');
      rendered = rendered.replace(/\{\{event\.eventId\}\}/g, context.event.eventId || '');
    }

    // Replace data variables - Support both {{data.key}} and {{key}} patterns
    if (context.data) {
      Object.entries(context.data).forEach(([key, value]) => {
        const stringValue = value !== null && value !== undefined ? String(value) : '';

        // Replace {{data.key}} pattern
        const dataRegex = new RegExp(`\\{\\{data\\.${key}\\}\\}`, 'g');
        rendered = rendered.replace(dataRegex, stringValue);

        // Replace {{key}} pattern (for backward compatibility)
        const directRegex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        rendered = rendered.replace(directRegex, stringValue);

        console.log(`🔄 Replaced {{${key}}} and {{data.${key}}} with: "${stringValue}"`);
      });
    }

    console.log(`✅ Final rendered template: "${rendered}"`);
    return rendered;
  }

  /**
   * Map event type to notification type enum
   */
  private mapEventTypeToNotificationType(eventType: string): string {
    const mapping: Record<string, string> = {
      'po.status.changed': 'PURCHASE_ORDER_APPROVAL',
      'po.approved': 'PURCHASE_ORDER_APPROVED',
      'po.rejected': 'PURCHASE_ORDER_REJECTED',
      'po.received': 'PURCHASE_ORDER_RECEIVED',
      'inventory.low_stock': 'INVENTORY_LOW_STOCK',
      'inventory.out_of_stock': 'INVENTORY_OUT_OF_STOCK',
      'inventory.batch.expiring': 'BATCH_EXPIRING',
      'inventory.batch.expired': 'BATCH_EXPIRED',
      'inventory.adjustment.pending': 'STOCK_ADJUSTMENT_PENDING',
      'inventory.adjustment.approved': 'STOCK_ADJUSTMENT_APPROVED',
      'inventory.adjustment.rejected': 'STOCK_ADJUSTMENT_REJECTED',
      'cash.audit.alert': 'CASH_AUDIT_ALERT',
      'cash.discrepancy.detected': 'FINANCIAL',
      'cash_audit.discrepancy_detected': 'FINANCIAL',
      'invoice.approved': 'FINANCIAL',
      'invoice.payment_made': 'FINANCIAL',
      'revenue.target.achieved': 'REVENUE_TARGET_ACHIEVED',
      'revenue.target.missed': 'REVENUE_TARGET_MISSED',
      'system.maintenance': 'SYSTEM_MAINTENANCE',
      'user.action.required': 'USER_ACTION_REQUIRED',
    };

    return mapping[eventType] || 'SYSTEM';
  }

  /**
   * Generate action URL for notification
   */
  private generateActionUrl(event: BaseEvent): string | undefined {
    switch (event.eventType) {
      case 'po.status.changed':
      case 'po.approved':
      case 'po.rejected':
      case 'po.received':
        return event.sourceId ? `/inventory/purchase-orders/${event.sourceId}` : undefined;
      
      case 'inventory.low_stock':
      case 'inventory.out_of_stock':
        return '/inventory/products';

      case 'inventory.adjustment.pending':
      case 'inventory.adjustment.approved':
      case 'inventory.adjustment.rejected':
        return '/inventory/stock/adjustments';

      case 'cash.audit.alert':
      case 'cash.discrepancy.detected':
      case 'cash_audit.discrepancy_detected':
        return '/admin/cash-audit';

      case 'invoice.approved':
      case 'invoice.payment_made':
        return event.sourceId ? `/finance/invoices/${event.sourceId}` : '/finance/invoices';
      
      default:
        return undefined;
    }
  }

  /**
   * Merge delivery methods from template defaults and user preferences
   */
  private mergeDeliveryMethods(templateMethods: string[], userMethods: string[]): string[] {
    // User preferences override template defaults
    return userMethods.length > 0 ? userMethods : templateMethods;
  }

  /**
   * Convert delivery methods array to config object
   */
  private convertDeliveryMethods(methods: string[]): NotificationDeliveryConfig {
    return {
      inApp: methods.includes('IN_APP'),
      toast: methods.includes('TOAST'),
      email: methods.includes('EMAIL'),
      sms: methods.includes('SMS'),
      push: methods.includes('PUSH'),
    };
  }

  /**
   * Create notifications in database
   */
  private async createNotifications(notifications: NotificationData[]): Promise<void> {
    try {
      const notificationData = notifications.map(notification => ({
        userId: notification.userId,
        title: notification.title,
        message: notification.message,
        type: notification.type as any,
        priority: notification.priority as any,
        actionUrl: notification.actionUrl,
        metadata: notification.metadata,
        eventType: notification.eventType,
        eventId: notification.eventId,
        deliveryMethods: notification.deliveryMethods, // This is already a JSON object
        expiresAt: notification.expiresAt,
      }));

      console.log(`Creating ${notifications.length} notifications in database...`);
      console.log('Sample notification data:', JSON.stringify(notificationData[0], null, 2));

      await prisma.notification.createMany({
        data: notificationData,
      });

      console.log(`✅ Successfully created ${notifications.length} notifications in database`);

      // Log the created notifications for debugging
      for (const notification of notifications) {
        console.log(`📧 Created notification for user ${notification.userId}: ${notification.title}`);
        if (notification.deliveryMethods?.inApp) {
          console.log(`  📱 In-App notification created`);
        }
        if (notification.deliveryMethods?.toast) {
          console.log(`  🍞 Toast notification will be shown`);
        }
        if (notification.deliveryMethods?.email) {
          console.log(`  📧 Email notification will be sent`);
        }
      }
    } catch (error) {
      console.error('❌ Error creating notifications in database:', error);
      console.error('Error details:', error.message);
      throw error;
    }
  }

  /**
   * Deliver notifications through various channels
   */
  private async deliverNotifications(notifications: NotificationData[]): Promise<void> {
    // This is where you'd implement actual delivery logic
    // For now, we'll just log the delivery attempts
    
    for (const notification of notifications) {
      if (notification.deliveryMethods?.toast) {
        console.log(`Would deliver toast notification to user ${notification.userId}: ${notification.title}`);
      }
      
      if (notification.deliveryMethods?.email) {
        console.log(`Would deliver email notification to user ${notification.userId}: ${notification.title}`);
      }
      
      if (notification.deliveryMethods?.sms) {
        console.log(`Would deliver SMS notification to user ${notification.userId}: ${notification.title}`);
      }
      
      if (notification.deliveryMethods?.push) {
        console.log(`Would deliver push notification to user ${notification.userId}: ${notification.title}`);
      }
    }
  }
}

export const notificationEngine = new NotificationEngine();
