/**
 * Purchase Order Integration Example
 * 
 * This file demonstrates how to integrate the new notification system
 * with existing Purchase Order workflows.
 */

import { 
  notifyPOStatusChanged,
  notify<PERSON>Approved,
  notifyPORejected,
  notifyPOOverdue,
} from '@/lib/notifications';

/**
 * Enhanced PO status change notification
 * This replaces the existing notification logic in po-status-management.ts
 */
export async function notifyPOStatusChange(
  purchaseOrderId: string,
  fromStatus: string,
  toStatus: string,
  actionUserId: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  try {
    console.log(`Notifying PO status change: ${purchaseOrderId} from ${fromStatus} to ${toStatus}`);

    // Emit the general status change event
    await notifyPOStatusChanged(purchaseOrderId, fromStatus, toStatus, {
      actionUserId,
      timestamp: new Date().toISOString(),
      ...metadata,
    });

    // Emit specific events based on the new status
    switch (toStatus) {
      case 'APPROVED':
        await notifyPOApproved(purchaseOrderId, actionUserId, {
          fromStatus,
          approvedAt: new Date().toISOString(),
          ...metadata,
        });
        break;

      case 'REJECTED':
        await notifyPORejected(
          purchaseOrderId, 
          actionUserId, 
          metadata.rejectionReason || 'No reason provided',
          {
            fromStatus,
            rejectedAt: new Date().toISOString(),
            ...metadata,
          }
        );
        break;

      case 'OVERDUE':
        const daysOverdue = metadata.daysOverdue || 0;
        await notifyPOOverdue(purchaseOrderId, daysOverdue, {
          fromStatus,
          overdueDetectedAt: new Date().toISOString(),
          ...metadata,
        });
        break;

      // Add more specific status notifications as needed
      default:
        // The general status change notification is sufficient
        break;
    }

    console.log(`PO status change notifications sent successfully for ${purchaseOrderId}`);
  } catch (error) {
    console.error(`Error sending PO status change notifications for ${purchaseOrderId}:`, error);
    // Don't throw here - notification failures shouldn't break the main workflow
  }
}

/**
 * Migration helper to replace existing notification calls
 * This function can be used to gradually migrate from the old system
 */
export async function migratePONotification(
  purchaseOrderId: string,
  notificationType: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  try {
    switch (notificationType) {
      case 'PURCHASE_ORDER_APPROVAL':
        // This would be triggered when PO is submitted for approval
        await notifyPOStatusChanged(
          purchaseOrderId,
          metadata.fromStatus || 'DRAFT',
          'PENDING_APPROVAL',
          metadata
        );
        break;

      case 'PURCHASE_ORDER_APPROVED':
        await notifyPOApproved(
          purchaseOrderId,
          metadata.approvedBy || 'system',
          metadata
        );
        break;

      case 'PURCHASE_ORDER_REJECTED':
        await notifyPORejected(
          purchaseOrderId,
          metadata.rejectedBy || 'system',
          metadata.rejectionReason || 'No reason provided',
          metadata
        );
        break;

      case 'PO_OVERDUE':
        await notifyPOOverdue(
          purchaseOrderId,
          metadata.daysOverdue || 0,
          metadata
        );
        break;

      default:
        console.warn(`Unknown notification type for migration: ${notificationType}`);
        break;
    }
  } catch (error) {
    console.error(`Error migrating PO notification ${notificationType} for ${purchaseOrderId}:`, error);
  }
}

/**
 * Example of how to integrate with existing PO workflow
 * This shows how to modify existing functions to use the new system
 */
export class PONotificationIntegration {
  /**
   * Replace the existing createPOApprovalNotifications function
   */
  static async createApprovalNotifications(
    purchaseOrderId: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    await notifyPOStatusChanged(
      purchaseOrderId,
      'DRAFT',
      'PENDING_APPROVAL',
      metadata
    );
  }

  /**
   * Replace the existing createPOStatusChangeNotifications function
   */
  static async createStatusChangeNotifications(
    purchaseOrderId: string,
    fromStatus: string,
    toStatus: string,
    actionUserId: string,
    metadata: Record<string, any> = {}
  ): Promise<void> {
    await notifyPOStatusChange(
      purchaseOrderId,
      fromStatus,
      toStatus,
      actionUserId,
      metadata
    );
  }

  /**
   * Batch notification for multiple POs (e.g., overdue detection)
   */
  static async createBatchOverdueNotifications(
    overduePOs: Array<{
      id: string;
      daysOverdue: number;
      metadata?: Record<string, any>;
    }>
  ): Promise<void> {
    const promises = overduePOs.map(po =>
      notifyPOOverdue(po.id, po.daysOverdue, po.metadata || {})
    );

    await Promise.allSettled(promises);
    console.log(`Sent overdue notifications for ${overduePOs.length} purchase orders`);
  }
}

/**
 * Utility function to extract PO metadata for notifications
 */
export function extractPOMetadata(purchaseOrder: any): Record<string, any> {
  return {
    poNumber: purchaseOrder.id?.slice(-8)?.toUpperCase() || 'Unknown',
    supplierName: purchaseOrder.supplier?.name || 'Unknown Supplier',
    total: Number(purchaseOrder.total) || 0,
    orderDate: purchaseOrder.orderDate || purchaseOrder.createdAt,
    expectedDeliveryDate: purchaseOrder.expectedDeliveryDate,
    createdBy: purchaseOrder.createdBy?.name || 'Unknown',
    approvedBy: purchaseOrder.approvedBy?.name,
    itemCount: purchaseOrder.items?.length || 0,
    priority: purchaseOrder.priority || 'NORMAL',
  };
}

/**
 * Example of how to use the new system in existing API routes
 */
export async function handlePOStatusUpdate(
  purchaseOrderId: string,
  newStatus: string,
  currentStatus: string,
  userId: string,
  additionalData: Record<string, any> = {}
): Promise<void> {
  try {
    // Your existing business logic here...
    
    // Then send notifications using the new system
    await notifyPOStatusChange(
      purchaseOrderId,
      currentStatus,
      newStatus,
      userId,
      additionalData
    );
    
    console.log(`PO ${purchaseOrderId} status updated and notifications sent`);
  } catch (error) {
    console.error(`Error handling PO status update for ${purchaseOrderId}:`, error);
    throw error;
  }
}
