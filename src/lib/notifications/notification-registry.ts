/**
 * Notification Registry - Central registration system for notification handlers
 * 
 * This registry allows features to register their notification handlers
 * without modifying core notification code.
 */

import { eventSystem, registerEventHandler, EVENT_TYPES } from '@/lib/events/event-system';
import { notificationEngine } from './notification-engine';

/**
 * Initialize the notification system by registering all handlers
 */
export function initializeNotificationSystem(): void {
  console.log('Initializing notification system...');

  // Register the core notification engine handler for all events
  registerNotificationEngineHandlers();

  // Register feature-specific handlers
  registerPurchaseOrderHandlers();
  registerInventoryHandlers();
  registerCashAuditHandlers();
  registerFinancialHandlers();
  registerRevenueHandlers();
  registerSystemHandlers();

  console.log('Notification system initialized successfully');
}

/**
 * Register the core notification engine to handle all events
 */
function registerNotificationEngineHandlers(): void {
  // Register a handler for each event type that should generate notifications
  const eventTypes = Object.values(EVENT_TYPES);
  
  eventTypes.forEach(eventType => {
    registerEventHandler({
      eventType,
      handler: async (event) => {
        await notificationEngine.processEvent(event);
      },
      priority: 10, // Lower priority so other handlers can run first
    });
  });

  console.log(`Registered notification engine handlers for ${eventTypes.length} event types`);
}

/**
 * Register Purchase Order specific notification handlers
 */
function registerPurchaseOrderHandlers(): void {
  // PO Status Changed Handler
  registerEventHandler({
    eventType: EVENT_TYPES.PO_STATUS_CHANGED,
    handler: async (event) => {
      console.log(`PO Status Changed: ${event.sourceId} -> ${event.payload.newStatus}`);
      
      // Additional PO-specific logic can go here
      // For example, updating related records, triggering workflows, etc.
    },
    priority: 5,
  });

  // PO Approved Handler
  registerEventHandler({
    eventType: EVENT_TYPES.PO_APPROVED,
    handler: async (event) => {
      console.log(`PO Approved: ${event.sourceId}`);
      
      // Could trigger automatic ordering, supplier notifications, etc.
    },
    priority: 5,
  });

  // PO Overdue Handler
  registerEventHandler({
    eventType: EVENT_TYPES.PO_OVERDUE,
    handler: async (event) => {
      console.log(`PO Overdue: ${event.sourceId}`);
      
      // Could trigger escalation workflows, supplier follow-ups, etc.
    },
    priority: 5,
  });

  console.log('Registered Purchase Order notification handlers');
}

/**
 * Register Inventory specific notification handlers
 */
function registerInventoryHandlers(): void {
  // Low Stock Handler
  registerEventHandler({
    eventType: EVENT_TYPES.INVENTORY_LOW_STOCK,
    handler: async (event) => {
      console.log(`Low Stock Alert: ${event.payload.productName} (${event.payload.currentStock})`);
      
      // Could trigger automatic reorder suggestions, supplier notifications, etc.
    },
    priority: 5,
  });

  // Out of Stock Handler
  registerEventHandler({
    eventType: EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
    handler: async (event) => {
      console.log(`Out of Stock Alert: ${event.payload.productName}`);
      
      // Could trigger urgent reorder processes, customer notifications, etc.
    },
    priority: 5,
  });

  // Batch Expiring Handler
  registerEventHandler({
    eventType: EVENT_TYPES.BATCH_EXPIRING,
    handler: async (event) => {
      console.log(`Batch Expiring: ${event.payload.batchNumber} expires ${event.payload.expiryDate}`);
      
      // Could trigger discount campaigns, supplier return processes, etc.
    },
    priority: 5,
  });

  console.log('Registered Inventory notification handlers');
}

/**
 * Register Cash Audit specific notification handlers
 */
function registerCashAuditHandlers(): void {
  // Cash Audit Alert Handler
  registerEventHandler({
    eventType: EVENT_TYPES.CASH_AUDIT_ALERT,
    handler: async (event) => {
      console.log(`Cash Audit Alert: ${event.payload.alertType} - ${event.payload.message}`);
      
      // Could trigger investigation workflows, manager notifications, etc.
    },
    priority: 5,
  });

  console.log('Registered Cash Audit notification handlers');
}

/**
 * Register Financial specific notification handlers
 */
function registerFinancialHandlers(): void {
  // Cash Reconciliation Discrepancy Handler
  registerEventHandler({
    eventType: EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
    handler: async (event) => {
      console.log(`Cash Reconciliation Discrepancy: ${event.payload.discrepancyAmountFormatted} by ${event.payload.cashierName}`);

      // Could trigger investigation workflows, audit alerts, etc.
    },
    priority: 5,
  });

  // Invoice Approved Handler
  registerEventHandler({
    eventType: EVENT_TYPES.INVOICE_APPROVED,
    handler: async (event) => {
      console.log(`Invoice Approved: ${event.payload.invoiceNumber} - ${event.payload.totalAmountFormatted}`);

      // Could trigger payment workflows, supplier notifications, etc.
    },
    priority: 5,
  });

  // Invoice Payment Made Handler
  registerEventHandler({
    eventType: EVENT_TYPES.INVOICE_PAYMENT_MADE,
    handler: async (event) => {
      console.log(`Invoice Payment Made: ${event.payload.paymentAmountFormatted} for ${event.payload.invoiceNumber}`);

      // Could trigger payment confirmations, accounting updates, etc.
    },
    priority: 5,
  });

  console.log('Registered Financial notification handlers');
}

/**
 * Register Revenue specific notification handlers
 */
function registerRevenueHandlers(): void {
  // Revenue Target Achieved Handler
  registerEventHandler({
    eventType: EVENT_TYPES.REVENUE_TARGET_ACHIEVED,
    handler: async (event) => {
      console.log(`Revenue Target Achieved: ${event.payload.targetType} - ${event.payload.amount}`);
      
      // Could trigger celebration workflows, bonus calculations, etc.
    },
    priority: 5,
  });

  // Revenue Target Missed Handler
  registerEventHandler({
    eventType: EVENT_TYPES.REVENUE_TARGET_MISSED,
    handler: async (event) => {
      console.log(`Revenue Target Missed: ${event.payload.targetType} - ${event.payload.shortfall}`);
      
      // Could trigger performance review workflows, strategy adjustments, etc.
    },
    priority: 5,
  });

  console.log('Registered Revenue notification handlers');
}

/**
 * Register System specific notification handlers
 */
function registerSystemHandlers(): void {
  // System Maintenance Handler
  registerEventHandler({
    eventType: EVENT_TYPES.SYSTEM_MAINTENANCE,
    handler: async (event) => {
      console.log(`System Maintenance: ${event.payload.maintenanceType} - ${event.payload.scheduledTime}`);
      
      // Could trigger user session warnings, backup processes, etc.
    },
    priority: 5,
  });

  // User Action Required Handler
  registerEventHandler({
    eventType: EVENT_TYPES.USER_ACTION_REQUIRED,
    handler: async (event) => {
      console.log(`User Action Required: ${event.payload.actionType} for user ${event.payload.userId}`);
      
      // Could trigger reminder workflows, escalation processes, etc.
    },
    priority: 5,
  });

  console.log('Registered System notification handlers');
}

/**
 * Register a custom notification handler for a specific feature
 * This is the main API that features should use to register their handlers
 */
export function registerFeatureNotificationHandler(
  eventType: string,
  handler: (event: any) => Promise<void>,
  priority: number = 50
): void {
  registerEventHandler({
    eventType,
    handler,
    priority,
  });

  console.log(`Registered custom notification handler for event type: ${eventType}`);
}

/**
 * Get all registered event types
 */
export function getRegisteredEventTypes(): string[] {
  return eventSystem.getRegisteredEventTypes();
}

/**
 * Get handlers for a specific event type
 */
export function getEventHandlers(eventType: string) {
  return eventSystem.getHandlers(eventType);
}

/**
 * Process any unprocessed events (for recovery/startup)
 */
export async function processUnprocessedEvents(): Promise<void> {
  await eventSystem.processUnprocessedEvents();
}

// Export event types for convenience
export { EVENT_TYPES } from '@/lib/events/event-system';
