/**
 * Preference Manager - Manages user notification preferences
 * 
 * This manager handles:
 * - User preference creation and updates
 * - Default preference initialization
 * - Preference validation
 * - Bulk preference operations
 */

import { prisma } from '@/lib/prisma';
import { EVENT_TYPES } from '@/lib/events/event-system';

export interface NotificationPreference {
  userId: string;
  eventType: string;
  enabled: boolean;
  deliveryMethods: string[];
  frequency: 'IMMEDIATE' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'NEVER';
  quietHours?: {
    enabled: boolean;
    startTime: string; // HH:MM format
    endTime: string;   // HH:MM format
    timezone: string;
  };
}

export interface UserPreferencesProfile {
  userId: string;
  preferences: NotificationPreference[];
  globalSettings: {
    enabled: boolean;
    defaultDeliveryMethods: string[];
    quietHours?: {
      enabled: boolean;
      startTime: string;
      endTime: string;
      timezone: string;
    };
  };
}

class PreferenceManager {
  /**
   * Initialize default preferences for a user
   */
  async initializeUserPreferences(userId: string): Promise<void> {
    console.log(`Initializing default preferences for user: ${userId}`);

    // Get user role for role-based filtering
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      console.error(`User not found: ${userId}`);
      return;
    }

    const defaultPreferences = this.getDefaultPreferences(userId, user.role);

    for (const preference of defaultPreferences) {
      await this.upsertPreference(preference);
    }

    console.log(`Initialized ${defaultPreferences.length} default preferences for user: ${userId} (${user.role})`);
  }

  /**
   * Get default preferences for all event types (with role-based filtering)
   */
  private getDefaultPreferences(userId: string, userRole: string): NotificationPreference[] {
    const eventTypes = Object.values(EVENT_TYPES);

    // Filter event types based on user role
    const allowedEventTypes = eventTypes.filter(eventType =>
      this.isEventTypeAllowedForRole(eventType, userRole)
    );

    return allowedEventTypes.map(eventType => ({
      userId,
      eventType,
      enabled: this.getDefaultEnabledState(eventType),
      deliveryMethods: this.getDefaultDeliveryMethods(eventType),
      frequency: this.getDefaultFrequency(eventType),
      quietHours: {
        enabled: false,
        startTime: '22:00',
        endTime: '08:00',
        timezone: 'UTC',
      },
    }));
  }

  /**
   * Check if an event type is allowed for a specific user role
   */
  private isEventTypeAllowedForRole(eventType: string, userRole: string): boolean {
    // Financial events are only available to SUPER_ADMIN and FINANCE_ADMIN
    const financialEvents = [
      EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
      EVENT_TYPES.INVOICE_APPROVED,
      EVENT_TYPES.INVOICE_PAYMENT_MADE,
      EVENT_TYPES.CASH_AUDIT_ALERT,
      EVENT_TYPES.CASH_DISCREPANCY_DETECTED,
    ];

    if (financialEvents.includes(eventType as any)) {
      return ['SUPER_ADMIN', 'FINANCE_ADMIN'].includes(userRole);
    }

    // Purchase Order events - exclude CASHIER
    const poEvents = [
      EVENT_TYPES.PO_STATUS_CHANGED,
      EVENT_TYPES.PO_CREATED,
      EVENT_TYPES.PO_APPROVED,
      EVENT_TYPES.PO_REJECTED,
      EVENT_TYPES.PO_RECEIVED,
      EVENT_TYPES.PO_OVERDUE,
    ];

    if (poEvents.includes(eventType as any)) {
      return !['CASHIER'].includes(userRole);
    }

    // Inventory events - exclude CASHIER
    const inventoryEvents = [
      EVENT_TYPES.INVENTORY_LOW_STOCK,
      EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
      EVENT_TYPES.BATCH_EXPIRING,
      EVENT_TYPES.BATCH_EXPIRED,
      EVENT_TYPES.STOCK_ADJUSTMENT_PENDING,
      EVENT_TYPES.STOCK_ADJUSTMENT_APPROVED,
      EVENT_TYPES.STOCK_ADJUSTMENT_REJECTED,
    ];

    if (inventoryEvents.includes(eventType as any)) {
      return !['CASHIER'].includes(userRole);
    }

    // System events are available to all roles
    const systemEvents = [
      EVENT_TYPES.SYSTEM_MAINTENANCE,
      EVENT_TYPES.USER_ACTION_REQUIRED,
      EVENT_TYPES.USER_LOGIN,
      EVENT_TYPES.USER_LOGOUT,
      EVENT_TYPES.USER_CREATED,
    ];

    if (systemEvents.includes(eventType as any)) {
      return true;
    }

    // Revenue events - exclude CASHIER
    const revenueEvents = [
      EVENT_TYPES.REVENUE_TARGET_ACHIEVED,
      EVENT_TYPES.REVENUE_TARGET_MISSED,
    ];

    if (revenueEvents.includes(eventType as any)) {
      return !['CASHIER'].includes(userRole);
    }

    // Default: allow all other events for non-CASHIER roles
    return !['CASHIER'].includes(userRole);
  }

  /**
   * Get default enabled state for an event type
   */
  private getDefaultEnabledState(eventType: string): boolean {
    // High priority events are enabled by default
    const highPriorityEvents = [
      EVENT_TYPES.PO_APPROVED,
      EVENT_TYPES.PO_REJECTED,
      EVENT_TYPES.PO_OVERDUE,
      EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
      EVENT_TYPES.CASH_AUDIT_ALERT,
      EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
      EVENT_TYPES.INVOICE_APPROVED,
      EVENT_TYPES.INVOICE_PAYMENT_MADE,
      EVENT_TYPES.SYSTEM_MAINTENANCE,
    ];

    return highPriorityEvents.includes(eventType as any);
  }

  /**
   * Get default delivery methods for an event type
   */
  private getDefaultDeliveryMethods(eventType: string): string[] {
    // Critical events get more delivery methods
    const criticalEvents = [
      EVENT_TYPES.PO_OVERDUE,
      EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
      EVENT_TYPES.CASH_AUDIT_ALERT,
      EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
      EVENT_TYPES.SYSTEM_MAINTENANCE,
    ];

    if (criticalEvents.includes(eventType as any)) {
      return ['IN_APP', 'TOAST', 'EMAIL'];
    }

    // Important events get in-app and toast
    const importantEvents = [
      EVENT_TYPES.PO_APPROVED,
      EVENT_TYPES.PO_REJECTED,
      EVENT_TYPES.INVENTORY_LOW_STOCK,
      EVENT_TYPES.INVOICE_APPROVED,
      EVENT_TYPES.INVOICE_PAYMENT_MADE,
      EVENT_TYPES.REVENUE_TARGET_ACHIEVED,
    ];

    if (importantEvents.includes(eventType as any)) {
      return ['IN_APP', 'TOAST'];
    }

    // Default to in-app only
    return ['IN_APP'];
  }

  /**
   * Get default frequency for an event type
   */
  private getDefaultFrequency(eventType: string): 'IMMEDIATE' | 'HOURLY' | 'DAILY' | 'WEEKLY' | 'NEVER' {
    // Most events should be immediate
    const immediateEvents = [
      EVENT_TYPES.PO_APPROVED,
      EVENT_TYPES.PO_REJECTED,
      EVENT_TYPES.PO_OVERDUE,
      EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
      EVENT_TYPES.CASH_AUDIT_ALERT,
      EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
      EVENT_TYPES.INVOICE_APPROVED,
      EVENT_TYPES.INVOICE_PAYMENT_MADE,
      EVENT_TYPES.SYSTEM_MAINTENANCE,
    ];

    if (immediateEvents.includes(eventType as any)) {
      return 'IMMEDIATE';
    }

    // Some events can be batched
    const batchableEvents = [
      EVENT_TYPES.INVENTORY_LOW_STOCK,
      EVENT_TYPES.BATCH_EXPIRING,
    ];

    if (batchableEvents.includes(eventType as any)) {
      return 'DAILY';
    }

    return 'IMMEDIATE';
  }

  /**
   * Create or update a preference
   */
  async upsertPreference(preference: NotificationPreference): Promise<void> {
    try {
      await prisma.notificationPreference.upsert({
        where: {
          userId_eventType: {
            userId: preference.userId,
            eventType: preference.eventType,
          },
        },
        update: {
          enabled: preference.enabled,
          deliveryMethods: preference.deliveryMethods,
          frequency: preference.frequency,
          quietHours: preference.quietHours,
        },
        create: {
          userId: preference.userId,
          eventType: preference.eventType,
          enabled: preference.enabled,
          deliveryMethods: preference.deliveryMethods,
          frequency: preference.frequency,
          quietHours: preference.quietHours,
        },
      });

      console.log(`Preference upserted for user ${preference.userId}, event ${preference.eventType}`);
    } catch (error) {
      console.error(`Error upserting preference for user ${preference.userId}, event ${preference.eventType}:`, error);
      throw error;
    }
  }

  /**
   * Get user preferences for a specific event type
   */
  async getUserPreference(userId: string, eventType: string): Promise<NotificationPreference | null> {
    try {
      const preference = await prisma.notificationPreference.findUnique({
        where: {
          userId_eventType: {
            userId,
            eventType,
          },
        },
      });

      if (!preference) {
        return null;
      }

      return {
        userId: preference.userId,
        eventType: preference.eventType,
        enabled: preference.enabled,
        deliveryMethods: preference.deliveryMethods as string[],
        frequency: preference.frequency as any,
        quietHours: preference.quietHours as any,
      };
    } catch (error) {
      console.error(`Error fetching preference for user ${userId}, event ${eventType}:`, error);
      return null;
    }
  }

  /**
   * Get all preferences for a user (with role-based filtering)
   */
  async getUserPreferences(userId: string): Promise<UserPreferencesProfile> {
    try {
      // Get user role for filtering
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { role: true },
      });

      if (!user) {
        console.error(`User not found: ${userId}`);
        return {
          userId,
          preferences: [],
          globalSettings: {
            enabled: true,
            defaultDeliveryMethods: ['IN_APP'],
          },
        };
      }

      const preferences = await prisma.notificationPreference.findMany({
        where: { userId },
        orderBy: { eventType: 'asc' },
      });

      // Get all allowed event types for this user's role
      const allEventTypes = Object.values(EVENT_TYPES);
      const allowedEventTypes = allEventTypes.filter(eventType =>
        this.isEventTypeAllowedForRole(eventType, user.role)
      );

      // Create a map of existing preferences
      const existingPreferencesMap = new Map(
        preferences.map(p => [p.eventType, p])
      );

      // Ensure all allowed event types have preferences (create defaults for missing ones)
      const mappedPreferences: NotificationPreference[] = allowedEventTypes.map(eventType => {
        const existingPreference = existingPreferencesMap.get(eventType);

        if (existingPreference) {
          // Use existing preference
          return {
            userId: existingPreference.userId,
            eventType: existingPreference.eventType,
            enabled: existingPreference.enabled,
            deliveryMethods: existingPreference.deliveryMethods as string[],
            frequency: existingPreference.frequency as any,
            quietHours: existingPreference.quietHours as any,
          };
        } else {
          // Create default preference for missing event type
          return {
            userId,
            eventType,
            enabled: this.getDefaultEnabledState(eventType),
            deliveryMethods: this.getDefaultDeliveryMethods(eventType),
            frequency: this.getDefaultFrequency(eventType),
            quietHours: {
              enabled: false,
              startTime: '22:00',
              endTime: '08:00',
              timezone: 'UTC',
            },
          };
        }
      });

      // Calculate global settings based on preferences
      const enabledCount = mappedPreferences.filter(p => p.enabled).length;
      const globalEnabled = enabledCount > 0;

      // Get most common delivery methods
      const allDeliveryMethods = mappedPreferences
        .filter(p => p.enabled)
        .flatMap(p => p.deliveryMethods);
      
      const methodCounts = allDeliveryMethods.reduce((acc, method) => {
        acc[method] = (acc[method] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const defaultDeliveryMethods = Object.entries(methodCounts)
        .filter(([_, count]) => count >= enabledCount / 2) // Methods used by at least half of enabled preferences
        .map(([method, _]) => method);

      return {
        userId,
        preferences: mappedPreferences,
        globalSettings: {
          enabled: globalEnabled,
          defaultDeliveryMethods: defaultDeliveryMethods.length > 0 ? defaultDeliveryMethods : ['IN_APP'],
        },
      };
    } catch (error) {
      console.error(`Error fetching preferences for user ${userId}:`, error);
      return {
        userId,
        preferences: [],
        globalSettings: {
          enabled: true,
          defaultDeliveryMethods: ['IN_APP'],
        },
      };
    }
  }

  /**
   * Update multiple preferences for a user
   */
  async updateUserPreferences(userId: string, preferences: Partial<NotificationPreference>[]): Promise<void> {
    try {
      const updatePromises = preferences.map(preference => {
        if (!preference.eventType) {
          throw new Error('Event type is required for preference update');
        }

        return this.upsertPreference({
          userId,
          eventType: preference.eventType,
          enabled: preference.enabled ?? true,
          deliveryMethods: preference.deliveryMethods ?? ['IN_APP'],
          frequency: preference.frequency ?? 'IMMEDIATE',
          quietHours: preference.quietHours,
        });
      });

      await Promise.all(updatePromises);
      console.log(`Updated ${preferences.length} preferences for user ${userId}`);
    } catch (error) {
      console.error(`Error updating preferences for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Disable all notifications for a user
   */
  async disableAllNotifications(userId: string): Promise<void> {
    try {
      await prisma.notificationPreference.updateMany({
        where: { userId },
        data: { enabled: false },
      });

      console.log(`Disabled all notifications for user ${userId}`);
    } catch (error) {
      console.error(`Error disabling notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Enable all notifications for a user
   */
  async enableAllNotifications(userId: string): Promise<void> {
    try {
      await prisma.notificationPreference.updateMany({
        where: { userId },
        data: { enabled: true },
      });

      console.log(`Enabled all notifications for user ${userId}`);
    } catch (error) {
      console.error(`Error enabling notifications for user ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Check if user should receive notification based on preferences and quiet hours
   */
  async shouldReceiveNotification(
    userId: string,
    eventType: string,
    currentTime: Date = new Date()
  ): Promise<boolean> {
    try {
      const preference = await this.getUserPreference(userId, eventType);
      
      if (!preference || !preference.enabled) {
        return false;
      }

      // Check quiet hours
      if (preference.quietHours?.enabled) {
        const isInQuietHours = this.isInQuietHours(currentTime, preference.quietHours);
        if (isInQuietHours) {
          // Only allow urgent notifications during quiet hours
          const urgentEvents = [
            EVENT_TYPES.CASH_AUDIT_ALERT,
            EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
            EVENT_TYPES.SYSTEM_MAINTENANCE,
          ];
          
          return urgentEvents.includes(eventType as any);
        }
      }

      return true;
    } catch (error) {
      console.error(`Error checking notification permission for user ${userId}, event ${eventType}:`, error);
      return false; // Fail safe - don't send notification if we can't check preferences
    }
  }

  /**
   * Check if current time is within quiet hours
   */
  private isInQuietHours(currentTime: Date, quietHours: any): boolean {
    try {
      const currentHour = currentTime.getHours();
      const currentMinute = currentTime.getMinutes();
      const currentTimeMinutes = currentHour * 60 + currentMinute;

      const [startHour, startMinute] = quietHours.startTime.split(':').map(Number);
      const [endHour, endMinute] = quietHours.endTime.split(':').map(Number);
      
      const startTimeMinutes = startHour * 60 + startMinute;
      const endTimeMinutes = endHour * 60 + endMinute;

      // Handle overnight quiet hours (e.g., 22:00 to 08:00)
      if (startTimeMinutes > endTimeMinutes) {
        return currentTimeMinutes >= startTimeMinutes || currentTimeMinutes <= endTimeMinutes;
      } else {
        return currentTimeMinutes >= startTimeMinutes && currentTimeMinutes <= endTimeMinutes;
      }
    } catch (error) {
      console.error('Error checking quiet hours:', error);
      return false;
    }
  }

  /**
   * Get users who should receive notifications for an event type
   */
  async getUsersForEventType(eventType: string): Promise<string[]> {
    try {
      const preferences = await prisma.notificationPreference.findMany({
        where: {
          eventType,
          enabled: true,
        },
        select: { userId: true },
      });

      return preferences.map(p => p.userId);
    } catch (error) {
      console.error(`Error fetching users for event type ${eventType}:`, error);
      return [];
    }
  }

  /**
   * Initialize preferences for all existing users
   */
  async initializeAllUserPreferences(): Promise<void> {
    try {
      const users = await prisma.user.findMany({
        where: { active: true },
        select: { id: true },
      });

      console.log(`Initializing preferences for ${users.length} users`);

      for (const user of users) {
        await this.initializeUserPreferences(user.id);
      }

      console.log('Completed preference initialization for all users');
    } catch (error) {
      console.error('Error initializing preferences for all users:', error);
      throw error;
    }
  }
}

export const preferenceManager = new PreferenceManager();
