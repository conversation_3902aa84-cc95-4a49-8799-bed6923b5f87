import { notify } from './index';
import { EVENT_TYPES } from '../events/event-system';

/**
 * Financial notification functions
 * These are re-exported to work around module caching issues
 */

export async function notifyInvoiceApproved(
  invoiceId: string,
  invoiceNumber: string,
  supplierName: string,
  totalAmount: number,
  approverName: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  // Format total amount in IDR
  const totalAmountFormatted = new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(totalAmount);

  await notify({
    eventType: EVENT_TYPES.INVOICE_APPROVED,
    sourceId: invoiceId,
    sourceType: 'invoice',
    payload: {
      invoiceId,
      invoiceNumber,
      supplierName,
      totalAmount,
      totalAmountFormatted,
      approverName,
      ...metadata,
    },
  });

  console.log(`✅ Invoice approval notification sent for ${invoiceNumber} - ${totalAmountFormatted}`);
}

export async function notifyInvoicePaymentMade(
  invoiceId: string,
  invoiceNumber: string,
  paymentAmount: number,
  paymentMethod: string,
  remainingBalance?: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  // Format payment amount in IDR
  const paymentAmountFormatted = new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(paymentAmount);

  // Format remaining balance if provided
  const remainingBalanceFormatted = remainingBalance !== undefined ? 
    new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(remainingBalance) : undefined;

  await notify({
    eventType: EVENT_TYPES.INVOICE_PAYMENT_MADE,
    sourceId: invoiceId,
    sourceType: 'invoice',
    payload: {
      invoiceId,
      invoiceNumber,
      paymentAmount,
      paymentAmountFormatted,
      paymentMethod,
      remainingBalance,
      remainingBalanceFormatted,
      ...metadata,
    },
  });

  console.log(`✅ Invoice payment notification sent for ${invoiceNumber} - ${paymentAmountFormatted}`);
}

export async function notifyCashReconciliationDiscrepancy(
  reconciliationId: string,
  cashierName: string,
  cashierRole: string,
  businessDate: string,
  discrepancyAmount: number,
  discrepancyCategory: string,
  drawerLocation: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  // Format discrepancy amount in IDR
  const discrepancyAmountFormatted = new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(discrepancyAmount);

  await notify({
    eventType: EVENT_TYPES.CASH_RECONCILIATION_DISCREPANCY,
    sourceId: reconciliationId,
    sourceType: 'cash_reconciliation',
    payload: {
      reconciliationId,
      cashierName,
      cashierRole,
      businessDate,
      discrepancyAmount,
      discrepancyAmountFormatted,
      discrepancyCategory,
      drawerLocation,
      ...metadata,
    },
  });

  console.log(`✅ Cash reconciliation discrepancy notification sent for ${discrepancyAmountFormatted} by ${cashierName}`);
}
