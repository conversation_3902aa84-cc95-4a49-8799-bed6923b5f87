import { format, parseISO, startOfHour, startOfDay, startOfWeek, startOfMonth } from 'date-fns';
import {
  SalesTrendData,
  ProductPerformanceData,
  PaymentMethodData,
  HourlyPatternData,
  CashierPerformanceData,
  CategoryPerformanceData,
  DrawerSessionData,
  TransactionVolumeData,
  RevenueSummary,
} from '@/lib/types/analytics';

// Transform raw transaction data to sales trends
export function transformToSalesTrends(transactions: any[]): SalesTrendData[] {
  console.log("[DataTransformer] transformToSalesTrends called with", transactions.length, "transactions");

  const dailyData = new Map<string, { revenue: number; count: number; total: number }>();

  transactions.forEach((transaction, index) => {
    try {
      if (transaction.status === 'VOIDED') return;

      console.log(`[DataTransformer] Processing transaction ${index}:`, {
        id: transaction.id,
        createdAt: transaction.createdAt,
        createdAtType: typeof transaction.createdAt,
        total: transaction.total,
        status: transaction.status
      });

      // Handle both Date objects and ISO strings
      let dateObj: Date;
      if (transaction.createdAt instanceof Date) {
        dateObj = transaction.createdAt;
      } else if (typeof transaction.createdAt === 'string') {
        dateObj = parseISO(transaction.createdAt);
      } else {
        console.error(`[DataTransformer] Invalid createdAt format for transaction ${index}:`, transaction.createdAt);
        return; // Skip this transaction
      }

      const date = format(dateObj, 'yyyy-MM-dd');
      const revenue = Number(transaction.total);

      if (!dailyData.has(date)) {
        dailyData.set(date, { revenue: 0, count: 0, total: 0 });
      }

      const dayData = dailyData.get(date)!;
      dayData.revenue += revenue;
      dayData.count += 1;
      dayData.total += revenue;
    } catch (error) {
      console.error(`[DataTransformer] Error processing transaction ${index}:`, error, transaction);
      throw error;
    }
  });

  const result = Array.from(dailyData.entries())
    .map(([date, data]) => ({
      date,
      value: data.revenue,
      revenue: data.revenue,
      transactions: data.count,
      averageOrderValue: data.count > 0 ? data.revenue / data.count : 0,
    }))
    .sort((a, b) => a.date.localeCompare(b.date));

  console.log("[DataTransformer] Returning", result.length, "data points");
  return result;
}

// Transform raw transaction data to product performance
export function transformToProductPerformance(
  transactionItems: any[],
  products: any[]
): ProductPerformanceData[] {
  console.log("[DataTransformer] Processing", transactionItems.length, "transaction items");
  console.log("[DataTransformer] Sample transaction item:", transactionItems[0]);

  const productMap = new Map(products.map(p => [p.id, p]));
  const productStats = new Map<string, { revenue: number; quantity: number }>();

  transactionItems.forEach((item, index) => {
    if (item.transaction?.status === 'VOIDED') return;

    const productId = item.productId;
    // Use unitPrice from TransactionItem schema, not price
    // Handle Prisma Decimal types properly
    const unitPrice = item.unitPrice ? Number(item.unitPrice.toString()) : Number(item.price || 0);
    const quantity = item.quantity ? Number(item.quantity.toString()) : 0;
    const revenue = unitPrice * quantity;

    if (index < 3) {
      console.log(`[DataTransformer] Item ${index}:`, {
        productId,
        unitPrice: item.unitPrice,
        unitPriceType: typeof item.unitPrice,
        unitPriceNumber: Number(item.unitPrice),
        price: item.price,
        quantity: item.quantity,
        quantityType: typeof item.quantity,
        quantityNumber: Number(item.quantity),
        calculatedRevenue: revenue,
        revenueType: typeof revenue
      });
    }

    if (!productStats.has(productId)) {
      productStats.set(productId, { revenue: 0, quantity: 0 });
    }

    const stats = productStats.get(productId)!;
    stats.revenue += revenue;
    stats.quantity += quantity;
  });

  console.log("[DataTransformer] Product stats map size:", productStats.size);

  const result = Array.from(productStats.entries())
    .map(([productId, stats]) => {
      const product = productMap.get(productId);
      if (!product) {
        console.log("[DataTransformer] Product not found for ID:", productId);
        return null;
      }

      const costPrice = Number(product.costPrice || 0);
      const profit = stats.revenue - (costPrice * stats.quantity);

      return {
        id: productId,
        name: product.name,
        revenue: stats.revenue,
        quantity: stats.quantity,
        category: product.category?.name || 'Uncategorized',
        profit,
      };
    })
    .filter(Boolean)
    .sort((a, b) => b!.revenue - a!.revenue) as ProductPerformanceData[];

  console.log("[DataTransformer] Final result:", result.slice(0, 3));
  return result;
}

// Transform raw transaction data to payment method distribution
export function transformToPaymentMethods(transactions: any[]): PaymentMethodData[] {
  const paymentStats = new Map<string, { amount: number; count: number }>();
  let totalAmount = 0;

  transactions.forEach(transaction => {
    if (transaction.status === 'VOIDED') return;

    const method = transaction.paymentMethod;
    const amount = Number(transaction.total);

    if (!paymentStats.has(method)) {
      paymentStats.set(method, { amount: 0, count: 0 });
    }

    const stats = paymentStats.get(method)!;
    stats.amount += amount;
    stats.count += 1;
    totalAmount += amount;
  });

  return Array.from(paymentStats.entries()).map(([method, stats]) => ({
    method,
    amount: stats.amount,
    count: stats.count,
    percentage: totalAmount > 0 ? (stats.amount / totalAmount) * 100 : 0,
  }));
}

// Transform raw transaction data to hourly patterns
export function transformToHourlyPatterns(transactions: any[]): HourlyPatternData[] {
  const hourlyStats = new Map<number, { revenue: number; count: number }>();

  // Initialize all hours
  for (let hour = 0; hour < 24; hour++) {
    hourlyStats.set(hour, { revenue: 0, count: 0 });
  }

  transactions.forEach(transaction => {
    if (transaction.status === 'VOIDED') return;

    // Handle both Date objects and ISO strings
    let dateObj: Date;
    if (transaction.createdAt instanceof Date) {
      dateObj = transaction.createdAt;
    } else if (typeof transaction.createdAt === 'string') {
      dateObj = parseISO(transaction.createdAt);
    } else {
      console.error('[DataTransformer] Invalid createdAt format in hourly patterns:', transaction.createdAt);
      return; // Skip this transaction
    }

    const hour = dateObj.getHours();
    const revenue = Number(transaction.total);

    const stats = hourlyStats.get(hour)!;
    stats.revenue += revenue;
    stats.count += 1;
  });

  return Array.from(hourlyStats.entries()).map(([hour, stats]) => ({
    hour,
    revenue: stats.revenue,
    transactions: stats.count,
    averageOrderValue: stats.count > 0 ? stats.revenue / stats.count : 0,
  }));
}

// Transform raw transaction data to cashier performance
export function transformToCashierPerformance(
  transactions: any[],
  users: any[]
): CashierPerformanceData[] {
  const userMap = new Map(users.map(u => [u.id, u]));
  const cashierStats = new Map<string, { revenue: number; count: number }>();

  transactions.forEach(transaction => {
    if (transaction.status === 'VOIDED') return;

    const cashierId = transaction.cashierId;
    const revenue = Number(transaction.total);

    if (!cashierStats.has(cashierId)) {
      cashierStats.set(cashierId, { revenue: 0, count: 0 });
    }

    const stats = cashierStats.get(cashierId)!;
    stats.revenue += revenue;
    stats.count += 1;
  });

  return Array.from(cashierStats.entries())
    .map(([cashierId, stats]) => {
      const user = userMap.get(cashierId);
      if (!user) return null;

      const averageOrderValue = stats.count > 0 ? stats.revenue / stats.count : 0;
      const efficiency = calculateCashierEfficiency(stats.count, stats.revenue);

      return {
        id: cashierId,
        name: user.name,
        revenue: stats.revenue,
        transactions: stats.count,
        averageOrderValue,
        efficiency,
      };
    })
    .filter(Boolean)
    .sort((a, b) => b!.revenue - a!.revenue) as CashierPerformanceData[];
}

// Transform raw transaction data to category performance
export function transformToCategoryPerformance(
  transactionItems: any[],
  categories: any[]
): CategoryPerformanceData[] {
  const categoryMap = new Map(categories.map(c => [c.id, c]));
  const categoryStats = new Map<string, { revenue: number; quantity: number }>();
  let totalRevenue = 0;

  transactionItems.forEach(item => {
    if (item.transaction?.status === 'VOIDED') return;

    const categoryId = item.product?.categoryId;
    if (!categoryId) return;

    // Use unitPrice from TransactionItem schema, not price
    // Handle Prisma Decimal types properly
    const unitPrice = item.unitPrice ? Number(item.unitPrice.toString()) : Number(item.price || 0);
    const quantity = item.quantity ? Number(item.quantity.toString()) : 0;
    const revenue = unitPrice * quantity;

    if (!categoryStats.has(categoryId)) {
      categoryStats.set(categoryId, { revenue: 0, quantity: 0 });
    }

    const stats = categoryStats.get(categoryId)!;
    stats.revenue += revenue;
    stats.quantity += quantity;
    totalRevenue += revenue;
  });

  return Array.from(categoryStats.entries())
    .map(([categoryId, stats]) => {
      const category = categoryMap.get(categoryId);
      if (!category) return null;

      return {
        id: categoryId,
        name: category.name,
        revenue: stats.revenue,
        quantity: stats.quantity,
        percentage: totalRevenue > 0 ? (stats.revenue / totalRevenue) * 100 : 0,
      };
    })
    .filter(Boolean)
    .sort((a, b) => b!.revenue - a!.revenue) as CategoryPerformanceData[];
}

// Transform raw drawer session data
export function transformToDrawerSessions(drawerSessions: any[]): DrawerSessionData[] {
  return drawerSessions.map(session => ({
    id: session.id,
    cashierName: session.user?.name || 'Unknown',
    openedAt: session.openedAt,
    closedAt: session.closedAt,
    openingBalance: Number(session.openingBalance),
    closingBalance: session.closingBalance ? Number(session.closingBalance) : undefined,
    expectedClosingBalance: session.expectedClosingBalance ? Number(session.expectedClosingBalance) : undefined,
    discrepancy: session.closingBalance && session.expectedClosingBalance
      ? Number(session.closingBalance) - Number(session.expectedClosingBalance)
      : undefined,
    transactionCount: session._count?.transactions || 0,
  }));
}

// Transform raw transaction data to volume distribution
export function transformToTransactionVolume(transactions: any[]): TransactionVolumeData[] {
  const ranges = [
    { min: 0, max: 50000, label: '< Rp 50K' },
    { min: 50000, max: 100000, label: 'Rp 50K - 100K' },
    { min: 100000, max: 250000, label: 'Rp 100K - 250K' },
    { min: 250000, max: 500000, label: 'Rp 250K - 500K' },
    { min: 500000, max: Infinity, label: '> Rp 500K' },
  ];

  const volumeStats = new Map<string, number>();
  let totalTransactions = 0;

  // Initialize ranges
  ranges.forEach(range => {
    volumeStats.set(range.label, 0);
  });

  transactions.forEach(transaction => {
    if (transaction.status === 'VOIDED') return;

    const amount = Number(transaction.total);
    totalTransactions += 1;

    const range = ranges.find(r => amount >= r.min && amount < r.max);
    if (range) {
      volumeStats.set(range.label, volumeStats.get(range.label)! + 1);
    }
  });

  return Array.from(volumeStats.entries()).map(([range, count]) => ({
    range,
    count,
    percentage: totalTransactions > 0 ? (count / totalTransactions) * 100 : 0,
  }));
}

// Calculate revenue summary
export function calculateRevenueSummary(
  currentTransactions: any[],
  previousTransactions: any[]
): RevenueSummary {
  const current = calculatePeriodStats(currentTransactions);
  const previous = calculatePeriodStats(previousTransactions);

  return {
    totalRevenue: current.revenue,
    totalTransactions: current.count,
    averageOrderValue: current.averageOrderValue,
    activeCustomers: current.uniqueCustomers,
    revenueGrowth: calculateGrowthPercentage(current.revenue, previous.revenue),
    transactionGrowth: calculateGrowthPercentage(current.count, previous.count),
    aovGrowth: calculateGrowthPercentage(current.averageOrderValue, previous.averageOrderValue),
    customerGrowth: calculateGrowthPercentage(current.uniqueCustomers, previous.uniqueCustomers),
  };
}

// Helper function to calculate period statistics
function calculatePeriodStats(transactions: any[]) {
  const validTransactions = transactions.filter(t => t.status !== 'VOIDED');
  const revenue = validTransactions.reduce((sum, t) => sum + Number(t.total), 0);
  const count = validTransactions.length;
  const uniqueCustomers = new Set(validTransactions.map(t => t.customerId).filter(Boolean)).size;

  return {
    revenue,
    count,
    averageOrderValue: count > 0 ? revenue / count : 0,
    uniqueCustomers,
  };
}

// Helper function to calculate growth percentage
function calculateGrowthPercentage(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 100 : 0;
  return ((current - previous) / previous) * 100;
}

// Helper function to calculate cashier efficiency
function calculateCashierEfficiency(transactionCount: number, revenue: number): number {
  // Improved efficiency calculation based on realistic metrics
  // Base score on transaction volume (transactions per day)
  const transactionScore = Math.min((transactionCount / 50) * 40, 40); // Max 40 points for 50+ transactions
  
  // Score based on revenue generation (revenue per transaction)
  const avgOrderValue = transactionCount > 0 ? revenue / transactionCount : 0;
  const revenueScore = Math.min((avgOrderValue / 100000) * 30, 30); // Max 30 points for 100k+ IDR AOV
  
  // Consistency bonus (balanced performance)
  const consistencyBonus = transactionCount > 10 && avgOrderValue > 50000 ? 20 : 10;
  
  // Final efficiency score (0-100)
  const efficiency = Math.round(transactionScore + revenueScore + consistencyBonus);
  return Math.max(10, Math.min(100, efficiency)); // Ensure 10-100 range
}
