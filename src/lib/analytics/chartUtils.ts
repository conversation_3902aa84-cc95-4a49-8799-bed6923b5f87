import { format, parseISO, startOfDay, endOfDay, subDays, subMonths, subYears } from 'date-fns';
import { DateRange, ChartDataPoint, AnalyticsFilters } from '@/lib/types/analytics';

// Color palettes for charts
export const CHART_COLORS = {
  primary: ['#3b82f6', '#1d4ed8', '#1e40af', '#1e3a8a'],
  success: ['#10b981', '#059669', '#047857', '#065f46'],
  warning: ['#f59e0b', '#d97706', '#b45309', '#92400e'],
  danger: ['#ef4444', '#dc2626', '#b91c1c', '#991b1b'],
  info: ['#06b6d4', '#0891b2', '#0e7490', '#155e75'],
  purple: ['#8b5cf6', '#7c3aed', '#6d28d9', '#5b21b6'],
  pink: ['#ec4899', '#db2777', '#be185d', '#9d174d'],
  indigo: ['#6366f1', '#4f46e5', '#4338ca', '#3730a3'],
  mixed: ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4', '#ec4899', '#6366f1'],
};

// Default chart configuration
export const DEFAULT_CHART_CONFIG = {
  height: 300,
  showLegend: true,
  showTooltip: true,
  showGrid: true,
  colors: CHART_COLORS.mixed,
};

// Date range presets
export const DATE_RANGE_PRESETS = {
  '7d': {
    label: '7 Days',
    from: subDays(new Date(), 7),
    to: new Date(),
  },
  '30d': {
    label: '30 Days',
    from: subDays(new Date(), 30),
    to: new Date(),
  },
  '90d': {
    label: '3 Months',
    from: subDays(new Date(), 90),
    to: new Date(),
  },
  '1y': {
    label: '1 Year',
    from: subYears(new Date(), 1),
    to: new Date(),
  },
};

// Format currency values
export function formatCurrency(value: number | null | undefined): string {
  if (value === null || value === undefined || isNaN(value)) {
    return 'Rp 0';
  }
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(value);
}

// Format percentage values
export function formatPercentage(value: number | null | undefined, decimals: number = 1): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0.0%';
  }
  return `${value.toFixed(decimals)}%`;
}

// Format large numbers with K, M, B suffixes
export function formatNumber(value: number | null | undefined): string {
  if (value === null || value === undefined || isNaN(value)) {
    return '0';
  }
  if (value >= 1000000000) {
    return `${(value / 1000000000).toFixed(1)}B`;
  }
  if (value >= 1000000) {
    return `${(value / 1000000).toFixed(1)}M`;
  }
  if (value >= 1000) {
    return `${(value / 1000).toFixed(1)}K`;
  }
  return value.toString();
}

// Format dates for chart labels
export function formatChartDate(date: string | Date, format_type: 'short' | 'medium' | 'long' = 'short'): string {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  switch (format_type) {
    case 'short':
      return format(dateObj, 'MMM dd');
    case 'medium':
      return format(dateObj, 'MMM dd, yyyy');
    case 'long':
      return format(dateObj, 'MMMM dd, yyyy');
    default:
      return format(dateObj, 'MMM dd');
  }
}

// Generate date range for charts
export function generateDateRange(from: Date, to: Date, interval: 'day' | 'week' | 'month' = 'day'): Date[] {
  const dates: Date[] = [];
  const current = new Date(from);
  
  while (current <= to) {
    dates.push(new Date(current));
    
    switch (interval) {
      case 'day':
        current.setDate(current.getDate() + 1);
        break;
      case 'week':
        current.setDate(current.getDate() + 7);
        break;
      case 'month':
        current.setMonth(current.getMonth() + 1);
        break;
    }
  }
  
  return dates;
}

// Calculate growth percentage
export function calculateGrowth(current: number | null | undefined, previous: number | null | undefined): number {
  const currentValue = current ?? 0;
  const previousValue = previous ?? 0;

  if (previousValue === 0) return currentValue > 0 ? 100 : 0;
  return ((currentValue - previousValue) / previousValue) * 100;
}

// Get growth indicator
export function getGrowthIndicator(growth: number | null | undefined): {
  color: string;
  symbol: string;
  text: string;
} {
  const growthValue = growth ?? 0;

  if (growthValue > 0) {
    return {
      color: 'text-green-600',
      symbol: '+',
      text: `+${growthValue.toFixed(1)}%`,
    };
  } else if (growthValue < 0) {
    return {
      color: 'text-red-600',
      symbol: '',
      text: `${growthValue.toFixed(1)}%`,
    };
  } else {
    return {
      color: 'text-muted-foreground',
      symbol: '',
      text: '0.0%',
    };
  }
}

// Transform data for different chart types
export function transformDataForChart(
  data: any[],
  chartType: 'line' | 'bar' | 'pie' | 'donut' | 'area',
  xKey: string,
  yKey: string
): any[] {
  switch (chartType) {
    case 'pie':
    case 'donut':
      return data.map((item, index) => ({
        ...item,
        name: item[xKey],
        value: item[yKey],
        fill: CHART_COLORS.mixed[index % CHART_COLORS.mixed.length],
      }));
    
    default:
      return data.map(item => ({
        ...item,
        [xKey]: typeof item[xKey] === 'string' && item[xKey].includes('T') 
          ? formatChartDate(item[xKey]) 
          : item[xKey],
      }));
  }
}

// Aggregate data by time period
export function aggregateDataByPeriod(
  data: ChartDataPoint[],
  period: 'day' | 'week' | 'month'
): ChartDataPoint[] {
  const aggregated = new Map<string, number>();
  
  data.forEach(item => {
    const date = parseISO(item.date);
    let key: string;
    
    switch (period) {
      case 'day':
        key = format(date, 'yyyy-MM-dd');
        break;
      case 'week':
        key = format(date, 'yyyy-ww');
        break;
      case 'month':
        key = format(date, 'yyyy-MM');
        break;
    }
    
    aggregated.set(key, (aggregated.get(key) || 0) + item.value);
  });
  
  return Array.from(aggregated.entries()).map(([date, value]) => ({
    date,
    value,
  }));
}

// Filter data based on analytics filters
export function filterAnalyticsData<T extends Record<string, any>>(
  data: T[],
  filters: AnalyticsFilters,
  dateField: string = 'date'
): T[] {
  return data.filter(item => {
    // Date range filter
    const itemDate = new Date(item[dateField]);
    if (itemDate < filters.dateRange.from || itemDate > filters.dateRange.to) {
      return false;
    }
    
    // Cashier filter
    if (filters.cashierIds && filters.cashierIds.length > 0) {
      if (!filters.cashierIds.includes(item.cashierId || item.userId)) {
        return false;
      }
    }
    
    // Category filter
    if (filters.categoryIds && filters.categoryIds.length > 0) {
      if (!filters.categoryIds.includes(item.categoryId)) {
        return false;
      }
    }
    
    // Payment method filter
    if (filters.paymentMethods && filters.paymentMethods.length > 0) {
      if (!filters.paymentMethods.includes(item.paymentMethod)) {
        return false;
      }
    }
    
    // Terminal filter
    if (filters.terminalIds && filters.terminalIds.length > 0) {
      if (!filters.terminalIds.includes(item.terminalId)) {
        return false;
      }
    }
    
    return true;
  });
}

// Generate chart filename for exports
export function generateChartFilename(chartTitle: string, format: string): string {
  const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm-ss');
  const sanitizedTitle = chartTitle.toLowerCase().replace(/[^a-z0-9]/g, '_');
  return `${sanitizedTitle}_${timestamp}.${format}`;
}
