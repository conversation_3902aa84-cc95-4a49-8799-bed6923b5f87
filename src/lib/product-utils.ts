/**
 * Helper functions for product-related operations
 */

interface Product {
  basePrice: number;
  discountValue?: number;
  discountType?: string;
  temporaryPrice?: {
    value: number;
    type: string;
  };
}

/**
 * Calculate the final price of a product considering discounts and temporary prices
 *
 * @param product The product object with price and discount information
 * @returns The calculated final price
 */
export function calculateProductPrice(product: Product): number {
  let price = product.basePrice;

  // First check if there's a temporary price (takes precedence over regular discount)
  if (product.temporaryPrice) {
    if (product.temporaryPrice.type === "FIXED") {
      return product.temporaryPrice.value;
    } else {
      // For PERCENTAGE type, calculate the discounted price
      return price - (price * product.temporaryPrice.value) / 100;
    }
  }

  // If no temporary price, check for product's built-in discount
  if (product.discountValue && product.discountType) {
    if (product.discountType === "FIXED") {
      return price - product.discountValue;
    } else if (product.discountType === "PERCENTAGE") {
      return price - (price * product.discountValue) / 100;
    }
  }

  return price;
}

/**
 * Check if a product has any discount (either temporary or permanent)
 *
 * @param product The product object
 * @returns True if the product has any discount
 */
export function hasDiscount(product: Product): boolean {
  return !!(
    product.temporaryPrice ||
    (product.discountValue && product.discountType)
  );
}

/**
 * Calculate the automatic discount amount for a product
 *
 * @param product The product object with price and discount information
 * @returns The automatic discount amount (not the final price)
 */
export function calculateAutoDiscountAmount(product: Product): number {
  // First check if there's a temporary price (takes precedence over regular discount)
  if (product.temporaryPrice) {
    if (product.temporaryPrice.type === "FIXED") {
      return product.basePrice - product.temporaryPrice.value;
    } else {
      // For PERCENTAGE type, calculate the discount amount
      return (product.basePrice * product.temporaryPrice.value) / 100;
    }
  }

  // If no temporary price, check for product's built-in discount
  if (product.discountValue && product.discountType) {
    if (product.discountType === "FIXED") {
      return product.discountValue;
    } else if (product.discountType === "PERCENTAGE") {
      return (product.basePrice * product.discountValue) / 100;
    }
  }

  return 0;
}
