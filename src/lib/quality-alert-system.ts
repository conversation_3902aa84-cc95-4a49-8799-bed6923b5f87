import { prisma } from "@/auth";
import { QualityTrendAnalysisService } from "@/lib/quality-trend-analysis";
import { QualityMonitoringService } from "@/lib/quality-monitoring-service";
import { 
  notifyQualityThresholdBreached, 
  notifySupplierQualityAlert,
  notifyQualityIssueEscalated 
} from "@/lib/notifications";

export interface QualityAlertRule {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  alertType: 'threshold' | 'trend' | 'anomaly' | 'prediction';
  severity: 'low' | 'medium' | 'high' | 'critical';
  conditions: {
    metric: string;
    operator: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
    value: number;
    timeframe?: number; // days
    consecutivePeriods?: number;
  }[];
  actions: {
    type: 'notification' | 'escalation' | 'auto_action';
    target: string;
    parameters: Record<string, any>;
  }[];
  cooldownMinutes: number; // Prevent spam
  lastTriggered?: string;
}

export interface QualityAlertInstance {
  id: string;
  ruleId: string;
  ruleName: string;
  supplierId?: string;
  supplierName?: string;
  productId?: string;
  productName?: string;
  alertType: string;
  severity: string;
  title: string;
  description: string;
  triggeredAt: string;
  resolvedAt?: string;
  status: 'active' | 'acknowledged' | 'resolved' | 'suppressed';
  metadata: Record<string, any>;
  actions: Array<{
    type: string;
    status: 'pending' | 'completed' | 'failed';
    executedAt?: string;
    result?: string;
  }>;
}

/**
 * Quality Alert System
 * Provides real-time monitoring and alerting for quality issues
 */
export class QualityAlertSystem {

  /**
   * Default alert rules for quality monitoring
   */
  private static readonly DEFAULT_ALERT_RULES: Omit<QualityAlertRule, 'id'>[] = [
    {
      name: 'High Return Rate Alert',
      description: 'Alert when supplier return rate exceeds threshold',
      enabled: true,
      alertType: 'threshold',
      severity: 'high',
      conditions: [
        {
          metric: 'return_rate',
          operator: 'gt',
          value: 5, // 5%
          timeframe: 30,
        }
      ],
      actions: [
        {
          type: 'notification',
          target: 'quality_team',
          parameters: { urgency: 'high' }
        }
      ],
      cooldownMinutes: 60,
    },
    {
      name: 'Quality Score Declining Trend',
      description: 'Alert when quality score shows declining trend',
      enabled: true,
      alertType: 'trend',
      severity: 'medium',
      conditions: [
        {
          metric: 'quality_score',
          operator: 'lt',
          value: 70,
          timeframe: 30,
          consecutivePeriods: 2,
        }
      ],
      actions: [
        {
          type: 'notification',
          target: 'quality_team',
          parameters: { urgency: 'medium' }
        }
      ],
      cooldownMinutes: 120,
    },
    {
      name: 'Critical Defect Rate',
      description: 'Alert when defect rate becomes critical',
      enabled: true,
      alertType: 'threshold',
      severity: 'critical',
      conditions: [
        {
          metric: 'defect_rate',
          operator: 'gt',
          value: 10, // 10%
          timeframe: 7,
        }
      ],
      actions: [
        {
          type: 'notification',
          target: 'management',
          parameters: { urgency: 'critical' }
        },
        {
          type: 'escalation',
          target: 'auto_escalate',
          parameters: { level: 2 }
        }
      ],
      cooldownMinutes: 30,
    },
    {
      name: 'High Return Value Impact',
      description: 'Alert when return value exceeds financial threshold',
      enabled: true,
      alertType: 'threshold',
      severity: 'high',
      conditions: [
        {
          metric: 'return_value',
          operator: 'gt',
          value: 2000000, // 2M IDR
          timeframe: 30,
        }
      ],
      actions: [
        {
          type: 'notification',
          target: 'finance_team',
          parameters: { urgency: 'high' }
        }
      ],
      cooldownMinutes: 60,
    },
  ];

  /**
   * Initialize the alert system with default rules
   */
  static async initializeAlertSystem(): Promise<void> {
    console.log('Initializing Quality Alert System...');

    // Check if alert rules already exist
    const existingRules = await prisma.systemSetting.findMany({
      where: {
        key: { startsWith: 'quality_alert_rule_' }
      }
    });

    if (existingRules.length === 0) {
      // Create default alert rules
      for (const rule of this.DEFAULT_ALERT_RULES) {
        const ruleId = `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await prisma.systemSetting.create({
          data: {
            key: `quality_alert_rule_${ruleId}`,
            value: JSON.stringify({ ...rule, id: ruleId }),
            description: `Quality alert rule: ${rule.name}`,
          }
        });
      }
      console.log(`Created ${this.DEFAULT_ALERT_RULES.length} default quality alert rules`);
    }

    console.log('Quality Alert System initialized successfully');
  }

  /**
   * Run quality monitoring and trigger alerts
   */
  static async runQualityMonitoring(): Promise<{
    alertsTriggered: number;
    alertInstances: QualityAlertInstance[];
  }> {
    console.log('Running quality monitoring and alert checks...');

    const alertInstances: QualityAlertInstance[] = [];
    let alertsTriggered = 0;

    try {
      // Get all active alert rules
      const alertRules = await this.getActiveAlertRules();

      // Get all active suppliers
      const suppliers = await prisma.supplier.findMany({
        where: { isActive: true },
        select: { id: true, name: true }
      });

      // Check each supplier against each rule
      for (const supplier of suppliers) {
        for (const rule of alertRules) {
          try {
            const alertInstance = await this.checkSupplierAgainstRule(supplier, rule);
            if (alertInstance) {
              alertInstances.push(alertInstance);
              alertsTriggered++;

              // Execute alert actions
              await this.executeAlertActions(alertInstance, rule);
            }
          } catch (error) {
            console.error(`Error checking supplier ${supplier.id} against rule ${rule.id}:`, error);
          }
        }
      }

      console.log(`Quality monitoring completed: ${alertsTriggered} alerts triggered`);

    } catch (error) {
      console.error('Error in quality monitoring:', error);
    }

    return {
      alertsTriggered,
      alertInstances,
    };
  }

  /**
   * Get all active alert rules
   */
  private static async getActiveAlertRules(): Promise<QualityAlertRule[]> {
    const ruleSettings = await prisma.systemSetting.findMany({
      where: {
        key: { startsWith: 'quality_alert_rule_' }
      }
    });

    const rules: QualityAlertRule[] = [];
    for (const setting of ruleSettings) {
      try {
        const rule = JSON.parse(setting.value) as QualityAlertRule;
        if (rule.enabled) {
          rules.push(rule);
        }
      } catch (error) {
        console.error(`Error parsing alert rule ${setting.key}:`, error);
      }
    }

    return rules;
  }

  /**
   * Check a supplier against a specific alert rule
   */
  private static async checkSupplierAgainstRule(
    supplier: { id: string; name: string },
    rule: QualityAlertRule
  ): Promise<QualityAlertInstance | null> {
    
    // Check cooldown period
    if (rule.lastTriggered) {
      const lastTriggered = new Date(rule.lastTriggered);
      const cooldownEnd = new Date(lastTriggered.getTime() + rule.cooldownMinutes * 60 * 1000);
      if (new Date() < cooldownEnd) {
        return null; // Still in cooldown
      }
    }

    // Check for existing active alerts of the same type for this supplier
    const existingAlert = await prisma.qualityAlert.findFirst({
      where: {
        supplierId: supplier.id,
        alertType: rule.alertType.toUpperCase(),
        status: { in: ['ACTIVE', 'ACKNOWLEDGED'] },
        ruleId: rule.id
      }
    });

    if (existingAlert) {
      return null; // Alert already exists and is active
    }

    // Get supplier metrics for evaluation
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (rule.conditions[0].timeframe || 30));

    const metrics = await QualityTrendAnalysisService.analyzeSupplierTrends(
      supplier.id,
      rule.conditions[0].timeframe || 30
    );

    // Evaluate conditions
    let conditionsMet = true;
    const evaluationResults: any[] = [];

    for (const condition of rule.conditions) {
      const metricValue = this.getMetricValue(metrics, condition.metric);
      const conditionMet = this.evaluateCondition(metricValue, condition);
      
      evaluationResults.push({
        metric: condition.metric,
        value: metricValue,
        condition: condition,
        met: conditionMet,
      });

      if (!conditionMet) {
        conditionsMet = false;
        break;
      }
    }

    if (!conditionsMet) {
      return null; // Conditions not met
    }

    // Create alert instance in database
    const alertData = {
      alertType: rule.alertType.toUpperCase(),
      severity: rule.severity.toUpperCase() as 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL',
      message: this.generateAlertDescription(rule, evaluationResults),
      supplierId: supplier.id,
      productId: null, // Could be enhanced to include specific products
      batchId: null, // Could be enhanced to include specific batches
      threshold: rule.conditions[0]?.value || null,
      currentValue: evaluationResults[0]?.value || null,
      status: 'ACTIVE' as const,
      escalationLevel: 0,
      ruleId: rule.id,
      metadata: {
        ruleName: rule.name,
        evaluationResults,
        metrics: metrics.currentMetrics,
        actions: rule.actions.map(action => ({
          type: action.type,
          status: 'pending',
        })),
      }
    };

    // Save alert to database
    const savedAlert = await prisma.qualityAlert.create({
      data: alertData,
      include: {
        supplier: { select: { id: true, name: true } },
        product: { select: { id: true, name: true, sku: true } },
        batch: { select: { id: true, batchNumber: true } }
      }
    });

    // Create alert instance for return value (backward compatibility)
    const alertInstance: QualityAlertInstance = {
      id: savedAlert.id,
      ruleId: rule.id,
      ruleName: rule.name,
      supplierId: supplier.id,
      supplierName: supplier.name,
      alertType: rule.alertType,
      severity: rule.severity,
      title: `${rule.name}: ${supplier.name}`,
      description: savedAlert.message,
      triggeredAt: savedAlert.triggeredAt.toISOString(),
      status: 'active',
      metadata: savedAlert.metadata as Record<string, any>,
      actions: rule.actions.map(action => ({
        type: action.type,
        status: 'pending',
      })),
    };

    // Update rule's last triggered time
    await this.updateRuleLastTriggered(rule.id);

    return alertInstance;
  }

  /**
   * Get metric value from analysis results
   */
  private static getMetricValue(analysis: any, metric: string): number {
    const metricMap: Record<string, string> = {
      'return_rate': 'returnRate',
      'defect_rate': 'defectRate',
      'quality_score': 'qualityScore',
      'return_value': 'returnValue',
    };

    const mappedMetric = metricMap[metric] || metric;
    return analysis.currentMetrics[mappedMetric] || 0;
  }

  /**
   * Evaluate a condition against a metric value
   */
  private static evaluateCondition(value: number, condition: any): boolean {
    switch (condition.operator) {
      case 'gt': return value > condition.value;
      case 'gte': return value >= condition.value;
      case 'lt': return value < condition.value;
      case 'lte': return value <= condition.value;
      case 'eq': return value === condition.value;
      default: return false;
    }
  }

  /**
   * Generate alert description
   */
  private static generateAlertDescription(rule: QualityAlertRule, evaluationResults: any[]): string {
    const results = evaluationResults
      .filter(r => r.met)
      .map(r => `${r.metric}: ${r.value.toFixed(2)} ${r.condition.operator} ${r.condition.value}`)
      .join(', ');

    return `${rule.description}. Triggered conditions: ${results}`;
  }

  /**
   * Execute alert actions
   */
  private static async executeAlertActions(
    alertInstance: QualityAlertInstance,
    rule: QualityAlertRule
  ): Promise<void> {
    for (const action of rule.actions) {
      try {
        switch (action.type) {
          case 'notification':
            await this.executeNotificationAction(alertInstance, action);
            break;
          case 'escalation':
            await this.executeEscalationAction(alertInstance, action);
            break;
          case 'auto_action':
            await this.executeAutoAction(alertInstance, action);
            break;
        }
      } catch (error) {
        console.error(`Error executing action ${action.type} for alert ${alertInstance.id}:`, error);
      }
    }
  }

  /**
   * Execute notification action
   */
  private static async executeNotificationAction(
    alertInstance: QualityAlertInstance,
    action: any
  ): Promise<void> {
    if (alertInstance.supplierId && alertInstance.supplierName) {
      await notifySupplierQualityAlert(
        alertInstance.supplierId,
        alertInstance.supplierName,
        alertInstance.alertType,
        alertInstance.severity,
        alertInstance.description,
        {
          alertId: alertInstance.id,
          ruleId: alertInstance.ruleId,
          urgency: action.parameters.urgency,
        }
      );
    }
  }

  /**
   * Execute escalation action
   */
  private static async executeEscalationAction(
    alertInstance: QualityAlertInstance,
    action: any
  ): Promise<void> {
    try {
      // Create a quality issue for high/critical alerts
      if (alertInstance.severity === 'high' || alertInstance.severity === 'critical') {
        const qualityIssue = await prisma.qualityIssue.create({
          data: {
            productId: alertInstance.productId || '', // Will need to be enhanced
            supplierId: alertInstance.supplierId!,
            issueType: 'QUALITY_DEGRADATION',
            severity: alertInstance.severity === 'critical' ? 'CRITICAL' : 'HIGH',
            description: `Auto-escalated from quality alert: ${alertInstance.description}`,
            defectCategory: alertInstance.alertType,
            affectedQuantity: 0, // Would need to calculate based on metrics
            reportedBy: 'system', // System-generated
            escalationLevel: action.parameters.level || 1,
            status: 'OPEN'
          }
        });

        // Update the alert to mark escalation
        await prisma.qualityAlert.update({
          where: { id: alertInstance.id },
          data: {
            status: 'ESCALATED',
            escalationLevel: action.parameters.level || 1,
            metadata: {
              ...alertInstance.metadata,
              escalatedToQualityIssue: qualityIssue.id,
              escalatedAt: new Date().toISOString()
            }
          }
        });

        console.log(`Created quality issue ${qualityIssue.id} from escalated alert ${alertInstance.id}`);
      }
    } catch (error) {
      console.error(`Error executing escalation action for alert ${alertInstance.id}:`, error);
    }
  }

  /**
   * Execute auto action
   */
  private static async executeAutoAction(
    alertInstance: QualityAlertInstance,
    action: any
  ): Promise<void> {
    // This would execute automated responses like creating quality issues
    console.log(`Executing auto action for alert ${alertInstance.id}`);
  }

  /**
   * Update rule's last triggered time
   */
  private static async updateRuleLastTriggered(ruleId: string): Promise<void> {
    const setting = await prisma.systemSetting.findFirst({
      where: { key: `quality_alert_rule_${ruleId}` }
    });

    if (setting) {
      const rule = JSON.parse(setting.value);
      rule.lastTriggered = new Date().toISOString();
      
      await prisma.systemSetting.update({
        where: { id: setting.id },
        data: { value: JSON.stringify(rule) }
      });
    }
  }
}
