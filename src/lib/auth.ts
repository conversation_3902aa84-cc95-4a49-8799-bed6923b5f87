import { NextAuthOptions } from "next-auth";
import Credential<PERSON><PERSON>rovider from "next-auth/providers/credentials";
import bcrypt from "bcryptjs";
import { z } from "zod";
import { prisma } from "@/auth";

// Define the login schema for validation
const loginSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(6, { message: "Password must be at least 6 characters" }),
});

export const authOptions: NextAuthOptions = {
  session: { strategy: "jwt" },
  pages: {
    signIn: "/login",
    error: "/login",
  },
  callbacks: {
    async session({ session, token }) {
      if (token.sub && session.user) {
        session.user.id = token.sub;
        session.user.role = token.role as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },
  },
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          console.log("Auth attempt with credentials:", { email: credentials.email });

          // Validate credentials
          const result = loginSchema.safeParse(credentials);
          if (!result.success) {
            console.log("Validation failed:", result.error);
            return null;
          }

          // Find user by email
          console.log("Looking for user with email:", credentials.email);
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
          });

          console.log("User found:", user ? "Yes" : "No");

          if (!user) {
            console.log("No user found with this email");
            return null;
          }

          if (!user.password) {
            console.log("User has no password set");
            return null;
          }

          if (!user.active) {
            console.log("User account is not active");
            return null;
          }

          // Verify password
          console.log("Verifying password...");
          const passwordMatch = await bcrypt.compare(credentials.password, user.password);
          console.log("Password match:", passwordMatch ? "Yes" : "No");

          if (!passwordMatch) {
            console.log("Password does not match");
            return null;
          }

          // Log activity
          console.log("Creating activity log entry");
          await prisma.activityLog.create({
            data: {
              userId: user.id,
              action: "LOGIN",
              details: "User logged in",
            },
          });

          console.log("Login successful, returning user data");
          return {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
          };
        } catch (error) {
          console.error("Auth error:", error);
          console.error("Error details:", {
            name: error.name,
            message: error.message,
            stack: error.stack
          });
          return null;
        }
      },
    }),
  ],
};
