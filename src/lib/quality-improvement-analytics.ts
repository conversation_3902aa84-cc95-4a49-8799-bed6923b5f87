import { prisma } from "@/auth";
import { calculateSupplierQualityMetrics } from "@/lib/supplier-quality-metrics";

export interface QualityImprovementEffectiveness {
  improvementId: string;
  title: string;
  supplierId: string;
  supplierName: string;
  improvementType: string;
  status: string;
  startDate: string;
  completionDate?: string;
  targetMetric?: string;
  beforeMetrics: {
    returnRate: number;
    defectRate: number;
    qualityScore: number;
    returnValue: number;
  };
  afterMetrics: {
    returnRate: number;
    defectRate: number;
    qualityScore: number;
    returnValue: number;
  };
  improvement: {
    returnRateImprovement: number; // Percentage points
    defectRateImprovement: number;
    qualityScoreImprovement: number;
    returnValueReduction: number; // IDR
  };
  effectivenessScore: number; // 0-100
  roi: number; // Return on investment percentage
  recommendations: string[];
}

export interface QualityImprovementDashboard {
  summary: {
    totalImprovements: number;
    activeImprovements: number;
    completedImprovements: number;
    averageEffectivenessScore: number;
    totalROI: number;
    suppliersImproved: number;
  };
  topPerformingImprovements: QualityImprovementEffectiveness[];
  improvementsByType: Array<{
    type: string;
    count: number;
    averageEffectiveness: number;
    averageROI: number;
  }>;
  supplierProgress: Array<{
    supplierId: string;
    supplierName: string;
    activeImprovements: number;
    completedImprovements: number;
    averageEffectiveness: number;
    qualityTrend: 'improving' | 'stable' | 'declining';
    currentQualityScore: number;
  }>;
  timelineAnalysis: Array<{
    month: string;
    improvementsStarted: number;
    improvementsCompleted: number;
    averageEffectiveness: number;
    totalROI: number;
  }>;
  recommendations: Array<{
    type: 'process' | 'supplier' | 'improvement_type';
    priority: 'high' | 'medium' | 'low';
    title: string;
    description: string;
    expectedImpact: string;
  }>;
}

/**
 * Quality Improvement Analytics Service
 * Provides comprehensive analytics and effectiveness tracking for quality improvements
 */
export class QualityImprovementAnalytics {

  /**
   * Calculate effectiveness of a specific quality improvement
   */
  static async calculateImprovementEffectiveness(
    improvementId: string
  ): Promise<QualityImprovementEffectiveness> {
    
    // Get improvement details
    const improvement = await prisma.supplierQualityImprovement.findUnique({
      where: { id: improvementId },
      include: {
        supplier: {
          select: { id: true, name: true }
        }
      }
    });

    if (!improvement) {
      throw new Error('Quality improvement not found');
    }

    // Calculate metrics before improvement (30 days before start date)
    const beforeStartDate = new Date(improvement.startDate);
    beforeStartDate.setDate(beforeStartDate.getDate() - 30);
    
    const beforeMetrics = await calculateSupplierQualityMetrics(
      improvement.supplierId,
      beforeStartDate,
      improvement.startDate
    );

    // Calculate metrics after improvement (30 days after completion or current date)
    const afterStartDate = improvement.actualCompletionDate || new Date();
    const afterEndDate = new Date(afterStartDate);
    afterEndDate.setDate(afterEndDate.getDate() + 30);

    const afterMetrics = await calculateSupplierQualityMetrics(
      improvement.supplierId,
      afterStartDate,
      afterEndDate
    );

    // Calculate improvements
    const returnRateImprovement = beforeMetrics.metrics.returnRate - afterMetrics.metrics.returnRate;
    const defectRateImprovement = beforeMetrics.metrics.defectRate - afterMetrics.metrics.defectRate;
    const qualityScoreImprovement = afterMetrics.metrics.qualityScore - beforeMetrics.metrics.qualityScore;
    const returnValueReduction = beforeMetrics.metrics.returnValue - afterMetrics.metrics.returnValue;

    // Calculate effectiveness score (0-100)
    const effectivenessScore = this.calculateEffectivenessScore({
      returnRateImprovement,
      defectRateImprovement,
      qualityScoreImprovement,
      returnValueReduction,
    });

    // Calculate ROI (simplified)
    const estimatedCost = 1000000; // 1M IDR default - would be configurable
    const roi = returnValueReduction > 0 ? (returnValueReduction / estimatedCost) * 100 : 0;

    // Generate recommendations
    const recommendations = this.generateImprovementRecommendations({
      returnRateImprovement,
      defectRateImprovement,
      qualityScoreImprovement,
      effectivenessScore,
      improvementType: improvement.improvementType,
    });

    return {
      improvementId,
      title: improvement.title,
      supplierId: improvement.supplierId,
      supplierName: improvement.supplier.name,
      improvementType: improvement.improvementType,
      status: improvement.status,
      startDate: improvement.startDate.toISOString(),
      completionDate: improvement.actualCompletionDate?.toISOString(),
      targetMetric: improvement.targetMetric,
      beforeMetrics: {
        returnRate: beforeMetrics.metrics.returnRate,
        defectRate: beforeMetrics.metrics.defectRate,
        qualityScore: beforeMetrics.metrics.qualityScore,
        returnValue: beforeMetrics.metrics.returnValue,
      },
      afterMetrics: {
        returnRate: afterMetrics.metrics.returnRate,
        defectRate: afterMetrics.metrics.defectRate,
        qualityScore: afterMetrics.metrics.qualityScore,
        returnValue: afterMetrics.metrics.returnValue,
      },
      improvement: {
        returnRateImprovement,
        defectRateImprovement,
        qualityScoreImprovement,
        returnValueReduction,
      },
      effectivenessScore,
      roi,
      recommendations,
    };
  }

  /**
   * Generate comprehensive quality improvement dashboard
   */
  static async generateImprovementDashboard(
    timeframeDays: number = 180
  ): Promise<QualityImprovementDashboard> {
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - timeframeDays);

    // Get all improvements in timeframe
    const improvements = await prisma.supplierQualityImprovement.findMany({
      where: {
        startDate: {
          gte: startDate,
          lte: endDate,
        }
      },
      include: {
        supplier: {
          select: { id: true, name: true }
        }
      }
    });

    // Calculate summary metrics
    const summary = {
      totalImprovements: improvements.length,
      activeImprovements: improvements.filter(i => i.status === 'IN_PROGRESS').length,
      completedImprovements: improvements.filter(i => i.status === 'COMPLETED').length,
      averageEffectivenessScore: 0,
      totalROI: 0,
      suppliersImproved: new Set(improvements.map(i => i.supplierId)).size,
    };

    // Calculate effectiveness for completed improvements
    const completedImprovements = improvements.filter(i => i.status === 'COMPLETED');
    const topPerformingImprovements: QualityImprovementEffectiveness[] = [];
    
    let totalEffectiveness = 0;
    let totalROI = 0;

    for (const improvement of completedImprovements.slice(0, 10)) { // Limit for performance
      try {
        const effectiveness = await this.calculateImprovementEffectiveness(improvement.id);
        topPerformingImprovements.push(effectiveness);
        totalEffectiveness += effectiveness.effectivenessScore;
        totalROI += effectiveness.roi;
      } catch (error) {
        console.error(`Error calculating effectiveness for improvement ${improvement.id}:`, error);
      }
    }

    summary.averageEffectivenessScore = completedImprovements.length > 0 
      ? totalEffectiveness / completedImprovements.length 
      : 0;
    summary.totalROI = totalROI;

    // Sort top performing improvements
    topPerformingImprovements.sort((a, b) => b.effectivenessScore - a.effectivenessScore);

    // Analyze improvements by type
    const improvementsByType = this.analyzeImprovementsByType(improvements, topPerformingImprovements);

    // Analyze supplier progress
    const supplierProgress = await this.analyzeSupplierProgress(improvements);

    // Generate timeline analysis
    const timelineAnalysis = this.generateTimelineAnalysis(improvements, startDate, endDate);

    // Generate recommendations
    const recommendations = this.generateDashboardRecommendations(
      summary,
      improvementsByType,
      supplierProgress
    );

    return {
      summary,
      topPerformingImprovements: topPerformingImprovements.slice(0, 5),
      improvementsByType,
      supplierProgress,
      timelineAnalysis,
      recommendations,
    };
  }

  /**
   * Calculate effectiveness score based on improvements
   */
  private static calculateEffectivenessScore(improvements: {
    returnRateImprovement: number;
    defectRateImprovement: number;
    qualityScoreImprovement: number;
    returnValueReduction: number;
  }): number {
    let score = 50; // Base score

    // Return rate improvement (up to 25 points)
    score += Math.min(improvements.returnRateImprovement * 5, 25);

    // Defect rate improvement (up to 20 points)
    score += Math.min(improvements.defectRateImprovement * 10, 20);

    // Quality score improvement (up to 25 points)
    score += Math.min(improvements.qualityScoreImprovement, 25);

    // Return value reduction (up to 30 points)
    if (improvements.returnValueReduction > 0) {
      score += Math.min((improvements.returnValueReduction / 1000000) * 10, 30);
    }

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  /**
   * Generate recommendations for improvement effectiveness
   */
  private static generateImprovementRecommendations(data: {
    returnRateImprovement: number;
    defectRateImprovement: number;
    qualityScoreImprovement: number;
    effectivenessScore: number;
    improvementType: string;
  }): string[] {
    const recommendations: string[] = [];

    if (data.effectivenessScore < 50) {
      recommendations.push('Consider reviewing improvement approach - effectiveness below target');
    }

    if (data.returnRateImprovement < 1) {
      recommendations.push('Focus on return rate reduction strategies');
    }

    if (data.defectRateImprovement < 0.5) {
      recommendations.push('Implement additional defect prevention measures');
    }

    if (data.qualityScoreImprovement < 5) {
      recommendations.push('Enhance quality control processes for better score improvement');
    }

    if (data.improvementType === 'TRAINING' && data.effectivenessScore > 80) {
      recommendations.push('Training approach is highly effective - consider expanding to other suppliers');
    }

    return recommendations;
  }

  /**
   * Analyze improvements by type
   */
  private static analyzeImprovementsByType(
    improvements: any[],
    effectivenessData: QualityImprovementEffectiveness[]
  ): any[] {
    const typeMap = new Map<string, {
      count: number;
      effectiveness: number[];
      roi: number[];
    }>();

    // Count improvements by type
    for (const improvement of improvements) {
      if (!typeMap.has(improvement.improvementType)) {
        typeMap.set(improvement.improvementType, {
          count: 0,
          effectiveness: [],
          roi: [],
        });
      }
      typeMap.get(improvement.improvementType)!.count++;
    }

    // Add effectiveness data
    for (const effectiveness of effectivenessData) {
      const typeData = typeMap.get(effectiveness.improvementType);
      if (typeData) {
        typeData.effectiveness.push(effectiveness.effectivenessScore);
        typeData.roi.push(effectiveness.roi);
      }
    }

    // Convert to array with averages
    return Array.from(typeMap.entries()).map(([type, data]) => ({
      type,
      count: data.count,
      averageEffectiveness: data.effectiveness.length > 0 
        ? data.effectiveness.reduce((sum, val) => sum + val, 0) / data.effectiveness.length 
        : 0,
      averageROI: data.roi.length > 0 
        ? data.roi.reduce((sum, val) => sum + val, 0) / data.roi.length 
        : 0,
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Analyze supplier progress
   */
  private static async analyzeSupplierProgress(improvements: any[]): Promise<any[]> {
    const supplierMap = new Map<string, {
      supplierName: string;
      active: number;
      completed: number;
    }>();

    for (const improvement of improvements) {
      if (!supplierMap.has(improvement.supplierId)) {
        supplierMap.set(improvement.supplierId, {
          supplierName: improvement.supplier.name,
          active: 0,
          completed: 0,
        });
      }

      const supplierData = supplierMap.get(improvement.supplierId)!;
      if (improvement.status === 'IN_PROGRESS') {
        supplierData.active++;
      } else if (improvement.status === 'COMPLETED') {
        supplierData.completed++;
      }
    }

    // Convert to array with additional metrics
    const supplierProgress = [];
    for (const [supplierId, data] of supplierMap) {
      // Get current quality score (simplified)
      const currentQualityScore = 75 + Math.random() * 20; // Placeholder

      supplierProgress.push({
        supplierId,
        supplierName: data.supplierName,
        activeImprovements: data.active,
        completedImprovements: data.completed,
        averageEffectiveness: 70 + Math.random() * 25, // Placeholder
        qualityTrend: 'improving' as const, // Placeholder
        currentQualityScore,
      });
    }

    return supplierProgress.sort((a, b) => 
      (b.activeImprovements + b.completedImprovements) - (a.activeImprovements + a.completedImprovements)
    );
  }

  /**
   * Generate timeline analysis
   */
  private static generateTimelineAnalysis(
    improvements: any[],
    startDate: Date,
    endDate: Date
  ): any[] {
    const timeline: any[] = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const monthKey = current.toISOString().substring(0, 7); // YYYY-MM
      const monthStart = new Date(current.getFullYear(), current.getMonth(), 1);
      const monthEnd = new Date(current.getFullYear(), current.getMonth() + 1, 0);

      const monthImprovements = improvements.filter(i => {
        const startDate = new Date(i.startDate);
        return startDate >= monthStart && startDate <= monthEnd;
      });

      const completedInMonth = improvements.filter(i => {
        const completionDate = i.actualCompletionDate ? new Date(i.actualCompletionDate) : null;
        return completionDate && completionDate >= monthStart && completionDate <= monthEnd;
      });

      timeline.push({
        month: monthKey,
        improvementsStarted: monthImprovements.length,
        improvementsCompleted: completedInMonth.length,
        averageEffectiveness: 70 + Math.random() * 20, // Placeholder
        totalROI: Math.random() * 100, // Placeholder
      });

      current.setMonth(current.getMonth() + 1);
    }

    return timeline;
  }

  /**
   * Generate dashboard recommendations
   */
  private static generateDashboardRecommendations(
    summary: any,
    improvementsByType: any[],
    supplierProgress: any[]
  ): any[] {
    const recommendations: any[] = [];

    // Check completion rate
    const completionRate = summary.totalImprovements > 0 
      ? (summary.completedImprovements / summary.totalImprovements) * 100 
      : 0;

    if (completionRate < 70) {
      recommendations.push({
        type: 'process',
        priority: 'high',
        title: 'Improve completion rate',
        description: `Current completion rate is ${completionRate.toFixed(1)}%. Focus on better project management and resource allocation.`,
        expectedImpact: 'Increase overall improvement effectiveness by 20-30%',
      });
    }

    // Check effectiveness
    if (summary.averageEffectivenessScore < 60) {
      recommendations.push({
        type: 'process',
        priority: 'high',
        title: 'Enhance improvement methodologies',
        description: 'Average effectiveness score is below target. Review and improve quality improvement processes.',
        expectedImpact: 'Increase average effectiveness by 15-25 points',
      });
    }

    // Check supplier engagement
    const suppliersWithMultipleImprovements = supplierProgress.filter(s => 
      s.activeImprovements + s.completedImprovements > 1
    ).length;

    if (suppliersWithMultipleImprovements < supplierProgress.length * 0.3) {
      recommendations.push({
        type: 'supplier',
        priority: 'medium',
        title: 'Increase supplier engagement',
        description: 'Many suppliers have only single improvements. Consider comprehensive supplier development programs.',
        expectedImpact: 'Improve supplier relationships and long-term quality',
      });
    }

    return recommendations;
  }
}
