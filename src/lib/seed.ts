import { hashPassword, prisma } from "@/auth";

async function main() {
  try {
    // Check if there are any users in the database
    const userCount = await prisma.user.count();

    if (userCount === 0) {
      console.log("No users found. Creating initial admin user...");

      // Create admin user
      const hashedPassword = await hashPassword("admin123");

      const admin = await prisma.user.create({
        data: {
          name: "Admin User",
          email: "<EMAIL>",
          password: hashedPassword,
          role: "SUPER_ADMIN",
        },
      });

      console.log(`Created admin user: ${admin.email}`);

      // Create activity log
      await prisma.activityLog.create({
        data: {
          userId: admin.id,
          action: "SYSTEM",
          details: "Initial admin user created during system setup",
        },
      });

      console.log("Initial setup completed successfully!");
    } else {
      console.log("Users already exist in the database. Skipping seed.");
    }
  } catch (error) {
    console.error("Error seeding database:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
