import { prisma } from "@/auth";
import { differenceInDays, subDays, subMonths, format, startOfMonth, endOfMonth } from "date-fns";

export interface DemandForecast {
  productId: string;
  productName: string;
  supplierId: string;
  supplierName: string;
  forecastPeriod: '30days' | '60days' | '90days';
  predictedDemand: number;
  confidenceLevel: number; // 0-100%
  currentStock: number;
  recommendedOrderQuantity: number;
  optimalOrderDate: Date;
  seasonalFactors: {
    month: number;
    monthName: string;
    adjustmentFactor: number; // 1.0 = average, 1.2 = 20% above average
    historicalDemand: number;
  }[];
  trendDirection: 'increasing' | 'stable' | 'decreasing';
  trendStrength: number; // 0-100%
  riskFactors: string[];
  businessInsights: string[];
}

export interface SupplierDemandAnalysis {
  supplierId: string;
  supplierName: string;
  totalProducts: number;
  forecasts: DemandForecast[];
  aggregateMetrics: {
    totalPredictedDemand: number;
    totalRecommendedOrderValue: number;
    averageConfidenceLevel: number;
    highRiskProducts: number;
    seasonalProducts: number;
  };
  recommendations: {
    priority: 'high' | 'medium' | 'low';
    action: string;
    impact: string;
    timeframe: string;
  }[];
}

/**
 * Advanced Demand Forecasting Engine
 * Uses historical consumption patterns, seasonal trends, and supplier-specific data
 */
export class DemandForecastingEngine {

  /**
   * Generate demand forecasts for all products from a specific supplier
   */
  static async generateSupplierDemandForecast(
    supplierId: string,
    forecastPeriod: '30days' | '60days' | '90days' = '60days'
  ): Promise<SupplierDemandAnalysis> {
    
    // Get supplier information and products
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      include: {
        productSuppliers: {
          where: { isActive: true },
          include: {
            product: {
              include: {
                storeStock: true,
                warehouseStock: true,
                transactionItems: {
                  where: {
                    transaction: {
                      status: { not: 'VOIDED' },
                      createdAt: {
                        gte: subMonths(new Date(), 12) // Last 12 months for seasonal analysis
                      }
                    }
                  },
                  include: {
                    transaction: {
                      select: {
                        createdAt: true,
                        status: true
                      }
                    }
                  }
                },
                stockBatches: {
                  where: {
                    productSupplier: {
                      supplierId: supplierId
                    }
                  },
                  orderBy: { receivedDate: 'desc' },
                  take: 10 // Recent batches for trend analysis
                }
              }
            }
          }
        }
      }
    });

    if (!supplier) {
      throw new Error("Supplier not found");
    }

    const forecasts: DemandForecast[] = [];
    
    // Generate forecasts for each product
    for (const productSupplier of supplier.productSuppliers) {
      const forecast = await this.generateProductDemandForecast(
        productSupplier.product,
        supplier,
        forecastPeriod
      );
      forecasts.push(forecast);
    }

    // Calculate aggregate metrics
    const aggregateMetrics = this.calculateAggregateMetrics(forecasts);
    
    // Generate supplier-level recommendations
    const recommendations = this.generateSupplierRecommendations(forecasts, supplier);

    return {
      supplierId: supplier.id,
      supplierName: supplier.name,
      totalProducts: forecasts.length,
      forecasts: forecasts.sort((a, b) => b.predictedDemand - a.predictedDemand),
      aggregateMetrics,
      recommendations
    };
  }

  /**
   * Generate demand forecast for a specific product
   */
  private static async generateProductDemandForecast(
    product: any,
    supplier: any,
    forecastPeriod: '30days' | '60days' | '90days'
  ): Promise<DemandForecast> {
    
    const forecastDays = forecastPeriod === '30days' ? 30 : 
                        forecastPeriod === '60days' ? 60 : 90;
    
    // Calculate historical consumption patterns
    const consumptionAnalysis = this.analyzeConsumptionPatterns(product.transactionItems);
    
    // Calculate seasonal factors
    const seasonalFactors = this.calculateSeasonalFactors(product.transactionItems);
    
    // Calculate trend direction and strength
    const trendAnalysis = this.analyzeTrend(product.transactionItems, product.stockBatches);
    
    // Calculate current stock
    const currentStock = Number(product.storeStock?.quantity || 0) + 
                        Number(product.warehouseStock?.quantity || 0);
    
    // Generate base demand prediction
    let baseDemand = consumptionAnalysis.averageDailyConsumption * forecastDays;
    
    // Apply seasonal adjustments
    const currentMonth = new Date().getMonth() + 1;
    const seasonalFactor = seasonalFactors.find(sf => sf.month === currentMonth)?.adjustmentFactor || 1.0;
    baseDemand *= seasonalFactor;
    
    // Apply trend adjustments
    if (trendAnalysis.direction === 'increasing') {
      baseDemand *= (1 + (trendAnalysis.strength / 100) * 0.5);
    } else if (trendAnalysis.direction === 'decreasing') {
      baseDemand *= (1 - (trendAnalysis.strength / 100) * 0.3);
    }
    
    const predictedDemand = Math.round(baseDemand);
    
    // Calculate confidence level
    const confidenceLevel = this.calculateConfidenceLevel(
      consumptionAnalysis,
      seasonalFactors,
      trendAnalysis
    );
    
    // Calculate recommended order quantity and timing
    const { recommendedOrderQuantity, optimalOrderDate } = this.calculateOrderRecommendations(
      predictedDemand,
      currentStock,
      consumptionAnalysis.averageDailyConsumption,
      supplier
    );
    
    // Generate risk factors and insights
    const riskFactors = this.identifyRiskFactors(
      product,
      consumptionAnalysis,
      trendAnalysis,
      currentStock,
      predictedDemand
    );
    
    const businessInsights = this.generateBusinessInsights(
      product,
      consumptionAnalysis,
      seasonalFactors,
      trendAnalysis
    );

    return {
      productId: product.id,
      productName: product.name,
      supplierId: supplier.id,
      supplierName: supplier.name,
      forecastPeriod,
      predictedDemand,
      confidenceLevel,
      currentStock,
      recommendedOrderQuantity,
      optimalOrderDate,
      seasonalFactors,
      trendDirection: trendAnalysis.direction,
      trendStrength: trendAnalysis.strength,
      riskFactors,
      businessInsights
    };
  }

  /**
   * Analyze historical consumption patterns
   */
  private static analyzeConsumptionPatterns(transactionItems: any[]) {
    if (transactionItems.length === 0) {
      return {
        averageDailyConsumption: 0,
        totalConsumption: 0,
        consumptionVariability: 0,
        dataQuality: 'poor'
      };
    }

    const totalQuantity = transactionItems.reduce((sum, item) => sum + Number(item.quantity), 0);
    const oldestTransaction = new Date(Math.min(...transactionItems.map(item => 
      new Date(item.transaction.createdAt).getTime()
    )));
    const newestTransaction = new Date(Math.max(...transactionItems.map(item => 
      new Date(item.transaction.createdAt).getTime()
    )));
    
    const daysCovered = Math.max(1, differenceInDays(newestTransaction, oldestTransaction));
    const averageDailyConsumption = totalQuantity / daysCovered;
    
    // Calculate variability (coefficient of variation)
    const dailyConsumption = this.groupConsumptionByDay(transactionItems);
    const variance = this.calculateVariance(dailyConsumption, averageDailyConsumption);
    const consumptionVariability = averageDailyConsumption > 0 ? 
      Math.sqrt(variance) / averageDailyConsumption : 0;
    
    const dataQuality = transactionItems.length >= 30 ? 'good' : 
                       transactionItems.length >= 10 ? 'fair' : 'poor';

    return {
      averageDailyConsumption,
      totalConsumption: totalQuantity,
      consumptionVariability,
      dataQuality
    };
  }

  /**
   * Calculate seasonal adjustment factors
   */
  private static calculateSeasonalFactors(transactionItems: any[]) {
    const monthlyConsumption = new Map<number, number>();
    
    // Initialize all months
    for (let month = 1; month <= 12; month++) {
      monthlyConsumption.set(month, 0);
    }
    
    // Group consumption by month
    transactionItems.forEach(item => {
      const month = new Date(item.transaction.createdAt).getMonth() + 1;
      const current = monthlyConsumption.get(month) || 0;
      monthlyConsumption.set(month, current + Number(item.quantity));
    });
    
    // Calculate average monthly consumption
    const totalConsumption = Array.from(monthlyConsumption.values()).reduce((sum, val) => sum + val, 0);
    const averageMonthlyConsumption = totalConsumption / 12;
    
    // Calculate seasonal factors
    const seasonalFactors = [];
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    for (let month = 1; month <= 12; month++) {
      const monthlyDemand = monthlyConsumption.get(month) || 0;
      const adjustmentFactor = averageMonthlyConsumption > 0 ? 
        monthlyDemand / averageMonthlyConsumption : 1.0;
      
      seasonalFactors.push({
        month,
        monthName: monthNames[month - 1],
        adjustmentFactor: Math.max(0.1, Math.min(3.0, adjustmentFactor)), // Cap between 0.1 and 3.0
        historicalDemand: monthlyDemand
      });
    }
    
    return seasonalFactors;
  }

  /**
   * Analyze demand trend direction and strength
   */
  private static analyzeTrend(transactionItems: any[], stockBatches: any[]) {
    if (transactionItems.length < 6) {
      return { direction: 'stable' as const, strength: 0 };
    }

    // Group consumption by month for trend analysis
    const monthlyData = new Map<string, number>();
    
    transactionItems.forEach(item => {
      const monthKey = format(new Date(item.transaction.createdAt), 'yyyy-MM');
      const current = monthlyData.get(monthKey) || 0;
      monthlyData.set(monthKey, current + Number(item.quantity));
    });
    
    const sortedMonths = Array.from(monthlyData.entries())
      .sort(([a], [b]) => a.localeCompare(b));
    
    if (sortedMonths.length < 3) {
      return { direction: 'stable' as const, strength: 0 };
    }
    
    // Calculate linear trend
    const values = sortedMonths.map(([, value]) => value);
    const trend = this.calculateLinearTrend(values);
    
    let direction: 'increasing' | 'stable' | 'decreasing';
    let strength: number;
    
    if (Math.abs(trend.slope) < 0.1) {
      direction = 'stable';
      strength = 0;
    } else if (trend.slope > 0) {
      direction = 'increasing';
      strength = Math.min(100, Math.abs(trend.slope) * 100);
    } else {
      direction = 'decreasing';
      strength = Math.min(100, Math.abs(trend.slope) * 100);
    }
    
    return { direction, strength };
  }

  /**
   * Calculate confidence level for the forecast
   */
  private static calculateConfidenceLevel(
    consumptionAnalysis: any,
    seasonalFactors: any[],
    trendAnalysis: any
  ): number {
    let confidence = 50; // Base confidence

    // Data quality impact based on actual data characteristics
    const dataPoints = consumptionAnalysis.totalTransactions || 0;
    const timeSpanDays = consumptionAnalysis.timeSpanDays || 0;

    // Sample size impact
    if (dataPoints >= 50 && timeSpanDays >= 90) confidence += 30; // Excellent data
    else if (dataPoints >= 20 && timeSpanDays >= 60) confidence += 20; // Good data
    else if (dataPoints >= 10 && timeSpanDays >= 30) confidence += 10; // Fair data
    else confidence -= 20; // Limited data

    // Variability impact (lower variability = higher confidence)
    if (consumptionAnalysis.consumptionVariability < 0.3) confidence += 15;
    else if (consumptionAnalysis.consumptionVariability > 1.0) confidence -= 15;

    // Seasonal pattern strength (stronger patterns = higher confidence)
    if (seasonalFactors.length >= 4) {
      const seasonalVariance = this.calculateSeasonalVariance(seasonalFactors);
      if (seasonalVariance > 0.2) confidence += 10; // Strong seasonal pattern
      else if (seasonalVariance < 0.05) confidence -= 5; // Weak seasonal pattern
    }

    // Trend consistency (stable trends = higher confidence)
    if (trendAnalysis.trendStrength) {
      if (trendAnalysis.trendStrength > 0.7) confidence += 10; // Strong trend
      else if (trendAnalysis.trendStrength < 0.3) confidence -= 5; // Weak trend
    }

    return Math.max(10, Math.min(95, Math.round(confidence)));
  }

  /**
   * Calculate seasonal variance to measure pattern strength
   */
  private static calculateSeasonalVariance(seasonalFactors: any[]): number {
    if (seasonalFactors.length < 2) return 0;

    const factors = seasonalFactors.map(sf => sf.factor || 1);
    const mean = factors.reduce((sum, f) => sum + f, 0) / factors.length;
    const variance = factors.reduce((sum, f) => sum + Math.pow(f - mean, 2), 0) / factors.length;

    return Math.sqrt(variance) / mean; // Coefficient of variation
  }

  /**
   * Calculate order recommendations
   */
  private static calculateOrderRecommendations(
    predictedDemand: number,
    currentStock: number,
    averageDailyConsumption: number,
    supplier: any
  ) {
    // Safety stock (15 days worth)
    const safetyStock = Math.ceil(averageDailyConsumption * 15);

    // Lead time buffer (assume 7 days default lead time)
    const leadTimeBuffer = Math.ceil(averageDailyConsumption * 7);

    // Calculate recommended order quantity
    const totalNeeded = predictedDemand + safetyStock + leadTimeBuffer;
    const recommendedOrderQuantity = Math.max(0, totalNeeded - currentStock);

    // Calculate optimal order date (when stock will reach reorder point)
    const reorderPoint = safetyStock + leadTimeBuffer;
    const daysUntilReorder = averageDailyConsumption > 0 ?
      Math.max(0, (currentStock - reorderPoint) / averageDailyConsumption) : 0;

    const optimalOrderDate = new Date();
    optimalOrderDate.setDate(optimalOrderDate.getDate() + Math.floor(daysUntilReorder));

    return { recommendedOrderQuantity, optimalOrderDate };
  }

  /**
   * Identify risk factors
   */
  private static identifyRiskFactors(
    product: any,
    consumptionAnalysis: any,
    trendAnalysis: any,
    currentStock: number,
    predictedDemand: number
  ): string[] {
    const risks: string[] = [];

    if (consumptionAnalysis.dataQuality === 'poor') {
      risks.push('Limited historical data - forecast reliability may be low');
    }

    if (consumptionAnalysis.consumptionVariability > 1.0) {
      risks.push('High demand variability - consider increasing safety stock');
    }

    if (trendAnalysis.direction === 'increasing' && trendAnalysis.strength > 50) {
      risks.push('Strong upward trend - may need to increase order quantities');
    }

    if (trendAnalysis.direction === 'decreasing' && trendAnalysis.strength > 30) {
      risks.push('Declining demand trend - review product lifecycle');
    }

    const stockoutRisk = currentStock / (predictedDemand || 1);
    if (stockoutRisk < 0.5) {
      risks.push('High stockout risk - immediate reorder recommended');
    }

    // Check for expiry risks
    if (product.stockBatches?.some((batch: any) =>
      batch.expiryDate && differenceInDays(batch.expiryDate, new Date()) < 30
    )) {
      risks.push('Expiring inventory - balance new orders with current stock');
    }

    return risks;
  }

  /**
   * Generate business insights
   */
  private static generateBusinessInsights(
    product: any,
    consumptionAnalysis: any,
    seasonalFactors: any[],
    trendAnalysis: any
  ): string[] {
    const insights: string[] = [];

    // Seasonal insights
    const highSeasonMonths = seasonalFactors
      .filter(sf => sf.adjustmentFactor > 1.2)
      .map(sf => sf.monthName);

    if (highSeasonMonths.length > 0) {
      insights.push(`Peak demand months: ${highSeasonMonths.join(', ')}`);
    }

    const lowSeasonMonths = seasonalFactors
      .filter(sf => sf.adjustmentFactor < 0.8)
      .map(sf => sf.monthName);

    if (lowSeasonMonths.length > 0) {
      insights.push(`Low demand months: ${lowSeasonMonths.join(', ')}`);
    }

    // Trend insights
    if (trendAnalysis.direction === 'increasing' && trendAnalysis.strength > 30) {
      insights.push('Growing product - consider negotiating volume discounts');
    }

    if (trendAnalysis.direction === 'stable' && consumptionAnalysis.consumptionVariability < 0.3) {
      insights.push('Stable, predictable demand - ideal for just-in-time ordering');
    }

    // Consumption pattern insights
    if (consumptionAnalysis.averageDailyConsumption > 10) {
      insights.push('High-velocity product - monitor stock levels closely');
    }

    return insights;
  }

  /**
   * Helper method to group consumption by day
   */
  private static groupConsumptionByDay(transactionItems: any[]): number[] {
    const dailyConsumption = new Map<string, number>();

    transactionItems.forEach(item => {
      const day = format(new Date(item.transaction.createdAt), 'yyyy-MM-dd');
      const current = dailyConsumption.get(day) || 0;
      dailyConsumption.set(day, current + Number(item.quantity));
    });

    return Array.from(dailyConsumption.values());
  }

  /**
   * Calculate variance for consumption data
   */
  private static calculateVariance(values: number[], mean: number): number {
    if (values.length === 0) return 0;

    const squaredDifferences = values.map(value => Math.pow(value - mean, 2));
    return squaredDifferences.reduce((sum, sq) => sum + sq, 0) / values.length;
  }

  /**
   * Calculate seasonal variability
   */
  private static calculateSeasonalVariability(seasonalFactors: any[]): number {
    const factors = seasonalFactors.map(sf => sf.adjustmentFactor);
    const mean = factors.reduce((sum, f) => sum + f, 0) / factors.length;
    const variance = this.calculateVariance(factors, mean);
    return Math.sqrt(variance);
  }

  /**
   * Calculate linear trend from time series data
   */
  private static calculateLinearTrend(values: number[]) {
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * values[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const intercept = (sumY - slope * sumX) / n;

    return { slope, intercept };
  }

  /**
   * Calculate aggregate metrics for supplier analysis
   */
  private static calculateAggregateMetrics(forecasts: DemandForecast[]) {
    const totalPredictedDemand = forecasts.reduce((sum, f) => sum + f.predictedDemand, 0);
    const totalRecommendedOrderValue = forecasts.reduce((sum, f) => sum + f.recommendedOrderQuantity, 0);
    const averageConfidenceLevel = forecasts.length > 0 ?
      forecasts.reduce((sum, f) => sum + f.confidenceLevel, 0) / forecasts.length : 0;
    const highRiskProducts = forecasts.filter(f => f.riskFactors.length > 2).length;
    const seasonalProducts = forecasts.filter(f =>
      f.seasonalFactors.some(sf => sf.adjustmentFactor > 1.3 || sf.adjustmentFactor < 0.7)
    ).length;

    return {
      totalPredictedDemand,
      totalRecommendedOrderValue,
      averageConfidenceLevel,
      highRiskProducts,
      seasonalProducts
    };
  }

  /**
   * Generate supplier-level recommendations
   */
  private static generateSupplierRecommendations(forecasts: DemandForecast[], supplier: any) {
    const recommendations: any[] = [];

    const highDemandProducts = forecasts.filter(f => f.predictedDemand > f.currentStock * 2);
    if (highDemandProducts.length > 0) {
      recommendations.push({
        priority: 'high' as const,
        action: `Review capacity for ${highDemandProducts.length} high-demand products`,
        impact: 'Prevent stockouts and maintain service levels',
        timeframe: 'Immediate'
      });
    }

    const seasonalProducts = forecasts.filter(f =>
      f.seasonalFactors.some(sf => sf.adjustmentFactor > 1.5)
    );
    if (seasonalProducts.length > 0) {
      recommendations.push({
        priority: 'medium' as const,
        action: `Plan seasonal inventory for ${seasonalProducts.length} products`,
        impact: 'Optimize inventory levels for seasonal demand',
        timeframe: '30-60 days'
      });
    }

    const lowConfidenceProducts = forecasts.filter(f => f.confidenceLevel < 60);
    if (lowConfidenceProducts.length > 0) {
      recommendations.push({
        priority: 'low' as const,
        action: `Improve demand data collection for ${lowConfidenceProducts.length} products`,
        impact: 'Increase forecast accuracy and reduce uncertainty',
        timeframe: '60-90 days'
      });
    }

    return recommendations;
  }
}
