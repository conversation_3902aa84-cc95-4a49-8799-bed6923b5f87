import { prisma } from "@/auth";

export interface QualityMetrics {
  returnRate: number; // Percentage of items returned
  defectRate: number; // Percentage of defective items
  customerSatisfactionScore: number; // Based on return reasons
  qualityTrend: 'improving' | 'declining' | 'stable';
  returnValue: number; // Total value of returns
  totalItemsSold: number;
  totalItemsReturned: number;
  returnsByReason: Array<{
    reason: string;
    count: number;
    percentage: number;
    value: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    returnRate: number;
    returnValue: number;
    returnCount: number;
  }>;
  qualityScore: number; // Overall quality score (0-100)
}

export interface SupplierQualityAnalysis {
  supplierId: string;
  supplierName: string;
  timeRange: string;
  metrics: QualityMetrics;
  recommendations: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

/**
 * Calculate comprehensive quality metrics for a supplier based on return data
 */
export async function calculateSupplierQualityMetrics(
  supplierId: string,
  startDate?: Date,
  endDate?: Date
): Promise<SupplierQualityAnalysis> {
  const dateFilter: any = {};
  if (startDate) dateFilter.gte = startDate;
  if (endDate) dateFilter.lte = endDate;

  // Get supplier information
  const supplier = await prisma.supplier.findUnique({
    where: { id: supplierId },
    select: { id: true, name: true }
  });

  if (!supplier) {
    throw new Error("Supplier not found");
  }

  // Get all products from this supplier that were sold
  const supplierProducts = await prisma.productSupplier.findMany({
    where: { 
      supplierId,
      isActive: true
    },
    include: {
      product: {
        include: {
          transactionItems: {
            where: {
              transaction: {
                createdAt: Object.keys(dateFilter).length > 0 ? dateFilter : undefined
              }
            },
            include: {
              transaction: true
            }
          }
        }
      }
    }
  });

  // Calculate total items sold from this supplier
  const totalItemsSold = supplierProducts.reduce((total, ps) => {
    return total + ps.product.transactionItems.reduce((sum, item) => sum + item.quantity, 0);
  }, 0);

  // Get customer returns for products from this supplier
  const customerReturns = await prisma.return.findMany({
    where: {
      createdAt: Object.keys(dateFilter).length > 0 ? dateFilter : undefined,
      items: {
        some: {
          product: {
            productSuppliers: {
              some: {
                supplierId,
                isActive: true
              }
            }
          }
        }
      }
    },
    include: {
      items: {
        include: {
          product: {
            include: {
              productSuppliers: {
                where: { supplierId, isActive: true }
              }
            }
          }
        }
      }
    }
  });

  // Filter return items to only include those from this supplier
  const supplierReturnItems = customerReturns.flatMap(ret => 
    ret.items.filter(item => 
      item.product.productSuppliers.some(ps => ps.supplierId === supplierId)
    ).map(item => ({
      ...item,
      returnReason: ret.reason,
      returnDate: ret.createdAt
    }))
  );

  const totalItemsReturned = supplierReturnItems.reduce((sum, item) => sum + item.quantity, 0);
  const returnValue = supplierReturnItems.reduce((sum, item) => sum + Number(item.subtotal), 0);

  // Calculate return rate
  const returnRate = totalItemsSold > 0 ? (totalItemsReturned / totalItemsSold) * 100 : 0;

  // Calculate defect rate (returns due to defects/quality issues)
  const defectiveReturns = supplierReturnItems.filter(item => 
    item.returnReason.toLowerCase().includes('defect') ||
    item.returnReason.toLowerCase().includes('damaged') ||
    item.returnReason.toLowerCase().includes('quality') ||
    item.returnReason.toLowerCase().includes('broken')
  );
  const defectRate = totalItemsSold > 0 ? (defectiveReturns.length / totalItemsSold) * 100 : 0;

  // Group returns by reason
  const returnsByReason = supplierReturnItems.reduce((acc, item) => {
    const reason = item.returnReason;
    if (!acc[reason]) {
      acc[reason] = { count: 0, value: 0 };
    }
    acc[reason].count += item.quantity;
    acc[reason].value += Number(item.subtotal);
    return acc;
  }, {} as Record<string, { count: number; value: number }>);

  const returnsByReasonArray = Object.entries(returnsByReason).map(([reason, data]) => ({
    reason,
    count: data.count,
    percentage: totalItemsReturned > 0 ? (data.count / totalItemsReturned) * 100 : 0,
    value: data.value
  }));

  // Calculate monthly trends
  const monthlyData = supplierReturnItems.reduce((acc, item) => {
    const month = item.returnDate.toISOString().substring(0, 7); // YYYY-MM
    if (!acc[month]) {
      acc[month] = { returnCount: 0, returnValue: 0 };
    }
    acc[month].returnCount += item.quantity;
    acc[month].returnValue += Number(item.subtotal);
    return acc;
  }, {} as Record<string, { returnCount: number; returnValue: number }>);

  const monthlyTrends = Object.entries(monthlyData).map(([month, data]) => ({
    month,
    returnRate: totalItemsSold > 0 ? (data.returnCount / totalItemsSold) * 100 : 0,
    returnValue: data.returnValue,
    returnCount: data.returnCount
  })).sort((a, b) => a.month.localeCompare(b.month));

  // Calculate quality trend
  let qualityTrend: 'improving' | 'declining' | 'stable' = 'stable';
  if (monthlyTrends.length >= 2) {
    const recent = monthlyTrends.slice(-2);
    const trendChange = recent[1].returnRate - recent[0].returnRate;
    if (trendChange > 1) qualityTrend = 'declining';
    else if (trendChange < -1) qualityTrend = 'improving';
  }

  // Calculate customer satisfaction score (inverse of return rate with weights)
  const satisfactionBase = Math.max(0, 100 - returnRate * 2);
  const defectPenalty = defectRate * 3;
  const customerSatisfactionScore = Math.max(0, satisfactionBase - defectPenalty);

  // Calculate overall quality score
  const qualityScore = Math.round(
    (customerSatisfactionScore * 0.4) + 
    (Math.max(0, 100 - returnRate * 5) * 0.3) + 
    (Math.max(0, 100 - defectRate * 10) * 0.3)
  );

  // Generate recommendations
  const recommendations: string[] = [];
  if (returnRate > 5) recommendations.push("High return rate detected. Review product quality and supplier processes.");
  if (defectRate > 2) recommendations.push("Significant defect rate. Implement quality control measures.");
  if (qualityTrend === 'declining') recommendations.push("Quality trend is declining. Schedule supplier review meeting.");
  if (returnValue > 1000000) recommendations.push("High return value impact. Consider supplier contract review.");

  // Determine risk level
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  if (returnRate > 10 || defectRate > 5 || qualityScore < 60) riskLevel = 'high';
  else if (returnRate > 5 || defectRate > 2 || qualityScore < 80) riskLevel = 'medium';

  const metrics: QualityMetrics = {
    returnRate,
    defectRate,
    customerSatisfactionScore,
    qualityTrend,
    returnValue,
    totalItemsSold,
    totalItemsReturned,
    returnsByReason: returnsByReasonArray,
    monthlyTrends,
    qualityScore
  };

  return {
    supplierId,
    supplierName: supplier.name,
    timeRange: `${startDate?.toISOString().split('T')[0] || 'All time'} - ${endDate?.toISOString().split('T')[0] || 'Present'}`,
    metrics,
    recommendations,
    riskLevel
  };
}

/**
 * Calculate quality metrics for all suppliers
 */
export async function calculateAllSuppliersQualityMetrics(
  startDate?: Date,
  endDate?: Date
): Promise<SupplierQualityAnalysis[]> {
  const suppliers = await prisma.supplier.findMany({
    where: { isActive: true },
    select: { id: true, name: true }
  });

  const results = await Promise.all(
    suppliers.map(supplier => 
      calculateSupplierQualityMetrics(supplier.id, startDate, endDate)
    )
  );

  return results.sort((a, b) => b.metrics.qualityScore - a.metrics.qualityScore);
}
