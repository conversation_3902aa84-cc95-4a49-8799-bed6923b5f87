/**
 * Notification System Main API
 *
 * This is the main entry point for the notification system.
 * It provides both the new modular API and legacy functions for backward compatibility.
 */

import { emitEvent, EVENT_TYPES, BaseEvent } from '@/lib/events/event-system';
import { initializeNotificationSystem } from './notifications/notification-registry';
import { preferenceManager } from './notifications/preference-manager';

// Legacy imports for backward compatibility
import {
  createNotification,
  createBulkNotifications,
  createPOApprovalNotifications,
  createPOStatusNotifications,
  markPONotificationsAsRead,
  getApprovalUsers,
  type CreateNotificationData
} from './notifications-legacy';

// Re-export commonly used types and constants
export { EVENT_TYPES } from '@/lib/events/event-system';
export type { BaseEvent } from '@/lib/events/event-system';
export type { NotificationPreference, UserPreferencesProfile } from './notifications/preference-manager';

// Re-export legacy types and functions for backward compatibility
export type { CreateNotificationData };
export {
  createNotification,
  createBulkNotifications,
  createPOApprovalNotifications,
  createPOStatusNotifications,
  markPONotificationsAsRead,
  getApprovalUsers
};

/**
 * Initialize the entire notification system
 * This should be called once during application startup
 */
export async function initializeNotifications(): Promise<void> {
  try {
    console.log('Starting notification system initialization...');

    // Initialize the event system and handlers
    initializeNotificationSystem();

    // Initialize preferences for existing users
    await preferenceManager.initializeAllUserPreferences();

    console.log('Notification system initialization completed successfully');
  } catch (error) {
    console.error('Error initializing notification system:', error);
    throw error;
  }
}

/**
 * Emit a notification event
 * This is the main API for triggering notifications
 */
export async function notify(event: Omit<BaseEvent, 'eventId' | 'timestamp'>): Promise<void> {
  try {
    const fullEvent = {
      ...event,
      eventId: `${event.eventType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };

    console.log(`🚀 notify() called for event type: ${event.eventType}`);
    console.log(`📋 Full event details:`, JSON.stringify(fullEvent, null, 2));

    await emitEvent(fullEvent);

    console.log(`✅ Event ${fullEvent.eventId} emitted successfully`);
  } catch (error) {
    console.error('❌ Error emitting notification event:', error);
    throw error;
  }
}

/**
 * Convenience functions for common notification scenarios
 */

// Purchase Order Notifications
export async function notifyPOStatusChanged(
  poId: string,
  fromStatus: string,
  toStatus: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.PO_STATUS_CHANGED,
    sourceId: poId,
    sourceType: 'purchase_order',
    payload: {
      poId,
      fromStatus,
      toStatus,
      ...metadata,
    },
  });
}

export async function notifyPOApproved(
  poId: string,
  approvedBy: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.PO_APPROVED,
    sourceId: poId,
    sourceType: 'purchase_order',
    payload: {
      poId,
      approvedBy,
      ...metadata,
    },
  });
}

export async function notifyPORejected(
  poId: string,
  rejectedBy: string,
  reason: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.PO_REJECTED,
    sourceId: poId,
    sourceType: 'purchase_order',
    payload: {
      poId,
      rejectedBy,
      rejectionReason: reason,
      ...metadata,
    },
  });
}

export async function notifyPOOverdue(
  poId: string,
  daysOverdue: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.PO_OVERDUE,
    sourceId: poId,
    sourceType: 'purchase_order',
    payload: {
      poId,
      daysOverdue,
      ...metadata,
    },
  });
}

// Inventory Notifications
export async function notifyLowStock(
  productId: string,
  productName: string,
  currentStock: number,
  minThreshold: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  console.log(`🔔 notifyLowStock called for product: ${productName} (ID: ${productId})`);
  console.log(`📊 Stock details: current=${currentStock}, min=${minThreshold}`);
  console.log(`📋 Additional metadata:`, metadata);

  const payload = {
    productId,
    productName,
    currentStock,
    minThreshold,
    ...metadata,
  };

  console.log(`📤 Emitting ${EVENT_TYPES.INVENTORY_LOW_STOCK} event with payload:`, payload);

  await notify({
    eventType: EVENT_TYPES.INVENTORY_LOW_STOCK,
    sourceId: productId,
    sourceType: 'product',
    payload,
  });

  console.log(`✅ Low stock notification event emitted for ${productName}`);
}

export async function notifyOutOfStock(
  productId: string,
  productName: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.INVENTORY_OUT_OF_STOCK,
    sourceId: productId,
    sourceType: 'product',
    payload: {
      productId,
      productName,
      ...metadata,
    },
  });
}

export async function notifyBatchExpiring(
  batchId: string,
  batchNumber: string,
  productName: string,
  expiryDate: string,
  daysUntilExpiry: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.BATCH_EXPIRING,
    sourceId: batchId,
    sourceType: 'batch',
    payload: {
      batchId,
      batchNumber,
      productName,
      expiryDate,
      daysUntilExpiry,
      ...metadata,
    },
  });
}

// Cash Audit Notifications
export async function notifyCashAuditAlert(
  reconciliationId: string,
  alertType: string,
  message: string,
  discrepancyAmount: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.CASH_AUDIT_ALERT,
    sourceId: reconciliationId,
    sourceType: 'cash_reconciliation',
    payload: {
      reconciliationId,
      alertType,
      message,
      discrepancyAmount,
      ...metadata,
    },
  });
}

// Revenue Notifications
export async function notifyRevenueTargetAchieved(
  targetId: string,
  targetType: string,
  targetAmount: number,
  actualAmount: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.REVENUE_TARGET_ACHIEVED,
    sourceId: targetId,
    sourceType: 'revenue_target',
    payload: {
      targetId,
      targetType,
      targetAmount,
      actualAmount,
      percentage: Math.round((actualAmount / targetAmount) * 100),
      ...metadata,
    },
  });
}

export async function notifyRevenueTargetMissed(
  targetId: string,
  targetType: string,
  targetAmount: number,
  actualAmount: number,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.REVENUE_TARGET_MISSED,
    sourceId: targetId,
    sourceType: 'revenue_target',
    payload: {
      targetId,
      targetType,
      targetAmount,
      actualAmount,
      percentage: Math.round((actualAmount / targetAmount) * 100),
      shortfall: targetAmount - actualAmount,
      ...metadata,
    },
  });
}

// System Notifications
export async function notifySystemMaintenance(
  maintenanceId: string,
  scheduledTime: string,
  duration: string,
  description: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.SYSTEM_MAINTENANCE,
    sourceId: maintenanceId,
    sourceType: 'system_maintenance',
    payload: {
      maintenanceId,
      scheduledTime,
      duration,
      description,
      ...metadata,
    },
  });
}

// Cash Management Notifications
export async function notifyCashDiscrepancy(
  reconciliationId: string,
  cashierName: string,
  cashierRole: string,
  businessDate: string,
  discrepancyAmount: number,
  discrepancyCategory: string,
  drawerName: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  const formattedAmount = new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(Math.abs(discrepancyAmount));

  const discrepancyType = discrepancyAmount > 0 ? "surplus" : "shortage";
  const formattedDiscrepancy = `${formattedAmount} ${discrepancyType}`;

  await notify({
    eventType: EVENT_TYPES.CASH_DISCREPANCY_DETECTED,
    sourceId: reconciliationId,
    sourceType: 'cash_reconciliation',
    payload: {
      reconciliationId,
      cashierName,
      cashierRole,
      businessDate,
      discrepancyAmount: formattedDiscrepancy,
      discrepancyCategory: discrepancyCategory || 'UNKNOWN',
      drawerName,
      rawDiscrepancyAmount: discrepancyAmount,
      ...metadata,
    },
  });

  console.log(`✅ Cash discrepancy notification event emitted for ${cashierName} - ${formattedDiscrepancy}`);
}

export async function notifyUserActionRequired(
  userId: string,
  actionType: string,
  actionDescription: string,
  actionInstructions: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.USER_ACTION_REQUIRED,
    sourceId: userId,
    sourceType: 'user',
    userId, // Set user context
    payload: {
      userId,
      actionType,
      actionDescription,
      actionInstructions,
      ...metadata,
    },
  });
}

// Quality Control Notifications
export async function notifyQualityThresholdBreached(
  thresholdType: string,
  thresholdValue: number,
  actualValue: number,
  supplierId?: string,
  supplierName?: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.QUALITY_THRESHOLD_BREACHED,
    sourceId: supplierId || `threshold_${thresholdType}`,
    sourceType: supplierId ? 'supplier' : 'quality_threshold',
    payload: {
      thresholdType,
      thresholdValue,
      actualValue,
      supplierId,
      supplierName,
      breachPercentage: Math.round((actualValue / thresholdValue) * 100),
      ...metadata,
    },
  });
}

export async function notifySupplierQualityAlert(
  supplierId: string,
  supplierName: string,
  alertType: string,
  severity: string,
  description: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.SUPPLIER_QUALITY_ALERT,
    sourceId: supplierId,
    sourceType: 'supplier',
    payload: {
      supplierId,
      supplierName,
      alertType,
      severity,
      description,
      ...metadata,
    },
  });
}

export async function notifyQualityIssueEscalated(
  qualityIssueId: string,
  issueType: string,
  severity: string,
  supplierId?: string,
  supplierName?: string,
  escalationLevel: number = 1,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.QUALITY_ISSUE_ESCALATED,
    sourceId: qualityIssueId,
    sourceType: 'quality_issue',
    payload: {
      qualityIssueId,
      issueType,
      severity,
      supplierId,
      supplierName,
      escalationLevel,
      ...metadata,
    },
  });
}

export async function notifyQualityIssueReported(
  qualityIssueId: string,
  issueType: string,
  severity: string,
  productId?: string,
  productName?: string,
  supplierId?: string,
  supplierName?: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.QUALITY_ISSUE_REPORTED,
    sourceId: qualityIssueId,
    sourceType: 'quality_issue',
    payload: {
      qualityIssueId,
      issueType,
      severity,
      productId,
      productName,
      supplierId,
      supplierName,
      ...metadata,
    },
  });
}

export async function notifyQualityImprovementDue(
  improvementId: string,
  improvementTitle: string,
  dueDate: string,
  assigneeId?: string,
  supplierId?: string,
  supplierName?: string,
  metadata: Record<string, any> = {}
): Promise<void> {
  await notify({
    eventType: EVENT_TYPES.QUALITY_IMPROVEMENT_DUE,
    sourceId: improvementId,
    sourceType: 'quality_improvement',
    payload: {
      improvementId,
      improvementTitle,
      dueDate,
      assigneeId,
      supplierId,
      supplierName,
      ...metadata,
    },
  });
}

/**
 * User preference management API
 */
export const preferences = {
  /**
   * Initialize preferences for a new user
   */
  async initializeForUser(userId: string): Promise<void> {
    await preferenceManager.initializeUserPreferences(userId);
  },

  /**
   * Get user preferences
   */
  async getForUser(userId: string) {
    return await preferenceManager.getUserPreferences(userId);
  },

  /**
   * Update user preferences
   */
  async updateForUser(userId: string, preferences: any[]) {
    await preferenceManager.updateUserPreferences(userId, preferences);
  },

  /**
   * Disable all notifications for a user
   */
  async disableAll(userId: string): Promise<void> {
    await preferenceManager.disableAllNotifications(userId);
  },

  /**
   * Enable all notifications for a user
   */
  async enableAll(userId: string): Promise<void> {
    await preferenceManager.enableAllNotifications(userId);
  },
};


