import { prisma } from "@/auth";
import { differenceInDays, subDays, subMonths, format } from "date-fns";
import { AutoPOGenerationEngine } from '@/lib/auto-po-generation-engine';

export interface OptimizationRecommendation {
  id: string;
  type: 'reorder' | 'overstock' | 'expiry_risk' | 'slow_moving' | 'price_optimization' | 'supplier_switch' | 'auto_po_suggestion';
  priority: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: {
    financial: number; // Estimated financial impact in IDR
    operational: string; // Operational benefit description
  };
  actionItems: string[];
  data: {
    productId?: string;
    productName?: string;
    supplierId?: string;
    supplierName?: string;
    currentStock?: number;
    recommendedAction?: string;
    timeframe?: string;
    confidence?: number; // 0-100 confidence score
    poSuggestionId?: string; // Link to auto PO suggestion
    urgencyLevel?: 'critical' | 'high' | 'medium' | 'low';
    daysUntilStockout?: number;
  };
  metrics: {
    turnoverRate?: number;
    daysOfStock?: number;
    expiryRisk?: number;
    costSavings?: number;
  };
}

export interface OptimizationAnalysis {
  summary: {
    totalRecommendations: number;
    highPriorityCount: number;
    estimatedSavings: number;
    riskMitigation: number;
  };
  recommendations: OptimizationRecommendation[];
  insights: {
    topRisks: string[];
    opportunities: string[];
    trends: string[];
  };
}

/**
 * Main inventory optimization engine
 */
export class InventoryOptimizationEngine {
  
  /**
   * Generate comprehensive optimization recommendations
   */
  static async generateRecommendations(
    categoryId?: string,
    timeRange: '30days' | '90days' | '6months' = '90days'
  ): Promise<OptimizationAnalysis> {
    const recommendations: OptimizationRecommendation[] = [];
    
    // Get date range for analysis
    const endDate = new Date();
    const startDate = timeRange === '30days' ? subDays(endDate, 30) :
                     timeRange === '90days' ? subDays(endDate, 90) :
                     subMonths(endDate, 6);

    // Run all recommendation algorithms
    const [
      reorderRecommendations,
      overstockRecommendations,
      expiryRiskRecommendations,
      slowMovingRecommendations,
      priceOptimizationRecommendations,
      supplierSwitchRecommendations,
      autoPOSuggestions
    ] = await Promise.all([
      this.analyzeReorderPoints(categoryId, startDate, endDate),
      this.analyzeOverstock(categoryId, startDate, endDate),
      this.analyzeExpiryRisks(categoryId),
      this.analyzeSlowMovingStock(categoryId, startDate, endDate),
      this.analyzePriceOptimization(categoryId, startDate, endDate),
      this.analyzeSupplierSwitchOpportunities(categoryId, startDate, endDate),
      this.getAutoPOSuggestions(categoryId)
    ]);

    recommendations.push(
      ...reorderRecommendations,
      ...overstockRecommendations,
      ...expiryRiskRecommendations,
      ...slowMovingRecommendations,
      ...priceOptimizationRecommendations,
      ...supplierSwitchRecommendations,
      ...autoPOSuggestions
    );

    // Sort by priority and impact
    recommendations.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 };
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return b.impact.financial - a.impact.financial;
    });

    // Calculate summary metrics
    const summary = {
      totalRecommendations: recommendations.length,
      highPriorityCount: recommendations.filter(r => r.priority === 'high').length,
      estimatedSavings: recommendations.reduce((sum, r) => sum + r.impact.financial, 0),
      riskMitigation: recommendations.filter(r => 
        r.type === 'expiry_risk' || r.type === 'overstock'
      ).length
    };

    // Generate insights
    const insights = this.generateInsights(recommendations);

    return {
      summary,
      recommendations: recommendations.slice(0, 50), // Limit to top 50 recommendations
      insights
    };
  }

  /**
   * Analyze products that need reordering based on batch consumption patterns
   */
  private static async analyzeReorderPoints(
    categoryId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];

    // Build query filter
    const where: any = { active: true };
    if (categoryId) where.categoryId = categoryId;

    // Get products with low stock and consumption data
    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        unit: true,
        storeStock: true,
        warehouseStock: true,
        stockBatches: {
          where: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 0 }
          },
          orderBy: { receivedDate: 'asc' }
        },
        transactionItems: {
          where: startDate && endDate ? {
            transaction: {
              createdAt: { gte: startDate, lte: endDate },
              status: { not: 'VOIDED' }
            }
          } : undefined,
          select: {
            quantity: true,
            transaction: {
              select: { createdAt: true }
            }
          }
        }
      }
    });

    for (const product of products) {
      const totalStock = Number(product.storeStock?.quantity || 0) + 
                        Number(product.warehouseStock?.quantity || 0);
      
      // Calculate consumption rate (units per day)
      const totalSold = product.transactionItems.reduce((sum, item) => 
        sum + Number(item.quantity), 0
      );
      
      const daysInPeriod = startDate && endDate ? 
        differenceInDays(endDate, startDate) : 90;
      
      const dailyConsumptionRate = totalSold / daysInPeriod;
      const daysOfStockRemaining = dailyConsumptionRate > 0 ? 
        totalStock / dailyConsumptionRate : Infinity;

      // Recommend reorder if less than 14 days of stock remaining
      if (daysOfStockRemaining < 14 && dailyConsumptionRate > 0) {
        const recommendedOrderQuantity = Math.ceil(dailyConsumptionRate * 30); // 30 days supply
        const estimatedCost = product.stockBatches.length > 0 ? 
          Number(product.stockBatches[0].purchasePrice) * recommendedOrderQuantity : 0;

        recommendations.push({
          id: `reorder-${product.id}`,
          type: 'reorder',
          priority: daysOfStockRemaining < 7 ? 'high' : 'medium',
          title: `Reorder ${product.name}`,
          description: `Stock will run out in ${Math.ceil(daysOfStockRemaining)} days based on current consumption rate`,
          impact: {
            financial: estimatedCost,
            operational: 'Prevent stockouts and maintain customer satisfaction'
          },
          actionItems: [
            `Order ${recommendedOrderQuantity} ${product.unit.abbreviation} immediately`,
            'Review supplier lead times',
            'Consider increasing minimum stock threshold'
          ],
          data: {
            productId: product.id,
            productName: product.name,
            currentStock: totalStock,
            recommendedAction: `Order ${recommendedOrderQuantity} units`,
            timeframe: 'Immediate',
            confidence: 85
          },
          metrics: {
            turnoverRate: dailyConsumptionRate,
            daysOfStock: daysOfStockRemaining,
            costSavings: 0
          }
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze products with excess inventory
   */
  private static async analyzeOverstock(
    categoryId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];

    const where: any = { active: true };
    if (categoryId) where.categoryId = categoryId;

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        unit: true,
        storeStock: true,
        warehouseStock: true,
        stockBatches: {
          where: { status: 'ACTIVE' },
          orderBy: { receivedDate: 'asc' }
        },
        transactionItems: {
          where: startDate && endDate ? {
            transaction: {
              createdAt: { gte: startDate, lte: endDate },
              status: { not: 'VOIDED' }
            }
          } : undefined,
          select: { quantity: true }
        }
      }
    });

    for (const product of products) {
      const totalStock = Number(product.storeStock?.quantity || 0) + 
                        Number(product.warehouseStock?.quantity || 0);
      
      const totalSold = product.transactionItems.reduce((sum, item) => 
        sum + Number(item.quantity), 0
      );
      
      const daysInPeriod = startDate && endDate ? 
        differenceInDays(endDate, startDate) : 90;
      
      const dailyConsumptionRate = totalSold / daysInPeriod;
      const daysOfStockRemaining = dailyConsumptionRate > 0 ? 
        totalStock / dailyConsumptionRate : Infinity;

      // Flag as overstock if more than 90 days of inventory
      if (daysOfStockRemaining > 90 && totalStock > 0) {
        const excessQuantity = Math.floor(totalStock - (dailyConsumptionRate * 60)); // Keep 60 days
        const tiedUpCapital = product.stockBatches.reduce((sum, batch) => 
          sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 0
        );

        recommendations.push({
          id: `overstock-${product.id}`,
          type: 'overstock',
          priority: daysOfStockRemaining > 180 ? 'high' : 'medium',
          title: `Reduce inventory for ${product.name}`,
          description: `${Math.ceil(daysOfStockRemaining)} days of stock on hand - consider promotional pricing`,
          impact: {
            financial: tiedUpCapital * 0.3, // Estimate 30% of tied capital as opportunity cost
            operational: 'Free up warehouse space and working capital'
          },
          actionItems: [
            'Run promotional campaign to increase sales velocity',
            'Consider bulk discounts to B2B customers',
            'Review reorder points and quantities',
            'Evaluate supplier minimum order requirements'
          ],
          data: {
            productId: product.id,
            productName: product.name,
            currentStock: totalStock,
            recommendedAction: `Reduce by ${excessQuantity} units`,
            timeframe: '30-60 days',
            confidence: 75
          },
          metrics: {
            turnoverRate: dailyConsumptionRate,
            daysOfStock: daysOfStockRemaining,
            costSavings: tiedUpCapital * 0.1 // Estimate 10% savings from freed capital
          }
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze products with expiry risks
   */
  private static async analyzeExpiryRisks(categoryId?: string): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const where: any = { active: true };
    if (categoryId) where.categoryId = categoryId;

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        unit: true,
        stockBatches: {
          where: {
            status: 'ACTIVE',
            expiryDate: { lte: thirtyDaysFromNow },
            remainingQuantity: { gt: 0 }
          },
          orderBy: { expiryDate: 'asc' }
        }
      }
    });

    for (const product of products) {
      if (product.stockBatches.length === 0) continue;

      const expiringQuantity = product.stockBatches.reduce((sum, batch) =>
        sum + Number(batch.remainingQuantity), 0
      );

      const expiringValue = product.stockBatches.reduce((sum, batch) =>
        sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 0
      );

      const daysUntilFirstExpiry = Math.ceil(
        (product.stockBatches[0].expiryDate!.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      );

      recommendations.push({
        id: `expiry-${product.id}`,
        type: 'expiry_risk',
        priority: daysUntilFirstExpiry <= 7 ? 'high' : daysUntilFirstExpiry <= 14 ? 'medium' : 'low',
        title: `Expiry risk for ${product.name}`,
        description: `${expiringQuantity} ${product.unit.abbreviation} expiring in ${daysUntilFirstExpiry} days`,
        impact: {
          financial: expiringValue,
          operational: 'Prevent inventory write-offs and waste'
        },
        actionItems: [
          'Implement promotional pricing to accelerate sales',
          'Consider donation or return to supplier if possible',
          'Review ordering patterns to prevent future expiry',
          'Improve FIFO rotation procedures'
        ],
        data: {
          productId: product.id,
          productName: product.name,
          currentStock: expiringQuantity,
          recommendedAction: 'Accelerate sales through promotions',
          timeframe: `${daysUntilFirstExpiry} days`,
          confidence: 95
        },
        metrics: {
          expiryRisk: expiringQuantity,
          costSavings: expiringValue * 0.8 // Potential savings if sold vs written off
        }
      });
    }

    return recommendations;
  }

  /**
   * Analyze slow-moving inventory
   */
  private static async analyzeSlowMovingStock(
    categoryId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];

    const where: any = { active: true };
    if (categoryId) where.categoryId = categoryId;

    const products = await prisma.product.findMany({
      where,
      include: {
        category: true,
        unit: true,
        storeStock: true,
        warehouseStock: true,
        stockBatches: {
          where: { status: 'ACTIVE' },
          orderBy: { receivedDate: 'asc' }
        },
        transactionItems: {
          where: startDate && endDate ? {
            transaction: {
              createdAt: { gte: startDate, lte: endDate },
              status: { not: 'VOIDED' }
            }
          } : undefined,
          select: { quantity: true }
        }
      }
    });

    for (const product of products) {
      const totalStock = Number(product.storeStock?.quantity || 0) +
                        Number(product.warehouseStock?.quantity || 0);

      const totalSold = product.transactionItems.reduce((sum, item) =>
        sum + Number(item.quantity), 0
      );

      // Consider slow-moving if less than 10% turnover in the period
      const turnoverRate = totalStock > 0 ? (totalSold / totalStock) * 100 : 0;

      if (turnoverRate < 10 && totalStock > 0 && product.stockBatches.length > 0) {
        const inventoryValue = product.stockBatches.reduce((sum, batch) =>
          sum + (Number(batch.remainingQuantity) * Number(batch.purchasePrice)), 0
        );

        const oldestBatch = product.stockBatches[0];
        const daysInStock = differenceInDays(new Date(), oldestBatch.receivedDate);

        recommendations.push({
          id: `slow-moving-${product.id}`,
          type: 'slow_moving',
          priority: daysInStock > 180 ? 'high' : daysInStock > 90 ? 'medium' : 'low',
          title: `Slow-moving inventory: ${product.name}`,
          description: `Only ${turnoverRate.toFixed(1)}% turnover rate, ${daysInStock} days in stock`,
          impact: {
            financial: inventoryValue * 0.2, // Estimate 20% carrying cost
            operational: 'Free up warehouse space and reduce carrying costs'
          },
          actionItems: [
            'Analyze demand patterns and seasonality',
            'Consider bundling with fast-moving products',
            'Review pricing strategy',
            'Evaluate discontinuation if consistently slow'
          ],
          data: {
            productId: product.id,
            productName: product.name,
            currentStock: totalStock,
            recommendedAction: 'Implement sales acceleration strategy',
            timeframe: '60-90 days',
            confidence: 70
          },
          metrics: {
            turnoverRate,
            daysOfStock: daysInStock,
            costSavings: inventoryValue * 0.1 // Estimate 10% savings from improved turnover
          }
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze price optimization opportunities
   */
  private static async analyzePriceOptimization(
    categoryId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];

    // Get products with multiple suppliers for price comparison
    const products = await prisma.product.findMany({
      where: {
        active: true,
        ...(categoryId && { categoryId })
      },
      include: {
        category: true,
        unit: true,
        productSuppliers: {
          where: { isActive: true },
          include: {
            supplier: true,
            stockBatches: {
              where: {
                status: 'ACTIVE',
                ...(startDate && endDate && {
                  receivedDate: { gte: startDate, lte: endDate }
                })
              },
              orderBy: { receivedDate: 'desc' },
              take: 5
            }
          }
        }
      }
    });

    for (const product of products) {
      if (product.productSuppliers.length < 2) continue;

      // Calculate average prices from recent batches
      const supplierPrices = product.productSuppliers.map(ps => {
        const recentBatches = ps.stockBatches.slice(0, 3);
        const avgPrice = recentBatches.length > 0 ?
          recentBatches.reduce((sum, batch) => sum + Number(batch.purchasePrice), 0) / recentBatches.length :
          Number(ps.purchasePrice);

        return {
          supplier: ps.supplier,
          avgPrice,
          batchCount: recentBatches.length
        };
      }).filter(sp => sp.batchCount > 0);

      if (supplierPrices.length < 2) continue;

      // Find price differences
      const sortedPrices = supplierPrices.sort((a, b) => a.avgPrice - b.avgPrice);
      const cheapest = sortedPrices[0];
      const mostExpensive = sortedPrices[sortedPrices.length - 1];
      const priceDifference = mostExpensive.avgPrice - cheapest.avgPrice;
      const percentageDifference = (priceDifference / mostExpensive.avgPrice) * 100;

      if (percentageDifference > 15) { // More than 15% price difference
        recommendations.push({
          id: `price-opt-${product.id}`,
          type: 'price_optimization',
          priority: percentageDifference > 30 ? 'high' : 'medium',
          title: `Price optimization for ${product.name}`,
          description: `${percentageDifference.toFixed(1)}% price difference between suppliers`,
          impact: {
            financial: priceDifference * 100, // Estimate based on 100 units
            operational: 'Reduce procurement costs through better supplier selection'
          },
          actionItems: [
            `Negotiate with ${mostExpensive.supplier.name} for better pricing`,
            `Consider switching to ${cheapest.supplier.name} for future orders`,
            'Review contract terms and volume discounts',
            'Evaluate total cost of ownership including delivery and quality'
          ],
          data: {
            productId: product.id,
            productName: product.name,
            supplierId: cheapest.supplier.id,
            supplierName: cheapest.supplier.name,
            recommendedAction: `Switch to lower-cost supplier or renegotiate`,
            timeframe: 'Next purchase order',
            confidence: 80
          },
          metrics: {
            costSavings: priceDifference * 100 // Estimate based on typical order quantity
          }
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze supplier switch opportunities based on performance
   */
  private static async analyzeSupplierSwitchOpportunities(
    categoryId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<OptimizationRecommendation[]> {
    const recommendations: OptimizationRecommendation[] = [];

    // This would integrate with existing supplier quality metrics
    // For now, we'll create basic recommendations based on batch performance

    const suppliers = await prisma.supplier.findMany({
      where: { isActive: true },
      include: {
        productSuppliers: {
          where: {
            isActive: true,
            product: categoryId ? { categoryId } : undefined
          },
          include: {
            product: true,
            stockBatches: {
              where: {
                status: { in: ['ACTIVE', 'SOLD_OUT', 'EXPIRED'] },
                ...(startDate && endDate && {
                  receivedDate: { gte: startDate, lte: endDate }
                })
              }
            }
          }
        }
      }
    });

    for (const supplier of suppliers) {
      if (supplier.productSuppliers.length === 0) continue;

      // Calculate supplier performance metrics
      const totalBatches = supplier.productSuppliers.reduce((sum, ps) =>
        sum + ps.stockBatches.length, 0
      );

      const expiredBatches = supplier.productSuppliers.reduce((sum, ps) =>
        sum + ps.stockBatches.filter(b => b.status === 'EXPIRED').length, 0
      );

      const expiryRate = totalBatches > 0 ? (expiredBatches / totalBatches) * 100 : 0;

      // Flag suppliers with high expiry rates
      if (expiryRate > 10 && totalBatches >= 5) {
        recommendations.push({
          id: `supplier-switch-${supplier.id}`,
          type: 'supplier_switch',
          priority: expiryRate > 20 ? 'high' : 'medium',
          title: `Review supplier performance: ${supplier.name}`,
          description: `${expiryRate.toFixed(1)}% of batches from this supplier have expired`,
          impact: {
            financial: expiredBatches * 50000, // Estimate average batch value
            operational: 'Improve product quality and reduce waste'
          },
          actionItems: [
            'Review supplier quality control processes',
            'Evaluate alternative suppliers',
            'Negotiate better expiry date guarantees',
            'Consider supplier development program'
          ],
          data: {
            supplierId: supplier.id,
            supplierName: supplier.name,
            recommendedAction: 'Performance review and improvement plan',
            timeframe: '30-60 days',
            confidence: 75
          },
          metrics: {
            expiryRisk: expiryRate
          }
        });
      }
    }

    return recommendations;
  }

  /**
   * Generate insights from recommendations
   */
  private static generateInsights(recommendations: OptimizationRecommendation[]): {
    topRisks: string[];
    opportunities: string[];
    trends: string[];
  } {
    const topRisks: string[] = [];
    const opportunities: string[] = [];
    const trends: string[] = [];

    const highPriorityCount = recommendations.filter(r => r.priority === 'high').length;
    const reorderCount = recommendations.filter(r => r.type === 'reorder').length;
    const overstockCount = recommendations.filter(r => r.type === 'overstock').length;
    const expiryRiskCount = recommendations.filter(r => r.type === 'expiry_risk').length;
    const slowMovingCount = recommendations.filter(r => r.type === 'slow_moving').length;
    const priceOptCount = recommendations.filter(r => r.type === 'price_optimization').length;
    const supplierSwitchCount = recommendations.filter(r => r.type === 'supplier_switch').length;

    // Risk Analysis
    if (highPriorityCount > 5) {
      topRisks.push(`${highPriorityCount} high-priority issues require immediate attention`);
    }

    if (reorderCount > 3) {
      topRisks.push(`${reorderCount} products at risk of stockout within 14 days`);
    }

    if (expiryRiskCount > 0) {
      topRisks.push(`${expiryRiskCount} batches expiring within 30 days - potential waste of ${this.formatCurrency(
        recommendations.filter(r => r.type === 'expiry_risk')
          .reduce((sum, r) => sum + r.impact.financial, 0)
      )}`);
    }

    if (slowMovingCount > 5) {
      topRisks.push(`${slowMovingCount} products showing slow movement patterns - review demand forecasting`);
    }

    if (supplierSwitchCount > 0) {
      topRisks.push(`${supplierSwitchCount} suppliers showing quality issues - consider performance reviews`);
    }

    // Opportunity Analysis
    if (overstockCount > 0) {
      const overstockValue = recommendations.filter(r => r.type === 'overstock')
        .reduce((sum, r) => sum + r.impact.financial, 0);
      opportunities.push(`${overstockCount} products with excess inventory could free up ${this.formatCurrency(overstockValue)} in working capital`);
    }

    if (priceOptCount > 0) {
      const priceOptValue = recommendations.filter(r => r.type === 'price_optimization')
        .reduce((sum, r) => sum + r.impact.financial, 0);
      opportunities.push(`${priceOptCount} supplier price optimization opportunities worth ${this.formatCurrency(priceOptValue)}`);
    }

    const totalSavings = recommendations.reduce((sum, r) => sum + r.impact.financial, 0);
    if (totalSavings > 1000000) {
      opportunities.push(`Total optimization potential: ${this.formatCurrency(totalSavings)} across all recommendations`);
    }

    if (recommendations.length > 10) {
      opportunities.push(`Comprehensive analysis identified ${recommendations.length} actionable optimization opportunities`);
    }

    // Trend Analysis
    const avgConfidence = recommendations.reduce((sum, r) => sum + (r.data.confidence || 0), 0) / recommendations.length;
    if (avgConfidence > 80) {
      trends.push(`High confidence recommendations (${avgConfidence.toFixed(0)}% average) indicate reliable data patterns`);
    }

    trends.push('Batch-based analytics provide more accurate demand forecasting than traditional methods');
    trends.push('FIFO consumption patterns enable precise inventory turnover optimization');

    if (reorderCount > overstockCount) {
      trends.push('Inventory levels trending toward understocking - consider increasing safety stock');
    } else if (overstockCount > reorderCount) {
      trends.push('Inventory levels trending toward overstocking - review ordering patterns');
    }

    if (expiryRiskCount > 0) {
      trends.push('Expiry risk patterns suggest need for improved FIFO rotation and demand forecasting');
    }

    if (priceOptCount > 0) {
      trends.push('Multiple supplier pricing opportunities indicate potential for cost reduction through negotiation');
    }

    return { topRisks, opportunities, trends };
  }

  /**
   * Get automatic PO suggestions and convert them to optimization recommendations
   */
  private static async getAutoPOSuggestions(categoryId?: string): Promise<OptimizationRecommendation[]> {
    try {
      const result = await AutoPOGenerationEngine.generatePOSuggestions(categoryId);

      return result.suggestions.map(suggestion => ({
        id: `auto-po-${suggestion.id}`,
        type: 'auto_po_suggestion' as const,
        priority: suggestion.urgencyLevel === 'critical' ? 'high' :
                 suggestion.urgencyLevel === 'high' ? 'high' : 'medium',
        title: `Auto PO Suggestion: ${suggestion.productName}`,
        description: `Stock will run out in ${suggestion.daysUntilStockout} days. Recommended supplier: ${suggestion.recommendedSupplier.supplierName}`,
        impact: {
          financial: suggestion.estimatedCost,
          operational: 'Prevent stockouts and maintain customer satisfaction'
        },
        actionItems: [
          `Create PO for ${suggestion.suggestedQuantity} units`,
          `Contact ${suggestion.recommendedSupplier.supplierName}`,
          'Review delivery timeline',
          'Confirm pricing and availability'
        ],
        data: {
          productId: suggestion.productId,
          productName: suggestion.productName,
          supplierId: suggestion.recommendedSupplier.supplierId,
          supplierName: suggestion.recommendedSupplier.supplierName,
          currentStock: suggestion.currentStock,
          recommendedAction: `Order ${suggestion.suggestedQuantity} units from ${suggestion.recommendedSupplier.supplierName}`,
          timeframe: suggestion.urgencyLevel === 'critical' ? 'Immediate' :
                    suggestion.urgencyLevel === 'high' ? '1-2 days' : '3-7 days',
          confidence: suggestion.demandForecast.confidenceLevel,
          poSuggestionId: suggestion.id,
          urgencyLevel: suggestion.urgencyLevel,
          daysUntilStockout: suggestion.daysUntilStockout,
        },
        metrics: {
          daysOfStock: suggestion.daysUntilStockout,
        }
      }));
    } catch (error) {
      console.error('Error getting auto PO suggestions for inventory optimization:', error);
      return [];
    }
  }

  /**
   * Helper method to format currency for insights
   */
  private static formatCurrency(amount: number): string {
    if (amount >= 1000000) {
      return `${(amount / 1000000).toFixed(1)}M IDR`;
    } else if (amount >= 1000) {
      return `${(amount / 1000).toFixed(0)}K IDR`;
    }
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
    }).format(amount);
  }
}
