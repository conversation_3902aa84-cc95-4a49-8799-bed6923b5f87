import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import util from 'util';
import { PrismaClient } from '@prisma/client';
import { addBackupHistoryEntry } from './backup-history';

// Convert exec to Promise-based
const execPromise = util.promisify(exec);

// Default backup directory
const DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups');

// Parse DATABASE_URL to get connection info
const getDatabaseConfig = () => {
  const databaseUrl = process.env.DATABASE_URL;
  if (databaseUrl) {
    try {
      const url = new URL(databaseUrl);
      return {
        host: url.hostname || 'localhost',
        port: parseInt(url.port || '5432'),
        database: url.pathname.slice(1) || 'npos', // Remove leading slash
        user: url.username || 'postgres',
        password: url.password || 'admin',
      };
    } catch (error) {
      console.warn('Failed to parse DATABASE_URL, using fallback values');
    }
  }

  // Fallback to individual environment variables
  return {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    database: process.env.DB_NAME || 'npos',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'admin',
  };
};

const DB_CONFIG = getDatabaseConfig();

// Get the current schema version from the Prisma schema file
export const getCurrentSchemaVersion = (): string => {
  try {
    // Try to read schema version from a version file if it exists
    const versionFilePath = path.join(process.cwd(), 'prisma', 'schema_version.txt');
    if (fs.existsSync(versionFilePath)) {
      return fs.readFileSync(versionFilePath, 'utf8').trim();
    }

    // If no version file exists, calculate a hash based on the schema structure
    const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
    if (!fs.existsSync(schemaPath)) {
      return 'unknown';
    }

    const schemaContent = fs.readFileSync(schemaPath, 'utf8');

    // Extract model and enum definitions more reliably
    // Use a more robust approach that handles multi-line definitions
    const modelNames: string[] = [];
    const enumNames: string[] = [];

    // Split content into lines and process line by line
    const lines = schemaContent.split('\n');
    let currentBlock = '';
    let inModel = false;
    let inEnum = false;
    let braceCount = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      // Check for model or enum start
      if (trimmedLine.startsWith('model ')) {
        const modelMatch = trimmedLine.match(/^model\s+(\w+)/);
        if (modelMatch) {
          modelNames.push(modelMatch[1]);
          inModel = true;
          braceCount = 0;
        }
      } else if (trimmedLine.startsWith('enum ')) {
        const enumMatch = trimmedLine.match(/^enum\s+(\w+)/);
        if (enumMatch) {
          enumNames.push(enumMatch[1]);
          inEnum = true;
          braceCount = 0;
        }
      }

      // Count braces to determine when block ends
      if (inModel || inEnum) {
        for (const char of line) {
          if (char === '{') braceCount++;
          if (char === '}') braceCount--;
        }

        // If we've closed all braces, we're done with this block
        if (braceCount === 0 && (inModel || inEnum)) {
          inModel = false;
          inEnum = false;
        }
      }
    }

    // Sort names for consistent ordering
    modelNames.sort();
    enumNames.sort();

    // Create a deterministic version string
    const modelCount = modelNames.length;
    const enumCount = enumNames.length;
    const allNames = [...modelNames, ...enumNames].join('-');

    // Create a version string that includes the counts and a hash of all names
    return `${modelCount}.${enumCount}.${Buffer.from(allNames).toString('base64').substring(0, 8)}`;
  } catch (error) {
    console.error('Error determining schema version:', error);
    return 'unknown';
  }
};

// Ensure backup directory exists
export const ensureBackupDir = (backupDir = DEFAULT_BACKUP_DIR): string => {
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  return backupDir;
};

// Generate a filename for the backup
export const generateBackupFilename = (prefix = 'npos-backup'): string => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, '-');
  return `${prefix}-${timestamp}.sql`;
};

// Check if PostgreSQL tools are available
export const checkPgToolsAvailable = async (): Promise<boolean> => {
  try {
    await execPromise('pg_dump --version');
    return true;
  } catch (error) {
    return false;
  }
};

// Create a backup using Prisma (fallback method)
export const createPrismaBackup = async (
  options: {
    backupDir?: string;
    filename?: string;
    comment?: string;
  } = {}
): Promise<{ filePath: string; size: number; timestamp: Date }> => {
  const backupDir = ensureBackupDir(options.backupDir);
  const filename = options.filename || generateBackupFilename();
  const filePath = path.join(backupDir, filename);

  try {
    console.log('Starting Prisma backup creation...');

    // Create a new Prisma client
    const prisma = new PrismaClient();

    // Create a JSON structure to store the data
    const backupData: {
      metadata: {
        timestamp: Date;
        comment: string;
        tables: string[];
        database: string;
        version: string;
        schemaVersion: string;
      };
      data: Record<string, any[]>;
    } = {
      metadata: {
        timestamp: new Date(),
        comment: options.comment || '',
        tables: [],
        database: DB_CONFIG.database,
        version: '1.0',
        schemaVersion: getCurrentSchemaVersion(),
      },
      data: {},
    };

    console.log('Backup metadata initialized:', backupData.metadata);

    // Manually define the tables we know exist in our schema
    // This is a simpler approach than trying to query the information_schema
    const knownTables = [
      'User',
      'Session',
      // 'ActivityLog' - Excluded to preserve activity logs during restore
      'Product',
      'ProductSupplier',
      'StockBatch',
      'Category',
      'Unit',
      'StoreStock',
      'WarehouseStock',
      'StockAdjustment',
      'StockHistory',
      'SimpleStockTransfer',
      'Transaction',
      'TransactionItem',
      'Customer',
      'PurchaseOrder',
      'PurchaseOrderItem',
      'PurchaseOrderReceiving',
      'PurchaseOrderReceivingItem',
      'PurchaseOrderTemplate',
      'PurchaseOrderTemplateItem',
      'Supplier',
      'Return',
      'ReturnItem',
      'SupplierReturn',
      'SupplierReturnItem',
      'Notification',
      'SystemSetting',
      'StoreInfo',
      'Conversation',
      'StarredConversation',
      'Participant',
      'Message',
      'CashDrawer',
      'Terminal',
      'DrawerSession',
      'CashReconciliation',
      'CashAuditAlert',
      'TemporaryPrice',
      'RevenueTarget',
      'POStatusHistory'
    ];

    // For each table, export the data
    console.log('Starting table backup process...');
    let totalRecords = 0;

    for (const tableName of knownTables) {
      try {
        console.log(`Backing up table: ${tableName}`);

        // Use the Prisma client's dynamic access to get all records
        // This is safer than raw SQL queries
        const records = await (prisma as any)[tableName].findMany();

        if (records && records.length > 0) {
          backupData.metadata.tables.push(tableName);
          backupData.data[tableName] = records;
          totalRecords += records.length;
          console.log(`  - Backed up ${records.length} records from ${tableName}`);
        } else {
          console.log(`  - Table ${tableName} is empty, skipping`);
        }
      } catch (tableError: any) {
        console.warn(`Could not backup table ${tableName}: ${tableError?.message || String(tableError)}`);
        // Skip tables that don't exist or can't be accessed
      }
    }

    console.log(`Total records backed up: ${totalRecords} from ${backupData.metadata.tables.length} tables`);

    // Validate that we have some data to backup
    if (backupData.metadata.tables.length === 0) {
      throw new Error('No tables found to backup. Database might be empty or inaccessible.');
    }

    // Write the backup to a file
    console.log('Writing backup data to file...');
    const backupContent = JSON.stringify(backupData, null, 2);

    if (!backupContent || backupContent.length < 100) {
      throw new Error('Backup content is too small, indicating a potential issue with data serialization.');
    }

    fs.writeFileSync(filePath, backupContent);

    // Get file stats and validate
    const stats = fs.statSync(filePath);
    console.log(`Backup file created: ${filePath} (${stats.size} bytes)`);

    if (stats.size === 0) {
      throw new Error('Backup file is empty after creation.');
    }

    // Create metadata file with additional information
    const metadataPath = `${filePath}.json`;
    const metadata = {
      filename,
      timestamp: new Date(),
      size: stats.size,
      comment: options.comment || '',
      method: 'prisma',
      schemaVersion: getCurrentSchemaVersion(),
      excludedTables: ['ActivityLog'],
      tablesBackedUp: backupData.metadata.tables,
      recordCount: Object.values(backupData.data).reduce((total, records) => total + records.length, 0)
    };

    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
    console.log(`Metadata file created: ${metadataPath}`);

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful backup operation
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath,
      fileName: filename,
      method: 'prisma',
      success: true,
      message: 'Backup created successfully using Prisma',
      size: stats.size,
      comment: options.comment,
    });

    return {
      filePath,
      size: stats.size,
      timestamp: new Date(),
    };
  } catch (error: any) {
    console.error('Prisma backup failed:', error);

    // Log the failed backup attempt
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: filePath,
      fileName: filename,
      method: 'prisma',
      success: false,
      message: `Prisma backup failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database backup failed (Prisma method): ${error?.message || String(error)}`);
  }
};

// Create a backup of the database
export const createBackup = async (
  options: {
    backupDir?: string;
    filename?: string;
    comment?: string;
    forcePrismaMethod?: boolean;
  } = {}
): Promise<{ filePath: string; size: number; timestamp: Date }> => {
  const backupDir = ensureBackupDir(options.backupDir);
  const filename = options.filename || generateBackupFilename();
  const filePath = path.join(backupDir, filename);

  // Check if PostgreSQL tools are available
  // Temporarily force Prisma method to avoid pg_dump password issues
  const pgToolsAvailable = false; // options.forcePrismaMethod ? false : await checkPgToolsAvailable();

  if (pgToolsAvailable) {
    // Use pg_dump method
    try {
      // Set PGPASSWORD environment variable for pg_dump
      const env = { ...process.env, PGPASSWORD: DB_CONFIG.password };

      // Create pg_dump command with exclusion for ActivityLog table and no password prompt
      const pgDumpCmd = `pg_dump -h ${DB_CONFIG.host} -p ${DB_CONFIG.port} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -F c -T "ActivityLog" --no-password -f "${filePath}"`;

      console.log('Creating backup using pg_dump...');
      console.log('Database config:', { host: DB_CONFIG.host, port: DB_CONFIG.port, user: DB_CONFIG.user, database: DB_CONFIG.database });

      // Execute pg_dump with password environment variable
      await execPromise(pgDumpCmd, { env });

      // Get file stats
      const stats = fs.statSync(filePath);

      // Create metadata file with additional information
      const metadataPath = `${filePath}.json`;
      const metadata = {
        filename,
        timestamp: new Date(),
        size: stats.size,
        comment: options.comment || '',
        method: 'pg_dump',
        schemaVersion: getCurrentSchemaVersion(),
        excludedTables: ['ActivityLog'],
      };

      fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));

      // Log the successful backup operation
      addBackupHistoryEntry({
        type: 'backup',
        timestamp: new Date().toISOString(),
        filePath,
        fileName: filename,
        method: 'pg_dump',
        success: true,
        message: 'Backup created successfully using pg_dump',
        size: stats.size,
        comment: options.comment,
      });

      return {
        filePath,
        size: stats.size,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('pg_dump backup failed:', error);
      console.log('Falling back to Prisma backup method...');

      // Log the failed backup attempt
      addBackupHistoryEntry({
        type: 'backup',
        timestamp: new Date().toISOString(),
        filePath: filePath,
        fileName: filename,
        method: 'pg_dump',
        success: false,
        message: `pg_dump backup failed: ${(error as any)?.message || String(error)}`,
      });

      // If pg_dump fails, try the Prisma method
      return createPrismaBackup(options);
    }
  } else {
    // Use Prisma method
    console.log('PostgreSQL tools not available, using Prisma backup method...');
    return createPrismaBackup(options);
  }
};

// Validate schema compatibility between backup and current schema
export const validateSchemaCompatibility = async (
  backupFilePath: string
): Promise<{ compatible: boolean; backupSchemaVersion: string; currentSchemaVersion: string; message: string }> => {
  const currentSchemaVersion = getCurrentSchemaVersion();
  let backupSchemaVersion = 'unknown';

  // Check if metadata file exists
  const metadataPath = `${backupFilePath}.json`;
  if (fs.existsSync(metadataPath)) {
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      backupSchemaVersion = metadata.schemaVersion || 'unknown';

      // If the backup was created with an older version that didn't track schema version
      if (backupSchemaVersion === 'unknown' && metadata.method) {
        return {
          compatible: false,
          backupSchemaVersion,
          currentSchemaVersion,
          message: 'This backup was created with an older version that did not track schema versions. Proceed with caution.'
        };
      }
    } catch (e) {
      console.warn(`Could not parse metadata for ${backupFilePath}`);
    }
  }

  // If we couldn't determine the backup schema version
  if (backupSchemaVersion === 'unknown') {
    return {
      compatible: false,
      backupSchemaVersion,
      currentSchemaVersion,
      message: 'Could not determine the schema version of this backup. Proceed with caution.'
    };
  }

  // Compare schema versions
  const compatible = backupSchemaVersion === currentSchemaVersion;

  return {
    compatible,
    backupSchemaVersion,
    currentSchemaVersion,
    message: compatible
      ? 'Schema versions match. Safe to restore.'
      : `Schema version mismatch. Backup schema (${backupSchemaVersion}) differs from current schema (${currentSchemaVersion}). Restoring may cause data inconsistencies.`
  };
};

// Restore database from backup
export const restoreBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string; schemaValidation?: any }> => {
  if (!fs.existsSync(backupFilePath)) {
    throw new Error(`Backup file not found: ${backupFilePath}`);
  }

  // Note: Schema validation is now handled separately before calling this function
  // This function assumes the schema has already been validated and is compatible

  // Check if this is a Prisma backup, pg_dump backup, or external upload
  const metadataPath = `${backupFilePath}.json`;
  let isPrismaBackup = false;
  let isExternalUpload = false;

  if (fs.existsSync(metadataPath)) {
    try {
      const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      isPrismaBackup = metadata.method === 'prisma';
      isExternalUpload = metadata.isExternalUpload === true;

      // If this is an external upload and PostgreSQL tools are not available,
      // we'll need to try the Prisma method even if it's not a Prisma backup
      if (isExternalUpload) {
        console.log('This is an external upload. Will check for PostgreSQL tools availability.');
      }
    } catch (e) {
      console.warn(`Could not parse metadata for ${backupFilePath}`);
    }
  } else {
    // Try to determine by file content
    try {
      const content = fs.readFileSync(backupFilePath, 'utf8');
      isPrismaBackup = content.startsWith('{') && content.includes('"metadata":');
    } catch (e) {
      console.warn(`Could not read backup file ${backupFilePath}`);
    }
  }

  // Get schema validation info
  const schemaValidation = await validateSchemaCompatibility(backupFilePath);

  if (isPrismaBackup) {
    // Restore using Prisma method
    const result = await restorePrismaBackup(backupFilePath);
    return { ...result, schemaValidation };
  } else {
    // Check if PostgreSQL tools are available
    const pgToolsAvailable = await checkPgToolsAvailable();

    if (pgToolsAvailable) {
      // Restore using pg_restore
      const result = await restorePgBackup(backupFilePath);
      return { ...result, schemaValidation };
    } else {
      // PostgreSQL tools not available, try to use Prisma method as fallback
      console.log('PostgreSQL tools not available, attempting to use Prisma method as fallback...');

      try {
        // Try to parse the SQL file as if it were a Prisma backup
        const result = await restorePrismaBackup(backupFilePath);
        return { ...result, schemaValidation };
      } catch (error) {
        // If Prisma method fails, throw a more helpful error
        const errorMessage = isExternalUpload
          ? 'This external SQL file cannot be restored without PostgreSQL tools. ' +
            'The system attempted to use the Prisma fallback method, but the file format is not compatible. ' +
            'Please install PostgreSQL tools or use a Prisma-compatible backup format.'
          : 'PostgreSQL tools (pg_restore) are not available and the backup could not be restored using the Prisma method. ' +
            'Please install PostgreSQL tools or convert this backup to a Prisma-compatible format.';

        throw new Error(errorMessage);
      }
    }
  }
};

// Restore database from a pg_dump backup
export const restorePgBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string }> => {
  // Check if PostgreSQL tools are available
  const pgToolsAvailable = await checkPgToolsAvailable();

  if (!pgToolsAvailable) {
    throw new Error('PostgreSQL tools (pg_restore) are not available. Cannot restore this backup format.');
  }

  // First, backup the ActivityLog table
  console.log('Backing up ActivityLog table before restore...');
  const prisma = new PrismaClient();
  const activityLogs = await prisma.activityLog.findMany();

  // Set PGPASSWORD environment variable for pg_restore
  const env = { ...process.env, PGPASSWORD: DB_CONFIG.password };

  // Create pg_restore command with no password prompt
  const pgRestoreCmd = `pg_restore -h ${DB_CONFIG.host} -p ${DB_CONFIG.port} -U ${DB_CONFIG.user} -d ${DB_CONFIG.database} -c --no-password "${backupFilePath}"`;

  try {
    console.log('Restoring backup using pg_restore...');
    console.log('Database config:', { host: DB_CONFIG.host, port: DB_CONFIG.port, user: DB_CONFIG.user, database: DB_CONFIG.database });

    // Execute pg_restore with password environment variable
    await execPromise(pgRestoreCmd, { env });

    // Restore the ActivityLog table
    console.log(`Restoring ${activityLogs.length} activity logs...`);
    if (activityLogs.length > 0) {
      await prisma.activityLog.createMany({
        data: activityLogs,
        skipDuplicates: true,
      });
    }

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful restore operation
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'pg_dump',
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)}`,
    });

    return {
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)}`,
    };
  } catch (error: any) {
    console.error('Restore failed:', error);

    // Log the failed restore attempt
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'pg_dump',
      success: false,
      message: `Restore failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database restore failed: ${error?.message || String(error)}`);
  }
};

// Restore database from a Prisma backup
export const restorePrismaBackup = async (
  backupFilePath: string
): Promise<{ success: boolean; message: string }> => {
  try {
    // Read the backup file
    const backupContent = fs.readFileSync(backupFilePath, 'utf8');

    // Try to parse as JSON
    let backupData;
    try {
      backupData = JSON.parse(backupContent);
    } catch (parseError) {
      // If it's not valid JSON, it might be a plain SQL file
      // For now, we can't handle plain SQL files without pg_restore
      throw new Error('The backup file is not in a Prisma-compatible format (not valid JSON).');
    }

    // Validate that it has the expected structure
    if (!backupData.metadata || !backupData.data || !Array.isArray(backupData.metadata.tables)) {
      throw new Error('The backup file is not in a valid Prisma backup format. Missing required metadata or data structure.');
    }

    // Create a new Prisma client
    const prisma = new PrismaClient();

    // First, backup the ActivityLog table
    console.log('Backing up ActivityLog table before restore...');
    const activityLogs = await prisma.activityLog.findMany();

    // For each table in the backup, restore the data
    for (const tableName of backupData.metadata.tables) {
      try {
        // We'll handle ActivityLog separately at the end
        if (tableName === 'ActivityLog') {
          console.log('ActivityLog table will be handled separately');
          continue;
        }

        // Use the Prisma client's dynamic access to clear and insert data
        // This is safer than raw SQL queries

        // First, delete all records from the table
        try {
          await (prisma as any)[tableName].deleteMany({});
        } catch (deleteError: any) {
          console.warn(`Could not clear table ${tableName}: ${deleteError?.message || String(deleteError)}`);
          // Continue anyway, as the table might be empty
        }

        // Get the records for this table
        const records = backupData.data[tableName];

        // If there are records, insert them
        if (records && records.length > 0) {
          // Use createMany to insert all records at once
          try {
            await (prisma as any)[tableName].createMany({
              data: records,
              skipDuplicates: true, // Skip records that would cause unique constraint violations
            });
          } catch (createManyError: any) {
            console.warn(`Could not use createMany for table ${tableName}, falling back to individual inserts: ${createManyError?.message || String(createManyError)}`);

            // If createMany fails, fall back to individual inserts
            for (const record of records) {
              try {
                await (prisma as any)[tableName].create({
                  data: record,
                });
              } catch (insertError: any) {
                console.warn(`Could not insert record into ${tableName}: ${insertError?.message || String(insertError)}`);
                // Continue with next record
              }
            }
          }
        }
      } catch (tableError: any) {
        console.warn(`Could not restore table ${tableName}: ${tableError?.message || String(tableError)}`);
        // Continue with next table
      }
    }

    // Restore the ActivityLog table
    console.log(`Restoring ${activityLogs.length} activity logs...`);
    if (activityLogs.length > 0) {
      await prisma.activityLog.createMany({
        data: activityLogs,
        skipDuplicates: true,
      });
    }

    // Close the Prisma client
    await prisma.$disconnect();

    // Log the successful restore operation
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'prisma',
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)} (Prisma method)`,
    });

    return {
      success: true,
      message: `Database restored successfully from ${path.basename(backupFilePath)} (Prisma method)`,
    };
  } catch (error: any) {
    console.error('Prisma restore failed:', error);

    // Log the failed restore attempt
    addBackupHistoryEntry({
      type: 'restore',
      timestamp: new Date().toISOString(),
      filePath: backupFilePath,
      fileName: path.basename(backupFilePath),
      method: 'prisma',
      success: false,
      message: `Prisma restore failed: ${error?.message || String(error)}`,
    });

    throw new Error(`Database restore failed (Prisma method): ${error?.message || String(error)}`);
  }
};

// List all available backups
export const listBackups = (
  backupDir = DEFAULT_BACKUP_DIR
): Array<{
  filename: string;
  path: string;
  timestamp: Date;
  size: number;
  comment?: string;
  schemaVersion?: string;
  currentSchemaVersion?: string;
  schemaCompatible?: boolean;
  method?: string;
}> => {
  ensureBackupDir(backupDir);

  // Get all .sql files in the backup directory
  const files = fs.readdirSync(backupDir)
    .filter(file => file.endsWith('.sql'))
    .map(filename => {
      const filePath = path.join(backupDir, filename);
      const stats = fs.statSync(filePath);

      // Try to read metadata if available
      let metadata: any = {};
      const metadataPath = `${filePath}.json`;
      if (fs.existsSync(metadataPath)) {
        try {
          metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
        } catch (e) {
          console.warn(`Could not parse metadata for ${filename}`);
        }
      }

      // Get the current schema version for comparison
      const currentSchemaVersion = getCurrentSchemaVersion();

      return {
        filename,
        path: filePath,
        timestamp: metadata.timestamp ? new Date(metadata.timestamp) : stats.mtime,
        size: stats.size,
        comment: metadata.comment,
        schemaVersion: metadata.schemaVersion || 'unknown',
        currentSchemaVersion,
        schemaCompatible: metadata.schemaVersion === currentSchemaVersion,
        method: metadata.method || 'unknown',
      };
    });

  // Sort by timestamp (newest first)
  return files.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
};

// Delete a backup
export const deleteBackup = (
  backupFilePath: string
): boolean => {
  if (!fs.existsSync(backupFilePath)) {
    throw new Error(`Backup file not found: ${backupFilePath}`);
  }

  // Delete the backup file
  fs.unlinkSync(backupFilePath);

  // Delete metadata file if it exists
  const metadataPath = `${backupFilePath}.json`;
  if (fs.existsSync(metadataPath)) {
    fs.unlinkSync(metadataPath);
  }

  return true;
};
