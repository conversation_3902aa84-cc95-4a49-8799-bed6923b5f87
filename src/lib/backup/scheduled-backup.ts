import fs from 'fs';
import path from 'path';
import { createBackup, listBackups } from './db-backup';
import { addBackupHistoryEntry } from './backup-history';

// Default backup directory
const DEFAULT_BACKUP_DIR = path.join(process.cwd(), 'backups');

// Maximum number of backups to keep (rotation)
const MAX_BACKUPS = 30; // Keep last 30 backups by default

// Perform a scheduled backup
export const performScheduledBackup = async (
  options: {
    backupDir?: string;
    maxBackups?: number;
    prefix?: string;
  } = {}
): Promise<{ success: boolean; message: string; backupPath?: string }> => {
  const backupDir = options.backupDir || DEFAULT_BACKUP_DIR;
  const maxBackups = options.maxBackups || MAX_BACKUPS;
  const prefix = options.prefix || 'scheduled-backup';

  try {
    // Create a new backup
    const timestamp = new Date();
    const comment = `Scheduled backup created at ${timestamp.toLocaleString()}`;
    const result = await createBackup({
      backupDir,
      filename: `${prefix}-${timestamp.toISOString().replace(/[:.]/g, '-')}.sql`,
      comment,
    });

    // Rotate backups if needed
    const allBackups = listBackups(backupDir);
    if (allBackups.length > maxBackups) {
      // Delete oldest backups
      const backupsToDelete = allBackups.slice(maxBackups);
      for (const backup of backupsToDelete) {
        try {
          fs.unlinkSync(backup.path);
          // Delete metadata file if it exists
          const metadataPath = `${backup.path}.json`;
          if (fs.existsSync(metadataPath)) {
            fs.unlinkSync(metadataPath);
          }

          // Log the backup deletion
          addBackupHistoryEntry({
            type: 'backup',
            timestamp: new Date().toISOString(),
            filePath: backup.path,
            fileName: backup.filename,
            method: 'rotation',
            success: true,
            message: `Backup deleted during rotation (keeping ${maxBackups} most recent backups)`,
          });
        } catch (error) {
          console.warn(`Failed to delete old backup ${backup.filename}:`, error);
        }
      }
    }

    // Log the scheduled backup
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: result.filePath,
      fileName: path.basename(result.filePath),
      method: 'scheduled',
      success: true,
      message: `Scheduled backup created successfully: ${path.basename(result.filePath)}`,
      size: result.size,
      comment: comment,
    });

    return {
      success: true,
      message: `Scheduled backup created successfully: ${path.basename(result.filePath)}`,
      backupPath: result.filePath,
    };
  } catch (error: any) {
    console.error('Scheduled backup failed:', error);

    // Log the failed scheduled backup
    addBackupHistoryEntry({
      type: 'backup',
      timestamp: new Date().toISOString(),
      filePath: '',
      fileName: `${prefix}-${new Date().toISOString().replace(/[:.]/g, '-')}.sql`,
      method: 'scheduled',
      success: false,
      message: `Scheduled backup failed: ${error?.message || String(error)}`,
    });

    return {
      success: false,
      message: `Scheduled backup failed: ${error?.message || String(error)}`,
    };
  }
};

// If this script is run directly (e.g., via cron)
if (require.main === module) {
  performScheduledBackup()
    .then(result => {
      console.log(result.message);
      process.exit(result.success ? 0 : 1);
    })
    .catch(error => {
      console.error('Unhandled error during scheduled backup:', error);
      process.exit(1);
    });
}
