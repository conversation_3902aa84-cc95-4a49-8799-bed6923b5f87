import fs from 'fs';
import path from 'path';

// Define the history file path
const HISTORY_FILE_PATH = path.join(process.cwd(), 'logs', 'backup-history.json');

// Define the history entry types
export type BackupHistoryEntry = {
  type: 'backup' | 'restore';
  timestamp: string;
  filePath: string;
  fileName: string;
  method: 'pg_dump' | 'prisma' | 'rotation' | 'scheduled' | 'manual' | 'download' | 'upload';
  success: boolean;
  message: string;
  size?: number;
  comment?: string;
};

// Initialize the history file if it doesn't exist
export const initHistoryFile = (): void => {
  const logsDir = path.dirname(HISTORY_FILE_PATH);

  // Ensure logs directory exists
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }

  // Create history file if it doesn't exist
  if (!fs.existsSync(HISTORY_FILE_PATH)) {
    fs.writeFileSync(HISTORY_FILE_PATH, JSON.stringify([], null, 2));
  }
};

// Get all history entries
export const getBackupHistory = (): BackupHistoryEntry[] => {
  initHistoryFile();

  try {
    const historyContent = fs.readFileSync(HISTORY_FILE_PATH, 'utf8');
    return JSON.parse(historyContent);
  } catch (error) {
    console.error('Error reading backup history:', error);
    return [];
  }
};

// Add a new history entry
export const addBackupHistoryEntry = (entry: BackupHistoryEntry): void => {
  initHistoryFile();

  try {
    // Read existing history
    const history = getBackupHistory();

    // Add new entry at the beginning (newest first)
    history.unshift({
      ...entry,
      timestamp: new Date().toISOString(), // Ensure timestamp is current
    });

    // Limit history to 1000 entries to prevent file from growing too large
    const limitedHistory = history.slice(0, 1000);

    // Write updated history
    fs.writeFileSync(HISTORY_FILE_PATH, JSON.stringify(limitedHistory, null, 2));
  } catch (error) {
    console.error('Error adding backup history entry:', error);
  }
};

// Get the last restore operation
export const getLastRestoreOperation = (): BackupHistoryEntry | null => {
  const history = getBackupHistory();

  // Find the most recent restore operation
  return history.find(entry => entry.type === 'restore' && entry.success) || null;
};

// Get the last backup operation
export const getLastBackupOperation = (): BackupHistoryEntry | null => {
  const history = getBackupHistory();

  // Find the most recent backup operation
  return history.find(entry => entry.type === 'backup' && entry.success) || null;
};
