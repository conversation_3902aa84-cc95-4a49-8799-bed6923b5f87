import { prisma } from '@/lib/prisma';
import { calculateSupplierQualityMetrics } from '@/lib/supplier-quality-metrics';
import { subMonths, differenceInDays } from 'date-fns';

export interface SupplierScore {
  supplierId: string;
  supplierName: string;
  productSupplierId: string;
  totalScore: number;
  breakdown: {
    priceScore: number;
    qualityScore: number;
    deliveryScore: number;
    moqScore: number;
    preferredScore: number;
  };
  reasoning: string[];
  purchasePrice: number;
  minimumOrderQuantity?: number;
  leadTimeDays?: number;
  isPreferred: boolean;
  qualityMetrics?: {
    returnRate: number;
    defectRate: number;
    qualityScore: number;
    riskLevel: string;
  };
  deliveryMetrics?: {
    averageDeliveryDays: number;
    onTimeDeliveryRate: number;
    totalDeliveries: number;
  };
}

export interface SupplierRecommendation {
  productId: string;
  productName: string;
  requestedQuantity: number;
  recommendedSupplier: SupplierScore;
  alternativeSuppliers: SupplierScore[];
  recommendation: {
    confidence: 'high' | 'medium' | 'low';
    reasoning: string[];
    warnings: string[];
  };
}

/**
 * Supplier Selection Engine
 * Automatically recommends the best supplier for products based on weighted criteria
 */
export class SupplierSelectionEngine {
  // Scoring weights (must sum to 100)
  private static readonly WEIGHTS = {
    PRICE: 30,
    QUALITY: 25,
    DELIVERY: 20,
    MOQ: 15,
    PREFERRED: 10,
  };

  /**
   * Get supplier recommendations for a single product
   */
  static async getSupplierRecommendation(
    productId: string,
    requestedQuantity: number
  ): Promise<SupplierRecommendation | null> {
    try {
      console.log(`Getting supplier recommendation for product ${productId}, quantity ${requestedQuantity}`);

      // Get product with all suppliers
      const product = await prisma.product.findUnique({
        where: { id: productId },
        include: {
          productSuppliers: {
            where: { isActive: true },
            include: {
              supplier: true,
            },
          },
        },
      });

      if (!product) {
        console.log(`Product ${productId} not found`);
        return null;
      }

      if (product.productSuppliers.length === 0) {
        console.log(`No active suppliers found for product ${productId}`);
        return null;
      }

      console.log(`Found ${product.productSuppliers.length} suppliers for product ${product.name}`);

      // Score all suppliers for this product
      const supplierScores = await Promise.all(
        product.productSuppliers.map(async (ps) => {
          try {
            return await this.scoreSupplier(ps, requestedQuantity);
          } catch (error) {
            console.error(`Error scoring supplier ${ps.supplier.name} for product ${product.name}:`, error);
            // Return a default low score instead of failing completely
            return {
              supplierId: ps.supplier.id,
              supplierName: ps.supplier.name,
              productSupplierId: ps.id,
              totalScore: 0,
              breakdown: {
                priceScore: 0,
                qualityScore: 0,
                deliveryScore: 0,
                moqScore: 0,
                preferredScore: 0,
              },
              reasoning: ['Error calculating supplier score'],
              purchasePrice: Number(ps.purchasePrice),
              minimumOrderQuantity: ps.minimumOrderQuantity,
              leadTimeDays: ps.leadTimeDays,
              isPreferred: ps.isPreferred,
            };
          }
        })
      );

      // Sort by total score (highest first)
      supplierScores.sort((a, b) => b.totalScore - a.totalScore);

      const [recommended, ...alternatives] = supplierScores;

      // Generate recommendation confidence and reasoning
      const recommendation = this.generateRecommendation(recommended, alternatives, requestedQuantity);

      return {
        productId: product.id,
        productName: product.name,
        requestedQuantity,
        recommendedSupplier: recommended,
        alternativeSuppliers: alternatives,
        recommendation,
      };
    } catch (error) {
      console.error(`Error getting supplier recommendation for product ${productId}:`, error);
      return null;
    }
  }

  /**
   * Get supplier recommendations for multiple products
   */
  static async getBulkSupplierRecommendations(
    items: Array<{ productId: string; quantity: number }>
  ): Promise<SupplierRecommendation[]> {
    const recommendations = await Promise.all(
      items.map(item =>
        this.getSupplierRecommendation(item.productId, item.quantity)
      )
    );

    return recommendations.filter(rec => rec !== null) as SupplierRecommendation[];
  }

  /**
   * Score a supplier based on weighted criteria
   */
  private static async scoreSupplier(
    productSupplier: any,
    requestedQuantity: number
  ): Promise<SupplierScore> {
    const supplier = productSupplier.supplier;

    try {
      // Calculate individual scores with error handling
      const priceScore = await this.calculatePriceScore(productSupplier).catch(error => {
        console.warn(`Error calculating price score for supplier ${supplier.name}:`, error);
        return 75; // Default neutral score
      });

      const qualityScore = await this.calculateQualityScore(supplier.id).catch(error => {
        console.warn(`Error calculating quality score for supplier ${supplier.name}:`, error);
        return 75; // Default neutral score
      });

      const deliveryScore = await this.calculateDeliveryScore(supplier.id).catch(error => {
        console.warn(`Error calculating delivery score for supplier ${supplier.name}:`, error);
        return 75; // Default neutral score
      });

      const moqScore = this.calculateMOQScore(productSupplier, requestedQuantity);
      const preferredScore = this.calculatePreferredScore(productSupplier);

      // Calculate weighted total score
      const totalScore = Math.round(
        (priceScore * this.WEIGHTS.PRICE +
          qualityScore * this.WEIGHTS.QUALITY +
          deliveryScore * this.WEIGHTS.DELIVERY +
          moqScore * this.WEIGHTS.MOQ +
          preferredScore * this.WEIGHTS.PREFERRED) / 100
      );

      // Generate reasoning
      const reasoning = this.generateScoreReasoning({
        priceScore,
        qualityScore,
        deliveryScore,
        moqScore,
        preferredScore,
        supplier,
        productSupplier,
      });

      return {
        supplierId: supplier.id,
        supplierName: supplier.name,
        productSupplierId: productSupplier.id,
        totalScore,
        breakdown: {
          priceScore,
          qualityScore,
          deliveryScore,
          moqScore,
          preferredScore,
        },
        reasoning,
        purchasePrice: Number(productSupplier.purchasePrice),
        minimumOrderQuantity: productSupplier.minimumOrderQuantity,
        leadTimeDays: productSupplier.leadTimeDays,
        isPreferred: productSupplier.isPreferred,
      };
    } catch (error) {
      console.error(`Error in scoreSupplier for ${supplier.name}:`, error);
      // Return a fallback score
      return {
        supplierId: supplier.id,
        supplierName: supplier.name,
        productSupplierId: productSupplier.id,
        totalScore: 50,
        breakdown: {
          priceScore: 50,
          qualityScore: 50,
          deliveryScore: 50,
          moqScore: 50,
          preferredScore: productSupplier.isPreferred ? 100 : 50,
        },
        reasoning: ['Error calculating detailed scores - using fallback values'],
        purchasePrice: Number(productSupplier.purchasePrice),
        minimumOrderQuantity: productSupplier.minimumOrderQuantity,
        leadTimeDays: productSupplier.leadTimeDays,
        isPreferred: productSupplier.isPreferred,
      };
    }
  }

  /**
   * Calculate price competitiveness score (0-100)
   */
  private static async calculatePriceScore(productSupplier: any): Promise<number> {
    // Get all suppliers for this product to compare prices
    const allSuppliers = await prisma.productSupplier.findMany({
      where: {
        productId: productSupplier.productId,
        isActive: true,
      },
    });

    if (allSuppliers.length === 1) {
      return 100; // Only supplier gets full score
    }

    const prices = allSuppliers.map(ps => Number(ps.purchasePrice));
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const currentPrice = Number(productSupplier.purchasePrice);

    // Score inversely proportional to price (lower price = higher score)
    if (maxPrice === minPrice) {
      return 100; // All prices are the same
    }

    const priceScore = Math.round(
      100 - ((currentPrice - minPrice) / (maxPrice - minPrice)) * 100
    );

    return Math.max(0, Math.min(100, priceScore));
  }

  /**
   * Calculate quality score based on existing quality metrics (0-100)
   */
  private static async calculateQualityScore(supplierId: string): Promise<number> {
    try {
      const qualityMetrics = await calculateSupplierQualityMetrics(
        supplierId,
        subMonths(new Date(), 6), // Last 6 months
        new Date()
      );

      return qualityMetrics.metrics.qualityScore;
    } catch (error) {
      console.warn(`Could not calculate quality score for supplier ${supplierId}:`, error);
      return 75; // Default neutral score for suppliers without quality data
    }
  }

  /**
   * Calculate delivery reliability score (0-100)
   */
  private static async calculateDeliveryScore(supplierId: string): Promise<number> {
    // Get purchase orders from last 6 months
    const sixMonthsAgo = subMonths(new Date(), 6);
    
    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: {
        supplierId,
        status: 'RECEIVED',
        orderDate: { gte: sixMonthsAgo },
        expectedDeliveryDate: { not: null },
        receivedAt: { not: null },
      },
    });

    if (purchaseOrders.length === 0) {
      return 75; // Default neutral score for suppliers without delivery history
    }

    let onTimeDeliveries = 0;
    let totalDeliveryDays = 0;

    for (const po of purchaseOrders) {
      if (po.expectedDeliveryDate && po.receivedAt) {
        const expectedDays = differenceInDays(po.expectedDeliveryDate, po.orderDate);
        const actualDays = differenceInDays(po.receivedAt, po.orderDate);
        
        totalDeliveryDays += actualDays;
        
        // Consider on-time if delivered within 2 days of expected date
        if (actualDays <= expectedDays + 2) {
          onTimeDeliveries++;
        }
      }
    }

    const onTimeRate = (onTimeDeliveries / purchaseOrders.length) * 100;
    const avgDeliveryDays = totalDeliveryDays / purchaseOrders.length;

    // Score based on on-time delivery rate (70%) and average delivery speed (30%)
    const onTimeScore = onTimeRate;
    const speedScore = Math.max(0, 100 - (avgDeliveryDays - 7) * 5); // Penalty for deliveries > 7 days
    
    return Math.round(onTimeScore * 0.7 + speedScore * 0.3);
  }

  /**
   * Calculate MOQ compatibility score (0-100)
   */
  private static calculateMOQScore(productSupplier: any, requestedQuantity: number): number {
    const moq = productSupplier.minimumOrderQuantity;
    
    if (!moq || moq <= 0) {
      return 100; // No MOQ restriction
    }

    if (requestedQuantity >= moq) {
      return 100; // Meets MOQ requirement
    }

    // Penalty based on how far below MOQ the request is
    const shortfall = moq - requestedQuantity;
    const penaltyPercentage = Math.min(80, (shortfall / moq) * 100);
    
    return Math.round(100 - penaltyPercentage);
  }

  /**
   * Calculate preferred supplier bonus score (0-100)
   */
  private static calculatePreferredScore(productSupplier: any): number {
    return productSupplier.isPreferred ? 100 : 50;
  }

  /**
   * Generate reasoning for supplier scores
   */
  private static generateScoreReasoning(params: {
    priceScore: number;
    qualityScore: number;
    deliveryScore: number;
    moqScore: number;
    preferredScore: number;
    supplier: any;
    productSupplier: any;
  }): string[] {
    const reasoning: string[] = [];
    const { priceScore, qualityScore, deliveryScore, moqScore, preferredScore, supplier, productSupplier } = params;

    // Price reasoning
    if (priceScore >= 90) {
      reasoning.push(`Excellent price competitiveness (${priceScore}/100)`);
    } else if (priceScore >= 70) {
      reasoning.push(`Good price competitiveness (${priceScore}/100)`);
    } else {
      reasoning.push(`Higher priced compared to alternatives (${priceScore}/100)`);
    }

    // Quality reasoning
    if (qualityScore >= 90) {
      reasoning.push(`Excellent quality track record (${qualityScore}/100)`);
    } else if (qualityScore >= 70) {
      reasoning.push(`Good quality performance (${qualityScore}/100)`);
    } else {
      reasoning.push(`Quality concerns based on return history (${qualityScore}/100)`);
    }

    // Delivery reasoning
    if (deliveryScore >= 90) {
      reasoning.push(`Excellent delivery reliability (${deliveryScore}/100)`);
    } else if (deliveryScore >= 70) {
      reasoning.push(`Good delivery performance (${deliveryScore}/100)`);
    } else {
      reasoning.push(`Delivery reliability concerns (${deliveryScore}/100)`);
    }

    // MOQ reasoning
    if (moqScore === 100) {
      if (productSupplier.minimumOrderQuantity) {
        reasoning.push(`Meets minimum order quantity requirement`);
      } else {
        reasoning.push(`No minimum order quantity restrictions`);
      }
    } else {
      reasoning.push(`Below minimum order quantity (${productSupplier.minimumOrderQuantity} required)`);
    }

    // Preferred status
    if (productSupplier.isPreferred) {
      reasoning.push(`Designated as preferred supplier`);
    }

    return reasoning;
  }

  /**
   * Generate overall recommendation with confidence level
   */
  private static generateRecommendation(
    recommended: SupplierScore,
    alternatives: SupplierScore[],
    requestedQuantity: number
  ): {
    confidence: 'high' | 'medium' | 'low';
    reasoning: string[];
    warnings: string[];
  } {
    const reasoning: string[] = [];
    const warnings: string[] = [];
    let confidence: 'high' | 'medium' | 'low' = 'medium';

    // Determine confidence based on score gap and absolute score
    const scoreGap = alternatives.length > 0 ? recommended.totalScore - alternatives[0].totalScore : 0;

    if (recommended.totalScore >= 85 && scoreGap >= 10) {
      confidence = 'high';
      reasoning.push(`Strong recommendation with ${recommended.totalScore}/100 score`);
    } else if (recommended.totalScore >= 70) {
      confidence = 'medium';
      reasoning.push(`Good option with ${recommended.totalScore}/100 score`);
    } else {
      confidence = 'low';
      reasoning.push(`Best available option but with concerns (${recommended.totalScore}/100 score)`);
      warnings.push(`All suppliers have relatively low scores - consider reviewing supplier options`);
    }

    // Add specific reasoning based on strengths
    if (recommended.breakdown.priceScore >= 90) {
      reasoning.push(`Most cost-effective option`);
    }
    if (recommended.breakdown.qualityScore >= 90) {
      reasoning.push(`Excellent quality track record`);
    }
    if (recommended.breakdown.deliveryScore >= 90) {
      reasoning.push(`Reliable delivery performance`);
    }

    // Add warnings for potential issues
    if (recommended.breakdown.moqScore < 100) {
      warnings.push(`Order quantity (${requestedQuantity}) is below supplier's minimum order quantity`);
    }
    if (recommended.breakdown.qualityScore < 60) {
      warnings.push(`Quality concerns - high return/defect rates with this supplier`);
    }
    if (recommended.breakdown.deliveryScore < 60) {
      warnings.push(`Delivery reliability concerns - frequent delays with this supplier`);
    }

    return { confidence, reasoning, warnings };
  }

  /**
   * Get detailed supplier metrics for display
   */
  static async getSupplierMetrics(supplierId: string): Promise<{
    qualityMetrics?: any;
    deliveryMetrics?: any;
  }> {
    try {
      // Get quality metrics
      const qualityMetrics = await calculateSupplierQualityMetrics(
        supplierId,
        subMonths(new Date(), 6),
        new Date()
      );

      // Get delivery metrics
      const deliveryMetrics = await this.calculateDetailedDeliveryMetrics(supplierId);

      return {
        qualityMetrics: qualityMetrics.metrics,
        deliveryMetrics,
      };
    } catch (error) {
      console.warn(`Could not get supplier metrics for ${supplierId}:`, error);
      return {};
    }
  }

  /**
   * Calculate detailed delivery metrics for display
   */
  private static async calculateDetailedDeliveryMetrics(supplierId: string) {
    const sixMonthsAgo = subMonths(new Date(), 6);

    const purchaseOrders = await prisma.purchaseOrder.findMany({
      where: {
        supplierId,
        status: 'RECEIVED',
        orderDate: { gte: sixMonthsAgo },
        expectedDeliveryDate: { not: null },
        receivedAt: { not: null },
      },
    });

    if (purchaseOrders.length === 0) {
      return null;
    }

    let onTimeDeliveries = 0;
    let totalDeliveryDays = 0;

    for (const po of purchaseOrders) {
      if (po.expectedDeliveryDate && po.receivedAt) {
        const expectedDays = differenceInDays(po.expectedDeliveryDate, po.orderDate);
        const actualDays = differenceInDays(po.receivedAt, po.orderDate);

        totalDeliveryDays += actualDays;

        if (actualDays <= expectedDays + 2) {
          onTimeDeliveries++;
        }
      }
    }

    return {
      averageDeliveryDays: Math.round(totalDeliveryDays / purchaseOrders.length),
      onTimeDeliveryRate: Math.round((onTimeDeliveries / purchaseOrders.length) * 100),
      totalDeliveries: purchaseOrders.length,
    };
  }
}
