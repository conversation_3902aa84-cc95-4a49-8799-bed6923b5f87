/**
 * Authentication System Test Script
 * 
 * This script can be run manually to test the authentication system.
 * It tests password hashing, verification, and role-based permissions.
 * 
 * Run with: npx ts-node --compiler-options '{"module":"CommonJS"}' src/lib/auth-test.ts
 */

import { hashPassword, verifyPassword, hasPermission, rolePermissions } from '@/auth';

async function testPasswordUtils() {
  console.log('\n=== Testing Password Utilities ===');
  
  const testPassword = 'TestPassword123!';
  
  // Test password hashing
  console.log('\nTesting password hashing...');
  const hashedPassword = await hashPassword(testPassword);
  console.log(`Original password: ${testPassword}`);
  console.log(`Hashed password: ${hashedPassword}`);
  console.log(`Hash length: ${hashedPassword.length}`);
  console.log(`Is hash different from original? ${hashedPassword !== testPassword ? 'Yes' : 'No'}`);
  
  // Test password verification
  console.log('\nTesting password verification...');
  const isValid = await verifyPassword(testPassword, hashedPassword);
  console.log(`Correct password verification: ${isValid ? 'Passed' : 'Failed'}`);
  
  const isInvalidValid = await verifyPassword('WrongPassword123!', hashedPassword);
  console.log(`Incorrect password verification: ${!isInvalidValid ? 'Passed' : 'Failed'}`);
  
  // Test hash uniqueness
  console.log('\nTesting hash uniqueness...');
  const hash1 = await hashPassword(testPassword);
  const hash2 = await hashPassword(testPassword);
  console.log(`Hash 1: ${hash1}`);
  console.log(`Hash 2: ${hash2}`);
  console.log(`Are hashes different? ${hash1 !== hash2 ? 'Yes' : 'No'}`);
  
  const isValid1 = await verifyPassword(testPassword, hash1);
  const isValid2 = await verifyPassword(testPassword, hash2);
  console.log(`Both verify correctly? ${isValid1 && isValid2 ? 'Yes' : 'No'}`);
}

function testRolePermissions() {
  console.log('\n=== Testing Role-Based Permissions ===');
  
  // Test permission definitions
  console.log('\nTesting permission definitions...');
  const roles = ['SUPER_ADMIN', 'CASHIER', 'FINANCE_ADMIN', 'WAREHOUSE_ADMIN', 'MARKETING'];
  
  for (const role of roles) {
    const permissions = rolePermissions[role as keyof typeof rolePermissions];
    console.log(`Role ${role} has ${permissions.length} permissions defined`);
  }
  
  // Test SUPER_ADMIN permissions
  console.log('\nTesting SUPER_ADMIN permissions...');
  console.log(`SUPER_ADMIN has wildcard permission? ${rolePermissions.SUPER_ADMIN.includes('*') ? 'Yes' : 'No'}`);
  console.log(`SUPER_ADMIN can access any.permission? ${hasPermission('SUPER_ADMIN', 'any.permission') ? 'Yes' : 'No'}`);
  
  // Test CASHIER permissions
  console.log('\nTesting CASHIER permissions...');
  console.log(`CASHIER can access pos.access? ${hasPermission('CASHIER', 'pos.access') ? 'Yes' : 'No'}`);
  console.log(`CASHIER can access customers.view? ${hasPermission('CASHIER', 'customers.view') ? 'Yes' : 'No'}`);
  console.log(`CASHIER can access admin.access? ${hasPermission('CASHIER', 'admin.access') ? 'Yes' : 'No'}`);
  
  // Test FINANCE_ADMIN permissions
  console.log('\nTesting FINANCE_ADMIN permissions...');
  console.log(`FINANCE_ADMIN can access transactions.view? ${hasPermission('FINANCE_ADMIN', 'transactions.view') ? 'Yes' : 'No'}`);
  console.log(`FINANCE_ADMIN can access reports.view? ${hasPermission('FINANCE_ADMIN', 'reports.view') ? 'Yes' : 'No'}`);
  console.log(`FINANCE_ADMIN can access pos.access? ${hasPermission('FINANCE_ADMIN', 'pos.access') ? 'Yes' : 'No'}`);
  
  // Test WAREHOUSE_ADMIN permissions
  console.log('\nTesting WAREHOUSE_ADMIN permissions...');
  console.log(`WAREHOUSE_ADMIN can access inventory.view? ${hasPermission('WAREHOUSE_ADMIN', 'inventory.view') ? 'Yes' : 'No'}`);
  console.log(`WAREHOUSE_ADMIN can access products.view? ${hasPermission('WAREHOUSE_ADMIN', 'products.view') ? 'Yes' : 'No'}`);
  console.log(`WAREHOUSE_ADMIN can access transactions.view? ${hasPermission('WAREHOUSE_ADMIN', 'transactions.view') ? 'Yes' : 'No'}`);
  
  // Test MARKETING permissions
  console.log('\nTesting MARKETING permissions...');
  console.log(`MARKETING can access products.view? ${hasPermission('MARKETING', 'products.view') ? 'Yes' : 'No'}`);
  console.log(`MARKETING can access customers.view? ${hasPermission('MARKETING', 'customers.view') ? 'Yes' : 'No'}`);
  console.log(`MARKETING can access products.edit? ${hasPermission('MARKETING', 'products.edit') ? 'Yes' : 'No'}`);
}

async function runTests() {
  console.log('=== Authentication System Tests ===');
  
  try {
    await testPasswordUtils();
    testRolePermissions();
    
    console.log('\n=== All Tests Completed ===');
  } catch (error) {
    console.error('Error running tests:', error);
  }
}

runTests();
