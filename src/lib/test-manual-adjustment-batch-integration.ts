import { prisma } from "@/lib/prisma";
import { BatchManagementService } from "@/lib/batch-management";

/**
 * Test manual stock adjustment integration with batch tracking
 * This test verifies that manual stock adjustments properly update batch quantities
 */
export async function testManualAdjustmentBatchIntegration() {
  console.log("🧪 Testing Manual Stock Adjustment Batch Integration...");

  try {
    // 1. Find a product with existing batches in store location
    const productWithBatches = await prisma.product.findFirst({
      where: {
        stockBatches: {
          some: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 5 }, // Need at least 5 units for testing
            storeStockId: { not: null } // Must be in store location
          }
        }
      },
      include: {
        stockBatches: {
          where: {
            status: 'ACTIVE',
            remainingQuantity: { gt: 0 },
            storeStockId: { not: null }
          },
          orderBy: { receivedDate: 'asc' }
        },
        storeStock: true
      }
    });

    if (!productWithBatches || !productWithBatches.storeStock) {
      console.log("❌ No suitable product found for testing. Need a product with active batches in store location.");
      return { success: false, error: "No suitable test data" };
    }

    console.log(`✅ Found test product: ${productWithBatches.name} (${productWithBatches.sku})`);
    console.log(`   Store stock quantity: ${productWithBatches.storeStock.quantity}`);
    console.log(`   Active batches: ${productWithBatches.stockBatches.length}`);

    // 2. Record initial state
    const initialStockQuantity = Number(productWithBatches.storeStock.quantity);
    const initialBatchTotal = productWithBatches.stockBatches.reduce(
      (sum, batch) => sum + Number(batch.remainingQuantity), 
      0
    );

    console.log(`   Initial stock quantity: ${initialStockQuantity}`);
    console.log(`   Initial batch total: ${initialBatchTotal}`);

    // Verify initial integrity
    if (initialStockQuantity !== initialBatchTotal) {
      console.log(`⚠️  Initial data integrity issue detected: stock (${initialStockQuantity}) ≠ batch total (${initialBatchTotal})`);
    }

    // 3. Test negative adjustment (reduction) - this should trigger batch consumption
    const reductionAmount = -3; // Reduce by 3 units
    console.log(`\n🔍 Testing negative adjustment: ${reductionAmount} units`);

    // Simulate the batch adjustment process
    const batchAdjustmentResult = await BatchManagementService.processBatchAdjustment(
      productWithBatches.id,
      reductionAmount,
      'store',
      prisma, // Use prisma directly for testing (not in a transaction)
      {
        source: "ADJUSTMENT",
        referenceType: "StockAdjustment",
        notes: "Test manual adjustment - batch integration verification",
        userId: "test-user-id",
        reason: "THEFT"
      }
    );

    console.log(`   Batch adjustment result:`, batchAdjustmentResult);

    // 4. Verify the batch adjustment worked correctly
    if (batchAdjustmentResult.success) {
      console.log(`✅ Batch adjustment successful`);
      console.log(`   Batches affected: ${batchAdjustmentResult.batchesAffected}`);
      console.log(`   Total adjusted: ${batchAdjustmentResult.totalAdjusted}`);

      // Check updated batch quantities
      const updatedBatches = await prisma.stockBatch.findMany({
        where: {
          productId: productWithBatches.id,
          storeStockId: { not: null },
          status: 'ACTIVE'
        },
        orderBy: { receivedDate: 'asc' }
      });

      const newBatchTotal = updatedBatches.reduce(
        (sum, batch) => sum + Number(batch.remainingQuantity), 
        0
      );

      console.log(`   Updated batch total: ${newBatchTotal}`);
      console.log(`   Expected batch total: ${initialBatchTotal + reductionAmount}`);

      if (newBatchTotal === initialBatchTotal + reductionAmount) {
        console.log(`✅ Batch quantities updated correctly`);
      } else {
        console.log(`❌ Batch quantity mismatch: expected ${initialBatchTotal + reductionAmount}, got ${newBatchTotal}`);
      }

      // 5. Test data integrity after adjustment
      const integrityCheck = await BatchManagementService.validateStockBatchIntegrity(
        productWithBatches.id,
        'store'
      );

      console.log(`\n🔍 Post-adjustment integrity check:`);
      console.log(`   Stock quantity: ${integrityCheck.stockQuantity}`);
      console.log(`   Batch total: ${integrityCheck.batchTotalQuantity}`);
      console.log(`   Difference: ${integrityCheck.difference}`);
      console.log(`   Integrity valid: ${integrityCheck.isValid ? '✅' : '❌'}`);

      return {
        success: true,
        productTested: {
          id: productWithBatches.id,
          name: productWithBatches.name,
          sku: productWithBatches.sku
        },
        initialState: {
          stockQuantity: initialStockQuantity,
          batchTotal: initialBatchTotal
        },
        adjustmentResult: batchAdjustmentResult,
        finalIntegrity: integrityCheck
      };

    } else {
      console.log(`❌ Batch adjustment failed:`, batchAdjustmentResult);
      return { success: false, error: "Batch adjustment failed", details: batchAdjustmentResult };
    }

  } catch (error) {
    console.error("❌ Manual Adjustment Batch Integration Test failed:", error);
    return {
      success: false,
      error: (error as Error).message
    };
  }
}

/**
 * Test positive adjustment handling
 */
export async function testPositiveAdjustmentHandling() {
  console.log("\n🧪 Testing Positive Adjustment Handling...");

  try {
    // Find any product for testing positive adjustments
    const product = await prisma.product.findFirst({
      include: {
        storeStock: true
      }
    });

    if (!product || !product.storeStock) {
      console.log("❌ No suitable product found for positive adjustment testing.");
      return { success: false, error: "No suitable test data" };
    }

    console.log(`✅ Testing positive adjustment on: ${product.name}`);

    // Test positive adjustment (should not affect batches directly)
    const positiveAdjustment = 5;
    const batchAdjustmentResult = await BatchManagementService.processBatchAdjustment(
      product.id,
      positiveAdjustment,
      'store',
      prisma,
      {
        source: "ADJUSTMENT",
        referenceType: "StockAdjustment",
        notes: "Test positive adjustment",
        userId: "test-user-id",
        reason: "RETURN"
      }
    );

    console.log(`   Positive adjustment result:`, batchAdjustmentResult);

    if (batchAdjustmentResult.success && batchAdjustmentResult.batchesAffected === 0) {
      console.log(`✅ Positive adjustment handled correctly (no batch changes)`);
      return { success: true, result: batchAdjustmentResult };
    } else {
      console.log(`❌ Unexpected positive adjustment behavior`);
      return { success: false, error: "Unexpected positive adjustment behavior" };
    }

  } catch (error) {
    console.error("❌ Positive Adjustment Test failed:", error);
    return { success: false, error: (error as Error).message };
  }
}

/**
 * Run all manual adjustment batch integration tests
 */
export async function runAllManualAdjustmentTests() {
  console.log("🚀 Running All Manual Adjustment Batch Integration Tests\n");

  const results = [];

  // Test 1: Negative adjustment with batch integration
  results.push(await testManualAdjustmentBatchIntegration());

  // Test 2: Positive adjustment handling
  results.push(await testPositiveAdjustmentHandling());

  // Summary
  const successCount = results.filter(r => r.success).length;
  const totalTests = results.length;

  console.log(`\n📊 Test Summary: ${successCount}/${totalTests} tests passed`);

  if (successCount === totalTests) {
    console.log("✅ All manual adjustment batch integration tests passed!");
  } else {
    console.log("❌ Some tests failed. Check the logs above for details.");
  }

  return {
    success: successCount === totalTests,
    results,
    summary: {
      total: totalTests,
      passed: successCount,
      failed: totalTests - successCount
    }
  };
}
