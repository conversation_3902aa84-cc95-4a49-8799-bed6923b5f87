import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { calculateWeightedAverageCost, calculateInventoryValuationWithSuppliers } from '../supplier-cost-calculations';

// Mock Prisma
const mockPrisma = {
  stockBatch: {
    findMany: jest.fn(),
  },
  product: {
    findMany: jest.fn(),
  },
};

// Mock the prisma import
jest.mock('../prisma', () => ({
  prisma: mockPrisma,
}));

describe('Supplier Cost Calculations', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('calculateWeightedAverageCost', () => {
    it('should calculate weighted average cost correctly for multiple suppliers', async () => {
      const mockBatches = [
        {
          id: '1',
          remainingQuantity: 10,
          purchasePrice: 100,
          receivedDate: new Date('2024-01-01'),
          status: 'ACTIVE',
          productSupplier: {
            supplier: {
              id: 'supplier1',
              name: 'Supplier A'
            }
          }
        },
        {
          id: '2',
          remainingQuantity: 20,
          purchasePrice: 150,
          receivedDate: new Date('2024-01-02'),
          status: 'ACTIVE',
          productSupplier: {
            supplier: {
              id: 'supplier2',
              name: 'Supplier B'
            }
          }
        }
      ];

      mockPrisma.stockBatch.findMany.mockResolvedValue(mockBatches);

      const result = await calculateWeightedAverageCost('product1');

      expect(result.totalQuantity).toBe(30);
      expect(result.totalValue).toBe(4000); // (10 * 100) + (20 * 150)
      expect(result.weightedAverageCost).toBeCloseTo(133.33, 2); // 4000 / 30
      expect(result.supplierBreakdown).toHaveLength(2);
    });

    it('should return zero values when no batches exist', async () => {
      mockPrisma.stockBatch.findMany.mockResolvedValue([]);

      const result = await calculateWeightedAverageCost('product1');

      expect(result.totalQuantity).toBe(0);
      expect(result.totalValue).toBe(0);
      expect(result.weightedAverageCost).toBe(0);
      expect(result.supplierBreakdown).toHaveLength(0);
    });

    it('should group batches by supplier correctly', async () => {
      const mockBatches = [
        {
          id: '1',
          remainingQuantity: 10,
          purchasePrice: 100,
          receivedDate: new Date('2024-01-01'),
          status: 'ACTIVE',
          productSupplier: {
            supplier: {
              id: 'supplier1',
              name: 'Supplier A'
            }
          }
        },
        {
          id: '2',
          remainingQuantity: 15,
          purchasePrice: 120,
          receivedDate: new Date('2024-01-02'),
          status: 'ACTIVE',
          productSupplier: {
            supplier: {
              id: 'supplier1',
              name: 'Supplier A'
            }
          }
        }
      ];

      mockPrisma.stockBatch.findMany.mockResolvedValue(mockBatches);

      const result = await calculateWeightedAverageCost('product1');

      expect(result.supplierBreakdown).toHaveLength(1);
      expect(result.supplierBreakdown[0].supplierName).toBe('Supplier A');
      expect(result.supplierBreakdown[0].totalQuantity).toBe(25);
      expect(result.supplierBreakdown[0].totalValue).toBe(2800); // (10 * 100) + (15 * 120)
      expect(result.supplierBreakdown[0].batchCount).toBe(2);
    });
  });

  describe('calculateInventoryValuationWithSuppliers', () => {
    it('should calculate inventory valuation with supplier breakdown', async () => {
      const mockProducts = [
        {
          id: 'product1',
          name: 'Test Product',
          sku: 'TEST001',
          active: true,
          category: { name: 'Test Category' },
          unit: { abbreviation: 'pcs' },
          storeStock: { quantity: 50 },
          warehouseStock: { quantity: 30 }
        }
      ];

      const mockBatches = [
        {
          id: '1',
          remainingQuantity: 80,
          purchasePrice: 100,
          receivedDate: new Date('2024-01-01'),
          status: 'ACTIVE',
          productSupplier: {
            supplier: {
              id: 'supplier1',
              name: 'Supplier A'
            }
          }
        }
      ];

      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.stockBatch.findMany.mockResolvedValue(mockBatches);

      const result = await calculateInventoryValuationWithSuppliers();

      expect(result).toHaveLength(1);
      expect(result[0].productName).toBe('Test Product');
      expect(result[0].totalQuantity).toBe(80);
      expect(result[0].weightedAverageCost).toBe(100);
      expect(result[0].totalValue).toBe(8000); // 80 * 100
      expect(result[0].supplierBreakdown).toHaveLength(1);
    });

    it('should filter by category when provided', async () => {
      const mockProducts = [
        {
          id: 'product1',
          name: 'Test Product',
          sku: 'TEST001',
          active: true,
          categoryId: 'category1',
          category: { name: 'Test Category' },
          unit: { abbreviation: 'pcs' },
          storeStock: { quantity: 50 },
          warehouseStock: { quantity: 30 }
        }
      ];

      mockPrisma.product.findMany.mockResolvedValue(mockProducts);
      mockPrisma.stockBatch.findMany.mockResolvedValue([]);

      await calculateInventoryValuationWithSuppliers('category1');

      expect(mockPrisma.product.findMany).toHaveBeenCalledWith({
        where: { active: true, categoryId: 'category1' },
        include: {
          category: true,
          unit: true,
          storeStock: true,
          warehouseStock: true
        },
        orderBy: {
          name: 'asc'
        }
      });
    });
  });
});

// Helper to create mock data
function createMockBatch(overrides = {}) {
  return {
    id: 'batch1',
    remainingQuantity: 10,
    purchasePrice: 100,
    receivedDate: new Date(),
    status: 'ACTIVE',
    productSupplier: {
      supplier: {
        id: 'supplier1',
        name: 'Test Supplier'
      }
    },
    ...overrides
  };
}

function createMockProduct(overrides = {}) {
  return {
    id: 'product1',
    name: 'Test Product',
    sku: 'TEST001',
    active: true,
    category: { name: 'Test Category' },
    unit: { abbreviation: 'pcs' },
    storeStock: { quantity: 50 },
    warehouseStock: { quantity: 30 },
    ...overrides
  };
}
