import { SupplierSelectionEngine } from '../supplier-selection-engine';

// Mock Prisma
jest.mock('../prisma', () => ({
  prisma: {
    product: {
      findUnique: jest.fn(),
    },
    productSupplier: {
      findMany: jest.fn(),
    },
    purchaseOrder: {
      findMany: jest.fn(),
    },
    supplier: {
      findUnique: jest.fn(),
    },
  },
}));

// Mock supplier quality metrics
jest.mock('../supplier-quality-metrics', () => ({
  calculateSupplierQualityMetrics: jest.fn().mockResolvedValue({
    metrics: {
      qualityScore: 85,
      returnRate: 2.5,
      defectRate: 1.2,
    },
  }),
}));

describe('SupplierSelectionEngine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getSupplierRecommendation', () => {
    it('should return null when product has no suppliers', async () => {
      const { prisma } = require('../prisma');
      
      prisma.product.findUnique.mockResolvedValue({
        id: 'product-1',
        name: 'Test Product',
        productSuppliers: [],
      });

      const result = await SupplierSelectionEngine.getSupplierRecommendation('product-1', 10);
      
      expect(result).toBeNull();
    });

    it('should return recommendation when product has suppliers', async () => {
      const { prisma } = require('../prisma');
      
      const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        productSuppliers: [
          {
            id: 'ps-1',
            productId: 'product-1',
            supplierId: 'supplier-1',
            purchasePrice: 100,
            minimumOrderQuantity: 5,
            leadTimeDays: 7,
            isPreferred: true,
            isActive: true,
            supplier: {
              id: 'supplier-1',
              name: 'Supplier A',
            },
          },
          {
            id: 'ps-2',
            productId: 'product-1',
            supplierId: 'supplier-2',
            purchasePrice: 120,
            minimumOrderQuantity: 10,
            leadTimeDays: 5,
            isPreferred: false,
            isActive: true,
            supplier: {
              id: 'supplier-2',
              name: 'Supplier B',
            },
          },
        ],
      };

      prisma.product.findUnique.mockResolvedValue(mockProduct);
      
      // Mock price comparison data
      prisma.productSupplier.findMany.mockResolvedValue(mockProduct.productSuppliers);
      
      // Mock delivery data
      prisma.purchaseOrder.findMany.mockResolvedValue([]);

      const result = await SupplierSelectionEngine.getSupplierRecommendation('product-1', 10);
      
      expect(result).not.toBeNull();
      expect(result?.productId).toBe('product-1');
      expect(result?.productName).toBe('Test Product');
      expect(result?.requestedQuantity).toBe(10);
      expect(result?.recommendedSupplier).toBeDefined();
      expect(result?.alternativeSuppliers).toBeDefined();
      expect(result?.recommendation).toBeDefined();
    });

    it('should prefer suppliers with better scores', async () => {
      const { prisma } = require('../prisma');
      
      const mockProduct = {
        id: 'product-1',
        name: 'Test Product',
        productSuppliers: [
          {
            id: 'ps-1',
            productId: 'product-1',
            supplierId: 'supplier-1',
            purchasePrice: 150, // Higher price
            minimumOrderQuantity: 5,
            leadTimeDays: 7,
            isPreferred: false, // Not preferred
            isActive: true,
            supplier: {
              id: 'supplier-1',
              name: 'Expensive Supplier',
            },
          },
          {
            id: 'ps-2',
            productId: 'product-1',
            supplierId: 'supplier-2',
            purchasePrice: 100, // Lower price
            minimumOrderQuantity: 5,
            leadTimeDays: 5,
            isPreferred: true, // Preferred
            isActive: true,
            supplier: {
              id: 'supplier-2',
              name: 'Good Supplier',
            },
          },
        ],
      };

      prisma.product.findUnique.mockResolvedValue(mockProduct);
      prisma.productSupplier.findMany.mockResolvedValue(mockProduct.productSuppliers);
      prisma.purchaseOrder.findMany.mockResolvedValue([]);

      const result = await SupplierSelectionEngine.getSupplierRecommendation('product-1', 10);
      
      expect(result?.recommendedSupplier.supplierName).toBe('Good Supplier');
      expect(result?.recommendedSupplier.totalScore).toBeGreaterThan(
        result?.alternativeSuppliers[0]?.totalScore || 0
      );
    });
  });

  describe('getBulkSupplierRecommendations', () => {
    it('should return recommendations for multiple products', async () => {
      const { prisma } = require('../prisma');
      
      const mockProducts = [
        {
          id: 'product-1',
          name: 'Product 1',
          productSuppliers: [
            {
              id: 'ps-1',
              productId: 'product-1',
              supplierId: 'supplier-1',
              purchasePrice: 100,
              minimumOrderQuantity: 5,
              isPreferred: true,
              supplier: { id: 'supplier-1', name: 'Supplier A' },
            },
          ],
        },
        {
          id: 'product-2',
          name: 'Product 2',
          productSuppliers: [
            {
              id: 'ps-2',
              productId: 'product-2',
              supplierId: 'supplier-2',
              purchasePrice: 200,
              minimumOrderQuantity: 10,
              isPreferred: true,
              supplier: { id: 'supplier-2', name: 'Supplier B' },
            },
          ],
        },
      ];

      prisma.product.findUnique
        .mockResolvedValueOnce(mockProducts[0])
        .mockResolvedValueOnce(mockProducts[1]);
      
      prisma.productSupplier.findMany
        .mockResolvedValueOnce(mockProducts[0].productSuppliers)
        .mockResolvedValueOnce(mockProducts[1].productSuppliers);
      
      prisma.purchaseOrder.findMany.mockResolvedValue([]);

      const items = [
        { productId: 'product-1', quantity: 10 },
        { productId: 'product-2', quantity: 5 },
      ];

      const result = await SupplierSelectionEngine.getBulkSupplierRecommendations(items);
      
      expect(result).toHaveLength(2);
      expect(result[0].productId).toBe('product-1');
      expect(result[1].productId).toBe('product-2');
    });
  });
});
