import type { Purchase<PERSON>rde<PERSON>, P<PERSON>tatus, StockBatch, PurchaseOrderItem } from "@prisma/client";
import { differenceInDays } from "date-fns";

interface PerformanceMetrics {
  performanceScore: number;
  qualityScore: number;
  supplierScore: number;
  hasDelays: boolean;
  delayReason?: string;
  batchMetrics: {
    totalBatches: number;
    activeBatches: number;
    expiringBatches: number;
    expiredBatches: number;
  };
}

type POWithBatches = PurchaseOrder & {
  items: PurchaseOrderItem[];
  stockBatches: StockBatch[];
};

/**
 * Calculate comprehensive performance metrics for a purchase order
 * This function supports both Priority 3 (Status Management) and Priority 4 (Analytics) requirements
 */
export async function calculatePerformanceMetrics(
  po: POWithBatches,
  newStatus: POStatus
): Promise<PerformanceMetrics> {
  // Initialize metrics
  let performanceScore = 100;
  let qualityScore = 100;
  let supplierScore = 100;
  let hasDelays = false;
  let delayReason: string | undefined;

  // Calculate batch-related metrics
  const batchMetrics = calculateBatchMetrics(po);

  // Calculate delay-related metrics
  if (po.expectedDeliveryDate) {
    const daysUntilDelivery = differenceInDays(
      po.expectedDeliveryDate,
      new Date()
    );

    if (daysUntilDelivery < 0) {
      hasDelays = true;
      delayReason = "Past expected delivery date";
      performanceScore -= Math.min(Math.abs(daysUntilDelivery) * 5, 50); // Deduct up to 50 points
    }
  }

  // Calculate quality-based metrics
  if (po.stockBatches && po.stockBatches.length > 0) {
    const qualityIssues = po.stockBatches.filter(batch => batch.status === "RECALLED");

    if (qualityIssues.length > 0) {
      qualityScore -= qualityIssues.length * 20; // Deduct 20 points per quality issue
    }
  }

  // Calculate supplier performance score
  supplierScore = calculateSupplierScore(po, {
    hasDelays,
    qualityScore,
    batchMetrics
  });

  return {
    performanceScore: Math.max(0, performanceScore),
    qualityScore: Math.max(0, qualityScore),
    supplierScore: Math.max(0, supplierScore),
    hasDelays,
    delayReason,
    batchMetrics
  };
}

/**
 * Calculate batch-related metrics
 * This function directly supports Priority 4 requirements
 */
function calculateBatchMetrics(po: POWithBatches): {
  totalBatches: number;
  activeBatches: number;
  expiringBatches: number;
  expiredBatches: number;
} {
  const now = new Date();
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

  const batches = po.stockBatches || [];

  return {
    totalBatches: batches.length,
    activeBatches: batches.filter(batch => batch.status === "ACTIVE").length,
    expiringBatches: batches.filter(batch => 
      batch.status === "ACTIVE" && 
      batch.expiryDate && 
      batch.expiryDate <= thirtyDaysFromNow &&
      batch.expiryDate > now
    ).length,
    expiredBatches: batches.filter(batch => 
      batch.expiryDate && 
      batch.expiryDate <= now
    ).length
  };
}

/**
 * Calculate supplier performance score
 * This function supports both current status management and future analytics
 */
function calculateSupplierScore(
  po: POWithBatches,
  metrics: {
    hasDelays: boolean;
    qualityScore: number;
    batchMetrics: {
      totalBatches: number;
      expiredBatches: number;
    };
  }
): number {
  let score = 100;

  // Deduct points for delays
  if (metrics.hasDelays) {
    score -= 30;
  }

  // Deduct points for quality issues
  if (metrics.qualityScore < 100) {
    score -= (100 - metrics.qualityScore) * 0.3;
  }

  // Deduct points for expired batches ratio
  if (metrics.batchMetrics.totalBatches > 0) {
    const expiredRatio = metrics.batchMetrics.expiredBatches / metrics.batchMetrics.totalBatches;
    score -= expiredRatio * 20;
  }

  return Math.max(0, score);
} 