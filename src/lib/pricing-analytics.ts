import { prisma } from "@/auth";

export interface PricingMetrics {
  averagePriceChange: number; // Percentage change over time
  priceVolatility: number; // Standard deviation of price changes
  competitivenessScore: number; // Compared to market average
  currentAveragePrice: number;
  marketAveragePrice: number;
  priceRank: number; // 1 = cheapest, higher = more expensive
  totalSuppliers: number;
  priceTrends: Array<{
    productId: string;
    productName: string;
    productSku: string;
    currentPrice: number;
    marketAverage: number;
    competitivenessScore: number;
    priceHistory: Array<{
      date: string;
      price: number;
      changePercentage: number;
    }>;
  }>;
  monthlyPriceTrends: Array<{
    month: string;
    averagePrice: number;
    changePercentage: number;
    marketAverage: number;
  }>;
}

export interface SupplierPricingAnalysis {
  supplierId: string;
  supplierName: string;
  timeRange: string;
  metrics: PricingMetrics;
  recommendations: string[];
  competitivenessLevel: 'excellent' | 'good' | 'average' | 'poor';
}

/**
 * Calculate comprehensive pricing metrics for a supplier
 */
export async function calculateSupplierPricingMetrics(
  supplierId: string,
  startDate?: Date,
  endDate?: Date
): Promise<SupplierPricingAnalysis> {
  const dateFilter: any = {};
  if (startDate) dateFilter.gte = startDate;
  if (endDate) dateFilter.lte = endDate;

  // Get supplier information
  const supplier = await prisma.supplier.findUnique({
    where: { id: supplierId },
    select: { id: true, name: true }
  });

  if (!supplier) {
    throw new Error("Supplier not found");
  }

  // Get supplier's product pricing history
  const supplierProducts = await prisma.productSupplier.findMany({
    where: { 
      supplierId,
      isActive: true
    },
    include: {
      product: {
        select: {
          id: true,
          name: true,
          sku: true
        }
      },
      stockBatches: {
        where: {
          receivedDate: Object.keys(dateFilter).length > 0 ? dateFilter : undefined
        },
        orderBy: {
          receivedDate: 'asc'
        },
        select: {
          purchasePrice: true,
          receivedDate: true,
          quantity: true
        }
      }
    }
  });

  // Get market data for comparison (all suppliers for the same products)
  const marketData = await prisma.productSupplier.findMany({
    where: {
      productId: {
        in: supplierProducts.map(sp => sp.productId)
      },
      isActive: true
    },
    include: {
      supplier: {
        select: { id: true, name: true }
      },
      stockBatches: {
        where: {
          receivedDate: Object.keys(dateFilter).length > 0 ? dateFilter : undefined
        },
        select: {
          purchasePrice: true,
          receivedDate: true,
          quantity: true
        }
      }
    }
  });

  // Calculate current average price for this supplier
  const currentPrices = supplierProducts.map(sp => Number(sp.purchasePrice));
  const currentAveragePrice = currentPrices.length > 0 
    ? currentPrices.reduce((sum, price) => sum + price, 0) / currentPrices.length 
    : 0;

  // Calculate market average price for comparison
  const allMarketPrices = marketData.map(md => Number(md.purchasePrice));
  const marketAveragePrice = allMarketPrices.length > 0
    ? allMarketPrices.reduce((sum, price) => sum + price, 0) / allMarketPrices.length
    : 0;

  // Calculate supplier's price rank
  const supplierAverages = new Map<string, number>();
  marketData.forEach(md => {
    if (!supplierAverages.has(md.supplierId)) {
      const supplierPrices = marketData
        .filter(item => item.supplierId === md.supplierId)
        .map(item => Number(item.purchasePrice));
      const avgPrice = supplierPrices.reduce((sum, price) => sum + price, 0) / supplierPrices.length;
      supplierAverages.set(md.supplierId, avgPrice);
    }
  });

  const sortedSuppliers = Array.from(supplierAverages.entries())
    .sort((a, b) => a[1] - b[1]);
  const priceRank = sortedSuppliers.findIndex(([id]) => id === supplierId) + 1;
  const totalSuppliers = sortedSuppliers.length;

  // Calculate price trends for each product
  const priceTrends = supplierProducts.map(sp => {
    const productMarketData = marketData.filter(md => md.productId === sp.productId);
    const productMarketPrices = productMarketData.map(md => Number(md.purchasePrice));
    const productMarketAverage = productMarketPrices.length > 0
      ? productMarketPrices.reduce((sum, price) => sum + price, 0) / productMarketPrices.length
      : 0;

    // Calculate competitiveness score for this product
    const competitivenessScore = productMarketAverage > 0 
      ? Math.max(0, 100 - ((Number(sp.purchasePrice) - productMarketAverage) / productMarketAverage * 100))
      : 100;

    // Calculate price history from stock batches
    const priceHistory = sp.stockBatches.map((batch, index) => {
      const prevBatch = index > 0 ? sp.stockBatches[index - 1] : null;
      const changePercentage = prevBatch 
        ? ((Number(batch.purchasePrice) - Number(prevBatch.purchasePrice)) / Number(prevBatch.purchasePrice)) * 100
        : 0;

      return {
        date: batch.receivedDate.toISOString().split('T')[0],
        price: Number(batch.purchasePrice),
        changePercentage
      };
    });

    return {
      productId: sp.productId,
      productName: sp.product.name,
      productSku: sp.product.sku,
      currentPrice: Number(sp.purchasePrice),
      marketAverage: productMarketAverage,
      competitivenessScore,
      priceHistory
    };
  });

  // Calculate monthly price trends
  const monthlyData = new Map<string, { prices: number[], marketPrices: number[] }>();
  
  supplierProducts.forEach(sp => {
    sp.stockBatches.forEach(batch => {
      const month = batch.receivedDate.toISOString().substring(0, 7); // YYYY-MM
      if (!monthlyData.has(month)) {
        monthlyData.set(month, { prices: [], marketPrices: [] });
      }
      monthlyData.get(month)!.prices.push(Number(batch.purchasePrice));
    });
  });

  // Add market prices for each month
  marketData.forEach(md => {
    md.stockBatches.forEach(batch => {
      const month = batch.receivedDate.toISOString().substring(0, 7);
      if (monthlyData.has(month)) {
        monthlyData.get(month)!.marketPrices.push(Number(batch.purchasePrice));
      }
    });
  });

  const monthlyPriceTrends = Array.from(monthlyData.entries()).map(([month, data]) => {
    const averagePrice = data.prices.reduce((sum, price) => sum + price, 0) / data.prices.length;
    const marketAverage = data.marketPrices.length > 0
      ? data.marketPrices.reduce((sum, price) => sum + price, 0) / data.marketPrices.length
      : 0;
    
    return { month, averagePrice, marketAverage, changePercentage: 0 };
  }).sort((a, b) => a.month.localeCompare(b.month));

  // Calculate change percentages for monthly trends
  monthlyPriceTrends.forEach((trend, index) => {
    if (index > 0) {
      const prevTrend = monthlyPriceTrends[index - 1];
      trend.changePercentage = ((trend.averagePrice - prevTrend.averagePrice) / prevTrend.averagePrice) * 100;
    }
  });

  // Calculate overall metrics
  const priceChanges = monthlyPriceTrends.slice(1).map(trend => trend.changePercentage);
  const averagePriceChange = priceChanges.length > 0
    ? priceChanges.reduce((sum, change) => sum + change, 0) / priceChanges.length
    : 0;

  const priceVolatility = priceChanges.length > 1
    ? Math.sqrt(priceChanges.reduce((sum, change) => sum + Math.pow(change - averagePriceChange, 2), 0) / (priceChanges.length - 1))
    : 0;

  const competitivenessScore = marketAveragePrice > 0
    ? Math.max(0, 100 - ((currentAveragePrice - marketAveragePrice) / marketAveragePrice * 100))
    : 100;

  // Generate recommendations
  const recommendations: string[] = [];
  if (competitivenessScore < 60) recommendations.push("Prices are significantly above market average. Consider renegotiating terms.");
  if (priceVolatility > 10) recommendations.push("High price volatility detected. Review pricing stability with supplier.");
  if (averagePriceChange > 5) recommendations.push("Prices are trending upward. Monitor for cost impact.");
  if (priceRank > totalSuppliers * 0.8) recommendations.push("Supplier ranks in top 20% most expensive. Evaluate alternatives.");

  // Determine competitiveness level
  let competitivenessLevel: 'excellent' | 'good' | 'average' | 'poor' = 'average';
  if (competitivenessScore >= 90) competitivenessLevel = 'excellent';
  else if (competitivenessScore >= 75) competitivenessLevel = 'good';
  else if (competitivenessScore < 50) competitivenessLevel = 'poor';

  const metrics: PricingMetrics = {
    averagePriceChange,
    priceVolatility,
    competitivenessScore,
    currentAveragePrice,
    marketAveragePrice,
    priceRank,
    totalSuppliers,
    priceTrends,
    monthlyPriceTrends
  };

  return {
    supplierId,
    supplierName: supplier.name,
    timeRange: `${startDate?.toISOString().split('T')[0] || 'All time'} - ${endDate?.toISOString().split('T')[0] || 'Present'}`,
    metrics,
    recommendations,
    competitivenessLevel
  };
}
