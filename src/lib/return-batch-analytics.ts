import { prisma } from "@/auth";

export interface BatchReturnAnalysis {
  batchId: string;
  batchNumber: string | null;
  productId: string;
  productName: string;
  supplierId: string;
  supplierName: string;
  totalQuantityReceived: number;
  totalQuantityReturned: number;
  returnRate: number; // Percentage
  returnValue: number;
  returnReasons: Array<{
    reason: string;
    count: number;
    quantity: number;
    value: number;
  }>;
  defectTypes: Array<{
    defectType: string;
    count: number;
    quantity: number;
  }>;
  qualityScore: number; // 0-100, based on return rate and defect severity
}

export interface SupplierReturnTraceability {
  supplierId: string;
  supplierName: string;
  totalBatches: number;
  batchesWithReturns: number;
  totalReturnValue: number;
  averageReturnRate: number;
  topDefectTypes: Array<{
    defectType: string;
    count: number;
    affectedBatches: number;
  }>;
  batchAnalysis: BatchReturnAnalysis[];
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string[];
}

/**
 * Analyze returns by batch to identify supplier quality issues
 */
export async function analyzeBatchReturns(
  supplierId?: string,
  startDate?: Date,
  endDate?: Date
): Promise<BatchReturnAnalysis[]> {
  const dateFilter: any = {};
  if (startDate) dateFilter.gte = startDate;
  if (endDate) dateFilter.lte = endDate;

  // Build where clause for returns
  const returnWhere: any = {
    returnDate: Object.keys(dateFilter).length > 0 ? dateFilter : undefined,
    items: {
      some: {
        batchId: { not: null },
        ...(supplierId ? {
          batch: {
            productSupplier: {
              supplierId: supplierId
            }
          }
        } : {})
      }
    }
  };

  // Get returns with batch information
  const returns = await prisma.return.findMany({
    where: returnWhere,
    include: {
      items: {
        where: {
          batchId: { not: null }
        },
        include: {
          product: {
            select: {
              id: true,
              name: true
            }
          },
          batch: {
            include: {
              productSupplier: {
                include: {
                  supplier: {
                    select: {
                      id: true,
                      name: true
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  });

  // Group by batch and analyze
  const batchMap = new Map<string, {
    batch: any;
    returns: Array<{
      reason: string;
      quantity: number;
      value: number;
      defectType?: string;
      defectNotes?: string;
    }>;
  }>();

  for (const returnRecord of returns) {
    for (const item of returnRecord.items) {
      if (!item.batch) continue;

      const batchId = item.batch.id;
      if (!batchMap.has(batchId)) {
        batchMap.set(batchId, {
          batch: item.batch,
          returns: []
        });
      }

      batchMap.get(batchId)!.returns.push({
        reason: returnRecord.reason,
        quantity: Number(item.quantity),
        value: Number(item.subtotal),
        defectType: item.defectType || undefined,
        defectNotes: item.defectNotes || undefined,
      });
    }
  }

  // Analyze each batch
  const batchAnalyses: BatchReturnAnalysis[] = [];
  for (const [batchId, data] of batchMap) {
    const batch = data.batch;
    const returns = data.returns;

    const totalQuantityReturned = returns.reduce((sum, r) => sum + r.quantity, 0);
    const returnValue = returns.reduce((sum, r) => sum + r.value, 0);
    const totalQuantityReceived = Number(batch.quantity);
    const returnRate = (totalQuantityReturned / totalQuantityReceived) * 100;

    // Group returns by reason
    const reasonMap = new Map<string, { count: number; quantity: number; value: number }>();
    for (const returnItem of returns) {
      const reason = returnItem.reason;
      if (!reasonMap.has(reason)) {
        reasonMap.set(reason, { count: 0, quantity: 0, value: 0 });
      }
      const reasonData = reasonMap.get(reason)!;
      reasonData.count++;
      reasonData.quantity += returnItem.quantity;
      reasonData.value += returnItem.value;
    }

    // Group defects by type
    const defectMap = new Map<string, { count: number; quantity: number }>();
    for (const returnItem of returns) {
      if (returnItem.defectType) {
        const defectType = returnItem.defectType;
        if (!defectMap.has(defectType)) {
          defectMap.set(defectType, { count: 0, quantity: 0 });
        }
        const defectData = defectMap.get(defectType)!;
        defectData.count++;
        defectData.quantity += returnItem.quantity;
      }
    }

    // Calculate quality score (0-100)
    let qualityScore = 100;
    qualityScore -= Math.min(returnRate * 5, 50); // Deduct up to 50 points for return rate
    qualityScore -= Math.min(defectMap.size * 10, 30); // Deduct up to 30 points for defect variety
    qualityScore = Math.max(0, qualityScore);

    batchAnalyses.push({
      batchId,
      batchNumber: batch.batchNumber,
      productId: batch.productId,
      productName: batch.product?.name || 'Unknown Product',
      supplierId: batch.productSupplier.supplier.id,
      supplierName: batch.productSupplier.supplier.name,
      totalQuantityReceived,
      totalQuantityReturned,
      returnRate,
      returnValue,
      returnReasons: Array.from(reasonMap.entries()).map(([reason, data]) => ({
        reason,
        count: data.count,
        quantity: data.quantity,
        value: data.value,
      })),
      defectTypes: Array.from(defectMap.entries()).map(([defectType, data]) => ({
        defectType,
        count: data.count,
        quantity: data.quantity,
      })),
      qualityScore: Math.round(qualityScore),
    });
  }

  return batchAnalyses.sort((a, b) => b.returnRate - a.returnRate);
}

/**
 * Generate supplier return traceability report
 */
export async function generateSupplierReturnTraceability(
  supplierId: string,
  startDate?: Date,
  endDate?: Date
): Promise<SupplierReturnTraceability> {
  const batchAnalyses = await analyzeBatchReturns(supplierId, startDate, endDate);
  
  if (batchAnalyses.length === 0) {
    const supplier = await prisma.supplier.findUnique({
      where: { id: supplierId },
      select: { name: true }
    });

    return {
      supplierId,
      supplierName: supplier?.name || 'Unknown Supplier',
      totalBatches: 0,
      batchesWithReturns: 0,
      totalReturnValue: 0,
      averageReturnRate: 0,
      topDefectTypes: [],
      batchAnalysis: [],
      riskLevel: 'low',
      recommendations: ['No return data available for analysis'],
    };
  }

  const supplierName = batchAnalyses[0].supplierName;
  const totalReturnValue = batchAnalyses.reduce((sum, b) => sum + b.returnValue, 0);
  const averageReturnRate = batchAnalyses.reduce((sum, b) => sum + b.returnRate, 0) / batchAnalyses.length;

  // Aggregate defect types across all batches
  const defectTypeMap = new Map<string, { count: number; affectedBatches: number }>();
  for (const batch of batchAnalyses) {
    const batchDefectTypes = new Set<string>();
    for (const defect of batch.defectTypes) {
      if (!defectTypeMap.has(defect.defectType)) {
        defectTypeMap.set(defect.defectType, { count: 0, affectedBatches: 0 });
      }
      defectTypeMap.get(defect.defectType)!.count += defect.count;
      batchDefectTypes.add(defect.defectType);
    }
    // Count affected batches
    for (const defectType of batchDefectTypes) {
      defectTypeMap.get(defectType)!.affectedBatches++;
    }
  }

  const topDefectTypes = Array.from(defectTypeMap.entries())
    .map(([defectType, data]) => ({
      defectType,
      count: data.count,
      affectedBatches: data.affectedBatches,
    }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Determine risk level
  let riskLevel: 'low' | 'medium' | 'high' = 'low';
  if (averageReturnRate > 10 || totalReturnValue > 5000000) riskLevel = 'high';
  else if (averageReturnRate > 5 || totalReturnValue > 1000000) riskLevel = 'medium';

  // Generate recommendations
  const recommendations: string[] = [];
  if (averageReturnRate > 5) {
    recommendations.push(`High average return rate (${averageReturnRate.toFixed(1)}%). Review quality control processes.`);
  }
  if (topDefectTypes.length > 0) {
    recommendations.push(`Top defect type: ${topDefectTypes[0].defectType}. Focus improvement efforts here.`);
  }
  if (totalReturnValue > 1000000) {
    recommendations.push(`High return value impact (${(totalReturnValue / 1000000).toFixed(1)}M IDR). Consider supplier contract review.`);
  }
  if (recommendations.length === 0) {
    recommendations.push('Supplier performance is within acceptable ranges. Continue monitoring.');
  }

  return {
    supplierId,
    supplierName,
    totalBatches: batchAnalyses.length,
    batchesWithReturns: batchAnalyses.length,
    totalReturnValue,
    averageReturnRate,
    topDefectTypes,
    batchAnalysis: batchAnalyses,
    riskLevel,
    recommendations,
  };
}
