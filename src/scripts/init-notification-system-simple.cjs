/**
 * Simple script to initialize the notification system
 */

const { PrismaClient } = require('../generated/prisma');

const prisma = new PrismaClient();

async function initializeNotificationSystem() {
  console.log('🚀 Initializing notification system...');

  try {
    // Check if tables exist by trying to query them
    console.log('1. Checking database tables...');
    
    try {
      await prisma.notificationTemplate.findFirst();
      console.log('✅ NotificationTemplate table exists');
    } catch (error) {
      console.log('❌ NotificationTemplate table missing:', error.message);
    }

    try {
      await prisma.notificationPreference.findFirst();
      console.log('✅ NotificationPreference table exists');
    } catch (error) {
      console.log('❌ NotificationPreference table missing:', error.message);
    }

    try {
      await prisma.notificationEvent.findFirst();
      console.log('✅ NotificationEvent table exists');
    } catch (error) {
      console.log('❌ NotificationEvent table missing:', error.message);
    }

    // Create default templates
    console.log('\n2. Creating default notification templates...');
    
    const defaultTemplates = [
      {
        eventType: 'po.status.changed',
        name: 'Purchase Order Status Changed',
        description: 'Notification when a purchase order status changes',
        titleTemplate: 'Purchase Order Status Updated',
        messageTemplate: 'Purchase Order #{{poId}} status changed from {{fromStatus}} to {{toStatus}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'NORMAL',
        variables: {
          poId: 'Purchase Order ID',
          fromStatus: 'Previous status',
          toStatus: 'New status'
        }
      },
      {
        eventType: 'po.approved',
        name: 'Purchase Order Approved',
        description: 'Notification when a purchase order is approved',
        titleTemplate: 'Purchase Order Approved',
        messageTemplate: 'Purchase Order #{{poId}} has been approved by {{approvedBy}}',
        defaultDeliveryMethods: ['IN_APP', 'TOAST', 'EMAIL'],
        defaultPriority: 'HIGH',
        variables: {
          poId: 'Purchase Order ID',
          approvedBy: 'User who approved the order'
        }
      },
      {
        eventType: 'inventory.low_stock',
        name: 'Low Stock Alert',
        description: 'Notification when product stock is low',
        titleTemplate: 'Low Stock Alert',
        messageTemplate: 'Product {{productName}} is running low ({{currentStock}} remaining, minimum: {{minThreshold}})',
        defaultDeliveryMethods: ['IN_APP', 'TOAST'],
        defaultPriority: 'HIGH',
        variables: {
          productName: 'Product name',
          currentStock: 'Current stock level',
          minThreshold: 'Minimum stock threshold'
        }
      }
    ];

    for (const template of defaultTemplates) {
      try {
        await prisma.notificationTemplate.upsert({
          where: { eventType: template.eventType },
          update: template,
          create: {
            id: `template_${template.eventType}_${Date.now()}`,
            ...template
          }
        });
        console.log(`✅ Created/updated template: ${template.name}`);
      } catch (error) {
        console.log(`❌ Failed to create template ${template.name}:`, error.message);
      }
    }

    // Create default preferences for existing users
    console.log('\n3. Creating default preferences for existing users...');
    
    try {
      const users = await prisma.user.findMany({
        select: { id: true, name: true, email: true }
      });

      console.log(`Found ${users.length} users`);

      for (const user of users) {
        // Create default preferences for each template
        for (const template of defaultTemplates) {
          try {
            await prisma.notificationPreference.upsert({
              where: {
                userId_eventType: {
                  userId: user.id,
                  eventType: template.eventType
                }
              },
              update: {},
              create: {
                id: `pref_${user.id}_${template.eventType}_${Date.now()}`,
                userId: user.id,
                eventType: template.eventType,
                enabled: true,
                deliveryMethods: template.defaultDeliveryMethods,
                frequency: 'IMMEDIATE'
              }
            });
          } catch (error) {
            console.log(`❌ Failed to create preference for user ${user.name}:`, error.message);
          }
        }
        console.log(`✅ Created preferences for user: ${user.name}`);
      }
    } catch (error) {
      console.log('❌ Failed to create user preferences:', error.message);
    }

    console.log('\n🎉 Notification system initialization completed!');

  } catch (error) {
    console.error('❌ Initialization failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the initialization
initializeNotificationSystem();
