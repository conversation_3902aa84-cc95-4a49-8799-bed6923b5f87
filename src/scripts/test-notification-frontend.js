/**
 * Test script to debug notification frontend issues
 */

console.log('🔍 Testing notification system frontend integration...');

// Test 1: Check if API endpoints respond correctly
async function testAPIEndpoints() {
  console.log('\n1. Testing API endpoints without authentication...');
  
  try {
    // Test preferences endpoint
    const preferencesResponse = await fetch('http://localhost:3000/api/notifications/preferences');
    console.log('Preferences endpoint status:', preferencesResponse.status);
    
    if (preferencesResponse.status === 403) {
      console.log('✅ Preferences endpoint working (403 expected without auth)');
    } else {
      const preferencesData = await preferencesResponse.json();
      console.log('Preferences response:', preferencesData);
    }

    // Test templates endpoint
    const templatesResponse = await fetch('http://localhost:3000/api/notifications/templates');
    console.log('Templates endpoint status:', templatesResponse.status);
    
    if (templatesResponse.status === 403) {
      console.log('✅ Templates endpoint working (403 expected without auth)');
    } else {
      const templatesData = await templatesResponse.json();
      console.log('Templates response:', templatesData);
    }

  } catch (error) {
    console.error('❌ API endpoint test failed:', error.message);
  }
}

// Test 2: Check database tables
async function testDatabaseTables() {
  console.log('\n2. Testing database table existence...');
  
  try {
    // This would require a database connection, so we'll skip for now
    console.log('⏭️ Database test skipped (requires database connection)');
  } catch (error) {
    console.error('❌ Database test failed:', error.message);
  }
}

// Test 3: Check if notification system is initialized
async function testNotificationSystemInit() {
  console.log('\n3. Testing notification system initialization...');
  
  try {
    // Test if we can import the notification system
    console.log('⏭️ Notification system init test skipped (requires module import)');
  } catch (error) {
    console.error('❌ Notification system init test failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting notification system tests...');
  
  await testAPIEndpoints();
  await testDatabaseTables();
  await testNotificationSystemInit();
  
  console.log('\n🎉 Tests completed!');
  console.log('\n📋 Next steps:');
  console.log('1. Ensure you are logged in when testing the frontend');
  console.log('2. Check browser console for detailed error messages');
  console.log('3. Verify database tables exist');
  console.log('4. Initialize notification system if needed');
}

// Check if we're in a browser or Node.js environment
if (typeof window !== 'undefined') {
  // Browser environment
  runTests();
} else {
  // Node.js environment
  console.log('This script should be run in a browser console');
  console.log('Copy and paste this code into your browser console while on the notification pages');
}

export {};
