/**
 * Test Script for Notification Module Imports
 * 
 * This script tests if all notification modules can be imported correctly
 * and helps debug import issues.
 */

console.log('🔍 Testing notification module imports...');

try {
  console.log('1. Testing event system import...');
  const eventSystem = require('@/lib/events/event-system');
  console.log('✅ Event system imported successfully');
  console.log('   Available exports:', Object.keys(eventSystem));

  console.log('\n2. Testing preference manager import...');
  const preferenceManager = require('@/lib/notifications/preference-manager');
  console.log('✅ Preference manager imported successfully');
  console.log('   Available exports:', Object.keys(preferenceManager));

  console.log('\n3. Testing template manager import...');
  const templateManager = require('@/lib/notifications/template-manager');
  console.log('✅ Template manager imported successfully');
  console.log('   Available exports:', Object.keys(templateManager));

  console.log('\n4. Testing notification registry import...');
  const notificationRegistry = require('@/lib/notifications/notification-registry');
  console.log('✅ Notification registry imported successfully');
  console.log('   Available exports:', Object.keys(notificationRegistry));

  console.log('\n5. Testing main notifications module import...');
  const notifications = require('@/lib/notifications');
  console.log('✅ Main notifications module imported successfully');
  console.log('   Available exports:', Object.keys(notifications));

  if (notifications.preferences) {
    console.log('✅ Preferences object is available');
    console.log('   Preferences methods:', Object.keys(notifications.preferences));
  } else {
    console.log('❌ Preferences object is NOT available');
  }

  if (notifications.templates) {
    console.log('✅ Templates object is available');
    console.log('   Templates methods:', Object.keys(notifications.templates));
  } else {
    console.log('❌ Templates object is NOT available');
  }

  console.log('\n6. Testing database connection...');
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Test if notification tables exist
    try {
      await prisma.notification.findFirst();
      console.log('✅ Notification table exists');
    } catch (error) {
      console.log('❌ Notification table issue:', error.message);
    }

    try {
      await prisma.notificationPreference.findFirst();
      console.log('✅ NotificationPreference table exists');
    } catch (error) {
      console.log('❌ NotificationPreference table issue:', error.message);
    }

    try {
      await prisma.notificationTemplate.findFirst();
      console.log('✅ NotificationTemplate table exists');
    } catch (error) {
      console.log('❌ NotificationTemplate table issue:', error.message);
    }

    try {
      await prisma.notificationEvent.findFirst();
      console.log('✅ NotificationEvent table exists');
    } catch (error) {
      console.log('❌ NotificationEvent table issue:', error.message);
    }

    await prisma.$disconnect();
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
  }

  console.log('\n7. Testing preferences functionality...');
  if (notifications.preferences) {
    try {
      // Test with a dummy user ID
      const testUserId = 'test-user-id';
      console.log(`   Testing getUserPreferences for user: ${testUserId}`);
      
      const userPrefs = await notifications.preferences.getForUser(testUserId);
      console.log('✅ getUserPreferences works');
      console.log('   Result:', userPrefs);
    } catch (error) {
      console.log('❌ getUserPreferences failed:', error.message);
    }
  }

  console.log('\n🎉 Import test completed!');

} catch (error) {
  console.error('❌ Import test failed:', error);
  console.error('Stack trace:', error.stack);
}

export {};
