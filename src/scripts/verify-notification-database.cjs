/**
 * Verify Notification Database Setup
 * 
 * This script checks if the notification system database tables exist
 * and provides detailed information about their current state.
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyNotificationDatabase() {
  console.log('🔍 Verifying notification system database setup...\n');

  try {
    // Test database connection
    console.log('1. Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // Check NotificationTemplate table
    console.log('2. Checking NotificationTemplate table...');
    try {
      const templateCount = await prisma.notificationTemplate.count();
      console.log(`✅ NotificationTemplate table exists with ${templateCount} records`);
      
      if (templateCount > 0) {
        const sampleTemplates = await prisma.notificationTemplate.findMany({
          take: 3,
          select: {
            eventType: true,
            name: true,
            defaultPriority: true
          }
        });
        console.log('   Sample templates:', sampleTemplates);
      }
    } catch (error) {
      console.log('❌ NotificationTemplate table error:', error.message);
    }

    // Check NotificationPreference table
    console.log('\n3. Checking NotificationPreference table...');
    try {
      const preferenceCount = await prisma.notificationPreference.count();
      console.log(`✅ NotificationPreference table exists with ${preferenceCount} records`);
      
      if (preferenceCount > 0) {
        const samplePreferences = await prisma.notificationPreference.findMany({
          take: 3,
          select: {
            eventType: true,
            enabled: true,
            deliveryMethods: true
          }
        });
        console.log('   Sample preferences:', samplePreferences);
      }
    } catch (error) {
      console.log('❌ NotificationPreference table error:', error.message);
    }

    // Check NotificationEvent table
    console.log('\n4. Checking NotificationEvent table...');
    try {
      const eventCount = await prisma.notificationEvent.count();
      console.log(`✅ NotificationEvent table exists with ${eventCount} records`);
    } catch (error) {
      console.log('❌ NotificationEvent table error:', error.message);
    }

    // Check User table (needed for preferences)
    console.log('\n5. Checking User table...');
    try {
      const userCount = await prisma.user.count();
      console.log(`✅ User table exists with ${userCount} records`);
      
      if (userCount > 0) {
        const superAdmins = await prisma.user.findMany({
          where: { role: 'SUPER_ADMIN' },
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        });
        console.log(`   Found ${superAdmins.length} SUPER_ADMIN users:`, superAdmins);
      }
    } catch (error) {
      console.log('❌ User table error:', error.message);
    }

    // Summary and recommendations
    console.log('\n📋 Summary and Recommendations:');
    
    const templateCount = await prisma.notificationTemplate.count().catch(() => 0);
    const preferenceCount = await prisma.notificationPreference.count().catch(() => 0);
    const userCount = await prisma.user.count().catch(() => 0);
    
    if (templateCount === 0) {
      console.log('⚠️  No notification templates found - system needs initialization');
    } else {
      console.log(`✅ ${templateCount} notification templates found`);
    }
    
    if (preferenceCount === 0) {
      console.log('⚠️  No notification preferences found - system needs initialization');
    } else {
      console.log(`✅ ${preferenceCount} notification preferences found`);
    }
    
    if (userCount === 0) {
      console.log('❌ No users found - cannot create preferences');
    } else {
      console.log(`✅ ${userCount} users found`);
    }

    console.log('\n🎯 Next Steps:');
    if (templateCount === 0 || preferenceCount === 0) {
      console.log('1. Log in to the application as a SUPER_ADMIN user');
      console.log('2. Navigate to http://localhost:3000/settings/notifications');
      console.log('3. Click "Check Status" to see current system state');
      console.log('4. Click "Initialize System" to create templates and preferences');
    } else {
      console.log('✅ Notification system appears to be fully set up!');
      console.log('   You can now use the notification preferences page.');
    }

  } catch (error) {
    console.error('❌ Critical error during verification:', error);
    console.error('Error details:', error.message);
    
    if (error.code === 'P1001') {
      console.log('\n💡 Database connection failed. Please check:');
      console.log('   - Database server is running');
      console.log('   - Connection string in .env file is correct');
      console.log('   - Database exists and is accessible');
    } else if (error.code === 'P2021') {
      console.log('\n💡 Table does not exist. Please run:');
      console.log('   npx prisma db push');
      console.log('   or');
      console.log('   npx prisma migrate dev --name add-notification-system');
    }
    
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the verification
verifyNotificationDatabase();
