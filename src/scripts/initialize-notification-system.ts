/**
 * Notification System Initialization Script
 * 
 * This script initializes the new modular notification system:
 * - Creates database tables (if not exists)
 * - Seeds default templates
 * - Initializes user preferences
 * - Migrates existing notifications
 */

import { PrismaClient } from '@prisma/client';
import { initializeNotifications } from '@/lib/notifications';

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting notification system initialization...');

  try {
    // Step 1: Initialize the notification system
    console.log('📋 Initializing notification system...');
    await initializeNotifications();

    // Step 2: Verify database schema
    console.log('🔍 Verifying database schema...');
    await verifyDatabaseSchema();

    // Step 3: Migrate existing notifications (if any)
    console.log('📦 Migrating existing notifications...');
    await migrateExistingNotifications();

    // Step 4: Create sample notifications for testing
    console.log('🧪 Creating sample notifications...');
    await createSampleNotifications();

    console.log('✅ Notification system initialization completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Update your application startup to call initializeNotifications()');
    console.log('2. Replace existing notification calls with the new API');
    console.log('3. Test the notification preferences UI');
    console.log('4. Configure email/SMS delivery methods if needed');

  } catch (error) {
    console.error('❌ Error during initialization:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function verifyDatabaseSchema() {
  try {
    // Check if new tables exist
    const tables = ['NotificationTemplate', 'NotificationPreference', 'NotificationEvent'];
    
    for (const table of tables) {
      try {
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
        console.log(`  ✓ Table ${table} exists`);
      } catch (error) {
        console.log(`  ❌ Table ${table} missing - please run database migration`);
        throw new Error(`Missing table: ${table}`);
      }
    }

    // Check if Notification table has new columns
    try {
      await prisma.$queryRaw`SELECT "eventType", "eventId", "deliveryMethods", "priority", "expiresAt" FROM "Notification" LIMIT 1`;
      console.log('  ✓ Notification table has new columns');
    } catch (error) {
      console.log('  ❌ Notification table missing new columns - please run database migration');
      throw new Error('Notification table needs migration');
    }

  } catch (error) {
    console.error('Database schema verification failed:', error);
    throw error;
  }
}

async function migrateExistingNotifications() {
  try {
    // Get existing notifications that don't have eventType set
    const existingNotifications = await prisma.notification.findMany({
      where: {
        eventType: null,
      },
      take: 100, // Process in batches
    });

    if (existingNotifications.length === 0) {
      console.log('  ✓ No existing notifications to migrate');
      return;
    }

    console.log(`  📦 Migrating ${existingNotifications.length} existing notifications...`);

    for (const notification of existingNotifications) {
      // Map old notification types to new event types
      const eventType = mapNotificationTypeToEventType(notification.type);
      
      await prisma.notification.update({
        where: { id: notification.id },
        data: {
          eventType,
          eventId: `migrated_${notification.id}`,
          deliveryMethods: ['IN_APP', 'TOAST'], // Default delivery methods
          priority: mapNotificationTypeToPriority(notification.type),
        },
      });
    }

    console.log(`  ✓ Migrated ${existingNotifications.length} notifications`);
  } catch (error) {
    console.error('Error migrating existing notifications:', error);
    throw error;
  }
}

function mapNotificationTypeToEventType(notificationType: string): string {
  const mapping: Record<string, string> = {
    'PURCHASE_ORDER_APPROVAL': 'po.status.changed',
    'PURCHASE_ORDER_APPROVED': 'po.approved',
    'PURCHASE_ORDER_REJECTED': 'po.rejected',
    'PURCHASE_ORDER_RECEIVED': 'po.received',
    'ALERT': 'system.alert',
    'INFO': 'system.info',
    'SYSTEM': 'system.notification',
    'MESSAGE': 'user.message',
  };

  return mapping[notificationType] || 'system.notification';
}

function mapNotificationTypeToPriority(notificationType: string): string {
  const priorityMapping: Record<string, string> = {
    'PURCHASE_ORDER_APPROVAL': 'HIGH',
    'PURCHASE_ORDER_APPROVED': 'HIGH',
    'PURCHASE_ORDER_REJECTED': 'HIGH',
    'PURCHASE_ORDER_RECEIVED': 'NORMAL',
    'ALERT': 'URGENT',
    'INFO': 'LOW',
    'SYSTEM': 'NORMAL',
    'MESSAGE': 'NORMAL',
  };

  return priorityMapping[notificationType] || 'NORMAL';
}

async function createSampleNotifications() {
  try {
    // Get a test user (first active user)
    const testUser = await prisma.user.findFirst({
      where: { active: true },
    });

    if (!testUser) {
      console.log('  ⚠️  No active users found - skipping sample notifications');
      return;
    }

    // Create sample notifications for testing
    const sampleNotifications = [
      {
        userId: testUser.id,
        title: 'Welcome to the New Notification System',
        message: 'The notification system has been upgraded with new features and customization options.',
        type: 'SYSTEM',
        eventType: 'system.notification',
        eventId: `sample_welcome_${Date.now()}`,
        deliveryMethods: ['IN_APP', 'TOAST'],
        priority: 'NORMAL',
        metadata: {
          category: 'system_upgrade',
          version: '2.0',
        },
      },
      {
        userId: testUser.id,
        title: 'Notification Preferences Available',
        message: 'You can now customize your notification preferences in the settings.',
        type: 'INFO',
        eventType: 'user.action.required',
        eventId: `sample_preferences_${Date.now()}`,
        deliveryMethods: ['IN_APP'],
        priority: 'LOW',
        actionUrl: '/settings/notifications',
        metadata: {
          action: 'configure_preferences',
        },
      },
    ];

    for (const notification of sampleNotifications) {
      await prisma.notification.create({
        data: notification,
      });
    }

    console.log(`  ✓ Created ${sampleNotifications.length} sample notifications`);
  } catch (error) {
    console.error('Error creating sample notifications:', error);
    // Don't throw here - sample notifications are optional
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { main as initializeNotificationSystem };
