#!/usr/bin/env ts-node

/**
 * Test script to verify quality notification imports work correctly
 */

console.log('🧪 Testing Quality Notification Imports...\n');

async function testQualityNotificationImports() {
  try {
    // Test importing from main notifications module
    console.log('1. Testing main notifications module import...');
    const notifications = await import('@/lib/notifications');
    console.log('✅ Main notifications module imported successfully');
    
    // Check if quality notification functions exist
    const qualityFunctions = [
      'notifyQualityThresholdBreached',
      'notifySupplierQualityAlert', 
      'notifyQualityIssueEscalated',
      'notifyQualityIssueReported',
      'notifyQualityImprovementDue'
    ];
    
    console.log('\n2. Checking quality notification functions...');
    for (const funcName of qualityFunctions) {
      if (typeof notifications[funcName] === 'function') {
        console.log(`✅ ${funcName} - Available`);
      } else {
        console.log(`❌ ${funcName} - Missing`);
      }
    }
    
    // Test importing quality alert system
    console.log('\n3. Testing quality alert system import...');
    const qualityAlertSystem = await import('@/lib/quality-alert-system');
    console.log('✅ Quality alert system imported successfully');
    console.log('Available exports:', Object.keys(qualityAlertSystem));
    
    // Test importing quality trend analysis
    console.log('\n4. Testing quality trend analysis import...');
    const qualityTrendAnalysis = await import('@/lib/quality-trend-analysis');
    console.log('✅ Quality trend analysis imported successfully');
    console.log('Available exports:', Object.keys(qualityTrendAnalysis));
    
    console.log('\n🎉 All quality notification imports working correctly!');
    
  } catch (error) {
    console.error('❌ Error testing quality notification imports:', error);
    process.exit(1);
  }
}

// Run the test
testQualityNotificationImports();
