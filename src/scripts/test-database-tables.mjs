/**
 * Test Database Tables
 * 
 * This script directly tests if the notification system database tables exist
 * and can be accessed using the Prisma client.
 */

import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function testDatabaseTables() {
  console.log('🔍 Testing notification system database tables...\n');

  try {
    // Test database connection
    console.log('1. Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // Test NotificationTemplate table
    console.log('2. Testing NotificationTemplate table...');
    try {
      const templateCount = await prisma.notificationTemplate.count();
      console.log(`✅ NotificationTemplate table exists with ${templateCount} records`);
      
      // Try to create a test template
      console.log('   Testing template creation...');
      const testTemplate = await prisma.notificationTemplate.upsert({
        where: { eventType: 'test.event' },
        update: {
          name: 'Test Template',
          description: 'Test template for verification',
          titleTemplate: 'Test Title',
          messageTemplate: 'Test message',
          defaultDeliveryMethods: ['IN_APP'],
          defaultPriority: 'NORMAL'
        },
        create: {
          eventType: 'test.event',
          name: 'Test Template',
          description: 'Test template for verification',
          titleTemplate: 'Test Title',
          messageTemplate: 'Test message',
          defaultDeliveryMethods: ['IN_APP'],
          defaultPriority: 'NORMAL'
        }
      });
      console.log(`   ✅ Template creation successful: ${testTemplate.id}`);
      
      // Clean up test template
      await prisma.notificationTemplate.delete({
        where: { id: testTemplate.id }
      });
      console.log('   ✅ Test template cleaned up');
      
    } catch (error) {
      console.log('❌ NotificationTemplate table error:', error.message);
      console.log('   Error code:', error.code);
      console.log('   Error details:', error);
    }

    // Test NotificationPreference table
    console.log('\n3. Testing NotificationPreference table...');
    try {
      const preferenceCount = await prisma.notificationPreference.count();
      console.log(`✅ NotificationPreference table exists with ${preferenceCount} records`);
    } catch (error) {
      console.log('❌ NotificationPreference table error:', error.message);
      console.log('   Error code:', error.code);
    }

    // Test NotificationEvent table
    console.log('\n4. Testing NotificationEvent table...');
    try {
      const eventCount = await prisma.notificationEvent.count();
      console.log(`✅ NotificationEvent table exists with ${eventCount} records`);
    } catch (error) {
      console.log('❌ NotificationEvent table error:', error.message);
      console.log('   Error code:', error.code);
    }

    // Test User table (needed for preferences)
    console.log('\n5. Testing User table...');
    try {
      const userCount = await prisma.user.count();
      console.log(`✅ User table exists with ${userCount} records`);
      
      if (userCount > 0) {
        const superAdmins = await prisma.user.findMany({
          where: { role: 'SUPER_ADMIN' },
          select: {
            id: true,
            name: true,
            email: true,
            role: true
          }
        });
        console.log(`   Found ${superAdmins.length} SUPER_ADMIN users:`, superAdmins);
      }
    } catch (error) {
      console.log('❌ User table error:', error.message);
      console.log('   Error code:', error.code);
    }

    // Test raw database query to check table existence
    console.log('\n6. Testing raw database queries...');
    try {
      // Check if tables exist using raw SQL
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name LIKE '%otification%'
        ORDER BY table_name;
      `;
      console.log('✅ Notification-related tables found:', tables);
    } catch (error) {
      console.log('❌ Raw query error:', error.message);
      console.log('   Error code:', error.code);
    }

    console.log('\n🎯 Summary:');
    console.log('If all tests passed, the notification system database is properly set up.');
    console.log('If any tests failed, the specific error messages above will help diagnose the issue.');

  } catch (error) {
    console.error('❌ Critical error during database testing:', error);
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    
    if (error.code === 'P1001') {
      console.log('\n💡 Database connection failed. Please check:');
      console.log('   - Database server is running');
      console.log('   - Connection string in .env file is correct');
      console.log('   - Database exists and is accessible');
    } else if (error.code === 'P2021') {
      console.log('\n💡 Table does not exist. Please run:');
      console.log('   npx prisma db push');
      console.log('   or');
      console.log('   npx prisma migrate dev --name add-notification-system');
    }
    
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testDatabaseTables();
