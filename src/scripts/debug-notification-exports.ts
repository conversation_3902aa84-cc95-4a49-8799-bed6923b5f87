/**
 * Debug Notification Exports
 * 
 * This script helps debug the notification module exports to identify
 * why the preferences export might not be found.
 */

console.log('🔍 Debugging notification module exports...');

async function debugExports() {
  try {
    console.log('\n1. Testing direct module import...');
    
    // Test importing the main module
    const notificationsModule = await import('@/lib/notifications/index');
    console.log('✅ Main module imported successfully');
    console.log('Available exports:', Object.keys(notificationsModule));
    
    if (notificationsModule.preferences) {
      console.log('✅ preferences export found');
      console.log('preferences methods:', Object.keys(notificationsModule.preferences));
    } else {
      console.log('❌ preferences export NOT found');
    }
    
    if (notificationsModule.templates) {
      console.log('✅ templates export found');
      console.log('templates methods:', Object.keys(notificationsModule.templates));
    } else {
      console.log('❌ templates export NOT found');
    }

    console.log('\n2. Testing individual component imports...');
    
    // Test preference manager
    try {
      const preferenceManagerModule = await import('@/lib/notifications/preference-manager');
      console.log('✅ Preference manager imported');
      console.log('Preference manager exports:', Object.keys(preferenceManagerModule));
      
      if (preferenceManagerModule.preferenceManager) {
        console.log('✅ preferenceManager instance found');
        console.log('preferenceManager methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(preferenceManagerModule.preferenceManager)));
      }
    } catch (error) {
      console.log('❌ Preference manager import failed:', error.message);
    }

    // Test template manager
    try {
      const templateManagerModule = await import('@/lib/notifications/template-manager');
      console.log('✅ Template manager imported');
      console.log('Template manager exports:', Object.keys(templateManagerModule));
    } catch (error) {
      console.log('❌ Template manager import failed:', error.message);
    }

    // Test event system
    try {
      const eventSystemModule = await import('@/lib/events/event-system');
      console.log('✅ Event system imported');
      console.log('Event system exports:', Object.keys(eventSystemModule));
    } catch (error) {
      console.log('❌ Event system import failed:', error.message);
    }

    console.log('\n3. Testing API route import pattern...');
    
    // Test the exact pattern used in API routes
    try {
      const { preferences } = await import('@/lib/notifications');
      
      if (preferences) {
        console.log('✅ Destructured preferences import works');
        console.log('preferences object type:', typeof preferences);
        console.log('preferences methods:', Object.keys(preferences));
        
        // Test if methods are callable
        if (typeof preferences.getForUser === 'function') {
          console.log('✅ getForUser method is callable');
        } else {
          console.log('❌ getForUser method is not callable');
        }
      } else {
        console.log('❌ Destructured preferences import failed - preferences is undefined');
      }
    } catch (error) {
      console.log('❌ Destructured import failed:', error.message);
      console.log('Error stack:', error.stack);
    }

    console.log('\n4. Testing database connection...');
    
    // Test if database connection works
    try {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();
      
      await prisma.$connect();
      console.log('✅ Database connection successful');
      
      // Test if notification tables exist
      try {
        await prisma.notification.findFirst();
        console.log('✅ Notification table accessible');
      } catch (error) {
        console.log('❌ Notification table issue:', error.message);
      }
      
      try {
        await prisma.notificationPreference.findFirst();
        console.log('✅ NotificationPreference table accessible');
      } catch (error) {
        console.log('❌ NotificationPreference table issue:', error.message);
      }
      
      await prisma.$disconnect();
    } catch (error) {
      console.log('❌ Database connection failed:', error.message);
    }

    console.log('\n5. Testing preferences functionality...');
    
    // Test if preferences actually work
    try {
      const { preferences } = await import('@/lib/notifications');
      
      if (preferences && preferences.getForUser) {
        // Test with a dummy user ID
        const testResult = await preferences.getForUser('test-user-debug');
        console.log('✅ preferences.getForUser executed successfully');
        console.log('Result type:', typeof testResult);
        console.log('Result structure:', testResult ? Object.keys(testResult) : 'null/undefined');
      }
    } catch (error) {
      console.log('❌ preferences.getForUser failed:', error.message);
    }

    console.log('\n🎉 Debug completed!');

  } catch (error) {
    console.error('❌ Debug script failed:', error);
    console.error('Stack trace:', error.stack);
  }
}

// Run the debug
debugExports().catch(console.error);

export {};
