/**
 * Database Check Script for Notification System
 * 
 * This script checks if the notification system database tables exist
 * and provides instructions for setting them up if they don't.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkNotificationDatabase() {
  console.log('🔍 Checking notification system database setup...');

  try {
    await prisma.$connect();
    console.log('✅ Database connection successful');

    const checks = [
      {
        name: 'Notification table (enhanced)',
        check: async () => {
          const result = await prisma.$queryRaw`
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Notification' 
            AND column_name IN ('eventType', 'eventId', 'deliveryMethods', 'priority', 'expiresAt')
          `;
          return Array.isArray(result) ? result.length : 0;
        },
        expected: 5,
        description: 'Enhanced Notification table with new columns'
      },
      {
        name: 'NotificationTemplate table',
        check: async () => {
          const result = await prisma.notificationTemplate.findFirst();
          return true;
        },
        expected: true,
        description: 'NotificationTemplate table for managing templates'
      },
      {
        name: 'NotificationPreference table',
        check: async () => {
          const result = await prisma.notificationPreference.findFirst();
          return true;
        },
        expected: true,
        description: 'NotificationPreference table for user preferences'
      },
      {
        name: 'NotificationEvent table',
        check: async () => {
          const result = await prisma.notificationEvent.findFirst();
          return true;
        },
        expected: true,
        description: 'NotificationEvent table for event tracking'
      }
    ];

    let allPassed = true;
    const results = [];

    for (const check of checks) {
      try {
        const result = await check.check();
        const passed = check.name.includes('enhanced') 
          ? result >= check.expected 
          : result === check.expected;
        
        results.push({
          name: check.name,
          passed,
          result,
          description: check.description
        });

        if (passed) {
          console.log(`✅ ${check.name}: OK`);
        } else {
          console.log(`❌ ${check.name}: FAILED`);
          allPassed = false;
        }
      } catch (error) {
        console.log(`❌ ${check.name}: ERROR - ${error.message}`);
        results.push({
          name: check.name,
          passed: false,
          error: error.message,
          description: check.description
        });
        allPassed = false;
      }
    }

    console.log('\n📊 Summary:');
    results.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.name}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });

    if (allPassed) {
      console.log('\n🎉 All database checks passed! The notification system should work correctly.');
    } else {
      console.log('\n⚠️  Some database checks failed. Please run the database migration:');
      console.log('');
      console.log('1. Generate Prisma client:');
      console.log('   npx prisma generate');
      console.log('');
      console.log('2. Run database migration:');
      console.log('   npx prisma migrate dev --name add-notification-system');
      console.log('');
      console.log('3. If migration fails, try:');
      console.log('   npx prisma db push');
      console.log('');
      console.log('4. Initialize the notification system:');
      console.log('   npx ts-node src/scripts/initialize-notification-system.ts');
    }

    // Additional checks
    console.log('\n🔍 Additional Information:');
    
    try {
      const userCount = await prisma.user.count();
      console.log(`📊 Total users in system: ${userCount}`);
      
      const notificationCount = await prisma.notification.count();
      console.log(`📊 Total notifications: ${notificationCount}`);
      
      if (allPassed) {
        const preferenceCount = await prisma.notificationPreference.count();
        console.log(`📊 Total notification preferences: ${preferenceCount}`);
        
        const templateCount = await prisma.notificationTemplate.count();
        console.log(`📊 Total notification templates: ${templateCount}`);
      }
    } catch (error) {
      console.log(`⚠️  Could not get additional stats: ${error.message}`);
    }

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    console.log('\nPlease check:');
    console.log('1. Database is running');
    console.log('2. Database connection string is correct');
    console.log('3. Database permissions are set up');
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check if called directly
if (require.main === module) {
  checkNotificationDatabase().catch(console.error);
}

export { checkNotificationDatabase };
