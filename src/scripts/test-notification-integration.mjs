/**
 * Test Notification Integration
 * 
 * This script tests the complete notification workflow:
 * 1. Initialize notification system
 * 2. Trigger a PO status change event
 * 3. Verify notifications are created
 * 4. Check in-app notifications in database
 */

import { PrismaClient } from '../generated/prisma/index.js';
import { initializeNotificationSystem } from '../lib/notifications/notification-registry.js';
import { notifyPOStatusChange } from '../lib/notifications/integrations/purchase-order-integration.js';

const prisma = new PrismaClient();

async function testNotificationIntegration() {
  console.log('🧪 Testing notification system integration...\n');

  try {
    // Step 1: Initialize notification system
    console.log('1. Initializing notification system...');
    initializeNotificationSystem();
    console.log('✅ Notification system initialized\n');

    // Step 2: Check if we have a test PO
    console.log('2. Finding a test Purchase Order...');
    const testPO = await prisma.purchaseOrder.findFirst({
      include: {
        supplier: true,
        createdBy: true,
      }
    });

    if (!testPO) {
      console.log('❌ No Purchase Orders found. Please create a PO first.');
      return;
    }

    console.log(`✅ Found test PO: ${testPO.id} (${testPO.supplier.name})\n`);

    // Step 3: Check notification templates
    console.log('3. Checking notification templates...');
    const templates = await prisma.notificationTemplate.findMany();
    console.log(`✅ Found ${templates.length} notification templates:`);
    templates.forEach(t => console.log(`   - ${t.eventType}: ${t.name}`));
    console.log('');

    // Step 4: Check user preferences
    console.log('4. Checking user notification preferences...');
    const preferences = await prisma.notificationPreference.findMany({
      include: { user: { select: { name: true, role: true } } }
    });
    console.log(`✅ Found ${preferences.length} user preferences:`);
    preferences.forEach(p => console.log(`   - ${p.user.name} (${p.user.role}): ${p.eventType} = ${p.enabled ? 'enabled' : 'disabled'}`));
    console.log('');

    // Step 5: Count existing notifications
    console.log('5. Counting existing notifications...');
    const existingNotifications = await prisma.notification.count();
    console.log(`📊 Existing notifications: ${existingNotifications}\n`);

    // Step 6: Trigger a notification event
    console.log('6. Triggering PO status change notification...');
    await notifyPOStatusChange(
      testPO.id,
      'DRAFT',
      'PENDING_APPROVAL',
      testPO.createdBy.id,
      {
        reason: 'OPERATIONAL_CHANGE',
        notes: 'Test notification integration',
        supplierName: testPO.supplier.name,
        total: Number(testPO.total),
        poNumber: testPO.id.slice(-8).toUpperCase(),
      }
    );
    console.log('✅ Notification event triggered\n');

    // Step 7: Wait a moment for processing
    console.log('7. Waiting for notification processing...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    console.log('✅ Processing complete\n');

    // Step 8: Check if notifications were created
    console.log('8. Checking for new notifications...');
    const newNotifications = await prisma.notification.findMany({
      where: {
        eventType: 'po.status.changed',
        createdAt: {
          gte: new Date(Date.now() - 60000) // Last minute
        }
      },
      include: {
        user: { select: { name: true, role: true } }
      },
      orderBy: { createdAt: 'desc' }
    });

    console.log(`📧 Found ${newNotifications.length} new notifications:`);
    newNotifications.forEach(n => {
      console.log(`   - ${n.user.name} (${n.user.role}): ${n.title}`);
      console.log(`     Message: ${n.message}`);
      console.log(`     Delivery: ${JSON.stringify(n.deliveryMethods)}`);
      console.log(`     Action URL: ${n.actionUrl || 'None'}`);
      console.log('');
    });

    // Step 9: Test in-app notification retrieval
    console.log('9. Testing in-app notification retrieval...');
    const inAppNotifications = await prisma.notification.findMany({
      where: {
        isRead: false,
        // Filter for notifications that have in-app delivery enabled
        deliveryMethods: {
          path: ['inApp'],
          equals: true
        }
      },
      include: {
        user: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`📱 Found ${inAppNotifications.length} unread in-app notifications:`);
    inAppNotifications.forEach(n => {
      console.log(`   - ${n.user.name}: ${n.title}`);
    });
    console.log('');

    // Step 10: Summary
    console.log('📊 Test Summary:');
    console.log(`   - Notification system: ✅ Initialized`);
    console.log(`   - Templates available: ${templates.length}`);
    console.log(`   - User preferences: ${preferences.length}`);
    console.log(`   - Notifications created: ${newNotifications.length}`);
    console.log(`   - In-app notifications: ${inAppNotifications.length}`);
    
    if (newNotifications.length > 0) {
      console.log('\n🎉 Notification integration test PASSED!');
      console.log('The notification system is working correctly.');
    } else {
      console.log('\n❌ Notification integration test FAILED!');
      console.log('No notifications were created. Check the logs above for issues.');
    }

  } catch (error) {
    console.error('❌ Error during notification integration test:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the test
testNotificationIntegration();
