/**
 * Fix Notification Import Issues
 * 
 * This script diagnoses and fixes common import issues with the notification system.
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function fixNotificationImports() {
  console.log('🔧 Fixing notification import issues...');

  // Step 1: Check if database tables exist
  console.log('\n1. Checking database tables...');
  try {
    await prisma.$connect();
    
    // Check if notification tables exist (NotificationTemplate was removed)
    const tables = ['NotificationPreference', 'NotificationEvent'];
    const missingTables = [];

    for (const table of tables) {
      try {
        await prisma.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
        console.log(`✅ Table ${table} exists`);
      } catch (error) {
        console.log(`❌ Table ${table} missing`);
        missingTables.push(table);
      }
    }

    // Note: NotificationTemplate table was removed - templates are now hardcoded
    console.log('ℹ️  NotificationTemplate table was removed - templates are now hardcoded in the notification engine');

    if (missingTables.length > 0) {
      console.log('\n⚠️  Missing database tables detected!');
      console.log('Please run the following commands:');
      console.log('');
      console.log('1. Generate Prisma client:');
      console.log('   npx prisma generate');
      console.log('');
      console.log('2. Run database migration:');
      console.log('   npx prisma migrate dev --name add-notification-system');
      console.log('');
      console.log('3. If migration fails, try pushing schema:');
      console.log('   npx prisma db push');
      
      await prisma.$disconnect();
      return false;
    }

    await prisma.$disconnect();
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }

  // Step 2: Test module imports
  console.log('\n2. Testing module imports...');
  try {
    // Test individual modules
    const eventSystem = await import('@/lib/events/event-system');
    console.log('✅ Event system imported');

    const preferenceManager = await import('@/lib/notifications/preference-manager');
    console.log('✅ Preference manager imported');

    const templateManager = await import('@/lib/notifications/template-manager');
    console.log('✅ Template manager imported');

    const notificationRegistry = await import('@/lib/notifications/notification-registry');
    console.log('✅ Notification registry imported');

    // Test main module
    const notifications = await import('@/lib/notifications');
    console.log('✅ Main notifications module imported');

    if (notifications.preferences) {
      console.log('✅ Preferences object available');
    } else {
      console.log('❌ Preferences object missing');
      return false;
    }

    if (notifications.templates) {
      console.log('✅ Templates object available');
    } else {
      console.log('❌ Templates object missing');
      return false;
    }

  } catch (error) {
    console.error('❌ Module import failed:', error.message);
    return false;
  }

  // Step 3: Test API functionality
  console.log('\n3. Testing API functionality...');
  try {
    const { preferences } = await import('@/lib/notifications');
    
    // Test with a dummy user ID
    const testUserId = 'test-import-fix';
    
    // This should not fail even if user doesn't exist
    const userPrefs = await preferences.getForUser(testUserId);
    console.log('✅ Preferences API working');
    
  } catch (error) {
    console.error('❌ API test failed:', error.message);
    return false;
  }

  console.log('\n🎉 All import tests passed!');
  return true;
}

async function createFixedAPIRoute() {
  console.log('\n4. Creating fixed API route...');
  
  const fixedRouteContent = `import { NextRequest, NextResponse } from "next/server";
import { verifyAuthToken } from "@/lib/auth-utils";

// GET /api/notifications/preferences - Get user notification preferences
export async function GET(request: NextRequest) {
  try {
    // Dynamic import to avoid module loading issues
    const { preferences } = await import("@/lib/notifications");
    
    if (!preferences) {
      return NextResponse.json(
        { error: "Notification preferences module not available" },
        { status: 500 }
      );
    }

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get user preferences
    const userPreferences = await preferences.getForUser(auth.user.id);

    return NextResponse.json({
      success: true,
      preferences: userPreferences,
    });
  } catch (error) {
    console.error("Error fetching notification preferences:", error);
    return NextResponse.json(
      { error: "Failed to fetch notification preferences", message: error.message },
      { status: 500 }
    );
  }
}

// PUT /api/notifications/preferences - Update user notification preferences
export async function PUT(request: NextRequest) {
  try {
    // Dynamic import to avoid module loading issues
    const { preferences } = await import("@/lib/notifications");
    
    if (!preferences) {
      return NextResponse.json(
        { error: "Notification preferences module not available" },
        { status: 500 }
      );
    }

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get request body
    const body = await request.json();

    // Validate request body
    if (!body.preferences || !Array.isArray(body.preferences)) {
      return NextResponse.json(
        { error: "Invalid request body. Expected 'preferences' array." },
        { status: 400 }
      );
    }

    // Update user preferences
    await preferences.updateForUser(auth.user.id, body.preferences);

    // Get updated preferences
    const updatedPreferences = await preferences.getForUser(auth.user.id);

    return NextResponse.json({
      success: true,
      message: "Notification preferences updated successfully",
      preferences: updatedPreferences,
    });
  } catch (error) {
    console.error("Error updating notification preferences:", error);
    return NextResponse.json(
      { error: "Failed to update notification preferences", message: error.message },
      { status: 500 }
    );
  }
}

// POST /api/notifications/preferences - Bulk enable/disable notifications
export async function POST(request: NextRequest) {
  try {
    // Dynamic import to avoid module loading issues
    const { preferences } = await import("@/lib/notifications");
    
    if (!preferences) {
      return NextResponse.json(
        { error: "Notification preferences module not available" },
        { status: 500 }
      );
    }

    // Check authentication
    const auth = await verifyAuthToken(request);

    if (!auth.authenticated) {
      return NextResponse.json(
        { error: auth.error },
        { status: auth.status }
      );
    }

    // Get request body to check action
    const body = await request.json();

    if (body.action === 'disable-all') {
      await preferences.disableAll(auth.user.id);
      
      return NextResponse.json({
        success: true,
        message: "All notifications disabled successfully",
      });
    } else if (body.action === 'enable-all') {
      await preferences.enableAll(auth.user.id);
      
      return NextResponse.json({
        success: true,
        message: "All notifications enabled successfully",
      });
    } else {
      return NextResponse.json(
        { error: "Invalid action. Expected 'disable-all' or 'enable-all'." },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error("Error updating notification preferences:", error);
    return NextResponse.json(
      { error: "Failed to update notification preferences", message: error.message },
      { status: 500 }
    );
  }
}
`;

  console.log('✅ Fixed API route content generated');
  console.log('📝 You can copy this content to src/app/api/notifications/preferences/route.ts');
  
  return fixedRouteContent;
}

async function main() {
  console.log('🚀 Starting notification import fix process...');
  
  const success = await fixNotificationImports();
  
  if (success) {
    console.log('\n✅ Import fix completed successfully!');
    console.log('\nThe notification preferences page should now work at:');
    console.log('http://localhost:3000/settings/notifications');
  } else {
    console.log('\n❌ Import fix failed. Please follow the instructions above.');
    await createFixedAPIRoute();
  }
}

// Run the fix if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { fixNotificationImports, createFixedAPIRoute };
