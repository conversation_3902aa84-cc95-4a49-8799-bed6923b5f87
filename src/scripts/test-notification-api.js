/**
 * Simple test script to verify notification API endpoints
 */

const fetch = require('node-fetch');

async function testNotificationAPI() {
  console.log('🔍 Testing notification API endpoints...');

  try {
    // Test preferences endpoint
    console.log('\n1. Testing preferences endpoint...');
    const preferencesResponse = await fetch('http://localhost:3000/api/notifications/preferences');
    console.log('Preferences status:', preferencesResponse.status);
    console.log('Preferences headers:', Object.fromEntries(preferencesResponse.headers));
    
    if (preferencesResponse.status === 403) {
      console.log('✅ Preferences endpoint working (403 expected without auth)');
    } else {
      const preferencesText = await preferencesResponse.text();
      console.log('Preferences response:', preferencesText.substring(0, 200) + '...');
    }

    // Test templates endpoint
    console.log('\n2. Testing templates endpoint...');
    const templatesResponse = await fetch('http://localhost:3000/api/notifications/templates');
    console.log('Templates status:', templatesResponse.status);
    console.log('Templates headers:', Object.fromEntries(templatesResponse.headers));
    
    if (templatesResponse.status === 403) {
      console.log('✅ Templates endpoint working (403 expected without auth)');
    } else {
      const templatesText = await templatesResponse.text();
      console.log('Templates response:', templatesText.substring(0, 200) + '...');
    }

    console.log('\n🎉 API endpoints are responding correctly!');
    console.log('Next step: Test with authentication in the browser');

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

testNotificationAPI();
