import React from "react";
import { render, screen, fireEvent, waitFor, act } from "@testing-library/react";
import { ChatWindow } from "@/components/chat/ChatWindow";
import { useClientAuth } from "@/hooks/use-client-auth";

// Mock the useClientAuth hook
jest.mock("@/hooks/use-client-auth");
const mockUseClientAuth = useClientAuth as jest.MockedFunction<typeof useClientAuth>;

// Mock fetch
const mockFetch = global.fetch as jest.MockedFunction<typeof fetch>;

// Mock date-fns format function
jest.mock("date-fns", () => ({
  format: jest.fn(() => "12:00 PM"),
}));

describe("ChatWindow", () => {
  const mockUser = {
    id: "user-1",
    name: "Test User",
    email: "<EMAIL>",
    role: "USER",
  };

  const mockConversation = {
    id: "conv-1",
    title: "Test Conversation",
    participants: [
      mockUser,
      { id: "user-2", name: "Other User", email: "<EMAIL>", role: "USER" },
    ],
    messages: [],
    isStarred: false,
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  };

  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    mockUseClientAuth.mockReturnValue({ user: mockUser });

    // Mock successful conversation fetch
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ conversation: mockConversation }),
    } as Response);
  });

  it("should render without crashing", async () => {
    await act(async () => {
      render(<ChatWindow conversationId="conv-1" onClose={mockOnClose} />);
    });

    expect(screen.getByText("Test Conversation")).toBeInTheDocument();
  });

  it('should show "No messages yet" when conversation has no messages', async () => {
    await act(async () => {
      render(<ChatWindow conversationId="conv-1" onClose={mockOnClose} />);
    });

    await waitFor(() => {
      expect(screen.getByText("No messages yet. Start the conversation!")).toBeInTheDocument();
    });
  });

  it("should handle first message without flickering", async () => {
    // Mock successful message send
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ conversation: mockConversation }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          conversation: {
            ...mockConversation,
            messages: [
              {
                id: "msg-1",
                content: "Hello world",
                senderId: "user-1",
                createdAt: new Date().toISOString(),
                sender: mockUser,
              },
            ],
          },
        }),
      } as Response);

    await act(async () => {
      render(<ChatWindow conversationId="conv-1" onClose={mockOnClose} />);
    });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText("No messages yet. Start the conversation!")).toBeInTheDocument();
    });

    // Type and send a message
    const input = screen.getByPlaceholderText("Type a message...");

    await act(async () => {
      fireEvent.change(input, { target: { value: "Hello world" } });
    });

    // Find the submit button (it's a type="submit" button)
    const sendButton = screen.getByRole("button", {
      name: (name, element) => element?.getAttribute("type") === "submit",
    });

    await act(async () => {
      fireEvent.click(sendButton);
    });

    // The temporary message should appear immediately
    await waitFor(() => {
      expect(screen.getByText("Hello world")).toBeInTheDocument();
    });

    // The "No messages yet" text should not appear after sending
    expect(screen.queryByText("No messages yet. Start the conversation!")).not.toBeInTheDocument();

    // Wait for the server response to be processed
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Message should still be visible (no flickering)
    expect(screen.getByText("Hello world")).toBeInTheDocument();
    expect(screen.queryByText("No messages yet. Start the conversation!")).not.toBeInTheDocument();
  });

  it("should merge temporary and server messages correctly", async () => {
    const serverMessage = {
      id: "msg-server-1",
      content: "Hello world",
      senderId: "user-1",
      createdAt: new Date().toISOString(),
      sender: mockUser,
    };

    // Mock the fetch responses
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ conversation: mockConversation }),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({}),
      } as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          conversation: {
            ...mockConversation,
            messages: [serverMessage],
          },
        }),
      } as Response);

    await act(async () => {
      render(<ChatWindow conversationId="conv-1" onClose={mockOnClose} />);
    });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText("No messages yet. Start the conversation!")).toBeInTheDocument();
    });

    // Send a message
    const input = screen.getByPlaceholderText("Type a message...");
    const sendButton = screen.getByRole("button", {
      name: (name, element) => element?.getAttribute("type") === "submit",
    });

    await act(async () => {
      fireEvent.change(input, { target: { value: "Hello world" } });
      fireEvent.click(sendButton);
    });

    // Temporary message should appear
    await waitFor(() => {
      expect(screen.getByText("Hello world")).toBeInTheDocument();
    });

    // Wait for server response
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Should only have one instance of the message (temp message replaced by server message)
    const messages = screen.getAllByText("Hello world");
    expect(messages).toHaveLength(1);
  });

  it("should handle message send errors gracefully", async () => {
    // Mock failed message send
    mockFetch
      .mockResolvedValueOnce({
        ok: true,
        json: async () => ({ conversation: mockConversation }),
      } as Response)
      .mockResolvedValueOnce({
        ok: false,
        json: async () => ({ error: "Failed to send message" }),
      } as Response);

    await act(async () => {
      render(<ChatWindow conversationId="conv-1" onClose={mockOnClose} />);
    });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText("No messages yet. Start the conversation!")).toBeInTheDocument();
    });

    // Send a message
    const input = screen.getByPlaceholderText("Type a message...");
    const sendButton = screen.getByRole("button", {
      name: (name, element) => element?.getAttribute("type") === "submit",
    });

    await act(async () => {
      fireEvent.change(input, { target: { value: "Hello world" } });
      fireEvent.click(sendButton);
    });

    // Wait for error handling - the temporary message should be removed immediately on error
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 100));
    });

    // Temporary message should be removed due to error
    await waitFor(() => {
      expect(screen.queryByText("Hello world")).not.toBeInTheDocument();
    });

    // Should show "No messages yet" again
    expect(screen.getByText("No messages yet. Start the conversation!")).toBeInTheDocument();
  });
});
