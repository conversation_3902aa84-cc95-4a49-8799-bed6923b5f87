/**
 * Test file for Stock Management Page Search Functionality
 * Tests the fix for TypeError when accessing undefined product properties
 */

describe('Stock Management Search Functionality', () => {
  // Mock data structures
  const mockStoreStock = [
    {
      id: 'store-1',
      productId: 'product-1',
      quantity: 100,
      minThreshold: 10,
      product: {
        id: 'product-1',
        name: 'Test Product 1',
        sku: 'TEST-001',
        category: { id: 'cat-1', name: 'Category 1' }
      }
    },
    {
      id: 'store-2',
      productId: 'product-2',
      quantity: 50,
      minThreshold: 5,
      product: {
        id: 'product-2',
        name: 'Test Product 2',
        sku: 'TEST-002',
        category: { id: 'cat-2', name: 'Category 2' }
      }
    },
    // Test case with undefined product (should be filtered out)
    {
      id: 'store-3',
      productId: 'product-3',
      quantity: 25,
      minThreshold: 3,
      product: undefined
    },
    // Test case with null product (should be filtered out)
    {
      id: 'store-4',
      productId: 'product-4',
      quantity: 75,
      minThreshold: 8,
      product: null
    }
  ];

  const mockLowStockItems = [
    {
      id: 'product-5',
      name: 'Low Stock Product 1',
      sku: 'LOW-001',
      category: { id: 'cat-3', name: 'Category 3' }
    },
    {
      id: 'product-6',
      name: 'Low Stock Product 2',
      sku: 'LOW-002',
      category: { id: 'cat-4', name: 'Category 4' }
    },
    // Test case with undefined name (should be filtered out)
    {
      id: 'product-7',
      name: undefined,
      sku: 'LOW-003',
      category: { id: 'cat-5', name: 'Category 5' }
    },
    // Test case with null item (should be filtered out)
    null
  ];

  // Filter function for stock items (storeStock and warehouseStock)
  const filterStock = (stock: any[], searchTerm: string, categoryFilter: string) => {
    return stock.filter((item) => {
      // Add null/undefined checks for item and item.product
      if (!item || !item.product) {
        return false;
      }

      const matchesSearch = searchTerm
        ? (item.product.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           item.product.sku?.toLowerCase().includes(searchTerm.toLowerCase()))
        : true;

      const matchesCategory = categoryFilter
        ? item.product.category?.id === categoryFilter
        : true;

      return matchesSearch && matchesCategory;
    });
  };

  // Filter function for low stock items (direct product structure)
  const filterLowStockItems = (items: any[], searchTerm: string, categoryFilter: string) => {
    return items.filter((item) => {
      // Add null/undefined checks for item
      if (!item) {
        return false;
      }

      const matchesSearch = searchTerm
        ? (item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
           item.sku?.toLowerCase().includes(searchTerm.toLowerCase()))
        : true;

      const matchesCategory = categoryFilter
        ? item.category?.id === categoryFilter
        : true;

      return matchesSearch && matchesCategory;
    });
  };

  describe('Stock Items Filtering (Store/Warehouse)', () => {
    it('should filter stock items by product name', () => {
      const result = filterStock(mockStoreStock, 'Test Product 1', '');
      expect(result).toHaveLength(1);
      expect(result[0].product.name).toBe('Test Product 1');
    });

    it('should filter stock items by SKU', () => {
      const result = filterStock(mockStoreStock, 'TEST-002', '');
      expect(result).toHaveLength(1);
      expect(result[0].product.sku).toBe('TEST-002');
    });

    it('should filter stock items by category', () => {
      const result = filterStock(mockStoreStock, '', 'cat-1');
      expect(result).toHaveLength(1);
      expect(result[0].product.category.id).toBe('cat-1');
    });

    it('should handle undefined/null product objects without throwing errors', () => {
      expect(() => {
        const result = filterStock(mockStoreStock, 'test', '');
        // Should not throw TypeError and should filter out items with undefined/null products
        expect(result).toHaveLength(2); // Only items with valid products
      }).not.toThrow();
    });

    it('should return empty array when no matches found', () => {
      const result = filterStock(mockStoreStock, 'nonexistent', '');
      expect(result).toHaveLength(0);
    });

    it('should return all valid items when no search term provided', () => {
      const result = filterStock(mockStoreStock, '', '');
      expect(result).toHaveLength(2); // Only items with valid products
    });
  });

  describe('Low Stock Items Filtering', () => {
    it('should filter low stock items by product name', () => {
      const result = filterLowStockItems(mockLowStockItems, 'Low Stock Product 1', '');
      expect(result).toHaveLength(1);
      expect(result[0].name).toBe('Low Stock Product 1');
    });

    it('should filter low stock items by SKU', () => {
      const result = filterLowStockItems(mockLowStockItems, 'LOW-002', '');
      expect(result).toHaveLength(1);
      expect(result[0].sku).toBe('LOW-002');
    });

    it('should filter low stock items by category', () => {
      const result = filterLowStockItems(mockLowStockItems, '', 'cat-3');
      expect(result).toHaveLength(1);
      expect(result[0].category.id).toBe('cat-3');
    });

    it('should handle undefined/null items without throwing errors', () => {
      expect(() => {
        const result = filterLowStockItems(mockLowStockItems, 'test', '');
        // Should not throw TypeError and should filter out null/undefined items
        expect(result).toHaveLength(0); // No items match 'test' search
      }).not.toThrow();
    });

    it('should handle items with undefined name property', () => {
      expect(() => {
        const result = filterLowStockItems(mockLowStockItems, 'LOW-003', '');
        // Should find item by SKU even if name is undefined
        expect(result).toHaveLength(1);
        expect(result[0].sku).toBe('LOW-003');
      }).not.toThrow();
    });

    it('should return empty array when no matches found', () => {
      const result = filterLowStockItems(mockLowStockItems, 'nonexistent', '');
      expect(result).toHaveLength(0);
    });

    it('should return all valid items when no search term provided', () => {
      const result = filterLowStockItems(mockLowStockItems, '', '');
      expect(result).toHaveLength(3); // All valid items (excluding null, but including undefined name)
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty arrays', () => {
      expect(() => {
        const stockResult = filterStock([], 'test', '');
        const lowStockResult = filterLowStockItems([], 'test', '');
        expect(stockResult).toHaveLength(0);
        expect(lowStockResult).toHaveLength(0);
      }).not.toThrow();
    });

    it('should handle case-insensitive search', () => {
      const stockResult = filterStock(mockStoreStock, 'test product', '');
      const lowStockResult = filterLowStockItems(mockLowStockItems, 'low stock', '');
      
      expect(stockResult).toHaveLength(2);
      expect(lowStockResult).toHaveLength(2);
    });

    it('should handle partial matches', () => {
      const stockResult = filterStock(mockStoreStock, 'Product', '');
      const lowStockResult = filterLowStockItems(mockLowStockItems, 'Stock', '');
      
      expect(stockResult).toHaveLength(2);
      expect(lowStockResult).toHaveLength(2);
    });
  });
});
