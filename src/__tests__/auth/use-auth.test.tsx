import { renderHook, waitFor } from "@testing-library/react";
import { useAuth } from "@/hooks/use-auth";

// Mock fetch
global.fetch = jest.fn();

describe("useAuth Hook", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns null user and loading true initially", async () => {
    // Mock fetch to delay response
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () =>
          new Promise((resolve) => {
            setTimeout(() => resolve({ user: null }), 100);
          }),
      })
    );

    const { result } = renderHook(() => useAuth());

    // Initially, user should be null and loading should be true
    expect(result.current.user).toBeNull();
    expect(result.current.loading).toBe(true);
  });

  it("returns user data when authenticated", async () => {
    // Mock authenticated user
    const mockUser = {
      id: "1",
      name: "Test User",
      email: "<EMAIL>",
      role: "CASHIER",
    };

    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ user: mockUser }),
    });

    const { result } = renderHook(() => useAuth());

    // Wait for the hook to update
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // User data should be available
    expect(result.current.user).toEqual(mockUser);
  });

  it("returns null user when not authenticated", async () => {
    // Mock no authenticated user
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({ user: null }),
    });

    const { result } = renderHook(() => useAuth());

    // Wait for the hook to update
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // User should be null
    expect(result.current.user).toBeNull();
  });

  it("handles auth errors gracefully", async () => {
    // Mock fetch error
    (global.fetch as jest.Mock).mockRejectedValue(new Error("Fetch error"));

    const { result } = renderHook(() => useAuth());

    // Wait for the hook to update
    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // User should be null on error
    expect(result.current.user).toBeNull();
  });
});
