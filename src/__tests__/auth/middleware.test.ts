// Import the middleware function
const { middleware } = require('@/middleware');

// Mock the auth function
const { auth } = require('@/auth');

// Mock NextResponse
const NextResponse = {
  next: jest.fn(() => ({ type: 'next' })),
  redirect: jest.fn((url) => ({ type: 'redirect', url })),
};

// Mock NextRequest
class NextRequest {
  constructor(url) {
    this.nextUrl = new URL(url);
    this.url = url;
  }
}

describe('Authentication Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Helper function to create a mock request
  const createMockRequest = (path) => {
    const url = `http://localhost:3000${path}`;
    return new NextRequest(url);
  };

  it('allows access to public routes without authentication', async () => {
    // Mock no authenticated user
    (auth as jest.Mock).mockResolvedValue(null);

    // Test public routes
    const publicRoutes = ['/', '/login', '/api/auth/signin'];

    for (const route of publicRoutes) {
      const req = createMockRequest(route);
      await middleware(req);

      expect(NextResponse.next).toHaveBeenCalled();
      expect(NextResponse.redirect).not.toHaveBeenCalled();

      jest.clearAllMocks();
    }
  });

  it('redirects to login for protected routes when not authenticated', async () => {
    // Mock no authenticated user
    (auth as jest.Mock).mockResolvedValue(null);

    // Test protected routes
    const protectedRoutes = ['/dashboard', '/admin/users', '/backup'];

    for (const route of protectedRoutes) {
      const req = createMockRequest(route);
      await middleware(req);

      expect(NextResponse.next).not.toHaveBeenCalled();
      expect(NextResponse.redirect).toHaveBeenCalled();

      // Check redirect URL contains login path
      const redirectCall = (NextResponse.redirect as jest.Mock).mock.calls[0][0];
      expect(redirectCall.pathname).toBe('/login');

      jest.clearAllMocks();
    }
  });

  it('allows access to dashboard for authenticated users', async () => {
    // Mock authenticated user
    (auth as jest.Mock).mockResolvedValue({
      user: { id: '1', name: 'Test User', email: '<EMAIL>', role: 'CASHIER' },
    });

    const req = createMockRequest('/dashboard');
    await middleware(req);

    expect(NextResponse.next).toHaveBeenCalled();
    expect(NextResponse.redirect).not.toHaveBeenCalled();
  });

  it('restricts access to admin routes for non-admin users', async () => {
    // Mock authenticated non-admin user
    (auth as jest.Mock).mockResolvedValue({
      user: { id: '1', name: 'Test User', email: '<EMAIL>', role: 'CASHIER' },
    });

    const req = createMockRequest('/admin/users');
    await middleware(req);

    expect(NextResponse.next).not.toHaveBeenCalled();
    expect(NextResponse.redirect).toHaveBeenCalled();

    // Check redirect URL is dashboard
    const redirectCall = (NextResponse.redirect as jest.Mock).mock.calls[0][0];
    expect(redirectCall.pathname).toBe('/dashboard');
  });

  it('allows access to admin routes for admin users', async () => {
    // Mock authenticated admin user
    (auth as jest.Mock).mockResolvedValue({
      user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
    });

    const req = createMockRequest('/admin/users');
    await middleware(req);

    expect(NextResponse.next).toHaveBeenCalled();
    expect(NextResponse.redirect).not.toHaveBeenCalled();
  });

  it('restricts access to backup routes for non-authorized users', async () => {
    // Mock authenticated user without backup permissions
    (auth as jest.Mock).mockResolvedValue({
      user: { id: '1', name: 'Test User', email: '<EMAIL>', role: 'CASHIER' },
    });

    const req = createMockRequest('/backup');
    await middleware(req);

    expect(NextResponse.next).not.toHaveBeenCalled();
    expect(NextResponse.redirect).toHaveBeenCalled();
  });

  it('allows access to backup routes for authorized users', async () => {
    // Test with both authorized roles
    const authorizedRoles = ['SUPER_ADMIN', 'FINANCE_ADMIN'];

    for (const role of authorizedRoles) {
      // Mock authenticated user with backup permissions
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Test User', email: '<EMAIL>', role },
      });

      const req = createMockRequest('/backup');
      await middleware(req);

      expect(NextResponse.next).toHaveBeenCalled();
      expect(NextResponse.redirect).not.toHaveBeenCalled();

      jest.clearAllMocks();
    }
  });
});
