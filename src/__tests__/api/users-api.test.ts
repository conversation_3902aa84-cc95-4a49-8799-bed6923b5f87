// Import the API route handlers
const { GET, POST } = require('@/app/api/users/route');

// Import the mocked functions
const { auth, prisma, hashPassword } = require('@/auth');

// Mock NextResponse
const NextResponse = {
  json: jest.fn((data, options) => ({ data, options })),
};

// Mock NextRequest
class NextRequest {
  constructor(body) {
    this.body = body;
  }

  async json() {
    return this.body;
  }
}

describe('Users API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/users', () => {
    it('returns 403 when user is not authenticated', async () => {
      // Mock no authenticated user
      (auth as jest.Mock).mockResolvedValue(null);

      await GET();

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    });

    it('returns 403 when user is not an admin', async () => {
      // Mock authenticated non-admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Test User', email: '<EMAIL>', role: 'CASHIER' },
      });

      await GET();

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    });

    it('returns users list when user is an admin', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Mock users data
      const mockUsers = [
        { id: '1', name: 'User 1', email: '<EMAIL>', role: 'CASHIER' },
        { id: '2', name: 'User 2', email: '<EMAIL>', role: 'FINANCE_ADMIN' },
      ];

      (prisma.user.findMany as jest.Mock).mockResolvedValue(mockUsers);

      await GET();

      expect(prisma.user.findMany).toHaveBeenCalled();
      expect(NextResponse.json).toHaveBeenCalledWith({ users: mockUsers });
    });

    it('handles errors gracefully', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Mock database error
      const error = new Error('Database error');
      (prisma.user.findMany as jest.Mock).mockRejectedValue(error);

      await GET();

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Failed to fetch users', message: error.message },
        { status: 500 }
      );
    });
  });

  describe('POST /api/users', () => {
    // Helper function to create a mock request
    const createMockRequest = (body) => {
      return new NextRequest(body);
    };

    it('returns 403 when user is not authenticated', async () => {
      // Mock no authenticated user
      (auth as jest.Mock).mockResolvedValue(null);

      const req = createMockRequest({});
      await POST(req);

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    });

    it('returns 403 when user is not an admin', async () => {
      // Mock authenticated non-admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Test User', email: '<EMAIL>', role: 'CASHIER' },
      });

      const req = createMockRequest({});
      await POST(req);

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Unauthorized' },
        { status: 403 }
      );
    });

    it('validates input data', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Invalid data (missing required fields)
      const req = createMockRequest({ name: 'Test User' });
      await POST(req);

      expect(NextResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({ error: 'Validation failed' }),
        { status: 400 }
      );
    });

    it('checks for existing email', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Mock existing user with same email
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({ id: '2', email: '<EMAIL>' });

      const req = createMockRequest({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'CASHIER',
      });

      await POST(req);

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Email already in use' },
        { status: 400 }
      );
    });

    it('creates a new user successfully', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Mock no existing user with same email
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Mock created user
      const mockCreatedUser = {
        id: '2',
        name: 'Test User',
        email: '<EMAIL>',
        role: 'CASHIER',
        active: true,
        createdAt: new Date(),
      };

      (prisma.user.create as jest.Mock).mockResolvedValue(mockCreatedUser);

      const req = createMockRequest({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'CASHIER',
      });

      await POST(req);

      // Check if user was created
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          name: 'Test User',
          email: '<EMAIL>',
          password: 'hashed-password', // From our mock
          role: 'CASHIER',
        },
        select: expect.any(Object),
      });

      // Check if activity was logged
      expect(prisma.activityLog.create).toHaveBeenCalled();

      // Check response
      expect(NextResponse.json).toHaveBeenCalledWith(
        { user: mockCreatedUser },
        { status: 201 }
      );
    });

    it('handles errors gracefully', async () => {
      // Mock authenticated admin user
      (auth as jest.Mock).mockResolvedValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>', role: 'SUPER_ADMIN' },
      });

      // Mock no existing user with same email
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Mock database error
      const error = new Error('Database error');
      (prisma.user.create as jest.Mock).mockRejectedValue(error);

      const req = createMockRequest({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'CASHIER',
      });

      await POST(req);

      expect(NextResponse.json).toHaveBeenCalledWith(
        { error: 'Failed to create user', message: error.message },
        { status: 500 }
      );
    });
  });
});
