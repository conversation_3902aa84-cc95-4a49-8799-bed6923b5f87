/**
 * Test script for Invoice Installment Functionality
 * This script tests the complete installment payment feature
 */

const testInstallmentFunctionality = async () => {
  console.log('🧪 Testing Invoice Installment Functionality...\n');

  try {
    // Test 1: Check if eligible POs are available
    console.log('📋 Test 1: Checking Available Purchase Orders...');
    const poResponse = await fetch('http://localhost:3000/api/purchase-orders?limit=100');
    
    if (!poResponse.ok) {
      throw new Error(`Failed to fetch POs: ${poResponse.status}`);
    }

    const poData = await poResponse.json();
    const eligiblePOs = (poData.purchaseOrders || []).filter(po => 
      ['ORDERED', 'PARTIALLY_RECEIVED', 'RECEIVED'].includes(po.status)
    );
    
    console.log(`   Found ${eligiblePOs.length} eligible POs for invoice creation`);
    
    if (eligiblePOs.length === 0) {
      console.log('   ⚠️  No eligible POs found. Create a PO with ORDERED status to test installments.');
      return;
    }

    const testPO = eligiblePOs[0];
    console.log(`   Using PO: ${testPO.id.slice(-8).toUpperCase()} (${testPO.status})`);

    // Test 2: Test installment form validation
    console.log('\n🔧 Test 2: Testing Installment Form Validation...');
    
    // Test case: 3 installments with proper amounts
    const testInstallments = [
      {
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        amount: 1000000, // 1M IDR
        description: '1st Installment'
      },
      {
        dueDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days
        amount: 1000000, // 1M IDR
        description: '2nd Installment'
      },
      {
        dueDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        amount: 1000000, // 1M IDR
        description: '3rd Installment'
      }
    ];

    const totalInstallmentAmount = testInstallments.reduce((sum, inst) => sum + inst.amount, 0);
    console.log(`   Total installment amount: ${totalInstallmentAmount.toLocaleString('id-ID')} IDR`);

    // Test 3: Create test invoice with installments
    console.log('\n📝 Test 3: Creating Test Invoice with Installments...');
    
    const testInvoiceData = {
      purchaseOrderId: testPO.id,
      invoiceNumber: `TEST-INV-${Date.now()}`,
      invoiceDate: new Date().toISOString(),
      taxPercentage: 11,
      enableInstallments: true,
      numberOfInstallments: 3,
      installments: testInstallments.map(inst => ({
        ...inst,
        dueDate: inst.dueDate.toISOString()
      })),
      items: testPO.items.slice(0, 2).map(item => ({
        purchaseOrderItemId: item.id,
        productId: item.productId,
        description: item.product.name,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
      }))
    };

    console.log('   Test invoice data prepared:');
    console.log(`     - Invoice Number: ${testInvoiceData.invoiceNumber}`);
    console.log(`     - Items: ${testInvoiceData.items.length}`);
    console.log(`     - Installments: ${testInvoiceData.numberOfInstallments}`);
    console.log(`     - Total Amount: ${totalInstallmentAmount.toLocaleString('id-ID')} IDR`);

    // Test 4: Validate installment business logic
    console.log('\n✅ Test 4: Validating Installment Business Logic...');
    
    // Check chronological order
    let isChronological = true;
    for (let i = 1; i < testInstallments.length; i++) {
      if (testInstallments[i].dueDate <= testInstallments[i - 1].dueDate) {
        isChronological = false;
        break;
      }
    }
    console.log(`   ✓ Due dates in chronological order: ${isChronological}`);

    // Check amount distribution
    const avgAmount = totalInstallmentAmount / testInstallments.length;
    const isEvenDistribution = testInstallments.every(inst => inst.amount === avgAmount);
    console.log(`   ✓ Even amount distribution: ${isEvenDistribution}`);

    // Check future dates
    const allFutureDates = testInstallments.every(inst => inst.dueDate > new Date());
    console.log(`   ✓ All due dates in future: ${allFutureDates}`);

    // Test 5: UI Component Testing Checklist
    console.log('\n🎨 Test 5: UI Component Testing Checklist...');
    console.log('   Manual testing required for:');
    console.log('   □ Installment toggle checkbox works');
    console.log('   □ Number of installments field (2-12 validation)');
    console.log('   □ Dynamic installment fields generation');
    console.log('   □ Due date pickers for each installment');
    console.log('   □ Amount fields with auto-calculation');
    console.log('   □ Description fields with default values');
    console.log('   □ Real-time validation error display');
    console.log('   □ Installment summary in sidebar');
    console.log('   □ Form submission with installment data');
    console.log('   □ Collapsible installment section');

    // Test 6: Database Schema Validation
    console.log('\n🗄️  Test 6: Database Schema Validation...');
    console.log('   Required database changes:');
    console.log('   □ Invoice.hasInstallments field added');
    console.log('   □ Invoice.numberOfInstallments field added');
    console.log('   □ InvoiceInstallment table created');
    console.log('   □ InstallmentStatus enum added');
    console.log('   □ Foreign key constraints in place');
    console.log('   □ Indexes for performance optimization');

    // Test 7: API Integration Test
    console.log('\n🔌 Test 7: API Integration Test...');
    console.log('   API endpoints to test:');
    console.log('   □ POST /api/invoices with installment data');
    console.log('   □ GET /api/invoices includes installment data');
    console.log('   □ GET /api/invoices/[id] includes installments');
    console.log('   □ Installment amount validation');
    console.log('   □ PO status validation (ORDERED allowed)');

    // Test Summary
    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Eligible POs found: ${eligiblePOs.length}`);
    console.log(`   ✅ Test data prepared successfully`);
    console.log(`   ✅ Business logic validation passed`);
    console.log(`   ⚠️  Manual UI testing required`);
    console.log(`   ⚠️  Database migration required`);
    console.log(`   ⚠️  API testing with real requests needed`);

    console.log('\n🚀 Next Steps:');
    console.log('   1. Run database migration for installment tables');
    console.log('   2. Test the invoice creation form UI');
    console.log('   3. Create a test invoice with installments');
    console.log('   4. Verify installment data in database');
    console.log('   5. Test installment display in invoice detail page');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

// Test data generators
const generateTestInstallments = (count, totalAmount, startDate = new Date()) => {
  const installmentAmount = totalAmount / count;
  const installments = [];
  
  for (let i = 0; i < count; i++) {
    const dueDate = new Date(startDate);
    dueDate.setMonth(dueDate.getMonth() + i + 1);
    
    installments.push({
      dueDate,
      amount: i === count - 1 
        ? totalAmount - (installmentAmount * (count - 1)) // Last installment gets remainder
        : installmentAmount,
      description: `${i + 1}${i === 0 ? 'st' : i === 1 ? 'nd' : i === 2 ? 'rd' : 'th'} Installment`,
    });
  }
  
  return installments;
};

// Export for browser console usage
if (typeof window !== 'undefined') {
  window.testInstallmentFunctionality = testInstallmentFunctionality;
  window.generateTestInstallments = generateTestInstallments;
  console.log('🔧 Installment testing functions loaded. Run testInstallmentFunctionality() to start testing.');
} else {
  // Node.js environment
  testInstallmentFunctionality();
}
