/**
 * Test script for Advanced Analytics features
 * Tests both Demand Forecasting and Seasonal Predictions
 *
 * Note: This script tests the API endpoints directly.
 * For full testing, you'll need to be logged in through the web interface.
 */

const BASE_URL = 'http://localhost:3000';

async function testAdvancedAnalytics() {
  console.log('🧪 Testing Advanced Analytics Implementation...\n');

  try {
    // Test 1: Get suppliers list
    console.log('1. Testing suppliers endpoint...');
    const suppliersResponse = await fetch(`${BASE_URL}/api/suppliers`);
    
    if (!suppliersResponse.ok) {
      throw new Error(`Suppliers API failed: ${suppliersResponse.status}`);
    }
    
    const suppliersData = await suppliersResponse.json();
    const suppliers = suppliersData.suppliers || [];
    
    console.log(`   ✅ Found ${suppliers.length} suppliers`);
    
    if (suppliers.length === 0) {
      console.log('   ⚠️  No suppliers found - cannot test analytics features');
      return;
    }

    const testSupplierId = suppliers[0].id;
    const testSupplierName = suppliers[0].name;
    console.log(`   📊 Using supplier: ${testSupplierName} (${testSupplierId})\n`);

    // Test 2: Demand Forecasting API
    console.log('2. Testing Demand Forecasting API...');
    
    const forecastResponse = await fetch(
      `${BASE_URL}/api/analytics/demand-forecasting?supplierId=${testSupplierId}&forecastPeriod=60days`
    );
    
    if (!forecastResponse.ok) {
      console.log(`   ❌ Demand Forecasting API failed: ${forecastResponse.status}`);
      const errorText = await forecastResponse.text();
      console.log(`   Error details: ${errorText}`);
    } else {
      const forecastData = await forecastResponse.json();
      console.log(`   ✅ Demand Forecasting API working`);
      console.log(`   📈 Generated forecasts for ${forecastData.data?.totalProducts || 0} products`);
      console.log(`   🎯 Average confidence: ${Math.round(forecastData.data?.aggregateMetrics?.averageConfidenceLevel || 0)}%`);
      console.log(`   📦 Total predicted demand: ${forecastData.data?.aggregateMetrics?.totalPredictedDemand || 0} units`);
    }

    console.log('');

    // Test 3: Seasonal Predictions API
    console.log('3. Testing Seasonal Predictions API...');
    
    const seasonalResponse = await fetch(
      `${BASE_URL}/api/analytics/seasonal-predictions?supplierId=${testSupplierId}`
    );
    
    if (!seasonalResponse.ok) {
      console.log(`   ❌ Seasonal Predictions API failed: ${seasonalResponse.status}`);
      const errorText = await seasonalResponse.text();
      console.log(`   Error details: ${errorText}`);
    } else {
      const seasonalData = await seasonalResponse.json();
      console.log(`   ✅ Seasonal Predictions API working`);
      console.log(`   🌍 Generated predictions for ${seasonalData.data?.upcomingSeasons?.length || 0} seasons`);
      console.log(`   ⚠️  Overall risk level: ${seasonalData.data?.overallRiskAssessment?.riskLevel || 'unknown'}`);
      console.log(`   💡 Strategic recommendations: ${seasonalData.data?.strategicRecommendations?.length || 0}`);
    }

    console.log('');

    // Test 4: Bulk Demand Forecasting
    console.log('4. Testing Bulk Demand Forecasting...');
    
    const bulkSupplierIds = suppliers.slice(0, Math.min(3, suppliers.length)).map(s => s.id);
    
    const bulkForecastResponse = await fetch(`${BASE_URL}/api/analytics/demand-forecasting`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        supplierIds: bulkSupplierIds,
        forecastPeriod: '60days'
      })
    });
    
    if (!bulkForecastResponse.ok) {
      console.log(`   ❌ Bulk Demand Forecasting failed: ${bulkForecastResponse.status}`);
    } else {
      const bulkData = await bulkForecastResponse.json();
      console.log(`   ✅ Bulk Demand Forecasting working`);
      console.log(`   📊 Processed ${bulkData.data?.forecasts?.length || 0}/${bulkSupplierIds.length} suppliers`);
      console.log(`   📈 Success rate: ${Math.round(bulkData.metadata?.successRate || 0)}%`);
    }

    console.log('');

    // Test 5: Page accessibility
    console.log('5. Testing Advanced Analytics page...');
    
    const pageResponse = await fetch(`${BASE_URL}/admin/analytics/advanced`);
    
    if (!pageResponse.ok) {
      console.log(`   ❌ Advanced Analytics page failed: ${pageResponse.status}`);
    } else {
      console.log(`   ✅ Advanced Analytics page accessible`);
    }

    console.log('\n🎉 Advanced Analytics testing completed!');
    console.log('\n📋 Summary:');
    console.log('   • Demand Forecasting: AI-powered demand predictions');
    console.log('   • Seasonal Predictions: Seasonal performance analysis');
    console.log('   • Bulk Processing: Multiple supplier analysis');
    console.log('   • Web Interface: User-friendly dashboard');
    console.log('\n🚀 Features are ready for production use!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure the development server is running (npm run dev)');
    console.log('   2. Check that the database has supplier and transaction data');
    console.log('   3. Verify authentication is properly configured');
    console.log('   4. Check browser console for additional error details');
  }
}

// Run the test
testAdvancedAnalytics();
