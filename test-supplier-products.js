// Test script to verify supplier products API returns correct data structure
const testSupplierProducts = async () => {
  try {
    console.log('Testing supplier products API...');
    
    // Replace with an actual supplier ID from your database
    const testSupplierId = 'your-supplier-id-here';
    
    const response = await fetch(`http://localhost:3001/api/suppliers/${testSupplierId}/products?active=true`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Add authentication headers if needed
        'Cookie': 'session-token=your-session-token-here'
      }
    });

    console.log('Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response:', errorText);
      return;
    }

    const data = await response.json();
    console.log('Success! Response structure:');
    console.log('- Supplier:', data.supplier);
    console.log('- Products count:', data.products?.length || 0);
    
    if (data.products && data.products.length > 0) {
      console.log('- First product structure:');
      const firstProduct = data.products[0];
      console.log('  - Product ID:', firstProduct.id);
      console.log('  - Product name:', firstProduct.name);
      console.log('  - Product SKU:', firstProduct.sku);
      console.log('  - Has supplierInfo:', !!firstProduct.supplierInfo);
      
      if (firstProduct.supplierInfo) {
        console.log('  - SupplierInfo structure:');
        console.log('    - ID:', firstProduct.supplierInfo.id);
        console.log('    - Purchase price:', firstProduct.supplierInfo.purchasePrice);
        console.log('    - MOQ:', firstProduct.supplierInfo.minimumOrderQuantity);
        console.log('    - Is preferred:', firstProduct.supplierInfo.isPreferred);
        console.log('    - Supplier product code:', firstProduct.supplierInfo.supplierProductCode);
      }
    }

    console.log('\nFull response:', JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testSupplierProducts();
