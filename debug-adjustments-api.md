# Debug Guide for Stock Adjustments API Issue

## Problem
Error: "Failed to fetch adjustments" when loading the stock adjustments page.

## Likely Causes & Solutions

### 1. Database Schema Not Updated
**Most Likely Issue**: The new approval workflow fields don't exist in the database yet.

**Solution**: Run the database migration:
```bash
cd /home/<USER>/Dev/NextJS/npos
npx prisma migrate dev --name add-stock-adjustment-approval-workflow
```

If the automatic migration fails, run the manual migration:
```bash
# Connect to your database and run the SQL in manual-migration.sql
```

### 2. Authentication Issues
**Check**: User authentication and role verification

**Debug Steps**:
1. Open browser developer tools
2. Go to Network tab
3. Try to load the adjustments page
4. Check the API request to `/api/inventory/adjustments`
5. Look at the response status and error message

**Common Issues**:
- 403 Forbidden: User doesn't have SUPER_ADMIN or WAREHOUSE_ADMIN role
- 401 Unauthorized: Session token is invalid or missing
- 500 Internal Server Error: Database schema issue

### 3. Prisma Client Out of Sync
**Solution**: Regenerate the Prisma client:
```bash
npx prisma generate
```

### 4. Database Connection Issues
**Check**: Database is running and accessible

## Quick Fixes Applied

### 1. Added Fallback Logic in API
The GET endpoint now has fallback logic to handle cases where the new schema fields don't exist yet.

### 2. Enhanced Error Handling
The frontend now shows more specific error messages to help identify the issue.

## Testing Steps

### Step 1: Check Database Schema
```sql
-- Check if new columns exist
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'StockAdjustment' 
AND column_name IN ('status', 'approvedById', 'approvedAt', 'rejectedAt', 'rejectionReason');

-- Check if enum exists
SELECT enumlabel FROM pg_enum WHERE enumtypid = (
  SELECT oid FROM pg_type WHERE typname = 'AdjustmentStatus'
);
```

### Step 2: Test API Directly
```bash
# Test the GET endpoint directly
curl -X GET "http://localhost:3000/api/inventory/adjustments" \
  -H "Content-Type: application/json" \
  -H "Cookie: session-token=YOUR_SESSION_TOKEN"
```

### Step 3: Check Server Logs
Look at the Next.js server console for detailed error messages.

## Expected Behavior After Fix

1. **WAREHOUSE_ADMIN users**: Can access the page and see adjustments
2. **SUPER_ADMIN users**: Can access the page and see approval actions
3. **Other roles**: Should see access denied message
4. **No authentication**: Should redirect to login

## If Issues Persist

1. **Check the browser console** for JavaScript errors
2. **Check the Network tab** for failed API requests
3. **Check server logs** for detailed error messages
4. **Verify user role** in the database
5. **Test with a SUPER_ADMIN user** first

## Manual Database Update (If Needed)

If you need to manually add the missing fields:

```sql
-- Add the enum type
CREATE TYPE "AdjustmentStatus" AS ENUM ('PENDING_APPROVAL', 'APPROVED', 'REJECTED', 'APPLIED');

-- Add the columns
ALTER TABLE "StockAdjustment" 
ADD COLUMN "status" "AdjustmentStatus" NOT NULL DEFAULT 'APPLIED',
ADD COLUMN "approvedById" TEXT,
ADD COLUMN "approvedAt" TIMESTAMP(3),
ADD COLUMN "rejectedAt" TIMESTAMP(3),
ADD COLUMN "rejectionReason" TEXT;

-- Add foreign key
ALTER TABLE "StockAdjustment" 
ADD CONSTRAINT "StockAdjustment_approvedById_fkey" 
FOREIGN KEY ("approvedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
```

## Next Steps

1. Try accessing the page again
2. Check the specific error message in the browser console
3. If still failing, run the database migration
4. Test with different user roles
5. Verify the approval workflow works end-to-end
